<?php

namespace App\Models;

use CodeIgniter\Model;

class PgWebhookQueueModel extends Model
{
    protected $DBGroup          = 'default';
    protected $table            = 'tb_autopay_pg_webhook_queue';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $insertID         = 0;
    protected $returnType       = 'object';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = ['pg_merchant_id', 'company_id', 'pg_webhook_id', 'event_type', 'status', 'last_retry_time', 'retries_count'];
}
