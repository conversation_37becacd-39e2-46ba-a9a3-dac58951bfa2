<?php

namespace App\Models;

use App\Traits\Models\HasDataTables;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\Model;

class PgOrderModel extends Model
{
    use HasDataTables;

    protected $table = 'tb_autopay_pg_order';

    protected $returnType = 'object';

    protected $allowedFields = [
        'company_id',
        'pg_merchant_id',
        'pg_customer_id',
        'pg_profile_id',
        'pg_agreement_id',
        'order_id',
        'order_status',
        'authentication_status',
        'order_amount',
        'order_currency',
        'order_description',
        'order_invoice_number',
        'order_discount_amount',
        'order_discount_code',
        'order_discount_description',
        'order_tax_amount',
        'custom_data',
        'user_agent',
        'ip_address',
    ];

    protected $companyId = null;

    public function setCompanyId($companyId): self
    {
        $this->companyId = $companyId;

        return $this;
    }

    protected function modifyDatatablesQuery(RequestInterface $request): void
    {
        if ($this->companyId) {
            $this->where('company_id', $this->companyId);
        }

        if ($keyword = trim(xss_clean($request->getPost('keyword')))) {
            $this
                ->groupStart()
                ->like('order_id', "%$keyword%")
                ->orLike('order_description', "%$keyword%")
                ->orLike('order_invoice_number', "%$keyword%")
                ->groupEnd();
        }

        if ($fromDate = trim(xss_clean($request->getPost('from_date')))) {
            $this->where('created_at >=', date('Y-m-d 00:00:00', strtotime($fromDate)));
        }

        if ($toDate = trim(xss_clean($request->getPost('to_date')))) {
            $this->where('created_at <=', date('Y-m-d 23:59:59', strtotime($toDate)));
        }

        if ($amountMin = trim(xss_clean($request->getPost('amount_min')))) {
            $this->where('order_amount >=', $amountMin);
        }

        if ($amountMax = trim(xss_clean($request->getPost('amount_max')))) {
            $this->where('order_amount <=', $amountMax);
        }

        if ($statuses = $request->getPost('statuses') ?: []) {
            $this->whereIn('order_status', $statuses);
        }

        if ($merchantIds = $request->getPost('merchant_ids') ?: []) {
            $this->whereIn('pg_merchant_id', $merchantIds);
        }

        if ($currency = trim(xss_clean($request->getPost('currency')))) {
            $this->where('order_currency', $currency);
        }
    }

    public function countAll(): int
    {
        return $this
            ->where('company_id', $this->companyId)
            ->countAllResults();
    }
}
