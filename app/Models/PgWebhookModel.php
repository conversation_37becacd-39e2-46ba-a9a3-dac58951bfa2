<?php

namespace App\Models;

use CodeIgniter\Model;
use App\Libraries\Webhook;
use App\Libraries\ProxyCURLRequest;
use App\Config\PaymentGatewayConfig;

class PgWebhookModel extends Model
{
    protected $DBGroup          = 'default';
    protected $table            = 'tb_autopay_pg_webhook';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $insertID         = 0;
    protected $returnType       = 'object';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = ['pg_merchant_id', 'company_id', 'auth_type', 'request_content_type', 'secret_key', 'webhook_url', 'allowed_event_types', 'active'];

    /**
     * @param "PAYMENT_TOKEN_FAILED" $eventType
     * @param "First"|"Retry" $type
     */
    public function send(string $eventType, object $pgWebhook, array $data, string $type = 'First')
    {
        /** @var PaymentGatewayConfig $paymentGatewayConfig */
        $paymentGatewayConfig = config(PaymentGatewayConfig::class);

        $webhook = new Webhook();

        $method = 'POST';

        $url = $pgWebhook->webhook_url;

        $headers = [
            'Content-Type' => $pgWebhook->request_content_type ?? 'application/json'
        ];

        $auth = [
            'type' => $pgWebhook->auth_type ?? null,
            'options' => [
                'secret_key' => $pgWebhook->secret_key ?? null
            ]
        ];

        $options = [
            'proxy' => $paymentGatewayConfig->webhookProxyName ?? null,
            'timeout' => $paymentGatewayConfig->webhookTimeout ?? 30,
        ];

        $data = array_merge($data, [
            'event_type' => $eventType,
        ]);

        $webhookLogData = [
            'pg_merchant_id' => $pgWebhook->pg_merchant_id,
            'company_id' => $pgWebhook->company_id,
            'pg_webhook_id' => $pgWebhook->id,
            'proxy' => $options['proxy'] ? ProxyCURLRequest::getProxy($options['proxy']) : null,
            'request_method' => $method,
            'request_url' => $url,
            'request_header' => json_encode($headers),
            'request_body' => count($data) ? json_encode($data) : null,
        ];

        $success = false;

        try {
            /** @var Response $response */
            $response = $webhook->send(
                $method,
                $pgWebhook->webhook_url,
                $headers,
                $data,
                $auth,
                $options
            );

            $webhookLogData['type'] = $type;
            $webhookLogData['response_status_code'] = $response->getStatusCode();
            $webhookLogData['response_body'] = $response->getBody();
            $webhookLogData['response_header'] = json_encode($response->headers());
            $webhookLogData['connected'] = 1;
            $success = true;
        } catch (\Throwable $e) {
            log_message('error', sprintf('[PgWebhookModel->send] Error sending webhook: %s', $e->getCode()));
            $webhookLogData['connected'] = 0;
            $webhookLogData['curl_error'] = $e->getMessage();
        }

        $pgWebhookLogModel = model(PgWebhookLogModel::class);
        $pgWebhookLogId = $pgWebhookLogModel->insert($webhookLogData);

        if (!$pgWebhookLogId) {
            log_message('error', sprintf('[PgWebhookModel->send] Failed to insert webhook log: %s', $pgWebhookLogModel->errors()));
        }

        return $success;
    }
}
