<?php

namespace App\Models;

use CodeIgniter\Model;
use App\Libraries\Webhook;
use CodeIgniter\HTTP\Response;
use App\Models\PgCustomerModel;
use App\Models\PgAgreementModel;
use App\Libraries\ProxyCURLRequest;
use App\Config\PaymentGatewayConfig;

class PgIpnModel extends Model
{
    protected $DBGroup          = 'default';
    protected $table            = 'tb_autopay_pg_ipn';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $insertID         = 0;
    protected $returnType       = 'object';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = ['pg_merchant_id', 'company_id', 'auth_type', 'request_content_type', 'secret_key', 'ipn_url', 'active'];

    /**
     * @param "ORDER_PAID"|"RENEWAL_ORDER_PAID" $notificationType
     * @param "First"|"Retry" $type
     */
    public function send(string $notificationType, object $ipn, object $pgOrder, object $pgTransaction, $type)
    {
        /** @var PaymentGatewayConfig $paymentGatewayConfig */
        $paymentGatewayConfig = config(PaymentGatewayConfig::class);

        $webhook = new Webhook();

        $method = 'POST';

        $url = $ipn->ipn_url;

        $headers = [
            'Content-Type' => $ipn->request_content_type ?? 'application/json'
        ];

        $pgCustomerModel = slavable_model(PgCustomerModel::class, 'PaymentGateway');
        $pgAgreementModel = slavable_model(PgAgreementModel::class, 'PaymentGateway');

        $pgCustomer = $pgCustomerModel->where([
            'company_id' => $pgOrder->company_id,
            'pg_merchant_id' => $pgOrder->pg_merchant_id,
            'id' => $pgOrder->pg_customer_id,
        ])->first();

        $pgAgreement = $pgCustomer ? $pgAgreementModel->where([
            'company_id' => $pgOrder->company_id,
            'pg_merchant_id' => $pgOrder->pg_merchant_id,
            'pg_customer_id' => $pgCustomer->id,
            'id' => $pgOrder->pg_agreement_id,
            'active' => 1,
            'auto_renew' => 1,
        ])->orderBy('id', 'desc')->first() : null;

        $data = [
            'timestamp' => time(),
            'notification_type' => $notificationType,
            'order' => [
                'id' => $pgOrder->id,
                'order_id' => $pgOrder->order_id,
                'order_status' => $pgOrder->order_status,
                'order_currency' => $pgOrder->order_currency,
                'order_amount' => $pgOrder->order_amount,
                'order_invoice_number' => $pgOrder->order_invoice_number,
                'custom_data' => json_decode($pgOrder->custom_data, true) ?? [],
                'user_agent' => $pgOrder->user_agent,
                'ip_address' => $pgOrder->ip_address,
                'order_description' => $pgOrder->order_description,
            ],
            'transaction' => [
                'id' => $pgTransaction->id,
                'payment_method' => $pgTransaction->payment_method,
                'transaction_id' => $pgTransaction->transaction_id,
                'transaction_type' => $pgTransaction->transaction_type,
                'transaction_date' => $pgTransaction->transaction_date,
                'transaction_status' => $pgTransaction->transaction_status,
                'transaction_amount' => $pgTransaction->transaction_amount,
                'transaction_currency' => $pgTransaction->transaction_currency,
                'authentication_status' => $pgTransaction->authentication_status,
                'card_number' => $pgTransaction->card_number,
                'card_holder_name' => $pgTransaction->card_holder_name,
                'card_expiry' => $pgTransaction->card_expiry,
                'card_funding_method' => $pgTransaction->card_funding_method,
                'card_brand' => $pgTransaction->card_brand,
            ],
            'customer' => is_object($pgCustomer) ? [
                'id' => $pgCustomer->id,
                'customer_id' => $pgCustomer->customer_id,
            ] : null,
            'agreement' => is_object($pgAgreement) ? [
                'id' => $pgAgreement->id,
                'agreement_name' => $pgAgreement->agreement_name,
                'agreement_id' => $pgAgreement->agreement_id,
                'type' => $pgAgreement->type,
                'status' => $pgAgreement->status,
                'active' => $pgAgreement->active,
                'auto_renew' => $pgAgreement->auto_renew,
                'amount_variability' => $pgAgreement->amount_variability,
                'amount_per_payment' => $pgAgreement->amount_per_payment,
                'min_amount_per_payment' => $pgAgreement->min_amount_per_payment,
                'max_amount_per_payment' => $pgAgreement->max_amount_per_payment,
                'start_date' => $pgAgreement->start_date,
                'expiry_date' => $pgAgreement->expiry_date,
                'next_due_date' => $pgAgreement->next_due_date,
                'payment_frequency' => $pgAgreement->payment_frequency,
                'minimum_days_between_payments' => $pgAgreement->minimum_days_between_payments,
            ] : null,
        ];

        $auth = [
            'type' => $ipn->auth_type ?? null,
            'options' => [
                'secret_key' => $ipn->secret_key ?? null
            ]
        ];

        $options = [
            'proxy' => $paymentGatewayConfig->ipnProxyName ?? null,
            'timeout' => $paymentGatewayConfig->ipnTimeout ?? 30,
        ];

        $ipnLogData = [
            'pg_merchant_id' => $ipn->pg_merchant_id,
            'company_id' => $ipn->company_id,
            'pg_order_id' => $pgOrder->id,
            'pg_transaction_id' => $pgTransaction->id,
            'pg_ipn_id' => $ipn->id,
            'proxy' => $options['proxy'] ? ProxyCURLRequest::getProxy($options['proxy']) : null,
            'request_method' => $method,
            'request_url' => $url,
            'request_header' => json_encode($headers),
            'request_body' => count($data) ? json_encode($data) : null,
        ];

        $success = false;

        try {
            /** @var Response $response */
            $response = $webhook->send(
                $method,
                $ipn->ipn_url,
                $headers,
                $data,
                $auth,
                $options
            );

            $jsonResponse = json_decode($response->getBody(), true);

            $ipnLogData['type'] = $type;
            $ipnLogData['response_status_code'] = $response->getStatusCode();
            $ipnLogData['response_body'] = $response->getBody();
            $ipnLogData['response_header'] = json_encode($response->headers());
            $ipnLogData['connected'] = 1;
            $success = true;

            if ($ipnLogData['response_status_code'] >= 200 && $ipnLogData['response_status_code'] < 300 && isset($jsonResponse['sync']) && $jsonResponse['sync']) {
                $ipnLogData['sync'] = 1;

                $syncPgOrderData = $jsonResponse['order'] ?? [];

                $filteredSyncPgOrderData = array_filter($syncPgOrderData, function ($value, $key) {
                    return in_array($key, ['order_invoice_id', 'custom_data']);
                }, ARRAY_FILTER_USE_BOTH);

                try {
                    model(PgOrderModel::class)->where('id', $pgOrder->id)->set($filteredSyncPgOrderData)->update();
                } catch (\Throwable $e) {
                    log_message('error', sprintf('[PgIpnModel->send] Error sync order: %s', $e->getMessage()));
                }
            }
        } catch (\Throwable $e) {
            log_message('error', sprintf('[PgIpnModel->send] Error sending IPN: %s', $e->getCode()));
            $ipnLogData['connected'] = 0;
            $ipnLogData['curl_error'] = $e->getMessage();
        }

        $ipnLogModel = model(PgIpnLogModel::class);
        $ipnLogId = $ipnLogModel->insert($ipnLogData);

        if (!$ipnLogId) {
            log_message('error', sprintf('[PgIpnModel->send] Failed to insert IPN log: %s', $ipnLogModel->errors()));
        }

        return $success;
    }
}
