<?php

namespace App\Models;

use CodeIgniter\Model;

class PgIpnLogModel extends Model
{
    protected $DBGroup          = 'default';
    protected $table            = 'tb_autopay_pg_ipn_log';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $insertID         = 0;
    protected $returnType       = 'object';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = ['pg_merchant_id', 'company_id', 'pg_order_id', 'pg_transaction_id', 'pg_ipn_id', 'pg_ipn_queue_id', 'type', 'proxy', 'request_method', 'request_url', 'request_header', 'request_body', 'response_body', 'response_status_code', 'response_header', 'connected', 'curl_error'];
}
