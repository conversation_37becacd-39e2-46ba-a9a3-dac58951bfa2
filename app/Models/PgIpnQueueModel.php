<?php

namespace App\Models;

use CodeIgniter\Model;

class PgIpnQueueModel extends Model
{
    protected $DBGroup          = 'default';
    protected $table            = 'tb_autopay_pg_ipn_queue';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $insertID         = 0;
    protected $returnType       = 'object';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = ['pg_merchant_id', 'company_id', 'pg_order_id', 'pg_transaction_id', 'pg_ipn_id', 'notification_type', 'status', 'last_retry_time', 'retries_count', 'sync'];
}
