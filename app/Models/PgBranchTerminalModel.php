<?php

namespace App\Models;

use CodeIgniter\Model;

class PgBranchTerminalModel extends Model
{
    protected $DBGroup          = 'default';
    protected $table            = 'tb_autopay_pg_branch_terminal';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $insertID         = 0;
    protected $returnType       = 'object';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = ['pg_merchant_id', 'company_id', 'pg_branch_id', 'name', 'code', 'active'];
}
