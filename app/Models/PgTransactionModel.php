<?php

namespace App\Models;

use CodeIgniter\Model;

class PgTransactionModel extends Model
{
    protected $DBGroup          = 'default';
    protected $table            = 'tb_autopay_pg_transaction';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $insertID         = 0;
    protected $returnType       = 'object';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = ['company_id', 'sms_parsed_id', 'pg_merchant_id', 'pg_order_id', 'pg_profile_id', 'payment_method', 'transaction_id', 'transaction_type', 'transaction_date', 'transaction_last_updated_date', 'authentication_status', 'transaction_status', 'transaction_amount', 'transaction_currency', 'card_number', 'card_holder_name', 'card_expiry', 'card_funding_method', 'card_brand'];
}
