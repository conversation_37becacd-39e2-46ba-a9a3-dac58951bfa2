<?php

namespace App\Features;

use Config\Billing;

class BillingFeature
{
    public bool $pgEnabled = false;
    
    public ?string $pgMerchantId = null;
    
    public function __construct()
    {
        /** @var Billing $config */
        $config = config(Billing::class);
        
        $this->pgEnabled = is_admin() ? true : (property_exists($config, 'pgEnabled') ? $config->pgEnabled : false);
        $this->pgMerchantId = property_exists($config, 'pgMerchantId') ? $config->pgMerchantId : false;
    }
}