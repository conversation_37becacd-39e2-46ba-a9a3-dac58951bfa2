<?php

namespace App\Features\Viber;

use App\Libraries\ViberChannelClient;
use App\Models\CounterModel;
use App\Models\NotificationViberModel;
use App\Models\NotificationViberQueueModel;
use App\Models\TransactionsModel;
use Config\ViberConfig;

class ViberFeature
{
    public bool $enabled = false;
    
    public function __construct()
    {
        helper(['general']);
        
        /** @var ViberConfig $config */
        $config = config(ViberConfig::class);
        
        $this->enabled = property_exists($config, 'enabled') ? $config->enabled || is_admin() : is_admin();
    }
    
    public function sendNotificationByTransactionId(int $transactionId): void
    {
        $notificationViberModel = model(NotificationViberModel::class);
        
        $queues = $notificationViberModel->checkAndAddQueue($transactionId);
        
        foreach ($queues as $queue) {
            $this->sendNotificationByQueue($queue);
        }
    }
    
    public function sendNotificationByQueue(array $queue)
    {
        $client = new ViberChannelClient();
        $notificationViberModel = model(NotificationViberModel::class);
        $notificationViberQueueModel = model(NotificationViberQueueModel::class);
        
        $response = $client->sendTextMessage($queue['auth_token'], $queue['sender_id'], $queue['message']);
        
        $queueStatus = 'SoftFailed';
        $messageToken = null;
        
        if (is_object($response)) {
            $queueLastLog = $response->getBody();
            $queueLastLogJson = json_decode($queueLastLog);
            $queueStatus = $response->getStatusCode() === 200 
            && property_exists($queueLastLogJson, 'status') 
            && $queueLastLogJson->status == 0 ? 'Success' : 'Failed';
        }
        
        $notificationViberQueueModel->set([
            'status' => $queueStatus,
            'last_log' => $queueLastLog,
        ])->where(['id' => $queue['id']])->update();
        
        if ($queueStatus === 'Success') {
            $transactionsModel = model(TransactionsModel::class);
            $transactionsModel
                ->set('chat_push_message', 'chat_push_message+1', false)
                ->where(['id' => $queue['transaction_id']])
                ->update();
            $counterModel = model(CounterModel::class);
            $viber = $notificationViberModel->where(['id' => $queue['notify_id']])->first();
            
            if ($viber) {
                $counterModel->chat($viber->company_id, false, 'viber');
            }
        }
    }
}