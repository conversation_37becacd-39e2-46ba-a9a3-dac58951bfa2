<?php

namespace App\Features\ShopBilling;

use App\Models\ProductModel;
use App\Models\AddonModel;
use App\Exceptions\ShopBillingFeatureException;

class OrderContext
{
    public int $shopAmount;
    
    public int $addonAmount;
    
    public string $billingCycle;
    
    public ?object $product = null;
    
    public ?object $addon = null;
    
    protected ShopBillingFeature $feature;
    
    public function __construct(ShopBillingFeature $feature, int $productId, int $shopAmount, string $billingCycle)
    {
        $this->feature = $feature;
        $this->shopAmount = $shopAmount;
        $this->addonAmount = $shopAmount - 1;
        $this->billingCycle = $billingCycle;
        
        $this->product = model(ProductModel::class)
            ->where('id', $productId)
            ->where('shop_limit > ', 0)
            ->where('active', 1)
            ->where('channel_partner_id', null)
            ->first();
        
        if (! is_object($this->product)) {
            throw new ShopBillingFeatureException('Product not found');
        }
        
        $this->addon = model(AddonModel::class)
            ->where('active', 1)
            ->where('hidden', 0)
            ->where('product_id', $this->product->id)
            ->first();
        
        if (! is_object($this->addon)) {
            throw new ShopBillingFeatureException('Addon not found');
        }
    }
    
    public function addonTotalPriceByBillingCycle(): float
    {
        return $this->addonAmount * $this->addonUnitPriceByBillingCycle();
    }
    
    public function addonUnitPriceByBillingCycle(): float
    {
        return $this->billingCycle == 'annually' ? $this->addon->price_annually * 12 : $this->addon->price_monthly;
    }
}