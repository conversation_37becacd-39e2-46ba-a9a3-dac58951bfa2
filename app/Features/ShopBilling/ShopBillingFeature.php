<?php

namespace App\Features\ShopBilling;

use App\Exceptions\ShopBillingFeatureException;
use App\Features\ShopBilling\CompanyContext;
use App\Features\ShopBilling\OrderContext;

class ShopBillingFeature
{
    public bool $enabled = false;
    
    public int $shopAmountLimit = 100;
    
    public ?int $trialDays = null;
    
    public ?CompanyContext $companyContext = null;
    
    public ?OrderContext $orderContext = null;
    
    public function __construct()
    {
        /** @var \Config\Billing $config */
        $config = config(\Config\Billing::class);
        $this->enabled = property_exists($config, 'shopBillingEnabled') ? $config->shopBillingEnabled || is_admin() : is_admin();
        $this->shopAmountLimit = property_exists($config, 'shopBillingShopAmountLimit')
            ? $config->shopBillingShopAmountLimit 
            : $this->shopAmountLimit;
        $this->trialDays = property_exists($config, 'shopBillingTrialDays')
            ? $config->shopBillingTrialDays
            : $this->trialDays;
    }
    
    public function withOrderContext(int $productId, int $shopAmount, string $billingCycle)
    {
        $this->orderContext = new OrderContext($this, $productId, $this->formatValidShopAmount($shopAmount), $this->formatValidBillingCycle($billingCycle));
        
        return $this;
    }
    
    public function withCompanyContext(int $companyId)
    {
        $this->companyContext = new CompanyContext($companyId);
        
        return $this;
    }
    
    public function determineIfOrderForShopBilling(): bool
    {
        return ! is_null($this->orderContext);
    }
    
    public function determineIfOrderIsSameSubscription()
    {
        try {
            $this->ensureWithCompanyContext();
            $this->ensureWithOrderContext();
        } catch (ShopBillingFeatureException $e) {
            return false;
        }

        if (!$this->companyContext->subscription) return false;

        return $this->companyContext->subscription->billing_cycle == $this->orderContext->billingCycle
            && $this->companyContext->subscription->plan_id == $this->orderContext->product->id
            && $this->companyContext->subscription->shop_limit == $this->orderContext->shopAmount;
    }
    
    public function determineIfOrderIsChangeShopAmount(): bool
    {
        return $this->determineIfOrderIsIncrementShopAmount()
            || $this->determineIfOrderIsDecrementShopAmount();
    }
    
    public function determineIfOrderIsIncrementShopAmount(): bool
    {
        try {
            $this->ensureWithCompanyContext();
            $this->ensureWithOrderContext();
        } catch (ShopBillingFeatureException $e) {
            return false;
        }

        if (!$this->companyContext->subscription) return false;
        
        return $this->companyContext->subscription->billing_cycle == $this->orderContext->billingCycle
            && $this->companyContext->subscription->plan_id == $this->orderContext->product->id
            && $this->companyContext->subscription->shop_limit < $this->orderContext->shopAmount;
    }
    
    public function determineIfOrderIsDecrementShopAmount(): bool
    {
        try {
            $this->ensureWithCompanyContext();
            $this->ensureWithOrderContext();
        } catch (ShopBillingFeatureException $e) {
            return false;
        }

        if (!$this->companyContext->subscription) return false;
        
        return $this->companyContext->subscription->billing_cycle == $this->orderContext->billingCycle
            && $this->companyContext->subscription->plan_id == $this->orderContext->product->id
            && $this->companyContext->subscription->shop_limit > $this->orderContext->shopAmount;
    }

    public function getChangeShopDiffCount(): int
    {
        try {
            $this->ensureWithCompanyContext();
            $this->ensureWithOrderContext();
        } catch (ShopBillingFeatureException $e) {
            return 0;
        }

        if (!$this->companyContext->subscription) return 0;
        
        return abs($this->companyContext->subscription->shop_limit - $this->orderContext->shopAmount);
    }
    
    public function ensureWithCompanyContext()
    {
        if (! $this->companyContext) {
            throw new ShopBillingFeatureException('Company context is not set');
        }
    }
    
    public function ensureWithOrderContext()
    {
        if (! $this->orderContext) {
            throw new ShopBillingFeatureException('Order context is not set');
        }
    }
    
    public function orderContext(): OrderContext
    {
        $this->ensureWithOrderContext();
        
        return $this->orderContext;
    }
    
    public function companyContext(): CompanyContext
    {
        $this->ensureWithCompanyContext();
        
        return $this->companyContext;
    }
    
    public function formatValidShopAmount($amount = ''): string
    {
        if ($amount < 0 || !is_numeric($amount)) {
            return 1;
        }
        
        if ($amount > $this->shopAmountLimit) {
            return $this->shopAmountLimit;
        }
        
        return $amount;
    }
    
    public function formatValidBillingCycle(string $billingCycle): string
    {
        return $billingCycle === 'annually' ? 'annually' : 'monthly';
    }
    
    public function determineIfSamePriceBetweenProductAndAddon(): bool
    {
        try {
            $this->ensureWithOrderContext();
            
            return $this->orderContext()->product->price_annually == $this->orderContext()->addon->price_annually 
            && $this->orderContext()->product->price_monthly == $this->orderContext()->addon->price_monthly;
        } catch (\Exception $e) {
            return false;
        }
    }
}