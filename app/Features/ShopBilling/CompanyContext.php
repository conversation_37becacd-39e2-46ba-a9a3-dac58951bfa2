<?php

namespace App\Features\ShopBilling;

use App\Exceptions\ShopBillingFeatureException;
use App\Models\ConfigurationModel;
use App\Models\CompanyModel;
use App\Models\CompanySubscriptionModel;
use App\Models\ShopModel;

class CompanyContext
{
    public ?object $company = null;
    
    public ?object $subscription = null;
    
    public function __construct(int $companyId)
    {
        $this->company = model(CompanyModel::class)
            ->where('id', $companyId)
            ->where('merchant_id', null)
            ->where('channel_partner_id', null)
            ->first();
        
        if (! is_object($this->company)) {
            throw new ShopBillingFeatureException('Company not found');
        }
        
        $this->subscription = model(CompanySubscriptionModel::class)->where('company_id', $this->company->id)->first();
    }
    
    public function canTrial(): bool
    {
        if (! $this->company->active 
        || in_array($this->company->status, ['Suspended', 'Terminated', 'Fraud', 'Cancelled']) 
        || $this->company->merchant_id
        || $this->company->channel_partner_id) {
            return false;
        }
        
        $config = model(ConfigurationModel::class)->where('company_id', $this->company->id)->where('setting', 'ShopBillingTried')->first();
        
        if ($config && $config->value == '1') {
            return false;
        }
        
        return true;
    }
    
    public function canAddShopBillingOrder(): bool
    {
        return !$this->subscription || $this->subscription->shop_limit > 0;
    }
    
    public function isShopBillingSubscription(): bool
    {
        return $this->subscription && $this->subscription->shop_limit > 0;
    }
    
    public function shopCountInUse(): int
    {
        return model(ShopModel::class)->where('company_id', $this->company->id)->countAllResults();
    }
    
    public function setTrial(): void
    {
        $config = model(ConfigurationModel::class)->where('company_id', $this->company->id)->where('setting', 'ShopBillingTried')->first();
        
        if ($config) {
            model(ConfigurationModel::class)->where('id', $config->id)->set([
                'value' => '1',
            ])->update();
        } else {
            model(ConfigurationModel::class)->insert([
                'company_id' => $this->company->id,
                'setting' => 'ShopBillingTried',
                'value' => '1',
            ]);
        }
    }
    
    public function isTrialTerminated(): bool
    {
        return $this->subscription && $this->subscription->status === 'Terminated' && $this->subscription->is_trial;
    }
}
