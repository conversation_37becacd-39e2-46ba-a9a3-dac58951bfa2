<?php

namespace App\Features\PaymentMethod\Contexts;

use App\Models\OnboardingSubmissionModel;
use App\Models\PgPaymentMethodModel;
use App\Features\PaymentMethod\PaymentMethodFeature;

class OnboardingContext
{
    public array $features = [];

    public array $onboardingStatus = [];

    protected PaymentMethodFeature $feature;

    protected int $companyId;

    public function __construct(PaymentMethodFeature $feature, int $companyId)
    {
        $this->feature = $feature;
        $this->companyId = $companyId;

        $this->loadOnboardingStatus();
        $this->loadPaymentFeatures();
    }

    protected function loadOnboardingStatus(): void
    {
        $methods = ['card', 'napas_qr'];
        $this->onboardingStatus = [];

        foreach ($methods as $method) {
            $submission = model(OnboardingSubmissionModel::class)
                ->getByCompanyAndMethod($this->companyId, $method);
            $this->onboardingStatus[$method] = $submission ? $submission->status : 'inactive';
        }
    }

    protected function loadPaymentFeatures(): void
    {
        $this->features = [
            [
                'id' => 'bank_qr',
                'payment_method' => 'bank_qr',
                'name' => 'Chuyển khoản ngân hàng qua QR',
                'description' => 'Nhận thanh toán trực tiếp vào tài khoản ngân hàng thông qua mã QR. Sử dụng ngay không cần đăng ký.',
                'icon' => 'bi-bank',
                'features' => ['Chuyển khoản trực tiếp vào TK ngân hàng', 'Không cần đăng ký phức tạp', 'Sử dụng ngay lập tức'],
                'is_default_enabled' => true,
            ],
            [
                'id' => 'napas_qr',
                'payment_method' => 'napas_qr',
                'name' => 'QR theo đơn hàng NAPAS',
                'description' => 'Thanh toán bằng QR code chuẩn quốc gia NAPAS theo từng đơn hàng cụ thể. Hỗ trợ tất cả ngân hàng trong nước.',
                'icon' => 'bi bi-qr-code',
                'features' => ['QR chuẩn quốc gia NAPAS', 'Theo dõi từng đơn hàng', 'Phí theo % giao dịch', 'Hỗ trợ tất cả ngân hàng nội địa']
            ],
            [
                'id' => 'card',
                'payment_method' => 'card',
                'name' => 'Thanh toán bằng thẻ',
                'description' => 'Chấp nhận thanh toán bằng thẻ tín dụng/ghi nợ quốc tế và trong nước. Được cung cấp bởi VPBank.',
                'icon' => 'bi-credit-card',
                'features' => ['Thẻ Visa, Mastercard, JCB', 'Thẻ ATM nội địa', 'Phí theo % giao dịch', 'Bảo mật 3D Secure']
            ],
        ];

        foreach ($this->features as &$feature) {
            if (isset($feature['is_default_enabled']) && $feature['is_default_enabled']) {
                $feature['status'] = 'available';
                $feature['onboard_completed'] = true;
                $feature['current_step'] = null;
            } else {
                $submission = model(OnboardingSubmissionModel::class)->getByCompanyAndMethod($this->companyId, $feature['payment_method']);
                $feature['status'] = $submission ? $submission->status : 'inactive';
                $feature['onboard_completed'] = $submission ? $submission->submitted_at !== null : false;
                $feature['current_step'] = $submission ? ($submission->current_step ?: 1) : null;
            }
        }
    }

    public function getFeatureStatus(string $featureId): string
    {
        foreach ($this->features as $feature) {
            if ($feature['id'] === $featureId) {
                return $feature['status'];
            }
        }

        return 'inactive';
    }

    public function isFeatureActive(string $featureId): bool
    {
        return $this->getFeatureStatus($featureId) === 'available';
    }

    public function getOnboardingStatus(string $method): string
    {
        return $this->onboardingStatus[$method] ?? 'inactive';
    }

    public function toggleFeature(string $featureId, string $action): array
    {
        $validFeatures = ['napas_qr', 'card'];

        if (! in_array($featureId, $validFeatures)) {
            return [
                'status' => false,
                'message' => 'Tính năng không hợp lệ'
            ];
        }

        if ($action === 'enable') {
            model(OnboardingSubmissionModel::class)->createOnboardingRecord($this->companyId, $featureId);

            return [
                'status' => true,
                'message' => 'Đang chuyển hướng đến quá trình đăng ký...',
                'redirect' => base_url("onboarding?feature={$featureId}&step=1"),
            ];
        } else if ($action === 'disable') {
            model(OnboardingSubmissionModel::class)->updateStatus($this->companyId, $featureId, 'rejected');

            return [
                'status' => true,
                'message' => 'Đã tạm ngưng tính năng thành công',
            ];
        }

        return [
            'status' => false,
            'message' => 'Hành động không hợp lệ',
        ];
    }

    public function togglePaymentMethod(object $merchant, string $paymentMethod, string $action): array
    {
        $existing = model(PgPaymentMethodModel::class)
            ->where([
                'pg_merchant_id' => $merchant->id,
                'payment_method' => $paymentMethod,
            ])
            ->first();

        if (! $existing) {
            return [
                'status' => false,
                'message' => 'Phương thức thanh toán không tồn tại',
            ];
        }

        if ($action === 'enable') {
            model(PgPaymentMethodModel::class)->update($existing->id, ['active' => 1]);

            return [
                'status' => true,
                'message' => 'Đã kích hoạt phương thức thanh toán',
            ];
        } else {
            model(PgPaymentMethodModel::class)->update($existing->id, ['active' => 0]);

            return [
                'status' => true,
                'message' => 'Đã tạm ngưng phương thức thanh toán',
            ];
        }
    }

    public function getAvailableFeatures(): array
    {
        return array_filter($this->features, fn($feature) => $feature['status'] === 'available');
    }

    public function getInactiveFeatures(): array
    {
        return array_filter($this->features, fn($feature) => $feature['status'] === 'inactive');
    }

    public function getPendingFeatures(): array
    {
        return array_filter($this->features, fn($feature) => in_array($feature['status'], ['pending', 'submitted']));
    }
}
