<?php

namespace App\Features\PaymentMethod\Contexts;

use Exception;
use App\Models\PgPaymentMethodModel;
use App\Models\PgProfileModel;
use App\Models\BankAccountModel;
use App\Models\BankSubAccountModel;
use App\Models\PgBranchProfileModel;
use App\Models\PgBranchModel;
use App\Models\PgBranchTerminalModel;
use App\Models\BidvEnterpriseAccountModel;
use App\Models\PgNapasVietqrProfileModel;
use App\Enums\PgPaymentMethod;
use App\Enums\PgProfileType;
use App\Features\PaymentMethod\PaymentMethodFeature;

class BankTransferContext
{
    public ?object $paymentMethod = null;

    public array $bankProfiles = [];

    public array $availableBankAccounts = [];

    protected PaymentMethodFeature $feature;

    protected object $merchant;

    public function __construct(PaymentMethodFeature $feature, object $merchant)
    {
        $this->feature = $feature;
        $this->merchant = $merchant;

        $this->loadPaymentMethod();
        $this->loadBankProfiles();
        $this->loadAvailableBankAccounts();
    }

    protected function loadPaymentMethod(): void
    {
        $this->paymentMethod = model(PgPaymentMethodModel::class)
            ->where([
                'pg_merchant_id' => $this->merchant->id,
                'payment_method' => PgPaymentMethod::BANK_TRANSFER
            ])
            ->where('company_id', $this->merchant->company_id)
            ->first();

        if (! $this->paymentMethod) {
            throw new Exception('Bank transfer payment method not found');
        }
    }

    protected function loadBankProfiles(): void
    {
        if (! $this->paymentMethod) return;

        $commonWhere = [
            'tb_autopay_pg_profile.pg_merchant_id' => $this->merchant->id,
            'tb_autopay_pg_profile.pg_payment_method_id' => $this->paymentMethod->id,
            'tb_autopay_pg_profile.active' => 1,
            'tb_autopay_pg_profile.company_id' => $this->merchant->company_id,
        ];

        $bankAccountProfiles = model(PgProfileModel::class)
            ->select('tb_autopay_pg_profile.*, tb_autopay_bank_account.account_number, tb_autopay_bank_account.account_holder_name, tb_autopay_bank_account.id as bank_account_id, tb_autopay_bank_account.bank_api_connected, bank.brand_name, bank.logo_path, bank.icon_path')
            ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id = tb_autopay_pg_profile.bank_account_id')
            ->join('tb_autopay_bank bank', 'bank.id = tb_autopay_bank_account.bank_id')
            ->where(array_merge($commonWhere, [
                'tb_autopay_pg_profile.type' => PgProfileType::BANK_ACCOUNT,
                'tb_autopay_bank_account.active' => 1,
                'tb_autopay_bank_account.bank_api_connected' => 1,
            ]))
            ->findAll();

        $napasProfiles = model(PgProfileModel::class)
            ->select('tb_autopay_pg_profile.*, tb_autopay_pg_napas_vietqr_profile.acquiring_account_holder_name as account_holder_name, tb_autopay_pg_napas_vietqr_profile.vac as account_number, "NAPAS VietQR" as brand_name, "" as logo_path, "" as icon_path')
            ->join('tb_autopay_pg_napas_vietqr_profile', 'tb_autopay_pg_napas_vietqr_profile.pg_profile_id = tb_autopay_pg_profile.id')
            ->where(array_merge($commonWhere, ['tb_autopay_pg_profile.type' => PgProfileType::NAPAS_VIETQR]))
            ->findAll();

        $this->bankProfiles = array_merge($bankAccountProfiles, $napasProfiles);
        usort($this->bankProfiles, fn($a, $b) => $a->default != $b->default ? $b->default - $a->default : $a->id - $b->id);
    }

    protected function loadAvailableBankAccounts(): void
    {
        $this->availableBankAccounts = model(BankAccountModel::class)
            ->select('tb_autopay_bank_account.*, tb_autopay_bank_account.bank_api_connected, bank.brand_name, bank.logo_path, bank.icon_path')
            ->join('tb_autopay_bank bank', 'bank.id = tb_autopay_bank_account.bank_id')
            ->where([
                'tb_autopay_bank_account.company_id' => $this->merchant->company_id,
                'tb_autopay_bank_account.active' => 1,
                'tb_autopay_bank_account.bank_api_connected' => 1,
            ])
            ->orderBy('tb_autopay_bank_account.id', 'DESC')
            ->findAll();
    }

    public function hasPaymentMethod(): bool
    {
        return $this->paymentMethod !== null;
    }

    public function getPaymentMethodId(): ?int
    {
        return $this->paymentMethod ? $this->paymentMethod->id : null;
    }

    public function getBankProfilesCount(): int
    {
        return count($this->bankProfiles);
    }

    public function getDefaultBankProfile(): ?object
    {
        foreach ($this->bankProfiles as $profile) {
            if ($profile->default) {
                return $profile;
            }
        }
        return null;
    }

    public function setDefaultBankProfile(int $profileId): bool
    {
        $profile = model(PgProfileModel::class)
            ->where([
                'id' => $profileId,
                'pg_merchant_id' => $this->merchant->id,
                'active' => 1,
                'company_id' => $this->merchant->company_id,
            ])
            ->whereIn('type', [PgProfileType::BANK_ACCOUNT, PgProfileType::NAPAS_VIETQR])
            ->first();

        if (! $profile) {
            return false;
        }

        model(PgProfileModel::class)
            ->where([
                'pg_merchant_id' => $this->merchant->id,
                'pg_payment_method_id' => $profile->pg_payment_method_id,
                'company_id' => $this->merchant->company_id,
            ])
            ->whereIn('type', [PgProfileType::BANK_ACCOUNT, PgProfileType::NAPAS_VIETQR])
            ->set(['default' => 0])
            ->update();

        return model(PgProfileModel::class)->update($profile->id, ['default' => true]);
    }

    public function getValidBankAccount(int $bankAccountId): ?object
    {
        return model(BankAccountModel::class)
            ->select(['tb_autopay_bank_account.id', 'tb_autopay_bank.brand_name'])
            ->join('tb_autopay_bank', 'tb_autopay_bank.id = tb_autopay_bank_account.bank_id')
            ->where([
                'tb_autopay_bank_account.id' => $bankAccountId,
                'tb_autopay_bank_account.company_id' => $this->merchant->company_id,
                'tb_autopay_bank_account.active' => 1,
                'tb_autopay_bank_account.bank_api_connected' => 1,
            ])
            ->first();
    }

    public function isBidvEnterprise(object $bankAccount): bool
    {
        if ($bankAccount->brand_name !== 'BIDV') {
            return false;
        }

        return !! model(BidvEnterpriseAccountModel::class)
            ->where('bank_account_id', $bankAccount->id)
            ->first();
    }

    public function validateVARequirements(string $bankName, bool $useDynamicVA, bool $supportsDynamicVA, ?int $bankSubAccountId): array
    {
        if ($useDynamicVA && ! $supportsDynamicVA) {
            return ['valid' => false, 'message' => 'Tài khoản ngân hàng này không hỗ trợ VA động theo đơn hàng'];
        }

        $banksRequireVA = ['BIDV', 'MSB', 'KienLongBank', 'OCB'];
        $requiresVA = in_array($bankName, $banksRequireVA);

        if ($requiresVA && ! $useDynamicVA && empty($bankSubAccountId)) {
            return ['valid' => false, 'message' => "Ngân hàng {$bankName} bắt buộc phải chọn tài khoản ảo (VA)"];
        }

        return ['valid' => true];
    }

    public function profileExists(int $bankAccountId): bool
    {
        return model(PgProfileModel::class)
            ->where([
                'pg_merchant_id' => $this->merchant->id,
                'pg_payment_method_id' => $this->paymentMethod->id,
                'bank_account_id' => $bankAccountId,
                'type' => PgProfileType::BANK_ACCOUNT,
                'company_id' => $this->merchant->company_id,
            ])
            ->first() !== null;
    }

    public function clearDefaultProfiles(): void
    {
        model(PgProfileModel::class)
            ->where([
                'pg_merchant_id' => $this->merchant->id,
                'pg_payment_method_id' => $this->paymentMethod->id,
                'company_id' => $this->merchant->company_id,
            ])
            ->whereIn('type', [PgProfileType::BANK_ACCOUNT, PgProfileType::NAPAS_VIETQR])
            ->set(['default' => 0])
            ->update();
    }

    public function createProfile(int $bankAccountId, bool $isDefault): int
    {
        return model(PgProfileModel::class)->insert([
            'pg_merchant_id' => $this->merchant->id,
            'pg_payment_method_id' => $this->paymentMethod->id,
            'type' => PgProfileType::BANK_ACCOUNT,
            'bank_account_id' => $bankAccountId,
            'default' => $isDefault,
            'active' => true,
            'company_id' => $this->merchant->company_id,
        ]);
    }

    public function createBranchProfile(int $profileId, int $bankAccountId, bool $useDynamicVA, ?int $bankSubAccountId, string $bankName): void
    {
        $pgBranch = model(PgBranchModel::class)
            ->where(['pg_merchant_id' => $this->merchant->id, 'company_id' => $this->merchant->company_id, 'active' => 1])
            ->first();

        $pgTerminal = model(PgBranchTerminalModel::class)
            ->where(['pg_merchant_id' => $this->merchant->id, 'company_id' => $this->merchant->company_id, 'active' => 1])
            ->first();

        if (! $pgBranch || ! $pgTerminal) return;

        $banksWithoutVA = ['TPBank', 'VPBank', 'VietinBank'];
        $forceMainAccount = in_array($bankName, $banksWithoutVA);

        $finalBankSubAccountId = null;
        if (! $forceMainAccount && ! $useDynamicVA && $bankSubAccountId) {
            $finalBankSubAccountId = $bankSubAccountId;
        }

        model(PgBranchProfileModel::class)->insert([
            'pg_merchant_id' => $this->merchant->id,
            'pg_branch_id' => $pgBranch->id,
            'pg_branch_terminal_id' => $pgTerminal->id,
            'pg_payment_method_id' => $this->paymentMethod->id,
            'pg_profile_id' => $profileId,
            'bank_account_id' => $bankAccountId,
            'bank_sub_account_id' => $finalBankSubAccountId,
            'use_dynamic_va' => $useDynamicVA ? 1 : 0,
            'company_id' => $this->merchant->company_id,
        ]);
    }

    public function getBankSubAccounts(int $bankAccountId): array
    {
        $bankSubAccounts = model(BankSubAccountModel::class)
            ->select(['id', 'sub_account', 'sub_holder_name', 'label', 'acc_type', 'active'])
            ->where([
                'bank_account_id' => $bankAccountId,
                'active' => true,
                'acc_type' => 'Real',
                'va_active' => true,
            ])
            ->findAll();

        return array_map(function ($subAccount) {
            return [
                'id' => (int) $subAccount->id,
                'account_number' => $subAccount->sub_account,
                'account_holder_name' => $subAccount->sub_holder_name ?: $subAccount->label,
                'label' => $subAccount->label,
                'acc_type' => $subAccount->acc_type
            ];
        }, $bankSubAccounts);
    }

    public function removeProfile(int $profileId): bool
    {
        $profile = model(PgProfileModel::class)
            ->where([
                'id' => $profileId,
                'pg_merchant_id' => $this->merchant->id,
                'company_id' => $this->merchant->company_id,
            ])
            ->whereIn('type', [PgProfileType::BANK_ACCOUNT, PgProfileType::NAPAS_VIETQR])
            ->first();

        if (! $profile) {
            return false;
        }

        $profileCount = model(PgProfileModel::class)
            ->where([
                'pg_merchant_id' => $this->merchant->id,
                'pg_payment_method_id' => $profile->pg_payment_method_id,
                'active' => 1,
            ])
            ->whereIn('type', [PgProfileType::BANK_ACCOUNT, PgProfileType::NAPAS_VIETQR])
            ->countAllResults();

        if ($profileCount <= 1) {
            throw new Exception('Không thể xóa tài khoản thụ hưởng cuối cùng. Phải có ít nhất một tài khoản thụ hưởng.');
        }

        if ($profile->default) {
            throw new Exception('Không thể xóa tài khoản thụ hưởng mặc định. Vui lòng chọn tài khoản thụ hưởng khác làm mặc định trước khi xóa.');
        }

        if ($profile->type === PgProfileType::NAPAS_VIETQR) {
            $napasRecord = model(PgNapasVietqrProfileModel::class)
                ->where('pg_profile_id', $profile->id)
                ->first();

            if ($napasRecord) {
                throw new Exception('Không thể xóa profile NAPAS VietQR đang có dữ liệu liên kết. Vui lòng liên hệ quản trị viên.');
            }
        }

        return model(PgProfileModel::class)->delete($profile->id);
    }

    public function getAllVAProfiles(): array
    {
        $branchProfiles = model(PgBranchProfileModel::class)
            ->select([
                'tb_autopay_pg_branch_profile.pg_profile_id',
                'tb_autopay_pg_branch_profile.use_dynamic_va',
                'tb_autopay_bank_sub_account.id',
                'tb_autopay_bank_sub_account.sub_account as account_number',
                'tb_autopay_bank_sub_account.sub_holder_name as account_holder_name'
            ])
            ->join('tb_autopay_bank_sub_account', 'tb_autopay_bank_sub_account.id = tb_autopay_pg_branch_profile.bank_sub_account_id', 'left')
            ->where([
                'tb_autopay_pg_branch_profile.pg_merchant_id' => $this->merchant->id,
                'tb_autopay_pg_branch_profile.company_id' => $this->merchant->company_id,
            ])
            ->findAll();

        $result = [];

        foreach ($branchProfiles as $branchProfile) {
            $result[$branchProfile->pg_profile_id] = [
                'id' => $branchProfile->id,
                'use_dynamic_va' => (bool) $branchProfile->use_dynamic_va,
                'account_number' => $branchProfile->account_number,
                'account_holder_name' => $branchProfile->account_holder_name,
            ];
        }

        return $result;
    }

    public function saveVAProfile(int $profileId, bool $useDynamicVA, ?int $bankSubAccountId): bool
    {
        $profile = model(PgProfileModel::class)
            ->where([
                'id' => $profileId,
                'pg_merchant_id' => $this->merchant->id,
                'company_id' => $this->merchant->company_id,
                'type' => PgProfileType::BANK_ACCOUNT,
                'active' => 1,
            ])
            ->first();

        if (! $profile) {
            throw new Exception('Profile không tồn tại hoặc không thuộc về merchant này');
        }

        $pgBranch = model(PgBranchModel::class)
            ->where(['pg_merchant_id' => $this->merchant->id, 'company_id' => $this->merchant->company_id, 'active' => 1])
            ->first();

        $pgTerminal = model(PgBranchTerminalModel::class)
            ->where(['pg_merchant_id' => $this->merchant->id, 'company_id' => $this->merchant->company_id, 'active' => 1])
            ->first();

        if (! $pgBranch || ! $pgTerminal) {
            throw new Exception('Branch hoặc Terminal không tồn tại');
        }

        $branchProfile = model(PgBranchProfileModel::class)
            ->where([
                'pg_profile_id' => $profileId,
                'pg_merchant_id' => $this->merchant->id,
                'company_id' => $this->merchant->company_id,
            ])
            ->first();

        $data = [
            'use_dynamic_va' => $useDynamicVA ? 1 : 0,
            'bank_sub_account_id' => $useDynamicVA ? null : $bankSubAccountId,
        ];

        if ($branchProfile) {
            return model(PgBranchProfileModel::class)->update($branchProfile->id, $data);
        } else {
            $data = array_merge($data, [
                'pg_merchant_id' => $this->merchant->id,
                'pg_branch_id' => $pgBranch->id,
                'pg_branch_terminal_id' => $pgTerminal->id,
                'pg_payment_method_id' => $profile->pg_payment_method_id,
                'pg_profile_id' => $profileId,
                'bank_account_id' => $profile->bank_account_id,
                'company_id' => $this->merchant->company_id,
            ]);

            return model(PgBranchProfileModel::class)->insert($data) > 0;
        }
    }
}
