<?php

namespace App\Features\PaymentMethod\Contexts;

use Exception;
use App\Models\PgMerchantModel;
use App\Models\PgPaymentMethodModel;
use App\Models\BankAccountModel;
use App\Features\PaymentMethod\PaymentMethodFeature;

class MerchantContext
{
    public ?object $merchant = null;

    public array $paymentMethods = [];

    public array $bankAccounts = [];

    public array $paymentMethodExistence = [];

    protected PaymentMethodFeature $feature;

    protected int $companyId;

    public function __construct(PaymentMethodFeature $feature, int $companyId)
    {
        $this->feature = $feature;
        $this->companyId = $companyId;

        $this->loadMerchant();
        $this->loadPaymentMethods();
        $this->loadBankAccounts();
        $this->loadPaymentMethodExistence();
    }

    protected function loadMerchant(): void
    {
        $this->merchant = model(PgMerchantModel::class)
            ->where('company_id', $this->companyId)
            ->first();

        if (! $this->merchant) {
            throw new Exception('Merchant not found for company');
        }
    }

    protected function loadPaymentMethods(): void
    {
        if (! $this->merchant) {
            return;
        }

        $this->paymentMethods = model(PgPaymentMethodModel::class)
            ->where('pg_merchant_id', $this->merchant->id)
            ->findAll();
    }

    protected function loadBankAccounts(): void
    {
        $this->bankAccounts = model(BankAccountModel::class)
            ->select('tb_autopay_bank_account.*, tb_autopay_bank_account.bank_api_connected, bank.brand_name, bank.logo_path, bank.icon_path')
            ->join('tb_autopay_bank bank', 'bank.id = tb_autopay_bank_account.bank_id')
            ->where([
                'tb_autopay_bank_account.company_id' => $this->companyId,
                'tb_autopay_bank_account.active' => 1,
                'tb_autopay_bank_account.bank_api_connected' => 1,
            ])
            ->orderBy('tb_autopay_bank_account.id', 'DESC')
            ->findAll();
    }

    protected function loadPaymentMethodExistence(): void
    {
        if (! $this->merchant) {
            return;
        }

        $paymentMethods = model(PgPaymentMethodModel::class)
            ->where('pg_merchant_id', $this->merchant->id)
            ->where('company_id', $this->companyId)
            ->findAll();

        $this->paymentMethodExistence = [];

        foreach ($paymentMethods as $method) {
            $this->paymentMethodExistence[$method->payment_method] = true;
        }
    }

    public function hasMerchant(): bool
    {
        return $this->merchant !== null;
    }

    public function getMerchantId(): ?int
    {
        return $this->merchant ? $this->merchant->id : null;
    }

    public function hasPaymentMethod(string $paymentMethod): bool
    {
        return isset($this->paymentMethodExistence[$paymentMethod]);
    }

    public function getPaymentMethodsCount(): int
    {
        return count($this->paymentMethods);
    }

    public function getBankAccountsCount(): int
    {
        return count($this->bankAccounts);
    }

    public function getActiveBankAccounts(): array
    {
        return array_filter($this->bankAccounts, fn($account) => $account->active == 1);
    }
}
