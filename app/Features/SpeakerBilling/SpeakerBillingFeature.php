<?php

namespace App\Features\SpeakerBilling;

use App\Features\SpeakerBilling\Contexts\CompanyContext;
use App\Features\SpeakerBilling\Contexts\OrderContext;
use App\Exceptions\SpeakerBillingFeatureException;
use App\Features\SpeakerBilling\Contexts\VaContext;
use Exception;

class SpeakerBillingFeature
{
    public bool $enabled = false;

    public ?int $defaultProductId = null;
    
    public ?int $vavLength = null;
    
    public ?int $prefixVav = null;
    
    protected ?CompanyContext $companyContext = null;
    
    protected ?OrderContext $orderContext = null;
    
    protected ?VaContext $vaContext = null;
    
    public function __construct()
    {
        $billingConfig = config(\Config\Billing::class);
        
        $this->enabled = property_exists($billingConfig, 'speakerBillingEnabled') 
            ? is_admin() || $billingConfig->speakerBillingEnabled 
            : false;
        $this->defaultProductId = property_exists($billingConfig, 'speakerBillingDefaultProductId') 
            ? $billingConfig->speakerBillingDefaultProductId 
            : null;
        $this->vavLength = property_exists($billingConfig, 'speakerBillingVavLength') 
            ? $billingConfig->speakerBillingVavLength 
            : 5;
        $this->prefixVav = property_exists($billingConfig, 'speakerBillingPrefixVav') 
            ? $billingConfig->speakerBillingPrefixVav 
            : '9';
    }
    
    public function withCompanyContext(int $companyId)
    {
        try {
            $this->companyContext = new CompanyContext($this, $companyId);
        } catch (Exception $e) {
            log_message('error', 'Failed to create CompanyContext: ' . $e->getMessage());
            $this->companyContext = null;
        }
        
        return $this;
    }
    
    public function ensureWithCompanyContext()
    {
        if (! $this->companyContext) {
            throw new SpeakerBillingFeatureException('Company context is not set');
        }
    }
    
    public function ensureWithOrderContext()
    {
        if (! $this->orderContext) {
            throw new SpeakerBillingFeatureException('Order context is not set');
        }
    }
    
    public function withOrderContext(int $productId = null)
    {
        try {
            $this->orderContext = new OrderContext($this, $productId ?? $this->defaultProductId);
        } catch (Exception $e) {
            $this->orderContext = null;
        }
        
        return $this;
    }
    
    public function orderContext(): OrderContext
    {
        $this->ensureWithOrderContext();
        
        return $this->orderContext;
    }
    
    public function companyContext(): CompanyContext
    {
        $this->ensureWithCompanyContext();
        
        return $this->companyContext;
    }
    
    public function withVaContext(): SpeakerBillingFeature
    {
        try {
            $this->vaContext = new VaContext($this);
        } catch (Exception $e) {
            $this->vaContext = null;
        }
        
        return $this;
    }
    
    public function vaContext(): VaContext
    {
        $this->ensureWithVaContext();
        
        return $this->vaContext;
    }
    
    public function ensureWithVaContext(): void
    {
        if (! $this->vaContext) {
            throw new SpeakerBillingFeatureException('VA context is not set');
        }
    }
}