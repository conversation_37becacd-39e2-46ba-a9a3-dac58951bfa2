<?php

namespace App\Features\SpeakerBilling\Contexts;

use App\Models\CompanyModel;
use App\Models\ConfigurationModel;
use App\Models\CompanySubscriptionModel;
use App\Exceptions\ShopBillingFeatureException;
use App\Exceptions\SpeakerBillingFeatureException;
use App\Features\SpeakerBilling\SpeakerBillingFeature;

class CompanyContext
{
    public ?object $company = null;
    
    public ?object $subscription = null;
    
    protected SpeakerBillingFeature $feature;
    
    public function __construct(SpeakerBillingFeature $feature, int $companyId)
    {
        $this->feature = $feature;
        
        $this->company = model(CompanyModel::class)
            ->where('id', $companyId)
            ->where('merchant_id', null)
            ->first();
        
        if (! is_object($this->company)) {
            throw new ShopBillingFeatureException('Company not found');
        }
        
        $this->subscription = model(CompanySubscriptionModel::class)->where('company_id', $this->company->id)->first();
    }
    
    public function canOrderSpeakerBillingProduct(): bool
    {
        try {
            $this->feature->ensureWithCompanyContext();
        } catch (SpeakerBillingFeatureException $e) {
            return false;
        }
        
        if (! $this->company->active 
        || in_array($this->company->status, ['Suspended', 'Terminated', 'Fraud', 'Cancelled']) 
        || $this->company->merchant_id
        || $this->company->channel_partner_id) {
            return false;
        }
        
        return is_null($this->subscription) || can_switch_plan_from_speaker_billing();
    }
    
    public function isSubscribedSpeakerBillingProduct(): bool
    {
        try {
            $this->feature->ensureWithCompanyContext();
        } catch (SpeakerBillingFeatureException $e) {
            return false;
        }
        
        return is_object($this->subscription) && $this->subscription->plan_id == $this->feature->defaultProductId;
    }
}