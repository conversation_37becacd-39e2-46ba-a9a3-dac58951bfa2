<?php

namespace App\Features\SpeakerBilling\Contexts;

use App\Features\SpeakerBilling\SpeakerBillingFeature;

class VaContext
{
    protected SpeakerBillingFeature $feature;
    
    public function __construct(SpeakerBillingFeature $feature)
    {
        $this->feature = $feature;
    }
    
    public function generateVAV(string $order): string
    {
        $vav = str_pad($order, $this->feature->vavLength, '0', STR_PAD_LEFT);
        
        $vav = preg_replace('/[34]/', '5', $vav);
        
        return $vav;
    }
}