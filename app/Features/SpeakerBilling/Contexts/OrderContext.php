<?php

namespace App\Features\SpeakerBilling\Contexts;

use App\Features\SpeakerBilling\SpeakerBillingFeature;
use App\Exceptions\SpeakerBillingFeatureException;
use App\Models\ConfigurationModel;
use App\Models\CompanyModel;
use App\Models\CompanySubscriptionModel;
use App\Models\InvoiceModel;
use App\Models\ProductModel;
use App\Models\OrderModel;

class OrderContext
{
    public ?object $product = null;

    protected SpeakerBillingFeature $feature;

    public function __construct(SpeakerBillingFeature $feature, int $productId)
    {
        $this->feature = $feature;

        $this->product = model(ProductModel::class)
            ->where('id', $productId)
            ->where('active', 1)
            ->where('channel_partner_id', null)
            ->where('billing_type', 'Free')
            ->first();

        if (! is_object($this->product)) {
            throw new SpeakerBillingFeatureException('Product not found');
        }
    }

    public function checkout()
    {
        $this->feature->ensureWithCompanyContext();
        $this->feature->ensureWithOrderContext();

        if (! $this->feature->companyContext()->canOrderSpeakerBillingProduct()) {
            throw new SpeakerBillingFeatureException('Company cannot initialize the speaker billing subscription');
        }

        $orderModel = model(OrderModel::class);
        $company = $this->feature->companyContext()->company;

        $orderId = $orderModel->insert([
            'company_id' => $company->id,
            'invoice_id' => 0,
            'total' => 0,
            'status' => 'Active',
            'order_ip' => service('request')->getIPAddress()
        ]);

        if (! $orderId) {
            throw new SpeakerBillingFeatureException('Order not created');
        }

        $currentSubscription = $this->getCurrentSubscription();

        if ($currentSubscription) {
            $hasUnpaidInvoice = model(InvoiceModel::class)
                ->where('company_id', $this->feature->companyContext()->company->id)
                ->where('status', 'Unpaid')
                ->countAllResults();

            if ($hasUnpaidInvoice > 0) {
                throw new SpeakerBillingFeatureException('Company has unpaid invoices, please pay before changing the subscription');
            }
        }

        $data = [
            'company_id' => $company->id,
            'order_id' => $orderId,
            'plan_id' => $this->product->id,
            'begin_date' => date('Y-m-d'),
            'end_date' => null,
            'billing_cycle' => 'free',
            'status' => 'Active',
            'auto_renew' => 0,
            'monthly_transaction_limit' => 0,
        ];

        if ($currentSubscription) {
            $subscriptionId = $currentSubscription->id;
            model(CompanySubscriptionModel::class)->update($subscriptionId, $data);
        } else {
            $subscriptionId = model(CompanySubscriptionModel::class)->insert($data);
        }

        if (! $subscriptionId) {
            $orderModel->delete($orderId);

            throw new SpeakerBillingFeatureException('Subscription not created');
        }

        model(CompanyModel::class)->where('id', $company->id)->set(['status' => 'Active'])->update();

        $configurationModel = model(ConfigurationModel::class);
        $configurationModel->set([
            'value' => 'on',
        ])->where([
            'company_id' => $company->id,
            'setting' => 'BankSubAccount'
        ])->update();

        return $subscriptionId;
    }

    protected function getCurrentSubscription()
    {
        return model(CompanySubscriptionModel::class)
            ->select('tb_autopay_company_subscription.id')
            ->join('tb_autopay_product', 'tb_autopay_product.id=tb_autopay_company_subscription.plan_id')
            ->join('tb_autopay_order', 'tb_autopay_order.id=tb_autopay_company_subscription.order_id')
            ->where('tb_autopay_company_subscription.company_id', $this->feature->companyContext()->company->id)
            ->first();
    }
}
