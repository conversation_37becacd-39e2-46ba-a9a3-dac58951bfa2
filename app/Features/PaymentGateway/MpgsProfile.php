<?php

namespace App\Features\PaymentGateway;

use Exception;
use App\Models\CompanyModel;
use App\Models\PgOrderModel;
use App\Models\PgProfileModel;
use App\Models\PgWebhookModel;
use App\Models\PgCustomerModel;
use App\Libraries\RabbitMQClient;
use App\Models\PgTransactionModel;
use PhpAmqpLib\Message\AMQPMessage;
use App\Libraries\MPGSRestApiClient;
use App\Models\CompanySubscriptionModel;
use App\Exceptions\PaymentGatewayException;
use CodeIgniter\Database\RawSql;

class MpgsProfile extends PaymentGatewayProfile
{
    protected string $merchantId;
    protected string $apiUsername;
    protected string $apiPassword;
    protected ?string $sessionId = null;
    
    public function __construct(int $profileId)
    {
        parent::__construct($profileId);
        
        if ($this->profile->type !== 'MPGS') {
            throw new PaymentGatewayException('Profile must be MPGS');
        }
        
        $mpgsProfile = model(PgProfileModel::class)->getMpgsProfile($this->profile->id);
        
        if (!$mpgsProfile) {
            throw new PaymentGatewayException('MPGS profile not found');
        }
        
        $this->merchantId = $mpgsProfile->merchant_id;
        $this->apiUsername = $mpgsProfile->api_username;
        $this->apiPassword = $mpgsProfile->api_password;
    }
    
    public function retrieveOrder(string $orderId): ?object
    {
        $client = $this->makeRestApiClient();
        
        try {
            $response = $client->retrieveOrder($orderId);
            
            if ($response->getStatusCode() != 200) {
                throw new PaymentGatewayException('Order is not found');
            }
            
            return json_decode($response->getBody());
        } catch (PaymentGatewayException $e) {
            $e->forceLogging();
            
            return null;
        }
    }
    
    /**
     * @return object{
        merchant: string,
        transaction: {
            id: string,
            amount: float,
            type: string
        }
     }
     */
    public function retrieveTransaction(string $orderId, string $transactionId): object
    {
        $client = $this->makeRestApiClient();
        
        $response = $client->retrieveTransaction($orderId, $transactionId);
        
        $responseJson = json_decode($response->getBody());
        
        return $responseJson;
    }
    
    public function setSessionId(string $sessionId): void
    {
        $this->sessionId = $sessionId;
    }
    
    public function standardizationCardBrand(?string $value): ?string
    {
        if (!$value) return null;
        
        if ($value === 'MASTERCARD') {
            return 'Mastercard';
        } else if ($value === 'VISA') {
            return 'Visa';
        } else if ($value === 'JCB') {
            return 'Jcb';
        } else if ($value === 'UNKNOWN') {
            return 'Unknown';
        }
        
        throw new PaymentGatewayException('Invalid card brand');
    }
    
    public function standardizationCardExpiry(?string $value): ?string
    {
        if (!$value) return null;
        
        return $value;
    }
    
    public function standardizationCardFundingMethod(?string $value): ?string
    {
        if (!$value) return null;
        
        if ($value === 'CHARGE') {
            return 'Charge';
        } else if ($value === 'CREDIT') {
            return 'Credit';
        } else if ($value === 'DEBIT') {
            return 'Debit';
        } else if ($value === 'UNKNOWN') {
            return 'Unknown';
        }
        
        throw new PaymentGatewayException('Invalid card funding method');
    }
    
    public function makeRestApiClient(): MPGSRestApiClient
    {
        return new MPGSRestApiClient(
            $this->merchantId,
            $this->apiUsername,
            $this->apiPassword
        );
    }
    
    /**
     * @param object $drTransaction: 
     */
    public function isNewDRApprovedPaymentTransaction(string $pgOrderId, object $drTransaction): bool
    {
        $pgTransactionModel = model(PgTransactionModel::class);
        
        $pgTransaction = $pgTransactionModel
            ->where('company_id', $this->profile->company_id)
            ->where('pg_merchant_id', $this->profile->pg_merchant_id)
            ->where('pg_order_id', $pgOrderId)
            ->where('transaction_id', $drTransaction->transaction->id)
            ->where('transaction_type', 'PAYMENT')
            ->where('transaction_status', 'APPROVED')
            ->first();
            
        if ($pgTransaction && !$pgTransaction->sms_parsed_id) {
            return true;
        }
        
        return $drTransaction->transaction->type == 'PAYMENT' && in_array($drTransaction->response->gatewayCode, ['APPROVED', 'APPROVED_AUTO']);
    }
    
    public function isNewDRVoidPaymentTransaction(string $pgOrderId, object $transaction): bool
    {
        $pgTransactionModel = model(PgTransactionModel::class);
        
        $pgTransaction = $pgTransactionModel
            ->where('company_id', $this->profile->company_id)
            ->where('pg_merchant_id', $this->profile->pg_merchant_id)
            ->where('pg_order_id', $pgOrderId)
            ->where('transaction_id', $transaction->transaction->id)
            ->where('transaction_type', 'VOID_PAYMENT')
            ->where('transaction_status', 'APPROVED')
            ->first();
            
        if ($pgTransaction && !$pgTransaction->sms_parsed_id) {
            return true;
        }
        
        return $transaction->transaction->type == 'VOID_PAYMENT' && in_array($transaction->response->gatewayCode, ['APPROVED', 'APPROVED_AUTO']);
    }
    
    public function syncDROrder(object $order, array $additionalData = []): int
    {
        helper('general');
        
        assert(property_exists($order, 'id'), 'DR order is required');
        
        $pgOrderModel = model(PgOrderModel::class);
        $pgTransactionModel = model(PgTransactionModel::class);
        $pgCustomerId = null;
        
        $pgOrderBuilder = $pgOrderModel
            ->where('company_id', $this->profile->company_id)
            ->where('pg_merchant_id', $this->profile->pg_merchant_id)
            ->where('order_id', $order->id);
        
        if (isset($additionalData['customer_id']) && $customerId = get_nested($order, 'customer.account.id', $additionalData['customer_id'])) {
            $pgCustomerModel = model(PgCustomerModel::class);
            
            $pgCustomer = $pgCustomerModel
                ->where('company_id', $this->profile->company_id)
                ->where('pg_merchant_id', $this->profile->pg_merchant_id)
                ->where('customer_id', $customerId)
                ->first();
                
            if (!$pgCustomer) {
                $pgCustomerId = $pgCustomerModel->insert([
                    'company_id' => $this->profile->company_id,
                    'pg_merchant_id' => $this->profile->pg_merchant_id,
                    'customer_id' => $customerId,
                ]);
            } else {
                $pgCustomerId = $pgCustomer->id;
            }
            
            $pgOrderBuilder->where('pg_customer_id', $pgCustomerId);
        }
        
        $pgOrder = $pgOrderBuilder->first();
            
        $safePgOrderData = [
            'company_id' => $this->profile->company_id,
            'pg_profile_id' => $this->profile->id,
            'pg_customer_id' => $pgCustomerId,
            'pg_merchant_id' => $this->profile->pg_merchant_id,
            'pg_agreement_id' => $additionalData['pg_agreement_id'] ?? null,
            'order_id' => $order->id,
            'order_status' =>  $order->status,
            'order_currency' =>  $order->currency,
            'authentication_status' => $order->authenticationStatus ?? $pgOrder->authentication_status ?? null,
            'order_amount' => $order->amount,
            'order_description' => $additionalData['order_description'] ?? $pgOrder->order_description ?? null,
            'order_invoice_number' => $order->invoiceNumber ?? $additionalData['order_invoice_number'] ?? $pgOrder->order_invoice_number ?? null,
            'order_discount_amount' => $order->discount->amount ?? $pgOrder->order_discount_amount ?? null,
            'order_discount_description' => $additionalData['order_discount_description'] ?? $pgOrder->order_discount_description ?? null,
            'order_discount_code' => $order->discount->code ?? $pgOrder->order_discount_code ?? null,
            'order_tax_amount' => $order->taxAmount ?? $pgOrder->order_tax_amount ?? null,
            'custom_data' => $additionalData['custom_data'] ?? $pgOrder->custom_data ?? null,
            'user_agent' => $additionalData['user_agent'] ?? $pgOrder->user_agent ?? null,
            'ip_address' => $additionalData['ip_address'] ?? $pgOrder->ip_address ?? null,
        ];

        if (!$pgOrder) {
            $pgOrderId = $pgOrderModel->insert($safePgOrderData);
        } else {
            $pgOrderId = $pgOrder->id;
            $pgOrderModel->where('id', $pgOrderId)->set($safePgOrderData)->update();
        }
        
        foreach ($order->transaction ?? [] as $transaction) {
            if (property_exists($transaction, 'authentication')
            && property_exists($transaction->authentication, '3ds2')
            && property_exists($transaction->authentication->{'3ds2'}, 'methodCompleted')
            && !$transaction->authentication->{'3ds2'}->methodCompleted) continue;
            
            $safePgTransactionData = [
                'company_id' => $this->profile->company_id,
                'pg_merchant_id' => $this->profile->pg_merchant_id,
                'pg_order_id' => $pgOrderId,
                'pg_profile_id' => $this->profile->id,
                'payment_method' => 'CARD',
                'transaction_id' => $transaction->transaction->id,
                'transaction_type' => $transaction->transaction->type,
                'transaction_date' => (new \DateTime($transaction->timeOfRecord))->setTimezone(new \DateTimeZone('Asia/Ho_Chi_Minh'))->format('Y-m-d H:i:s'),
                'transaction_last_updated_date' => (new \DateTime($transaction->timeOfLastUpdate))->setTimezone(new \DateTimeZone('Asia/Ho_Chi_Minh'))->format('Y-m-d H:i:s'),
                'transaction_status' => $transaction->response->gatewayCode,
                'authentication_status' => $transaction->transaction->authenticationStatus ?? null,
                'transaction_amount' => $transaction->transaction->amount,
                'transaction_currency' => $transaction->transaction->currency,
            ];
            
            if ($transaction->sourceOfFunds->type == 'CARD') {
                $safePgTransactionData = array_merge($safePgTransactionData, [
                    'card_number' => $transaction->sourceOfFunds->provided->card->number,
                    'card_holder_name' => $transaction->sourceOfFunds->provided->card->nameOnCard ?? null,
                    'card_expiry' => $transaction->sourceOfFunds->provided->card->expiry->month . $transaction->sourceOfFunds->provided->card->expiry->year,
                    'card_funding_method' => $transaction->sourceOfFunds->provided->card->fundingMethod,
                    'card_brand' => $transaction->sourceOfFunds->provided->card->brand
                ]);
            }
            
            $pgTransaction = $pgTransactionModel
                ->where('company_id', $this->profile->company_id)
                ->where('pg_merchant_id', $this->profile->pg_merchant_id)
                ->where('pg_profile_id', $this->profile->id)
                ->where('pg_order_id', $pgOrderId)
                ->where('transaction_id', $transaction->transaction->id)
                ->where('payment_method', 'CARD')
                ->where('transaction_type', $transaction->transaction->type)
                ->first();
                
            if (!$pgTransaction) {
                $pgTransaction = $pgTransactionModel->insert($safePgTransactionData);
            } else {
                $pgTransactionModel->where('id', $pgTransaction->id)->set($safePgTransactionData)->update();
            }
        }
        
        return $pgOrderId;
    }
    
    public function determineIfDROrderCaptured(object $order): bool
    {
        $paymentTransactions = array_values(array_filter(
            $order->transaction ?? [], 
            fn ($transaction) => $transaction->transaction->type == 'PAYMENT' && in_array($transaction->response->gatewayCode, ['APPROVED', 'APPROVED_AUTO'])
        ));
        
        $totalTransactionAmount = array_reduce(
            $paymentTransactions,
            fn ($sum, $transaction) => $sum + $transaction->transaction->amount,
            0
        );
        
        return count($paymentTransactions)
        && $order->totalAuthorizedAmount === $order->totalCapturedAmount
        && $order->totalCapturedAmount > 0
        && $totalTransactionAmount >= $order->totalCapturedAmount;
    }
    
    public function determineIfDROrderVerified(object $order): bool
    {
        return $order->status === 'VERIFIED';
    }
    
    public function determineIfDROrderCancelledByVoidTransaction(object $order): bool
    {
        return $order->status === 'CANCELLED' 
        && count(array_filter(
            $order->transaction ?? [], 
            fn ($transaction) => $transaction->transaction->type == 'PAYMENT' && in_array($transaction->response->gatewayCode, ['APPROVED', 'APPROVED_AUTO'])
        ))
        && count(array_filter(
            $order->transaction ?? [], 
            fn ($transaction) => $transaction->transaction->type == 'VOID_PAYMENT' && in_array($transaction->response->gatewayCode, ['APPROVED', 'APPROVED_AUTO'])
        ));
    }
    
    public function queryDROrder(string $orderId): ?object
    {
        $client = $this->makeRestApiClient();
        
        $response = $client->retrieveOrder($orderId);
        
        if ($response->getStatusCode() != 200) {
            return null;
        }
        
        $orderDetails = json_decode($response->getBody());
        
        return $orderDetails;
    }
    
    public function queryDRSession(string $sessionId): ?object
    {
        $client = $this->makeRestApiClient();
        
        $response = $client->retrieveSession($sessionId);
        
        if ($response->getStatusCode() != 200) {
            return null;
        }
        
        $sessionDetails = json_decode($response->getBody());
        
        return $sessionDetails;
    }

    public function pushIpn(string $notificationType, int $pgOrderId, int $pgTransactionId): void
    {
        helper(['general']);
        
        $pgOrder = model(PgOrderModel::class)
            ->where('id', $pgOrderId)
            ->where('company_id', $this->profile->company_id)
            ->where('pg_merchant_id', $this->profile->pg_merchant_id)
            ->first();
            
        if (!$pgOrder) {
            throw new Exception('[MPGS_PROFILE] PG order not found');
        }
        
        $pgTransactionModel = model(PgTransactionModel::class);
        $pgTransaction = $pgTransactionModel
            ->where('company_id', $this->profile->company_id)
            ->where('pg_merchant_id', $this->profile->pg_merchant_id)
            ->where('pg_order_id', $pgOrder->id)
            ->where('id', $pgTransactionId)
            ->first();

        if (!$pgTransaction) {
            throw new Exception('[MPGS_PROFILE] PG transaction not found');
        }

        $rabbitmq = new RabbitMQClient();

        if ($rabbitmq->connect()) {
            $msg = new AMQPMessage(
                json_encode([
                    'pg_merchant_id' => $this->profile->pg_merchant_id,
                    'pg_order_id' => $pgOrder->id,
                    'pg_transaction_id' => $pgTransaction->id,
                    'notification_type' => $notificationType,
                ]),
                ['delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT]
            );

            try {
                $rabbitmq->queueDeclare('pg_ipn');
                $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('pg_ipn', 'name'));
            } catch (\Throwable $e) {
                log_message('error', sprintf('[MPGS_PROFILE] Failed to publish the message of pg_ipn worker: %s | Trace: %s', $e->getMessage(), $e->getTraceAsString()));
            }
        }
    }

    public function pushWebhookEvent(string $eventType, array $data = []): void
    {
        helper(['general']);
        
        $pgWebhookModel = model(PgWebhookModel::class);
        $pgWebhook = $pgWebhookModel
            ->where('company_id', $this->profile->company_id)
            ->where('pg_merchant_id', $this->profile->pg_merchant_id)
            ->where('active', 1)
            ->groupStart()
                ->where(new RawSql("JSON_CONTAINS(allowed_event_types, JSON_QUOTE(" . db_connect()->escape($eventType) . "))"))
                ->orWhere('allowed_event_types', '*')
            ->groupEnd()
            ->first();
        
        if (!$pgWebhook) {
            throw new Exception('[MPGS_PROFILE] PG webhook not found: ' . (string) db_connect()->getLastQuery());
        }
        
        $rabbitmq = new RabbitMQClient();

        if ($rabbitmq->connect()) {
            $msg = new AMQPMessage(
                json_encode([
                    'pg_webhook_id' => $pgWebhook->id,
                    'event_type' => $eventType,
                    'data' => $data,
                ]),
                ['delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT]
            );

            try {
                $rabbitmq->queueDeclare('pg_webhook');
                $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('pg_webhook', 'name'));
            } catch (\Throwable $e) {
                log_message('error', sprintf('[MPGS_PROFILE] Failed to publish the message of pg_webhook worker: %s | Trace: %s', $e->getMessage(), $e->getTraceAsString()));
            }
        }
    }

    public function validatePaymentFields(array $fields): void
    {
        //
    }
}
