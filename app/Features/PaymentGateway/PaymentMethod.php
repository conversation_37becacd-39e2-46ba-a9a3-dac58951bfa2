<?php

namespace App\Features\PaymentGateway;

use App\Models\PgProfileModel;
use App\Models\PgMerchantModel;
use App\Models\PgPaymentMethodModel;
use App\Exceptions\PaymentGatewayException;
use App\Features\PaymentGateway\NapasVietQrProfile;
use Exception;

class PaymentMethod
{
    public object $model;
    
    public object $merchant;
    
    public function __construct(object $model)
    {
        $this->model = $model;
        
        $this->merchant = model(PgMerchantModel::class)->where('id', $this->model->pg_merchant_id)->first();
        
        if (!$this->merchant) {
            throw new PaymentGatewayFeature('Merchant ID not found');
        }
    }
    
    public static function makeFromCheckout(int $merchantId, string $paymentMethod)
    {
        $model = model(PgPaymentMethodModel::class)
            ->where('pg_merchant_id', $merchantId)
            ->where('payment_method', $paymentMethod)
            ->first();
            
        if (!$model) {
            throw new PaymentGatewayFeature('Payment method ID not found');
        }
         
        return new static($model);
    }
    
    public function findAvailableProfile(?string $profileClass = null): ?PaymentGatewayProfile
    {
        $profileModel = model(PgProfileModel::class);
        $profile = $profileModel
            ->where('company_id', $this->model->company_id)
            ->where('pg_merchant_id', $this->model->pg_merchant_id)
            ->where('pg_payment_method_id', $this->model->id)
            ->where('default', 1)
            ->where('active', 1)
            ->first();

        if (!$profile) {
            return null;
        }
        
        return $this->resolveProfile($profile, $profileClass);
    }
    
    /**
     * @return array<PaymentGatewayProfile>
     */
    public function getAvailableProfiles(?string $profileClass = null): array
    {
        $profileModel = model(PgProfileModel::class);
        $profiles = $profileModel
            ->where('company_id', $this->model->company_id)
            ->where('pg_merchant_id', $this->model->pg_merchant_id)
            ->where('pg_payment_method_id', $this->model->id)
            ->where('active', 1)
            ->orderBy('default', 'DESC')
            ->get()->getResult();

        return array_map(function ($pgProfile) use ($profileClass) {
            return $this->resolveProfile($pgProfile, $profileClass);
        }, $profiles);
    }
    
    protected function resolveProfile(object $pgProfile, ?string $profileClass = null): PaymentGatewayProfile
    {
        if ($pgProfile->type === 'MPGS') {
            return new MpgsProfile((int) $pgProfile->id);
        } elseif ($pgProfile->type === 'BANK_ACCOUNT') {
            return new BankAccountProfile((int) $pgProfile->id);
        } elseif ($pgProfile->type === 'NAPAS_VIETQR') {
            return new NapasVietQrProfile((int) $pgProfile->id);
        } else {
            throw new Exception('PG profile type not support');
        }
        
        if ($profileClass && !$pgProfile instanceof $profileClass) {
            throw new Exception('Specified PG profile type not found');
        }
            
        return $profile;
    }
}