<?php

namespace App\Features\PaymentGateway;

use Exception;
use App\Models\PgProfileModel;

class NapasVietQrProfile extends PaymentGatewayProfile
{
    public string $vac;
    public string $acquiringBankBin;
    public string $acquiringAccountHolderName;
    
    public function __construct(int $profileId)
    {
        parent::__construct($profileId);
        
        if ($this->profile->type !== 'NAPAS_VIETQR') {
            throw new Exception('Profile must be NAPAS_VIETQR');
        }
        
        $napasVietQrProfile = model(PgProfileModel::class)->getNapasVietQrProfile($this->profile->id);

        if (!$napasVietQrProfile) {
            throw new Exception('NAPAS_VIETQR profile not found');
        }

        $this->vac = $napasVietQrProfile->vac;
        $this->acquiringBankBin = $napasVietQrProfile->acquiring_bank_bin;
        $this->acquiringAccountHolderName = $napasVietQrProfile->acquiring_account_holder_name;
    }

    public function pushIpn(string $notificationType, int $pgOrderId, int $pgTransactionId): void
    {
        // Implement the logic to push the IPN message
    }

    public function pushWebhookEvent(string $eventType, array $data = []): void
    {
        // Implement the logic to push the webhook event
    }

    public function validatePaymentFields(array $fields): void
    {
        $rules = [
            'order_id' => ['required', 'string', 'regex_match[/^[A-Z0-9]{7}$/]'],
        ];

        $validation = service('validation');
        $validation->setRules($rules);

        if ($validation->run($fields) === false) {
            throw new Exception('Invalid payment fields: ' . implode(', ', $validation->getErrors()));
        }
    }
}