<?php

namespace App\Features\PaymentGateway;

class BankAccountProfile extends PaymentGatewayProfile
{
    public function pushIpn(string $notificationType, int $pgOrderId, int $pgTransactionId): void
    {
        // Implement the logic to push the IPN message
    }

    public function pushWebhookEvent(string $eventType, array $data = []): void
    {
        // Implement the logic to push the webhook event
    }

    public function validatePaymentFields(array $fields): void
    {
        //
    }
}