<?php

namespace App\Features\PaymentGateway;

use App\Exceptions\PaymentGatewayException;
use App\Models\PgAgreementModel;
use App\Models\PgMerchantModel;
use App\Models\PgProfileModel;
use Exception;

class Agreement
{
    public object $model;
    
    public object $merchant;
    
    public function __construct(object $model)
    {
        $this->model = $model;
        
        $this->merchant = model(PgMerchantModel::class)->where('id', $this->model->pg_merchant_id)->first();
        
        if (!$this->merchant) {
            throw new Exception('Merchant ID not found');
        }
    }
    
    public static function makeFromAgreementId(int $merchantId, string $agreementId)
    {
        $model = model(PgAgreementModel::class)
            ->where('pg_merchant_id', $merchantId)
            ->where('agreement_id', $agreementId)
            ->first();
            
        if (!$model) {
            throw new Exception('Agreement ID not found');
        }
         
        return new static($model);
    }
    
    public function findAvailableProfile(?string $profileClass = null): ?PaymentGatewayProfile
    {
        $profileModel = model(PgProfileModel::class);
        $profile = $profileModel
            ->where('company_id', $this->model->company_id)
            ->where('pg_merchant_id', $this->model->pg_merchant_id)
            ->where('id', $this->model->pg_profile_id)
            ->where('default', 1)
            ->where('active', 1)
            ->first();

        if (!$profile) {
            return null;
        }
        
        if ($profile->type === 'MPGS') {
            $profile = new MpgsProfile((int) $profile->id);
        } else {
            throw new PaymentGatewayException('Profile type not support');
        }
        
        if ($profileClass && !$profile instanceof $profileClass) {
            throw new PaymentGatewayException('Specified profile type not found');
        }
            
        return $profile;
    }
}