<?php

namespace App\Features\PaymentGateway;

use App\Models\CompanyModel;
use App\Models\PgProfileModel;

abstract class PaymentGatewayProfile
{
    public object $profile;
    
    public object $company;
    
    public function __construct(string $profileId)
    {
        $profileModel = model(PgProfileModel::class);
        $this->profile = $profileModel->where('id', $profileId)->first();
        
        if (!$this->profile) {
            throw new PaymentGatewayFeature('Profile ID not found');
        }
        
        $this->company = model(CompanyModel::class)->where('id', $this->profile->company_id)->first();
        
        if (!$this->company) {
            throw new PaymentGatewayFeature('Company ID not found');
        }
    }
    
    /**
     * @param "ORDER_PAID"|"RENEWAL_ORDER_PAID" $notificationType
     */
    abstract public function pushIpn(string $notificationType, int $pgOrderId, int $pgTransactionId): void;

    /**
     * @param "PAYMENT_TOKEN_INVALID" $eventType
     */
    abstract public function pushWebhookEvent(string $eventType, array $data = []): void;

    abstract public function validatePaymentFields(array $fields): void;
}