<?php

namespace App\Features\PaymentGateway;

use Exception;
use App\Models\PgOrderModel;
use App\Models\PgProfileModel;
use App\Models\PgMerchantModel;
use App\Features\PaymentGateway\Contexts\OrderContext;
use App\Features\PaymentGateway\Contexts\CheckoutContext;
use App\Features\PaymentGateway\Contexts\AgreementContext;
use App\Features\PaymentGateway\Contexts\BankTransferCheckoutContext;
use App\Features\PaymentGateway\Contexts\MpgsOrderContext;
use App\Features\PaymentGateway\Contexts\MpgsCheckoutContext;
use App\Features\PaymentGateway\Contexts\MpgsAgreementContext;

class PaymentGatewayFeature
{
    protected CheckoutContext $checkoutContext;
    
    protected AgreementContext $agreementContext;
    
    protected OrderContext $orderContext;
    
    const PAYMENT_METHODS = ['CARD', 'BANK_TRANSFER'];
    
    public function __construct()
    {
        //
    }
    
    public function getPgMerchantById(string $id): ?object
    {
        return model(PgMerchantModel::class)->where('id', $id)->first();
    }
    
    public function resolveProfileById(int $pgProfileId, ?string $profileClass = null): ?PaymentGatewayProfile
    {
        $profileModel = model(PgProfileModel::class);
        $profile = $profileModel
            ->where('id', $pgProfileId)
            ->where('default', 1)
            ->where('active', 1)
            ->first();

        if (!$profile) {
            return null;
        }
        
        if ($profile->type === 'MPGS') {
            $profile = new MpgsProfile((int) $profile->id);
        } else {
            throw new Exception('PG profile type not support');
        }
        
        if ($profileClass && !$profile instanceof $profileClass) {
            throw new Exception('Specified PG profile type not found');
        }
            
        return $profile;
    }
    
    /**
     * @param array<int,mixed> $fields
     */
    public function signFields(array $fields, string $secretKey): string
    {
        $signed = [];
        $signedFields = array_values(array_filter(array_keys($fields), fn ($field) => !in_array($field, ['session_id', 'signature', 'success_indicator', 'checkoutVersion', 'trace_id', 'step', 'order_id'])));
        
        foreach ($signedFields as $field) {
            if (!isset($fields[$field])) continue;
            
            $signed[] = $field . "=" . ($fields[$field] ?? '');
        }
        
        return base64_encode(hash_hmac('sha256', implode(',', $signed), $secretKey, true));
    }
    
    public function withCheckoutContext(string $merchantId, string $paymentMethod, ?string $profileClass = null): CheckoutContext
    {
        $merchant = model(PgMerchantModel::class)->where('merchant_id', $merchantId)->first();
        
        if (!$merchant) {
            throw new Exception('Merchant not found');
        }
        
        $paymentMethod = PaymentMethod::makeFromCheckout($merchant->id, $paymentMethod);
        
        if ($paymentMethod->model->payment_method === 'CARD') {
            $profile = $paymentMethod->findAvailableProfile($profileClass);
            
            if (!$profile) {
                throw new Exception('Payment gateway profile not found');
            }
            
            if ($profile instanceof MpgsProfile) {
                $this->checkoutContext = new MpgsCheckoutContext($this, $merchant, $profile);
            }
        } else if ($paymentMethod->model->payment_method === 'BANK_TRANSFER') {
            $profiles = $paymentMethod->getAvailableProfiles($profileClass);

            if (!count($profiles)) {
                throw new Exception('Payment gateway profiles not found');
            }
            
            $this->checkoutContext = new BankTransferCheckoutContext($this, $merchant, $profiles);
        } else {
            throw new Exception('Invalid payment method');
        }
            
        return $this->checkoutContext;
    }
    
    public function checkoutContext(): CheckoutContext
    {
        assert($this->checkoutContext instanceof CheckoutContext, 'Checkout context not initialized');
        
        return $this->checkoutContext;
    }
    
    public function withAgreementContext(string $merchantId, string $agreementId): void
    {
        $merchant = model(PgMerchantModel::class)->where('merchant_id', $merchantId)->first();
        
        if (!$merchant) {
            throw new Exception('PG merchant not found');
        }
        
        $agreement = Agreement::makeFromAgreementId($merchant->id, $agreementId);
            
        $profile = $agreement->findAvailableProfile();
        
        if (!$profile) {
            throw new Exception('PG profile not found');
        }
        
        if ($profile instanceof MpgsProfile) {
            $this->agreementContext = new MpgsAgreementContext($this, $merchant, $profile);
        } else {
            throw new Exception('Invalid agreement profile');
        }
    }
    
    public function agreementContext(): AgreementContext
    {
        assert($this->agreementContext instanceof AgreementContext, 'Agreement context not initialized');
        
        return $this->agreementContext;
    }
    
    public function withOrderContext(string $merchantId, string $orderId): void
    {
        $pgMerchant = model(PgMerchantModel::class)->where('merchant_id', $merchantId)->first();
        
        if (!$pgMerchant) {
            throw new Exception('PG merchant not found');
        }
        
        $pgOrder = model(PgOrderModel::class)
            ->where('pg_merchant_id', $pgMerchant->id)
            ->where('order_id', $orderId)
            ->first();
            
        if (!$pgOrder) {
            throw new Exception('Order not found', 404);
        }
            
        $profile = $this->resolveProfileById($pgOrder->pg_profile_id);
        
        if (!$profile) {
            throw new Exception('PG profile not found');
        }
        
        if ($profile instanceof MpgsProfile) {
            $this->orderContext = new MpgsOrderContext($this, $pgMerchant, $profile);
            $this->orderContext->pgOrder = $pgOrder;
        } else {
            throw new Exception('PG profile Invalid');
        }
    }
    
    public function orderContext(): OrderContext
    {
        assert($this->orderContext instanceof OrderContext, 'Order context not initialized');
        
        return $this->orderContext;
    }
    
    public function baseUrl($path): string
    {
        return base_url('/pay/v1/' . trim($path, '/'));
    }
}
