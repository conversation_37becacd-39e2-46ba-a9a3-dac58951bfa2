<?php

namespace App\Features\PaymentGateway\Contexts;

use Exception;
use App\Models\PgOrderModel;
use App\Models\PgBranchModel;
use App\Models\BankAccountModel;
use App\Models\BankSubAccountModel;
use App\Models\PgBranchProfileModel;
use App\Models\PgBranchTerminalModel;
use App\Models\MbbEnterpriseAccountModel;
use App\Features\PaymentGateway\BankAccountProfile;
use App\Features\PaymentGateway\NapasVietQrProfile;
use App\Features\PaymentGateway\PaymentGatewayFeature;
use App\Features\PaymentGateway\Contexts\CheckoutContext;

class BankTransferCheckoutContext extends CheckoutContext
{
    use HasVietQR;
    
    /**
     * @var array<BankAccountProfile>
     */
    public array $profiles = [];
    
    public ?string $branchCode = '001';

    public ?string $branchTerminalCode = '01';
    
    public ?object $pgBranch = null;

    public ?object $pgBranchTerminal = null;
    
    public array $pgBranchProfiles = [];
    
    public function __construct(PaymentGatewayFeature $feature, object $merchant, array $profiles)
    {
        parent::__construct($feature, 'BANK_TRANSFER');

        $this->merchant = $merchant;
        $this->profiles = $profiles;
    }

    public function initOnetimePaymentFields(): array
    {        
        assert($this->isOnetimePayment(), 'Must be onetime payment');

        return [];
    }

    public function initRecurringPaymentFields(): array
    {
        throw new Exception('Recurring payments are not supported for bank transfer checkout.');
    }

    public function renderOnetimePaymentCheckoutView(): string
    {
        $this->loadPgBranch();
        $this->loadPgBranchTerminal();
        $this->loadPgBranchProfiles();
        
        if (!count($this->pgBranchProfiles)) {
            throw new Exception('No bank accounts available for this branch.');
        }

        $this->pgBranchProfiles[0]->profile->validatePaymentFields($this->getPaymentFields());

        return view('pay/v1/checkout/bank-transfer/onetime', [
            'fields' => $this->getPaymentFields(),
            'context' => $this
        ]);
    }

    public function renderRecurringPaymentCheckoutView(): string
    {
        throw new Exception('Recurring payments are not supported for bank transfer checkout.');
    }

    public function initAgreementPaymentFields(): array
    {
        throw new Exception('Agreement payments are not supported for bank transfer checkout.');
    }

    public function renderAgreementPaymentCheckoutView(): string
    {
        throw new Exception('Agreement payments are not supported for bank transfer checkout.');
    }

    public function initPgSession(array $meta = []): void
    {
        throw new Exception('PG Session initialization is not supported for bank transfer checkout.');
    }

    public function initPgOrder(array $meta = []): void
    {
        /** @var IncomingRequest $request */
        $request = service('request');

        $pgOrderModel = model(PgOrderModel::class);
        $pgBranchProfile = $this->getSelectedPgBranchProfile();

        if (!$pgBranchProfile) {
            throw new Exception('No bank account selected for the order.');
        }

        $pgOrder = $pgOrderModel
            ->where('pg_merchant_id', $pgBranchProfile->profile->pg_merchant_id)
            ->where('order_id', $this->orderId)
            ->first();

        $safePgOrderData = [
            'company_id' => $pgBranchProfile->profile->company_id,
            'pg_profile_id' => $pgBranchProfile->profile->id,
            'pg_merchant_id' => $pgBranchProfile->profile->pg_merchant_id,
            'pg_agreement_id' => $additionalData['pg_agreement_id'] ?? null,
            'order_id' => $this->orderId,
            'order_status' => 'AUTHENTICATION_NOT_NEEDED',
            'order_currency' =>  $this->currency,
            'order_amount' => $this->orderAmount,
            'order_description' => $this->orderDescription,
            'order_invoice_number' => $this->orderInvoiceNumber,
            'order_discount_amount' => $this->orderDiscountAmount,
            'order_discount_description' => $this->orderDiscountDescription,
            'order_discount_code' => $this->orderDiscountCode,
            'order_tax_amount' => $this->orderTaxAmount,
            'custom_data' => $this->customData,
            'user_agent' => method_exists($request, 'getUserAgent') ? (string) $request->getUserAgent() : null,
            'ip_address' => method_exists($request, 'getIPAddress') ? $request->getIPAddress() : null,
        ];
    }
    
    public function loadPgBranch(): void
    {
        if (!$this->branchCode) return;

        $this->pgBranch = model(PgBranchModel::class)
            ->where('pg_merchant_id', $this->merchant->id)
            ->where('company_id', $this->merchant->company_id)
            ->where('code', $this->branchCode)
            ->where('active', 1)
            ->first();
    }

    public function loadPgBranchTerminal(): void
    {
        if (!$this->branchTerminalCode) return;

        $this->pgBranchTerminal = model(PgBranchTerminalModel::class)
            ->where('pg_merchant_id', $this->merchant->id)
            ->where('pg_branch_id', $this->pgBranch->id)
            ->where('company_id', $this->merchant->company_id)
            ->where('code', $this->branchTerminalCode)
            ->where('active', 1)
            ->first();
    }
    
    public function loadPgBranchProfiles(): void
    {
        if (!$this->pgBranch) return;

        $this->pgBranchProfiles = model(PgBranchProfileModel::class)
            ->select(['tb_autopay_pg_branch_profile.*'])
            ->join('tb_autopay_pg_profile', 'tb_autopay_pg_profile.id = tb_autopay_pg_branch_profile.pg_profile_id')
            ->where('tb_autopay_pg_branch_profile.pg_merchant_id', $this->merchant->id)
            ->where('tb_autopay_pg_branch_profile.company_id', $this->merchant->company_id)
            ->where('tb_autopay_pg_branch_profile.pg_branch_id', $this->pgBranch->id)
            ->whereIn('tb_autopay_pg_branch_profile.pg_profile_id', array_map(function ($profile) {
                return $profile->profile->id;
            }, $this->profiles))
            ->orderBy('tb_autopay_pg_profile.default', 'DESC')
            ->get()
            ->getResult();
            
        foreach ($this->pgBranchProfiles as $pgBranchProfile) {
            $pgBranchProfile->profile = array_values(array_filter($this->profiles, function ($profile) use ($pgBranchProfile) {
                return $profile->profile->id === $pgBranchProfile->pg_profile_id;
            }))[0] ?? null;
            
            if (!$pgBranchProfile->profile) {
                throw new Exception('Profile not found for branch profile ID: ' . $pgBranchProfile->id);
            }

            if ($pgBranchProfile->profile instanceof NapasVietQrProfile) {
                $accountNumber = sprintf('%s%s%s%s', $pgBranchProfile->profile->vac, $this->pgBranch->code, $this->pgBranchTerminal->code, $this->orderId);
                $pgBranchProfile->qrcode = $this->generateVietQR($pgBranchProfile->profile->acquiringBankBin, $accountNumber, $pgBranchProfile->profile->acquiringAccountHolderName, $this->orderId, $this->orderAmount);
            } else {
                $pgBranchProfile->bank_account = model(BankAccountModel::class)
                    ->select(['tb_autopay_bank_account.*', 'tb_autopay_bank.icon_path as bank_icon_path', 'tb_autopay_bank.logo_path as bank_logo_path', 'tb_autopay_bank.full_name as bank_full_name', 'tb_autopay_bank.short_name as bank_short_name', 'tb_autopay_bank.brand_name as bank_brand_name'])
                    ->join('tb_autopay_bank', 'tb_autopay_bank.id = tb_autopay_bank_account.bank_id')
                    ->where('tb_autopay_bank_account.id', $pgBranchProfile->bank_account_id)
                    ->where('tb_autopay_bank_account.active', 1)
                    ->first();
                    
                if (!$pgBranchProfile->bank_account) {
                    throw new Exception('Bank account not found for profile ID: ' . $pgBranchProfile->id);
                }
                
                switch ($pgBranchProfile->bank_account->bank_brand_name) {
                    case 'MBB':
                        $pgBranchProfile->bank_account->enterprise = model(MbbEnterpriseAccountModel::class)
                            ->where('bank_account_id', $pgBranchProfile->bank_account->id)
                            ->where('active', 1)
                            ->first();
                        break;
                    default:
                        $pgBranchProfile->bank_account->enterprise = null;
                        break;
                }
                    
                if ($pgBranchProfile->bank_sub_account_id) {
                    $pgBranchProfile->va = model(BankSubAccountModel::class)
                        ->where('id', $pgBranchProfile->bank_sub_account_id)
                        ->where('active', 1)
                        ->first();
                }
                
                $pgBranchProfile->qrcode = $this->generateVietQRById(
                    $pgBranchProfile->bank_account->id,
                    $pgBranchProfile->bank_sub_account_id,
                    $this->orderId,
                    $this->orderAmount
                );
            }
        }
    }

    public function getSelectedPgBranchProfile(): ?object
    {
        if (!count($this->pgBranchProfiles)) {
            return null;
        }

        return $this->pgBranchProfiles[0];
    }
    
    protected function additionalPaymentFieldSchema(): array
    {
        return [
            [
                'field_name' => 'branch_code',
                'property_name' => 'branchCode'
            ],
            [
                'field_name' => 'branch_terminal_code',
                'property_name' => 'branchTerminalCode'
            ],
        ];
    }
}