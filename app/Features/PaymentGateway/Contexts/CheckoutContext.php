<?php

namespace App\Features\PaymentGateway\Contexts;

use Exception;
use App\Models\PgOrderModel;
use App\Models\PgSessionModel;
use App\Models\PgCustomerModel;
use App\Models\PgAgreementModel;
use App\Models\PgTransactionModel;
use App\Features\PaymentGateway\PaymentGatewayFeature;

abstract class CheckoutContext
{
    protected PaymentGatewayFeature $feature;

    public object $merchant;

    public string $paymentMethod;

    public string $currency = 'VND';

    public ?string $orderId = null;

    public ?string $orderDescription = null;

    public ?int $orderAmount = null;
    
    public ?int $placeholderOrderAmount = null;
    
    public ?string $orderInvoiceNumber = null;
    
    public ?string $orderDiscountCode = null;
    
    public ?string $orderDiscountDescription = null;
    
    public ?int $orderDiscountAmount = null;
    
    public ?int $orderTaxAmount = null;
    
    public string $operation = 'PURCHASE'; // PURCHASE, VERIFY
    
    public ?string $agreementName = null;

    public ?string $agreementId = null;

    public ?string $agreementType = null;
    
    public ?int $agreementAmountPerPayment = null;
    
    public ?string $agreementPaymentFrequency = null;

    public ?string $customerId = null;

    public ?string $successUrl = null;

    public ?string $errorUrl = null;

    public ?string $cancelUrl = null;

    public ?string $signature = null;
    
    public array $additionalFields = [];
    
    public ?object $pgOrder = null;    

    public ?object $pgAgreement = null;
    
    public ?object $pgCustomer = null;
    
    public ?string $cardId = null;
    
    public array $pgCards = [];
    
    public ?object $pgCard = null;
    
    public ?object $pgTransaction = null;
    
    public ?string $transactionId = null;
    
    public ?string $customData = null;
    
    public ?string $traceId = null;
    
    public ?object $pgSession = null;

    public ?object $pgObject = null;
    
    public ?string $nonce = null;
    
    public ?string $timestamp = null;

    public function __construct(PaymentGatewayFeature $feature, string $paymentMethod)
    {
        $this->feature = $feature;
        $this->paymentMethod = $paymentMethod;
    }

    public function loadPaymentFields(array $fields): void
    {
        helper(['general']);
        
        $this->cardId = $fields['card_id'] ?? null;
        $this->orderId = $fields['order_id'] ?? null;
        $this->transactionId = $fields['transaction_id'] ?? null;
        $this->orderDescription = $fields['order_description'] ?? null;
        $this->orderAmount = $fields['order_amount'] ?? null;
        $this->placeholderOrderAmount = $fields['placeholder_order_amount'] ?? null;
        $this->currency = $fields['currency'] ?? 'VND';
        $this->orderInvoiceNumber = $fields['order_invoice_number'] ?? null;
        $this->orderDiscountAmount = $fields['order_discount_amount'] ?? null;
        $this->orderDiscountCode = $fields['order_discount_code'] ?? null;
        $this->orderDiscountDescription = $fields['order_discount_description'] ?? null;
        $this->orderTaxAmount = $fields['order_tax_amount'] ?? null;
        $this->operation = $fields['operation'] ?? $this->operation;
        $this->agreementName = $fields['agreement_name'] ?? null;
        $this->agreementId = $fields['agreement_id'] ?? null;
        $this->agreementAmountPerPayment = $fields['agreement_amount_per_payment'] ?? null;
        $this->agreementType = $fields['agreement_type'] ?? null;
        $this->agreementPaymentFrequency = $fields['agreement_payment_frequency'] ?? null;
        $this->customerId = $fields['customer_id'] ?? null;
        $this->successUrl = $fields['success_url'] ?? null;
        $this->errorUrl = $fields['error_url'] ?? null;
        $this->cancelUrl = $fields['cancel_url'] ?? null;
        $this->customData = $fields['custom_data'] ?? null;
        $this->traceId = $fields['trace_id'] ?? null;
        $this->nonce = $fields['nonce'] ?? null;

        foreach ($this->additionalPaymentFieldSchema() as $schema) {
            if (property_exists($this, $schema['property_name']) && isset($fields[$schema['field_name']])) {
                $this->{$schema['property_name']} = $fields[$schema['field_name']];
            }
        }
        
        if ($this->signFields($fields) != $fields['signature']) {
            throw new Exception('Signature invalid', 400);
        }
    }
    
    public function loadPgSession(): void
    {
        if (!$this->traceId) return;
        
        $this->pgSession = model(PgSessionModel::class)
            ->where('pg_merchant_id', $this->merchant->id)
            ->where('company_id', $this->merchant->company_id)
            ->where('trace_id', $this->traceId)
            ->where('expires_at >', date('Y-m-d H:i:s'))
            ->first();
    }
    
    protected function updatePgSession(array $data = []): bool
    {
        assert(is_object($this->pgSession), 'PG session must be loaded');
        
        return model(PgSessionModel::class)->where('id', $this->pgSession->id)->set($data)->update();
    }
    
    public function loadPgOrder(): void
    {
        if (!$this->orderId) return;
        
        $this->pgOrder = slavable_model(PgOrderModel::class, 'PaymentGateway')
            ->where('pg_merchant_id', $this->merchant->id)
            ->where('company_id', $this->merchant->company_id)
            ->where('order_id', $this->orderId)
            ->where('pg_customer_id', is_object($this->pgCustomer) ? $this->pgCustomer->id : null)
            ->first();
    }

    public function loadPgAgreement(): void
    {
        if (!$this->agreementId) return;
        
        $this->pgAgreement = slavable_model(PgAgreementModel::class, 'PaymentGateway')
            ->select(['tb_autopay_pg_agreement.*', 'tb_autopay_pg_order.order_currency'])
            ->join('tb_autopay_pg_order', 'tb_autopay_pg_order.id = tb_autopay_pg_agreement.pg_order_id')
            ->where('tb_autopay_pg_agreement.pg_merchant_id', $this->merchant->id)
            ->where('tb_autopay_pg_agreement.company_id', $this->merchant->company_id)
            ->where('tb_autopay_pg_agreement.agreement_id', $this->agreementId)
            ->first();
    }
    
    public function loadPgCustomer(): void
    {
        if (!$this->customerId) return;
        
        $this->pgCustomer = slavable_model(PgCustomerModel::class, 'PaymentGateway')
            ->where('pg_merchant_id', $this->merchant->id)
            ->where('company_id', $this->merchant->company_id)
            ->where('customer_id', $this->customerId)
            ->first();
    }

    public function isOnetimePayment(): bool
    {
        return $this->agreementType === null;
    }

    public function isAgreementPayment(): bool
    {
        return $this->agreementType !== null;
    }

    public function initPaymentFields(array $fields)
    {
        $fields['order_id'] = $this->generateOrderId();

        $this->loadPaymentFields($fields);
        
        $additionalFields = $this->isAgreementPayment() ? $this->initAgreementPaymentFields() : $this->initOnetimePaymentFields();
        
        $fields = array_merge($fields, $additionalFields);
        
        if ($this->isAgreementPayment() && $this->orderAmount == 0) {
            $fields['placeholder_order_amount'] = 20000;
        }
        
        $fields['signature'] = $this->signFields($fields);
        
        $this->loadPaymentFields($fields);
    }

    public function getPaymentFields($nonce = false): array
    {
        $fields['merchant'] = $this->merchant->merchant_id;
        $fields['payment_method'] = $this->paymentMethod;
        $fields['operation'] = $this->operation;
        $fields['card_id'] = $this->cardId;
        $fields['order_id'] = $this->orderId;
        $fields['order_description'] = $this->orderDescription;
        $fields['order_amount'] = $this->orderAmount;
        if ($this->placeholderOrderAmount > 0) {
            $fields['placeholder_order_amount'] = $this->placeholderOrderAmount;
        }
        $fields['currency'] = $this->currency;
        $fields['order_invoice_number'] = $this->orderInvoiceNumber ?? null;
        $fields['order_discount_code'] = $this->orderDiscountCode ?? null;
        $fields['order_discount_description'] = $this->orderDiscountDescription ?? null;
        $fields['order_discount_amount'] = $this->orderDiscountAmount ?? null;
        $fields['agreement_name'] = $this->agreementName;
        $fields['agreement_id'] = $this->agreementId;
        $fields['agreement_type'] = $this->agreementType;
        $fields['agreement_amount_per_payment'] = $this->agreementAmountPerPayment;
        $fields['agreement_payment_frequency'] = $this->agreementPaymentFrequency;
        $fields['customer_id'] = $this->customerId;
        $fields['transaction_id'] = $this->transactionId;
        $fields['success_url'] = $this->successUrl ?? '';
        $fields['error_url'] = $this->errorUrl ?? '';
        $fields['cancel_url'] = $this->cancelUrl ?? '';
        $fields['custom_data'] = $this->customData;
        $fields['trace_id'] = $this->traceId;
        
        if ($nonce) {
            $fields['nonce'] = md5(uniqid());
        }

        $fields = array_merge($fields, $this->getAdditionalPaymentFields());
        
        $fields = array_filter($fields, fn($value) => $value !== null);

        $fields['signature'] = $this->feature->signFields($fields, $this->merchant->secret_key);

        return $fields;
    }

    public function renderCheckoutView(): string
    {
        $this->loadPgSession();
        
        if ($this->isAgreementPayment()) {
            return $this->renderAgreementPaymentCheckoutView();
        }

        return $this->renderOnetimePaymentCheckoutView();
    }
    
    public function isFulfilledCheckout(): bool
    {
        if (!is_object($this->pgOrder)) return false;
        
        if ($this->pgOrder->order_status === 'CAPTURED') return true;
        
        return  $this->pgOrder->order_status === 'CANCELLED'
            && model(PgTransactionModel::class)->where('pg_order_id', $this->pgOrder->id)
                ->where('transaction_type', 'VOID_PAYMENT')
                ->where('transaction_status', 'APPROVED')
                ->countAllResults();
    }

    protected function additionalPaymentFieldSchema(): array
    {
        return [];
    }
    
    protected function getAdditionalPaymentFields(): array
    {
        $fields = [];
        
        foreach ($this->additionalPaymentFieldSchema() as $schema) {
            $fields[$schema['field_name']] = $this->{$schema['property_name']};
        }

        return $fields;
    }
    
    /**
     * @param array<int,mixed> $fields
     */
    public function signFields(array $fields): string
    {
        assert(is_object($this->merchant) && $this->merchant->secret_key, 'The merchant must be exist and have a secret key');
        
        return $this->feature->signFields($fields, $this->merchant->secret_key);
    }
    
    public function mustBeVoidTransaction(): bool
    {
        return $this->placeholderOrderAmount > 0 && $this->orderAmount === 0 && $this->isAgreementPayment();
    }
    
    public function capturableOrderAmount(): int
    {
        return $this->placeholderOrderAmount > 0 && $this->orderAmount === 0 ? $this->placeholderOrderAmount : $this->orderAmount;
    }
    
    public function isSessionCancelled(): bool
    {
        assert(is_object($this->pgSession), 'PG session must be loaded');
        
        return $this->pgSession->status === 'CANCELLED';
    }
    
    public function isSessionFulfilled(): bool
    {
        assert(is_object($this->pgSession), 'PG session must be loaded');
        
        return $this->pgSession->status === 'FULFILLED';
    }
    
    public function isSessionPending(): bool
    {
        assert(is_object($this->pgSession), 'PG session must be loaded');
        
        return $this->pgSession->status === 'PENDING';
    }
    
    public function isSessionInitiated(): bool
    {
        assert(is_object($this->pgSession), 'PG session must be loaded');
        
        return $this->pgSession->status === 'INITIATED';
    }
    
    public function markSessionAsFulfilled(array $data = []): bool
    {
        return $this->updatePgSession(array_merge([
            'status' => 'FULFILLED',
            'error_code' => 0,
            'error_desc' => 'Order fulfilled',
            'pg_order_id' => is_object($this->pgOrder) ? $this->pgOrder->id : null
        ], $data));
    }
    
    public function markSessionAsPending(array $data = []): bool
    {
        return $this->updatePgSession(array_merge([
            'status' => 'PENDING',
            'pg_order_id' => is_object($this->pgOrder) ? $this->pgOrder->id : null
        ], $data));
    }
    
    public function markSessionAsCancelled(array $data = []): bool
    {
        return $this->updatePgSession(array_merge([
            'status' => 'CANCELLED',
            'pg_order_id' => is_object($this->pgOrder) ? $this->pgOrder->id : null
        ], $data));
    }
    
    public function markSessionAsFailed(array $data = []): bool
    {
        return $this->updatePgSession(array_merge([
            'status' => 'FAILED',
            'pg_order_id' => is_object($this->pgOrder) ? $this->pgOrder->id : null
        ], $data));
    }
    
    public function getSessionMeta(?string $key = null)
    {
        assert(is_object($this->pgSession), 'PG session must be loaded');
        
        $meta = $this->pgSession->meta ? json_decode($this->pgSession->meta, true) : [];
        
        return $key ? ($meta[$key] ?? null) : $meta;
    }
    
    public function getCurrencyLabel(): string
    {
        return $this->currency === 'VND' ? 'đ' : $this->currency;
    }
    
    public function getAgreementPaymentFrequencyLabel(): string
    {
        $mapping = [
            'DAILY' => 'Hằng ngày',
            'MONTHLY' => 'Hằng tháng',
            'YEARLY' => 'Hằng năm'
        ];
        
        return $mapping[$this->agreementPaymentFrequency] ?? $this->agreementPaymentFrequency;
    }

    public function getAgreementPaymentFrequencyDays(): int
    {
        $mapping = [
            'DAILY' => 1,
            'MONTHLY' => 28,
            'YEARLY' => 365
        ];
        
        return $mapping[$this->agreementPaymentFrequency];
    }
    
    abstract public function initOnetimePaymentFields(): array;
    
    abstract public function initAgreementPaymentFields(): array;
    
    abstract public function renderOnetimePaymentCheckoutView(): string;
    
    abstract public function renderAgreementPaymentCheckoutView(): string;
    
    abstract public function initPgSession();

    abstract public function initPgOrder();

    abstract public function generateOrderId(): string;
}
