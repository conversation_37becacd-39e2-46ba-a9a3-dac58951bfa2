<?php

namespace App\Features\PaymentGateway\Contexts;

use App\Features\PaymentGateway\PaymentGatewayFeature;
use App\Models\PgAgreementModel;
use App\Models\PgCardModel;
use App\Models\PgCustomerModel;
use Exception;

abstract class AgreementContext
{
    protected PaymentGatewayFeature $feature;

    public ?object $pgMerchant = null;

    public ?string $agreementId = null;
    
    public ?string $customerId = null;

    public ?string $successUrl = null;

    public ?string $errorUrl = null;

    public ?string $cancelUrl = null;

    public ?string $signature = null;
    
    public ?object $pgAgreement = null;
    
    public ?object $pgCustomer = null;
    
    public ?object $pgCard = null;
    
    public ?string $customData = null;
    
    public function __construct(PaymentGatewayFeature $feature)
    {
        $this->feature = $feature;
    }

    public function loadFields(array $fields): void
    {
        helper(['general']);
        
        $this->agreementId = $fields['agreement_id'] ?? null;
        $this->customerId = $fields['customer_id'] ?? null;
        $this->successUrl = $fields['success_url'] ?? null;
        $this->errorUrl = $fields['error_url'] ?? null;
        $this->cancelUrl = $fields['cancel_url'] ?? null;
        $this->customData = $fields['custom_data'] ?? null;
        
        $this->loadPgAgreement();
        
        if (!$this->pgAgreement) {
            throw new Exception('PG agreement not found', 400);
        }
        
        $this->loadPgCard();
        
        if (!$this->pgCard) {
            throw new Exception('PG card not found', 400);
        }
        
        $this->loadPgCustomer();
        
        if (!$this->pgCustomer) {
            throw new Exception('PG customer not found', 400);
        }
        
        foreach ($this->additionalFieldSchema() as $schema) {
            if (property_exists($this, $schema['property_name']) && isset($fields[$schema['field_name']])) {
                $this->{$schema['property_name']} = $fields[$schema['field_name']];
            }
        }
    }
    
    public function loadPgAgreement(): void
    {
        if (!$this->agreementId) return;
        
        $this->pgAgreement = slavable_model(PgAgreementModel::class, 'PaymentGateway')
            ->select(['tb_autopay_pg_agreement.*', 'tb_autopay_pg_order.order_currency'])
            ->join('tb_autopay_pg_order', 'tb_autopay_pg_order.id = tb_autopay_pg_agreement.pg_order_id')
            ->where('tb_autopay_pg_agreement.pg_merchant_id', $this->pgMerchant->id)
            ->where('tb_autopay_pg_agreement.company_id', $this->pgMerchant->company_id)
            ->where('tb_autopay_pg_agreement.agreement_id', $this->agreementId)
            ->first();
    }
    
    public function loadPgCard(): void
    {
        if (!$this->pgAgreement) return;
        
        $this->pgCard = model(PgCardModel::class)
            ->where('pg_merchant_id', $this->pgMerchant->id)
            ->where('company_id', $this->pgMerchant->company_id)
            ->where('id', $this->pgAgreement->pg_card_id)
            ->first();
    }
    
    public function loadPgCustomer(): void
    {
        if (!$this->pgAgreement) return;
        
        $this->pgCustomer = model(PgCustomerModel::class)
            ->where('pg_merchant_id', $this->pgMerchant->id)
            ->where('company_id', $this->pgMerchant->company_id)
            ->where('id', $this->pgAgreement->pg_customer_id)
            ->first();
    }

    public function getFields(): array
    {
        $fields['merchant'] = $this->pgMerchant->merchant_id;
        $fields['agreement_id'] = $this->agreementId;
        $fields['customer_id'] = $this->customerId;
        $fields['success_url'] = $this->successUrl;
        $fields['error_url'] = $this->errorUrl;
        $fields['cancel_url'] = $this->cancelUrl;
        $fields['custom_data'] = $this->customData;

        $fields = array_merge($fields, $this->getAdditionalFields());
        
        $fields = array_filter($fields, fn($value) => $value !== null);

        $fields['signature'] = $this->feature->signFields($fields, $this->pgMerchant->secret_key);

        return $fields;
    }
    
    protected function additionalFieldSchema(): array
    {
        return [];
    }
    
    protected function getAdditionalFields(): array
    {
        $fields = [];
        
        foreach ($this->additionalFieldSchema() as $schema) {
            $fields[$schema['field_name']] = $this->{$schema['property_name']};
        }

        return $fields;
    }
    
    abstract public function cancelAgreement(): bool;
    
    abstract public function renewAgreement(array $data = []): bool;
}
