<?php

namespace App\Features\PaymentGateway\Contexts;

use App\Features\PaymentGateway\PaymentGatewayFeature;
use App\Models\PgOrderModel;

abstract class OrderContext
{
    protected PaymentGatewayFeature $feature;

    public ?object $pgMerchant = null;

    public ?object $pgOrder = null;
    
    public function __construct(PaymentGatewayFeature $feature)
    {
        $this->feature = $feature;
    }
    
    public function loadPgOrder(string $orderId)
    {
        $this->pgOrder = model(PgOrderModel::class)
            ->where('pg_merchant_id', $this->pgMerchant->id)
            ->where('company_id', $this->pgMerchant->company_id)
            ->where('order_id', $orderId)
            ->first();
    }

    abstract public function voidOrder();
}
