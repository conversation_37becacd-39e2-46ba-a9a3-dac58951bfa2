<?php

namespace App\Features\PaymentGateway\Contexts;

use App\Features\PaymentGateway\MpgsProfile;
use App\Features\PaymentGateway\PaymentGatewayFeature;
use App\Models\PgTransactionModel;
use Exception;

class MpgsOrderContext extends OrderContext
{
    public ?MpgsProfile $profile = null;
    
    public function __construct(PaymentGatewayFeature $feature, object $merchant, MpgsProfile $profile)
    {
        parent::__construct($feature);
        
        $this->pgMerchant = $merchant;
        $this->profile = $profile;
    }
    
    public function voidOrder()
    {
        assert(is_object($this->pgOrder), 'PG order must be loaded');

        if ($this->pgOrder->order_status !== 'CAPTURED') {
            throw new Exception('Order must be captured to void', 400);
        }
        
        $paymentTransaction = model(PgTransactionModel::class)
            ->where('pg_merchant_id', $this->pgMerchant->id)
            ->where('pg_order_id', $this->pgOrder->id)
            ->where('pg_profile_id', $this->profile->profile->id)
            ->where('transaction_type', 'PAYMENT')
            ->whereIn('transaction_status', ['APPROVED', 'APPROVED_AUTO'])
            ->whereIn('payment_method', ['CARD'])
            ->first();
            
        if (!$paymentTransaction) {
            throw new Exception('Payment transaction not found', 400);
        }
        
        $client = $this->profile->makeRestApiClient();
        
        $response = $client->voidTransaction($this->pgOrder->order_id, 'VOID-' . $paymentTransaction->transaction_id, $paymentTransaction->transaction_id);
        
        if ($response->getStatusCode() !== 201) {
            throw new Exception('Void transaction failed');
        }

        $drOrder = $this->profile->queryDROrder($this->pgOrder->order_id);
        
        if (!$drOrder) {
            throw new Exception('DR order not found');
        }
        
        $pgOrderId = $this->profile->syncDROrder($drOrder);
        
        if (property_exists($drOrder, 'transaction')) {
            foreach ($drOrder->transaction ?? [] as $drTransaction) {
                if ($this->profile->isNewDRVoidPaymentTransaction($pgOrderId, $drTransaction)) {
                    // $this->profile->handleNewDRTransactionApproved($this->pgOrder->id, $drTransaction);
                }
            }
        }
    }
}
