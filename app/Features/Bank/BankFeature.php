<?php

namespace App\Features\Bank;

use App\Models\BankModel;
use App\Features\Bank\BankFilter;
use App\Features\Bank\Contexts\BankContext;
use Exception;
use App\Exceptions\BankFeatureException;

class BankFeature
{
    public array $banks;
    
    public BankContext $bankContext;
    
    public function __construct()
    {
        $this->banks = model(BankModel::class)->where('active', 1)->orderBy('id', 'ASC')->get()->getResult();
    }
    
    public function bankFilter(): BankFilter
    {
        return new BankFilter($this->banks);
    }
}
