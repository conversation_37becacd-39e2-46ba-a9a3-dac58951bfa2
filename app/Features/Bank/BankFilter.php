<?php

namespace App\Features\Bank;

use App\Models\BankModel;

class BankFilter
{
    public $banks;
    
    protected bool $onlyIndividualApiSupport = false;
    protected bool $onlyEnterpriseApiSupport = false;
    
    public function __construct(array $banks)
    {
        $this->banks = $banks;
    }
    
    public function onlyApiSupport(): BankFilter
    {
        $this->banks = array_filter($this->banks, fn($bank) => $bank->invididual_api_connection || $bank->enterprise_api_connection);
        
        return $this;
    }
    
    public function onlyIndividualSupport(): BankFilter
    {
        helper(['general']);

        $this->onlyIndividualApiSupport = true;
        $this->banks = array_filter($this->banks, function($bank) {
            if (is_shop_billing_subscription() && $bank->brand_name === 'VietinBank') {
                return false;
            }

            return $bank->invididual_api_connection;
        });
        return $this;
    }
    
    public function onlyEnterpriseSupport(): BankFilter
    {
        $this->onlyEnterpriseApiSupport = true;
        $this->banks = array_filter($this->banks, fn($bank) => $bank->enterprise_api_connection);
        
        return $this;
    }
    
    public function sort(): BankFilter
    {
        if ($this->onlyIndividualApiSupport) {
            usort($this->banks, fn($a, $b) => $a->invididual_order - $b->invididual_order);
        } elseif ($this->onlyEnterpriseApiSupport) {
            usort($this->banks, fn($a, $b) => $a->enterprise_order - $b->enterprise_order);
        }
        
        return $this;
    }
    
    public function findByBrandName(string $brandName): ?object
    {
        return array_values(array_filter($this->banks, fn($bank) => $bank->brand_name === $brandName))[0] ?? null;
    }
    
    public function toArray(): array
    {
        return $this->banks;
    }
}
