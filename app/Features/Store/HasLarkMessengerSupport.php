<?php

namespace App\Features\Store;

use CodeIgniter\Model;
use App\Models\BankShopLinkModel;
use App\Models\NotificationLarkMessengerModel;

trait HasLarkMessengerSupport
{
    /**
     * @return array
     */
    public function getLinkableLarkMessengerStores(string $botWebhookUrl, string $transactionType, ?callable $builderCallback = null)
    {
        $builder = $this->storeBuilder();
        
        if (is_callable($builderCallback)) {
            $builder = $builderCallback($builder);
        }
        
        $stores = $builder->get()->getResult();
        
        $storeIds = array_column($stores, 'id');
        
        $storeLinks = count($storeIds) 
            ? model(BankShopLinkModel::class)->select(['shop_id', 'bank_account_id', 'bank_sub_account_id'])->whereIn('shop_id', $storeIds)->findAll() 
            : [];
        
        $stores = array_map(function ($store) use ($storeLinks, $botWebhookUrl, $transactionType) {
            $links = array_values(array_filter($storeLinks, fn ($link) => $link['shop_id'] === $store->id));
            $store->links = $links;
            
            if (! count($links)) {
                return $store;
            }
            
            $linked = model(NotificationLarkMessengerModel::class)
                ->where('bot_webhook_url', $botWebhookUrl)
                ->where('transaction_type', $transactionType)
                ->whereIn('bank_account_id', array_column($links, 'bank_account_id'))
                ->whereIn('sub_account_id', $this->replaceLarkMessengerSubAccountIdForPrimaryAccount(array_column($links, 'bank_sub_account_id')))
                ->countAllResults() > 0;
                
            if (!$linked) {
                return $store;
            }
            
            return false;
        }, $stores);
        
        return array_values(array_filter($stores));
    }
    
    /**
     * @return array
     */
    public function getLinkedLarkMessengerStores(string $botWebhookUrl, string $transactionType, ?callable $builderCallback = null)
    {
        $builder = $this->storeBuilder();
        
        if (is_callable($builderCallback)) {
            $builder = $builderCallback($builder);
        }
        
        $stores = $builder->get()->getResult();
        
        $storeIds = array_column($stores, 'id');
        
        $storeLinks = count($storeIds) 
            ? model(BankShopLinkModel::class)->select(['shop_id', 'bank_account_id', 'bank_sub_account_id'])->whereIn('shop_id', $storeIds)->findAll() 
            : [];
        
        $stores = array_map(function ($store) use ($storeLinks, $botWebhookUrl, $transactionType) {
            $links = array_filter($storeLinks, fn ($link) => $link['shop_id'] === $store->id);
            $store->links = $links;
            
            if (! count($links)) {
                return false;
            }
            
            $linked = model(NotificationLarkMessengerModel::class)
                ->where('bot_webhook_url', $botWebhookUrl)
                ->where('transaction_type', $transactionType)
                ->whereIn('bank_account_id', array_column($links, 'bank_account_id'))
                ->whereIn('sub_account_id', $this->replaceLarkMessengerSubAccountIdForPrimaryAccount(array_column($links, 'bank_sub_account_id')))
                ->countAllResults() > 0;
                
            if (!$linked) {
                return false;
            }
            
            return $store;
        }, $stores);
        
        return array_values(array_filter($stores));
    }
    
    /**
     * @param array<int,mixed> $storeIds
     * @param array<int,mixed> $data
     */
    public function linkStoresToLarkMessenger(array $storeIds, array $data): array
    {
        $this->ensureWithCompanyContext();
        
        $linkableStores = $this->getLinkableLarkMessengerStores($data['bot_webhook_url'], $data['transaction_type'], function ($builder) use ($storeIds) {
            return $builder->whereIn('id', $storeIds);
        });
        
        $links = array_merge(...array_column($linkableStores, 'links'));
        
        $notificationLarkMessengerIds = [];
        
        foreach ($links as $link) {
            $notificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);
            
            $safeNotificationLarkMessengerData = [
                'company_id' => $this->companyContext()->company->id,
                'bank_account_id' => $link['bank_account_id'],
                'sub_account_id' => $this->replaceLarkMessengerSubAccountIdForPrimaryAccount($link['bank_sub_account_id']),
                'transaction_type' => $data['transaction_type'],
                'webhook_type' => 'No',
                'verify_payment' => 'No',
                'bot_webhook_url' => $data['bot_webhook_url'],
                'description' => $data['description'],
                'hide_accumulated' => $data['hide_accumulated'] ? 1 : 0,
                'hide_details_link' => $data['hide_details_link'] ? 1 : 0,
                'is_template_custom' => $data['is_template_custom'] ? 1 : 0,
                'template_custom' => $data['template_custom'],
                'template_name' => $data['template_name'],
            ];

            $notificationLarkMessengerId = $notificationLarkMessengerModel->insert($safeNotificationLarkMessengerData);
            
            if ($notificationLarkMessengerId) {
                array_push($notificationLarkMessengerIds, $notificationLarkMessengerId);
            }
        }
        
        return $notificationLarkMessengerIds;
    }
    
    /**
     * @return void
     * @param array<int,mixed> $storeIds
     */
    public function unlinkStoresToLarkMessenger(array $storeIds, string $botWebhookUrl, string $transactionType)
    {
        $this->ensureWithCompanyContext();
        
        $linkedStores = $this->getLinkedLarkMessengerStores($botWebhookUrl, $transactionType, function ($builder) use ($storeIds) {
            return $builder->whereIn('id', $storeIds);
        });
        
        $links = array_merge(...array_column($linkedStores, 'links'));
        
        foreach ($links as $link) {
            $this->larkMessengerBuilder()
                ->where('bot_webhook_url', $botWebhookUrl)
                ->where('transaction_type', $transactionType)
                ->where('bank_account_id', $link['bank_account_id'])
                ->where('sub_account_id', $this->replaceLarkMessengerSubAccountIdForPrimaryAccount($link['bank_sub_account_id']))
                ->delete();
        }
    }
    
    public function larkMessengerBuilder(): Model
    {
        $builder = model(NotificationLarkMessengerModel::class)
            ->groupBy([
                'tb_autopay_notification_larkmessenger.bot_webhook_url', 
                'tb_autopay_notification_larkmessenger.transaction_type'
            ])
            ->orderBy('tb_autopay_notification_larkmessenger.id', 'desc');
            
        if ($this->companyContext()) {
            $builder->where('tb_autopay_notification_larkmessenger.company_id', $this->companyContext()->company->id);
        }
        
        return $builder;
    }
    
    /**
     * @param array<int,mixed> $storeIds
     */
    public function getStoreLarkMessengers(array $storeIds = [], ?string $type = null, ?callable $builderCallback = null): array
    {
        $builder = $this->larkMessengerBuilder();
        
        if ($builderCallback) {
            $builder = $builderCallback($builder);
        }
        
        $larkMessengers = $builder->get()->getResult();
        
        $larkMessengers = array_map(function ($larkMessenger) use ($storeIds, $type) {
            $larkMessenger->stores = $this->getLinkedLarkMessengerStores($larkMessenger->bot_webhook_url, $larkMessenger->transaction_type);
            $larkMessenger->transaction_type_label = $larkMessenger->transaction_type == 'All' ? 'Cả tiền vào/ra' : ($larkMessenger->transaction_type == 'In_only' ? 'Chỉ tiền vào' : 'Chỉ tiền ra');
            
            if (count($storeIds) && count(array_intersect($storeIds, array_column($larkMessenger->stores, 'id'))) != count($storeIds)) {
                return $type == 'linkable' ? $larkMessenger : false;
            }

            if ($type == 'linkable') {
                return false;
            }
            
            return $larkMessenger;
        }, $larkMessengers);
        
        $larkMessengers = array_values(array_filter($larkMessengers));
        
        return $larkMessengers;
    }

    /**
     * @param mixed $data
     */
    public function editStoreLarkMessenger(string $botWebhookUrl, string $transactionType, $data): bool
    {
        $this->ensureWithCompanyContext();
        
        if (isset($data['active'])) {
            $updateBuilder = $this->larkMessengerBuilder() 
                ->where('bot_webhook_url', $botWebhookUrl)
                ->where('transaction_type', $transactionType);
            
            return $updateBuilder->set(['active' => $data['active'] ? 1 : 0])->update();
        }
        
        $safeNotificationLarkMessengerData = array_filter([
            'transaction_type' => $data['transaction_type'] ? $data['transaction_type'] : false,
            'bot_webhook_url' => $data['bot_webhook_url'] ? $data['bot_webhook_url'] : false,
            'description' => $data['description'] ? $data['description'] : false,
            'hide_accumulated' => isset($data['hide_accumulated']) ? ($data['hide_accumulated'] ? 1 : 0) : false,
            'hide_details_link' => isset($data['hide_details_link']) ? ($data['hide_details_link'] ? 1 : 0) : false,
            'is_template_custom' => isset($data['is_template_custom']) ? ($data['is_template_custom'] ? 1 : 0) : false,
            'template_custom' => $data['template_custom'] ? $data['template_custom'] : false,
            'template_name' => $data['template_name'] ? $data['template_name'] : false,
        ], fn($value, $key) => $value !== false, ARRAY_FILTER_USE_BOTH);
        
        $updateBuilder = $this->larkMessengerBuilder() 
            ->where('bot_webhook_url', $botWebhookUrl)
            ->where('transaction_type', $transactionType);
        
        return $updateBuilder->set($safeNotificationLarkMessengerData)->update();
    }

    /**
     * @return void
     */
    public function deleteStoreLarkMessenger(string $botWebhookUrl, string $transactionType)
    {
        $this->ensureWithCompanyContext();
        
        $this->larkMessengerBuilder()->where('bot_webhook_url', $botWebhookUrl)->where('transaction_type', $transactionType)->delete();
    }
    
    public function replaceLarkMessengerSubAccountIdForPrimaryAccount($value)
    {
        if (is_array($value)) {
            return array_map(fn ($item) => !$item ? -1 : $item, $value);
        }
        
        return !$value ? -1 : $value;
    }
}