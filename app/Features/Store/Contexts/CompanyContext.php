<?php

namespace App\Features\Store\Contexts;

use App\Exceptions\StoreFeatureException;
use App\Models\CompanyModel;
use App\Features\Store\StoreFeature;

class CompanyContext
{
    public ?object $company = null;
    
    protected StoreFeature $feature;
    
    public function __construct(StoreFeature $feature, int $companyId)
    {
        $this->feature = $feature;
        
        $this->company = model(CompanyModel::class)
            ->where('id', $companyId)
            ->where('merchant_id', null)
            ->first();
        
        if (! is_object($this->company)) {
            throw new StoreFeatureException('Company not found');
        }
    }
}
