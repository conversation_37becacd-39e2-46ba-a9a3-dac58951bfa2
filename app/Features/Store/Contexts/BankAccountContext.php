<?php

namespace App\Features\Store\Contexts;

use App\Exceptions\StoreFeatureException;
use App\Features\Store\StoreFeature;
use App\Models\BankShopLinkModel;
use App\Models\BankSubAccountModel;
use App\Models\NotificationLarkMessengerModel;
use App\Models\NotificationTelegramModel;
use App\Models\NotificationViberModel;
use App\Models\UserPermissionBankModel;
use App\Models\UserPermissionBankSubModel;
use CodeIgniter\Model;
use CodeIgniter\Database\RawSql;

class BankAccountContext
{
    protected StoreFeature $feature;
    
    public ?object $bankAccount = null;
    
    public function __construct(StoreFeature $feature, int $bankAccountId)
    {
        $this->feature = $feature;
        
        $this->bankAccount = $this->feature->bankAccountBuilder()->where('tb_autopay_bank_account.id', $bankAccountId)->first();
            
        if (! is_object($this->bankAccount)) {
            throw new StoreFeatureException('Bank account not found', 404);
        }
    }
    
    public function linkToStore(?int $bankSubAccountId = null): int
    {
        $this->feature->ensureWithSpecifiedStore();
        $this->ensureWithSpecifiedBankAccount();
        
        if ($this->determineIfBankAccountRequireBankSubAccountToLinkStore() && !$bankSubAccountId) {
            throw new StoreFeatureException('Virtual account is required to link store', 400);
        }
        
        if ($bankSubAccountId) {
            $this->ensureHasBankSubAccounts([$bankSubAccountId]);
        }
        
        if ($this->determineIfLinkableToStore($bankSubAccountId)) {
            throw new StoreFeatureException('The store link exists', 409);
        }
        
        $qrcode = [
            'bank_account_id' => $this->bankAccount->id,
            'bank_sub_account_id' => $bankSubAccountId
        ];
        
        $notificationTelegramModel = model(NotificationTelegramModel::class);
        $notificationViberModel = model(NotificationViberModel::class);
        $notificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);
        
        // Handle sync to telegrams
        $telegrams = $this->feature->getStoreTelegrams([$this->feature->store->id]);
        
        foreach ($telegrams as $telegram) {
            $safeNotificationTelegramData = (array) $telegram;
            
            $safeNotificationTelegramData['bank_account_id'] = $qrcode['bank_account_id'];
            $safeNotificationTelegramData['sub_account_id'] = $this->feature->replaceTelegramSubAccountIdForPrimaryAccount($qrcode['bank_sub_account_id']);
            
            $notificationTelegramModel->insert($safeNotificationTelegramData);
        }

        // Handle sync to viber
        $viber = $this->feature->getStoreVibers([$this->feature->store->id]);

        foreach ($viber as $viber) {
            $safeNotificationViberData = (array) $viber;

            $safeNotificationViberData['bank_account_id'] = $qrcode['bank_account_id'];
            $safeNotificationViberData['sub_account_id'] = $this->feature->replaceViberSubAccountIdForPrimaryAccount($qrcode['bank_sub_account_id']);

            $notificationViberModel->insert($safeNotificationViberData);
        }

        // Handle sync to lark messengers
        $larkMessengers = $this->feature->getStoreLarkMessengers([$this->feature->store->id]);
        
        foreach ($larkMessengers as $larkMessenger) {
            $safeNotificationLarkMessengerData = (array) $larkMessenger;
            
            $safeNotificationLarkMessengerData['bank_account_id'] = $qrcode['bank_account_id'];
            $safeNotificationLarkMessengerData['sub_account_id'] = $this->feature->replaceLarkMessengerSubAccountIdForPrimaryAccount($qrcode['bank_sub_account_id']);
            
            $notificationLarkMessengerModel->insert($safeNotificationLarkMessengerData);
        }
        
        // Handle sync to staffs
        $staffs = $this->feature->getStoreStaffs([$this->feature->store->id]);
        
        foreach ($staffs as $staff) {
            try {
                model(UserPermissionBankModel::class)->insert([
                    'user_id' => $staff->id,
                    'bank_account_id' => $qrcode['bank_account_id'],
                    'hide_amount_out' => 1,
                    'hide_accumulated' => 1
                ]);
            } catch (\Exception $e) {
            }
            
            if ($qrcode['bank_sub_account_id']) {
                try {
                    model(UserPermissionBankSubModel::class)->insert([
                        'user_id' => $staff->id,
                        'sub_account_id' => $qrcode['bank_sub_account_id']
                    ]);
                } catch (\Exception $e) {
                }
            }
        }
        
        $linked = model(BankShopLinkModel::class)->insert([
            'bank_account_id' => $this->bankAccount->id,
            'shop_id' => $this->feature->store->id,
            'bank_sub_account_id' => $bankSubAccountId,
        ]);
        
        return $linked;
    }
    
    public function determineIfBankAccountRequireBankSubAccountToLinkStore(): bool
    {
        $this->ensureWithSpecifiedBankAccount();
        
        return in_array($this->bankAccount->brand_name, ['OCB', 'BIDV', 'KienLongBank', 'MSB']);
    }
    
    public function ensureWithSpecifiedBankAccount()
    {
        if (! $this->bankAccount) {
            throw new StoreFeatureException('Specified bank account is not set');
        }
    }
    
    public function ensureHasBankSubAccounts(array $bankSubAccountIds)
    {
        if (! model(BankSubAccountModel::class)->where('bank_account_id', $this->bankAccount->id)->whereIn('id', $bankSubAccountIds)->countAllResults()) {
            throw new StoreFeatureException('Bank sub account not belongs to bank account');
        }
    }
    
    public function bankSubAccountBuilder(?string $type = null): Model
    {
        $this->ensureWithSpecifiedBankAccount();
        
        if (!in_array($type, ['linkable'])) {
            $type = null;
        }
        
        $builder = model(BankSubAccountModel::class)->where('bank_account_id', $this->bankAccount->id)->orderBy('id', 'desc');
        
        if ($type === 'linkable') {
            $builder->where(new RawSql('NOT EXISTS(SELECT id FROM tb_autopay_bank_shop_link WHERE bank_sub_account_id = tb_autopay_bank_sub_account.id)'));
        }
        
        return $builder;
    }

    public function determineIfLinkableToStore(?int $bankSubAccountId = null): bool
    {
        $this->ensureWithSpecifiedBankAccount();
        
        return model(BankShopLinkModel::class)
            ->where('bank_account_id', $this->bankAccount->id)
            ->where('bank_sub_account_id', $bankSubAccountId)
            ->countAllResults() === 1;
    }
}
