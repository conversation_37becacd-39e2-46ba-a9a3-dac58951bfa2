<?php

namespace App\Features\Store;

use CodeIgniter\Model;
use App\Models\BankShopLinkModel;
use App\Models\NotificationTelegramModel;

trait HasTelegramSupport
{
    public function getLinkableTelegramStores(string $chatId, int $messageThreadId = 0, string $transactionType, ?callable $builderCallback = null)
    {
        /** @var StoreFeature $this */
        
        $builder = $this->storeBuilder();
        
        if (is_callable($builderCallback)) {
            $builder = $builderCallback($builder);
        }
        
        $stores = $builder->get()->getResult();
        
        $storeIds = array_column($stores, 'id');
        
        $storeLinks = count($storeIds) 
            ? model(BankShopLinkModel::class)->select(['shop_id', 'bank_account_id', 'bank_sub_account_id'])->whereIn('shop_id', $storeIds)->findAll() 
            : [];
        
        $stores = array_map(function ($store) use ($storeLinks, $chatId, $messageThreadId, $transactionType) {
            $links = array_values(array_filter($storeLinks, fn ($link) => $link['shop_id'] === $store->id));
            $store->links = $links;
            
            if (! count($links)) {
                return $store;
            }
            
            $linked = model(NotificationTelegramModel::class)
                ->where('chat_id', $chatId)
                ->where('message_thread_id', $messageThreadId)
                ->where('transaction_type', $transactionType)
                ->whereIn('bank_account_id', array_column($links, 'bank_account_id'))
                ->whereIn('sub_account_id', $this->replaceTelegramSubAccountIdForPrimaryAccount(array_column($links, 'bank_sub_account_id')))
                ->countAllResults() > 0;
                
            if (!$linked) {
                return $store;
            }
            
            return false;
        }, $stores);
        
        return array_values(array_filter($stores));
    }
    
    public function getLinkedTelegramStores(string $chatId, int $messageThreadId = 0, string $transactionType, ?callable $builderCallback = null)
    {
        $builder = $this->storeBuilder();
        
        if (is_callable($builderCallback)) {
            $builder = $builderCallback($builder);
        }
        
        $stores = $builder->get()->getResult();
        
        $storeIds = array_column($stores, 'id');
        
        $storeLinks = count($storeIds) 
            ? model(BankShopLinkModel::class)->select(['shop_id', 'bank_account_id', 'bank_sub_account_id'])->whereIn('shop_id', $storeIds)->findAll() 
            : [];
        
        $stores = array_map(function ($store) use ($storeLinks, $chatId, $messageThreadId, $transactionType) {
            $links = array_filter($storeLinks, fn ($link) => $link['shop_id'] === $store->id);
            $store->links = $links;
            
            if (! count($links)) {
                return false;
            }
            
            $linked = model(NotificationTelegramModel::class)
                ->where('chat_id', $chatId)
                ->where('message_thread_id', $messageThreadId)
                ->where('transaction_type', $transactionType)
                ->whereIn('bank_account_id', array_column($links, 'bank_account_id'))
                ->whereIn('sub_account_id', $this->replaceTelegramSubAccountIdForPrimaryAccount(array_column($links, 'bank_sub_account_id')))
                ->countAllResults() > 0;
                
            if (!$linked) {
                return false;
            }
            
            return $store;
        }, $stores);
        
        return array_values(array_filter($stores));
    }
    /**
     * @param array<int,mixed> $storeIds
     * @param array<int,mixed> $data
     */
    public function linkStoresToTelegram(array $storeIds, array $data): array
    {
        $this->ensureWithCompanyContext();
        
        $linkableStores = $this->getLinkableTelegramStores($data['chat_id'], $data['message_thread_id'], $data['transaction_type'], function ($builder) use ($storeIds) {
            return $builder->whereIn('id', $storeIds);
        });
        
        $links = array_merge(...array_column($linkableStores, 'links'));
        
        $existingTelegram = $this->telegramBuilder()
            ->where('chat_id', $data['chat_id'])
            ->where('message_thread_id', $data['message_thread_id'])
            ->where('transaction_type', $data['transaction_type'])
            ->first();
        
        $notificationTelegramIds = [];
        
        foreach ($links as $link) {
            $notificationTelegramModel = model(NotificationTelegramModel::class);
            
            $safeNotificationTelegramData = [
                'company_id' => $this->companyContext()->company->id,
                'bank_account_id' => $link['bank_account_id'],
                'sub_account_id' => $this->replaceTelegramSubAccountIdForPrimaryAccount($link['bank_sub_account_id']),
                'transaction_type' => $data['transaction_type'],
                'webhook_type' => 'No',
                'verify_payment' => 'No',
                'chat_id' => $data['chat_id'],
                'message_thread_id' => $data['message_thread_id'],
                'description' => $data['description'],
                'hide_accumulated' => $data['hide_accumulated'] ? 1 : 0,
                'hide_details_link' => $data['hide_details_link'] ? 1 : 0,
                'is_template_custom' => $data['is_template_custom'] ? 1 : 0,
                'template_custom' => $data['template_custom'],
                'template_name' => $data['template_name'],
                'active' => 1,
            ];

            $notificationTelegramId = $notificationTelegramModel->insert($safeNotificationTelegramData);
            
            if ($notificationTelegramId) {
                array_push($notificationTelegramIds, $notificationTelegramId);
            }
        }
        
        return $notificationTelegramIds;
    }
    /**
     * @param array<int,mixed> $storeIds
     */
    public function unlinkStoresToTelegram(array $storeIds, string $chatId, int $messageThreadId = 0, string $transactionType)
    {
        $this->ensureWithCompanyContext();
        
        $linkedStores = $this->getLinkedTelegramStores($chatId, $messageThreadId, $transactionType, function ($builder) use ($storeIds) {
            return $builder->whereIn('id', $storeIds);
        });
        
        $links = array_merge(...array_column($linkedStores, 'links'));
        
        foreach ($links as $link) {
            $this->telegramBuilder()
                ->where('chat_id', $chatId)
                ->where('message_thread_id', $messageThreadId)
                ->where('transaction_type', $transactionType)
                ->where('bank_account_id', $link['bank_account_id'])
                ->where('sub_account_id', $this->replaceTelegramSubAccountIdForPrimaryAccount($link['bank_sub_account_id']))
                ->delete();
        }
    }
    
    public function telegramBuilder(): Model
    {
        $builder = model(NotificationTelegramModel::class)
            ->groupBy([
                'tb_autopay_notification_telegram.chat_id', 
                'tb_autopay_notification_telegram.message_thread_id',
                'tb_autopay_notification_telegram.transaction_type'
            ])
            ->orderBy('tb_autopay_notification_telegram.id', 'desc');
            
        if ($this->companyContext()) {
            $builder->where('tb_autopay_notification_telegram.company_id', $this->companyContext()->company->id);
        }
        
        return $builder;
    }
    
    /**
     * @param array<int,mixed> $storeIds
     */
    public function getStoreTelegrams(array $storeIds = [], ?string $type = null, ?callable $builderCallback = null): array
    {
        $builder = $this->telegramBuilder();
        
        if ($builderCallback) {
            $builder = $builderCallback($builder);
        }
        
        $telegrams = $builder->get()->getResult();
        
        $telegrams = array_map(function ($telegram) use ($storeIds, $type) {
            $telegram->stores = $this->getLinkedTelegramStores($telegram->chat_id, $telegram->message_thread_id, $telegram->transaction_type);
            $telegram->transaction_type_label = $telegram->transaction_type == 'All' ? 'Cả tiền vào/ra' : ($telegram->transaction_type == 'In_only' ? 'Chỉ tiền vào' : 'Chỉ tiền ra');
           
            if (count($storeIds) && count(array_intersect($storeIds, array_column($telegram->stores, 'id'))) != count($storeIds)) {
                return $type == 'linkable' ? $telegram : false;
            }

            if ($type == 'linkable') {
                return false;
            }
            
            return $telegram;
        }, $telegrams);
        
        $telegrams = array_values(array_filter($telegrams));
        
        return $telegrams;
    }
    
    /**
     * @param mixed $data
     */
    public function editStoreTelegram(string $chatId, int $messageThreadId = 0, string $transactionType, $data): bool
    {
        $this->ensureWithCompanyContext();
        
        if (isset($data['active'])) {
            $updateBuilder = $this->telegramBuilder() 
                ->where('chat_id', $chatId)
                ->where('message_thread_id', $messageThreadId)
                ->where('transaction_type', $transactionType);
                
            return $updateBuilder->set(['active' => $data['active'] ? 1 : 0])->update();
        }
        
        $safeNotificationTelegramData = array_filter([
            'transaction_type' => $data['transaction_type'] ? $data['transaction_type'] : false,
            'chat_id' => $data['chat_id'] ? $data['chat_id'] : false,
            'message_thread_id' => $data['message_thread_id'] ? $data['message_thread_id'] : false,
            'description' => $data['description'] ? $data['description'] : false,
            'hide_accumulated' => isset($data['hide_accumulated']) ? ($data['hide_accumulated'] ? 1 : 0) : false,
            'hide_details_link' => isset($data['hide_details_link']) ? ($data['hide_details_link'] ? 1 : 0) : false,
            'is_template_custom' => isset($data['is_template_custom']) ? ($data['is_template_custom'] ? 1 : 0) : false,
            'template_custom' => $data['template_custom'] ? $data['template_custom'] : false,
            'template_name' => $data['template_name'] ? $data['template_name'] : false,
        ], fn($value, $key) => $value !== false, ARRAY_FILTER_USE_BOTH);
        
        $updateBuilder = $this->telegramBuilder() 
            ->where('chat_id', $chatId)
            ->where('message_thread_id', $messageThreadId)
            ->where('transaction_type', $transactionType);

        return $updateBuilder->set($safeNotificationTelegramData)->update();
    }
    
    public function deleteStoreTelegram(string $chatId, int $messageThreadId = 0, string $transactionType)
    {
        $this->ensureWithCompanyContext();
        
        $this->telegramBuilder()->where('chat_id', $chatId)->where('message_thread_id', $messageThreadId)->where('transaction_type', $transactionType)->delete();
    }
    
    public function replaceTelegramSubAccountIdForPrimaryAccount($value)
    {
        if (is_array($value)) {
            return array_map(fn ($item) => !$item ? -1 : $item, $value);
        }
        
        return !$value ? -1 : $value;
    }
    
}