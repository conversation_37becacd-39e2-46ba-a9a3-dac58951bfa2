<?php

namespace App\Features\Store;

use App\Models\OutputDeviceModel;
use CodeIgniter\Model;

trait HasSpeakerSupport
{
    public function linkStoreToSpeaker(int $speakerId): bool
    {
        /** @var StoreFeature $this */
        $this->ensureWithCompanyContext();
        $this->ensureWithSpecifiedStore();
        
        $speakerModel = model(OutputDeviceModel::class);
        
        $speaker = $speakerModel
            ->where('id', $speakerId)
            ->where('company_id', $this->companyContext()->company->id)
            ->first();
            
        if (!$speaker) {
            throw new \Exception('Speaker not found', 404);
        }
        
        return $speakerModel
            ->where('id', $speakerId)
            ->set(['shop_id' => $this->store->id])
            ->update();
    }
    
    public function speakerBuilder(): Model
    {
        /** @var StoreFeature $this */

        $this->ensureWithCompanyContext();
        
        $builder = model(OutputDeviceModel::class)
            ->select(['id', 'name', 'name', 'vendor', 'model', 'active', 'shop_id', 'serial_number'])
            ->where('tb_autopay_output_device.company_id', $this->companyContext()->company->id)
            ->orderBy('tb_autopay_output_device.id', 'DESC');
    
        return $builder;
    }
    
    public function getStoreSpeakers(array $storeIds = [], ?string $type = null, ?callable $builderCallback = null): array
    {
        /** @var StoreFeature $this */
        
        $builder = $this->speakerBuilder();
        
        if ($builderCallback) {
            $builder = $builderCallback($builder);
        }
        
        $speakers = $builder->get()->getResult();
        
        $speakers = array_map(function ($speaker) use ($storeIds, $type) {
            if (count($storeIds) && !in_array($speaker->shop_id, $storeIds)) {
                return $type == 'linkable' ? $speaker : false;
            }

            if ($type == 'linkable') {
                return false;
            }
            
            return $speaker;
        }, $speakers);
        
        $speakers = array_values(array_filter($speakers));
        
        return $speakers;
    }
}