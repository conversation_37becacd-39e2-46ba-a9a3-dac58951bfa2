<?php

namespace App\Features\Store;

use CodeIgniter\Model;
use App\Models\BankShopLinkModel;
use App\Models\NotificationViberModel;

trait HasViberSupport
{
    public function getLinkableViberStores(string $channelId, string $transactionType, ?callable $builderCallback = null)
    {
        /** @var StoreFeature $this */

        $builder = $this->storeBuilder();

        if (is_callable($builderCallback)) {
            $builder = $builderCallback($builder);
        }

        $stores = $builder->get()->getResult();

        $storeIds = array_column($stores, 'id');

        $storeLinks = count($storeIds)
            ? model(BankShopLinkModel::class)->select(['shop_id', 'bank_account_id', 'bank_sub_account_id'])->whereIn('shop_id', $storeIds)->findAll()
            : [];

        $stores = array_map(function ($store) use ($storeLinks, $channelId, $transactionType) {
            $links = array_values(array_filter($storeLinks, fn($link) => $link['shop_id'] === $store->id));
            $store->links = $links;

            if (! count($links)) {
                return $store;
            }

            $linked = model(NotificationViberModel::class)
                ->where('channel_id', $channelId)
                ->where('transaction_type', $transactionType)
                ->whereIn('bank_account_id', array_column($links, 'bank_account_id'))
                ->whereIn('sub_account_id', $this->replaceViberSubAccountIdForPrimaryAccount(array_column($links, 'bank_sub_account_id')))
                ->countAllResults() > 0;

            if (!$linked) {
                return $store;
            }

            return false;
        }, $stores);

        return array_values(array_filter($stores));
    }

    public function getLinkedViberStores(string $channelId, string $transactionType, ?callable $builderCallback = null)
    {
        $builder = $this->storeBuilder();

        if (is_callable($builderCallback)) {
            $builder = $builderCallback($builder);
        }

        $stores = $builder->get()->getResult();

        $storeIds = array_column($stores, 'id');

        $storeLinks = count($storeIds)
            ? model(BankShopLinkModel::class)->select(['shop_id', 'bank_account_id', 'bank_sub_account_id'])->whereIn('shop_id', $storeIds)->findAll()
            : [];

        $stores = array_map(function ($store) use ($storeLinks, $channelId, $transactionType) {
            $links = array_filter($storeLinks, fn($link) => $link['shop_id'] === $store->id);
            $store->links = $links;

            if (! count($links)) {
                return false;
            }

            $linked = model(NotificationViberModel::class)
                ->where('channel_id', $channelId)
                ->where('transaction_type', $transactionType)
                ->whereIn('bank_account_id', array_column($links, 'bank_account_id'))
                ->whereIn('sub_account_id', $this->replaceViberSubAccountIdForPrimaryAccount(array_column($links, 'bank_sub_account_id')))
                ->countAllResults() > 0;

            if (!$linked) {
                return false;
            }

            return $store;
        }, $stores);

        return array_values(array_filter($stores));
    }

    /**
     * @param array<int,mixed> $storeIds
     * @param array<int,mixed> $data
     */
    public function linkStoresToViber(array $storeIds, array $data): array
    {
        $this->ensureWithCompanyContext();

        $linkableStores = $this->getLinkableViberStores($data['channel_id'], $data['transaction_type'], function ($builder) use ($storeIds) {
            return $builder->whereIn('id', $storeIds);
        });

        $links = array_merge(...array_column($linkableStores, 'links'));

        $existingViber = $this->viberBuilder()
            ->where('channel_id', $data['channel_id'])
            ->where('transaction_type', $data['transaction_type'])
            ->first();

        $notificationViberIds = [];

        $notificationViberModel = model(NotificationViberModel::class);

        foreach ($links as $link) {
            $safeNotificationViberData = array_filter([
                'company_id' => $this->companyContext()->company->id,
                'bank_account_id' => $link['bank_account_id'],
                'sub_account_id' => $this->replaceViberSubAccountIdForPrimaryAccount($link['bank_sub_account_id']),
                'channel_id' => $data['channel_id'],
                'auth_token' => $data['auth_token'] ?? ($existingViber->auth_token ?? null),
                'sender_id' => $data['sender_id'] ?? ($existingViber->sender_id ?? null),
                'description' => $data['channel_name'] ?? ($existingViber->description ?? null),
                'transaction_type' => $data['transaction_type'],
                'webhook_type' => $data['webhook_type'] ?? ($existingViber->webhook_type ?? 'All'),
                'verify_payment' => $data['verify_payment'] ?? ($existingViber->verify_payment ?? 'Skip'),
                'amount_in_great_than_equal_to' => $data['amount_gte'] ?? ($existingViber->amount_in_great_than_equal_to ?? 0),
                'amount_in_less_than_equal_to' => $data['amount_lte'] ?? ($existingViber->amount_in_less_than_equal_to ?? 0),
                'contains_content' => $data['contains_content'] ?? ($existingViber->contains_content ?? null),
                'ignore_phrases' => $data['ignore_phrases'] ?? ($existingViber->ignore_phrases ?? null),
                'hide_accumulated' => $data['hide_accumulated'] ?? ($existingViber->hide_accumulated ?? 0),
                'hide_details_link' => $data['hide_details_link'] ?? ($existingViber->hide_details_link ?? 0),
                'is_template_custom' => $data['is_template_custom'] ?? ($existingViber->is_template_custom ?? 0),
                'template_custom' => $data['template_custom'] ?? ($existingViber->template_custom ?? null),
                'template_name' => $data['template_name'] ?? ($existingViber->template_name ?? 'template_1'),
                'active' => $data['active'] ?? ($existingViber->active ?? 1),
            ], fn($value, $key) => $value !== null, ARRAY_FILTER_USE_BOTH);

            $notificationViberId = $notificationViberModel->insert($safeNotificationViberData);

            if ($notificationViberId) {
                array_push($notificationViberIds, $notificationViberId);
            }
        }

        return $notificationViberIds;
    }

    /**
     * @param array<int,mixed> $storeIds
     */
    public function unlinkStoresToViber(array $storeIds, string $channelId, string $transactionType)
    {
        $this->ensureWithCompanyContext();

        $linkedStores = $this->getLinkedViberStores($channelId, $transactionType, function ($builder) use ($storeIds) {
            return $builder->whereIn('id', $storeIds);
        });

        $links = array_merge(...array_column($linkedStores, 'links'));

        foreach ($links as $link) {
            model(NotificationViberModel::class)
                ->where('company_id', $this->companyContext()->company->id)
                ->where('channel_id', $channelId)
                ->where('transaction_type', $transactionType)
                ->where('bank_account_id', $link['bank_account_id'])
                ->where('sub_account_id', $this->replaceViberSubAccountIdForPrimaryAccount($link['bank_sub_account_id']))
                ->delete();
        }
    }

    public function viberBuilder(): Model
    {
        $builder = model(NotificationViberModel::class)
            ->groupBy([
                'tb_autopay_notification_viber.channel_id',
                'tb_autopay_notification_viber.transaction_type'
            ])
            ->orderBy('tb_autopay_notification_viber.id', 'desc');

        if ($this->companyContext()) {
            $builder->where('tb_autopay_notification_viber.company_id', $this->companyContext()->company->id);
        }

        return $builder;
    }

    /**
     * @param array<int,mixed> $storeIds
     */
    public function getStoreVibers(array $storeIds = [], ?string $type = null, ?callable $builderCallback = null): array
    {
        $builder = $this->viberBuilder();

        if ($builderCallback) {
            $builder = $builderCallback($builder);
        }

        $vibers = $builder->findAll();

        $vibers = array_map(function ($viber) use ($storeIds, $type) {
            $viber->stores = $this->getLinkedViberStores($viber->channel_id, $viber->transaction_type);
            $viber->transaction_type_label = $viber->transaction_type == 'All' ? 'Cả tiền vào/ra' : ($viber->transaction_type == 'In_only' ? 'Chỉ tiền vào' : 'Chỉ tiền ra');

            if (count($storeIds) && count(array_intersect($storeIds, array_column($viber->stores, 'id'))) != count($storeIds)) {
                return $type == 'linkable' ? $viber : false;
            }

            if ($type == 'linkable') {
                return false;
            }

            return $viber;
        }, $vibers);

        return array_values(array_filter($vibers));
    }

    /**
     * @param mixed $data
     */
    public function editStoreViber(string $channelId, string $transactionType, $data): bool
    {
        $this->ensureWithCompanyContext();

        if (isset($data['active'])) {
            $updateBuilder = $this->viberBuilder()
                ->where('channel_id', $channelId)
                ->where('transaction_type', $transactionType);

            return $updateBuilder->set(['active' => $data['active'] ? 1 : 0])->update();
        }

        $safeNotificationViberData = array_filter([
            'channel_id' => $data['channel_id'] ? $data['channel_id'] : false,
            'description' => $data['description'] ? $data['description'] : false,
            'auth_token' => $data['auth_token'] ? $data['auth_token'] : false,
            'transaction_type' => $data['transaction_type'] ? $data['transaction_type'] : false,
            'hide_accumulated' => isset($data['hide_accumulated']) ? ($data['hide_accumulated'] ? 1 : 0) : false,
            'hide_details_link' => isset($data['hide_details_link']) ? ($data['hide_details_link'] ? 1 : 0) : false,
            'is_template_custom' => isset($data['is_template_custom']) ? ($data['is_template_custom'] ? 1 : 0) : false,
            'template_custom' => $data['template_custom'] ? $data['template_custom'] : false,
            'template_name' => $data['template_name'] ? $data['template_name'] : false,
        ], fn($value, $key) => $value !== false, ARRAY_FILTER_USE_BOTH);

        $updateBuilder = $this->viberBuilder()
            ->where('channel_id', $channelId)
            ->where('transaction_type', $transactionType);

        return $updateBuilder->set($safeNotificationViberData)->update();
    }

    public function deleteStoreViber(string $channelId, string $transactionType)
    {
        $this->ensureWithCompanyContext();

        $this->viberBuilder()->where('channel_id', $channelId)->where('transaction_type', $transactionType)->delete();
    }

    public function replaceViberSubAccountIdForPrimaryAccount($subAccountIds)
    {
        if (is_array($subAccountIds)) {
            return array_map(fn ($item) => !$item ? -1 : $item, $subAccountIds);
        }
        
        return !$subAccountIds ? -1 : $subAccountIds;
    }
}
