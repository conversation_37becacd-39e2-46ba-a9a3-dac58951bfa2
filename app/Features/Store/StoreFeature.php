<?php

namespace App\Features\Store;

use App\Controllers\Notificationtelegram;
use App\Features\Store\Contexts\CompanyContext;
use App\Exceptions\StoreFeatureException;
use App\Features\Store\Contexts\BankAccountContext;
use App\Models\BankAccountCashflowModel;
use App\Models\ShopModel;
use App\Models\UserModel;
use CodeIgniter\Model;
use App\Models\BankAccountModel;
use App\Models\BankIntegrationOutputDeviceModel;
use App\Models\BankShopLinkModel;
use App\Models\BankSubAccountCashflowModel;
use App\Models\BankSubAccountModel;
use App\Models\CompanyUserModel;
use App\Models\CounterModel;
use App\Models\FcmLogModel;
use App\Models\NotificationLarkMessengerModel;
use App\Models\NotificationLarkMessengerQueueModel;
use App\Models\NotificationTelegramModel;
use App\Models\NotificationTelegramQueueModel;
use App\Models\NotificationViberModel;
use App\Models\OutputDeviceReplayMessageQueueModel;
use App\Models\TransactionsModel;
use App\Models\UserPermissionBankModel;
use App\Models\UserPermissionBankSubModel;
use App\Models\UserPermissionFeatureModel;
use CodeIgniter\Database\RawSql;

class StoreFeature
{
    use HasTelegramSupport;
    use HasViberSupport;
    use HasLarkMessengerSupport;
    use HasStaffSupport;
    use HasSpeakerSupport;
    
    public ?CompanyContext $companyContext = null;
    
    public ?BankAccountContext $bankAccountContext = null;
    
    public ?object $store = null;
    
    public function __construct(?int $storeId = null)
    {
        if ($storeId) {
            $this->store = model(ShopModel::class)->where('id', $storeId)->first();
        }
    }
    
    public function ensureWithSpecifiedStore()
    {
        if (! $this->store) {
            throw new StoreFeatureException('Specified store is not set');
        }
    }
    
    public function withCompanyContext(int $companyId)
    {
        try {
            $this->companyContext = new CompanyContext($this, $companyId);
            
            if ($this->store && $this->store->company_id != $companyId) {
                throw new StoreFeatureException('Store does not belong to the company');
            }
        } catch (\Exception $e) {
            $this->companyContext = null;
        }
        
        return $this;
    }
    
    public function ensureWithCompanyContext()
    {
        if (! $this->companyContext) {
            throw new StoreFeatureException('Company context is not set');
        }
    }
    
    public function companyContext(): CompanyContext
    {
        $this->ensureWithCompanyContext();
        
        return $this->companyContext;
    }
    
    public function withBankAccountContext(int $bankAccountId)
    {
        try {
            $this->bankAccountContext = new BankAccountContext($this, $bankAccountId);
        } catch (\Exception $e) {
            $this->companyContext = null;
        }
        
        return $this;
    }
    
    public function ensureWithBankAccountContext()
    {
        if (! $this->bankAccountContext) {
            throw new StoreFeatureException('Bank account context is not set');
        }
    }
    
    public function bankAccountContext(): BankAccountContext
    {
        $this->ensureWithBankAccountContext();
        
        return $this->bankAccountContext;
    }
    
    public function bankAccountBuilder(?string $type = null): Model
    {
        if (!in_array($type, ['linkable'])) {
            $type = null;
        }
        
        $builder = model(BankAccountModel::class)
            ->select([
                'tb_autopay_bank_account.id', 'tb_autopay_bank_account.bank_id', 'tb_autopay_bank_account.account_number', 'tb_autopay_bank_account.account_holder_name', 'tb_autopay_bank.icon_path', 'tb_autopay_bank.bin', 'tb_autopay_bank.brand_name', 'tb_autopay_bank.short_name', 'tb_autopay_bank_account.label', 'tb_autopay_bank_account.active'
            ])
            ->join('tb_autopay_bank', 'tb_autopay_bank.id = tb_autopay_bank_account.bank_id')
            ->where('tb_autopay_bank_account.active', 1)
            ->where('tb_autopay_bank_account.bank_api_connected', 1)
            ->orderBy('id', 'desc');
            
        if ($this->companyContext()) {
            $builder->where('tb_autopay_bank_account.company_id', $this->companyContext()->company->id);
        }
        
        return $builder;
    }
    
    public function aliasBankSubAccountLabelFriendly(string $bankBrandName): string
    {
        return in_array($bankBrandName, ['BIDV', 'OCB', 'MSB', 'KienLongBank', 'MBBank']) ? 'Số tài khoản nhận thanh toán' : 'Mã thanh toán';
    }
    
    public function qrCodeBuilder(): Model
    {
        $this->ensureWithSpecifiedStore();
        $this->ensureWithCompanyContext();
        
        $builder = model(BankShopLinkModel::class)
            ->select([
                'tb_autopay_bank_shop_link.id',
                'tb_autopay_bank.brand_name', 'tb_autopay_bank.bin', 'tb_autopay_bank.icon_path',
                'tb_autopay_bank_account.account_number', 'tb_autopay_bank_account.account_holder_name', 'tb_autopay_bank_account.company_id', 'tb_autopay_bank_account.bank_id',
                'tb_autopay_bank_sub_account.sub_account', 'tb_autopay_bank_sub_account.acc_type',
            ])
            ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id = tb_autopay_bank_shop_link.bank_account_id')
            ->join('tb_autopay_bank', 'tb_autopay_bank.id = tb_autopay_bank_account.bank_id')
            ->join('tb_autopay_bank_sub_account', 'tb_autopay_bank_sub_account.id = tb_autopay_bank_shop_link.bank_sub_account_id', 'left')
            ->where('tb_autopay_bank_shop_link.shop_id', $this->store->id)
            ->where('tb_autopay_bank_account.company_id', $this->companyContext()->company->id)
            ->orderBy('id', 'desc');
        
        return $builder;
    }
    
    public function resolveQrCodeUrl(string $brandName, string $bin, string $accountNumber, ?string $subAccount = null, ?string $subAccountType = null)
    {
        $remark = '';
        
        if ($brandName === 'VietinBank') {
            $remark .= 'SEVQR ';
        }
        
        if ($subAccount && $subAccountType === 'Virtual') {
            $remark .=  'TKP' . $subAccount;
        }
        
        if ($subAccount && $subAccountType === 'Real') {
            $accountNumber = $subAccount;
        }
        
        return sprintf('https://qr.sepay.vn/img?bank=%s&acc=%s&template=&des=%s', $bin, $accountNumber, $remark);
    }
    
    public function unlinkQrcode(int $qrcodeId): int
    {
        $this->ensureWithSpecifiedStore();
        $this->ensureWithCompanyContext();
        
        $qrcode = model(BankShopLinkModel::class)
            ->join('tb_autopay_shop', 'tb_autopay_shop.id = tb_autopay_bank_shop_link.shop_id')
            ->where('tb_autopay_bank_shop_link.id', $qrcodeId)
            ->where('tb_autopay_bank_shop_link.shop_id', $this->store->id)
            ->first();
            
        if (!$qrcode) {
            throw new \Exception('QR code not found');
        }
        
        $storeLinks = model(BankShopLinkModel::class)
            ->select(['shop_id', 'bank_account_id', 'bank_sub_account_id'])
            ->where('shop_id', $qrcode['shop_id'])
            ->where('id !=', $qrcodeId)
            ->findAll();
            
        $companyId = $this->companyContext()->company->id;
        
        $notificationTelegramModel = model(NotificationTelegramModel::class);
        $notificationViberModel = model(NotificationViberModel::class);
        $notificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);
        
        $bankSubAccountIds = array_column($storeLinks, 'bank_sub_account_id');
        $primaryQrcode = $qrcode['bank_sub_account_id'] == 0;
        
        // Handle sync to Telegrams
        $notificationTelegramModel
            ->where('company_id', $companyId)
            ->where('bank_account_id', $qrcode['bank_account_id'])
            ->where('sub_account_id', $this->replaceTelegramSubAccountIdForPrimaryAccount($qrcode['bank_sub_account_id']))
            ->delete();

        // Handle sync to Viber
        $notificationViberModel
            ->where('company_id', $companyId)
            ->where('bank_account_id', $qrcode['bank_account_id'])
            ->where('sub_account_id', $this->replaceViberSubAccountIdForPrimaryAccount($qrcode['bank_sub_account_id']))
            ->delete();
            
        // Handle sync to Lark messengers
        $notificationLarkMessengerModel
            ->where('company_id', $companyId)
            ->where('bank_account_id', $qrcode['bank_account_id'])
            ->where('sub_account_id', $this->replaceLarkMessengerSubAccountIdForPrimaryAccount($qrcode['bank_sub_account_id']))
            ->delete();
            
        // Handle sync to staffs
        $staffs = $this->getStoreStaffs([$qrcode['shop_id']]);
        
        if (count($staffs)) {
            $determineBankAccountIfHasAnySubAccount = count($bankSubAccountIds) ? model(BankSubAccountModel::class)
                ->where('bank_account_id', $qrcode['bank_account_id'])
                ->whereIn('id', $bankSubAccountIds)
                ->countAllResults() > 0 : false;
                
            if ($primaryQrcode && !$determineBankAccountIfHasAnySubAccount) {
                model(UserPermissionBankModel::class)
                    ->where('bank_account_id', $qrcode['bank_account_id'])
                    ->whereIn('user_id', array_column($staffs, 'id'))
                    ->delete();
            }
            
            if (!$primaryQrcode) {
                model(UserPermissionBankSubModel::class)
                    ->where('sub_account_id', $qrcode['bank_sub_account_id'])
                    ->whereIn('user_id', array_column($staffs, 'id'))
                    ->delete();
            }
        }
        
        // Handle sync to speakers
        model(BankIntegrationOutputDeviceModel::class)
            ->where('bank_account_id', $qrcode['bank_account_id'])
            ->where('sub_account_id', $qrcode['bank_sub_account_id'] ? $qrcode['bank_sub_account_id'] : null)
            ->where('company_id', $companyId)
            ->delete();
        
        $deleted = model(BankShopLinkModel::class)
            ->where('id', $qrcodeId)
            ->where('shop_id', $this->store->id)
            ->delete();
            
        // TODO: handle delete sub account & transaction
        
        return $deleted;
    }
    
    public function storeBuilder(?string $type = null): Model
    {
        $builder = model(ShopModel::class)->orderBy('tb_autopay_shop.id', 'DESC');

        if ($this->companyContext) {
            $builder->where('tb_autopay_shop.company_id', $this->companyContext()->company->id);
        }
        
        return $builder;
    }
    
    public function getStores(?callable $builderCallback = null): array
    {
        $builder = $this->storeBuilder();

        if (is_callable($builderCallback)) {
            $builder = $builderCallback($builder);
        }
        
        $stores = $builder->get()->getResult();
        
        if (count($stores)) {
            $revenues = $this->getStoreRevenues(array_column($stores, 'id'), date('Y-m-d'));
        } else {
            $revenues = [];
        }
        
        $stores = array_map(function ($store) use ($revenues) {
            $revenue = array_values(array_filter($revenues, fn ($revenue) => $revenue['id'] == $store->id))[0]['revenue'] ?? 0;
            $store->total_amount_in = $revenue;
            return $store;
        }, $stores);
        
        return $stores;
    }
    
    public function getCashflowStore(int $storeId, ?string $startDate = null, ?string $endDate = null): array
    {
        $this->ensureWithCompanyContext();
        
        $links = model(BankShopLinkModel::class)->where('shop_id', $storeId)->get()->getResult();
        
        if (!count($links)) {
            return [];
        }
        
        $builder = slavable_model(BankAccountCashflowModel::class, 'Transactions')
            ->select(['sum(total_amount_in) as `total_amount_in`, sum(total_amount_out) as `total_amount_out`'])
            ->where('company_id', $this->companyContext()->company->id)
            ->where('date >=', date('Y-m-d', strtotime($startDate)))
            ->whereIn('bank_account_id', array_column($links, 'bank_account_id'));
            
        if ($endDate) {
            $builder->where('date <=', date('Y-m-d', strtotime($endDate)));
        }    
            
        $result = $builder->get()->getRow();
        
        return ['total_amount_in' => $result->total_amount_in, 'total_amount_out' => $result->total_amount_out];
    }
    
    public function getLinkedBankAccountStores(int $bankAccountId, ?callable $builderCallback = null): array
    {
        /** @var StoreFeature $this */
        
        $builder = $this->storeBuilder();
        
        if (is_callable($builderCallback)) {
            $builder = $builderCallback($builder);
        }
        
        $stores = $builder->get()->getResult();
        
        $storeIds = array_column($stores, 'id');
        
        $storeLinks = count($storeIds)
            ? model(BankShopLinkModel::class)->select(['shop_id', 'bank_account_id', 'bank_sub_account_id'])->whereIn('shop_id', $storeIds)->where('bank_account_id', $bankAccountId)->findAll() 
            : [];
        
        $stores = array_map(function ($store) use ($storeLinks, $bankAccountId) {
            $links = array_filter($storeLinks, fn ($link) => $link['shop_id'] === $store->id);
            
            if (!count($links)) {
                return false;
            }
            
            return $store;
        }, $stores);
        
        return array_values(array_filter($stores));
    }
    
    /**
     * @param array<int,mixed> $storeIds
     */
    public function getBankAccounts(array $storeIds, ?callable $builderCallback = null): array
    {
        /** @var StoreFeature $this */
        
        $builder = $this->bankAccountBuilder();
        
        if ($builderCallback) {
            $builder = $builderCallback($builder);
        }
        
        $bankAccounts = $builder->get()->getResult();
        
        $bankAccounts = array_map(function ($bankAccount) use ($storeIds) {
            $bankAccount->stores = $this->getLinkedBankAccountStores($bankAccount->id);
            
            $bankAccount->linked = !(count($storeIds) && !count(array_intersect($storeIds, array_column($bankAccount->stores, 'id'))));
            
            return $bankAccount;
        }, $bankAccounts);
        
        $bankAccounts = array_values(array_filter($bankAccounts));
        
        return $bankAccounts;
    }

    public function editStore(array $data): bool
    {
        $this->ensureWithSpecifiedStore();
        $this->ensureWithCompanyContext();
        
        $safeStoreData = array_filter([
            'name' => $data['name'] ,
            'address' => $data['address'],
        ], fn ($value) => $value !== null);
        
        if (!count($safeStoreData)) {
            return true;
        }
        
        return model(ShopModel::class)
            ->where('company_id', $this->companyContext()->company->id)
            ->where('id', $this->store->id)
            ->set($safeStoreData)->update();
    }
    
    public function deleteStore()
    {
        $this->ensureWithSpecifiedStore();
        $this->ensureWithCompanyContext();
        
        foreach ($this->getStoreTelegrams([$this->store->id]) as $telegram) {
            $this->unlinkStoresToTelegram([$this->store->id], $telegram->chat_id, $telegram->message_thread_id, $telegram->transaction_type);
        }
        
        foreach ($this->getStoreLarkMessengers([$this->store->id]) as $larkMessenger) {
            $this->unlinkStoresToLarkMessenger([$this->store->id], $larkMessenger->botWebhookUrl, $larkMessenger->transaction_type);
        }
        
        foreach ($this->getStoreStaffs([$this->store->id]) as $staff) {
            $this->unlinkStoresToStaff([$this->store->id], $staff->id);
        }
        
        $qrcodes = model(BankShopLinkModel::class)
            ->select(['tb_autopay_bank_shop_link.*', 'tb_autopay_bank_sub_account.sub_account'])
            ->join('tb_autopay_bank_sub_account', 'tb_autopay_bank_sub_account.id = tb_autopay_bank_shop_link.bank_sub_account_id', 'left')
            ->where('shop_id', $this->store->id)
            ->get()->getResult();
        
        $transactions = [];
        
        foreach ($qrcodes as $qrcode) {
            $qrcodeTransactionBuilder = slavable_model(TransactionsModel::class, 'Transactions')
                ->where('bank_account_id', $qrcode->bank_account_id);
                
            if ($qrcode->sub_account) {
                $qrcodeTransactionBuilder->where('sub_account', $qrcode->sub_account);
            } else {
                $qrcodeTransactionBuilder->groupStart()
                    ->where('sub_account', null)
                    ->orWhere('sub_account', '')
                ->groupEnd();
            }
                
            $qrcodeTransactions = $qrcodeTransactionBuilder->get()->getResult();
                
            if (!count($qrcodeTransactions)) continue;
                
            $transactions = array_merge($transactions, $qrcodeTransactions);
        }
        
        if (count($transactions)) {
            model(TransactionsModel::class)->delete(array_column($transactions, 'id'));
    
            add_system_log([
                'company_id' => $this->companyContext()->company->id,
                'data_type' => 'soft_delete_all_transactions',
                'description' => 'Soft delete all transactions',
                'level' => 'Info',
                'by' => StoreFeature::class, 
            ]);
        }
        
        if (count($qrcodeTransactions)) {
            model(BankShopLinkModel::class)->delete(array_column($qrcodeTransactions, 'id'));
        }
        
        model(ShopModel::class)
            ->where('company_id', $this->companyContext()->company->id)
            ->where('id', $this->store->id)
            ->delete();
    }
    
    /**
     * Get transactions for specific stores with filtering and sorting
     * 
     * @param array $storeIds Array of store IDs to filter transactions
     * @param array $filter Filter options (amount_in, amount_out, start_date, end_date, bank_account_id, sub_code)
     * @param array $sorter Sorting options (amount_in, amount_out, start_date, end_date)
     * @param array $datatableParams DataTables.js parameters (draw, start, length, search, order)
     * @return array Array of transaction records with DataTables.js format
     */
    public function getTransactions(int $userId, array $storeIds = [], array $filter = [], array $datatableParams = []): array
    {
        $this->ensureWithCompanyContext();
        
        $user = model(UserModel::class)
            ->select(['tb_autopay_user.*', 'tb_autopay_company_user.role', 'tb_autopay_company_user.company_id'])
            ->join('tb_autopay_company_user', 'tb_autopay_company_user.user_id = tb_autopay_user.id')
            ->where('tb_autopay_company_user.company_id', $this->companyContext()->company->id)
            ->where('tb_autopay_user.id', $userId)
            ->first();
            
        if (!$user) {
            throw new \Exception('User not found');
        }
        
        $builder = slavable_model(TransactionsModel::class, 'Transactions')
            ->select([
                'tb_autopay_sms_parsed.id',
                'tb_autopay_sms_parsed.bank_account_id',
                'tb_autopay_sms_parsed.amount_in',
                'tb_autopay_sms_parsed.amount_out',
                'tb_autopay_sms_parsed.transaction_date',
                'tb_autopay_bank.brand_name',
                'tb_autopay_bank.icon_path',
                'tb_autopay_bank_account.account_number',
                'tb_autopay_bank_account.account_holder_name',
                'tb_autopay_sms_parsed.sub_account',
                'tb_autopay_sms_parsed.chat_push_message',
                'tb_autopay_sms_parsed.transaction_content',
                'tb_autopay_sms_parsed.datecreated',
                'tb_autopay_bank_shop_link.shop_id',
            ])
            ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id = tb_autopay_sms_parsed.bank_account_id')
            ->join('tb_autopay_bank', 'tb_autopay_bank.brand_name = tb_autopay_sms_parsed.gateway')
            ->join('tb_autopay_bank_sub_account', 'tb_autopay_bank_sub_account.sub_account = tb_autopay_sms_parsed.sub_account', 'left')
            ->join('tb_autopay_bank_shop_link', 'tb_autopay_bank_shop_link.bank_account_id = tb_autopay_sms_parsed.bank_account_id and (tb_autopay_bank_shop_link.bank_sub_account_id = tb_autopay_bank_sub_account.id or (tb_autopay_bank_shop_link.bank_sub_account_id IS NULL AND tb_autopay_bank_sub_account.id IS NULL))')
            ->where('tb_autopay_bank_account.company_id', $this->companyContext()->company->id)
            ->where('tb_autopay_sms_parsed.parser_status', 'Success');
            
        if ($user->role == 'User') {
            $builder->where('tb_autopay_sms_parsed.amount_in >', 0);
        }
        
        if (count($storeIds)) {
            $builder->whereIn('tb_autopay_bank_shop_link.shop_id', $storeIds);
        }
        
        if (isset($filter['qrcode_ids']) && count($filter['qrcode_ids'])) {
            $builder->whereIn('tb_autopay_bank_shop_link.id', $filter['qrcode_ids']);
        }

        if (isset($filter['transfer_type']) && $filter['transfer_type'] === 'in') {
            $builder->where('tb_autopay_sms_parsed.amount_in >', 0)->where('amount_out', 0);
        }
        
        if (isset($filter['transfer_type']) && $filter['transfer_type'] === 'out') {
            $builder->where('tb_autopay_sms_parsed.amount_out >', 0)->where('amount_in', 0);
        }

        if (!empty($filter['transaction_date'])) {
            $dateParts = explode(' ', $filter['transaction_date']);
            
            $startDate = date('Y-m-d', strtotime($dateParts[0])) . ' 00:00:00';
            
            $builder->where('tb_autopay_sms_parsed.transaction_date >=', $startDate);
            
            if (count($dateParts) === 2) {
                $endDate = date('Y-m-d', strtotime($dateParts[1])) . ' 23:59:59';
                
                $builder->where('tb_autopay_sms_parsed.transaction_date <=', $endDate);
            }
        }

        // Apply DataTables search
        if (!empty($datatableParams['search']['value'])) {
            $searchValue = $datatableParams['search']['value'];
            $builder->groupStart()
                ->like('tb_autopay_bank_account.account_number', $searchValue)
                ->orLike('tb_autopay_sms_parsed.sub_account', $searchValue)
                ->orLike('tb_autopay_sms_parsed.amount_in', $searchValue)
                ->orLike('tb_autopay_sms_parsed.amount_out', $searchValue)
                ->orLike('tb_autopay_sms_parsed.id', $searchValue)
                ->orLike('tb_autopay_sms_parsed.transaction_content', $searchValue)
                ->orLike('tb_autopay_sms_parsed.reference_number', $searchValue)
                ->groupEnd();
        }

        // Get total records count before pagination
        $recordsTotal = $builder->countAllResults(false);

        // Apply DataTables sorting
        if (!empty($datatableParams['order'])) {
            foreach ($datatableParams['order'] as $order) {
                $columnIndex = $order['column'];
                $direction = strtoupper($order['dir']);
                
                // Map column index to actual column name based on frontend columns
                $columnMap = [
                    1 => 'tb_autopay_sms_parsed.account_number', // QR Code column
                    2 => 'tb_autopay_sms_parsed.amount_in', // Amount column
                    3 => 'tb_autopay_sms_parsed.transaction_date', // Time column
                ];
                
                if (isset($columnMap[$columnIndex])) {
                    if ($columnMap[$columnIndex] == 'tb_autopay_sms_parsed.account_number') {
                        $builder->orderBy('tb_autopay_sms_parsed.sub_account', $direction);
                    }
                    
                    $builder->orderBy($columnMap[$columnIndex], $direction);
                }
            }
        } else {
            // Default sorting by ID descending
            $builder->orderBy('tb_autopay_sms_parsed.id', 'DESC');
        }

        // Apply DataTables pagination
        if (isset($datatableParams['start']) && isset($datatableParams['length'])) {
            $builder->limit($datatableParams['length'], $datatableParams['start']);
        }

        // Get filtered records count
        $recordsFiltered = $builder->countAllResults(false);

        // Execute the query and get results
        $data = $builder->get()->getResult();
        
        if (in_array($user->role, ['Admin', 'SuperAdmin']) && count($data)) {
            $mobilePushCounter = slavable_model(FcmLogModel::class, 'Transactions')->select(['transaction_id', 'COUNT(transaction_id) as count'])->whereIn('transaction_id', array_column($data, 'id'))->where('status', 'success')->groupBy('transaction_id')->get()->getResult();
            
            $outputDevicePushCounters = slavable_model(OutputDeviceReplayMessageQueueModel::class, 'Transactions')->select(['transaction_id', 'COUNT(transaction_id) as count'])->whereIn('transaction_id', array_column($data, 'id'))->groupBy('transaction_id')->get()->getResult();
            
            foreach ($data as &$row) {
                $row->mobile_push_count = array_values(array_filter($mobilePushCounter, function($counter) use ($row) {
                    return $counter->transaction_id == $row->id;
                }))[0]->count ?? 0;
                
                $row->output_device_push_count = isset($outputDevicePushCounters) ? array_values(array_filter($outputDevicePushCounters, function($counter) use ($row) {
                    return $counter->transaction_id == $row->id;
                }))[0]->count ?? 0 : 0;
            }
        }

        return [
            'draw' => $datatableParams['draw'] ?? 1,
            'recordsTotal' => $recordsTotal,
            'recordsFiltered' => $recordsFiltered,
            'data' => $data,
            'storeIds' => $storeIds
        ];
    }
    
    public function getStoreRevenues(array $storeIds, ?string $startDate = null, ?string $endDate = null)
    {
        $this->ensureWithCompanyContext();
        
        $storeLinks = count($storeIds)
            ? model(BankShopLinkModel::class)->select(['shop_id', 'tb_autopay_bank_shop_link.bank_account_id', 'bank_sub_account_id', 'sub_account'])
                ->join('tb_autopay_bank_sub_account', 'tb_autopay_bank_sub_account.id = tb_autopay_bank_shop_link.bank_sub_account_id', 'left')
                ->whereIn('shop_id', $storeIds)
                ->findAll() 
            : [];
            
        if (empty($storeLinks)) {
            return [];
        }
        
        $revenues = [];
        
        $transactionsModel = slavable_model(TransactionsModel::class, 'Transactions');
        
        $transactionBuilder = $transactionsModel
            ->select(['SUM(amount_in) as revenue', 'tb_autopay_sms_parsed.bank_account_id as bank_account_id', 'tb_autopay_sms_parsed.sub_account as sub_account'])
            ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id = tb_autopay_sms_parsed.bank_account_id')
            ->join('tb_autopay_bank', 'tb_autopay_bank.brand_name = tb_autopay_sms_parsed.gateway')
            ->where('tb_autopay_sms_parsed.amount_in >', 0)
            ->where('tb_autopay_bank_account.company_id', $this->companyContext()->company->id)
            ->where('tb_autopay_sms_parsed.parser_status', 'Success')
            ->where('tb_autopay_sms_parsed.deleted_at', null)
            ->groupBy(['tb_autopay_sms_parsed.bank_account_id', 'tb_autopay_sms_parsed.sub_account']);
            
        if ($startDate) {
            $transactionBuilder->where('tb_autopay_sms_parsed.transaction_date >=', $startDate);
        }
        
        if ($endDate) {
            $transactionBuilder->where('tb_autopay_sms_parsed.transaction_date <=', $endDate);
        }
        
        $transactions = $transactionBuilder->get()->getResult();
        
        foreach ($storeIds as $storeId) {
            $specifiedStoreLinks = array_values(array_filter($storeLinks, fn ($storeLink) => $storeLink['shop_id'] == $storeId));
            
            $revenue = 0;
            
            foreach ($specifiedStoreLinks as $storeLink) {
                $transaction = array_values(array_filter($transactions, function($transaction) use ($storeLink) {
                    return $transaction->bank_account_id == $storeLink['bank_account_id'] 
                    && $transaction->sub_account == $storeLink['sub_account'];
                }))[0] ?? null;
                
                if ($transaction) {
                    $revenue += $transaction->revenue;
                }
            }
            
            
            $revenues[] = ['id' => $storeId, 'revenue' => $revenue];
        }
        
        return $revenues;
    }
    
    public function getStoreCounters(array $storeIds, ?string $startDate = null, ?string $endDate = null)
    {
        $this->ensureWithCompanyContext();
        
        $storeLinks = count($storeIds)
            ? model(BankShopLinkModel::class)->select(['shop_id', 'tb_autopay_bank_shop_link.bank_account_id', 'bank_sub_account_id', 'sub_account'])
                ->join('tb_autopay_bank_sub_account', 'tb_autopay_bank_sub_account.id = tb_autopay_bank_shop_link.bank_sub_account_id', 'left')
                ->whereIn('shop_id', $storeIds)
                ->findAll() 
            : [];
            
        if (empty($storeLinks)) {
            return [];
        }
        
        $filterByDate = function (&$builder, ?string $startDate = null, ?string $endDate = null) {
            if ($startDate) {
                $builder->where('transaction_date >=', $startDate);
            }
            
            if ($endDate) {
                $builder->where('transaction_date <=', $endDate);
            }
        };

        $transactionsModel = slavable_model(TransactionsModel::class, 'Transactions');
        $bankAccountTransactionBuilder = $transactionsModel
            ->select(['bank_account_id', 'amount_in', 'amount_out'])
            ->whereIn('bank_account_id', array_column($storeLinks, 'bank_account_id'))
            ->groupStart()
                ->where('sub_account', '')
                ->orWhere('sub_account', null)
            ->groupEnd();
                    
        $filterByDate($bankAccountTransactionBuilder, $startDate, $endDate);

        $bankAccountTransactions = $bankAccountTransactionBuilder->get()->getResult();
        
        $bankSubAccountTransactionBuilder = $transactionsModel
            ->select(['sub_account', 'amount_in', 'amount_out'])
            ->whereIn('sub_account', array_column($storeLinks, 'sub_account'));
                    
        $filterByDate($bankAccountTransactionBuilder, $startDate, $endDate);

        $bankSubAccountTransactions = $bankSubAccountTransactionBuilder->get()->getResult();
        
        $counters = [];
            
        foreach ($storeIds as $storeId) {
            $inCounter = 0;
            $outCounter = 0;
            
            $specifiedStoreLinks = array_values(array_filter($storeLinks, fn ($storeLink) => $storeLink['shop_id'] == $storeId));

            $bankAccountIds = array_column(array_filter($specifiedStoreLinks, fn ($storeLink) => !$storeLink['bank_sub_account_id']), 'bank_account_id');
            
            foreach ($bankAccountIds as $id) {
                $inCounter += count(array_filter($bankAccountTransactions, fn ($transaction) => $transaction->bank_account_id == $id && $transaction->amount_in > 0));
                $outCounter += count(array_filter($bankAccountTransactions, fn ($transaction) => $transaction->bank_account_id == $id && $transaction->amount_out > 0));
            }
            
            $bankSubAccounts = array_column(array_filter($specifiedStoreLinks, fn ($storeLink) => $storeLink['sub_account']), 'sub_account');
            
            foreach ($bankSubAccounts as $va) {
                $inCounter += count(array_filter($bankSubAccountTransactions, fn ($transaction) => $transaction->sub_account == $va && $transaction->amount_in > 0));
                $outCounter += count(array_filter($bankSubAccountTransactions, fn ($transaction) => $transaction->sub_account == $va && $transaction->amount_out > 0));
            }
            
            $counters[] = ['id' => $storeId, 'in_transaction' => $inCounter, 'out_transaction' => $outCounter, 'total_transaction' => $inCounter + $outCounter];
        }
        
        return $counters;
    }
}
