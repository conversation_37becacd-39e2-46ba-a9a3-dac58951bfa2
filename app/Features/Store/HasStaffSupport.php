<?php

namespace App\Features\Store;

use CodeIgniter\Model;
use App\Models\BankShopLinkModel;
use App\Models\CompanyUserModel;
use App\Models\UserModel;
use App\Models\UserPermissionBankModel;
use App\Models\UserPermissionBankSubModel;
use App\Models\UserPermissionFeatureModel;

trait HasStaffSupport
{
    public function staffBuilder(): Model
    {
        /** @var StoreFeature $this */

        $this->ensureWithCompanyContext();
        
        $builder = model(UserModel::class)
            ->select(['tb_autopay_user.*', 'tb_autopay_company_user.role', 'tb_autopay_company_user.company_id'])
            ->join('tb_autopay_company_user', 'tb_autopay_company_user.user_id = tb_autopay_user.id')
            ->where('tb_autopay_company_user.company_id', $this->companyContext()->company->id)
            ->whereNotIn('tb_autopay_company_user.role', ['SuperAdmin', 'Admin'])
            ->orderBy('tb_autopay_user.id', 'DESC');
    
        return $builder;
    }
    
    /**
     * @param array<int,mixed> $data
     */
    public function createStaff(array $data): int
    {
        /** @var StoreFeature $this */
        
        $this->ensureWithCompanyContext();
        $companyId = $this->companyContext()->company->id;
        
        $userModel = model(UserModel::class);
        $companyUserModel = model(CompanyUserModel::class);
        $userPermissionFeatureModel = model(UserPermissionFeatureModel::class);

        if (!empty($data['fullname'])) {
            $fullName = trim($data['fullname']);
            $nameParts = explode(' ', $fullName, 2);

            if (count($nameParts) > 1) {
                $data['firstname'] = $nameParts[0];
                $data['lastname'] = $nameParts[1];
            } else {
                $data['firstname'] = $fullName;
                $data['lastname'] = '';
            }
        }
        
        $staffData = [
            'firstname' => mb_convert_case(xss_clean($data['firstname']), MB_CASE_TITLE, "UTF-8"),
            'lastname' => mb_convert_case(xss_clean($data['lastname']), MB_CASE_TITLE, "UTF-8"), 
            'email' => $data['email'],
            'password' => password_hash($data['password'], PASSWORD_DEFAULT),
            'active' => 1,
        ];

        $staffId = $userModel->insert($staffData);
        
        if (!is_numeric($staffId)) {
            throw new \Exception("Create staff failed");
        }

        $companyUserData = [
            'company_id' => $companyId,
            'user_id' => $staffId,
            'role' => 'User'
        ];
        
        $addedStaffToCompany = $companyUserModel->insert($companyUserData);

        if (!$addedStaffToCompany) {
            throw new \Exception("Link staff to company failed");
        }

        $userPermissionFeatureModel->initialize_permission($staffId, $companyId);
        
        try {
            $userPermissionFeatureModel->where('user_id', $staffId)->where('company_id', $companyId)->where('feature_slug', 'PushMobileTransactionNotification')->set([
                'can_view_all' => 1
            ])->update();
            $userPermissionFeatureModel->where('user_id', $staffId)->where('company_id', $companyId)->where('feature_slug', 'Transactions')->set([
                'can_view_all' => 1
            ])->update();
        } catch (\Exception $e) {
            log_message('error', '[StoreFeature] Failed to set staff permission ' . $e->getMessage());
        }
        
        return $staffId;
    }
    
    public function getLinkableStaffStores(int $userId, ?callable $builderCallback = null): array
    {
        /** @var StoreFeature $this */
        
        $builder = $this->storeBuilder();
        
        if (is_callable($builderCallback)) {
            $builder = $builderCallback($builder);
        }
        
        $stores = $builder->get()->getResult();
        
        $storeIds = array_column($stores, 'id');
        
        $storeLinks = count($storeIds) 
            ? model(BankShopLinkModel::class)->select(['shop_id', 'bank_account_id', 'bank_sub_account_id'])->whereIn('shop_id', $storeIds)->findAll() 
            : [];
        
        $stores = array_map(function ($store) use ($storeLinks, $userId) {
            $links = array_values(array_filter($storeLinks, fn ($link) => $link['shop_id'] === $store->id));
            $store->links = $links;
            
            if (! count($links)) {
                return $store;
            }
            
            $bankAccountIds = array_unique(array_column($links, 'bank_account_id'));
            
            $subAccountIds = array_unique(array_column($links, 'bank_sub_account_id'));
            
            $linked = model(UserPermissionBankModel::class)
                ->where('user_id', $userId)
                ->whereIn('bank_account_id', $bankAccountIds)
                ->countAllResults() === count($bankAccountIds)
                && (
                    (count($subAccountIds) === 1 && !$subAccountIds[0]) 
                    || model(UserPermissionBankSubModel::class)
                    ->where('user_id', $userId)
                    ->whereIn('sub_account_id', $subAccountIds)
                    ->countAllResults() > 0
                );
                
            if (!$linked) {
                return $store;
            }
            
            return false;
        }, $stores);
        
        return array_values(array_filter($stores));
    }
    
    public function getLinkedStaffStores(int $userId, ?callable $builderCallback = null): array
    {
        /** @var StoreFeature $this */
        
        $builder = $this->storeBuilder();
        
        if (is_callable($builderCallback)) {
            $builder = $builderCallback($builder);
        }
        
        $stores = $builder->get()->getResult();
        
        $storeIds = array_column($stores, 'id');
        
        $storeLinks = count($storeIds)
            ? model(BankShopLinkModel::class)->select(['shop_id', 'bank_account_id', 'bank_sub_account_id'])->whereIn('shop_id', $storeIds)->findAll() 
            : [];
        
        $stores = array_map(function ($store) use ($storeLinks, $userId) {
            $links = array_filter($storeLinks, fn ($link) => $link['shop_id'] === $store->id);
            $store->links = $links;
            
            if (! count($links)) {
                return false;
            }
            
            $bankAccountIds = array_unique(array_column($links, 'bank_account_id'));
            
            $subAccountIds = array_unique(array_column($links, 'bank_sub_account_id'));
            
            $linked = model(UserPermissionBankModel::class)
                ->where('user_id', $userId)
                ->whereIn('bank_account_id', $bankAccountIds)
                ->countAllResults() === count($bankAccountIds)
                && (
                    (count($subAccountIds) === 1 && !$subAccountIds[0]) 
                    || model(UserPermissionBankSubModel::class)
                    ->where('user_id', $userId)
                    ->whereIn('sub_account_id', $subAccountIds)
                    ->countAllResults() > 0
                );
                
            if (!$linked) {
                return false;
            }
            
            return $store;
        }, $stores);
        
        return array_values(array_filter($stores));
    }
    
    /**
     * @param array<int,mixed> $storeIds
     */
    public function linkStoresToStaff(array $storeIds, int $userId): void
    {
        /** @var StoreFeature $this */
        
        $this->ensureWithCompanyContext();
        
        $linkableStores = $this->getLinkableStaffStores($userId, function ($builder) use ($storeIds) {
            return $builder->whereIn('id', $storeIds);
        });
        
        $links = array_merge(...array_column($linkableStores, 'links'));
        
        $staff = $this->staffBuilder()->where('tb_autopay_user.id', $userId)->first();
        
        if (!$staff) {
            throw new \Exception('Staff not found');
        }
        
        foreach ($links as $link) {
            try {
                model(UserPermissionBankModel::class)->insert([
                    'user_id' => $userId,
                    'bank_account_id' => $link['bank_account_id'],
                    'hide_amount_out' => 1,
                    'hide_accumulated' => 1
                ]);
            } catch (\Exception $e) {
            }
            
            if ($link['bank_sub_account_id']) {
                try {
                    model(UserPermissionBankSubModel::class)->insert([
                        'user_id' => $userId,
                        'sub_account_id' => $link['bank_sub_account_id']
                    ]);
                } catch (\Exception $e) {
                }
            }
        }
    }
    
    /**
     * @param array<int,mixed> $storeIds
     */
    public function unlinkStoresToStaff(array $storeIds, int $staffId): void
    {
        /** @var StoreFeature $this */
        
        $this->ensureWithCompanyContext();
        
        $linkedStores = $this->getLinkedStaffStores($staffId, function ($builder) use ($storeIds) {
            return $builder->whereIn('id', $storeIds);
        });
        
        $links = array_merge(...array_column($linkedStores, 'links'));
        
        foreach ($links as $link) {
            try {
                model(UserPermissionBankModel::class)
                    ->where('bank_account_id', $link['bank_account_id'])
                    ->where('user_id', $staffId)
                    ->delete();
            } catch (\Exception $e) {
            }
            
            if ($link['bank_sub_account_id']) {
                try {
                    model(UserPermissionBankSubModel::class)
                        ->where('sub_account_id', $link['bank_sub_account_id'])
                        ->where('user_id', $staffId)
                        ->delete();
                } catch (\Exception $e) {
                }
            }
        }
    }
    
    public function getStoreStaffs(array $storeIds = [], ?string $type = null, ?callable $builderCallback = null): array
    {
        /** @var StoreFeature $this */
        
        $builder = $this->staffBuilder();
        
        if ($builderCallback) {
            $builder = $builderCallback($builder);
        }
        
        $staffs = $builder->get()->getResult();
        
        $staffs = array_map(function ($staff) use ($storeIds, $type) {
            $staff->stores = $this->getLinkedStaffStores($staff->id);
            $staff->avatar_url = get_gravatar($staff->email, 32);
            
            if (count($storeIds) && count(array_intersect($storeIds, array_column($staff->stores, 'id'))) != count($storeIds)) {
                return $type == 'linkable' ? $staff : false;
            }

            if ($type == 'linkable') {
                return false;
            }
            
            return $staff;
        }, $staffs);
        
        $staffs = array_values(array_filter($staffs));
        
        return $staffs;
    }
    
    /**
     * @param mixed $data
     */
    public function editStoreStaff(int $staffId, $data): bool
    {
        /** @var StoreFeature $this */
        
        $this->ensureWithCompanyContext();
        
        if (isset($data['active'])) {
            $updateBuilder = model(UserModel::class)->where('tb_autopay_user.id', $staffId);
                
            return $updateBuilder->set(['active' => $data['active'] ? 1 : 0])->update();
        }
        
        if (!empty($data['fullname'])) {
            $fullName = trim($data['fullname']);
            $nameParts = explode(' ', $fullName, 2);

            if (count($nameParts) > 1) {
                $data['firstname'] = $nameParts[0];
                $data['lastname'] = $nameParts[1];
            } else {
                $data['firstname'] = $fullName;
                $data['lastname'] = '';
            }
        } else {
            $data['firstname'] = '';
            $data['lastname'] = '';
        }
        
        $safeStaffData = array_filter([
            'firstname' => $data['firstname'] ? mb_convert_case(xss_clean($data['firstname']), MB_CASE_TITLE, "UTF-8") : false,
            'lastname' => $data['lastname'] ? mb_convert_case(xss_clean($data['lastname']), MB_CASE_TITLE, "UTF-8") : false,
            'password' => $data['password'] ? password_hash($data['password'], PASSWORD_DEFAULT) : false,
        ], fn($value, $key) => $value !== false, ARRAY_FILTER_USE_BOTH);

        $updateBuilder = model(UserModel::class)->where('tb_autopay_user.id', $staffId);

        return $updateBuilder->set($safeStaffData)->update();
    }
    
    public function deleteStoreStaff(int $staffId): void
    {
        /** @var StoreFeature $this */
        
        $this->ensureWithCompanyContext();
        
        model(UserModel::class)->where('tb_autopay_user.id', $staffId)->delete();
        model(CompanyUserModel::class)->where('user_id', $staffId)->delete();
        model(UserPermissionFeatureModel::class)->where('user_id', $staffId)->delete();
        model(UserPermissionBankModel::class)->where('user_id', $staffId)->delete();
        model(UserPermissionBankSubModel::class)->where('user_id', $staffId)->delete();
    }
}