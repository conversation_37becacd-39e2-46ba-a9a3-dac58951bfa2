<?php

namespace App\Features\BankAccount;

use App\Features\BankAccount\Contexts\BankAccountContext;
use App\Features\BankAccount\Contexts\CompanyContext;
use App\Exceptions\BankAccountFeatureException;
use App\Features\BankAccount\Contexts\BankAccountConnectContext;

class BankAccountFeature
{
    protected ?BankAccountContext $bankAccountContext = null;

    protected ?CompanyContext $companyContext = null;

    protected ?BankAccountConnectContext $bankAccountConnectContext = null;

    const BANK_ACCOUNT_CONNECT_CONTEXT_CLASS_MAPPING = [
        'MBBank' => \App\Features\BankAccount\Contexts\MBBankBankAccountConnectContext::class,
        'OCB' => \App\Features\BankAccount\Contexts\OCBBankAccountConnectContext::class,
        'BIDV' => \App\Features\BankAccount\Contexts\BIDVBankAccountConnectContext::class,
        'VietinBank' => \App\Features\BankAccount\Contexts\VietinBankBankAccountConnectContext::class,
        'MSB' => \App\Features\BankAccount\Contexts\MSBBankAccountConnectContext::class,
        'TPBank' => \App\Features\BankAccount\Contexts\TPBankBankAccountConnectContext::class,
        'KienLongBank' => \App\Features\BankAccount\Contexts\KienLongBankBankAccountConnectContext::class,
        'ACB' => \App\Features\BankAccount\Contexts\ACBBankAccountConnectContext::class,
        'VPBank' => \App\Features\BankAccount\Contexts\VPBankBankAccountConnectContext::class,
        'ABBANK' => \App\Features\BankAccount\Contexts\AbbankBankAccountConnectContext::class,
    ];

    public function __construct()
    {
        //
    }

    public function withBankAccountConnectContext(?string $brandName = null, ?string $type = null): BankAccountFeature
    {
        $bankAccountConnectContextClass = self::BANK_ACCOUNT_CONNECT_CONTEXT_CLASS_MAPPING[$brandName] ?? null;

        if (! $bankAccountConnectContextClass) {
            throw new BankAccountFeatureException('Bank account connect context class not found');
        }

        $this->bankAccountConnectContext = new $bankAccountConnectContextClass($this, $type);

        return $this;
    }

    public function bankAccountConnectContext(): BankAccountConnectContext
    {
        $this->ensureWithBankAccountConnectContext();

        return $this->bankAccountConnectContext;
    }

    public function ensureWithBankAccountConnectContext(): void
    {
        if (! $this->bankAccountConnectContext) {
            throw new BankAccountFeatureException('Bank account connect context not set');
        }
    }

    public function withCompanyContext(int $companyId): BankAccountFeature
    {
        try {
            $this->companyContext = new CompanyContext($this, $companyId);
        } catch (BankAccountFeatureException $e) {
            $this->companyContext = null;
        }

        return $this;
    }

    public function companyContext(): CompanyContext
    {
        $this->ensureWithCompanyContext();

        return $this->companyContext;
    }

    public function ensureWithCompanyContext(): void
    {
        if (! $this->companyContext) {
            throw new BankAccountFeatureException('Company context is not set');
        }
    }

    public function determineIfCompanyContextIsSet(): bool
    {
        return ! is_null($this->companyContext);
    }

    public function withBankAccountContext(?int $bankId = null, ?int $id = null): BankAccountFeature
    {
        try {
            $this->bankAccountContext = new BankAccountContext($this, $bankId, $id);
        } catch (BankAccountFeatureException $e) {
            $this->bankAccountContext = null;
        }

        return $this;
    }

    public function bankAccountContext(): BankAccountContext
    {
        $this->ensureWithBankAccountContext();

        return $this->bankAccountContext;
    }

    public function ensureWithBankAccountContext(): void
    {
        if (! $this->bankAccountContext) {
            throw new BankAccountFeatureException('Bank account context is not set');
        }
    }

    /**
     * @return array<string,mixed>[]
     */
    public function allBankAccountConnectSchema(): array
    {
        $schema = [];

        foreach (self::BANK_ACCOUNT_CONNECT_CONTEXT_CLASS_MAPPING as $key => $class) {
            $context = new $class($this);

            if ($context->hasIndividualBankAccountConnectSupport()) {
                $schema[] = $context->individualBankAccountConnectSchema()->toArray();
            }

            if ($context->hasEnterpriseBankAccountConnectSupport()) {
                $schema[] = $context->enterpriseBankAccountConnectSchema()->toArray();
            }
        }

        return $schema;
    }
}
