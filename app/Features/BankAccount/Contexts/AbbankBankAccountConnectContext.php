<?php

namespace App\Features\BankAccount\Contexts;

use App\Features\BankAccount\Contexts\Interfaces\CanCheckConfirmedBankAccountConnect;
use App\Features\BankAccount\Contexts\Interfaces\HasDetermineBankAccountExists;
use App\Libraries\AbbankClient;
use App\Models\AbbankLinkedAccountModel;
use App\Models\MCCCategoryModel;
use Config\Services;
use Exception;
use App\Models\BankAccountModel;

class AbbankBankAccountConnectContext extends BankAccountConnectContext implements CanCheckConfirmedBankAccountConnect, HasDetermineBankAccountExists
{
    public bool $skipCreateBankAccount = true;

    protected string $mcc;

    protected string $merchantName;

    protected string $merchantCity;

    public function brandName(): string
    {
        return 'ABBANK';
    }

    public function bankId(): int
    {
        return 19;
    }

    public function hasIndividualBankAccountConnectSupport(): bool
    {
        return true;
    }

    public function hasEnterpriseBankAccountConnectSupport(): bool
    {
        return false;
    }

    public function authorizeIndividualBankAccountConnect(): bool
    {
        $this->feature->ensureWithCompanyContext();

        return true;
    }

    public function authorizeEnterpriseBankAccountConnect(): bool
    {
        return false;
    }

    public function requestIndividualBankAccountConnect(): int
    {
        $client = new AbbankClient();
        $outputDevice = $this->getOutputDevice();

        if (! $outputDevice || $outputDevice->bank_id != $this->bankId()) {
            return $this->failResponse(['account_number' => 'Không tìm thấy thiết bị loa thanh toán của ngân hàng ABBANK.']);
        }

        try {
            $response = $client->linkAccount(
                $outputDevice->serial_number,
                $this->accountNumber,
                $this->phoneNumber,
                $this->identificationNumber,
                $this->mcc,
                $this->merchantName,
                $this->merchantCity,
            );

            $data = json_decode($response->getBody(), true);

            if ($response->getStatusCode() != 200) {
                return $this->failResponse(['status' => false, 'message' => $data['details']]);
            }
        } catch (Exception $e) {
            $data = json_decode($e->getResponse()->getBody(), true);

            switch ($data['code']) {
                case '01':
                    return $this->failResponse(['account_number' => 'Tài khoản này đã được liên kết với thiết bị loa này']);
                case '02':
                    return $this->failResponse(['account_number' => 'Thông tin tài khoản ngân hàng không chính xác. Vui lòng kiểm tra lại số tài khoản, số CCCD và số điện thoại để đảm bảo khớp với thông tin đã đăng ký tại ABBANK']);
                case '99':
                    return $this->failResponse(['error' => 'Đã có lỗi xảy ra, vui lòng thử lại sau']);
                default:
                    log_message('error', '[ABBANK] requestIndividualBankAccountConnect: ' . json_encode($data));

                    throw $e;
            }

            log_message('error', '[ABBANK] requestIndividualBankAccountConnect: ' . json_encode($data));

            throw $e;
        }

        return $this->requestConnectWithApi($data['data']['sessionId']);
    }

    public function requestEnterpriseBankAccountConnect(): int
    {
        throw $this->bankAccountConnectContextException('Unavailable service');
    }

    public function confirmIndividualBankAccountConnect(): int
    {
        if ($this->verifiedOtpRequestConnect) {
            return self::SUCCESS_CODE;
        }

        if (! $this->otpRequestId) {
            return $this->failResponse(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng tải lại trang'], self::UNEXPECTED_ERROR_CODE);
        }

        try {
            $client = new AbbankClient();
            $response = $client->verifyOtp($this->otp, $this->otpRequestId);
            $data = json_decode($response->getBody(), true);

            if ($response->getStatusCode() != 200) {
                return $this->failResponse(['status' => false, 'message' => $data['details']]);
            }

            return $this->verifyOtpRequestConnect();
        } catch (Exception $e) {
            $data = json_decode($e->getResponse()->getBody(), true);

            switch ($data['code']) {
                case '05':
                case '06':
                    return $this->failResponse(['otp' => 'Mã xác thực không chính xác']);
                case '07':
                    return $this->failResponse(['otp' => 'Bạn đã nhập sai mã xác thực quá số lần cho phép']);
                case '99':
                    return $this->failResponse(['otp' => 'Đã có lỗi xảy ra, vui lòng thử lại sau']);
                default:
                    log_message('error', '[ABBANK] confirmIndividualBankAccountConnect: ' . json_encode($data));

                    throw $e;
            }

            log_message('error', '[ABBANK] confirmIndividualBankAccountConnect: ' . json_encode($data));

            throw $e;
        }
    }

    public function confirmEnterpriseBankAccountConnect(): int
    {
        throw $this->bankAccountConnectContextException('Unavailable service');
    }

    public function isConfirmedIndividualBankAccountConnect(): bool
    {
        if ($outputDevice = $this->getOutputDevice()) {
            return model(AbbankLinkedAccountModel::class)
                ->where('bank_account_id', $this->bankAccountId)
                ->where('output_device_id', $outputDevice->id)
                ->countAllResults() > 0;
        }

        return false;
    }

    public function isConfirmedEnterpriseBankAccountConnect(): bool
    {
        return false;
    }

    public function individualBankAccountConnectSchema(): BankAccountConnectSchema
    {
        return (new BankAccountConnectSchema('ABBANK', 'individual'))
            ->setCanOnline()
            ->enablePhoneNumberField()
            ->enableIdentificationNumberField()
            ->enablePhoneNumberField()
            ->enableLabelField(false)
            ->modifyField('account_number', [
                'input_type' => 'text',
                'help_text' => 'Nhập số điện thoại hoặc số đẹp hiển thị trên app ABBANK, chỉ nhập số tài khoản 13 số nếu không có số đẹp',
            ])
            ->modifyField('account_holder_name', [
                'help_text' => 'Tên người thụ hưởng của số tài khoản hiển thị trên app ABBANK của bạn',
            ])
            ->setAdditionalFields([
                'mcc' => [
                    'enabled' => true,
                    'type' => 'select',
                    'label' => 'Loại hình kinh doanh',
                    'options' => $this->getCachedMCCCategories(),
                    'rules' => [
                        'required' => true,
                    ],
                    'className' => 'tom-select',
                ],
                'merchantName' => [
                    'enabled' => true,
                    'label' => 'Tên gợi nhớ/viết tắt người bán',
                    'rules' => [
                        'required' => true,
                        'max_length' => 25,
                    ],
                    'placeholder' => 'Ví dụ: Fuji mart Hoàng Cầu',
                ],
                'merchantCity' => [
                    'enabled' => true,
                    'label' => 'Tỉnh/thành phố người bán',
                    'rules' => [
                        'required' => true,
                        'max_length' => 15,
                    ],
                    'placeholder' => 'Ví dụ: 36 Hoàng Cầu',
                ],
            ])
            ->supportOtp()
            ->createVaOption(false, [
                'is_official' => false,
                'otp' => [
                    'support' => false,
                ],
            ]);
    }

    public function enterpriseBankAccountConnectSchema(): BankAccountConnectSchema
    {
        throw $this->bankAccountConnectContextException('Unavailable service');
    }

    public function determineIfBankAccountExistsByAccountNumber(): bool
    {
        $currentCompany = $this->feature->companyContext()->company;
        
        $existsInBankAccount = model(BankAccountModel::class)
            ->where('account_number', $this->accountNumber)
            ->where('bank_id', $this->bankId())
            ->first();

        if ($existsInBankAccount) {
            if ($existsInBankAccount->company_id != $currentCompany->id) {
                return true;
            }
        }

        return false;
    }

    protected function createdBankAccount(int $result): void
    {
        if ($outputDevice = $this->getOutputDevice()) {
            model(AbbankLinkedAccountModel::class)->insert([
                'output_device_id' => $outputDevice->id,
                'bank_account_id' => $result,
                'mcc' => $this->mcc,
                'merchant_name' => $this->merchantName,
                'merchant_city' => $this->merchantCity,
            ]);

            $this->bankAccountId = $result;

            $this->createInhouseVa();
        }
    }

    public function handleBankClientException(Exception $e): string
    {
        switch ($e->getCode()) {
            case 400:
                $data = json_decode($e->getResponse()->getBody(), true);
                return $data['message'];
            default:
                return 'Đã có lỗi xảy ra, vui lòng thử lại sau hoặc liên hệ SePay để được hỗ trợ.';
        }
    }

    protected function handleExistingBankAccount(object $bankAccount): void
    {
        $this->createdBankAccount($bankAccount->id);
    }

    protected function getCachedMCCCategories(): array
    {
        try {
            $cacheKey = 'mcc_categories';

            $cachedValue = Services::cache()->get($cacheKey);

            if (! $cachedValue) {
                $cachedValue = $this->getMCCCategories();

                Services::cache()->save($cacheKey, $cachedValue, MONTH);
            }

            return $cachedValue;
        } catch (Exception $e) {
            return $this->getMCCCategories();
        }
    }

    protected function getMCCCategories(): array
    {
        $categories = model(MCCCategoryModel::class)
            ->orderBy('order', 'asc')
            ->findAll();

        $cachedValue = [];

        foreach ($categories as $category) {
            $cachedValue[$category->code] = $category->description;
        }

        return $cachedValue;
    }
}
