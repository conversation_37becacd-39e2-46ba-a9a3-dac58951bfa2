<?php

namespace App\Features\BankAccount\Contexts;

use App\Exceptions\DisableBankClientException;
use App\Libraries\VpbankClient;
use App\Models\VpbankBankAccountMetadataModel;
use App\Models\VpbankClientAccessTokenModel;
use Exception;
use App\Features\BankAccount\Contexts\Interfaces\CanCheckConfirmedBankAccountConnect;

class VPBankBankAccountConnectContext extends BankAccountConnectContext implements CanCheckConfirmedBankAccountConnect
{
    protected ?VpbankClient $client = null;

    public ?string $registerId = null;

    public function brandName(): string
    {
        return 'VPBank';
    }

    public function bankId(): int
    {
        return 2;
    }

    public function hasIndividualBankAccountConnectSupport(): bool
    {
        return true;
    }

    public function hasEnterpriseBankAccountConnectSupport(): bool
    {
        return true;
    }

    public function authorizeIndividualBankAccountConnect(): bool
    {
        $this->feature->ensureWithCompanyContext();

        $company = $this->feature->companyContext()->company;

        $authorized = model(VpbankClientAccessTokenModel::class)
            ->where([
                'expires_at >' => date('Y-m-d H:i:s'),
                'company_id' => $company->id,
                'authorize_code !=' => ''
            ])->countAllResults() > 0;

        if ($authorized) {
            return true;
        }
        
        $this->client = new VpbankClient;
        $response = $this->client->getLoginPageUrl();
        $loginPageUrl = $response->getHeaderLine('Location');

        if (strpos($loginPageUrl, 'login.do') < 0) {
            throw $this->bankAccountConnectContextException('Generate Login page URL failed');
        }

        $this->webviewUrl = $loginPageUrl;
        
        return false;
    }

    public function authorizeEnterpriseBankAccountConnect(): bool
    {
        /** @var \Config\Vpbank $vpbankConfig */
        $vpbankConfig = config(\Config\Vpbank::class);
        $this->webviewUrl = $vpbankConfig->orgFeatureVpbankNeobizUrl;
        
        return false;
    }

    public function requestIndividualBankAccountConnect(): int
    {
        $this->client = $this->makeClientWithCompanyContext();
        
        $this->registerId = md5(uniqid() . $this->accountNumber);
        
        $response = $this->client->initRegister(
            $this->identificationNumber,
            $this->accountNumber,
            $this->accountHolderName,
            $this->registerId,
        );

        $responseData = json_decode($response->getBody());

        if (!$responseData) {
            throw $this->bankAccountConnectContextException('Parse response data failed');
        }
        
        if (property_exists($responseData, 'error')) {
            if ($responseData->error == 'ISS-010') {
                throw new Exception('Integration type not found');
            }

            if (in_array($responseData->error, ['ISS-002', 'ISS-006', 'ISS-007'])) {
                return $this->failResponse(['account_number' => 'Số tài khoản không tồn tại hoặc đã bị khóa ở hệ thống ngân hàng VPBank.']);
            }

            if ($responseData->error == 'ISS-003') {
                return $this->failResponse(['identification_number' => 'Số CCCD/CMND không được đăng ký cho số tài khoản trên.']);
            }

            if ($responseData->error == 'ISS-004') {
                return $this->failResponse(['account_holder_name' => 'Tên người thụ hưởng không khớp với thông tin số tài khoản trên.']);
            }

            if ($responseData->error == 'ISS-005') {
                return $this->failResponse(['account_number' => 'Số tài khoản phải là khách hàng cá nhân.']);
            }

            if ($responseData->error == 'ISS-008') {
                return $this->failResponse(['account_number' => 'Số tài khoản đang được liên kết tại VPBank, liên hệ SePay để được hỗ trợ.']);
            }

            if ($responseData->error == 'ISS-013') {
                return $this->failResponse(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng thử lại sau.']);
            }

            if ($responseData->error == 'ISS-020') {
                return $this->failResponse(['account_number' => 'Tài khoản không hợp lệ, vui lòng liên hệ SePay để được hỗ trợ.']);
            }
        }

        if (! property_exists($responseData, 'requestId')) {
            throw $this->bankAccountConnectContextException('requestId not found');
        }

        $requestId = $responseData->requestId;

        $response = $this->client->sendOtp($requestId);

        $otpPageUrl = $response->getHeaderLine('location');

        if (!$otpPageUrl) {
            throw $this->bankAccountConnectContextException('OTP page URL generated failed');
        }

        return $this->requestConnectWithWebView($otpPageUrl, $requestId);
    }

    public function requestEnterpriseBankAccountConnect(): int
    {
        throw $this->bankAccountConnectContextException('Unavailable service');
    }

    public function confirmIndividualBankAccountConnect(): int
    {
        if ($this->isConfirmedIndividualBankAccountConnect()) return true;

        $this->client = $this->makeClientWithCompanyContext();
        
        $response = $this->client->submitRegister($this->otpRequestId);
        $responseData = json_decode($response->getBody());

        if (!$responseData) {
            throw $this->bankAccountConnectContextException('Parse response data failed');
        }
     
        if (property_exists($responseData, 'error')) {
            if ($responseData->error == 'ISS-010') {
                throw $this->bankAccountConnectContextException('Integration type not found');
            }

            if ($responseData->error == 'ISS-008') {
                throw $this->bankAccountConnectContextException('The customer already has registered for this provider');
            }

            if ($responseData->error == 'ISS-009') {
                throw $this->bankAccountConnectContextException('The operation has not been approved yet');
            }
        }
        
        return $this->verifyOtpRequestConnect();
    }

    public function confirmEnterpriseBankAccountConnect(): int
    {
        throw $this->bankAccountConnectContextException('Unavailable service');
    }
    
    public function isConfirmedIndividualBankAccountConnect(): bool
    {
        return model(VpbankBankAccountMetadataModel::class)
            ->where('bank_account_id', $this->bankAccountId)
            ->where('register_id', $this->registerId)
            ->countAllResults() > 0;
    }

    public function isConfirmedEnterpriseBankAccountConnect(): bool
    {
        throw $this->bankAccountConnectContextException('Unavailable service');
    }

    protected function createdBankAccount(int $result): void
    {
        $this->client = $this->makeClientWithCompanyContext();
        
        $created = model(VpbankBankAccountMetadataModel::class)->insert([
            'bank_account_id' => $result,
            'register_id' => $this->registerId
        ]);
        
        if (!$created) {
            $this->client->deregister($this->registerId);
            
            throw $this->bankAccountConnectContextException('Failed to create bank account metadata');
        }
    }
    
    private function makeClientWithCompanyContext(): VpbankClient
    {
        $this->feature->ensureWithCompanyContext();
        $company = $this->feature->companyContext()->company;
        
        return new VpbankClient($company->id);
    }

    public function individualBankAccountConnectSchema(): BankAccountConnectSchema
    {
        $schema = new BankAccountConnectSchema('VPBank', 'individual');

        return $schema
            ->setCanOnline()
            ->supportOtp()
            ->setAuthorizeConnectWebview(400, 680)
            ->setConfirmConnectWebview(400, 680)
            ->authorizeConnectWithRedirect()
            ->confirmConnectWithRedirect()
            ->enablePhoneNumberField(false)
            ->enableIdentificationNumberField()
            ->createVaOption(true, array_merge([
                'is_official' => false,
                'otp' => ['support' => false],
            ], $this->isSpeakerBillingSubscription() ? $this->defaultCreateVaOptionForSpeakerBilling() : []));
    }

    public function enterpriseBankAccountConnectSchema(): BankAccountConnectSchema
    {
        throw $this->bankAccountConnectContextException('Unavailable service');
    }

    public function handleBankClientException(Exception $e): string
    {
        if ($this->client) {
            $this->client->forceDebug();
        }

        if ($e->getCode() == 504 || strpos($e->getMessage(), 'Connection timed out') !== false || strpos($e->getMessage(), 'Operation timed out') !== false) {
            return 'Hệ thống ngân hàng VPBank đang bận, vui lòng thử lại sau.';
        }

        if ($e instanceof DisableBankClientException) {
            return 'Hệ thống ngân hàng VPBank đang bảo trì, vui lòng liên hệ SePay để biết thêm thông tin chi tiết.';
        }

        return 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ.';
    }
    
    protected function additionalSession(): array
    {
        return ['registerId' => 'register_id'];
    }
}
