<?php

namespace App\Features\BankAccount\Contexts;

use App\Features\BankAccount\BankAccountFeature;
use App\Exceptions\BankAccountFeatureException;
use App\Features\BankAccount\Contexts\BankAccountConnectSchema;
use App\Features\BankAccount\Contexts\Interfaces\CanCheckConfirmedBankAccountConnect;
use App\Features\BankAccount\Contexts\Interfaces\CanCreateVaAutomatically;
use App\Features\BankAccount\Contexts\Interfaces\HasCreateOfficialVaSupport;
use Exception;
use App\Models\BankAccountModel;
use App\Models\ConfigurationModel;
use App\Models\BankSubAccountModel;
use App\Features\BankAccount\Contexts\Interfaces\CanLookupAccountHolderName;
use App\Features\BankAccount\Contexts\Interfaces\RequireCreateVaToBankAccountConnect;
use App\Features\SpeakerBilling\SpeakerBillingFeature;
use App\Models\OutputDeviceDecalModel;
use App\Models\OutputDeviceModel;

abstract class BankAccountConnectContext
{
    protected BankAccountFeature $feature;

    public string $brandName;

    public ?string $type = null;

    public ?string $accountNumber = null;

    public ?string $accountHolderName = null;

    public ?string $identificationNumber = null;

    public ?string $phoneNumber = null;

    public ?string $label = null;

    public ?string $otpRequestId = null;

    public ?array $requestConnectData = null;

    public ?int $requestConnectCode = null;

    public ?array $responseData = null;

    public ?int $responseCode = null;

    public bool $verifiedOtpRequestConnect = false;
    
    public ?string $otp = null;

    public ?string $webviewUrl = null;

    public ?string $defaultVAV = null;

    /**
     * The VAV without prefix.
     */
    public ?string $vav = null;

    public array $additionalData = [];

    public bool $skipCreateBankAccount = false;

    public ?int $firstVaId = null;
    
    public ?int $bankAccountId = null;

    public ?object $outputDevice = null;
    
    public ?object $outputDeviceDecal = null;

    public const SUCCESS_CODE = 0;

    public const INVALID_ERROR_CODE = 400;

    public const UNEXPECTED_ERROR_CODE = 500;

    public const UNAUTHORIZED_ERROR_CODE = 403;

    public function __construct(
        BankAccountFeature $feature,
        ?string $type = null
    ) {
        $this->feature = $feature;

        if (is_null($type)) {
            return;
        } else {
            $this->type = $type === 'enterprise' ? 'enterprise' : 'individual';
        }

        if (!$this->canConnectWithIndividual() && !$this->canConnectWithEnterprise()) {
            throw new BankAccountFeatureException('Bank account type is not supported');
        }
    }

    public function canConfirmRequest(): bool
    {
        return (bool) $this->otpRequestId || in_array($this->schema()->confirmConnectType, ['interval', 'callback']);
    }

    public function canConnectWithIndividual(): bool
    {
        return $this->type === 'individual' && $this->hasIndividualBankAccountConnectSupport();
    }

    public function canConnectWithEnterprise(): bool
    {
        return $this->type === 'enterprise' && $this->hasEnterpriseBankAccountConnectSupport();
    }

    public function schema(): BankAccountConnectSchema
    {
        if ($this->canConnectWithIndividual()) {
            return $this->individualBankAccountConnectSchema();
        }

        return $this->enterpriseBankAccountConnectSchema();
    }

    public function authorize(): bool
    {
        $this->clearSession();

        if ($this->canConnectWithIndividual()) {
            return $this->authorizeIndividualBankAccountConnect();
        }

        return $this->authorizeEnterpriseBankAccountConnect();
    }

    public function requestConnect(): int
    {
        if ($this instanceof RequireCreateVaToBankAccountConnect) {
            try {
                $this->generateVAV();
            } catch (Exception $e) {
                //
            }
            
            if (model(BankSubAccountModel::class)->where('sub_account', $this->getVA())->countAllResults() > 0) {
                log_message('error', 'Default VAV maybe duplicated: ' . $this->getVA());
                return $this->failResponse(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ.'], self::UNEXPECTED_ERROR_CODE);
            }
        }

        if ($this->canConnectWithIndividual()) {
            return $this->requestIndividualBankAccountConnect();
        } else {
            return $this->requestEnterpriseBankAccountConnect();
        }
    }

    public function canRetryRequestConnect(): bool
    {
        return $this->getSession() !== null && $this->otpRequestId !== null;
    }
    
    public function canRetryRequestCreateOfficialVa(): bool
    {
        return $this->getSession() !== null && $this->otpRequestId !== null && $this->bankAccountId !== null;
    }

    public function confirmConnect(): int
    {
        if ($this->canConnectWithIndividual()) {
            $result = $this->confirmIndividualBankAccountConnect();
        } else {
            $result = $this->confirmEnterpriseBankAccountConnect();
        }

        if ($result !== self::SUCCESS_CODE || !$this->verifiedOtpRequestConnect) {
            return $result;
        }

        $bankAccountId = $this->createBankAccount();
        $this->bankAccountId = $bankAccountId;

        if ($this instanceof RequireCreateVaToBankAccountConnect) {
            $this->setFirstVaId($this->createVaToBankAccountConnect($bankAccountId));
        } else if ($this instanceof CanCreateVaAutomatically) {
            $this->setFirstVaId($this->createVaAutomatically($bankAccountId));
        }

        $this->saveSession();

        return $bankAccountId;
    }

    public function isConfirmedConnect(): bool
    {
        if (! $this instanceof CanCheckConfirmedBankAccountConnect) {
            throw $this->bankAccountConnectContextException('isConfirmedConnect not supported');
        }
        
        if ($this->canConnectWithIndividual()) {
            return $this->isConfirmedIndividualBankAccountConnect();
        }

        return $this->isConfirmedEnterpriseBankAccountConnect();
    }
    
    public function isOkResponse(): bool
    {
        return $this->responseCode === self::SUCCESS_CODE;
    }

    public function createBankAccount(): int
    {
        if (!$this->verifiedOtpRequestConnect) return 0;
        
        $this->feature->ensureWithCompanyContext();
        
        $bankAccountModel = model(BankAccountModel::class);
        $bankAccountModel->skipApplyReferral();
        
        $bankAccount = $bankAccountModel->where(['account_number' => $this->accountNumber, 'bank_id' => $this->bankId()])->first();

        $session = service('session');
        $request = service('request');
        $company = $this->feature->companyContext()->company;
        $bankSubAccountConfig = get_configuration('BankSubAccount');
        
        if ($bankSubAccountConfig == 'off') {
            $configurationModel = model(ConfigurationModel::class);
            $configurationModel->set([
                'value' => 'on',
            ])->where([
                'company_id' => $company->id,
                'setting' => 'BankSubAccount'
            ])->update();
        }
        
        if ($this->skipCreateBankAccount && is_object($bankAccount)) {
            $this->handleExistingBankAccount($bankAccount);

            return $bankAccount->id;
        }

        if (is_object($bankAccount)) {
            throw $this->bankAccountConnectContextException('Create duplicate account number');
        }

        $result = $bankAccountModel->insert([
            'company_id' => $company->id,
            'account_holder_name' => remove_accents($this->accountHolderName, true),
            'account_number' => trim(xss_clean($this->accountNumber)),
            'identification_number' => trim(xss_clean($this->identificationNumber)),
            'phone_number' => trim(xss_clean($this->phoneNumber)),
            'bank_id' => $this->bankId(),
            'label' => trim(xss_clean($this->label)),
            'active' => 1,
            'bank_api' => 1,
            'bank_api_connected' => 1,
        ]);

        $log = ['data_id' => 0, 'company_id' => $company->id, 'data_type' => 'bank_account_add', 'description' => 'Liên kết tài khoản ngân hàng ' . $this->schema()->bank->brand_name, 'user_id' => $session->get('user_logged_in')['user_id'], 'ip' => $request->getIPAddress(), 'user_agent' => $request->getUserAgent()->getAgentString(), 'status' => 'Failed'];

        if ($result) {
            $this->createdBankAccount($result);

            $log['data_id'] = $result;
            $log['status'] = 'Success';
        }

        add_user_log($log);

        return $result;
    }

    public function setField(string $name, string $value): void
    {
        if ($name === 'account_number') {
            $this->accountNumber = $value;
        } elseif ($name === 'account_holder_name') {
            $this->accountHolderName = $value;
        } elseif ($name === 'identification_number') {
            $this->identificationNumber = $value;
        } elseif ($name === 'phone_number') {
            $this->phoneNumber = $value;
        } elseif ($name === 'label') {
            $this->label = $value;
        } elseif ($name === 'vav') {
            $this->vav = $value;
        } else {
            if (property_exists($this, $name)) {
                $this->{$name} = $value;
            }
        }
    }
    /**
     * @param array<int,mixed> $data
     */
    public function failRequestConnect(array $data = [], int $code = BankAccountConnectContext::INVALID_ERROR_CODE): int
    {
        $this->requestConnectData = $data;
        $this->requestConnectCode = $code;

        return $this->requestConnectCode;
    }
    /**
     * @param array<int,mixed> $data
     */
    public function failResponse(array $data = [], int $code = BankAccountConnectContext::INVALID_ERROR_CODE): int
    {
        $this->responseData = $data;
        $this->responseCode = $code;

        return $this->responseCode;
    }

    public function saveSession(): void
    {
        $session = service('session');

        $data = array_merge($session->get('bank_account_connect_context') ?? [],[
            'type' => $this->type,
            'bank_id' => $this->bankId(),
            'verified_otp_request_connect' => $this->verifiedOtpRequestConnect,
            'otp_request_id' => $this->otpRequestId,
            'account_number' => $this->accountNumber,
            'account_holder_name' => $this->accountHolderName,
            'identification_number' => $this->identificationNumber,
            'phone_number' => $this->phoneNumber,
            'label' => $this->label,
            'vav' => $this->vav,
            'first_va_id' => $this->firstVaId,
            'additional_data' => $this->additionalData,
            'bank_account_id' => $this->bankAccountId,
        ]);

        foreach ($this->schema()->additionalFields ?? [] as $name => $fieldOptions) {
            if (!isset($fieldOptions['enabled']) || !$fieldOptions['enabled']) continue;

            if (property_exists($this, $name)) {
                $data[$name] = $this->{$name};
            }
        }

        foreach ($this->additionalSession() as $propertyName => $sessionName) {
            if (property_exists($this, $propertyName)) {
                $data[$sessionName] = $this->{$propertyName};
            }
        }

        $session->set('bank_account_connect_context', $data);
    }

    protected function additionalSession(): array
    {
        return [];
    }

    public function getSession(): ?array
    {
        $session = service('session');

        return $session->get('bank_account_connect_context');
    }

    public function loadFromSession(): void
    {
        $session = service('session');
        $data = $session->get('bank_account_connect_context');

        if (!$data) return;

        $this->type = $data['type'];
        $this->verifiedOtpRequestConnect = $data['verified_otp_request_connect'] ?? false;
        $this->otpRequestId = $data['otp_request_id'] ?? null;
        $this->accountNumber = $data['account_number'] ?? null;
        $this->accountHolderName = $data['account_holder_name'] ?? null;
        $this->identificationNumber = $data['identification_number'] ?? null;
        $this->phoneNumber = $data['phone_number'] ?? null;
        $this->label = $data['label'] ?? null;
        $this->vav = $data['vav'] ?? null;
        $this->additionalData = $data['additional_data'] ?? [];
        $this->firstVaId = $data['first_va_id'] ?? null;
        $this->bankAccountId = $data['bank_account_id'] ?? null;

        foreach ($this->schema()->additionalFields ?? [] as $name => $fieldOptions) {
            if (!isset($fieldOptions['enabled']) || !$fieldOptions['enabled']) continue;

            if (property_exists($this, $name)) {
                $this->{$name} = $data[$name];
            }
        }

        foreach ($this->additionalSession() as $propertyName => $sessionName) {
            if (property_exists($this, $propertyName)) {
                $this->{$propertyName} = $data[$sessionName];
            }
        }
        
        $this->loadOutputDeviceDecalIfPresent();
    }
    
    public function loadOutputDeviceDecalIfPresent(): void
    {
        $session = service('session');
        $data = $session->get('bank_account_connect_context');
        
        if (!$this->isSpeakerBillingSubscription() && !$this->isShopBillingSubscription()) {
            return;
        }
        
        if ($data['output_device_id']) {
            $outputDeviceDecalBuilder = model(OutputDeviceDecalModel::class)
                ->join('tb_autopay_output_device', 'tb_autopay_output_device.id = tb_autopay_output_device_decal.output_device_id')
                ->groupStart()
                    ->where('tb_autopay_output_device.company_id', null)
                    ->orWhere('tb_autopay_output_device.company_id', $this->feature->companyContext()->company->id)
                ->groupEnd()
                ->where('tb_autopay_output_device_decal.output_device_id', $data['output_device_id'])
                ->where('tb_autopay_output_device_decal.account_number', $this->accountNumber)
                ->where('tb_autopay_output_device_decal.virtual_account_number !=', null)
                ->where('tb_autopay_output_device_decal.bank_id', $this->bankId());
                
            if ($this->accountNumber) {
                $outputDeviceDecalBuilder->where('tb_autopay_output_device_decal.account_number', $this->accountNumber);
            }
                
            $this->outputDeviceDecal = $outputDeviceDecalBuilder->first();
        } else {
            throw $this->bankAccountConnectContextException('Missing output device ID in the session');
        }
    }

    public function getOutputDevice(): ?object
    {
        if (! $this->isSpeakerBillingSubscription()) {
            return null;
        }

        $data = service('session')->get('bank_account_connect_context');

        if (! $data['output_device_id']) {
            return null;
        }

        if ($this->outputDevice) {
            return (object) $this->outputDevice;
        }

        return $this->outputDevice = (object) model(OutputDeviceModel::class)->find($data['output_device_id']);
    }

    public function clearSession(): void
    {
        $session = service('session');
        $session->remove('bank_account_connect_context');
    }

    public function initSession(?int $outputDeviceId = null): void
    {
        $session = service('session');
        $outputDevice = null;
        
        if ($this->isSpeakerBillingSubscription() || $this->isShopBillingSubscription()) {
            $outputDeviceBuilder = model(OutputDeviceModel::class)
                ->groupStart()
                    ->where('company_id', null)
                    ->orWhere('company_id', $this->feature->companyContext()->company->id)
                ->groupEnd();
                
            if ($session->has('output_device_integration') && isset($session->get('output_device_integration')['serial_number'])) {
                $outputDeviceBuilder
                    ->where('serial_number', $session->get('output_device_integration')['serial_number'])
                    ->where('vendor', $session->get('output_device_integration')['vendor'])
                    ->where('model', $session->get('output_device_integration')['model']);
            } else if ($outputDeviceId) {
                $outputDeviceBuilder->where('id', $outputDeviceId);
            }
            
            $outputDevice = $outputDeviceBuilder->first();
        }
        
        $session->set('bank_account_connect_context', [
            'type' => $this->type,
            'bank_id' => $this->bankId(),
            'output_device_id' => $outputDevice ? $outputDevice['id'] : null,
        ]);
    }

    public function hasLookupAccountHolderNameSupport(): bool
    {
        return $this instanceof CanLookupAccountHolderName;
    }

    public function getVAV(): ?string
    {
        if ($this->schema()->fields['vav']['enabled']) {
            return $this->schema()->fields['vav']['prefix'] . $this->vav ?? $this->defaultVAV;
        }

        return null;
    }
    
    public function getVAVWithoutPrefix(): ?string
    {
        if ($this->schema()->fields['vav']['enabled']) {
            return $this->vav ?? $this->defaultVAV;
        }

        return null;
    }
    
    public function getVAVPrefix(): ?string
    {
        if ($this->schema()->fields['vav']['enabled']) {
            return $this->schema()->fields['vav']['prefix'];
        }

        return null;
    }
    
    public function getVAC(): ?string
    {
        if ($this->schema()->fields['vav']['enabled']) {
            return $this->schema()->fields['vav']['vac'];
        }

        return null;
    }

    public function getVA(): ?string
    {
        if ($this->schema()->fields['vav']['enabled']) {
            return $this->schema()->fields['vav']['vac'] . $this->getVAV();
        }

        return null;
    }

    public function verifyOtpRequestConnect(): int
    {
        $this->responseCode = self::SUCCESS_CODE;
        $this->verifiedOtpRequestConnect = true;
        $this->saveSession();

        return $this->responseCode;
    }

    public function requestConnectWithWebView(string $webviewUrl, ?string $otpRequestId = null): int
    {
        $this->webviewUrl = $webviewUrl;
        $this->otpRequestId = $otpRequestId;
        $this->responseCode = self::SUCCESS_CODE;

        $this->saveSession();

        return $this->responseCode;
    }

    public function requestConnectWithApi(string $otpRequestId): int
    {
        $this->otpRequestId = $otpRequestId;
        $this->responseCode = self::SUCCESS_CODE;
        $this->verifiedOtpRequestConnect = false;

        $this->saveSession();

        return $this->responseCode;
    }

    public function isSpeakerBillingSubscription(): bool
    {
        /** @var SpeakerBillingFeature $speakerBillingFeature */
        $speakerBillingFeature = service('speakerBillingFeature');

        try {
            return $speakerBillingFeature->companyContext()->isSubscribedSpeakerBillingProduct();
        } catch (\Exception $e) {
            return false;
        }
    }
    
    public function isShopBillingSubscription(): bool
    {
        try {
            return is_shop_billing_subscription();
        } catch (\Exception $e) {
            return false;
        }
    }

    public function bankAccountConnectContextException(string $message): void
    {
        throw new BankAccountFeatureException(sprintf('%s: %s', get_class($this), $message));
    }

    public function setFirstVaId(int $id): void
    {
        $this->firstVaId = $id;
    }
    
    protected function createInhouseVaForSpeakerBillingSubscription(int $bankAccountId): int
    {
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $outputDeviceDecalModel = model(OutputDeviceDecalModel::class);
        $va = null;
        
        if (($this->isSpeakerBillingSubscription() || $this->isShopBillingSubscription()) && $this->outputDeviceDecal) {
            $va = $this->outputDeviceDecal->virtual_account_number;
        } else {
            $failedCount = 0;
            
            do {
                $va = 'L' . strtoupper(bin2hex(random_bytes(2)));
                
                if ($bankSubAccountModel->where('sub_account', $va)->countAllResults() === 0 
                && $outputDeviceDecalModel->where('virtual_account_number', $va)->countAllResults() === 0) {
                    break;
                }
                
                $failedCount++;
            } while ($failedCount < 3);
        }
        
        if (!$va) {
            throw $this->bankAccountConnectContextException('Failed to generate TKP virtual account');
        }
        
        $bankSubAccountId = $bankSubAccountModel->insert([
            'bank_account_id' => $bankAccountId,
            'acc_type' => 'Virtual',
            'sub_account' => $va,
            'sub_holder_name' => $this->accountHolderName,
        ]);
        
        if (!$bankSubAccountId) {
            throw $this->bankAccountConnectContextException('Failed to create bank sub account');
        }
        
        return $bankSubAccountId;
    }
    
    public function requestCreateOfficialVa(): int
    {
        if (!$this instanceof HasCreateOfficialVaSupport) {
            throw $this->bankAccountConnectContextException('Service unavailable');
        }
        
        if ($this->canConnectWithIndividual()) {
            return $this->requestCreateIndividualOfficialVa();
        } else {
            return $this->requestCreateEnterpriseOfficialVa();
        }
    }
    
    public function confirmCreateOfficialVa(): int
    {
        if (!$this instanceof HasCreateOfficialVaSupport) {
            throw $this->bankAccountConnectContextException('Service unavailable');
        }
        
        if ($this->canConnectWithIndividual()) {
            $result = $this->confirmCreateIndividualOfficialVa();
        } else {
            $result = $this->confirmCreateEnterpriseOfficialVa();
        }
        
        if ($result !== self::SUCCESS_CODE || !$this->verifiedOtpRequestConnect) {
            return $result;
        }

        $this->saveSession();

        return $this->firstVaId;
    }
    
    public function requestCreateOfficialVaWithApi(string $otpRequestId): int
    {
        $this->otpRequestId = $otpRequestId;
        $this->responseCode = self::SUCCESS_CODE;
        $this->verifiedOtpRequestConnect = false;

        $this->saveSession();

        return $this->responseCode;
    }
    
    public function verifyOtpCreateOfficialVa(string $id): int
    {
        $this->responseCode = self::SUCCESS_CODE;
        $this->verifiedOtpRequestConnect = true;
        $this->setFirstVaId($id);
        $this->saveSession();

        return $this->responseCode;
    }
    
    public function createInhouseVa(): int
    {
        if ($this->schema()->createVaOption['is_official']) {
            throw $this->bankAccountConnectContextException('Unavailable service');
        }
        
        if (!$this->bankAccountId) {
            throw $this->bankAccountConnectContextException('Bank account ID not found');
        }
        
        $this->setFirstVaId($this->createInhouseVaForSpeakerBillingSubscription($this->bankAccountId));
        $this->saveSession();
        
        return self::SUCCESS_CODE;
    }

    protected function handleExistingBankAccount(object $bankAccount): void
    {
        //
    }

    /**
     * @return array<string,mixed>
     */
    protected function defaultCreateVaOptionForSpeakerBilling(bool $optionDefault = true): array
    {
        return [
            'title' => 'Cấu hình cách loa phát âm thanh thông báo',
            'content' => '
                <p class="mb-0">Bạn muốn cứ có tiền vào tài khoản chính là loa phát âm thanh, hay chỉ phát âm thanh khi khách thanh toán qua quét QR?</p>
            ',
            'options' => [
                'true' => [
                    'default' => $optionDefault,
                    'label' => 'Chỉ phát âm thanh với giao dịch qua quét QR',
                    'description' => 'Chỉ khi nào khách quét QR thanh toán thì loa mới phát âm thanh, các giao dịch không qua quét QR thì loa sẽ không phát.',
                ],
                'false' => [
                    'default' => !$optionDefault,
                    'label' => 'Cả tài khoản chính và QR',
                    'description' => 'Phát âm thanh tất cả giao dịch tiền vào tài khoản. Kể cả quét QR và không quét QR.',
                    'help_text' => '
                        <div class="alert alert-warning text-sm mb-0">
                            <div class="alert-message">
                                <p class="mb-0"><i class="bi bi-exclamation-triangle-fill text-warning me-1"></i> Tùy chọn này loa sẽ phát thông báo cả các giao dịch không liên quan thanh toán tại cửa hàng như trả lãi tiền gửi (nếu có)...</p>
                            </div>
                        </div>
                    '
                ]
            ],
        ];
    }

    abstract public function bankId(): int;

    abstract public function authorizeIndividualBankAccountConnect(): bool;

    abstract public function authorizeEnterpriseBankAccountConnect(): bool;

    abstract public function requestIndividualBankAccountConnect(): int;

    abstract public function requestEnterpriseBankAccountConnect(): int;

    abstract public function confirmIndividualBankAccountConnect(): int;

    abstract public function confirmEnterpriseBankAccountConnect(): int;

    abstract protected function createdBankAccount(int $id): void;

    abstract public function hasIndividualBankAccountConnectSupport(): bool;

    abstract public function hasEnterpriseBankAccountConnectSupport(): bool;

    abstract public function individualBankAccountConnectSchema(): BankAccountConnectSchema;

    abstract public function enterpriseBankAccountConnectSchema(): BankAccountConnectSchema;

    abstract public function handleBankClientException(Exception $e): string;
}
