<?php

namespace App\Features\BankAccount\Contexts;

use App\Libraries\Tpbank;
use App\Models\BankAccountModel;
use App\Models\TpbankTraceCompanyMapModel;
use Exception;

class TPBankBankAccountConnectContext extends BankAccountConnectContext
{
    protected ?Tpbank $client = null;

    public bool $skipCreateBankAccount = true;

    public function brandName(): string
    {
        return 'TPBank';
    }

    public function bankId(): int
    {
        return 12;
    }

    public function hasIndividualBankAccountConnectSupport(): bool
    {
        return true;
    }

    public function hasEnterpriseBankAccountConnectSupport(): bool
    {
        return true;
    }

    public function authorizeIndividualBankAccountConnect(): bool
    {
        return true;
    }
    public function authorizeEnterpriseBankAccountConnect(): bool
    {
        return true;
    }

    public function requestIndividualBankAccountConnect(): int
    {
        $this->feature->ensureWithCompanyContext();

        $company = $this->feature->companyContext()->company;

        $this->client = new Tpbank;

        return $this->requestConnectWithWebView($this->client->getWebViewUrl($company->id, $company->full_name, 'RB'));
    }

    public function requestEnterpriseBankAccountConnect(): int
    {
        $company = $this->feature->companyContext()->company;

        $this->client = new Tpbank;

        return $this->requestConnectWithWebView($this->client->getWebViewUrl($company->id, $company->full_name, 'CB'));
    }

    public function confirmIndividualBankAccountConnect(): int
    {
        $this->feature->ensureWithCompanyContext();

        $company = $this->feature->companyContext()->company;

        $tpbankTraceCompanyMapModel = model(TpbankTraceCompanyMapModel::class);
        $bankAccountModel = model(BankAccountModel::class);

        $trace = $tpbankTraceCompanyMapModel
            ->where('company_id', $company->id)
            ->where('bank_account_id !=', null)
            ->first();

        if (! $trace) {
            return $this->failResponse(['status' => false], self::UNAUTHORIZED_ERROR_CODE);
        }

        $tpbankTraceCompanyMapModel->delete($trace->id);

        $bankAccountDetails = $bankAccountModel
            ->where('id', $trace->bank_account_id)
            ->where('company_id', $company->id)
            ->where('bank_api_connected', 1)
            ->where('bank_api', 1)
            ->where('bank_id', $this->bankId())
            ->first();

        if (!$bankAccountDetails) {
            return $this->failResponse(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ.'], self::UNEXPECTED_ERROR_CODE);
        }

        $this->accountHolderName = $bankAccountDetails->account_holder_name;
        $this->accountNumber = $bankAccountDetails->account_number;

        return $this->verifyOtpRequestConnect();
    }

    public function confirmEnterpriseBankAccountConnect(): int
    {
        return $this->confirmIndividualBankAccountConnect();
    }

    protected function createdBankAccount(int $result): void
    {
        //
    }

    public function individualBankAccountConnectSchema(): BankAccountConnectSchema
    {
        $schema = new BankAccountConnectSchema($this->brandName(), 'individual');

        return $this->defaultBankAccountConnectSchema($schema)
            ->setRequestConnectContent(view('_components/bank-account/request-connect-content/tpbank', ['is_enterprise' => false], ['saveData' => false, 'debug' => false]));
    }

    public function enterpriseBankAccountConnectSchema(): BankAccountConnectSchema
    {
        $schema = new BankAccountConnectSchema($this->brandName(), 'enterprise');

        return $this->defaultBankAccountConnectSchema($schema)
            ->setRequestConnectContent(view('_components/bank-account/request-connect-content/tpbank', ['is_enterprise' => true], ['saveData' => false, 'debug' => false]));
    }

    private function defaultBankAccountConnectSchema(BankAccountConnectSchema $schema): BankAccountConnectSchema
    {
        return $schema
            ->setCanOnline()
            ->setRequestConnectWebview(1200, 680)
            ->confirmConnectWithCallback()
            ->createVaOption(true, array_merge([
                'is_official' => false,
                'otp' => [
                    'support' => false,
                ]
            ], $this->isSpeakerBillingSubscription() ? $this->defaultCreateVaOptionForSpeakerBilling(false): []));
    }

    public function handleBankClientException(Exception $e): string
    {
        return 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ.';
    }
}
