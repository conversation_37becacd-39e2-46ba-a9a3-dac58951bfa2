<?php

namespace App\Features\BankAccount\Contexts;

use App\Features\BankAccount\BankAccountFeature;
use App\Features\BankAccount\Contexts\BankAccountConnectContext;
use App\Libraries\OcbClient;
use App\Models\ConfigurationModel;
use Exception;
use App\Features\BankAccount\Contexts\Interfaces\CanLookupAccountHolderName;
use App\Features\BankAccount\Contexts\Interfaces\RequireCreateVaToBankAccountConnect;
use App\Models\BankSubAccountModel;
use App\Models\OcbBankSubAccountMetaData;

class OCBBankAccountConnectContext extends BankAccountConnectContext implements CanLookupAccountHolderName, RequireCreateVaToBankAccountConnect
{
    protected ?OcbClient $client = null;

    protected BankAccountFeature $feature;

    public ?string $address = null;

    public function brandName(): string
    {
        return 'OCB';
    }
    
    public function bankId(): int
    {
        return 18;
    }

    public function hasIndividualBankAccountConnectSupport(): bool
    {
        return true;
    }

    public function hasEnterpriseBankAccountConnectSupport(): bool
    {
        return true;
    }

    public function authorizeIndividualBankAccountConnect(): bool
    {
        return true;
    }
    public function authorizeEnterpriseBankAccountConnect(): bool
    {
        return true;
    }

    public function requestIndividualBankAccountConnect(): int
    {
        $this->feature->ensureWithCompanyContext();

        $session = service('session');
        $user = $session->get('user_logged_in');
        $company = $this->feature->companyContext()->company;

        $this->additionalData['mcc'] = '0004';
        $this->additionalData['merchantName'] = remove_accents($user['lastname'] . ' ' . $user['firstname'], true);
        $this->additionalData['email'] =  $user['email'];
        $this->additionalData['address'] = remove_accents($this->address, true);

        $this->client = new OcbClient;
        $response = $this->client->registerMerchantStep1(
            $this->identificationNumber,
            $this->phoneNumber,
            $this->accountNumber,
            $this->client->getPartnerCode(),
            $this->additionalData['email'],
            $this->additionalData['merchantName'],
            $this->additionalData['merchantName'],
            $this->additionalData['mcc'],
            $this->additionalData['address'],
            $this->getVAV(),
        );

        $responseData = json_decode($response->getBody(), true);

        // SIMULATE
        // $responseData = [
        //     'trace' => ['bankRefNo' => uniqid()]
        // ];

        if (isset($responseData['trace']['bankRefNo']) && !isset($responseData['error'])) {
            return $this->requestConnectWithApi($responseData['trace']['bankRefNo']);
        }

        if ($responseData['error']['code'] == '41776') {
            return $this->failResponse(['status' => false, 'message' => 'Số tài khoản định danh đã được sử dụng.'], self::UNEXPECTED_ERROR_CODE);
        }

        if ($responseData['error']['code'] == '41749') {
            return $this->failResponse(['phone_number' => 'Số điện thoại không được đăng ký cho tài khoản trên.']);
        }

        if ($responseData['error']['code'] == '41744') {
            return $this->failResponse(['identification_number' =>  'Số CCCD/CMND không được đăng ký cho tài khoản trên.']);
        }

        if ($responseData['error']['code'] == '504') {
            return $this->failResponse(['status' => false, 'message' => 'Hệ thống ngân hàng OCB đang bận, vui lòng thử lại sau.'], self::UNEXPECTED_ERROR_CODE);
        }

        throw $this->bankAccountConnectContextException('Unexpected response ' . $response->getBody());
    }

    public function requestEnterpriseBankAccountConnect(): int
    {
        throw $this->bankAccountConnectContextException('Unavailable service');
    }

    public function confirmIndividualBankAccountConnect(): int
    {
        $this->client = new OcbClient;
        $response = $this->client->registerMerchantStep2(
            $this->client->getPartnerCode(),
            $this->otp,
            $this->otpRequestId
        );

        $responseData = json_decode($response->getBody(), true);

        // SIMULATE
        // $responseData = [
        //     'trace' => [
        //         'bankRefNo' => uniqid()
        //     ]
        // ];

        if (isset($responseData['trace']['bankRefNo']) && !isset($responseData['error'])) {
            return $this->verifyOtpRequestConnect();
        }

        if ($responseData['error']['code'] == '41731') {
            return $this->failResponse(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng tải lại trang.'], self::UNEXPECTED_ERROR_CODE);
        }

        if ($responseData['error']['code'] == '504') {
            return $this->failResponse(['status' => false, 'message' => 'Hệ thống ngân hàng OCB đang bận, vui lòng thử lại sau.'], self::UNEXPECTED_ERROR_CODE);
        }

        if ($responseData['error']['code'] == '41724') {
            return $this->failResponse(['otp' => 'OTP không chính xác.']);
        }

        if ($responseData['error']['code'] == '41726') {
            return $this->failResponse(['otp' => 'OTP đã hết hạn, vui lòng yêu cầu lại mã OTP mới.']);
        }

        throw $this->bankAccountConnectContextException('Unexpected response ' . $response->getBody());
    }

    public function confirmEnterpriseBankAccountConnect(): int
    {
        throw $this->bankAccountConnectContextException('Unavailable service');
    }

    protected function createdBankAccount(int $result): void
    {
        //
    }

    public function individualBankAccountConnectSchema(): BankAccountConnectSchema
    {
        $schema = new BankAccountConnectSchema($this->brandName(), 'individual');
        $this->client = new OcbClient;

        $vac = $this->client->getPrefixVA();
        $prefixVAV = '';
        
        if ($this->isSpeakerBillingSubscription()) {
            /** 
             * @var SpeakerBillingFeature $speakerBillingFeature
             */
            $speakerBillingFeature = service('speakerBillingFeature');

            $prefixVAV = $speakerBillingFeature->prefixVav;
        }

        return $schema
            ->setCanOnline()
            ->supportOtp()
            ->requireAgreement(true, [
                'type' => 'modal',
                'content' => view('_components/bank-account/agreements/ocb', [], ['saveData' => false, 'debug' => false])
            ])
            ->requireVa(true, [
                'vac' => $vac,
                'prefix' => $prefixVAV,
                'rules' => [],
            ])
            ->setAdditionalFields([
                'address' => [
                    'enabled' => true,
                    'label' => 'Địa chỉ',
                    'rules' => [
                        'required' => true,
                        'max_length' => '100'
                    ]
                ]
            ])
            ->canLookupAccountHolderName()
            ->enableIdentificationNumberField()
            ->enablePhoneNumberField();
    }

    public function enterpriseBankAccountConnectSchema(): BankAccountConnectSchema
    {
        $schema = new BankAccountConnectSchema($this->brandName(), 'enterprise');

        return $schema->setCanOnline(false);
    }

    public function lookupAccountHolderName(string $accountNumber): ?string
    {
        try {
            $this->client = new OcbClient;
            $response = $this->client->retrieveOCBRecipientName($accountNumber);

            $responseData = json_decode($response->getBody(), true);

            if (isset($responseData['trace']['bankRefNo']) && !isset($responseData['error'])) {
                return $responseData['data']['queryResult']['accountName'];
            }

            return null;
        } catch (Exception $e) {
            return null;
        }
    }

    public function createVaToBankAccountConnect(int $id): int
    {
        if (!$this->vav) {
            throw $this->bankAccountConnectContextException('VAV is empty');
        }
        
        $bankSubAccountId = model(BankSubAccountModel::class)->insert([
            'bank_account_id' => $id,
            'sub_account' => $this->getVA(),
            'acc_type' => 'Real',
            'sub_holder_name' => $this->accountHolderName,
        ]);

        if (!$bankSubAccountId) {
            return 0;
        }
        
        $configuration = model(ConfigurationModel::class);
        $vaOrderConfig = $configuration->where('company_id', 0)->where('setting', 'OcbSpeakerBillingVaOrderConfig')->first();
        
        if (! is_object($vaOrderConfig)) {
            $configuration->insert([
                'company_id' => 0,
                'setting' => 'OcbSpeakerBillingVaOrderConfig',
                'value' => 1
            ]);
        } else {
            $newVaOrder = (int) $this->getVAVWithoutPrefix();
            
            if ($vaOrderConfig->value < $newVaOrder) {
                $configuration->where('id', $vaOrderConfig->id)->set([
                    'value' => (int) $this->getVAVWithoutPrefix()
                ])->update();    
            }
        }

        model(OcbBankSubAccountMetaData::class)->insert([
            'bank_account_id' => $id,
            'sub_account_id' => $bankSubAccountId,
            'email' => $this->additionalData['email'] ?? '',
            'merchant_name' => $this->additionalData['merchantName'] ?? '',
            'mcc' => $this->additionalData['mcc'] ?? '',
            'merchant_address' => $this->additionalData['address'] ?? '',
        ]);

        return $bankSubAccountId;
    }

    public function handleBankClientException(Exception $e): string
    {
        if (strpos($e->getMessage(), 'Operation timed out') !== false) {
            return 'Hệ thống ngân hàng OCB đang bận, vui lòng thử lại sau';
        }

        return 'Đã có lỗi xảy ra, vui lòng thử lại sau hoặc liên hệ SePay để được hỗ trợ.';
    }

    public function generateVAV(): void
    {
        if (!$this->isSpeakerBillingSubscription() && !$this->outputDeviceDecal) {
            $attempts = 5;
            
            do {
                $vav = strtoupper(random_string('alnum', 10));
                $va = $this->getVAC() . $vav;
                
                if (model(BankSubAccountModel::class)->where('sub_account', $va)->countAllResults()) {
                    $attempts--;
                    continue;
                }
                
                break;
            } while ($attempts > 0);
            
            $this->vav = $vav;
            return;
        }
        
        if ($this->outputDeviceDecal && $this->isSpeakerBillingSubscription()) {
            $this->vav = str_replace($this->getVAC() . $this->getVAVPrefix(), '', $this->outputDeviceDecal->virtual_account_number);
            return;
        }
        
        if ($this->outputDeviceDecal && $this->isShopBillingSubscription()) {
            $this->vav = str_replace($this->getVAC(), '', $this->outputDeviceDecal->virtual_account_number);
            return;
        }
        
        $configuration = model(ConfigurationModel::class);
        $ocbSpeakerBillingVaOrderConfig = $configuration->where('company_id', 0)->where('setting', 'OcbSpeakerBillingVaOrderConfig')->first();
        
        if (! is_object($ocbSpeakerBillingVaOrderConfig)) {
            $vaOrder = 1;
        } else {
            $vaOrder = $ocbSpeakerBillingVaOrderConfig->value + 1;
        }
       
        /** @var SpeakerBillingFeature $speakerBillingFeature */
        $speakerBillingFeature = service('speakerBillingFeature');
        
        $this->vav = $speakerBillingFeature->withVaContext()->vaContext()->generateVAV($vaOrder);
    }
}
