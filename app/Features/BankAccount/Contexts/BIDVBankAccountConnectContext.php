<?php

namespace App\Features\BankAccount\Contexts;

use App\Exceptions\BankAccountFeatureException;
use App\Exceptions\DisableBankClientException;
use App\Features\BankAccount\BankAccountFeature;
use App\Features\BankAccount\Contexts\Interfaces\RequireCreateVaToBankAccountConnect;
use App\Libraries\BidvClient;
use App\Models\BankSubAccountModel;
use Exception;
use App\Features\SpeakerBilling\SpeakerBillingFeature;
use App\Models\ConfigurationModel;

class BIDVBankAccountConnectContext extends BankAccountConnectContext implements RequireCreateVaToBankAccountConnect
{
    protected ?BidvClient $client = null;

    protected BankAccountFeature $feature;

    protected string $individualVAC;

    /**
     * @throws BankAccountFeatureException
     */
    public function __construct(BankAccountFeature $feature, ?string $type = null)
    {
        parent::__construct($feature, $type);

        $config = config(\Config\Bidv::class);

        $this->individualVAC = $config->personalVaPrefix;
    }
    
    public function brandName(): string
    {
        return 'BIDV';
    }

    public function bankId(): int
    {
        return 9;
    }

    public function hasIndividualBankAccountConnectSupport(): bool
    {
        return true;
    }

    public function hasEnterpriseBankAccountConnectSupport(): bool
    {
        return true;
    }

    public function authorizeIndividualBankAccountConnect(): bool
    {
        return true;
    }
    public function authorizeEnterpriseBankAccountConnect(): bool
    {
        return true;
    }

    public function individualBankAccountConnectSchema(): BankAccountConnectSchema
    {
        $schema = new BankAccountConnectSchema($this->brandName(), 'individual');

        $vac = $this->individualVAC; 
        $prefixVAV = '';
        
        if ($this->isSpeakerBillingSubscription()) {
            /** 
             * @var SpeakerBillingFeature $speakerBillingFeature
             */
            $speakerBillingFeature = service('speakerBillingFeature');

            $prefixVAV = $speakerBillingFeature->prefixVav;
        }

        return $schema
            ->supportOtp()
            ->requireAgreement(true, [
                'type' => 'modal',
                'content' => view('_components/bank-account/agreements/bidv', [], ['saveData' => false, 'debug' => false])
            ])
            ->requireVa(true, [
                'vac' => $vac,
                'prefix' => $prefixVAV,
                'rules' => []
            ])
            ->enableIdentificationNumberField()
            ->enablePhoneNumberField();
    }

    public function enterpriseBankAccountConnectSchema(): BankAccountConnectSchema
    {
        $schema = new BankAccountConnectSchema($this->brandName(), 'enterprise');

        return $schema->setCanOnline(false);
    }

    public function requestIndividualBankAccountConnect(): int
    {
        try {
            $this->client = new BidvClient;
            
            $response = $this->client->createVAQLBH(
                $this->getVAV(),
                $this->accountHolderName,
                $this->accountNumber,
                $this->accountHolderName,
                $this->identificationNumber,
                $this->phoneNumber,
            );

            return $this->requestConnectWithApi(trim(xss_clean($response->getHeaderLine('X-API-Interaction-ID'))));
        } catch (Exception $e) {
            $errorCode = $e->getCode();

            if ($errorCode == '182')
                return $this->failResponse(['status' => false, 'message' => 'Số tài khoản định danh đã được sử dụng.'], self::UNEXPECTED_ERROR_CODE);

            if ($errorCode == '184')
                return $this->failResponse(['sub_id' => 'Số tài khoản định danh (VA) không được chấp nhận.']);

            if ($errorCode == '033')
                return $this->failResponse(['account_number' => 'Số tài khoản không tồn tại hoặc đã bị khóa trên hệ thống ngân hàng BIDV.']);

            if ($errorCode == '040')
                return $this->failResponse(['account_holder_name' => 'Tên chủ tài khoản không khớp với số tài khoản trên.']);

            if ($errorCode == '021')
                return $this->failResponse(['identification_number' => 'Số CCCD/CMND không được đăng ký với số tài khoản trên.']);

            if ($errorCode == '022')
                return $this->failResponse(['phone_number' => 'Số điện thoại không được đăng ký với số tài khoản trên.']);

            if ($errorCode == '060')
                return $this->failResponse(['status' => false, 'message' => "Bạn đã yêu cầu OTP quá nhiều lần, vui lòng thử lại sau"]);

            if ($errorCode == '172')
                return $this->failResponse(['account_number' => 'Số tài khoản không thuộc nhóm khách hàng cá nhân của ngân hàng BIDV.']);

            throw new BankAccountFeatureException('Unexpect response ' . $e->getMessage());
        }
    }

    public function requestEnterpriseBankAccountConnect(): int
    {
        throw $this->bankAccountConnectContextException('Unavailable service');
    }

    public function confirmIndividualBankAccountConnect(): int
    {
        try {
            $this->client = new BidvClient;
            
            $response = $this->client->confirmVAQLBH(
                $this->getVAV(),
                $this->otpRequestId,
                $this->otp
            );

            return $this->verifyOtpRequestConnect();
        } catch (Exception $e) {
            $errorCode = $e->getCode();

            if ($errorCode == '018')
                return $this->failResponse(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng tải lại trang'], self::UNEXPECTED_ERROR_CODE);

            if ($errorCode == '004')
                return $this->failResponse(['otp' => 'OTP đã hết hiệu lực, vui lòng lấy mã OTP mới.']);

            if ($errorCode == '005')
                return $this->failResponse(['otp' => 'OTP không chính xác.']);

            if ($errorCode == '061')
                return $this->failResponse(['otp' => 'Bạn nhập sai quá nhiều lần, vui lòng lấy lại OTP mới.']);

            throw $this->bankAccountConnectContextException('Unexpect response ' . $e->getMessage());
        }
    }

    public function confirmEnterpriseBankAccountConnect(): int
    {
        throw $this->bankAccountConnectContextException('Unavailable service');
    }

    protected function createdBankAccount(int $result): void
    {
        //
    }

    public function handleBankClientException(Exception $e): string
    {
        if ($this->client) {
            $this->client->forceDebug();
        }

        if ($e instanceof DisableBankClientException) {
            return 'Hệ thống ngân hàng BIDV đang bảo trì, vui lòng liên hệ SePay để biết thêm thông tin chi tiết.';
        }

        return 'Đã có lỗi xảy ra, vui lòng thử lại sau hoặc liên hệ SePay để được hỗ trợ.';
    }

    public function createVaToBankAccountConnect(int $id): int
    {        
        if (!$this->vav) {
            throw $this->bankAccountConnectContextException('VAV is empty');
        }
        
        $bankSubAccountId = model(BankSubAccountModel::class)->insert([
            'sub_account' => $this->getVA(),
            'sub_holder_name' => $this->accountHolderName,
            'acc_type' => 'Real',
            'bank_account_id' => $id,
            'va_active' => 1,
        ]);
        
        if (!$bankSubAccountId) {
            throw $this->bankAccountConnectContextException('Insert bank sub account failed');
        }
        
        $configuration = model(ConfigurationModel::class);
        $vaOrderConfig = $configuration->where('company_id', 0)->where('setting', 'BidvSpeakerBillingVaOrderConfig')->first();
        
        if (! is_object($vaOrderConfig)) {
            $configuration->insert([
                'company_id' => 0,
                'setting' => 'BidvSpeakerBillingVaOrderConfig',
                'value' => 1
            ]);
        } else {
            $newVaOrder = (int) $this->getVAVWithoutPrefix();
            
            if ($vaOrderConfig->value < $newVaOrder) {
                $configuration->where('id', $vaOrderConfig->id)->set([
                    'value' => (int) $this->getVAVWithoutPrefix()
                ])->update();    
            }
        }
        
        return $bankSubAccountId;
    }

    public function generateVAV(): void
    {
        if (!$this->isSpeakerBillingSubscription() && !$this->outputDeviceDecal) {
            $attempts = 5;
            
            do {
                $vav = strtoupper(random_string('alnum', 10));
                $va = $this->getVAC() . $vav;
                
                if (model(BankSubAccountModel::class)->where('sub_account', $va)->countAllResults()) {
                    $attempts--;
                    continue;
                }
                
                break;
            } while ($attempts > 0);
            
            $this->vav = $vav;
            return;
        }
        
        if ($this->outputDeviceDecal && $this->isSpeakerBillingSubscription()) {
            $this->vav = str_replace($this->getVAC() . $this->getVAVPrefix(), '', $this->outputDeviceDecal->virtual_account_number);
            return;
        }
        
        if ($this->outputDeviceDecal && $this->isShopBillingSubscription()) {
            $this->vav = str_replace($this->getVAC(), '', $this->outputDeviceDecal->virtual_account_number);
            return;
        }
        
        $configuration = model(ConfigurationModel::class);
        $bidvSpeakerBillingVaOrderConfig = $configuration->where('company_id', 0)->where('setting', 'BidvSpeakerBillingVaOrderConfig')->first();
        
        if (! is_object($bidvSpeakerBillingVaOrderConfig)) {
            $vaOrder = 1;
        } else {
            $vaOrder = $bidvSpeakerBillingVaOrderConfig->value + 1;
        }
       
        /** @var SpeakerBillingFeature $speakerBillingFeature */
        $speakerBillingFeature = service('speakerBillingFeature');
        
        $this->vav = $speakerBillingFeature->withVaContext()->vaContext()->generateVAV($vaOrder);
    }
}
