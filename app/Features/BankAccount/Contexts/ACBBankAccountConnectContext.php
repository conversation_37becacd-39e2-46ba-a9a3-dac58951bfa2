<?php

namespace App\Features\BankAccount\Contexts;

use App\Exceptions\DisableBankClientException;
use App\Features\BankAccount\Contexts\Interfaces\HasCreateOfficialVaSupport;
use App\Libraries\AcbClient;
use App\Models\AcbBankAccountMetaDataModel;
use App\Models\AcbEnterpriseAccountModel;
use App\Models\BankSubAccountModel;
use Exception;

class ACBBankAccountConnectContext extends BankAccountConnectContext implements HasCreateOfficialVaSupport
{
    protected ?AcbClient $client = null;

    protected ?string $authorizedId = null;
    
    protected ?string $authorizationType= null;
    
    protected ?string $username = null;

    public function brandName(): string
    {
        return 'ACB';
    }

    public function bankId(): int
    {
        return 3;
    }

    public function hasIndividualBankAccountConnectSupport(): bool
    {
        return true;
    }

    public function hasEnterpriseBankAccountConnectSupport(): bool
    {
        return true;
    }

    public function authorizeIndividualBankAccountConnect(): bool
    {
        return true;
    }
    public function authorizeEnterpriseBankAccountConnect(): bool
    {
        return true;
    }

    public function individualBankAccountConnectSchema(): BankAccountConnectSchema
    {
        $schema = new BankAccountConnectSchema($this->brandName(), 'individual');
        
        if ($this->authorizationType !== 'SMS') {
            $schema->supportOtp(true, [
                'help_text' => 'Quý khách đang liên kết tài khoản ngân hàng <b>:account_number</b> với SePay, vui lòng kiểm tra ứng dụng ACB ONE để lấy mã OTP.'
            ]);
        } else {
            $schema->supportOtp(true);
        }
        
        if ($this->isSpeakerBillingSubscription() || $this->isShopBillingSubscription()) {
            $schema->createVaOption(true, array_merge([
                'is_official' => false,
                'content' => 'Bắt đầu tạo một QR nhận thanh toán đầu tiên cho tài khoản ngân hàng ACB của bạn để quản lý dòng tiền phát từ loa thanh toán.',
                'otp' => [
                    'help_text' => $this->authorizationType !== 'SMS' ? 'Quý khách đang tạo QR nhận thanh toán cho tài khoản ngân hàng <b>:account_number</b> với SePay, vui lòng kiểm tra ứng dụng ACB ONE để lấy mã OTP.' : 'Quý khách đang tạo QR nhận thanh toán cho tài khoản ngân hàng <b>:account_number</b> với SePay, vui lòng kiểm tra số điện thoại <b>:phone_number</b> để lấy mã OTP.',
                ]
            ], $this->isSpeakerBillingSubscription() ? $this->defaultCreateVaOptionForSpeakerBilling() : []));
        }

        return $schema
            ->enablePhoneNumberField(true, [
                'help_text' => 'Nếu số điện thoại đăng ký dịch vụ có phương thức xác thực là SMS sẽ bị thu phí theo <a href="https://acb.com.vn/thong-bao/thong-bao-thay-doi-bieu-phi-sms-banking-tu-ngay-********" target="_blank">biểu phí của ACB theo từng thời kỳ</a>'
            ])
            ->requireAgreement(true, [
                'type' => 'modal',
                'content' => view('_components/bank-account/agreements/acb', [], ['saveData' => false, 'debug' => false])
            ]);
    }

    public function enterpriseBankAccountConnectSchema(): BankAccountConnectSchema
    {
        $schema = new BankAccountConnectSchema($this->brandName(), 'enterprise');
        
        if ($this->authorizationType !== 'SMS') {
            $schema->supportOtp(true, [
                'help_text' => 'Quý khách đang liên kết tài khoản ngân hàng <b>:account_number</b> với SePay, vui lòng kiểm tra ứng dụng ACB ONE BIZ để lấy mã OTP.'
            ]);
        } else {
            $schema->supportOtp(true);
        }
        
        if ($this->isSpeakerBillingSubscription() || $this->isShopBillingSubscription()) {
            $schema->createVaOption(true, array_merge([
                'is_official' => false,
                'content' => 'Bắt đầu tạo một QR nhận thanh toán đầu tiên cho tài khoản ngân hàng ACB của bạn để quản lý dòng tiền phát từ loa thanh toán.',
                'otp' => [
                    'help_text' => $this->authorizationType !== 'SMS' ? 'Quý khách đang tạo QR nhận thanh toán cho tài khoản ngân hàng <b>:account_number</b> với SePay, vui lòng kiểm tra ứng dụng ACB ONE BIZ để lấy mã OTP.' : 'Quý khách đang tạo QR nhận thanh toán cho tài khoản ngân hàng <b>:account_number</b> với SePay, vui lòng kiểm tra số điện thoại <b>:phone_number</b> để lấy mã OTP.',
                ]
            ], $this->isSpeakerBillingSubscription() ? $this->defaultCreateVaOptionForSpeakerBilling() : []));
        }

        return $schema
            ->enablePhoneNumberField(true, [
                'help_text' => 'Nếu số điện thoại đăng ký dịch vụ có phương thức xác thực là SMS sẽ bị thu phí theo <a href="https://acb.com.vn/thong-bao/thong-bao-thay-doi-bieu-phi-sms-banking-tu-ngay-********" target="_blank">biểu phí của ACB theo từng thời kỳ</a>'
            ])
            ->requireAgreement(true, [
                'type' => 'modal',
                'content' => view('_components/bank-account/agreements/acb', [], ['saveData' => false, 'debug' => false])
            ])
            ->setAdditionalFields([
                'username' => [
                    'enabled' => true,
                    'label' => 'Tên đăng nhập ACB ONE BIZ',
                    'help_text' => 'Điền chính xác tên đăng nhập ACB ONE BIZ ứng với số tài khoản ngân hàng ở trên',
                    'rules' => [
                        'required' => true,
                        'max_length' => '100'
                    ]
                ]
            ]);
    }

    public function requestIndividualBankAccountConnect(): int
    {
        try {
            $this->client = new AcbClient;
            $response = $this->client->registerPushNotification(
                'PERS',
                $this->accountNumber,
                $this->phoneNumber,
                'ALL',
                'NONE',
                true,
            );

            $responseData = json_decode($response->getBody());

            $this->authorizedId = $responseData->responseData->authorizationId;
            
            $this->authorizationType = $responseData->responseData->authorizationType ?? 'SMS';
            
            return $this->requestConnectWithApi($responseData->responseData->requestId);
        } catch (Exception $e) {
            $errorCode = $e->getCode();

            if ($errorCode == '2010008') {
                return $this->failResponse(['account_number' => 'Số tài khoản không tồn tại hoặc đã bị khóa trên hệ thống ngân hàng ACB.']);
            }

            if (in_array($errorCode, ['2010005', '1020002'])) {
                return $this->failResponse(['phone_number' => 'Số điện thoại không được đăng ký cho tài khoản ngân hàng.']);
            }

            if (in_array($errorCode, ['********'])) {
                return $this->failResponse(['status' => false, 'message' => 'Yêu cầu quá nhiều lần, vui lòng thử lại sau 30 phút.'], self::UNEXPECTED_ERROR_CODE);
            }

            if (in_array($errorCode, ['********'])) {
                return $this->failResponse(['phone_number' => 'Số tài khoản này không thuộc nhóm khách hàng cá nhân.']);
            }

            throw $e;
        }
    }

    public function requestEnterpriseBankAccountConnect(): int
    {
        try {
            $this->client = new AcbClient;
            $response = $this->client->registerPushNotification(
                'ORG',
                $this->accountNumber,
                $this->phoneNumber,
                'ALL',
                'NONE',
                true,
                null,
                $this->username
            );

            $responseData = json_decode($response->getBody());

            $this->authorizedId = $responseData->responseData->authorizationId;
            
            $this->authorizationType = $responseData->responseData->authorizationType ?? 'SMS';
            
            return $this->requestConnectWithApi($responseData->responseData->requestId);
        } catch (Exception $e) {
            $errorCode = $e->getCode();

            if ($errorCode == '2010008') {
                return $this->failResponse(['account_number' => 'Số tài khoản không tồn tại hoặc đã bị khóa trên hệ thống ngân hàng ACB.']);
            }

            if (in_array($errorCode, ['2010005', '1020002'])) {
                return $this->failResponse(['phone_number' => 'Số điện thoại không được đăng ký cho tài khoản ngân hàng.']);
            }

            if (in_array($errorCode, ['********'])) {
                return $this->failResponse(['status' => false, 'message' => 'Yêu cầu quá nhiều lần, vui lòng thử lại sau 30 phút.'], self::UNEXPECTED_ERROR_CODE);
            }

            if (in_array($errorCode, ['********'])) {
                return $this->failResponse(['username' => 'Tên đăng nhập ACB ONE BIZ không được đăng ký cho tài khoản trên.']);
            }
            
            throw $e;
        }
    }

    public function confirmIndividualBankAccountConnect(): int
    {
        if (!$this->authorizedId) {
            throw $this->bankAccountConnectContextException('Missing authorizedId');
        }

        try {
            $this->client = new AcbClient;
            
            $this->client->verifyOtpNotification(
                $this->otpRequestId,
                $this->authorizedId,
                $this->otp
            );

            return $this->verifyOtpRequestConnect();
        } catch (Exception $e) {
            $errorCode = $e->getCode();

            if (in_array($errorCode, ['********', '********'])) {
                return $this->failResponse(['otp' => 'OTP không chính xác hoặc đã hết hiệu lực.']);
            }

            throw $e;
        }
    }

    public function confirmEnterpriseBankAccountConnect(): int
    {
        return $this->confirmIndividualBankAccountConnect();
    }

    protected function createdBankAccount(int $result): void
    {
        if ($this->canConnectWithEnterprise()) {
            $this->createEnterpriseAccount($result);
        }
        
        model(AcbBankAccountMetaDataModel::class)->insert([
            'bank_account_id' => $result,
            'realtime_transfer_type' => 'ALL',
        ]);
    }
    
    private function createEnterpriseAccount(int $id): void
    {
        model(AcbEnterpriseAccountModel::class)->insert(['bank_account_id' => $id, 'username' => $this->username]);
    }

    public function handleBankClientException(Exception $e): string
    {
        if ($this->client) {
            $this->client->forceDebug();
        }

        if (strpos($e->getMessage(), 'Operation timed out') !== false) {
            return 'Hệ thống ngân hàng ACB đang bận, vui lòng thử lại sau.';
        }

        if ($e instanceof DisableBankClientException) {
            return 'Hệ thống ngân hàng ACB đang bảo trì, vui lòng liên hệ SePay để biết thêm thông tin chi tiết';
        }

        return 'Đã có lỗi xảy ra, vui lòng thử lại sau hoặc liên hệ SePay để được hỗ trợ.';
    }

    /**
     * @return array<int,string>
     */
    protected function additionalSession(): array
    {
        return [
            'authorizedId' => 'authorized_id',
            'username' => 'username'
        ];
    }

    public function requestCreateIndividualOfficialVa(): int
    {
        if (!$this->bankAccountId) {
            throw $this->bankAccountConnectContextException('Bank account ID not found');
        }
        
        try {
            $this->client = new AcbClient;

            $response = $this->client->registerVa(
                'PERS',
                $this->accountNumber,
                $this->phoneNumber,
                true,
                [
                    'virtualAccountPrefixCode' => 'LOC',
                    'beneficiaryNameRule' => 1,
                    'virtualAccountExplain' => $this->accountHolderName
                ]
            );

            $responseData = json_decode($response->getBody());
            
            $this->authorizedId = $responseData->responseData->authorizationId;
            
            $this->authorizationType = $responseData->responseData->authorizationType ?? 'SMS';
            
            return $this->requestCreateOfficialVaWithApi($responseData->responseData->requestId);
        } catch (Exception $e) {
            $errorCode = $e->getCode();

            if (in_array($errorCode, ['********'])) {
                $this->failResponse(['status' => false, 'message' => "Tài khoản ngân hàng của bạn đã bị khóa, vui lòng liên hệ ngân hàng."]);
            }

            if (in_array($errorCode, ['********'])) {
                return $this->failResponse(['status' => false, 'message' => "Quý khách vui lòng thử lại sau ít phút nữa"]);
            }
            
            if (in_array($errorCode, ['********'])) {
                return $this->failResponse(['status' => false, 'message' => "Quý khách đã đạt giới hạn số lượng tài khoản định danh cho phép, vui lòng liên hệ SePay để được hỗ trợ"]);
            }

            throw $e;
        }
    }
    
    public function confirmCreateIndividualOfficialVa(): int
    {
        if (!$this->bankAccountId) {
            throw $this->bankAccountConnectContextException('Bank account ID not found');
        }
       
        try {
            $this->client = new AcbClient;
            
            $response = $this->client->verifyOtpVa(
                $this->otpRequestId,
                $this->authorizedId,
                $this->otp
            );

            $responseData = json_decode($response->getBody());

            $va = $responseData->responseData->virtualAccountNumber;

            $id = model(BankSubAccountModel::class)->insert([
                'sub_account' => $va,
                'sub_holder_name' => $this->accountHolderName,
                'acc_type' => 'Real',
                'bank_account_id' => $this->bankAccountId,
                'va_active' => 1,
            ]);
            
            if (!$id) {
                return $this->failResponse(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ.'], self::UNEXPECTED_ERROR_CODE);
            }

            return $this->verifyOtpCreateOfficialVa($id);
        } catch (Exception $e) {
            $errorCode = $e->getCode();

            if (in_array($errorCode, ['********', '********'])) {
                return $this->failResponse(['otp' => 'OTP không chính xác hoặc đã hết hiệu lực.']);
            }

            if (in_array($errorCode, ['********'])) {
                return $this->failResponse(['otp' => 'Bạn đã nhập sai quá số lần quy định, vui lòng lấy lại OTP mới.']);
            }

            throw $e;
        }
    }
    
    public function requestCreateEnterpriseOfficialVa(): int
    {
        throw $this->bankAccountConnectContextException('Service unavailable');
    }
    
    public function confirmCreateEnterpriseOfficialVa(): int
    {
        throw $this->bankAccountConnectContextException('Service unavailable');
    }
}
