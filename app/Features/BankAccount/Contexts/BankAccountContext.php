<?php

namespace App\Features\BankAccount\Contexts;

use App\Features\BankAccount\BankAccountFeature;
use App\Models\BankAccountModel;
use App\Exceptions\BankAccountFeatureException;

class BankAccountContext
{
    public ?object $bankAccount = null;
    
    protected BankAccountFeature $feature;
    
    protected ?int $bankId = null;
    
    public function __construct(BankAccountFeature $feature, ?int $bankId = null, ?int $bankAccountId = null)
    {
        $this->feature = $feature;
        $this->bankId = $bankId;
        
        if (!$bankId && !$bankAccountId) {
            throw new BankAccountFeatureException('Bank ID or Bank account ID is required');
        }
        
        if (!$bankAccountId) {
            return;
        }
        
        $bankAccountBuilder = model(BankAccountModel::class)
            ->where('id', $bankAccountId)
            ->where('bank_id', $this->bankId)
            ->where('merchant_id', null);
                
        if ($this->feature->determineIfCompanyContextIsSet()) {
            $bankAccountBuilder->where('company_id', $this->feature->companyContext()->company->id);
        }
        
        $this->bankAccount = $bankAccountBuilder->first();
        
        if (!$this->bankAccount) {
            throw new BankAccountFeatureException('Bank account not found');
        }
    }
    
    public function determineIfBankAccountExistsByAccountNumber(string $accountNumber): bool
    {
        return model(BankAccountModel::class)
            ->where('account_number', $accountNumber)
            ->where('bank_id', $this->bankId)
            ->countAllResults() > 0;
    }
}