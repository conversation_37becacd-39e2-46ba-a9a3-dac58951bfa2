<?php

namespace App\Features\BankAccount\Contexts;

use App\Exceptions\BankAccountFeatureException;
use App\Features\BankAccount\BankAccountFeature;
use Exception;

class MSBBankAccountConnectContext extends BankAccountConnectContext
{
    public string $brandName = 'MSB';

    protected BankAccountFeature $feature;

    public function bankId(): int
    {
        return 10;
    }

    public function hasIndividualBankAccountConnectSupport(): bool
    {
        return true;
    }

    public function hasEnterpriseBankAccountConnectSupport(): bool
    {
        return true;
    }

    public function authorizeIndividualBankAccountConnect(): bool
    {
        return true;
    }
    public function authorizeEnterpriseBankAccountConnect(): bool
    {
        return true;
    }

    public function requestIndividualBankAccountConnect(): int
    {
        $config = config(\Config\Msb::class);

        return $this->requestConnectWithWebView($config->msbPortalUrl);
    }

    public function requestEnterpriseBankAccountConnect(): int
    {
        return $this->requestIndividualBankAccountConnect();
    }

    public function confirmIndividualBankAccountConnect(): int
    {
        throw $this->bankAccountConnectContextException('Unavailable service');
    }

    public function confirmEnterpriseBankAccountConnect(): int
    {
        throw $this->bankAccountConnectContextException('Unavailable service');
    }

    protected function createdBankAccount(int $result): void
    {
        //
    }

    public function individualBankAccountConnectSchema(): BankAccountConnectSchema
    {
        $schema = new BankAccountConnectSchema($this->brandName, 'individual');

        return $this->defaultBankAccountConnectSchema($schema);
    }

    public function enterpriseBankAccountConnectSchema(): BankAccountConnectSchema
    {
        $schema = new BankAccountConnectSchema($this->brandName, 'enterprise');

        return $this->defaultBankAccountConnectSchema($schema);
    }

    private function defaultBankAccountConnectSchema(BankAccountConnectSchema $schema): BankAccountConnectSchema
    {
        return $schema
            ->setCanOnline()
            ->setRequestConnectWebview(1200, 680)
            ->setRequestConnectContent(view('_components/bank-account/request-connect-content/msb', [], ['saveData' => false, 'debug' => false]));
    }

    public function handleBankClientException(Exception $e): string
    {
        return 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ.';
    }
}
