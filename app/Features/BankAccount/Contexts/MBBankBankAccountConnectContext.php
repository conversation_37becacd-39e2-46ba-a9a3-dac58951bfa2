<?php

namespace App\Features\BankAccount\Contexts;

use App\Exceptions\DisableBankClientException;
use App\Features\BankAccount\Contexts\Interfaces\HasCreateOfficialVaSupport;
use App\Libraries\MbbClient;
use App\Libraries\MbbMmsClient;
use App\Models\BankSubAccountMetadataModel;
use App\Models\BankSubAccountModel;
use App\Models\MbbMmsMerchantModel;
use App\Models\MbbMmsTerminalModel;
use Exception;
use App\Features\BankAccount\Contexts\Interfaces\CanLookupAccountHolderName;
use App\Models\MbbEnterpriseAccountModel;

class MBBankBankAccountConnectContext extends BankAccountConnectContext implements CanLookupAccountHolderName, HasCreateOfficialVaSupport
{
    protected ?MbbClient $client = null;
    
    public function brandName(): string
    {
        return 'MBBank';
    }

    public function bankId(): int
    {
        return 8;
    }

    public function hasIndividualBankAccountConnectSupport(): bool
    {
        return true;
    }

    public function hasEnterpriseBankAccountConnectSupport(): bool
    {
        return true;
    }

    public function authorizeIndividualBankAccountConnect(): bool
    {
        return true;
    }
    public function authorizeEnterpriseBankAccountConnect(): bool
    {
        return true;
    }

    public function requestIndividualBankAccountConnect(): int
    {
        $this->client = new MbbClient;
        $response = $this->client->requestPushTransactionMessageSubscribe(
            $this->identificationNumber,
            $this->accountNumber,
            $this->accountHolderName,
            $this->phoneNumber,
            'SMS',
            'DC',
            'WEB_APP'
        );
        $responseData = json_decode($response->getBody(), true);

        if (! is_array($responseData)) {
            throw $this->bankAccountConnectContextException('Invalid response');
        }

        $errorCode = $responseData['errorCode'] ?? $responseData['soaErrorCode'];
        $errorDesc = $responseData['errorDesc'] ?? $responseData['soaErrorDesc'];

        // SIMULATE
        // $errorCode = '000';
        // $responseData['data']['requestId'] = uniqid();
        // SIMULATE

        if (in_array($errorCode, ['000', '40504'])) {
            if ($errorCode == '40504') {
                return $this->verifyOtpRequestConnect();
            }

            return $this->requestConnectWithApi($responseData['data']['requestId']);
        }

        if ($errorCode == '410') {
            return $this->failResponse([
                'identification_number' => 'Số CCCD/CMND không được đăng ký cho tài khoản trên.',
                'phone_number' => 'Số điện thoại không được đăng ký cho tài khoản trên, vui lòng liên hệ SePay để được hỗ trợ.'
            ]);
        }

        if ($errorCode == '293') {
            return $this->failResponse(['identification_number' =>  'Số CCCD/CMND không được đăng ký cho tài khoản trên.']);
        }

        if (in_array($errorCode, ['1020', '4422'])) {
            return $this->failResponse(['account_number' => 'Số tài khoản không tồn tại trên hệ thống ngân hàng MBBank.']);
        }

        // account holder name is invalid
        if ($errorCode == '40600') {
            return $this->failResponse(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng tải lại trang.'], self::UNEXPECTED_ERROR_CODE);
        }

        if ($errorCode == '219') {
            return $this->failResponse(['phone_number' => 'Số điện thoại không được đăng ký cho tài khoản trên, vui lòng liên hệ SePay để được hỗ trợ.']);
        }

        throw $this->bankAccountConnectContextException('#' . $errorCode . ' ' . $errorDesc);
    }

    public function requestEnterpriseBankAccountConnect(): int
    {
        return $this->requestIndividualBankAccountConnect();
    }

    public function confirmIndividualBankAccountConnect(): int
    {
        if ($this->verifiedOtpRequestConnect) {
            return self::SUCCESS_CODE;
        }

        if (!$this->otpRequestId) {
            return $this->failResponse(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng tải lại trang'], self::UNEXPECTED_ERROR_CODE);
        }

        $this->client = new MbbClient;
        $response = $this->client->confirmPushTransactionMessageSubscribe(
            $this->otpRequestId,
            $this->otp,
            'SMS',
            'WEB_APP'
        );
        $responseData = json_decode($response->getBody(), true);

        if (! is_array($responseData)) {
            throw $this->bankAccountConnectContextException('Invalid response');
        }

        $errorCode = $responseData['errorCode'] ?? $responseData['soaErrorCode'];
        $errorDesc = $responseData['errorDesc'] ?? $responseData['soaErrorDesc'];

        // SIMUATE
        // $errorCode = '000';
        // SIMULATE

        if ($errorCode == '000') {
            return $this->verifyOtpRequestConnect();
        }

        if (in_array($errorCode, ['40507', '237'])) {
            return $this->failResponse(['otp' => 'OTP không chính xác hoặc đã hết hiệu lực.']);
        }

        throw $this->bankAccountConnectContextException('#' . $errorCode . ' ' . is_array($errorDesc) ? json_encode($errorDesc) : $errorDesc);
    }

    public function confirmEnterpriseBankAccountConnect(): int
    {
        return $this->confirmIndividualBankAccountConnect();
    }

    protected function createdBankAccount(int $result): void
    {
        if ($this->canConnectWithEnterprise()) {
            $this->createEnterpriseAccount($result);
        }

        $this->syncTerminalToBankAccount($result);
    }

    private function createEnterpriseAccount(int $id): void
    {
        model(MbbEnterpriseAccountModel::class)->insert(['bank_account_id' => $id]);
    }

    private function syncTerminalToBankAccount(int $id): void
    {
        if (($this->isSpeakerBillingSubscription() || $this->isShopBillingSubscription()) && $this->outputDeviceDecal) {
            model(MbbMmsTerminalModel::class)->where('output_device_id', $this->outputDeviceDecal->output_device_id)->set(['bank_account_id' => $id])->update();
            
            return;
        }
        
        $mbbMmsConfig = config(\Config\MbbMms::class);

        if (!is_admin() && !$mbbMmsConfig->enabled) return;

        $merchant = model(MbbMmsMerchantModel::class)->first();
        $client = new MbbMmsClient();
        $client->setMerchantById($merchant->id);

        $response = $client->syncTerminalsWithNonOtp([
            array_merge([
                'terminalName' => $this->accountHolderName,
                'provinceCode' => '1',
                'districtCode' => '6',
                'wardsCode' => '178',
                'mccCode' => '1024',
                'fee' => 0,
                'bankCode' => '311',
                'bankCodeBranch' => '********',
                'bankAccountNumber' => $client->encryptData($this->accountNumber),
                'bankAccountName' => $this->accountHolderName,
                'bankCurrencyCode' => 1,
            ]),
        ]);

        $data = json_decode($response->getBody(), true);

        if ($data['errorCode'] != '000') {
            log_message('error', 'Sync terminal failed: ' . $response->getBody());
        }

        $result = $data['data']['result'][0];

        $model = model(MbbMmsTerminalModel::class);

        if ($result['syncStatus'] === 'create_ok') {
            $model->insert([
                'merchant_id' => $merchant->id,
                'bank_account_id' => $id,
                'terminal_id' => $result['terminalId'],
            ]);
        }
    }
    
    public function individualBankAccountConnectSchema(): BankAccountConnectSchema
    {
        $schema = new BankAccountConnectSchema($this->brandName(), 'individual');

        return $this->defaultBankAccountConnectSchema($schema);
    }

    public function enterpriseBankAccountConnectSchema(): BankAccountConnectSchema
    {
        $schema = new BankAccountConnectSchema($this->brandName(), 'enterprise');

        return $this->defaultBankAccountConnectSchema($schema)
            ->enableIdentificationNumberField(true, [
                'label' => 'Mã số thuế',
            ]);
    }

    private function defaultBankAccountConnectSchema(BankAccountConnectSchema $schema): BankAccountConnectSchema
    {
        return $schema
            ->enablePhoneNumberField()
            ->enableIdentificationNumberField()
            ->canLookupAccountHolderName()
            ->supportOtp(true, [
                'rules' => [
                    'regex_match' => '/^[0-9]{8}$/'
                ]
            ])
            ->createVaOption(true, array_merge([
                'otp' => [
                    'support' => false,
                ],
            ], $this->isSpeakerBillingSubscription() ? $this->defaultCreateVaOptionForSpeakerBilling() : []))
            ->requireAgreement(true, [
                'type' => 'modal',
                'content' => view('_components/bank-account/agreements/mbbank', [], ['saveData' => false, 'debug' => false])
            ])
            ->setCanOnline();
    }

    public function lookupAccountHolderName(string $accountNumber): ?string
    {
        $this->client = new MbbClient;
        $response = $this->client->getBankAccountInfo($accountNumber, 'ACCOUNT', 'INHOUSE');
        $responseData = json_decode($response->getBody(), true);

        if (! is_array($responseData)) {
            throw $this->bankAccountConnectContextException('Invalid response ' . $response->getBody());
        }

        $errorCode = $responseData['errorCode'] ?? $responseData['soaErrorCode'];
        $errorDesc = $responseData['errorDesc'] ?? $responseData['soaErrorDesc'];

        if ($errorCode == '000') {
            return $responseData['data']['accountName'];
        }

        if (in_array($errorCode, ['3014', '3001', '200'])) {
            return null;
        }

        throw $this->bankAccountConnectContextException('Unexpected response ' . $response->getBody());
    }

    public function handleBankClientException(Exception $e): string
    {
        if ($this->client) $this->client->logRequest();

        if (strpos($e->getMessage(), 'Operation timed out') !== false)
            return 'Hệ thống ngân hàng MB đang bận, vui lòng thử lại sau.';

        if (strpos($e->getMessage(), '40509 - Fail to subscribe to transaction notification') !== false) {
            return 'Tài khoản của quý khách đang liên kết ở nền tảng khác, vui lòng liên hệ SePay để được hỗ trợ.';
        }

        if ($e instanceof DisableBankClientException) {
            return 'Hệ thống ngân hàng MB đang bảo trì, vui lòng liên hệ SePay để biết thêm thông tin chi tiết.';
        }

        return 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ.';
    }

    public function requestCreateIndividualOfficialVa(): int
    {
        if (!$this->bankAccountId) {
            throw $this->bankAccountConnectContextException('Bank account ID not found');
        }
        
        $this->verifiedOtpRequestConnect = true;
        
        return self::SUCCESS_CODE;
    }
    
    public function confirmCreateIndividualOfficialVa(): int
    {
        if (!$this->bankAccountId) {
            throw $this->bankAccountConnectContextException('Bank account ID not found');
        }
        
        if ($this->outputDeviceDecal) {
            $va = $this->outputDeviceDecal->virtual_account_number;
            $qrcode = $this->outputDeviceDecal->qrcode;
        } else {
            $mbbMmsTerminal = model(MbbMmsTerminalModel::class)
                ->where('bank_account_id', $this->bankAccountId)
                ->first();
                
            if (!$mbbMmsTerminal) {
                throw $this->bankAccountConnectContextException('Terminal not found');
            }
    
            $client = new MbbMmsClient();
            $client->setMerchantById($mbbMmsTerminal->merchant_id);
    
            $response = $client->createQrCode(
                $mbbMmsTerminal->terminal_id,
                1,
                2,
                11,
                null,
                null,
                0,
                0,
                0,
                null,
                $this->accountHolderName,
                null,
                null
            );
    
            $resData = json_decode($response->getBody(), true);
    
            if ($resData['errorCode'] != '000') {
                throw $this->bankAccountConnectContextException('Create VA failed - ' . $response->getBody());
            }
            
            $va = $resData['data']['accountQR'];
            $qrcode = $resData['data']['qrcode'];
        }

        $id = model(BankSubAccountModel::class)->insert([
            'bank_account_id' => $this->bankAccountId,
            'acc_type' => 'Real',
            'sub_account' =>  $va,
            'sub_holder_name' => $this->accountHolderName,
        ]);
        
        if (!$id) {
            throw $this->bankAccountConnectContextException('Insert bank sub account failed');
        }

        model(BankSubAccountMetadataModel::class)->insert([
            'bank_account_id' => $this->bankAccountId,
            'bank_sub_account_id' => $id,
            'qrcode' => $qrcode,
        ]);
        
        return $this->verifyOtpCreateOfficialVa($id);
    }
    
    public function requestCreateEnterpriseOfficialVa(): int
    {
        return $this->requestCreateIndividualOfficialVa();
    }
    
    public function confirmCreateEnterpriseOfficialVa(): int
    {
        throw $this->confirmCreateIndividualOfficialVa();
    }
}
