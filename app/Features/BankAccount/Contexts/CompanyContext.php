<?php

namespace App\Features\BankAccount\Contexts;

use App\Features\BankAccount\BankAccountFeature;
use App\Exceptions\BankAccountFeatureException;
use App\Models\CompanyModel;
use App\Models\CompanySubscriptionModel;

class CompanyContext
{
    public ?object $company = null;
    
    protected BankAccountFeature $feature;
    
    public function __construct(BankAccountFeature $feature, int $companyId)
    {
        $this->feature = $feature;
        
        $this->company = model(CompanyModel::class)
            ->where('id', $companyId)
            ->where('merchant_id', null)
            ->first();
        
        if (! is_object($this->company)) {
            throw new BankAccountFeatureException('Company not found');
        }
    }
}