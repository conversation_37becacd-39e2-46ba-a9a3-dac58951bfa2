<?php

namespace App\Features\BankAccount\Contexts;

use Exception;
use App\Libraries\VietinbankClient;
use App\Exceptions\DisableBankClientException;
use App\Features\BankAccount\BankAccountFeature;
use App\Features\BankAccount\Contexts\BankAccountConnectSchema;

class VietinBankBankAccountConnectContext extends BankAccountConnectContext
{
    protected ?VietinbankClient $client = null;

    protected BankAccountFeature $feature;

    public function brandName(): string
    {
        return 'VietinBank';
    }

    public function bankId(): int
    {
        return 6;
    }

    public function hasIndividualBankAccountConnectSupport(): bool
    {
        helper(['general']);

        if (is_shop_billing_subscription()) {
            return false;
        }

        return true;
    }

    public function hasEnterpriseBankAccountConnectSupport(): bool
    {
        return true;
    }

    public function authorizeIndividualBankAccountConnect(): bool
    {
        return true;
    }
    public function authorizeEnterpriseBankAccountConnect(): bool
    {
        return true;
    }

    public function requestIndividualBankAccountConnect(): int
    {
        try {
            try {
                $this->client = new VietinbankClient;
                $response = $this->client->alertRegisterOTP(
                    '2', // personal type
                    $this->accountHolderName,
                    $this->accountNumber,
                    $this->identificationNumber,
                    $this->phoneNumber,
                );

                $responseData = json_decode($response->getBody());
                $errorCode = $responseData->status->code;
            } catch (Exception $e) {
                if ($e->getCode() != '13') throw $e;

                $responseData = null;
                $errorCode = '13';
            }

            if (in_array($errorCode, ['00', '13'])) {
                return $this->requestConnectWithApi($responseData->requestId);
            }

            throw $this->bankAccountConnectContextException('Unexpected response ' . $response->getBody());
        } catch (Exception $e) {
            $errorCode = $e->getCode();

             if ($errorCode == '2')
                return $this->failResponse(['account_number' => 'Số tài khoản không tồn tại hoặc đã bị khóa trên hệ thống ngân hàng VietinBank.']);

            if ($errorCode == '4')
                return $this->failResponse(['account_holder_name' =>  'Thông tin tên chủ tài khoản không trùng khớp.']);

            if ($errorCode == '75')
                return $this->failResponse(['identification_number' =>  'Số CCCD/CMND không được đăng ký cho tài khoản trên.']);

            if ($errorCode == '76')
                return $this->failResponse(['phone_number' => 'Số điện thoại không được đăng ký cho tài khoản trên, vui lòng liên hệ SePay để được hỗ trợ.']);

            throw $e;
        }
    }

    public function requestEnterpriseBankAccountConnect(): int
    {
        throw $this->bankAccountConnectContextException('Unavailable service');
    }

    public function confirmIndividualBankAccountConnect(): int
    {
        try {
            $this->client = new VietinbankClient;
            $this->client->alertVerifyOTP($this->otpRequestId, $this->otp);

            return $this->verifyOtpRequestConnect();
        } catch (Exception $e) {
            $errorCode = $e->getCode();

            if (in_array($errorCode, ['3', '31', '33'])) {
                return $this->failResponse(['otp' => 'OTP không chính xác hoặc đã hết hiệu lực.']);
            }

            throw $this->bankAccountConnectContextException('Unexpected response ' . $e->getMessage());
        }
    }

    public function confirmEnterpriseBankAccountConnect(): int
    {
        throw $this->bankAccountConnectContextException('Unavailable service');
    }

    protected function createdBankAccount(int $result): void
    {
        //
    }

    public function individualBankAccountConnectSchema(): BankAccountConnectSchema
    {
        $schema = new BankAccountConnectSchema($this->brandName(), 'individual');

        return $schema
            ->setCanOnline()
            ->enablePhoneNumberField()
            ->enableIdentificationNumberField()
            ->supportOtp()
            ->requireAgreement(true, [
                'type' => 'modal',
                'content' => view('_components/bank-account/agreements/vietinbank', [], ['saveData' => false, 'debug' => false])
            ])
            ->createVaOption(true, array_merge([
                'is_official' => false,
                'otp' => ['support' => false],
            ], $this->isSpeakerBillingSubscription() ? $this->defaultCreateVaOptionForSpeakerBilling() : []));
    }

    public function enterpriseBankAccountConnectSchema(): BankAccountConnectSchema
    {
        $schema = new BankAccountConnectSchema($this->brandName(), 'enterprise');

        return $schema->setCanOnline(false);
    }

    public function handleBankClientException(Exception $e): string
    {
        if ($this->client) {
            $this->client->forceDebug();
        }

        if (strpos($e->getMessage(), 'timed out') !== false)
            return 'Hệ thống ngân hàng VietinBank đang bận, vui lòng thử lại sau.';

        if ($e instanceof DisableBankClientException) {
            return 'Hệ thống ngân hàng VietinBank đang bảo trì, vui lòng liên hệ SePay để biết thêm thông tin chi tiết.';
        }

        return 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ.';
    }
}
