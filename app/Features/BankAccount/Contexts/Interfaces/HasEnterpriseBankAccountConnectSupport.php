<?php

namespace App\Features\BankAccount\Contexts\Interfaces;

use App\Features\BankAccount\Contexts\BankAccountConnectSchema;

interface HasEnterpriseBankAccountConnectSupport
{
    public function authorizeEnterpriseBankAccountConnect(): bool;
    
    public function enterpriseBankAccountConnectSchema(): BankAccountConnectSchema;
    
    public function requestEnterpriseBankAccountConnect(): void;
}