<?php

namespace App\Features\BankAccount\Contexts\Interfaces;

use App\Features\BankAccount\Contexts\BankAccountConnectSchema;

interface HasIndividualBankAccountConnectSupport
{
    public function authorizeIndividualBankAccountConnect(): bool;
    
    public function individualBankAccountConnectSchema(): BankAccountConnectSchema;
    
    public function requestIndividualBankAccountConnect(): void;
}
