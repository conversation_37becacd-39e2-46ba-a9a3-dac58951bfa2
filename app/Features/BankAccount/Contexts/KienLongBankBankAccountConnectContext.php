<?php

namespace App\Features\BankAccount\Contexts;

use App\Features\BankAccount\BankAccountFeature;
use App\Features\BankAccount\Contexts\BankAccountConnectContext;
use App\Models\ConfigurationModel;
use Exception;
use App\Features\BankAccount\Contexts\Interfaces\CanLookupAccountHolderName;
use App\Models\BankSubAccountModel;
use src\KPayClient;
use src\KPayPacker;
use src\verifyaccountno\request\CheckAccountNoRequest;
use src\verifyaccountno\request\LinkAccountRequest;
use src\verifyaccountno\request\VerifyLinkAccountRequest;
use src\virtualaccount\request\EnableVirtualAccountRequest;

class KienLongBankBankAccountConnectContext extends BankAccountConnectContext implements CanLookupAccountHolderName
{
    public string $brandName = 'KienLongBank';

    protected KPayClient $client;

    protected BankAccountFeature $feature;

    public function __construct(BankAccountFeature $feature, ?string $type = null)
    {
        parent::__construct($feature, $type);

        $klbConfig = config(\Config\Klb::class);

        $kPayPacker = new KPayPacker(
            $klbConfig->KlbClientID,
            $klbConfig->KlbEncryptKey,
            $klbConfig->KlbSecretKey,
            $klbConfig->KlbAcceptTimeDiff,
            $klbConfig->KlbHost,
        );

        $this->client = new KPayClient($kPayPacker);
    }

    public function bankId(): int
    {
        return 17;
    }

    public function hasIndividualBankAccountConnectSupport(): bool
    {
        return true;
    }

    public function hasEnterpriseBankAccountConnectSupport(): bool
    {
        return true;
    }

    public function authorizeIndividualBankAccountConnect(): bool
    {
        return true;
    }
    public function authorizeEnterpriseBankAccountConnect(): bool
    {
        return true;
    }

    public function requestIndividualBankAccountConnect(): int
    {
        $linkAccountRequest = new LinkAccountRequest($this->accountNumber);
        $linkAccountResponse = $this->client->linkAccountNo($linkAccountRequest);

        return $this->requestConnectWithApi($linkAccountResponse->getSessionId());
    }

    public function requestEnterpriseBankAccountConnect(): int
    {
        return $this->requestIndividualBankAccountConnect();
    }

    public function confirmIndividualBankAccountConnect(): int
    {
        try {
            $request = new VerifyLinkAccountRequest(
                $this->otpRequestId,
                $this->accountNumber,
                $this->otp
            );

            $response = $this->client->verifyLinkAccountNo($request);

            if ($response->isSuccess()) {
                return $this->verifyOtpRequestConnect();
            }

            return $this->failResponse(['otp' => 'OTP không chính xác hoặc đã hết hiệu lực.']);
        } catch (Exception $e) {
            return $this->failResponse(['otp' => 'OTP không chính xác hoặc đã hết hiệu lực.']);
        }
    }

    public function confirmEnterpriseBankAccountConnect(): int
    {
        return $this->confirmIndividualBankAccountConnect();
    }

    protected function createdBankAccount(int $result): void
    {
        if ($this->isSpeakerBillingSubscription() || $this->isShopBillingSubscription()) {
            $this->setFirstVaId($this->createFirstVa($result));
        }
    }

    private function createFirstVa(int $bankAccountId): int
    {
        $configurationModel = model(ConfigurationModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);

        $klbVaOrderLastNumberConfig = $configurationModel->where(['company_id' => 0, 'setting' => 'KLBVaOrderLastNumber'])->first();
        if (!$klbVaOrderLastNumberConfig) {
            $configurationModel->insert(['company_id' => 0, 'setting' => 'KLBVaOrderLastNumber', 'value' => 0]);
            $klbVaOrderLastNumber = 0;
        } else {
            $klbVaOrderLastNumber = $klbVaOrderLastNumberConfig->value;
        }

        $safeBankSubAccountData = [
            'sub_holder_name' => $this->accountHolderName,
            'va_order' => $klbVaOrderLastNumber + 1,
            'bank_account_id' => $bankAccountId,
            'acc_type' => 'Real'
        ];

        $timeout = 0;
        $fixAmount = 0;
        $fixContent = '';
        $request = new EnableVirtualAccountRequest($safeBankSubAccountData['va_order'], $timeout, $fixAmount, $fixContent, $this->accountNumber);
        $response = $this->client->enableVirtualAccount($request);

        if (!is_object($response) || (is_object($response) && !property_exists($response, 'virtualAccount'))) {
            $this->bankAccountConnectContextException('Create VA failed');
        }

        $safeBankSubAccountData['sub_account'] = $response->virtualAccount;

        $bankSubAccountId = $bankSubAccountModel->insert($safeBankSubAccountData);

        if (!$bankSubAccountId) {
            $this->bankAccountConnectContextException('Insert bank sub account failed');
        }

        $configurationModel
            ->where(['company_id' => 0, 'setting' => 'KLBVaOrderLastNumber'])
            ->set(['value' => $safeBankSubAccountData['va_order']])
            ->update();

        return $bankSubAccountId;
    }

    public function individualBankAccountConnectSchema(): BankAccountConnectSchema
    {
        $schema = new BankAccountConnectSchema($this->brandName, 'individual');

        return $this->defaultBankAccountConnectSchema($schema);
    }

    public function enterpriseBankAccountConnectSchema(): BankAccountConnectSchema
    {
        $schema = new BankAccountConnectSchema($this->brandName, 'enterprise');

        return $this->defaultBankAccountConnectSchema($schema);
    }

    protected function defaultBankAccountConnectSchema(BankAccountConnectSchema $schema): BankAccountConnectSchema
    {
        return $schema
            ->setCanOnline(true)
            ->enablePhoneNumberField(false)
            ->supportOtp(true, [
                'help_text' => 'Quý khách đang liên kết tài khoản ngân hàng <b>:account_number</b> với SePay, vui lòng kiểm tra số điện thoại để lấy mã OTP.'
            ])
            ->requireAgreement(true, [
                'type' => 'modal',
                'content' => view('_components/bank-account/agreements/kienlongbank', [], ['saveData' => false, 'debug' => false])
            ])
            ->canLookupAccountHolderName();;
    }

    public function lookupAccountHolderName(string $accountNumber): ?string
    {
        $request = new CheckAccountNoRequest($accountNumber);

        try {
            $response = $this->client->checkAccountNo($request);
        } catch (Exception $e) {
            $response = null;
        }

        if (is_object($response) && method_exists($response, 'getAccountName')) {
            return $response->getAccountName();
        }

        return null;
    }

    public function handleBankClientException(Exception $e): string
    {
        return 'Đã có lỗi xảy ra, vui lòng thử lại sau hoặc liên hệ SePay để được hỗ trợ.';
    }
}
