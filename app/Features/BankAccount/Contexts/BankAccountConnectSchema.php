<?php

namespace App\Features\BankAccount\Contexts;

use App\Exceptions\BankAccountFeatureException;

class BankAccountConnectSchema
{
    public object $bank;

    public string $type;

    public bool $canLookupAccountHolderName = false;

    public bool $canOnline = true;

    public ?string $requestConnectContent = null;

    public array $authorizeConnectWebview = [
        'width' => 600,
        'height' => 480,
        'auto_open' => false,
    ];
    
    public array $confirmConnectWebview = [
        'width' => 600,
        'height' => 480,
        'auto_open' => false,
    ];
    
    public array $requestConnectWebview = [
        'width' => 600,
        'height' => 480,
        'auto_open' => false,
    ];

    public ?string $confirmConnectType = null; // "api", "redirect", "interval", "callback"

    public ?string $authorizeConnectType = null; // "api", "redirect", "callback", "interval"
    
    public array $agreement = [
        'required' => false,
        'type' => 'modal', // modal, link
        'content' => '',
        'url' => ''
    ];

    public array $otp = [
        'support' => true,
        'help_text' => 'Qu<PERSON> khách đang liên kết tài khoản ngân hàng <b>:account_number</b> với SePay, vui lòng kiểm tra số điện thoại <b>:phone_number</b> để lấy mã OTP.',
        'rules' => [
            'regex_match' => '/^[0-9]{6}$/'
        ],
    ];
    
    public array $createVaOption = [
        'support' => false,
        'is_official' => true,
        'otp' => [
            'support' => true,
            'help_text' => 'Quý khách đang yêu cầu tạo tài khoản định danh cho số tài khoản ngân hàng <b>:account_number</b> với SePay, vui lòng kiểm tra số điện thoại <b>:phone_number</b> để lấy mã OTP.',
            'rules' => [
                'regex_match' => '/^[0-9]{6}$/'
            ],
        ],
        'fields' => [
            'vav' => [
                'enabled' => false,
                'label' => 'Số tài khoản định danh (VA)',
                'rules' => [
                    'required' => true,
                    'max_length' => 19,
                ],
                'vac' => '',
                'prefix' => ''
            ],
        ],
        'title' => 'Nguồn nhận biến động số dư cho tài khoản :brand_name',
        'content' => '
            <p>Bạn có thể bắt đầu với tài khoản định danh đầu tiên để dễ dàng quản lý dòng tiền của bạn hoặc nhận tất cả biến động số dư từ tài khoản chính.</p>
            <div class="alert alert-info">
                <div class="alert-message">
                    <p class="mb-1 fw-bold text-info"><i class="bi bi-info-circle-fill"></i> Quan trọng</p>
                    <p class="mb-0">Bạn có thể dễ dàng tùy chỉnh lại nguồn nhận biến động số dư để phù hợp với nhu cầu sau này.</p>
                </div>
            </div>
        ',
        'options' => [
            'true' => [
                'default' => true,
                'label' => 'Tài khoản định danh',
                'description' => 'Phân bổ giao dịch báo có của bạn với tài khoản định danh',
            ],
            'false' => [
                'label' => 'Số tài khoản chính',
                'description' => 'Nhận tất cả biến động số dư từ số tài khoản chính <b>:account_number</b> của bạn',
            ]
        ],
    ];

    public array $fields = [
        'account_holder_name' => [
            'enabled' => true,
            'label' => 'Tên người thụ hưởng',
            'help_text' => 'Tên người thụ hưởng của số tài khoản trên',
            'rules' => [
                'required' => true,
                'max_length' => 100,
            ],
            'disabled' => false
        ],
        'account_number' => [
            'enabled' => true,
            'label' => 'Số tài khoản',
            'input_type' => 'number',
            'rules' => [
                'required' => true,
                'max_length' => 20,
            ],
            'help_text' => 'Lưu ý không điền số tài khoản định danh như số điện thoại...'
        ],
        'phone_number' => [
            'enabled' => true,
            'label' => 'Số điện thoại',
            'rules' => [
                'required' => true,
                'min_length' => 10,
                'max_length' => 11,
                'regex_match' => '/^[0-9]{10,11}$/'
            ],
            'help_text' => ''
        ],
        'identification_number' => [
            'enabled' => false,
            'label' => 'Số CCCD/CMND',
            'rules' => [
                'required' => true,
                'max_length' => 20,
            ],
            'help_text' => ''
        ],
        'vav' => [
            'enabled' => false,
            'label' => 'Số tài khoản định danh (VA)',
            'rules' => [
                'required' => true,
                'max_length' => 19,
            ],
            'vac' => '',
            'prefix' => ''
        ],
        'label' => [
            'enabled' => true,
            'label' => 'Tên gợi nhớ',
            'rules' => [
                'required' => false,
                'max_length' => 100,
            ],
        ],
    ];

    public array $additionalFields = [];

    public function __construct(string $brandName, string $type = 'individual')
    {
        $bankFeature = service('bankFeature');
        $builder = $bankFeature->bankFilter()->onlyApiSupport();
        $this->type = $type === 'enterprise' ? 'enterprise' : 'individual';

        if ($this->type === 'enterprise') {
            $builder->onlyEnterpriseSupport();
        } else {
            $builder->onlyIndividualSupport();
        }

        $this->bank = $builder->findByBrandName($brandName);

        if (!$this->bank) {
            throw new BankAccountFeatureException('Bank account type is not supported');
        }
    }

    public function canLookupAccountHolderName(bool $value = true): BankAccountConnectSchema
    {
        $this->canLookupAccountHolderName = $value;

        $this->fields['account_holder_name']['help_text'] = $value
            ? 'Tên người thụ hưởng sẽ tự động điền khi bạn nhập số tài khoản'
            : $this->fields['account_holder_name']['help_text'];
        $this->fields['account_holder_name']['disabled'] = !$value;

        return $this;
    }

    /**
     * @param array<int,mixed> $data
     */
    public function enablePhoneNumberField(bool $value = true, array $data = []): BankAccountConnectSchema
    {
        $this->fields['phone_number']['enabled'] = $value;

        foreach ($data as $key => $value) {
            if (isset($this->fields['phone_number'][$key])) {
                $this->fields['phone_number'][$key] = $value;
            }
        }

        return $this;
    }
    /**
     * @param array<int,mixed> $data
     */
    public function enableIdentificationNumberField(bool $value = true, array $data = []): BankAccountConnectSchema
    {
        $this->fields['identification_number']['enabled'] = $value;

        foreach ($data as $key => $value) {
            if (isset($this->fields['identification_number'][$key])) {
                $this->fields['identification_number'][$key] = $value;
            }
        }

        return $this;
    }

    public function enableLabelField(bool $value = true, array $data = []): BankAccountConnectSchema
    {
        $this->fields['label']['enabled'] = $value;

        return $this;
    }

    public function modifyField(string $name, array $data = []): BankAccountConnectSchema
    {
        foreach ($data as $key => $value) {
            if (isset($this->fields[$name][$key])) {
                $this->fields[$name][$key] = $value;
            }
        }

        return $this;
    }

    /**
     * @param array<int,mixed> $data
     */
    public function supportOtp(bool $value = true, array $data = []): BankAccountConnectSchema
    {
        $this->otp['support'] = $value;

        foreach ($data as $key => $value) {
            if (isset($this->otp[$key])) {
                $this->otp[$key] = $value;
            }
        }

        return $this;
    }
    
    /**
     * @param array<int,mixed> $data
     */
    public function requireVa(bool $value = true, array $data = []): BankAccountConnectSchema
    {
        $this->fields['vav']['enabled'] = $value;

        foreach ($data as $key => $value) {
            if (isset($this->fields['vav'][$key])) {
                $this->fields['vav'][$key] = $value;
            }
        }

        $this->fields['vav']['max_length'] = 19 - strlen($this->fields['vav']['vac']);

        return $this;
    }
    
    /**
     * @param array<int,mixed> $data
     */
    public function createVaOption(bool $value = true, array $data = []): BankAccountConnectSchema
    {
        
        $this->createVaOption['support'] = $value;

        foreach ($data as $key => $value) {
            if (isset($this->createVaOption[$key])) {
                $this->createVaOption[$key] = $value;
            }
        }

        return $this;
    }

    public function setCanOnline(bool $value = true): BankAccountConnectSchema
    {
        $this->canOnline = $value;

        return $this;
    }
    /**
     * @param array<int,mixed> $data
     */
    public function setAdditionalFields(array $data = []): BankAccountConnectSchema
    {
        $this->additionalFields = $data;

        return $this;
    }
    /**
     * @param array<int,mixed> $data
     */
    public function requireAgreement(bool $value = true, array $data = []): BankAccountConnectSchema
    {
        $this->agreement['required'] = $value;

        foreach ($data as $key => $value) {
            if (isset($this->agreement[$key])) {
                $this->agreement[$key] = $value;
            }
        }

        return $this;
    }

    public function setRequestConnectWebview(?int $width = null, ?int $height = null, bool $autoOpen = false): BankAccountConnectSchema
    {
        if ($width > 0) {
            $this->requestConnectWebview['width'] = $width;
        }

        if ($height > 0) {
            $this->requestConnectWebview['height'] = $height;
        }

        $this->requestConnectWebview['auto_open'] = $autoOpen;

        foreach ($this->fields as $name => $field) {
            $this->fields[$name]['enabled'] = false;
        }

        foreach ($this->additionalFields as $name => $field) {
            $this->fields[$name]['enabled'] = false;
        }

        return $this;
    }
    
    public function setConfirmConnectWebview(?int $width = null, ?int $height = null, bool $autoOpen = false): BankAccountConnectSchema
    {
        if ($width > 0) {
            $this->confirmConnectWebview['width'] = $width;
        }

        if ($height > 0) {
            $this->confirmConnectWebview['height'] = $height;
        }

        $this->confirmConnectWebview['auto_open'] = $autoOpen;

        return $this;
    }
    
    public function setAuthorizeConnectWebview(?int $width = null, ?int $height = null, bool $autoOpen = false): BankAccountConnectSchema
    {
        if ($width > 0) {
            $this->authorizeConnectWebview['width'] = $width;
        }

        if ($height > 0) {
            $this->authorizeConnectWebview['height'] = $height;
        }

        $this->authorizeConnectWebview['auto_open'] = $autoOpen;

        return $this;
    }

    public function setRequestConnectContent(string $content): BankAccountConnectSchema
    {
        $this->requestConnectContent = $content;

        return $this;
    }

    public function confirmConnectWithCallback(): BankAccountConnectSchema
    {
        $this->confirmConnectType = 'callback';

        $this->supportOtp(false);

        return $this;
    }

    public function confirmConnectWithApi(): BankAccountConnectSchema
    {
        $this->confirmConnectType = 'api';

        $this->supportOtp(true);

        return $this;
    }

    public function confirmConnectWithRedirect(): BankAccountConnectSchema
    {
        $this->confirmConnectType = 'redirect';

        $this->supportOtp(false);

        return $this;
    }
    
    public function authorizeConnectWithRedirect(): BankAccountConnectSchema
    {
        $this->authorizeConnectType = 'redirect';

        return $this;
    }

    /**
     * @return array<string,mixed>
     */
    public function toArray(): array
    {
        $data = [
            'bank' => $this->bank,
            'type' => $this->type,
            'can_online' => $this->canOnline,
        ];

        if (!$this->canOnline) {
            return $data;
        }

        $data['can_lookup_account_holder_name'] = $this->canLookupAccountHolderName;
        $data['otp'] = $this->otp;
        $data['confirm_connect_type'] = $this->confirmConnectType;
        $data['request_connect_content'] = $this->requestConnectContent;
        $data['agreement'] = $this->agreement;
        $data['fields'] = $this->fields;
        $data['additional_fields'] = $this->additionalFields;
        $data['request_connect_webview'] = $this->requestConnectWebview;
        $data['authorize_connect_webview'] = $this->authorizeConnectWebview;
        $data['confirm_connect_webview'] = $this->confirmConnectWebview;
        $data['confirm_connect_type'] = $this->confirmConnectType;
        $data['authorize_connect_type'] = $this->authorizeConnectType;
        $data['create_va_option'] = $this->createVaOption;

        return $data;
    }

    /**
     * @return array<string, array>
     */
    public function fieldRules(): array
    {
        $rules = [];

        foreach (array_merge($this->fields, $this->additionalFields) as $name => $fieldOption) {
            if (!isset($fieldOption['enabled']) || !$fieldOption['enabled']) continue;

            $rules[$name] = array_filter([
                isset($fieldOption['rules']['required']) && $fieldOption['rules']['required'] ? 'required' : 'permit_empty',
                isset($fieldOption['type']) && $fieldOption['type'] === 'select' ? 'in_list[' . implode(',', array_keys($fieldOption['options'])) . ']' : 'string',
                isset($fieldOption['rules']['max_length']) ? 'max_length[' . $fieldOption['rules']['max_length'] . ']' : null,
                isset($fieldOption['rules']['min_length']) ? 'min_length[' . $fieldOption['rules']['min_length'] . ']' : null,
                isset($fieldOption['rules']['regex_match']) ? 'regex_match[' . $fieldOption['rules']['regex_match'] . ']' : null,
            ]);
        }

        return $rules;
    }
    /**
     * @return array<string, array>
     */
    public function createVaOptionFieldRules(): array
    {
        $rules = [];

        foreach (array_merge($this->createVaOption['fields']) as $name => $fieldOption) {
            if (!isset($fieldOption['enabled']) || !$fieldOption['enabled']) continue;

            $rules[$name] = array_filter([
                isset($fieldOption['rules']['required']) && $fieldOption['rules']['required'] ? 'required' : 'permit_empty',
                'string',
                isset($fieldOption['rules']['max_length']) ? 'max_length[' . $fieldOption['rules']['max_length'] . ']' : null,
                isset($fieldOption['rules']['min_length']) ? 'min_length[' . $fieldOption['rules']['min_length'] . ']' : null,
                isset($fieldOption['rules']['regex_match']) ? 'regex_match[' . $fieldOption['rules']['regex_match'] . ']' : null,
            ]);
        }

        return $rules;
    }
}
