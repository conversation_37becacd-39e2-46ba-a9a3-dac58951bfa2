<?php

namespace App\Commands;

use CodeIgniter\CLI\CLI;
use App\Libraries\NapasClient;
use CodeIgniter\CLI\BaseCommand;

class QueryDRNapasCommand extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'Napas';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'napas:query-dr';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = '';

    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'napas:query-dr [arguments] [options]';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = [
        'id' => 'ID of the investigation to query',
    ];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = [];

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        $id = $params[0] ?? null;
        
        if (empty($id)) {
            CLI::error('ID is required');
            CLI::write('Usage: php spark napas:query-dr <investigation_id>');
            return;
        }

        $client = new NapasClient();
        $response = $client->investigation($id);

        if ($response->getStatusCode() !== 200) {
            CLI::error('Error: ' . $response->getReasonPhrase());
            CLI::write($response->getBody(), 'red');
            return;
        }
        
        CLI::write($response->getBody(), 'green');
    }
}
