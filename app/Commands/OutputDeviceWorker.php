<?php

namespace App\Commands;

use CodeIgniter\CLI\CLI;
use CodeIgniter\CLI\BaseCommand;
use App\Libraries\RabbitMQClient;
use App\Actions\OutputDevice\ExecuteOutputDevice;

class OutputDeviceWorker extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'Worker';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'worker:output_device';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = '';

    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'command:name [arguments] [options]';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = [];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = [];

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        $rabbitmq = new RabbitMQClient();
        $connected = $rabbitmq->connect();

        if (!$connected) {
            CLI::error('Cannot connect to RabbitMQ.');
            return;
        }

        $rabbitmq->queueDeclare('output_device');

        CLI::write("[*] Waiting for messages. To exit press CTRL+C\n");

        $callback = function ($msg) {
            $db = db_connect();

            CLI::write("[" . date('Y-m-d H:i:s') . "] Received " . $msg->getBody() . "\n");
            $data = json_decode($msg->getBody(), true);
            $transactionID = $data['parser_id'];
            $results = ExecuteOutputDevice::run($transactionID,0);
            log_message("debug","CLI tran: ".($transactionID));
            log_message("debug","CLI: ".($data['parser_id']));
            CLI::write(json_encode($results));
            CLI::write("[" . date('Y-m-d H:i:s') . "] Done\n");
            log_message('error', '[PID: ' . getmypid() . ' - Ouput device worker debug]: ' . $data['parser_id']);
            $msg->ack();

            $db->close();
        };
        
        $rabbitmq->channel->basic_qos(null, 1, false);
        $rabbitmq->channel->basic_consume($rabbitmq->getQueueDeclaration('output_device', 'name'), '', false, false, false, false, $callback);

        try {
            $rabbitmq->channel->consume();
        } catch (\Throwable $e) {
            log_message('error', 'output device worker failed: ' . $e->getMessage() . ' - ' . $e->getTraceAsString());
            CLI::error($e->getMessage());
        }

        $rabbitmq->close();
    }
}
