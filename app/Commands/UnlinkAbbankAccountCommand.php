<?php

namespace App\Commands;

use App\Actions\BankAccount\DeleteCompanyBankAccountAction;
use App\Libraries\AbbankClient;
use App\Models\BankAccountModel;
use App\Models\CompanyModel;
use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use Exception;
use GuzzleHttp\Exception\RequestException;

class UnlinkAbbankAccountCommand extends BaseCommand
{
    protected $group = 'CodeIgniter';

    protected $name = 'unlink:abbank:account';

    protected $description = 'Unlink ABBANK account manually';

    protected $usage = 'unlink:abbank:account [serialNumber] [accountNumber] [options]';

    protected $arguments = [
        'serialNumber' => 'Serial number of the device',
        'accountNumber' => 'ABBANK account number',
    ];

    public function run(array $params)
    {
        if (! isset($params[0]) || ! isset($params[1])) {
            CLI::write('Serial number and account number are required');
            return;
        }

        try {
            $client = new AbbankClient();
            $response = $client->unlinkAccount($params[0], $params[1]);

            $data = json_decode($response->getBody()->getContents(), true);

            if ($data['code'] == '00') {
                $bankAccountModel = model(BankAccountModel::class);

                $bankAccount = $bankAccountModel->where('account_number', $params[1])->first();

                if (! $bankAccount) {
                    CLI::write('Bank account not found');
                    return;
                }

                $company = model(CompanyModel::class)->find($bankAccount->company_id);

                DeleteCompanyBankAccountAction::run($bankAccount, $company, $_SERVER['REMOTE_ADDR'], self::class);

                CLI::write('Unlink account success');
            } else {
                CLI::write('Unlink account failed: ' . $data['details'] ?? $data['message']);
            }
        } catch (RequestException $e) {
            if ($e->hasResponse()) {
                $errorBody = $e->getResponse()->getBody()->getContents();
                CLI::write('Unlink account failed: ' . $errorBody);
            } else {
                CLI::write('Unlink account failed: ' . $e->getMessage());
            }
        } catch (Exception $e) {
            CLI::write('Unlink account failed: ' . $e->getMessage());
        }
    }
}
