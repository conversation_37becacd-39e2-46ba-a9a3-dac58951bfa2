<?php

namespace App\Commands;

use App\Models\TpbankTraceCompanyMapModel;
use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class PruneTpbankTraceCommand extends BaseCommand
{
    protected $group = 'TPBank';

    protected $name = 'tpbank:prune-traces';

    protected $description = 'Prune all expired TPBank trace numbers.';

    protected $usage = 'tpbank:prune-traces [days]';

    protected $arguments = [
        'days' => 'The number of days to keep the trace numbers. Default is 7 days.',
    ];

    public function run(array $params)
    {
        $days = $params[0] ?? 7;

        if (! is_numeric($days) || $days < 1) {
            CLI::write('Days must be a positive integer.', 'red');

            return;
        }

        CLI::write('Pruning all expired TPBank trace numbers...', 'yellow');

        model(TpbankTraceCompanyMapModel::class)
            ->where('created_at <', date('Y-m-d H:i:s', strtotime("-{$days} days")))
            ->delete();

        if ($deleted = db_connect()->affectedRows()) {
            CLI::write("{$deleted} trace numbers have been pruned.", 'green');
        } else {
            CLI::write('No trace numbers have been pruned.', 'yellow');
        }
    }
}
