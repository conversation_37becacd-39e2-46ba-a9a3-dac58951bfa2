<?php

namespace App\Commands;

use CodeIgniter\CLI\CLI;
use CodeIgniter\CLI\BaseCommand;
use App\Models\MerchantAccessTokenModel;

class PruneMerchantAccessTokenCommand extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'Merchant';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'merchant:prune-access-tokens';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = 'Prune all expired merchant access tokens.';

    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'merchant:prune-access-tokens';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = [];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = [];

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        model(MerchantAccessTokenModel::class)->where(['expires_at <=' => date('Y-m-d H:i:s')])->delete();

        CLI::write("Deleted all expired tokens");
    }
}
