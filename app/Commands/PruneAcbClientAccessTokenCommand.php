<?php

namespace App\Commands;

use CodeIgniter\CLI\CLI;
use CodeIgniter\CLI\BaseCommand;
use App\Models\AcbClientAccessTokenModel;

class PruneAcbClientAccessTokenCommand extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'ACB';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'acb:prune-access-token';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = '';

    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'acb:prune-access-token';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = [];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = [];

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        model(AcbClientAccessTokenModel::class)->where(['expires_at <=' => date('Y-m-d H:i:s')])->delete();

        CLI::write("Deleted all expired tokens");
    }
}
