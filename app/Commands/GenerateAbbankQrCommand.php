<?php

namespace App\Commands;

use App\Libraries\AbbankClient;
use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use Exception;
use GuzzleHttp\Exception\RequestException;

class GenerateAbbankQrCommand extends BaseCommand
{
    protected $group = 'CodeIgniter';

    protected $name = 'abbank:generate-qr';

    protected $description = 'Generate ABBANK QR code';

    protected $usage = 'abbank:generate-qr [deviceId] [accountNumber] [amount] [description] [options]';

    protected $arguments = [
        'deviceId'      => 'Device ID',
        'accountNumber' => 'ABBANK account number',
    ];

    protected $options = [
        '--amount'        => 'Amount to pay',
        '--description'   => 'Payment description',
    ];

    public function run(array $params)
    {
        if (count($params) < 2) {
            CLI::write('Device ID, account number are required');
            return;
        }

        $deviceId = $params[0];
        $accountNumber = $params[1];
        $amount = $params[2] ?? null;
        $description = $params[3] ?? null;
        
        try {
            $client = new AbbankClient();
            $response = $client->generateQr(
                $deviceId,
                $accountNumber,
                '970425',
                $amount,
                'VND',
                $description,
                $amount ? '12' : '11'
            );

            $data = json_decode($response->getBody()->getContents(), true);

            if ($data['code'] == '00') {
                CLI::write('QR code generated successfully');
                CLI::write('QR code: ' . ($data['data']['qrData'] ?? 'N/A'));
            } else {
                CLI::write('Failed to generate QR code: ' . $data['details'] ?? $data['message']);
            }
        } catch (RequestException $e) {
            if ($e->hasResponse()) {
                $errorBody = $e->getResponse()->getBody()->getContents();
                CLI::write('Failed to generate QR code: ' . $errorBody);
            } else {
                CLI::write('Failed to generate QR code: ' . $e->getMessage());
            }
        } catch (Exception $e) {
            CLI::write('Failed to generate QR code: ' . $e->getMessage());
        }
    }
} 