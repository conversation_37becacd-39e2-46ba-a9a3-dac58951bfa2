<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use App\Models\MbbNotificationTokenModel;

class PruneMbbNotificationTokenCommand extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'MBB';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'mbb:prune-notification-tokens';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = 'Prune all the expired MBB notification tokens.';

    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'mbb:prune-notification-tokens';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = [];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = [];

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        model(MbbNotificationTokenModel::class)->where(['expires_at <=' => date('Y-m-d H:i:s')])->delete();

        CLI::write("Deleted all expired tokens");
    }
}
