<?php

namespace App\Commands;

use CodeIgniter\CLI\CLI;
use App\Models\PgIpnModel;
use App\Models\PgOrderModel;
use App\Models\PgIpnQueueModel;
use CodeIgniter\CLI\BaseCommand;
use App\Libraries\RabbitMQClient;
use App\Models\PgTransactionModel;

class PgIpnWorker extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'Worker';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'worker:pg:ipn';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = '';

    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'worker:pg:ipn';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = [];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = [];

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        helper(['general']);

        $rabbitmq = new RabbitMQClient();
        $connected = $rabbitmq->connect();

        if (!$connected) {
            CLI::error('Cannot connect to RabbitMQ.');
            return;
        }

        $queueName = 'pg_ipn';
        $rabbitmq->queueDeclare($queueName);

        CLI::write("[*] Waiting for messages. To exit press CTRL+C\n");

        $callback = function ($msg) {
            $db = db_connect();

            CLI::write(sprintf("[%s] Received %s\n", date('Y-m-d H:i:s'), $msg->getBody()));
            /**
             * @var array{pg_merchant_id:int,pg_order_id:int,pg_transaction_id:int,notification_type:"ORDER_PAID"|"RENEWAL_ORDER_PAID"} $data
             */
            $data = json_decode($msg->getBody(), true);

            /** @var PgIpnModel $pgIpnModel */
            $pgIpnModel = slavable_model(PgIpnModel::class, 'PaymentGateway');
            $pgIpn = $pgIpnModel->where('pg_merchant_id', $data['pg_merchant_id'])->where('active', 1)->first();

            if (!$pgIpn) {
                log_message('error', sprintf('[PgIpnWorker] PG IPN not found for merchant ID: %d', $data['pg_merchant_id']));
                CLI::error(sprintf('PG IPN not found for merchant ID: %d', $data['pg_merchant_id']));
                $msg->ack();
                return;
            }

            $pgOrderModel = slavable_model(PgOrderModel::class, 'PaymentGateway');
            $pgTransactionModel = slavable_model(PgTransactionModel::class, 'PaymentGateway');

            $pgOrder = $pgOrderModel->find($data['pg_order_id']);

            if (!$pgOrder) {
                $pgOrder = model(PgOrderModel::class)->find($data['pg_merchant_id']);
            }

            if (!$pgOrder) {
                log_message('error', sprintf('[PgIpnWorker] PG Order not found for ID: %d | Merchant ID: %s', $data['pg_order_id'], $data['pg_merchant_id']));
                CLI::error(sprintf('PG Order not found for ID: %d | Merchant ID: %s', $data['pg_order_id'], $data['pg_merchant_id']));
                $msg->ack();
                return;
            }

            $pgTransaction = $pgTransactionModel->where('pg_order_id', $data['pg_order_id'])
                ->where('pg_merchant_id', $data['pg_merchant_id'])
                ->where('id', $data['pg_transaction_id'])
                ->first();

            if (!$pgTransaction) {
                $pgTransaction = model(PgTransactionModel::class)->where('pg_order_id', $data['pg_order_id'])
                    ->where('pg_merchant_id', $data['pg_merchant_id'])
                    ->where('id', $data['pg_transaction_id'])
                    ->first();
            }

            if (!$pgTransaction) {
                log_message('error', sprintf('[PgIpnWorker] PG Transaction not found for ID: %d | PG Merchant ID: %d | PG Order ID: %d', $data['pg_transaction_id'], $data['pg_merchant_id'], $data['pg_order_id']));
                CLI::error(sprintf('PG Transaction not found for ID: %d | PG Merchant ID: %d | PG Order ID: %d', $data['pg_transaction_id'], $data['pg_merchant_id'], $data['pg_order_id']));
                $msg->ack();
                return;
            }

            $success = $pgIpnModel->send($data['notification_type'], $pgIpn, $pgOrder, $pgTransaction, 'First');

            if (!$success) {
                $ipnQueueModel = model(PgIpnQueueModel::class);
                $ipnQueueModel->insert([
                    'pg_merchant_id' => $pgIpn->pg_merchant_id,
                    'company_id' => $pgIpn->company_id,
                    'pg_order_id' => $pgOrder->id,
                    'pg_transaction_id' => $pgTransaction->id,
                    'pg_ipn_id' => $pgIpn->id,
                    'status' => 'SoftFailed',
                ]);
            }

            CLI::write(sprintf("[%s] Done\n", date('Y-m-d H:i:s')));

            log_message('error', sprintf('[PgIpnWorker] Acked message: %s | PID: %s', $msg->getBody(), getmypid()));
            $msg->ack();

            $db->close();
        };
        
        $rabbitmq->channel->basic_qos(null, 1, false);
        $rabbitmq->channel->basic_consume($rabbitmq->getQueueDeclaration($queueName, 'name'), '', false, false, false, false, $callback);

        try {
            $rabbitmq->channel->consume();
        } catch (\Throwable $e) {
            log_message('error', sprintf('[PgIpnWorker] Failed to consume message: %s | Trace: %s', $e->getMessage(), $e->getTraceAsString()));
            CLI::error($e->getMessage());
        }

        $rabbitmq->close();
    }
}
