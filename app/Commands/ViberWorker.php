<?php

namespace App\Commands;

use App\Features\Viber\ViberFeature;
use App\Libraries\RabbitMQClient;
use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class ViberWorker extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'CodeIgniter';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'worker:viber';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = '';

    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'command:name [arguments] [options]';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = [];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = [];

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        $rabbitmq = new RabbitMQClient();
        $connected = $rabbitmq->connect();
        if (!$connected) {
            CLI::error('Cannot connect to RabbitMQ.');
            return;
        }
        $rabbitmq->queueDeclare('viber');
        CLI::write("[*] Waiting for messages. To exit press CTRL+C\n");
        $callback = function ($msg) use ($rabbitmq) {
            $db = db_connect();
            CLI::write("[" . date('Y-m-d H:i:s') . "] Received " . $msg->getBody() . "\n");
            $data = json_decode($msg->getBody(), true);
            $transactionId = (int) $data['parser_id'];
            
            $viberFeature = new ViberFeature();
            $viberFeature->sendNotificationByTransactionId($transactionId);
            
            CLI::write("[" . date('Y-m-d H:i:s') . "] Done\n");
            log_message('error', '[PID: ' . getmypid() . ' - Viber worker debug]: ' . $transactionId);
            $msg->ack();
            $db->close();
        };
        
        $rabbitmq->channel->basic_qos(null, 1, false);
        $rabbitmq->channel->basic_consume($rabbitmq->getQueueDeclaration('viber', 'name'), '', false, false, false, false, $callback);
        try {
            $rabbitmq->channel->consume();
        } catch (\Throwable $e) {
            log_message('error', 'Viber worker: ' . $e->getMessage());
            CLI::error($e->getMessage());
        }
        $rabbitmq->close();
    }
}
