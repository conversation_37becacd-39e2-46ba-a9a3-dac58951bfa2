<?php

namespace App\Commands;

use App\Models\OutputDeviceReplayMessageQueueModel;
use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use App\Actions\OutputDevice\ExecuteOutputDevice;
use App\Libraries\FlametechvnClient;
use App\Models\OutputDeviceModel;
use Config\MQTT;
use App\Models\TransactionsModel;
class OutputDeviceRetryWorker extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'CodeIgniter';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'worker:output_device_retry';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = '';

    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'worker:output_device_retry [arguments] [options]';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = [];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = [];

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        CLI::write('Starting OutputDeviceRetryWorker...');
        $OutputDeviceReplayMessageQueueModel = model(OutputDeviceReplayMessageQueueModel::class);
        $OutputDeviceModel = model(OutputDeviceModel::class);
    
        while (true) {
            // Lấy MQTT configuration
            $configMQTT = config(MQTT::class);
    
            if (empty($configMQTT)) {
                CLI::write('Not found config !', 'yellow');
                sleep(20);
                continue;
            }
    
            if (!isset($configMQTT->retry_time)) {
                CLI::write('Not found retry time !', 'yellow');
                sleep(20);
                continue;
            }
    
            // Tính toán thời gian hiện tại trừ đi khoảng thời gian retry
            $current_time_retry = date('Y-m-d H:i:s', time() - $configMQTT->retry_time);
            
            // Lấy các bản ghi có status là 'Failed' và nằm trong khoảng thời gian cần kiểm tra
            $failedMessages = $OutputDeviceReplayMessageQueueModel
                ->where('status', 'Failed')
                ->where(['created_at >=' =>$current_time_retry])
                ->findAll();
    
            if (empty($failedMessages)) {
                CLI::write('No failed messages found!', 'yellow');
                sleep(20);
                continue;
            }
    
            foreach ($failedMessages as $message) {
                $created_at = strtotime($message['created_at']);
    
                // Kiểm tra thời gian retry
                if (time() - $created_at >= $configMQTT->retry_time) {
                    CLI::write('Ex Time for Message ID: ' . $message['id'], 'yellow');
                    continue;
                }
    
                CLI::write('Message ID will retry: ' . $message['id']);
    
                // Kiểm tra số lần retry
                if ($message['retries_count'] >= $configMQTT->max_retry_count) {
                    CLI::write("Retry max count reached for Message ID: " . $message['id'], 'red');
                    continue;
                }
    
                $device = $OutputDeviceModel->where(['id' => $message['output_device_id']])->get()->getRowArray();
                if($device['vendor'] == "Aisino"){

                    // Kiểm tra trạng thái thiết bị
                    if (empty($device) || $device['online_status'] === "off") {
                        CLI::write("Device is Offline for Message ID: " . $message['id'] ."--- S/N : ".$device['serial_number'], 'yellow');
                        continue;
                    }
                    // Thực hiện retry
                    $result = ExecuteOutputDevice::pushMessageByDeviceID($message['output_device_id'],$message['transaction_id'], 0);

                    // Cập nhật trạng thái và số lần retry
                    if(!empty($result)){

                        CLI::write("Result retry for Message ID " . $message['id'] . ": " . $result);
        
                        // Cập nhật trạng thái và số lần retry
                        $OutputDeviceReplayMessageQueueModel->set([
                            'status' => "Success",
                            'retries_count' => $message['retries_count'] + 1
                        ])->where(['id' => $message['id']])->update();
            
                        CLI::write('Message ID ' . $message['id'] . ' processed successfully!', 'green');
                        continue;
                    }else{
                        $OutputDeviceReplayMessageQueueModel->set([
                            'status' => "Failed",
                            'retries_count' => $message['retries_count'] + 1
                        ])->where(['id' => $message['id']])->update();

                        CLI::write('❌ Message ID ' . $message['id'] . ' error MQTT!', 'red');
                        log_message("error", "Gửi thông tin thất bại, ID thiết bị: " . json_encode($result));
                        continue;
                    }

                   
                }

                if($device['vendor'] == "Flametechvn"){
                    $FlametechvnClient = new FlametechvnClient;
                    helper('general');
                    $TransactionModel = slavable_model(TransactionsModel::class, 'ExecuteOutputDevice');
                    $data_device = model(OutputDeviceModel::class)->where(['id' => $message['output_device_id']])->get()->getRowArray();
                    $data_transaction = $TransactionModel->where(['id' => $message['transaction_id']])->get()->getRowArray();

                    // log data
                    $log_data = array(
                        'output_device_id' => $message['output_device_id'],
                        'transaction_id' => $data_transaction['id'],
                        'message' => (isset($device['vendor']) && $device['vendor'] == "Aisino") 
                        ? ((isset($data_transaction['amount_in']) ? (int)$data_transaction['amount_in'] : 0) . " - " . ExecuteOutputDevice::amountToAudio((int)($data_transaction['amount_in'] ?? 0))) 
                        : (int)($data_transaction['amount_in'] ?? 0),
                    );
                    // online
                    $result_push = $FlametechvnClient->pushNoti($data_device['external_device_token'], (int)$data_transaction['amount_in'],$data_transaction['id']);
                    if(!empty($result_push['status']) && $result_push['code'] == 200){
                        $queue_message['status'] = "Success";
                        $log_data['status'] = $queue_message['status'];
                        log_message("debug", $queue_message['status'] == "Success" ?
                            "Gửi thông tin tới thiết bị thành công, ID thiết bị " . json_encode($message['output_device_id']) :
                            "Gửi thông tin tới thiết bị thất bại, ID thiết bị " . json_encode($message['output_device_id']));
                        // Cập nhật trạng thái và số lần retry
                        $OutputDeviceReplayMessageQueueModel->set([
                            'status' => "Success",
                            'retries_count' => $message['retries_count'] + 1
                        ])->where(['id' => $message['id']])->update();
                        $log_data['status'] = "Success";
                        CLI::write('Message ID ' . $message['id'] . ' processed successfully!', 'green');
                        $savelog = ExecuteOutputDevice::saveLog($log_data);
                        continue;
                       
                            
                                                  
                    }else if(empty($result['status']) && $result_push['code'] == 400){
                        // offline
                        $queue_message['status'] = "Pending";
                        $log_data['status'] = "Pending";
                        log_message("debug", "Thiết bị đang offline, ID thiết bị " . json_encode($message['output_device_id']));
                        CLI::write("Device is Offline for Message ID: " . $message['id'] ."--- S/N : ".$message['output_device_id'], 'yellow');
                        continue;

                    }else{
                        $OutputDeviceReplayMessageQueueModel->set([
                            'status' => "Failed",
                            'retries_count' => $message['retries_count'] + 1
                        ])->where(['id' => $message['id']])->update();
                        CLI::write('❌ Message ID ' . $message['id'] . ' error API push notify FlametechVN!', 'red');
                        log_message("error", "Gửi thông tin thất bại, ID thiết bị: " . json_encode($result_push));
                        $log_data['status'] = "Failed";
                        $savelog = ExecuteOutputDevice::saveLog($log_data);
                        continue;
                    }


            
                   
                }

                CLI::write("Device Not Find Vendor for Message ID: " . $message['id'] ."--- S/N : ".$device['serial_number'], 'yellow');
                continue;
    
            }
    
            sleep(20);
        }
    }
    
    
}
