<?php

namespace App\Commands;

use CodeIgniter\CLI\CLI;
use App\Models\WebhooksModel;
use CodeIgniter\CLI\BaseCommand;
use App\Models\TransactionsModel;
use App\Models\WebhooksQueueModel;

class RetryWebhookWorker extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'Worker';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'worker:retry-webhook';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = '';

    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'worker:retry-webhook';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = [];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = [];

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        $rabbitmq = new \App\Libraries\RabbitMQClient();
        $connected = $rabbitmq->connect();

        if (!$connected) {
            CLI::error('Cannot connect to RabbitMQ.');
            return;
        }

        $rabbitmq->queueDeclare('retry_webhook');

        CLI::write("[*] Waiting for messages. To exit press CTRL+C\n");

        $callback = function ($msg) {
            $db = db_connect();

            CLI::write("[" . date('Y-m-d H:i:s') . "] Received " . $msg->getBody() . "\n");
            
            $queue = json_decode($msg->getBody());

            $webhooksQueueModel = model(WebhooksQueueModel::class);
            $webhooksModel = model(WebhooksModel::class);
            $transactionsModel = model(TransactionsModel::class);

            $hooks_details = $webhooksModel->where(['id' => $queue->webhook_id])->get()->getRow();
            $transaction_details = $transactionsModel->where(['id' => $queue->transaction_id])->get()->getRow();

            if(is_object($hooks_details) && is_object($transaction_details)) {
                $result = $webhooksModel->doWebhook($hooks_details, $transaction_details, TRUE);

                $queue_status = 'SoftFailed';
                if(isset($result['connect_success']) && $result['connect_success'] == 1 && !is_retry_hook_with_conditions($hooks_details, $result)) {
                    $queue_status = 'Success';
                }
                $retries_count = $queue->retries_count + 1;

            } else {
                $retries_count = $queue->retries_count;
                $queue_status = "Failed";
            }
            
            $webhooksQueueModel->set(['status' => $queue_status,'retries_count' => $retries_count, 'last_retry_time' => date("Y-m-d H:i:s")])->where(['id' => $queue->id])->update();

            CLI::write("[" . date('Y-m-d H:i:s') . "] Done\n");
            log_message('error', '[PID: ' . getmypid() . ' - Retry webhook worker debug]: ' . $queue->transaction_id);
            $msg->ack();

            $cache = service('cache');
            $cache->delete('retry_webhook_queuing_' . $queue->id);

            $db->close();
        };
        
        $rabbitmq->channel->basic_qos(null, 1, false);
        $rabbitmq->channel->basic_consume($rabbitmq->getQueueDeclaration('retry_webhook', 'name'), '', false, false, false, false, $callback);

        try {
            $rabbitmq->channel->consume();
        } catch (\Throwable $e) {
            log_message('error', 'Retry webhook worker: ' . $e->getMessage());
            CLI::error($e->getMessage());
        }

        $rabbitmq->close();
    }
}
