<?php

namespace App\Commands;

use Exception;
use CodeIgniter\CLI\CLI;
use App\Models\CompanyModel;
use App\Models\FcmDeviceTokenModel;
use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\Database\RawSql;
use App\Libraries\RabbitMQClient;
use App\Models\UserPermissionFeatureModel;
use App\Actions\Mobile\PushFcmByTokenQueueAction;

class MobileTransactionNotificationPushingWorker extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'Worker';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'worker:mobile-transaction-notification-pushing';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = '';

    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'command:name [arguments] [options]';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = [];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = [];

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        $rabbitmq = new RabbitMQClient();
        $connected = $rabbitmq->connect();

        if (!$connected) {
            CLI::error('Cannot connect to RabbitMQ.');
            return;
        }

        $rabbitmq->queueDeclare('mobile_transaction_notification_pushing');

        CLI::write("[*] Waiting for messages. To exit press CTRL+C\n");

        $callback = function ($msg) {
            $db = db_connect();

            CLI::write("[" . date('Y-m-d H:i:s') . "] Received " . $msg->getBody() . "\n");
            
            $data = json_decode($msg->getBody());
            $deviceTokenModel = model(FcmDeviceTokenModel::class);

            $bank = model(BankModel::class)->where('brand_name', $data->transaction_details->gateway)->first();

            $adminDeviceTokens = $deviceTokenModel
                ->select([
                    'tb_autopay_fcm_device_token.id',
                    'tb_autopay_fcm_device_token.token',
                    'tb_autopay_fcm_device_token.user_id',
                ])
                ->join('tb_autopay_company_user', 'tb_autopay_company_user.id = tb_autopay_fcm_device_token.user_id')
                ->where('tb_autopay_company_user.company_id', $data->bank_account_details->company_id)
                ->where('tb_autopay_company_user.company_id', $data->bank_account_details->company_id)
                ->where('tb_autopay_fcm_device_token.active', 1)
                ->where('tb_autopay_fcm_device_token.is_browser', 0)
                ->whereIn('tb_autopay_company_user.role', ['Admin', 'SuperAdmin'])
                ->get()->getResult();

            $adminDeviceTokens = array_map(function($deviceToken) {
                $deviceToken->hide_amount_out = 0;
                $deviceToken->hide_accumulated = 0;
                $deviceToken->hide_reference_number = 0;
                $deviceToken->hide_transaction_content = 0;

                return $deviceToken;
            }, $adminDeviceTokens);

            $userDeviceTokenBuilder = $deviceTokenModel
                ->select([
                    'tb_autopay_fcm_device_token.id',
                    'tb_autopay_fcm_device_token.token',
                    'tb_autopay_fcm_device_token.user_id',
                    'tb_autopay_user_permission_bank.hide_amount_out',
                    'tb_autopay_user_permission_bank.hide_accumulated',
                    'tb_autopay_user_permission_bank.hide_reference_number',
                    'tb_autopay_user_permission_bank.hide_transaction_content',
                ])
                ->join('tb_autopay_user', 'tb_autopay_user.id = tb_autopay_fcm_device_token.user_id')
                ->join('tb_autopay_company_user', 'tb_autopay_company_user.id = tb_autopay_fcm_device_token.user_id')
                ->join('tb_autopay_user_permission_bank', 'tb_autopay_user_permission_bank.user_id = tb_autopay_fcm_device_token.user_id')
                ->where('tb_autopay_user_permission_bank.bank_account_id', $data->bank_account_details->id)
                ->where('tb_autopay_company_user.company_id', $data->bank_account_details->company_id)
                ->where('tb_autopay_fcm_device_token.active', 1)
                ->where('tb_autopay_fcm_device_token.is_browser', 0)
                ->where('tb_autopay_user.active', 1)
                ->where(new RawSql("EXISTS(SELECT 1 FROM tb_autopay_user_permission_feature WHERE feature_slug = 'Transactions' AND can_view_all = 1 AND user_id = tb_autopay_fcm_device_token.user_id)"))
                ->where(new RawSql("EXISTS(SELECT 1 FROM tb_autopay_user_permission_feature WHERE feature_slug = 'PushMobileTransactionNotification' AND can_view_all = 1 AND user_id = tb_autopay_fcm_device_token.user_id)"));

            if (is_object($data->bank_sub_account_details)) {
                $userDeviceTokenBuilder->join('tb_autopay_user_permission_bank_sub', 'tb_autopay_user_permission_bank_sub.user_id = tb_autopay_fcm_device_token.user_id')
                    ->where('tb_autopay_user_permission_bank_sub.sub_account_id', $data->bank_sub_account_details->id);
            }
                
            $userDeviceTokens = $userDeviceTokenBuilder->get()->getResult();

            $deviceTokens = array_merge($adminDeviceTokens, $userDeviceTokens);

            log_message('error', '[PID: ' . getmypid() . ' - Mobile transaction notification pushing queue]: ' . $data->transaction_details->id);

            foreach ($deviceTokens as $deviceToken) {
                if ($data->transaction_details->amount_out > 0 && $deviceToken->hide_amount_out) {
                    continue;
                }

                $notification = [];

                $notification['title'] = 'Thông báo biến động số dư';

                $defaultTemplate = 'STK: {accountNumber} ({bankCode})' . chr(10)
                    . 'Số tiền: ({transferType}) {amount}' . chr(10)
                    . 'Số dư: {accumulated}';

                $hasVaTemplate = 'STK: {accountNumber} ({bankCode})' . chr(10)
                    . 'VA: {subAccountNumber}' . chr(10)
                    . 'Số tiền: ({transferType}) {amount}' . chr(10)
                    . 'Số dư: {accumulated}';

                $template = is_object($data->bank_sub_account_details) ? $hasVaTemplate : $defaultTemplate;

                $accountNumber = $data->bank_account_details->account_number;
                $subAccountNumber = is_object($data->bank_sub_account_details) 
                    ? $data->bank_sub_account_details->sub_account 
                    : null;
                $bankCode = $bank->code;
                $transferType = $data->transaction_details->amount_in > 0 ? '+' : '-';
                $currency = $data->transaction_details->currency;
                
                $amount = number_format($data->transaction_details->amount_in > 0 
                    ? $data->transaction_details->amount_in 
                    : $data->transaction_details->amount_out, 0) . ' ' . $currency;

                $accumulated = $data->bank_account_details->bank_sms 
                || ($data->bank_account_details->bank_api && in_array($data->bank_account_details->bank_id, [6]))
                    ? number_format($data->transaction_details->accumulated, 0) . ' ' . $currency
                    : 'Không xác định';
                $accumulated = $deviceToken->hide_accumulated ? '***' : $accumulated;
                
                $body = $template;
                $body = str_replace('{accountNumber}', $accountNumber, $body);
                $body = str_replace('{subAccountNumber}', $subAccountNumber, $body);
                $body = str_replace('{bankCode}', $bankCode, $body);
                $body = str_replace('{transferType}', $transferType, $body);
                $body = str_replace('{amount}', $amount, $body);
                $body = str_replace('{accumulated}', $accumulated, $body);

                $notification['body'] = $body;

                $apnAlert = [];
                $apnAlert['title'] = (is_object($data->bank_sub_account_details) ? $subAccountNumber : $accountNumber) . ' (' . $bankCode . ')';
                $apnAlert['subtitle'] = "Số tiền: ({$transferType}) {$amount}";
                $apnAlert['body'] = 'Nội dung: ' . $data->transaction_details->transaction_content . chr(10) . 'Số dư: ' . $accumulated;

                $fcmData = [
                    'click_action' => 'FLUTTER_CLICK_ACTION',
                    'transaction_id' =>  $data->transaction_details->transaction_id
                ];

                $apns = [
                    'headers' => [
                        'apns-priority' => '10'
                    ],
                    'payload' => [
                        'aps' => [
                            'alert' => $apnAlert,
                            'category' => 'NEW_MESSAGE_CATEGORY',
                            'mutable-content' => 1,
                            'content-available' => 1,
                            'sound' => 'iphone_note_sms.caf'
                        ]
                    ]
                ];

                try {
                    PushFcmByTokenQueueAction::run($deviceToken->token, $notification, $fcmData, $apns, $deviceToken->id, $data->transaction_details->id, $deviceToken->user_id);
                } catch (Exception $e) {
                    log_message('error', 'PushFcmByTokenQueueAction failed: ' . $deviceToken->token . ' - ' . $e->getMessage() . ' - ' . $e->getTraceAsString());
                }
            }

            CLI::write('Push done!');

            $msg->ack();
            $db->close();
        };
        
        $rabbitmq->channel->basic_qos(null, 1, false);
        $rabbitmq->channel->basic_consume($rabbitmq->getQueueDeclaration('mobile_transaction_notification_pushing', 'name'), '', false, false, false, false, $callback);

        try {
            $rabbitmq->channel->consume();
        } catch (\Throwable $e) {
            log_message('error', 'Mobile transaction notification pushing worker faield: ' . $e->getMessage() . ' - ' . $e->getTraceAsString());
            CLI::error($e->getMessage() . ' - ' . $e->getTraceAsString());
        }

        $rabbitmq->close();
    }
}
