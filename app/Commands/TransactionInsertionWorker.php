<?php

namespace App\Commands;

use CodeIgniter\CLI\CLI;
use App\Models\SmsParserModel;
use CodeIgniter\CLI\BaseCommand;
use App\Actions\Merchant\PushMerchantTransactionNotificationAction;

class TransactionInsertionWorker extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'Worker';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'worker:transaction-insertion';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = '';

    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'command:name [arguments] [options]';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = [];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = [];

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        $rabbitmq = new \App\Libraries\RabbitMQClient();
        $connected = $rabbitmq->connect();

        if (!$connected) {
            CLI::error('Cannot connect to RabbitMQ.');
            return;
        }

        $rabbitmq->queueDeclare('transaction_insertion');

        CLI::write("[*] Waiting for messages. To exit press CTRL+C\n");


        $callback = function ($msg) {
            $db = db_connect();

            CLI::write("[" . date('Y-m-d H:i:s') . "] Received " . $msg->getBody() . "\n");
            $smsParserModel = model(SmsParserModel::class);
            $data = json_decode($msg->getBody());
            $inserted = $smsParserModel->insert($data->transaction_details);
            $data->transaction_details->id = $inserted;

            log_message('error', '[PID: ' . getmypid() . ' - Transaction insertion queue]: ' . $data->transaction_details->transaction_id);

            if ($inserted) {
                log_message('error', 'Transaction insertion: ' . json_encode($data));

                CLI::write("[" . date('Y-m-d H:i:s') . "] Done\n");

                if ($data->transaction_details->merchant_id) {
                    
                    try {
                        PushMerchantTransactionNotificationAction::run(
                            $data->bank_account_details, 
                            $data->transaction_details,
                            false,
                        );
                    } catch (Exception $e) {
                        log_message('error', '[PushableMerchantTransactionNotificationAction] ' . $e->getMessage() . ' - ' . $e->getTraceAsString());
                    }
                }
            } else {
                CLI::error("[" . date('Y-m-d H:i:s') . "] Transaction insertion failed\n");
                log_message('error', 'Transaction insertion failed: ' . json_encode($data));
            }
            
            $msg->ack();

            $db->close();
        };
        
        $rabbitmq->channel->basic_qos(null, 1, false);
        $rabbitmq->channel->basic_consume($rabbitmq->getQueueDeclaration('transaction_insertion', 'name'), '', false, false, false, false, $callback);

        try {
            $rabbitmq->channel->consume();
        } catch (\Throwable $e) {
            log_message('error', 'Transaction insertion worker: ' . $e->getMessage() . ' - ' . $e->getTraceAsString());
            CLI::error($e->getMessage() . ' - ' . $e->getTraceAsString());
        }

        $rabbitmq->close();
    }
}
