<?php

namespace App\Commands;

use App\Models\TransactionsModel;
use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use DateTime;

class IpnDowntimeMonitorCommand extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'Monitor';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'monitor:ipn-downtime';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = '';

    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'monitor:ipn-donwtime';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = [];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = [];
    
    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        helper(['general']);
        
        $now = new DateTime();
        $config = config(\Config\IpnDowntimeMonitorConfig::class);
    
        $this->validateConfig($config);
        
        $downtimeIpns = [];
        
        foreach ($config->monitors as $monitor) {
            foreach ($monitor['ranges'] as $range) {
                $startTime = DateTime::createFromFormat('H:i', $range['start']);
                $endTime = DateTime::createFromFormat('H:i', $range['end']);
                
                if ($endTime < $startTime) {
                    $endTime->modify('+1 day');
                }
                
                $frequency = $range['frequency'];
                $minute = (int) $now->format('i');
                
                if ($now < $startTime) continue;
                
                if ($now > $endTime) continue;
                
                if ($minute % $frequency !== 0) continue;
                
                $lastMonitorStartTime = $now->modify(sprintf('-%s minute', $frequency));
                
                $isDowntime = $this->determineIfIpnIsDowntime($monitor['gateway'], $lastMonitorStartTime->format('Y-m-d H:i:00'), $endTime->format('Y-m-d H:i:00'));
                
                if (!$isDowntime) continue;
                
                $downtimeIpns[] = [
                    'gateway' => $monitor['gateway'],
                    'range' => $range,
                    'last_monitor_start_time' => $lastMonitorStartTime->format('H:i')
                ];
            }
        }
        
        if (count($downtimeIpns)) {
            $this->sendNotification($config->telegramChatId, $downtimeIpns);
            
            CLI::write('Downtime IPNs: ' . json_encode($downtimeIpns));
        } else {
            CLI::write('No any IPNs downtime');
        }
    }
    
    protected function validateConfig($config): void
    {
        assert(property_exists($config, 'monitors'), '`monitors` property must be defined');
        
        assert($config->monitors, '`monitors` property must be array');
        
        foreach ($config->monitors as $monitorIndex => $monitor) {
            assert(isset($monitor['gateway']), sprintf('[Monitor %s] `gateway` index must be defined', $monitorIndex));
            assert(isset($monitor['ranges']), sprintf('[Monitor %s] `ranges` index must be defined', $monitorIndex));
            
            foreach ($monitor['ranges'] as $rangeIndex => $range) {
                assert(isset($range['start']), sprintf('[Monitor %s][Range %s] `start` index must be defined', $monitorIndex, $rangeIndex));
                assert(isset($range['end']), sprintf('[Monitor %s][Range %s] `end` index must be defined', $monitorIndex, $rangeIndex));
                assert(isset($range['frequency']), sprintf('[Monitor %s][Range %s] `frequency` index must be defined', $monitorIndex, $rangeIndex));
                
                assert(preg_match('/\d{2}\:\d{2}/', $range['start']), sprintf('[Monitor %s][Range %s] `start` index must be H:i format', $monitorIndex, $rangeIndex));
                assert(preg_match('/\d{2}\:\d{2}/', $range['end']), sprintf('[Monitor %s][Range %s] `end` index must be H:i format', $monitorIndex, $rangeIndex));
                assert(is_integer($range['frequency']) && $range['frequency'] > 0, sprintf('[Monitor %s][Range %s] `frequency` index must be positive integer', $monitorIndex, $rangeIndex));
            }
        }
    }
    
    protected function determineIfIpnIsDowntime($gateway, $startTime, $endTime)
    {
        $transactionModel = slavable_model(TransactionsModel::class, 'Transactions');
        
        $latestApiTransaction= $transactionModel
            ->select(['tb_autopay_sms_parsed.id', 'tb_autopay_sms_parsed.transaction_date', 'tb_autopay_sms_parsed.datecreated', 'tb_autopay_bank_account.company_id', 'tb_autopay_sms_parsed.bank_account_id'])
            ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id = tb_autopay_sms_parsed.bank_account_id')
            ->where('gateway', $gateway)
            ->where('source', 'BankAPINotify')
            ->where('parser_status', 'Success')
            ->orderBy('transaction_date', 'desc')
            ->where('transaction_date >=', $startTime)
            ->where('transaction_date <=', $endTime)
            ->first();
            
        return !$latestApiTransaction;
    }
    
    protected function sendNotification($chatId, $downtimeIpns)
    {
        $monitorBotConfig = config(\Config\MonitorBotConfig::class);
        $botToken = $monitorBotConfig->botToken;
        
        $messageLines = [];
        $messageLines[] = '⁉️⁉️⁉️';
        $messageLines[] = sprintf('[%s] Phát hiện *KHÔNG* ghi nhận giao dịch nào ở các ngân hàng:', (new DateTime())->format('Y-m-d H:i:s'));
        $messageLines[] = '';
        
        foreach ($downtimeIpns as $ipn) {
            $messageLines[] = sprintf(
                '*%s*: từ lúc *%s* trong khoảng theo dõi %s-%s/%s phút', 
                $ipn['gateway'],
                $ipn['last_monitor_start_time'],
                $ipn['range']['start'], 
                $ipn['range']['end'], 
                $ipn['range']['frequency']
            );
        }
        
        $message = implode(PHP_EOL, $messageLines);
        
        $specialChars = ['_', '[', ']', '(', ')', '~', '>', '#', '+', '-', '=', '|', '{', '}', '.', '!', '/'];
        
        foreach ($specialChars as $char) {
            $message = str_replace($char, "\\" . $char, $message);
        }
        
        $keyboard = [
            'inline_keyboard' => [
                [
                    ['text' => 'Thử giao dịch ngay', 'url' => 'https://my.sepay.vn/createqr']
                ],
            ]
        ];
        
        $data = [
            'chat_id' => $chatId,
            'text' => $message,
            'reply_markup' => json_encode($keyboard),
            'parse_mode' => 'MarkdownV2'
        ];
        
        $url = "https://api.telegram.org/bot$botToken/sendMessage";
        
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        $response = curl_exec($ch);
        curl_close($ch);
        
        echo $response;
    }
}
