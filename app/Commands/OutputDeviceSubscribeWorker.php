<?php

namespace App\Commands;

use App\Models\OutputDeviceModel;
use App\Models\BankIntegrationOutputDeviceModel;
use App\Models\MQTTServerModel;
use App\Models\OutputDeviceReplayMessageQueueModel;
use CodeIgniter\CLI\CLI;
use CodeIgniter\CLI\BaseCommand;
use Config\MQTT;
use PhpMqtt\Client\ConnectionSettings;
use PhpMqtt\Client\MqttClient;
use PhpMqtt\Client\Exceptions\MqttClientException;
use App\Actions\OutputDevice\ExecuteOutputDevice;
class OutputDeviceSubscribeWorker extends BaseCommand
{
    protected $group = 'Worker';
    protected $name = 'worker:output_device_subscribe';
    protected $description = '';
    protected $usage = 'command:name [arguments] [options]';
    protected $arguments = [];
    protected $options = [];

    public function run(array $params)
    {
        $hostname_server = $params[0] ?? null; // First parameter
    
        if (empty($hostname_server)) {
            $MQTTServerModel = model(MQTTServerModel::class);
        
            $ServerData = $MQTTServerModel
                ->orderBy("id", "asc")
                ->get()
                ->getRowArray();
        
            if (empty($ServerData)) {
                CLI::write('No records found in the database.', 'yellow');
                return;
            }
        
            $hostname_server = $ServerData['hostname'];
            CLI::write('Hostname not provided. Using hostname from DB: ' . $hostname_server, 'green');
        }
    
        while (true) {
            $current_time = time();
            list($year, $month, $day, $hour, $minute, $second) = explode('-', date('Y-m-d-H-i-s', $current_time));

            CLI::write("[$year-$month-$day $hour:$minute:$second] Hostname start at.", 'yellow');

            if ($hour == 2 && $minute == 59) {
                CLI::write("[$year-$month-$day $hour:$minute:$second] Killing process...", 'red');
                exit(0);
            }
        


            if (empty($hostname_server)) {
                CLI::write('Hostname parameter is missing.', 'yellow');
                sleep(3);
                return;
            }
            $MQTTServerModel = model(MQTTServerModel::class);
            $query = $MQTTServerModel->where(['hostname' => $hostname_server])->get();

            if ($query && $query->getNumRows() > 0) {
                $ServerData = $query->getResultArray();
            } else {
                // Không có kết quả hoặc truy vấn bị lỗi
                CLI::write('No server data found in DB.', 'yellow');
                sleep(3);
                return;
            }

        
            if (empty($ServerData)) {
                CLI::write('No server data found.', 'yellow');
                sleep(3);
                continue;
            }
        
            try {
                foreach ($ServerData as $server) {
                    $clientId = "clientId" . $server['username'];
                    $client = new MqttClient(
                        $server['hostname'],
                        $server['port'],
                        $clientId,
                        MqttClient::MQTT_3_1_1
                    );
                
                    try {
                        $connectSettings = (new \PhpMqtt\Client\ConnectionSettings())
                            ->setUseTls($server['ssl_enabled'])
                            ->setUsername($server['username'])
                            ->setPassword($server['password']);
                        $client->connect($connectSettings, false);
                        log_message("debug", "Successfully connected to MQTT broker at " . $server['hostname'] . ":" . $server['port']);
                    } catch (MqttClientException $e) {
                        log_message("error", "Failed to connect to MQTT broker: " . $e->getMessage() . " PID ID: " .getmypid());
                    }
                
                    // Subscribe to topic
                    $topic = "#"; // Subscribe to all topics
                    if ($client->isConnected()) {
                        log_message("debug", "Client connected successfully. Subscribing to topic: " . $topic);
                        $client->subscribe(
                            $topic,
                            function (string $topic, string $message, bool $retained) use ($client) {
                                log_message("debug", "Message received on topic: " . $topic);
                                log_message("debug", "Received data: " . $message);
                                $array_status = [1 => "on", 0 => "off"];
                                $data = json_decode($message, true);
                                if (json_last_error() !== JSON_ERROR_NONE) {
                                    log_message("error", "JSON decode error: " . json_last_error_msg());
                                    CLI::write('error decode data MQTT.', 'yellow');
                                    return;
                                }

                                
                                $serial_explode = explode("will_topic_", $topic)[1] ?? "";
                                if (!empty($serial_explode)) {
                                    $updateStatusDevice = [
                                        "serial_number" => $serial_explode,
                                        "online_status" => $array_status[$data["online"]] ?? "unknown",
                                    ];
                                    $OutputDeviceModel = model(OutputDeviceModel::class);
                                    $updated = $OutputDeviceModel->set($updateStatusDevice)
                                        ->where(['serial_number' => $serial_explode])
                                        ->update();
                                    if ($updated) {

                                        $device_data = $OutputDeviceModel->where(['serial_number' => $serial_explode])->get()->getRowArray();
                                        $device_id = $device_data['id']??"";
                                        
                                        if($device_data['model'] == "Q190"){
                                            $check_push = ExecuteOutputDevice::pushMessageQRMqtt($device_id);
                                            if(empty($check_push)){
                                                log_message("debug", "Error Push QR - Device ID : " . $device_id);
                                            }else{
                                                log_message("debug", "Send QR Success - Device ID : " . $device_id);
                                            }
                                        }
                                       

                                        log_message("debug", "Successfully updated status - Serial: " . $serial_explode . ", Status: " . $array_status[$data["online"]]);
                                    } else {
                                        log_message("error", "Failed to update status - Serial: " . $serial_explode . ", Status: " . $array_status[$data["online"]]);
                                    }
                                } else {
                                    log_message("error", "Unable to determine serial from data: " . $message);
                                }
                            },
                            MqttClient::QOS_AT_MOST_ONCE
                        );
                        CLI::write("Start subscribing to server {$server['hostname']} with user {$server['username']}", 'yellow');
                        $client->loop(true); // Process once
                          
                    } else {
                        log_message("error", "Client is not connected. Cannot subscribe to topic." . " PID ID: " .getmypid());
                    }
                }
            } catch (\Exception $e) {
                log_message("error", "Error while subscribing to topic. Exception: " . $e->getMessage() ." PID ID: " .getmypid());
            }
            CLI::write('Unable to connect to the server.', 'yellow');
            sleep(3);
        }
    }

    
    
    
}
