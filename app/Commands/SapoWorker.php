<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use App\Models\SapoModel;

class SapoWorker extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'Worker';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'worker:sapo';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = 'The worker that handles to Sapo queues.';

    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'worker:sapo';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = [];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = [];

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        $rabbitmq = new \App\Libraries\RabbitMQClient();
        $connected = $rabbitmq->connect();

        if (!$connected) {
            CLI::error('Cannot connect to RabbitMQ.');
            return;
        }

        $rabbitmq->queueDeclare('sapo');

        CLI::write("[*] Waiting for messages. To exit press CTRL+C\n");


        $callback = function ($msg) {
            $db = db_connect();

            CLI::write("[" . date('Y-m-d H:i:s') . "] Received " . $msg->getBody() . "\n");
            $sapoModel = model(SapoModel::class);
            $data = json_decode($msg->getBody(), true);
            $sapoModel->doWebhooks($data['account_number'], $data['parser_id']);
            CLI::write("[" . date('Y-m-d H:i:s') . "] Done\n");
            log_message('error', '[PID: ' . getmypid() . ' - Sapo worker debug]: ' . $data['parser_id']);
            $msg->ack();

            $db->close();
        };
        
        $rabbitmq->channel->basic_qos(null, 1, false);
        $rabbitmq->channel->basic_consume($rabbitmq->getQueueDeclaration('sapo', 'name'), '', false, false, false, false, $callback);

        try {
            $rabbitmq->channel->consume();
        } catch (\Throwable $e) {
            log_message('error', 'Sapo worker: ' . $e->getMessage());
            CLI::error($e->getMessage());
        }

        $rabbitmq->close();
    }
}
