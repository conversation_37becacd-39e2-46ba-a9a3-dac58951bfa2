<?php

namespace App\Commands;

use CodeIgniter\CLI\CLI;
use App\Models\BankModel;
use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\Database\RawSql;
use App\Models\TransactionsModel;
use Config\DelayApiTransactionTracking;
use DateTime;

class TrackDelayApiTransactionCommand extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'Analysis';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'analysis:track-delay-api-transaction';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = '';

    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'analysis:track-delay-transaction';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = [];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = [];

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        helper(['general']);

        $transactionModel = slavable_model(TransactionsModel::class, 'TrackDelayApiTransactionCommand');
        $config = config(DelayApiTransactionTracking::class);
        $keywordContentExcluding = $config->keywordContentExcluding ?? [];

        foreach ($config->gateways ?? [] as $gateway) {
            $additionFilterCallback = null;
            $defaultGateway = $gateway;

            if ($defaultGateway === 'MBB_MMS') {
                $gateway = 'MBBank';

                $additionFilterCallback = function($builder) {
                    $builder->where('sub_account !=', null)
                        ->where(new RawSql('EXISTS(SELECT id FROM tb_autopay_mbb_mms_notification WHERE id = tb_autopay_sms_parsed.sms_id)'));
                };
            } else if ($defaultGateway === 'MBBank') {
                $additionFilterCallback = function($builder) {
                    $builder->where(new RawSql('NOT EXISTS(SELECT id FROM tb_autopay_mbb_mms_notification WHERE id = tb_autopay_sms_parsed.sms_id)'));
                };
            }

            $trackingSeconds = $config->trackingSeconds[$gateway] ?? $config->trackingSeconds['default'];
            $allowDelayApiTransactionSeconds = $config->allowDelayApiTransactionSeconds[$gateway] ?? $config->allowDelayApiTransactionSeconds['default'];
            $skipDebitTransaction = $config->skipDebitTransaction[$gateway] ?? $config->skipDebitTransaction['default'] ?? false;

            $lastApiTransactionBuilder = $transactionModel
                ->select(['tb_autopay_sms_parsed.id', 'tb_autopay_sms_parsed.transaction_date', 'tb_autopay_sms_parsed.datecreated', 'tb_autopay_bank_account.company_id', 'tb_autopay_sms_parsed.bank_account_id', 'tb_autopay_sms_parsed.reference_number'])
                ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id = tb_autopay_sms_parsed.bank_account_id')
                ->where('gateway', $gateway)
                ->where('source', 'BankAPINotify')
                ->where('parser_status', 'Success')
                ->orderBy('transaction_date', 'desc')
                ->where('TIMESTAMPDIFF(SECOND, datecreated, NOW()) <=', $trackingSeconds)
                ->where('TIMESTAMPDIFF(SECOND, transaction_date, datecreated) >', $allowDelayApiTransactionSeconds);

            if (in_array($gateway, ['MBBank'])) {
                $lastApiTransactionBuilder->like('reference_number', 'FT', 'after');
            }

            if (array_key_exists('*', $keywordContentExcluding)) {
                foreach ($keywordContentExcluding['*'] ?? [] as $keyword) {
                    $lastApiTransactionBuilder->notLike('body', $keyword);
                }
            }

            if (array_key_exists($gateway, $keywordContentExcluding)) {
                foreach ($keywordContentExcluding[$gateway] ?? [] as $keyword) {
                    $lastApiTransactionBuilder->notLike('body', $keyword);
                }
            }

            if ($skipDebitTransaction) {
                $lastApiTransactionBuilder->where('amount_in >', 0);
            }

            if (is_callable($additionFilterCallback)) {
                $additionFilterCallback($lastApiTransactionBuilder);
            }
                
            $lastApiTransaction = $lastApiTransactionBuilder->first();

            if (!$lastApiTransaction) {
                continue;
            }
            
            $this->sendNotification($config->telegramChatId, $defaultGateway, $lastApiTransaction);

            CLI::write($gateway . ': ' . json_encode($lastApiTransaction));
        }
    }
    
    protected function sendNotification($chatId, $gateway, $lastApiTransaction)
    {
        $monitorBotConfig = config(\Config\MonitorBotConfig::class);
        $botToken = $monitorBotConfig->botToken;
        
        $messageLines = [];
        $messageLines[] = '🐌⇢🏁';
        $messageLines[] = sprintf('[%s] Phát hiện *DELAY* giao dịch ngân hàng *%s*', (new DateTime())->format('Y-m-d H:i:s'), $gateway);
        $messageLines[] = '';
        $messageLines[] = sprintf('Mã giao dịch: %s', $lastApiTransaction->id);
        $messageLines[] = '';
        $messageLines[] = sprintf('ID công ty: %s', $lastApiTransaction->company_id);
        $messageLines[] = '';
        $messageLines[] = sprintf('Mã truy vấn ngân hàng: %s', $lastApiTransaction->reference_number);
        $messageLines[] = '';
        $messageLines[] = sprintf('Thời gian ghi nhận giao dịch từ ngân hàng: %s', $lastApiTransaction->transaction_date);
        $messageLines[] = '';
        $messageLines[] = sprintf('Thời gian thực nhận: %s', $lastApiTransaction->datecreated);
        
        $message = implode(PHP_EOL, $messageLines);
        
        $specialChars = ['_', '[', ']', '(', ')', '~', '>', '#', '+', '-', '=', '|', '{', '}', '.', '!', '/'];
        
        foreach ($specialChars as $char) {
            $message = str_replace($char, "\\" . $char, $message);
        }
        
        $keyboard = [
            'inline_keyboard' => [
                [
                    ['text' => 'Đi đến kiểm tra công ty', 'url' => 'https://ad.sepay.vn/company/details/' . $lastApiTransaction->company_id]
                ],
            ]
        ];
        
        $data = [
            'chat_id' => $chatId,
            'text' => $message,
            'reply_markup' => json_encode($keyboard),
            'parse_mode' => 'MarkdownV2'
        ];
        
        $url = "https://api.telegram.org/bot$botToken/sendMessage";
        
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        $response = curl_exec($ch);
        curl_close($ch);
        
        echo $response;
    }
}
