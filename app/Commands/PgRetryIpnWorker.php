<?php

namespace App\Commands;

use CodeIgniter\CLI\CLI;
use App\Models\PgIpnModel;
use App\Models\PgOrderModel;
use App\Models\PgIpnQueueModel;
use CodeIgniter\CLI\BaseCommand;
use App\Libraries\RabbitMQClient;
use App\Models\PgTransactionModel;

class PgRetryIpnWorker extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'Worker';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'worker:pg:retry-ipn';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = '';

    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'worker:pg:retry-ipn';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = [];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = [];

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        helper(['general']);

        $rabbitmq = new RabbitMQClient();
        $connected = $rabbitmq->connect();

        if (!$connected) {
            CLI::error('Cannot connect to RabbitMQ.');
            return;
        }

        $queueName = 'pg_retry_ipn';
        $rabbitmq->queueDeclare($queueName);

        CLI::write("[*] Waiting for messages. To exit press CTRL+C\n");

        $callback = function ($msg) {
            $db = db_connect();

            CLI::write(sprintf("[%s] Received %s\n", date('Y-m-d H:i:s'), $msg->getBody()));
            
            /**
             * @var array{queue_id:int} $data
             */
            $data = json_decode($msg->getBody(), true);

            /** @var PgIpnQueueModel $slavableModel */
            $slavableModel = slavable_model(PgIpnQueueModel::class, 'PaymentGateway');
            $pgIpnQueueModel = model(PgIpnQueueModel::class);
            $pgIpnQueue = $slavableModel->where('id', $data['queue_id'])->first();

            if (!$pgIpnQueue) {
                log_message('error', sprintf('[PgRetryIpnWorker] PG IPN not found for queue ID: %d', $data['queue_id']));
                CLI::error(sprintf('PG IPN not found for queue ID: %d', $data['queue_id']));
                return;
            }

            /** @var PgIpnModel $pgIpnQueueModel */
            $pgIpnModel = slavable_model(PgIpnModel::class, 'PaymentGateway');
            $pgIpn = $pgIpnModel->where('pg_merchant_id', $pgIpnQueue->pg_merchant_id)->where('active', 1)->first();

            $pgOrderModel = slavable_model(PgOrderModel::class, 'PaymentGateway');
            $pgTransactionModel = slavable_model(PgTransactionModel::class, 'PaymentGateway');

            $pgOrder = $pgOrderModel->find($pgIpnQueue->pg_order_id);

            if (!$pgOrder) {
                log_message('error', sprintf('[PgRetryIpnWorker] PG Order not found for ID: %d | Merchant ID: %s', $pgIpnQueue->pg_order_id, $pgIpnQueue->pg_merchant_id));
                CLI::error(sprintf('PG Order not found for ID: %d | Merchant ID: %s', $pgIpnQueue->pg_order_id, $pgIpnQueue->pg_merchant_id));
                $msg->ack();
                return;
            }

            $pgTransaction = $pgTransactionModel->where('pg_order_id', $pgIpnQueue->pg_order_id)
                ->where('pg_merchant_id', $pgIpnQueue->pg_merchant_id)
                ->where('id', $pgIpnQueue->pg_transaction_id)
                ->first();

            if (!$pgTransaction) {
                log_message('error', sprintf('[PgRetryIpnWorker] PG Transaction not found for ID: %d | PG Merchant ID: %d | PG Order ID: %d', $pgIpnQueue->pg_transaction_id, $pgIpnQueue->pg_merchant_id, $pgIpnQueue->pg_order_id));
                CLI::error(sprintf('PG Transaction not found for ID: %d | PG Merchant ID: %d | PG Order ID: %d', $pgIpnQueue->pg_transaction_id, $pgIpnQueue->pg_merchant_id, $pgIpnQueue->pg_order_id));
                $msg->ack();
                return;
            }

            $success = $pgIpnModel->send($pgIpnQueue->notification_type, $pgIpn, $pgOrder, $pgTransaction, 'Retry');

            $pgIpnQueueData = [
                'last_retry_time' => date('Y-m-d H:i:s'),
                'retries_count' => $pgIpnQueue->retries_count + 1,
            ];
            
            if ($success) {
                $pgIpnQueueData = [
                    'status' => 'Success',
                ];
            }

            $pgIpnQueueModel->where('id', $pgIpnQueue->id)->set($pgIpnQueueData)->update();

            CLI::write(sprintf("[%s] Done\n", date('Y-m-d H:i:s')));

            log_message('error', sprintf('[PgRetryIpnWorker] Acked message: %s | PID: %s', $msg->getBody(), getmypid()));
            $msg->ack();

            $db->close();
        };
        
        $rabbitmq->channel->basic_qos(null, 1, false);
        $rabbitmq->channel->basic_consume($rabbitmq->getQueueDeclaration($queueName, 'name'), '', false, false, false, false, $callback);

        try {
            $rabbitmq->channel->consume();
        } catch (\Throwable $e) {
            log_message('error', sprintf('[PgRetryIpnWorker] Failed to consume message: %s | Trace: %s', $e->getMessage(), $e->getTraceAsString()));
            CLI::error($e->getMessage());
        }

        $rabbitmq->close();
    }
}
