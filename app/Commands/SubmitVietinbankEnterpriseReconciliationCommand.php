<?php

namespace App\Commands;

use Exception;
use CodeIgniter\CLI\CLI;
use CodeIgniter\I18n\Time;
use App\Libraries\SFTPClient;
use CodeIgniter\CLI\BaseCommand;
use App\Models\TransactionsModel;

class SubmitVietinbankEnterpriseReconciliationCommand extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'VietinBank';
    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'vietinbank:submit-enterprise-reconciliation';
    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = '';
    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'vietinbank:submit-enterprise-reconciliation';
    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = [];
    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = [];
    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        helper('general');

        try {
            $date = count($params) > 0 ? trim($params[0]) : null;

            if (!$date) {
                $date = date_create(date('Y-m-d'));
                date_sub($date, date_interval_create_from_date_string("1 days"));
                $date = date_format($date, 'Y-m-d');
            }

            if (!preg_match('/\d{4}\-\d{2}\-\d{2}/', $date)) {
                throw new Exception;
            }

            $time = Time::parse($date);
        } catch (Exception $e) {
            return CLI::error("Date input must be with Y-m-d format");
        }

        $config = config(\Config\Vietinbank::class);
        $client = new SFTPClient($config->sftpHost, $config->sftpPort);
        $client->loginWithPassword($config->sftpUsername, $config->sftpPassword);

        try {
            $inputFileName = 'VIETINBANK_IN/' . $time->toLocalizedString('YMd') . '_' . $config->enterpriseReconciliationProviderId . '_THUCHIHO_IN.txt';
            $inputContents = $client->getFileContents($inputFileName);
            $inputRows = explode(PHP_EOL, $inputContents);
            $inputHeaderRow = array_shift($inputRows);
            $inputFooterRow = array_pop($inputRows);

            if (!$inputHeaderRow || !$inputFooterRow) {
                throw new Exception("SubmitVietinbankEnterpriseReconciliationCommand: The $inputFileName file invalid format.");
            }

            $inputHeaderColumns = explode(',', $inputHeaderRow);
            $inputRecords = [];

            foreach ($inputRows as $key => $row) {
                $line = $key + 1;
                $fields = explode('|', $row);

                if (count($fields) != count($inputHeaderColumns)) {
                    throw new Exception("SubmitVietinbankEnterpriseReconciliationCommand: The $inputFileName file invalid format at line " . $line);
                }

                $record = array_combine($inputHeaderColumns, $fields);
                $inputRecords[] = $record;
            }

            $transactionsModel = slavable_model(TransactionsModel::class, 'SubmitVietinbankEnterpriseReconciliationCommand');

            $bothTransactionsBuilder = $transactionsModel
                ->select([
                    'tb_autopay_vietinbank_enterprise_notification.transId', 
                    'tb_autopay_vietinbank_enterprise_notification.providerId', 
                    'tb_autopay_vietinbank_enterprise_notification.sendBankId', 
                    'tb_autopay_vietinbank_enterprise_notification.sendBranchId', 
                    'tb_autopay_vietinbank_enterprise_notification.recvAcctId', 
                    'tb_autopay_vietinbank_enterprise_notification.recvVirtualAcctId', 
                    'tb_autopay_vietinbank_enterprise_notification.recvVirtualAcctName',
                    'tb_autopay_vietinbank_enterprise_notification.custCode',
                    'tb_autopay_vietinbank_enterprise_notification.recvAcctName',
                    'tb_autopay_vietinbank_enterprise_notification.amount',
                    'tb_autopay_vietinbank_enterprise_notification.currencyCode',
                    'tb_autopay_vietinbank_enterprise_notification.transTime',
                    'tb_autopay_vietinbank_enterprise_notification.bankTransId',
                ])
                ->join('tb_autopay_vietinbank_enterprise_notification', 'tb_autopay_vietinbank_enterprise_notification.id = tb_autopay_sms_parsed.sms_id')
                ->where('DATE(transaction_date)', $time->toLocalizedString('YMd'))
                ->where('gateway', 'Vietinbank')
                ->where('source', 'BankAPINotify')
                ->orderBy('transaction_date', 'ASC');

            if (count($inputRecords)) {
                $bothTransactionsList = $bothTransactionsBuilder->whereIn('tb_autopay_sms_parsed.reference_number', array_column($inputRecords, 'BANKTRANSID'))->get()->getResult();
            } else {
                $bothTransactionsList = [];
            }

            $missingTransactionsAtVietinBankBuilder = $transactionsModel
                ->select([
                    'tb_autopay_vietinbank_enterprise_notification.transId', 
                    'tb_autopay_vietinbank_enterprise_notification.providerId', 
                    'tb_autopay_vietinbank_enterprise_notification.sendBankId', 
                    'tb_autopay_vietinbank_enterprise_notification.sendBranchId', 
                    'tb_autopay_vietinbank_enterprise_notification.recvAcctId', 
                    'tb_autopay_vietinbank_enterprise_notification.recvVirtualAcctId', 
                    'tb_autopay_vietinbank_enterprise_notification.recvVirtualAcctName',
                    'tb_autopay_vietinbank_enterprise_notification.custCode',
                    'tb_autopay_vietinbank_enterprise_notification.recvAcctName',
                    'tb_autopay_vietinbank_enterprise_notification.amount',
                    'tb_autopay_vietinbank_enterprise_notification.currencyCode',
                    'tb_autopay_vietinbank_enterprise_notification.transTime',
                    'tb_autopay_vietinbank_enterprise_notification.bankTransId',
                ])
                ->join('tb_autopay_vietinbank_enterprise_notification', 'tb_autopay_vietinbank_enterprise_notification.id = tb_autopay_sms_parsed.sms_id')
                ->where('DATE(transaction_date)', $time->toLocalizedString('YMd'))
                ->where('gateway', 'Vietinbank')
                ->where('source', 'BankAPINotify')
                ->orderBy('transaction_date', 'ASC');

            if (count($inputRecords)) {
                $missingTransactionsAtVietinBankBuilder->whereNotIn('tb_autopay_sms_parsed.reference_number', array_column($inputRecords, 'BANKTRANSID'));
            }

            $missingTransactionsAtVietinBankList = $missingTransactionsAtVietinBankBuilder->get()->getResult();

            $outputRecords = [];
            $outputHeaderColumns = ['RECORDTYPE','TRANSID','PROVIDERID','MERCHANTID','BANKCODE','BRANCHCODE','CUSTACCTNO','RECVVIRTUALACCTID','RECVVIRTUALACCTNAME','CUSTCODE','CUSTNAME','AMOUNT','CURRENCYCODE','PAYREFNO','MERCHANTACCTNO','BILLCYCLE','BILLID','TRANSTIME','STATUS','BANKTRANSID','PAYMENTCHANNEL','REVERSE1','REVERSE2','REVERSE3','REVERSE4','REVERSE5','reconcileStatus','recordChecksum'];

            foreach ($inputRecords as $key => $inputRecord) {
                $bothTransaction = array_values(array_filter($bothTransactionsList, function($transaction) use ($inputRecord) {
                    return $inputRecord['BANKTRANSID'] == $transaction->bankTransId;
                }))[0] ?? null;

                $outputRecord = $inputRecord;
                $checksumValue = array_pop($outputRecord);

                if (!$bothTransaction) {
                    $outputRecord['reconcileStatus'] = '01';
                } else if ($bothTransaction && ($bothTransaction->amount != $inputRecord['AMOUNT'] || $bothTransaction->transTime != $inputRecord['TRANSTIME'])) {
                    $outputRecord['AMOUNT'] = $bothTransaction->amount;
                    $outputRecord['TRANSTIME'] = $bothTransaction->transTime;
                    $outputRecord['reconcileStatus'] = '03';
                } else {
                    $outputRecord['reconcileStatus'] = '00';
                }

                $outputRecord['recordChecksum'] = md5(implode('', array_values($outputRecord)) . $config->enterpriseReconciliationPrivateKey);
                $outputRecords[] = $outputRecord;
            }

            foreach ($missingTransactionsAtVietinBankList as $transaction) {
                $outputRecord = array_combine($outputHeaderColumns, [
                    '002',
                    $transaction->transId,
                    $transaction->providerId,
                    $transaction->providerId,
                    $transaction->sendBankId,
                    $transaction->sendBranchId,
                    $transaction->recvAcctId,
                    $transaction->recvVirtualAcctId ? $transaction->recvVirtualAcctId : $transaction->custCode,
                    $transaction->recvVirtualAcctName,
                    $transaction->custCode,
                    $transaction->recvAcctName,
                    $transaction->amount,
                    $transaction->currencyCode,
                    $transaction->custCode,
                    '',
                    '',
                    '',
                    $transaction->transTime,
                    'S',
                    $transaction->bankTransId,
                    '',
                    '',
                    '',
                    '',
                    '',
                    '',
                    '',
                    ''
                ]);
            
                $outputRecord['reconcileStatus'] = '02';
                $outputRecord['recordChecksum'] = md5(implode('', array_values($outputRecord)) . $config->enterpriseReconciliationPrivateKey);
                $outputRecords[] = $outputRecord;
            }

            $outputContents = implode(',', $outputHeaderColumns) . PHP_EOL;

            foreach ($outputRecords as $outputRecord) {
                $outputContents .= implode('|', array_values($outputRecord)) . PHP_EOL;
            }

            $outputFooterRow = [
                'recordType' => '009',
                'providerId' => $config->enterpriseReconciliationProviderId,
                'userID' => '',
                'recordNo' => count($outputRecords),
                'transTime' => date('YmdHis')
            ];

            $outputFooterRow['fileChecksum'] = md5(implode('', array_values($outputFooterRow)) . $config->enterpriseReconciliationPrivateKey);

            $outputContents .= implode('|', array_values($outputFooterRow));

            $outputFileName = 'VIETINBANK_OUT/' . $time->toLocalizedString('YMd') . '_' . $config->enterpriseReconciliationProviderId . '_THUHOTKAO_OUT.txt';
            $client->uploadFile($outputFileName, $outputContents);

            CLI::write($inputContents);
            CLI::write($outputFileName);
            CLI::write($outputContents);
        } catch (Exception $e) {
            log_message('error', $e->getMessage());
            CLI::error($e->getMessage() . '-' . $e->getTraceAsString());
        }
    }
}
