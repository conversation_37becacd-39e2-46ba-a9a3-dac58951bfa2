<?php

namespace App\Commands;

use CodeIgniter\CLI\CLI;
use App\Models\BankAccountModel;
use CodeIgniter\CLI\BaseCommand;
use App\Models\BidvEnterpriseAccountModel;

class SetupBidvEnterpriseAccountCommand extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'BIDV';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'bidv:setup-enterprise-account';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = 'Setup BIDV enteprise account.';

    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'bidv:setup-enterprise-account [BANK_ACCOUNT_ID] [options]';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = [
        'bank_account_id' => 'The BIDV bank account ID.'
    ];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = [
        '--prefix-id' => 'The Prefix ID for BIDV enterprise account.'
    ];

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        $bankAccountId = $params[0] ?? null;

        if (!$bankAccountId) {
            CLI::error("[BANK_ACCOUNT_ID] argument is required.");
            return;
        }

        $bankAccountDetails = model(BankAccountModel::class)->where(['id' => $bankAccountId])->get()->getRow();

        if (!$bankAccountDetails) {
            CLI::error("The bank account ID do not exist.");
            return;
        }

        $prefixId = trim(strtoupper($params['prefix-id'] ?? ''));

        if (!$prefixId) {
            CLI::error("[prefix-id] option is required.");
            return;
        }

        $bidvEnterpriseAccountModel = model(BidvEnterpriseAccountModel::class);
        $bidvEnterpriseAccountDetails = $bidvEnterpriseAccountModel->where('bank_account_id', $bankAccountId)->get()->getRow();

        try {
            if (!$bidvEnterpriseAccountDetails) {
                $bidvEnterpriseAccountModel->insert(['bank_account_id' => $bankAccountId, 'prefix_id' => $prefixId]);
            } else {
                $bidvEnterpriseAccountModel->update($bidvEnterpriseAccountDetails->id, ['prefix_id' => $prefixId]);
            }
        } catch (\Exception $e) {
            CLI::error("The prefix ID is duplicated.");
        }
    }
}
