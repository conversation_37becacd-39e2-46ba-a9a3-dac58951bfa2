<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use Config\OAuth2;
use phpseclib3\Crypt\RSA;

class OAuth2KeysCommand extends BaseCommand
{
    protected $group = 'OAuth2';

    protected $name = 'oauth2:keys';

    protected $description = 'Create the encryption keys for OAuth2.';

    protected $options = [
        'force' => 'Force the operation to run when encryption keys already exist.',
        'length' => 'The length of the encryption keys',
    ];

    public function run(array $params)
    {
        $config = config(OAuth2::class);

        $publicKey = $config->publicKey;
        $privateKey = $config->privateKey;

        $force = array_key_exists('force', $params);

        if ((file_exists($publicKey) || file_exists($privateKey)) && ! $force) {
            CLI::error('Encryption keys already exist. Use the --force flag to overwrite them.');
            return;
        }

        $key = RSA::createKey($params['length'] ?? 4096);

        if (! is_dir(dirname($publicKey))) {
            mkdir(dirname($publicKey), 0700, true);
        }

        file_put_contents($publicKey, $key->getPublicKey());
        file_put_contents($privateKey, (string) $key);

        chmod($publicKey, 0600);
        chmod($privateKey, 0600);

        CLI::write('Encryption keys generated successfully.', 'green');
    }
}
