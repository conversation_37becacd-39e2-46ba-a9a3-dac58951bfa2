<?php

namespace App\Commands;

use CodeIgniter\CLI\CLI;
use App\Models\CounterModel;
use CodeIgniter\CLI\BaseCommand;
use App\Models\TransactionsModel;
use App\Models\NotificationTelegramModel;

class TelegramCounterWorker extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'Worker';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'worker:telegram-counter';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = 'The worker that handles to telegram queues.';

    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'worker:telegram-counter';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = [];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = [];

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        $rabbitmq = new \App\Libraries\RabbitMQClient();
        $connected = $rabbitmq->connect();

        if (!$connected) {
            CLI::error('Cannot connect to RabbitMQ.');
            return;
        }

        $rabbitmq->queueDeclare('telegram_counter');

        CLI::write("[*] Waiting for messages. To exit press CTRL+C\n");


        $callback = function ($msg) {
            $db = db_connect();

            CLI::write("[" . date('Y-m-d H:i:s') . "] Received " . $msg->getBody() . "\n");

            $queue = json_decode($msg->getBody());
            
            $transactionsModel = model(TransactionsModel::class);

            $transactionsModel->set('chat_push_message', 'chat_push_message+1', false)->where(['id' => $queue->transaction_id])->update();

            $notificationTelegramModel = model(NotificationTelegramModel::class);
            $counterModel = model(CounterModel::class);
            $result = $notificationTelegramModel->where(['id' => $queue->notify_id])->get()->getRow();
            if($result)
                $counterModel->chat($result->company_id, FALSE, 'telegram');
            
            CLI::write("[" . date('Y-m-d H:i:s') . "] Done\n");
            log_message('error', '[PID: ' . getmypid() . ' - Telegram counter worker debug]: ' . $queue->id);
            $msg->ack();

            $db->close();
        };
        
        $rabbitmq->channel->basic_qos(null, 1, false);
        $rabbitmq->channel->basic_consume($rabbitmq->getQueueDeclaration('telegram_counter', 'name'), '', false, false, false, false, $callback);

        try {
            $rabbitmq->channel->consume();
        } catch (\Throwable $e) {
            log_message('error', 'Telegram counter worker: ' . $e->getMessage());
            CLI::error($e->getMessage());
        }

        $rabbitmq->close();
    }
}
