<?php

namespace App\Commands;

use App\Models\OAuthAuthCodeModel;
use App\Models\OAuthRefreshTokenModel;
use App\Models\OAuthTokenModel;
use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class OAuth2PurgeCommand extends BaseCommand
{
    protected $group = 'OAuth2';

    protected $name = 'oauth2:purge';

    protected $description = 'Purge revoked and expired tokens and authencation codes.';

    protected $options = [
        'hours' => 'The number of hours to retain expired tokens',
    ];

    public function run(array $params)
    {
        $expired = $params['hours'] ?? 24;
        $expired = (int) $expired;

        $expired = date('Y-m-d H:i:s', strtotime("-$expired hours"));

        model(OAuthTokenModel::class)
            ->where('revoked', true)
            ->orWhere('expires_at <', $expired)
            ->delete();
        model(OAuthAuthCodeModel::class)
            ->where('expires_at <', $expired)
            ->delete();
        model(OAuthRefreshTokenModel::class)
            ->where('revoked', true)
            ->orWhere('expires_at <', $expired)
            ->delete();

        CLI::write(sprintf('Purged expired and revoked tokens and auth codes older than %s.', $expired));
    }
}
