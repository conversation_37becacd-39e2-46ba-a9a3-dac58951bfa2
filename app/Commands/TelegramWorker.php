<?php

namespace App\Commands;

use CodeIgniter\CLI\CLI;
use CodeIgniter\CLI\BaseCommand;
use PhpAmqpLib\Message\AMQPMessage;
use App\Models\NotificationTelegramQueueModel;

class TelegramWorker extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'Worker';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'worker:telegram';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = 'The worker that handles to telegram queues.';

    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'worker:telegram';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = [];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = [];

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        $rabbitmq = new \App\Libraries\RabbitMQClient();
        $connected = $rabbitmq->connect();

        if (!$connected) {
            CLI::error('Cannot connect to RabbitMQ.');
            return;
        }

        $rabbitmq->queueDeclare('telegram');

        CLI::write("[*] Waiting for messages. To exit press CTRL+C\n");


        $callback = function ($msg) use ($rabbitmq) {
            $db = db_connect();

            CLI::write("[" . date('Y-m-d H:i:s') . "] Received " . $msg->getBody() . "\n");

            $queue = json_decode($msg->getBody());

            $notificationTelegramQueueModel = model(NotificationTelegramQueueModel::class);
            $result = $notificationTelegramQueueModel->sendTelegramMessage($queue->message, $queue->chat_id, 'html', $queue->message_thread_id);

            $queue_status = 'SoftFailed';
            $queue_last_log = '';

            if(isset($result['response']))
                $queue_last_log = NotificationTelegramQueueModel::summaryLastLogResponse($result['response']);

            if(isset($result['success']) && $result['success'] == TRUE) {
                if(isset($result['response'])) {

                    $api_response = json_decode($result['response']);

                    if($api_response) {
                        if(is_object($api_response) && isset($api_response->ok) && $api_response->ok == true) {
                            $queue_status = 'Success';
                        }

                        if(is_object($api_response) && isset($api_response->ok) && $api_response->ok == false) {
                            $queue_status = 'Failed';
                        }
                    }
                }
            }

            $notificationTelegramQueueModel->set(['status' => $queue_status,'last_log' => $queue_last_log])->where(['id' => $queue->id])->update();

            if ($queue_status == 'Success') {
                $telegramCounterMsg = new AMQPMessage(
                    json_encode($queue),
                    array('delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT)
                );

                try {
                    $rabbitmq->queueDeclare('telegram_counter');
                    $rabbitmq->channel->basic_publish($telegramCounterMsg, '', $rabbitmq->getQueueDeclaration('telegram_counter', 'name'));
                } catch (\Exception $e) {
                    log_message('error', 'Telegram counter queue failed: ' . $e->getMessage());
                }
            }
            
            CLI::write("[" . date('Y-m-d H:i:s') . "] Done\n");
            log_message('error', '[PID: ' . getmypid() . ' - Telegram worker debug]: ' . $queue->id);
            $msg->ack();

            $db->close();
        };
        
        $rabbitmq->channel->basic_qos(null, 1, false);
        $rabbitmq->channel->basic_consume($rabbitmq->getQueueDeclaration('telegram', 'name'), '', false, false, false, false, $callback);

        try {
            $rabbitmq->channel->consume();
        } catch (\Throwable $e) {
            log_message('error', 'Telegram worker: ' . $e->getMessage());
            CLI::error($e->getMessage());
        }

        $rabbitmq->close();
    }
}
