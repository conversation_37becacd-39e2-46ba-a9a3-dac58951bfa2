<?php

namespace App\Commands;

use CodeIgniter\CLI\CLI;
use App\Models\PgWebhookModel;
use CodeIgniter\CLI\BaseCommand;
use App\Libraries\RabbitMQClient;
use App\Models\PgWebhookQueueModel;

class PgWebhookWorker extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'Worker';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'worker:pg:webhook';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = '';

    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'worker:pg:webhook';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = [];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = [];

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        helper(['general']);

        $rabbitmq = new RabbitMQClient();
        $connected = $rabbitmq->connect();

        if (!$connected) {
            CLI::error('Cannot connect to RabbitMQ.');
            return;
        }

        $queueName = 'pg_webhook';
        $rabbitmq->queueDeclare($queueName);

        CLI::write("[*] Waiting for messages. To exit press CTRL+C\n");

        $callback = function ($msg) {
            $db = db_connect();

            CLI::write(sprintf("[%s] Received %s\n", date('Y-m-d H:i:s'), $msg->getBody()));

            /**
             * @var array{pg_webhook_id:int,data:array,event_type:string} $data
             */
            $data = json_decode($msg->getBody(), true);

            /** @var PgWebhookModel $pgWebhookModel */
            $pgWebhookModel = slavable_model(PgWebhookModel::class, 'PaymentGateway');
            $pgWebhook = $pgWebhookModel->where('id', $data['pg_webhook_id'])->where('active', 1)->first();

            if (!$pgWebhook) {
                log_message('error', sprintf('[PgWebhookWorker] PG Webhook not found for ID: %d', $data['pg_webhook_id']));
                CLI::error(sprintf('PG Webhook not found for ID: %d', $data['pg_webhook_id']));
                $msg->ack();
                return;
            }

            $success = $pgWebhookModel->send($data['event_type'], $pgWebhook, $data['data'], 'First');

            if (!$success) {
                $webhookQueueModel = model(PgWebhookQueueModel::class);
                $webhookQueueModel->insert([
                    'pg_merchant_id' => $pgWebhook->pg_merchant_id,
                    'company_id' => $pgWebhook->company_id,
                    'pg_webhook_id' => $pgWebhook->id,
                    'status' => 'SoftFailed',
                ]);
            }

            CLI::write(sprintf("[%s] Done\n", date('Y-m-d H:i:s')));

            log_message('error', sprintf('[PgWebhookWorker] Acked message: %s | PID: %s', $msg->getBody(), getmypid()));
            $msg->ack();

            $db->close();
        };
        
        $rabbitmq->channel->basic_qos(null, 1, false);
        $rabbitmq->channel->basic_consume($rabbitmq->getQueueDeclaration($queueName, 'name'), '', false, false, false, false, $callback);

        try {
            $rabbitmq->channel->consume();
        } catch (\Throwable $e) {
            log_message('error', sprintf('[PgWebhookWorker] Failed to consume message: %s | Trace: %s', $e->getMessage(), $e->getTraceAsString()));
            CLI::error($e->getMessage());
        }

        $rabbitmq->close();
    }
}
