<?php

namespace App\Commands;

use CodeIgniter\CLI\CLI;
use App\Models\PgCardModel;
use App\Models\PgProfileModel;
use App\Models\PgCustomerModel;
use App\Models\PgAgreementModel;
use CodeIgniter\CLI\BaseCommand;
use App\Models\CompanySubscriptionModel;
use App\Features\PaymentGateway\MpgsProfile;
use App\Features\PaymentGateway\PaymentGatewayFeature;

class PgCardHealthCheckCommand extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'Payment Gateway';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'pg:card-health-check';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = '';

    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'pg:card-health-check';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = [];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = [];

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        $pgCardModel = model(PgCardModel::class);
        $pgAgreementModel = model(PgAgreementModel::class);
        $pgCustomerModel = model(PgCustomerModel::class);

        $pgCards = $pgCardModel->where('status', 'VALID')->get()->getResult();
        
        foreach ($pgCards as $pgCard) {
            $paymentGatewayFeature = new PaymentGatewayFeature;
            
            $pgProfile = model(PgProfileModel::class)->where('id', $pgCard->pg_profile_id)->first();
            
            if (!$pgProfile || !$pgProfile->active) {
                continue;
            }
            
            $pgMerchant = $paymentGatewayFeature->getPgMerchantById($pgCard->pg_merchant_id);
            
            if (!$pgMerchant || !$pgMerchant->active) {
                continue;
            }
            
            $profile = $paymentGatewayFeature->resolveProfileById($pgCard->pg_profile_id);
            
            if ($profile instanceof MpgsProfile) {
                $client = $profile->makeRestApiClient();
                $response = $client->retrieveToken($pgCard->payment_token);
                
                if ($response->getStatusCode() !== 200) {
                    $pgCardModel->where('id', $pgCard->id)->set(['status' => 'INVALID'])->update();
                    $pgCard->status = 'INVALID';
                }
                
                $responseJson = json_decode($response->getBody());
                
                $safeCardUpdateData = [
                    'status' => $response->status ?? $pgCard->status,
                    'last_used_at' => $responseJson->usage->lastUsedTime ?? $pgCard->last_used_at
                ];
                
                try {
                    $updated = $pgCardModel->where('id', $pgCard->id)->set($safeCardUpdateData)->update();
                    
                    if (!$updated) {
                        continue;
                    }
                    
                    if ($safeCardUpdateData['status'] === 'VALID') {
                        continue;
                    }
                    
                    $pgAgreements = $pgAgreementModel->where('pg_card_id', $pgCard->id)->get()->getResult();
                    
                    if (!count($pgAgreements)) {
                        continue;
                    }
                    
                    $pgAgreementModel->whereIn('id', array_column($pgAgreements, 'id'))->set(['auto_renew' => 0])->update();

                    $pgCustomers = $pgCustomerModel->whereIn('id', array_column($pgAgreements, 'pg_customer_id'))->get()->getResult();

                    $profile->pushWebhookEvent('PAYMENT_TOKEN_INVALID', [
                        'card' => [
                            'id' => $pgCard->id,
                            'status' => $pgCard->status,
                            'last_used_at' => $pgCard->last_used_at,
                        ],
                        'agreements' => array_map(function ($agreement) {
                            return [
                                'id' => $agreement->id,
                                'agreement_name' => $agreement->agreement_name,
                                'agreement_id' => $agreement->agreement_id,
                                'type' => $agreement->type,
                                'status' => $agreement->status,
                                'active' => $agreement->active,
                                'auto_renew' => $agreement->auto_renew,
                                'amount_variability' => $agreement->amount_variability,
                                'amount_per_payment' => $agreement->amount_per_payment,
                                'min_amount_per_payment' => $agreement->min_amount_per_payment,
                                'max_amount_per_payment' => $agreement->max_amount_per_payment,
                                'start_date' => $agreement->start_date,
                                'expiry_date' => $agreement->expiry_date,
                                'next_due_date' => $agreement->next_due_date,
                                'payment_frequency' => $agreement->payment_frequency,
                                'minimum_days_between_payments' => $agreement->minimum_days_between_payments,
                                'pg_customer_id' => $agreement->pg_customer_id,
                            ];
                        }, $pgAgreements),
                        'customers' => array_map(function ($customer) {
                            return [
                                'id' => $customer->id,
                                'customer_id' => $customer->customer_id,
                            ];
                        }, $pgCustomers),
                    ]);

                    CLI::write('Turned off auto renew agreement: ' . count($pgAgreements));
                } catch (\Throwable $e) {
                    log_message('error', sprintf('[PaymentGatewayCardHealthCheckCommand->run] %s | Trace: %s', $e->getMessage(), $e->getTraceAsString()));

                    CLI::error(sprintf('Unexpected error: %s', $e->getMessage()));
                }
            }
        }

        CLI::write('PG Card health Check completed successfully.');
    }
}
