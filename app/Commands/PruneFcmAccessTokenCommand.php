<?php

namespace App\Commands;

use CodeIgniter\CLI\CLI;
use App\Libraries\FcmClient;
use CodeIgniter\CLI\BaseCommand;
use App\Models\FcmAccessTokenModel;

class PruneFcmAccessTokenCommand extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'Mobile';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'fcm:prune-access-token';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = '';

    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'command:name [arguments] [options]';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = [];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = [];

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        model(FcmAccessTokenModel::class)->where(['expires_at <=' => date('Y-m-d H:i:s')])->delete();
        CLI::write("Deleted all expired tokens");
    }
}
