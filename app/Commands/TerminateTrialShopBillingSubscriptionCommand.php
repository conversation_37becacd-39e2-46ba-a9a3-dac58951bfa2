<?php

namespace App\Commands;

use App\Models\CompanySubscriptionModel;
use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class TerminateTrialShopBillingSubscriptionCommand extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'Shop Billing';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'shop-billing:terminate-trial-subscription';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = '';

    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'shop-billing:terminate-trial-subscription';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = [];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = [];

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        helper(['general']);
        
        $companySubscriptionModel = model(CompanySubscriptionModel::class);
        
        $subscriptions = $companySubscriptionModel
            ->where('is_trial', 1)
            ->where('auto_renew', 0)
            ->where('shop_limit > ', 0)
            ->where('status', 'Active')
            ->where('end_date <=', date('Y-m-d'))
            ->get()->getResult();
            
        if (count($subscriptions)) {
            $companySubscriptionModel->whereIn('id', array_column($subscriptions, 'id'))->set(['status' => 'Terminated'])->update();
            
            foreach ($subscriptions as $subscription) {
                add_system_log([
                    'company_id' => $subscription->company_id, 
                    'data_type' => 'terminate_trial_shop_billing_subscription', 
                    'description' => "Terminated trial shop billing subscription", 
                    'type' => 'Info', 
                    'by' => 'Cron'
                ]);
            }
        }    
    }
}
