<?php

namespace App\Commands;

use CodeIgniter\CLI\CLI;
use App\Models\PgAgreementModel;
use CodeIgniter\CLI\BaseCommand;
use App\Libraries\RabbitMQClient;
use App\Features\PaymentGateway\PaymentGatewayFeature;
use App\Features\PaymentGateway\Contexts\AgreementContext;
use App\Models\PgMerchantModel;
use App\Models\PgOrderModel;

class PgRenewAgreementWorker extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'Worker';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'worker:pg:renew-agreement';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = '';

    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'worker:pg:renew-agreement';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = [];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = [];

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
         helper(['general']);

        $rabbitmq = new RabbitMQClient();
        $connected = $rabbitmq->connect();

        if (!$connected) {
            CLI::error('Cannot connect to RabbitMQ.');
            return;
        }

        $queueName = 'pg_renew_agreement';
        $rabbitmq->queueDeclare($queueName);

        CLI::write("[*] Waiting for messages. To exit press CTRL+C\n");

        $callback = function ($msg) {
            $db = db_connect();

            CLI::write(sprintf("[%s] Received %s\n", date('Y-m-d H:i:s'), $msg->getBody()));
            
            /**
             * @var array{pg_merchant_id:int,pg_agreement_id:int,company_subscription_id:int} $data
             */
            $data = json_decode($msg->getBody(), true);

            $pgMerchantModal = slavable_model(PgMerchantModel::class, 'PaymentGateway');
            $pgAgreementModel = slavable_model(PgAgreementModel::class, 'PaymentGateway');
            $pgOrderModel = slavable_model(PgOrderModel::class, 'PaymentGateway');

            $pgMerchant = $pgMerchantModal->where('id', $data['pg_merchant_id'])
                ->where('active', 1)
                ->first();

            if (!$pgMerchant) {
                CLI::error(sprintf('PgMerchant with ID %d not found or inactive.', $data['pg_merchant_id']));
                log_message('error', sprintf('[PgRenewAgreementWorker] PgMerchant with ID %d not found or inactive', $data['pg_merchant_id']));
                $msg->ack();
                return;
            }

            $pgAgreement = $pgAgreementModel->where('id', $data['pg_agreement_id'])
                ->where('pg_merchant_id', $data['pg_merchant_id'])
                ->where('active', 1)
                ->where('auto_renew', 1)
                ->first();

            if (!$pgAgreement) {
                CLI::error(sprintf('PgAgreement with ID %d not found for merchant ID %d.', $data['pg_agreement_id'], $data['pg_merchant_id']));
                log_message('error', sprintf('[PgRenewAgreementWorker] PgAgreement with ID %d not found for merchant ID %d', $data['pg_agreement_id'], $data['pg_merchant_id']));
                $msg->ack();
                return;
            }

            $pgOrder = property_exists($pgAgreement, 'pg_order_id') ? $pgOrderModel->where('pg_merchant_id', $data['pg_merchant_id'])
                ->where('pg_agreement_id', $data['pg_agreement_id'])
                ->where('id', $pgAgreement->pg_order_id)
                ->first() : null;

            if (!$pgOrder) {
                CLI::error(sprintf('PgOrder with ID %d not found for merchant ID %d.', $pgAgreement->pg_order_id, $data['pg_merchant_id']));
                log_message('error', sprintf('[PgRenewAgreementWorker] PgOrder with ID %d not found for merchant ID %d', $pgAgreement->pg_order_id, $data['pg_merchant_id']));
                $msg->ack();
                return;
            }

            /** @var AgreementContext $agreementCheckout **/
            $agreementCheckout = null;

            $fields = [
                'agreement_id' => $pgAgreement->agreement_id,
                'order_id' => sprintf('RN-%s-%s', $pgAgreement->id, date('YmdHis')),
            ];
            
            try {
                $paymentGatewayFeature = new PaymentGatewayFeature;

                $paymentGatewayFeature->withAgreementContext($pgMerchant->merchant_id, $pgAgreement->agreement_id);
                $paymentGatewayFeature->agreementContext()->loadFields($fields);
                $agreementCheckout = $paymentGatewayFeature->agreementContext();
                
                $agreementCheckout->renewAgreement();

                CLI::write(sprintf("[%s] Done\n", date('Y-m-d H:i:s')));

                log_message('error', sprintf('[PgRenewAgreementWorker] Acked message: %s | PID: %s', $msg->getBody(), getmypid()));
                $msg->ack();

                $db->close();
            } catch (\Throwable $e) {
                if ($e->getCode() === 400) {
                    CLI::error($e->getMessage());
                    log_message('error', sprintf('[PgRenewAgreementWorker] Error renewing agreement: %s', $e->getMessage()));
                    $msg->ack();
                    return;
                }
                
                if ($e->getCode() === 419) {
                    CLI::error($e->getMessage());
                    log_message('error', sprintf('[PgRenewAgreementWorker] Too many requests: %s', $e->getMessage()));
                    $msg->ack();
                    return;
                }

                CLI::error(sprintf('An unexpected error occurred: %s', $e->getMessage()));
                log_message('error', sprintf('[PgRenewAgreementWorker] An unexpected error occurred: %s | Trace: %s', $e->getMessage(), $e->getTraceAsString()));
            }
        };
        
        $rabbitmq->channel->basic_qos(null, 1, false);
        $rabbitmq->channel->basic_consume($rabbitmq->getQueueDeclaration($queueName, 'name'), '', false, false, false, false, $callback);

        try {
            $rabbitmq->channel->consume();
        } catch (\Throwable $e) {
            log_message('error', sprintf('[PgRenewAgreementWorker] Failed to consume message: %s | Trace: %s', $e->getMessage(), $e->getTraceAsString()));
            CLI::error($e->getMessage());
        }

        $rabbitmq->close();
    }
}
