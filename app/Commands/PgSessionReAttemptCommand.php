<?php

namespace App\Commands;

use App\Libraries\RabbitMQClient;
use App\Models\PgSessionModel;
use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use Exception;
use PhpAmqpLib\Message\AMQPMessage;

class PgSessionReAttemptCommand extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'Payment Gateway';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'pg:re-attempt-session';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = '';

    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'pg:re-attempt-session <trace_id> [options]';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = ['trace_id' => 'The PG session trace ID'];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = [];

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        $traceId = $params[0] ?? '';
        
        if (!$traceId) {
            CLI::error('Please enter trace ID');
            return;
        }
        
        $pgSession = model(PgSessionModel::class)->where('trace_id', $traceId)->first();
        
        if (!$pgSession) {
            CLI::error('PG session not found');
            return;
        }
        
        $rabbitmq = new RabbitMQClient;
        $queuable = $rabbitmq->connect();
        
        if (!$queuable) {
            throw new Exception('Cannot connect RabbitMQClient');
        }
        
        $msg = new AMQPMessage($pgSession->fields, ['delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT]);
        
        if (!$pgSession->queue_name) {
            CLI::error('PG session queue name empty');
            return;
        }
        
        $rabbitmq->queueDeclare($pgSession->queue_name);
        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration($pgSession->queue_name, 'name'));
        
        CLI::write('Message sent');
    }
}
