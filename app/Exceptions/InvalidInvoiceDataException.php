<?php

namespace App\Exceptions;

use Exception;

class InvalidInvoiceDataException extends Exception
{
    protected array $messages = [];

    public static function withMessage(string $key, string $message): self
    {
        $instance = new self();
        $instance->messages[$key] = $message;

        return $instance;
    }

    public function getMessages(): array
    {
        return $this->messages;
    }
}
