<?php

namespace App\Controllers;

use App\Config\Invoice;
use App\Models\InvoiceCustomerInfoModel;
use App\Models\InvoiceItemModel;
use App\Models\InvoiceModel;

class Invoices extends BaseController
{
    public function index()
    { 
        $data = [
            'page_title' => 'Hóa đơn',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        if(!in_array($this->company_details->role,['Admin', 'SuperAdmin']))
            show_404();
      
        echo theme_view('templates/autopay/header',$data);
        echo view('invoices/index',$data);
        echo theme_view('templates/autopay/footer',$data);

    }

    public function ajax_invoices_list() {

        if(!in_array($this->company_details->role,['Admin', 'SuperAdmin']))
            show_404();
     
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        $invoiceModel = model(InvoiceModel::class);

        $invoices = $invoiceModel->getDatatables($this->user_session['company_id']);
         
        $data = array();

        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');

        foreach ($invoices as $invoice) {

         
            $no++;
            $row = array();
            
            $row[] = $no;
            $row[] = "<a href='" . base_url('invoices/details/' . $invoice->id) . "'>#". esc($invoice->id) . "</a>";

            if($invoice->type == "NewOrder")
                $row[] = "<span class='text-info'>Đăng ký mới</span>";  
            else if($invoice->type == "Recurring")
                $row[] = "<span class='text-warning'>Gia hạn</span>";  
            else if($invoice->type == "Credit")
                $row[] = "<span class=''>Thêm tiền</span>";  
            else if($invoice->type == "Excess")
                $row[] = "<span class='text-danger'>Vượt hạn mức</span>"; 
            else if ($invoice->type == "SubscriptionChange")
                $row[] = "<span class='text-success'>Đổi gói dịch vụ</span>";
            else
                $row[] = esc($invoice->type);
                
            if($invoice->status == "Unpaid")
                $row[] = "<span class='badge rounded-pill bg-danger'>Chưa thanh toán</span>";  
            else if($invoice->status == "Paid")
                $row[] = "<span class='badge rounded-pill bg-success'>Đã thanh toán</span>";  
            else if($invoice->status == "Cancelled")
                $row[] = "<span class='badge rounded-pill bg-secondary'>Đã hủy</span>";  
            else if($invoice->status == "Refunded")
                $row[] = "<span class='badge rounded-pill bg-warning'>Đã hoàn tiền</span>";  
            else
                $row[] = esc($invoice->status);  

            $row[] = number_format($invoice->total) . " đ";
            
            $row[] = esc($invoice->date);
            $row[] = "<a href='" . base_url('invoices/details/' . $invoice->id) . "'>Xem <i class='bi bi-chevron-right'></i></a>";

           
            $data[] = $row;
        }
 
        $output = array(
            "draw" => $draw,
            "recordsTotal" => $invoiceModel->countAll($this->user_session['company_id']),
            "recordsFiltered" => $invoiceModel->countFiltered($this->user_session['company_id']),
            "data" => $data,
        );
        return $this->response->setJSON($output);
    
    
    }

    public function details($invoice_id='') {
        $data = [
            'page_title' => 'Hóa đơn',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        if(!in_array($this->company_details->role,['Admin', 'SuperAdmin']))
            show_404();

        if(!is_numeric($invoice_id))
            show_404();

        $invoiceModel = model(InvoiceModel::class);
        $invoiceItemModel = model(InvoiceItemModel::class);

        $data['invoice_details'] = $invoiceModel->where(['id' => $invoice_id ,'company_id' =>$this->user_session['company_id']])->get()->getRow();

        if(!is_object($data['invoice_details']))
            show_404();

        $data['invoice_items'] = $invoiceItemModel->where(['invoice_id' => $invoice_id])->orderBy('position','ASC')->get()->getResult();

        $data['paycode'] = create_paycode($invoice_id);
        $data['qrcode'] = '';

        if($data['invoice_details']->status == "Unpaid") {
            $data['qrcode'] = "https://qr.sepay.vn/img?bank=MBBank&acc=*************&template=&amount=" . intval($data['invoice_details']->total) . "&des=" . $data['paycode'];
        }


        $data['customerInfos'] = model(InvoiceCustomerInfoModel::class)->where('company_id', $this->user_session['company_id'])->findAll();
        $data['canRequestVatInvoice'] = config(Invoice::class)->vatInvoiceRequestEnabled && $data['invoice_details']->total > 0;

        echo theme_view('templates/autopay/header',$data);
        echo view('invoices/details',$data);
        echo theme_view('templates/autopay/footer',$data);

  
    }

    public function ajax_check_status() {
    
        if ($this->request->getMethod(true) != 'POST')
            return '';

        $invoice_id = $this->request->getPost('invoice_id');

        if(!is_numeric($invoice_id))
            return $this->response->setJSON(array('status'=>FALSE));

        $invoiceModel = model(InvoiceModel::class);
        $invoice_details = $invoiceModel->where(['company_id' => $this->user_session['company_id'], 'id' => $invoice_id])->get()->getRow();

        if(is_object($invoice_details))
            return $this->response->setJSON(array('status'=>TRUE, 'invoice_status' => $invoice_details->status));
        else
            return $this->response->setJSON(array('status'=>FALSE));




        
    }

    public function lookup($invoice_id = '', $type = '') 
    {
        $data = [
            'page_title' => 'Tra cứu hóa đơn',
        ];

        if(!is_numeric($invoice_id) || !is_string($type))
            show_404();

        $invoiceModel = model(InvoiceModel::class);
        $invoiceItemModel = model(InvoiceItemModel::class);

        $data['invoice_details'] = $invoiceModel->where(['id' => $invoice_id ,'type' =>$type])->get()->getRow();

        if(!is_object($data['invoice_details']))
            show_404();

        $companyModel = model(CompanyModel::class);
        $data['company_details'] = $companyModel->where(['id' => $data['invoice_details']->company_id])->get()->getRow();

        $data['invoice_items'] = $invoiceItemModel->where(['invoice_id' => $invoice_id])->orderBy('position','ASC')->get()->getResult();

        $data['paycode'] = create_paycode($invoice_id);
        $data['qrcode'] = '';

        if($data['invoice_details']->status == "Unpaid") {
            $data['qrcode'] = "https://qr.sepay.vn/img?bank=MBBank&acc=*************&template=&amount=" . intval($data['invoice_details']->total) . "&des=" . $data['paycode'];
        }

        echo view('invoices/lookup',$data);
    }
 
    public function ajax_check_status_lookup() {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        $invoice_id = xss_clean($this->request->getPost('invoice_id'));
        $type = xss_clean($this->request->getPost('type'));
        $company_id = xss_clean($this->request->getPost('company_id'));

        if(!is_numeric($invoice_id) || !is_string($type) || !is_numeric($company_id))
            return $this->response->setJSON(array('status'=>FALSE));

        $invoiceModel = model(InvoiceModel::class);
        $invoice_details = $invoiceModel->where(['company_id' => $company_id, 'id' => $invoice_id, 'type' => $type])->get()->getRow();

        if(is_object($invoice_details))
            return $this->response->setJSON(array('status'=>TRUE, 'invoice_status' => $invoice_details->status));
        else
            return $this->response->setJSON(array('status'=>FALSE));
    }
    
}
