<?php

namespace App\Controllers;

use Config\Mobile;
use CodeIgniter\API\ResponseTrait;
use App\Controllers\BaseController;
use App\Models\ShopModel;
use App\Models\BankAccountModel;
use App\Models\BankSubAccountModel;

class Mobileapp extends BaseController
{
    use ResponseTrait;
    
    public function __construct()
    {
        $this->mobileConfig = config(Mobile::class);
        $this->mobileConfig->visibility = $this->mobileConfig->visibility ?? false;
        $this->mobileConfig->downloadOneLinkUrl = $this->mobileConfig->downloadOneLinkUrl ?? '';
        $this->mobileConfig->downloadOneLinkQrUrl = $this->mobileConfig->downloadOneLinkQrUrl ?? '';
    }

    public function index()
    {
        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin']))
            return $this->respond(['status' => false, 'message' => 'Bạn không có quyền truy cập.']);

        $data = [
            'page_title' => 'Mobile app',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'mobile_config' => $this->mobileConfig
        ];

        echo theme_view('templates/autopay/header', $data);
        echo theme_view('sepayapp/index', $data);
        echo theme_view('templates/autopay/footer', $data);
    }

    public function step1()
    {
        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin']))
            return $this->respond(['status' => false, 'message' => 'Bạn không có quyền truy cập.']);

        $data = [
            'page_title' => 'Ứng dụng SePay',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        $bankAccounts = model(BankAccountModel::class)
            ->select(['tb_autopay_bank_account.id', 'tb_autopay_bank_account.account_number', 'tb_autopay_bank_account.label', 'tb_autopay_bank_account.bank_sms', 'tb_autopay_bank_account.bank_api', 'tb_autopay_bank_account.bank_api_connected', 'tb_autopay_bank_account.account_holder_name', 'tb_autopay_bank_account.bank_sms_connected', 'tb_autopay_bank.icon_path'])
            ->join('tb_autopay_bank', 'tb_autopay_bank.id = tb_autopay_bank_account.bank_id')
            ->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id']])
            ->groupStart()
                ->groupStart()
                    ->where('tb_autopay_bank_account.bank_api', 1)
                    ->where('tb_autopay_bank_account.bank_api_connected', 1)
                ->groupEnd()
                ->orWhere('tb_autopay_bank_account.bank_sms', 1)
            ->groupEnd()
            ->get()->getResult();

        $data['bank_accounts'] = $bankAccounts;

        echo theme_view('templates/autopay/header', $data);
        echo view('sepayapp/step1', $data);
        echo theme_view('templates/autopay/footer', $data);
    }

    public function shop_step1()
    {
        //Channel Partner
        is_channel_partner();

        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin']))
            return $this->respond(['status' => false, 'message' => 'Bạn không có quyền truy cập.']);

        $data = [
            'page_title' => 'Ứng dụng SePay',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        $shopModel = model(ShopModel::class);

        $data['shops'] = $shopModel
        ->select(['tb_autopay_shop.name', 'tb_autopay_shop.id', 'tb_autopay_shop.address'])
        ->where([
            'tb_autopay_shop.company_id' => $this->company_details->id,
            'tb_autopay_shop.active' => 1
            ])
        ->orderBy('tb_autopay_shop.id', 'DESC')
        ->get()
        ->getResult();

        if (!$data['shops']){
            session()->setFlashdata('error_shop', 'Cửa hàng không tồn tại hoặc chưa được kích hoạt');
            return redirect()->to(base_url('mobileapp'));
        } 

        echo theme_view('templates/autopay/header', $data);
        echo theme_view('sepayapp/step1', $data);
        echo theme_view('templates/autopay/footer', $data);
    }

    public function step2($bankAccountId = '')
    {
        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin']))
            return $this->respond(['status' => false, 'message' => 'Bạn không có quyền truy cập.']);

        $data = [
            'page_title' => 'Mobile app',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        $bankAccountId = trim(xss_clean($bankAccountId));

        if (!is_numeric($bankAccountId)) show_404();

        $bankAccountDetails = model(BankAccountModel::class)
            ->select(['tb_autopay_bank_account.id', 'tb_autopay_bank_account.account_number', 'tb_autopay_bank_account.label', 'tb_autopay_bank_account.bank_sms', 'tb_autopay_bank_account.bank_api', 'tb_autopay_bank_account.bank_api_connected', 'tb_autopay_bank_account.account_holder_name', 'tb_autopay_bank_account.bank_sms_connected', 'tb_autopay_bank.icon_path'])
            ->join('tb_autopay_bank', 'tb_autopay_bank.id = tb_autopay_bank_account.bank_id')
            ->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id']])
            ->where(['tb_autopay_bank_account.id' => $bankAccountId])
            ->groupStart()
                ->groupStart()
                    ->where('tb_autopay_bank_account.bank_api', 1)
                    ->where('tb_autopay_bank_account.bank_api_connected', 1)
                ->groupEnd()
                ->orWhere('tb_autopay_bank_account.bank_sms', 1)
            ->groupEnd()
            ->first();

        if (!$bankAccountDetails) show_404();

        $bankSubAccounts = model(BankSubAccountModel::class)
            ->select(['id', 'sub_account', 'sub_holder_name', 'label'])
            ->where('bank_account_id', $bankAccountDetails->id)
            ->get()->getResult();

        $data['bank_account_details'] = $bankAccountDetails;
        $data['bank_sub_accounts'] = $bankSubAccounts;

        echo theme_view('templates/autopay/header', $data);
        echo view('sepayapp/step2', $data);
        echo theme_view('templates/autopay/footer', $data);
    }

    public function step3($bankAccountId = '')
    {
        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin']))
            return $this->respond(['status' => false, 'message' => 'Bạn không có quyền truy cập.']);
        
        $data = [
            'page_title' => 'Mobile app',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'mobile_config' => $this->mobileConfig
        ];

        if (! $this->validateData([
            'va_id' => $this->request->getGet('va_id'),
        ], [
            'va_id' => 'required|string|is_natural',
        ])) {
            return redirect()->to(base_url('mobileapp'));
        }

        $bankAccountId = trim(xss_clean($bankAccountId));
        $bankSubAccountId = trim(xss_clean($this->request->getGet('va_id')));

        if (!is_numeric($bankAccountId) || !is_numeric($bankSubAccountId)) show_404();

        $bankAccountDetails = model(BankAccountModel::class)
            ->select(['tb_autopay_bank_account.id', 'tb_autopay_bank_account.account_number', 'tb_autopay_bank_account.label', 'tb_autopay_bank_account.bank_sms', 'tb_autopay_bank_account.bank_api', 'tb_autopay_bank_account.bank_api_connected', 'tb_autopay_bank_account.account_holder_name', 'tb_autopay_bank_account.bank_sms_connected', 'tb_autopay_bank.icon_path'])
            ->join('tb_autopay_bank', 'tb_autopay_bank.id = tb_autopay_bank_account.bank_id')
            ->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id']])
            ->where(['tb_autopay_bank_account.id' => $bankAccountId])
            ->groupStart()
                ->groupStart()
                    ->where('tb_autopay_bank_account.bank_api', 1)
                    ->where('tb_autopay_bank_account.bank_api_connected', 1)
                ->groupEnd()
                ->orWhere('tb_autopay_bank_account.bank_sms', 1)
            ->groupEnd()
            ->first();

        if (!$bankAccountDetails) show_404();

        $bankSubAccountDetails = model(BankSubAccountModel::class)
            ->select(['id', 'sub_account', 'sub_holder_name', 'label'])
            ->where('bank_account_id', $bankAccountDetails->id)
            ->where('id', $bankSubAccountId)
            ->first();

        if (!$bankSubAccountDetails) show_404();

        $data['bank_account_details'] = $bankAccountDetails;
        $data['bank_sub_account_details'] = $bankSubAccountDetails;

        echo theme_view('templates/autopay/header', $data);
        echo view('sepayapp/step3', $data);
        echo theme_view('templates/autopay/footer', $data);
    }
    
    public function ajax_shop_step1()
    {
        //Channel Partner
        is_channel_partner();

        if ($this->request->getMethod(true) != 'POST')
        return '';

        $data = [
            'shop_id_list' => $this->request->getPost('shop_id_list'),
        ];

        if (!count($data['shop_id_list'])) {
            return $this->response->setJSON(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng tải lại trang','test'=> $data]);
        }

        $session = service('session');

        $shopModel = model(ShopModel::class);

        $bank_account_mobile = $shopModel->select('tb_autopay_shop.name, tb_autopay_bank_shop_link.shop_id, tb_autopay_bank_shop_link.bank_account_id, tb_autopay_bank_shop_link.bank_sub_account_id')
        ->join('tb_autopay_bank_shop_link', 'tb_autopay_shop.id = tb_autopay_bank_shop_link.shop_id')
        ->where(['tb_autopay_shop.company_id' => $this->company_details->id])
        ->whereIn('tb_autopay_bank_shop_link.shop_id', $data['shop_id_list'])  // Sửa đổi ở đây
        ->orderBy('tb_autopay_shop.id', 'ASC')
        ->get()
        ->getResult();

        if (!$bank_account_mobile) return $this->failNotFound();

        $session->set('mobileapp', $bank_account_mobile);

        return $this->respond(['status' => true]);
    }

    public function shop_step2()
    {
        //Channel Partner
        is_channel_partner();
        
        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin']))
            return $this->respond(['status' => false, 'message' => 'Bạn không có quyền truy cập.']);

        $data = [
            'page_title' => 'Ứng dụng SePay',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'mobile_config' => $this->mobileConfig
        ];

        $session = service('session');

        $data['bank_account_mobile'] = $session->get('mobileapp');

        echo theme_view('templates/autopay/header', $data);
        echo theme_view('sepayapp/shop_step2', $data);
        echo theme_view('templates/autopay/footer', $data);
    }
}
