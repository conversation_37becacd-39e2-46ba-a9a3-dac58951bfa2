<?php

namespace App\Controllers\Api\V1;

use App\Exceptions\UnauthorizedException;
use App\Models\TransactionsModel;

class Transactions extends BaseController
{
    public function index()
    {
        try {
            $this->can('transaction:read');
            $this->hasPermission('Transactions', 'can_view_all');
        } catch (UnauthorizedException $e) {
            return $this->unauthorized($e->getMessage());
        }

        $data = $this->request->getGet([
            'bank_account_id',
            'account_number',
            'reference_number',
            'from_date',
            'to_date',
            'amount_out',
            'amount_in',
        ]);

        $rules = [
            'bank_account_id' => 'permit_empty|integer',
            'account_number' => 'permit_empty|integer',
            'reference_number' => 'permit_empty|string',
            'from_date' => 'permit_empty|valid_date',
            'to_date' => 'permit_empty|valid_date',
            'amount_out' => 'permit_empty|numeric',
            'amount_in' => 'permit_empty|numeric',
        ];

        if (! $this->validateData($data, $rules)) {
            return $this->validationError($this->validator->getErrors());
        }

        $transactionsModel = slavable_model(TransactionsModel::class, 'Api');

        $query = $transactionsModel
            ->select([
                'tb_autopay_sms_parsed.id',
                'tb_autopay_bank_account.id as bank_account_id',
                'tb_autopay_bank.brand_name as bank_brand_name',
                'tb_autopay_bank_account.account_number',
                'tb_autopay_sms_parsed.transaction_date',
                'tb_autopay_sms_parsed.amount_out',
                'tb_autopay_sms_parsed.amount_in',
                'tb_autopay_sms_parsed.accumulated',
                'tb_autopay_sms_parsed.transaction_content',
                'tb_autopay_sms_parsed.reference_number',
                'tb_autopay_sms_parsed.code',
                'tb_autopay_sms_parsed.sub_account',
            ])
            ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id=tb_autopay_sms_parsed.bank_account_id')
            ->join('tb_autopay_bank', 'tb_autopay_bank.brand_name = tb_autopay_sms_parsed.gateway')
            ->where('tb_autopay_bank_account.company_id', $this->company->id)
            ->where('tb_autopay_sms_parsed.parser_status', 'Success')
            ->orderBy('tb_autopay_sms_parsed.transaction_date', 'desc');

        if ($data['bank_account_id']) {
            $query->where('tb_autopay_sms_parsed.bank_account_id', $data['bank_account_id']);
        }

        if ($data['account_number']) {
            $query->where('tb_autopay_bank_account.account_number', $data['account_number']);
        }

        if ($data['reference_number']) {
            $query->where('tb_autopay_sms_parsed.reference_number', $data['reference_number']);
        }

        if ($data['from_date']) {
            $query->where('tb_autopay_sms_parsed.transaction_date >=', $data['from_date']);
        }

        if ($data['to_date']) {
            $query->where('tb_autopay_sms_parsed.transaction_date <=', $data['to_date']);
        }

        if ($data['amount_out'] && $data['amount_out'] >= 0) {
            $query->where('tb_autopay_sms_parsed.amount_out', $data['amount_out']);
        }

        if ($data['amount_in'] && $data['amount_in'] >= 0) {
            $query->where('tb_autopay_sms_parsed.amount_in', $data['amount_in']);
        }

        $transactions = $query->paginate();

        $data = array_map(function ($transaction) {
            return [
                'id' => intval($transaction->id),
                'bank_account_id' => intval($transaction->bank_account_id),
                'bank_brand_name' => $transaction->bank_brand_name,
                'account_number' => $transaction->account_number,
                'transaction_date' => $transaction->transaction_date,
                'amount_out' => floatval($transaction->amount_out),
                'amount_in' => floatval($transaction->amount_in),
                'accumulated' => floatval($transaction->accumulated),
                'transaction_content' => $transaction->transaction_content,
                'reference_number' => $transaction->reference_number,
                'code' => $transaction->code ?: null,
                'sub_account' => $transaction->sub_account,
            ];
        }, $transactions);

        return $this->success($data, $transactionsModel->pager);
    }
}
