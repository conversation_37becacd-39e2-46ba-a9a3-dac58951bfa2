<?php

namespace App\Controllers\Api\V1;

use App\Exceptions\UnauthorizedException;
use App\Models\BankAccountModel;
use App\Models\BankSubAccountModel;
use CodeIgniter\Model;

class BankAccounts extends BaseController
{
    public function index()
    {
        try {
            $this->can('bank-account:read');
            $this->hasPermission('BankAccount', 'can_view_all');
        } catch (UnauthorizedException $e) {
            return $this->unauthorized($e->getMessage());
        }

        $query = $this->fetchBankAccountData();
        $bankAccounts = $query->paginate();
        $formattedAccounts = array_map([$this, 'formatBankAccount'], $bankAccounts);

        return $this->success($formattedAccounts, $query->pager);
    }

    public function show($id = null)
    {
        try {
            $this->can('bank-account:read');
            $this->hasPermission('BankAccount', 'can_view_all');
        } catch (UnauthorizedException $e) {
            return $this->unauthorized($e->getMessage());
        }

        $bankAccount = $this->fetchBankAccountData()
            ->where('tb_autopay_bank_account.id', $id)
            ->first();

        if (! $bankAccount) {
            return $this->notFound();
        }

        return $this->success($this->formatBankAccount($bankAccount));
    }

    public function subAccounts($id = null)
    {
        try {
            $this->can('bank-account:read');
            $this->hasPermission('BankAccount', 'can_view_all');
        } catch (UnauthorizedException $e) {
            return $this->unauthorized($e->getMessage());
        }

        $bankAccount = $this->fetchBankAccountData()
            ->where('tb_autopay_bank_account.id', $id)
            ->first();

        if (! $bankAccount) {
            return $this->notFound();
        }

        $model = model(BankSubAccountModel::class);

        $bankSubAccounts = $model
            ->select(['id', 'bank_account_id', 'sub_account', 'sub_holder_name', 'label', 'acc_type', 'active', 'va_active', 'created_at', 'updated_at'])
            ->where('bank_account_id', $id)
            ->paginate();

        return $this->success(array_map(function ($subAccount) {
            return [
                'id' => (int) $subAccount->id,
                'bank_account_id' => (int) $subAccount->bank_account_id,
                'account_number' => $subAccount->sub_account,
                'account_holder_name' => $subAccount->sub_holder_name,
                'label' => $subAccount->label ?: null,
                'acc_type' => $subAccount->acc_type,
                'active' => (bool) $subAccount->active,
                'va_active' => (bool) $subAccount->va_active,
                'created_at' => $subAccount->created_at,
                'updated_at' => $subAccount->updated_at,
            ];
        }, $bankSubAccounts), $model->pager);
    }

    protected function fetchBankAccountData(): Model
    {
        return model(BankAccountModel::class)
            ->select([
                'tb_autopay_bank_account.id',
                'tb_autopay_bank_account.label',
                'tb_autopay_bank_account.account_holder_name',
                'tb_autopay_bank_account.account_number',
                'tb_autopay_bank_account.accumulated',
                'tb_autopay_bank_account.active',
                'tb_autopay_bank_account.created_at',
                'tb_autopay_bank.short_name as bank_short_name',
                'tb_autopay_bank.brand_name as bank_brand_name',
                'tb_autopay_bank.full_name as bank_full_name',
                'tb_autopay_bank.code as bank_code',
                'tb_autopay_bank.bin as bank_bin',
                'tb_autopay_bank.icon_path as icon_path',
                'tb_autopay_bank.logo_path as logo_path',
            ])
            ->join('tb_autopay_bank', 'tb_autopay_bank.id = tb_autopay_bank_account.bank_id')
            ->where('company_id', $this->company->id);
    }

    protected function formatBankAccount(object $bankAccount): array
    {
        return [
            'id' => (int) $bankAccount->id,
            'label' => $bankAccount->label ?: null,
            'account_holder_name' => $bankAccount->account_holder_name,
            'account_number' => $bankAccount->account_number,
            'accumulated' => (float) $bankAccount->accumulated,
            'active' => (bool) $bankAccount->active,
            'created_at' => $bankAccount->created_at,
            'bank' => [
                'short_name' => $bankAccount->bank_short_name,
                'brand_name' => $bankAccount->bank_brand_name,
                'full_name' => $bankAccount->bank_full_name,
                'code' => $bankAccount->bank_code,
                'bin' => $bankAccount->bank_bin,
                'icon_url' => base_url('assets/images/banklogo/' . $bankAccount->icon_path),
                'logo_url' => base_url('assets/images/banklogo/' . $bankAccount->logo_path),
            ],
        ];
    }
}
