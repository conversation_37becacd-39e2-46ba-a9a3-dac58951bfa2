<?php

namespace App\Controllers\Api\V1;

use App\Exceptions\UnauthorizedException;
use App\Models\ConfigurationModel;

class User extends BaseController
{
    public function me()
    {
        try {
            $this->can('profile');
        } catch (UnauthorizedException $e) {
            return $this->unauthorized($e->getMessage());
        }

        helper('general');

        return $this->success([
            'id' => (int) $this->user->id,
            'first_name' => $this->user->firstname,
            'last_name' => $this->user->lastname,
            'email' => $this->user->email,
            'phone' => $this->user->phonenumber,
            'avatar' => get_gravatar($this->user->email),
        ]);
    }

    public function company()
    {
        try {
            $this->can('company');
        } catch (UnauthorizedException $e) {
            return $this->unauthorized($e->getMessage());
        }

        if (! in_array($this->company->role, ['Admin', 'SuperAdmin'])) {
            return $this->forbidden();
        }

        return $this->success([
            'id' => $this->company->id,
            'full_name' => $this->company->full_name,
            'short_name' => $this->company->short_name,
            'role' => $this->company->role,
            'status' => $this->company->status,
            'subscription' => $this->company->plan_name,
            'begin_date' => $this->company->begin_date,
            'end_date' => $this->company->end_date,
            'configurations' => $this->getCompanyConfigurations(),
        ]);
    }

    public function updateCompanyConfigurations()
    {
        try {
            $this->can('company');
        } catch (UnauthorizedException $e) {
            return $this->unauthorized($e->getMessage());
        }

        if (! in_array($this->company->role, ['Admin', 'SuperAdmin'])) {
            return $this->forbidden();
        }

        $rules = [
            'payment_code_formats' => 'if_exist|required|is_array',
            'payment_code_formats.*.prefix' => 'permit_empty|required_with[payment_code_formats.*]|alpha|min_length[2]|max_length[3]',
            'payment_code_formats.*.suffix_from' => 'permit_empty|required_with[payment_code_formats.*]|is_natural|less_than_equal_to[30]|greater_than_equal_to[1]',
            'payment_code_formats.*.suffix_to' => 'permit_empty|required_with[payment_code_formats.*]|is_natural|less_than_equal_to[30]|greater_than_equal_to[1]',
            'payment_code_formats.*.character_type' => 'permit_empty|required_with[payment_code_formats.*]|in_list[NumberOnly,NumberAndLetter]',
            'payment_code_formats.*.is_active' => 'permit_empty|required_with[payment_code_formats.*]|in_list[0,1]',
        ];

        if (! $this->validate($rules)) {
            return $this->validationError($this->validator->getErrors());
        }

        $paymentCodeFormats = $this->request->getVar('payment_code_formats');

        if (empty($paymentCodeFormats)) {
            return $this->updated($this->getCompanyConfigurations());
        }

        foreach ($paymentCodeFormats as $index => $format) {
            if ((int) $format->suffix_from > (int) $format->suffix_to) {
                return $this->validationError([
                    'payment_code_formats' => 'Giá trị "Từ" phải nhỏ hơn hoặc bằng giá trị "Đến"',
                ]);
            }
        }

        $configurationModel = model(ConfigurationModel::class);

        $existingConfig = $configurationModel
            ->where('company_id', $this->company->id)
            ->where('setting', 'PayCodeStructures')
            ->first();

        $existingFormats = [];
        if ($existingConfig) {
            $existingFormats = json_decode($existingConfig->value, true) ?: [];
        }

        $existingPrefixes = [];

        foreach ($existingFormats as $format) {
            $existingPrefixes[$format['prefix']] = true;
        }

        foreach ($paymentCodeFormats as $newFormat) {
            $formatData = [
                'prefix' => $newFormat->prefix,
                'suffix_from' => (int) $newFormat->suffix_from,
                'suffix_to' => (int) $newFormat->suffix_to,
                'character_type' => $newFormat->character_type,
                'is_active' => $newFormat->is_active ? 'on' : 'off',
            ];

            if (isset($existingPrefixes[$newFormat->prefix])) {
                foreach ($existingFormats as $key => $existingFormat) {
                    if ($existingFormat['prefix'] === $newFormat->prefix) {
                        $existingFormats[$key] = $formatData;
                        break;
                    }
                }
            } else {
                $existingFormats[] = $formatData;
            }
        }

        if (! $existingConfig) {
            $configurationModel->insert([
                'company_id' => $this->company->id,
                'setting' => 'PayCodeStructures',
                'value' => json_encode($existingFormats),
            ]);
        } else {
            $configurationModel->update(
                $existingConfig->id,
                ['value' => json_encode($existingFormats)]
            );
        }

        add_user_log([
            'data_id' => $this->company->id,
            'company_id' => $this->company->id,
            'data_type' => 'company_change_configuration',
            'description' => 'Cập nhật cấu trúc mã thanh toán',
            'user_id' => $this->user->id,
            'ip' => $this->request->getIPAddress(),
            'user_agent' => $this->request->getUserAgent()->getAgentString(),
            'status' => 'Success',
        ]);
        
        return $this->updated($this->getCompanyConfigurations(), 'Configuration updated successfully');
    }

    protected function getCompanyConfigurations(): array
    {
        $configurations = model(ConfigurationModel::class)
            ->select('setting, value')
            ->where('company_id', $this->company->id)
            ->findAll();

        $configurations = array_column($configurations, 'value', 'setting');

        $paymentCodeFormats = json_decode($configurations['PayCodeStructures'] ?? '[]', true) ?: [
            [
                'prefix' => $configurations['PayCodePrefix'] ?? 'DH',
                'suffix_from' => $configurations['PayCodeSuffixFrom'] ?? 6,
                'suffix_to' => $configurations['PayCodeSuffixTo'] ?? 8,
                'character_type' => $configurations['PayCodeSuffixCharacterType'] ?? 'NumberOnly',
                'is_active' => 'on',
            ],
        ];

        $paymentCodeFormats = array_map(function ($item) {
            return [
                'prefix' => $item['prefix'],
                'suffix_from' => (int) $item['suffix_from'],
                'suffix_to' => (int) $item['suffix_to'],
                'character_type' => $item['character_type'],
                'is_active' => isset($item['is_active']) && ($item['is_active'] === 'on' || $item['is_active'] === true),
            ];
        }, $paymentCodeFormats);

        return [
            'bank_sub_account' => $configurations['BankSubAccount'] === 'off',
            'paycode' => $configurations['PayCode'] === 'on',
            'data_storage_time' => $configurations['DataStorageTime'],
            'payment_code_formats' => $paymentCodeFormats,
        ];
    }
}
