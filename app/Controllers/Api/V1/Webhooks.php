<?php

namespace App\Controllers\Api\V1;

use App\Exceptions\UnauthorizedException;
use App\Models\BankAccountModel;
use App\Models\BankSubAccountModel;
use App\Models\WebhooksBankSubAccountModel;
use App\Models\WebhooksModel;
use InvalidArgumentException;

class Webhooks extends BaseController
{
    public function index()
    {
        try {
            $this->can('webhook:read');
            $this->hasPermission('Webhooks', 'can_view_all');
        } catch (UnauthorizedException $e) {
            return $this->unauthorized($e->getMessage());
        }

        $data = [
            'webhook_url' => $this->request->getGet('webhook_url'),
            'api_key' => $this->request->getGet('api_key'),
            'active' => $this->request->getGet('active'),
        ];

        foreach ($data as $key => $value) {
            if ($value === null) {
                unset($data[$key]);
            }
        }

        $rules = [
            'webhook_url' => 'permit_empty|if_exist|string',
            'api_key' => 'permit_empty|if_exist|string',
            'active' => 'permit_empty|if_exist|in_list[0,1]',
        ];

        if (! $this->validateData($data, $rules)) {
            return $this->validationError($this->validator->getErrors());
        }

        $model = slavable_model(WebhooksModel::class, 'Webhooks');

        $query = $model
            ->select([
                'id',
                'bank_account_id',
                'name',
                'event_type',
                'authen_type',
                'webhook_url',
                'is_verify_payment',
                'skip_if_no_code',
                'retry_conditions',
                'request_content_type',
                'api_key',
                'oauth2_client_id',
                'oauth2_client_secret',
                'oauth2_access_token_url',
                'only_va',
                'active',
                'created_at',
            ])
            ->where('company_id', $this->company->id)
            ->where('deleted_at', null);

        if (isset($data['webhook_url']) && is_string($data['webhook_url'])) {
            $query->like('webhook_url', $data['webhook_url']);
        }

        if (isset($data['api_key']) && is_string($data['api_key'])) {
            $query->where('api_key', $data['api_key']);
        }

        if (isset($data['active'])) {
            $query->where('active', $data['active']);
        }

        $webhooks = $query
            ->orderBy('id', 'desc')
            ->paginate();

        $bankSubAccounts = [];

        if ($webhooks) {
            $bankSubAccounts = model(WebhooksBankSubAccountModel::class)
                ->select('webhook_id, bank_sub_account_id')
                ->whereIn('webhook_id', array_column($webhooks, 'id'))
                ->findAll();
        }

        $webhooks = array_map(
            fn($webhook) => $this->getWebhookData($webhook, $bankSubAccounts),
            $webhooks
        );

        return $this->success($webhooks, $model->pager);
    }

    public function create()
    {
        try {
            $this->can('webhook:write');
            $this->hasPermission('Webhooks', 'can_add');
        } catch (UnauthorizedException $e) {
            return $this->unauthorized($e->getMessage());
        }

        $webhooksModel = model(WebhooksModel::class);

        $rules = [
            'bank_account_id' => ['label' => 'Tài khoản ngân hàng', 'rules' => 'required|integer|is_natural'],
            'only_va' => ['label' => 'Chỉ nhận giao dịch từ tài khoản ảo', 'rules' => 'if_exist|required|in_list[0,1]'],
            'bank_sub_account_ids' => [
                'label' => 'Tài khoản ảo',
                'rules' => 'if_exist|required_with[only_va]|valid_bank_sub_account_ids[]',
                'errors' => [
                    'required_with' => 'Hãy chọn tài khoản ảo nếu bạn chọn "Chỉ nhận giao dịch từ tài khoản ảo (VA) của tài khoản ngân hàng này"',
                ],
            ],
            'event_type' => ['label' => 'Bắn WebHooks khi', 'rules' => 'required|in_list[All,In_only,Out_only]'],
            'authen_type' => ['label' => 'Kiểu chứng thực', 'rules' => 'required|in_list[No_Authen,OAuth2.0,Api_Key]'],
            'webhook_url' => ['label' => 'Gọi đến URL', 'rules' => array_filter(['required', 'valid_url_strict', ! is_admin() ? 'valid_webhooks_url' : null])],
            'name' => ['label' => 'Đặt tên', 'rules' => 'required|max_length[250]'],
            'is_verify_payment' => 'required|in_list[0,1]',
            'skip_if_no_code' => 'if_exist|in_list[0,1]',
            'active' => 'if_exist|in_list[0,1]',
            'retry_conditions' => 'if_exist|in_list[' . implode(',', array_keys($webhooksModel->defaultRetryConditions)) . ']',
        ];

        $data = (array) $this->request->getVar();

        $authType = $this->request->getVar('authen_type');

        switch ($authType) {
            case 'OAuth2.0':
                $rules['oauth2_access_token_url'] = ['label' => 'OAuth 2.0 Access Token URL', 'rules' => 'required|max_length[1000]'];
                $rules['oauth2_client_id'] = ['label' => 'OAuth 2.0 Client IDs', 'rules' => 'required|max_length[200]'];
                $rules['oauth2_client_secret'] = ['label' => 'OAuth 2.0 Client Secret', 'rules' => 'required|max_length[200]'];
                break;
            case 'Api_Key':
                $rules['api_key'] = ['label' => 'API Key', 'rules' => 'required|max_length[1000]'];
                $rules['request_content_type'] = ['label' => 'Request Content type', 'rules' => 'required|in_list[Json,multipart_form-data]'];
                break;
            default:
                $rules['request_content_type'] = ['label' => 'Request Content type', 'rules' => 'required|in_list[Json,multipart_form-data]'];
                break;
        }

        if (! $this->validateData($data, $rules)) {
            return $this->validationError($this->validator->getErrors());
        }

        $bankAccountId = $this->request->getVar('bank_account_id');
        $bankSubAccountIds = $this->request->getVar('bank_sub_account_ids');

        try {
            $this->validateBankAccount($bankAccountId, $this->request->getVar('only_va') ? $bankSubAccountIds : null);
        } catch (InvalidArgumentException $e) {
            return $this->validationError(['bank_account_id' => $e->getMessage()]);
        }

        $retryConditions = $this->processRetryConditions(
            $this->request->getVar('retry_conditions') ?: [],
            $webhooksModel->defaultRetryConditions
        );

        $data = [
            'company_id' => $this->company->id,
            'bank_account_id' => $bankAccountId,
            'event_type' => $this->request->getVar('event_type'),
            'authen_type' => $authType,
            'webhook_url' => trim($this->request->getVar('webhook_url')),
            'name' => trim(xss_clean($this->request->getVar('name'))),
            'is_verify_payment' => $this->request->getVar('is_verify_payment') ?: false,
            'skip_if_no_code' => $this->request->getVar('skip_if_no_code') ?: false,
            'active' => $this->request->getVar('active') ?: true,
            'retry_conditions' => $retryConditions,
            'only_va' => $this->request->getVar('only_va') ?: false,
        ];

        switch ($authType) {
            case 'OAuth2.0':
                $data['oauth2_access_token_url'] = trim($this->request->getVar('oauth2_access_token_url'));
                $data['oauth2_client_id'] = trim($this->request->getVar('oauth2_client_id'));
                $data['oauth2_client_secret'] = trim($this->request->getVar('oauth2_client_secret'));
                break;
            case 'Api_Key':
                $data['api_key'] = trim(xss_clean($this->request->getVar('api_key')));
                $data['request_content_type'] = trim($this->request->getVar('request_content_type'));
                break;
            case 'No_Authen':
                $data['request_content_type'] = trim($this->request->getVar('request_content_type'));
                break;
        }

        $insertedId = $webhooksModel->insert($data);

        if (! $insertedId) {
            add_user_log([
                'data_id' => 0,
                'company_id' => $this->company->id,
                'data_type' => 'webhook_add',
                'description' => 'Thêm WebHooks',
                'user_id' => $this->user->id,
                'ip' => $this->request->getIPAddress(),
                'user_agent' => $this->request->getUserAgent()->getAgentString(),
                'status' => 'Failed',
            ]);
            
            return $this->error('Failed to create webhook');
        }

        $webhooksModel->syncBankSubAccounts(
            $insertedId,
            $this->request->getVar('bank_sub_account_ids') ?? []
        );

        add_user_log([
            'data_id' => $insertedId,
            'company_id' => $this->company->id,
            'data_type' => 'webhook_add',
            'description' => 'Thêm WebHooks',
            'user_id' => $this->user->id,
            'ip' => $this->request->getIPAddress(),
            'user_agent' => $this->request->getUserAgent()->getAgentString(),
            'status' => 'Success',
        ]);
        
        return $this->created(['id' => $insertedId], 'Webhook created successfully');
    }

    public function show($id = null)
    {
        try {
            $this->can('webhook:read');
            $this->hasPermission('Webhooks', 'can_view_all');
        } catch (UnauthorizedException $e) {
            return $this->unauthorized($e->getMessage());
        }

        if (! $id) {
            return $this->notFound();
        }

        $webhooksModel = model(WebhooksModel::class);
        $webhook = $webhooksModel
            ->where('id', $id)
            ->where('company_id', $this->company->id)
            ->first();

        if (! $webhook) {
            return $this->notFound();
        }

        $bankSubAccounts = model(WebhooksBankSubAccountModel::class)
            ->select('webhook_id, bank_sub_account_id')
            ->where('webhook_id', $id)
            ->findAll();

        $data = $this->getWebhookData($webhook, $bankSubAccounts);

        return $this->success($data);
    }

    public function update($id = null)
    {
        try {
            $this->can('webhook:write');
            $this->hasPermission('Webhooks', 'can_edit');
        } catch (UnauthorizedException $e) {
            return $this->unauthorized($e->getMessage());
        }

        if (! $id) {
            return $this->notFound();
        }

        $webhooksModel = model(WebhooksModel::class);

        $webhook = $webhooksModel
            ->where('id', $id)
            ->where('company_id', $this->company->id)
            ->first();

        if (! $webhook) {
            return $this->notFound();
        }

        $rules = [
            'bank_account_id' => ['label' => 'Tài khoản ngân hàng', 'rules' => 'if_exist|integer|is_natural'],
            'only_va' => ['label' => 'Chỉ nhận giao dịch từ tài khoản ảo', 'rules' => 'if_exist|in_list[0,1]'],
            'bank_sub_account_ids' => [
                'label' => 'Tài khoản ảo',
                'rules' => 'if_exist|required_with[only_va,1]|valid_bank_sub_account_ids[]',
            ],
            'event_type' => ['label' => 'Bắn WebHooks khi', 'rules' => 'if_exist|in_list[All,In_only,Out_only]'],
            'authen_type' => ['label' => 'Kiểu chứng thực', 'rules' => 'if_exist|in_list[No_Authen,OAuth2.0,Api_Key]'],
            'webhook_url' => ['label' => 'Gọi đến URL', 'rules' => array_filter(['if_exist', 'valid_url_strict', ! is_admin() ? 'valid_webhooks_url' : null])],
            'name' => ['label' => 'Tên WebHook', 'rules' => 'if_exist|max_length[250]'],
            'is_verify_payment' => 'if_exist|in_list[0,1]',
            'skip_if_no_code' => 'if_exist|in_list[0,1]',
            'active' => 'if_exist|in_list[0,1]',
            'retry_conditions' => 'if_exist',
        ];

        $authType = $this->request->getVar('authen_type');

        switch ($authType) {
            case 'OAuth2.0':
                $rules['oauth2_access_token_url'] = ['label' => 'OAuth 2.0 Access Token URL', 'rules' => 'if_exist|max_length[1000]'];
                $rules['oauth2_client_id'] = ['label' => 'OAuth 2.0 Client ID', 'rules' => 'if_exist|max_length[200]'];
                $rules['oauth2_client_secret'] = ['label' => 'OAuth 2.0 Client Secret', 'rules' => 'if_exist|max_length[200]'];
                break;
            case 'Api_Key':
                $rules['api_key'] = ['label' => 'API Key', 'rules' => 'if_exist|max_length[1000]'];
                $rules['request_content_type'] = ['label' => 'Request Content Type', 'rules' => 'if_exist|in_list[Json,multipart_form-data]'];
                break;
            case 'No_Authen':
                $rules['request_content_type'] = ['label' => 'Request Content Type', 'rules' => 'if_exist|in_list[Json,multipart_form-data]'];
                break;
        }

        if (! $this->validate($rules)) {
            return $this->validationError($this->validator->getErrors());
        }

        $data = [];

        foreach ($rules as $key => $rule) {
            if ($this->request->getVar($key) !== null) {
                $data[$key] = $this->request->getVar($key);
            }
        }

        if (isset($data['bank_account_id']) || isset($data['bank_sub_account_ids'])) {
            try {
                $this->validateBankAccount(
                    $data['bank_account_id'] ?? $webhook->bank_account_id,
                        $data['bank_sub_account_ids'] ?? null
                );
            } catch (InvalidArgumentException $e) {
                return $this->validationError(['bank_account_id' => $e->getMessage()]);
            }
        }

        if (isset($data['retry_conditions'])) {
            $data['retry_conditions'] = $this->processRetryConditions(
                $data['retry_conditions'],
                $webhooksModel->defaultRetryConditions
            );
        }

        if (empty($data)) {
            return $this->error('No data to update');
        }

        if (! $webhooksModel->update($id, $data)) {
            return $this->error('Failed to update webhook');
        }

        if (isset($data['bank_sub_account_ids']) && $data['only_va']) {
            $webhooksModel->syncBankSubAccounts($id, $data['bank_sub_account_ids']);
        }

        add_user_log([
            'data_id' => $id,
            'company_id' => $this->company->id,
            'data_type' => 'webhook_update',
            'description' => 'Cập nhật WebHooks thành công',
            'user_id' => $this->user->id,
            'ip' => $this->request->getIPAddress(),
            'user_agent' => $this->request->getUserAgent()->getAgentString(),
            'status' => 'Success',
        ]);
        
        return $this->updated($data, 'Webhook updated successfully');
    }

    public function delete($id = null)
    {
        try {
            $this->can('webhook:delete');
            $this->hasPermission('Webhooks', 'id');
        } catch (UnauthorizedException $e) {
            return $this->unauthorized($e->getMessage());
        }

        if (! $id) {
            return $this->notFound();
        }

        $webhooksModel = model(WebhooksModel::class);
        $webhooksBankSubAccountModel = model(WebhooksBankSubAccountModel::class);

        $webhook = $webhooksModel
            ->where('id', $id)
            ->where('company_id', $this->company->id)
            ->first();

        if (! $webhook) {
            return $this->notFound();
        }

        $webhooksModel->delete($id);
        $webhooksBankSubAccountModel->where('webhook_id', $id)->delete();

        add_user_log([
            'data_id' => $id,
            'company_id' => $this->company->id,
            'data_type' => 'webhook_delete',
            'description' => 'Xóa WebHooks',
            'user_id' => $this->user->id,
            'ip' => $this->request->getIPAddress(),
            'user_agent' => $this->request->getUserAgent()->getAgentString(),
            'status' => 'Success',
        ]);

        return $this->deleted(null, 'Webhook deleted successfully');
    }

    private function validateBankAccount($bankAccountId, $bankSubAccountIds = null)
    {
        $bankAccount = model(BankAccountModel::class)
            ->where('company_id', $this->company->id)
            ->find($bankAccountId);

        if (! $bankAccount) {
            throw new InvalidArgumentException('Không tìm thấy tài khoản ngân hàng.');
        }

        if ($bankSubAccountIds) {
            $bankSubAccounts = model(BankSubAccountModel::class)
                ->select('id')
                ->whereIn('id', $bankSubAccountIds)
                ->where('bank_account_id', $bankAccountId)
                ->findAll();

            $validIds = array_column($bankSubAccounts, 'id');
            $invalidIds = array_diff($bankSubAccountIds, $validIds);

            if (!empty($invalidIds)) {
                throw new InvalidArgumentException(
                    implode(', ', $invalidIds) . ' không phải là tài khoản ảo hợp lệ.'
                );
            }
        }
    }

    protected function processRetryConditions($incomingConditions, $defaultConditions)
    {
        foreach ($defaultConditions as $condition => $value) {
            $defaultConditions[$condition] = in_array($condition, $incomingConditions) ? 1 : 0;
        }

        return json_encode($defaultConditions);
    }

    protected function getWebhookData($webhook, $bankSubAccounts = []): array
    {
        $data = [
            'id' => (int) $webhook->id,
            'bank_account_id' => (int) $webhook->bank_account_id,
            'name' => $webhook->name,
            'event_type' => $webhook->event_type,
            'authen_type' => $webhook->authen_type,
            'webhook_url' => $webhook->webhook_url,
            'is_verify_payment' => (bool) $webhook->is_verify_payment,
            'skip_if_no_code' => (bool) $webhook->skip_if_no_code,
            'retry_conditions' => json_decode($webhook->retry_conditions, true),
            'only_va' => (bool) $webhook->only_va,
            'active' => (bool) $webhook->active,
            'created_at' => $webhook->created_at,
        ];

        switch ($webhook->authen_type) {
            case 'OAuth2.0':
                $data['oauth2_client_id'] = $webhook->oauth2_client_id;
                $data['oauth2_client_secret'] = $webhook->oauth2_client_secret;
                $data['oauth2_access_token_url'] = $webhook->oauth2_access_token_url;
                break;
            case 'Api_Key':
                $data['api_key'] = $webhook->api_key;
                $data['request_content_type'] = $webhook->request_content_type;
                break;
            case 'No_Authen':
                $data['request_content_type'] = $webhook->request_content_type;
                break;
        }

        if ($webhook->only_va && $bankSubAccounts) {
            $webhookBankSubAccounts = array_filter(
                $bankSubAccounts,
                fn($bankSubAccount) => $bankSubAccount->webhook_id === $webhook->id
            );

            $data['bank_sub_account_ids'] = array_values(
                array_map(
                    fn($bankSubAccount) => (int) $bankSubAccount->bank_sub_account_id,
                    $webhookBankSubAccounts
                )
            );
        }

        return $data;
    }
}
