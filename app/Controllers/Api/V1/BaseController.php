<?php

namespace App\Controllers\Api\V1;

use App\Exceptions\UnauthorizedException;
use App\Libraries\OAuthServer\Repositories\ClientRepository;
use App\Models\CompanyUserModel;
use App\Models\OAuthTokenModel;
use App\Models\UserModel;
use App\Models\UserPermissionFeatureModel;
use CodeIgniter\Exceptions\PageNotFoundException;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\RequestTrait;
use CodeIgniter\HTTP\ResponseInterface;
use CodeIgniter\Pager\Pager;
use CodeIgniter\RESTful\ResourceController;
use Config\Services;
use Psr\Log\LoggerInterface;

class BaseController extends ResourceController
{
    use RequestTrait;

    protected $helpers = ['general'];

    protected $user = null;

    protected $company = null;

    protected $client = null;

    protected $accessToken = null;

    protected array $oauthAttributes = [];

    protected ClientRepository $clientRepository;

    public function __construct()
    {
        $this->clientRepository = new ClientRepository();
    }

    public function initController(RequestInterface $request, ResponseInterface $response, LoggerInterface $logger)
    {
        parent::initController($request, $response, $logger);

        $path = ltrim($this->request->uri->getPath(), '/');
        $routes = Services::routes()->getRoutes();
        $found = false;

        foreach ($routes as $pattern => $handler) {
            $pattern = '#^' . str_replace(['/', '(:any)', '(:num)', '(:alpha)'], ['\\/', '[^/]+', '[0-9]+', '[a-zA-Z]+'], $pattern) . '$#';
            if (preg_match($pattern, $path)) {
                $found = true;
                break;
            }
        }

        if (! $found) {
            throw PageNotFoundException::forPageNotFound();
        }

        $this->oauthAttributes = $this->request->oauthAttributes;

        $this->authenticate();
    }

    public function authenticate()
    {
        if (! is_null($this->user)) {
            return;
        }

        $client = $this->clientRepository->findActive($this->oauthAttributes['oauth_client_id']);

        if (! $client) {
            return;
        }

        $user = model(UserModel::class)
            ->where('id', $this->oauthAttributes['oauth_user_id'])
            ->where('active', true)
            ->first();

        if (! $user) {
            return;
        }

        $company = model(CompanyUserModel::class)
            ->select([
                'tb_autopay_company_user.user_id',
                'tb_autopay_company_user.company_id',
                'tb_autopay_company_user.role',
                'tb_autopay_company.full_name',
                'tb_autopay_company.short_name',
                'tb_autopay_company.id',
                'tb_autopay_company.created_at',
                'tb_autopay_company.status',
                'tb_autopay_company_subscription.plan_id',
                'tb_autopay_company_subscription.begin_date',
                'tb_autopay_company_subscription.end_date',
                'tb_autopay_product.name as plan_name',
            ])
            ->join('tb_autopay_company', 'tb_autopay_company.id = tb_autopay_company_user.company_id')
            ->join('tb_autopay_company_subscription', 'tb_autopay_company_subscription.company_id = tb_autopay_company.id', 'left')
            ->join('tb_autopay_product', 'tb_autopay_product.id = tb_autopay_company_subscription.plan_id', 'left')
            ->where('tb_autopay_company.active', true)
            ->where('tb_autopay_company_user.user_id', $user->id)
            ->first();

        if (! $company) {
            return null;
        }

        $token = model(OAuthTokenModel::class)
            ->where('id', $this->oauthAttributes['oauth_access_token_id'])
            ->where('revoked', false)
            ->where('expires_at >', date('Y-m-d H:i:s'))
            ->first();

        $this->user = $user;
        $this->client = $client;
        $this->company = $company;
        $this->accessToken = $token;
    }

    public function can(...$scopes): void
    {
        if (! $this->accessToken) {
            throw new UnauthorizedException('You do not have the required permissions to access this resource.');
        }

        $availableScopes = json_decode($this->accessToken->scopes, true);

        foreach ($scopes as $scope) {
            if (! in_array($scope, $availableScopes)) {
                throw new UnauthorizedException('You do not have the required permissions to access this resource.');
            }
        }
    }

    public function hasPermission(string $permission, string $can)
    {
        $companyUserModel = model(CompanyUserModel::class);

        $companyUser = $companyUserModel
            ->select('role')
            ->where('user_id', $this->user->id)
            ->where('company_id', $this->company->id)
            ->first();

        if (! $companyUser) {
            throw new UnauthorizedException('You do not have the required permissions to access this resource.');
        }

        if (in_array($companyUser->role, ['SuperAdmin', 'Admin'])) {
            return;
        }

        $userPermissionFeatures = model(UserPermissionFeatureModel::class)
            ->where('user_id', $this->user->id)
            ->where('company_id', $this->company->id)
            ->where('feature_slug', $permission)
            ->where($can, true)
            ->countAllResults();

        if ($userPermissionFeatures === 0) {
            throw new UnauthorizedException('You do not have the required permissions to access this resource.');
        }
    }

    protected function success($data = [], ?Pager $pager = null, int $statusCode = 200, ?string $message = null)
    {
        $response = [
            'status' => 'success',
        ];

        if ($message) {
            $response['message'] = $message;
        }

        if ($data !== null) {
            $response['data'] = $data;
        }

        if ($pager) {
            $response['meta'] = [
                'pagination' => [
                    'total' => $pager->getTotal(),
                    'per_page' => $pager->getPerPage(),
                    'current_page' => $pager->getCurrentPage(),
                    'last_page' => $pager->getPageCount(),
                ]
            ];
        }

        return $this->response
            ->setStatusCode($statusCode)
            ->setJSON($response);
    }

    protected function created($data = null, ?string $message = null)
    {
        return $this->success($data, null, 201, $message);
    }

    protected function updated($data = null, ?string $message = null)
    {
        return $this->success($data, null, 200, $message);
    }

    protected function deleted($data = null, ?string $message = null)
    {
        return $this->success($data, null, 204, $message);
    }

    protected function error(string $message, int $statusCode = 400, ?string $errorCode = null)
    {
        $response = [
            'status' => 'error',
            'message' => $message
        ];

        if ($errorCode) {
            $response['error_code'] = $errorCode;
        }

        return $this->response
            ->setStatusCode($statusCode)
            ->setJSON($response);
    }

    protected function validationError(array $errors, ?string $message = null)
    {
        $response = [
            'status' => 'error',
            'message' => $message ?? 'The provided data failed validation',
            'errors' => $errors
        ];

        return $this->response
            ->setStatusCode(422)
            ->setJSON($response);
    }

    protected function unauthorized(?string $message = null)
    {
        $response = $this->error(
            $message ?? 'You do not have the required permissions to access this resource',
            401,
            'unauthorized'
        );

        return $response->setHeader('WWW-Authenticate', 'Bearer');
    }

    protected function forbidden(?string $message = null)
    {
        return $this->error(
            $message ?? 'You do not have permission to access this resource',
            403,
            'forbidden'
        );
    }

    protected function notFound(?string $message = null)
    {
        return $this->error(
            $message ?? 'The requested resource was not found',
            404,
            'not_found'
        );
    }
}
