<?php

namespace App\Controllers;
use App\Models\BankAccountModel;
use App\Models\UserModel;
use App\Models\CompanyModel;
use App\Models\CompanyUserModel;
use App\Models\PersonalAccessTokenModel;
use App\Models\UserPermissionFeatureModel;

use CodeIgniter\Controller;

class User extends BaseController
{
    
    public function my_profile()
    { 
         
        $data = [
            'page_title' => 'Tài khoản',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];
  
        $userModel = model(UserModel::class);
        $companyUserModel = model(CompanyUserModel::class);

        $data['user'] = $userModel->select('firstname, lastname, email,username,created_at,active')->where(['id' => $this->user_details->id, 'active' => 1])->get()->getRow();

        $data['our_companies'] = $companyUserModel->select('tb_autopay_company.full_name,tb_autopay_company.user_owner_id,tb_autopay_company.short_name')->join("tb_autopay_company","tb_autopay_company.id=tb_autopay_company_user.company_id","inner")->where(['tb_autopay_company_user.user_id' => $this->user_details->id])->get()->getResult(); 
     
        if(!is_object($data['user']))
            redirect()->to(site_url('login/logout'));

        echo theme_view('templates/autopay/header',$data);
        echo view('user/my_profile',$data);
        echo theme_view('templates/autopay/footer',$data);

    }

    public function ajax_account_permission_update() {
        if ($this->request->getMethod(true) != 'POST')
            return '';


        if(!in_array($this->company_details->role,['Admin','SuperAdmin']))
            return redirect()->to(base_url());
 
        $validation =  \Config\Services::validation();

        helper('text');
 
        if(! $this->validate([
            'assign_permission' => "required|in_list[1]",
            'user_id' => "required|integer|is_natural",
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        } 

        $user_id = $this->request->getPost('user_id');

        $companyUserModel = model(CompanyUserModel::class);

        $result = $companyUserModel->where(['user_id' => $user_id,'company_id' => $this->user_session['company_id']])->get()->getRow();

        if(!is_object($result))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>'Người dùng này không tồn tại'));

        if(in_array($result->role,['Admin','SuperAdmin']))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>'Bạn không cần phân quyền cho quản trị viên, bởi quản trị viên được toàn quyền'));

        $bankAccountModel = model(BankAccountModel::class);      
        $results = $bankAccountModel->get_bank_account_company($this->user_session['company_id']);
        $company_bank_accounts = [];
        foreach($results as $result) {
            array_push($company_bank_accounts, $result->id);
        }
     
       

        $userPermissionBankModel = model(UserPermissionBankModel::class);

        $permissions_subcheck = [];

        $bank_accounts_checked = $this->request->getPost('pacc');

        if(!is_array($bank_accounts_checked)) {
            $userPermissionBankModel->where(['user_id' => $user_id])->delete();
            return $this->response->setJSON(array('status'=>TRUE,'message'=>'Đã cập nhật'));
        }
             
        foreach($bank_accounts_checked as $bank_account_id) {
            if(in_array($bank_account_id, $company_bank_accounts)) {
                $permissions_subcheck[$bank_account_id]['hide_amount_out'] = 0;
                $permissions_subcheck[$bank_account_id]['hide_accumulated'] = 0;
                $permissions_subcheck[$bank_account_id]['hide_reference_number'] = 0;
                $permissions_subcheck[$bank_account_id]['hide_transaction_content'] = 0;
            }
          
        }

        
        foreach($this->request->getPost() as $key => $column) {

            if($key == 'user_id' || $key == 'assign_permission')
                continue;
 
            if(is_array($column) && $key != 'pacc') {
                foreach($column as $bank_account_id) {
                    if(in_array($bank_account_id, $company_bank_accounts)) { 
                        if(in_array($bank_account_id, $bank_accounts_checked))
                            $permissions_subcheck[$bank_account_id][$key] = 1;
                    }
                   
                } 
            }
            
        } 

        $userPermissionBankModel->where(['user_id' => $user_id])->delete();

        foreach($permissions_subcheck as $bank_account_id => $data_update) {
            $data_update['user_id'] = $user_id;
            $data_update['bank_account_id'] = $bank_account_id;

            $userPermissionBankModel->insert($data_update);
        }

        return $this->response->setJSON(array("status"=>true));
        
    }

    public function ajax_feature_permission_update() {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!in_array($this->company_details->role,['Admin','SuperAdmin']))
            return redirect()->to(base_url());
 
        $validation =  \Config\Services::validation();

        helper('text');
 
        if(! $this->validate([
            'assign_permission_feature' => "required|in_list[1]",
            'user_id' => "required|integer|is_natural",
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        } 

        $user_id = $this->request->getPost('user_id');

        $companyUserModel = model(CompanyUserModel::class);

        $result = $companyUserModel->where(['user_id' => $user_id,'company_id' => $this->user_session['company_id']])->get()->getRow();

        if(!is_object($result))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>'Người dùng này không tồn tại'));

        if(in_array($result->role,['Admin','SuperAdmin']))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>'Bạn không cần phân quyền cho quản trị viên, bởi quản trị viên được toàn quyền'));

        $userPermissionFeatureModel = model(UserPermissionFeatureModel::class);      
      
        // reset permission
        $userPermissionFeatureModel->set(['can_view_all'=>0,'can_add'=>0,'can_edit'=>0,'can_delete'=>0])->where(['user_id' => $user_id, 'company_id' => $this->user_session['company_id']])->update();
 
        $permissions_subcheck = [];
        foreach($this->request->getPost() as $key => $column) {

            if($key == 'user_id' || $key == 'assign_permission_feature')
                continue;
 
                
            if(is_array($column) && in_array($key, ['can_view_all','can_add','can_edit','can_delete'])) {
                foreach($column as $feature_slug) {

                    $userPermissionFeatureModel->set([$key => 1])->where(['user_id'=>$user_id, 'company_id' => $this->user_session['company_id'],'feature_slug' => $feature_slug])->update();

                } 
            } 
            
        } 
      
        return $this->response->setJSON(array("status"=>true));
        
    }

    public function change_password() {
        helper(['form','general','url']);

        $data = [
            'page_title' => 'Đổi mật khẩu',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];
  
        $userModel = model(UserModel::class);
        $companyUserModel = model(CompanyUserModel::class);

        $user_details = $userModel->select('firstname, password, lastname, email,created_at,active')->where(['id' => $this->user_details->id, 'active' => 1])->get()->getRow();

        $data['our_companies'] = $companyUserModel->select('tb_autopay_company.full_name,tb_autopay_company.user_owner_id,tb_autopay_company.short_name')->join("tb_autopay_company","tb_autopay_company.id=tb_autopay_company_user.company_id","inner")->where(['tb_autopay_company_user.user_id' => $this->user_details->id])->get()->getResult(); 
     
        if(!is_object($user_details))
            redirect()->to(site_url('login/logout'));

        $data['validation_errors'] = [];

        if ($this->request->getMethod(true) == 'POST') {

            $old_password = $this->request->getPost('old_password');
            $new_password = $this->request->getPost('new_password');

            if($this->user_session['company_id'] == 5) {
                set_alert('error','Tính năng này không khả dụng ở tài khoản Demo');
                return redirect()->back();
            }

            $validation =  \Config\Services::validation();
    
            if(! $this->validate([
                'old_password' =>['label' => 'Mật khẩu cũ', 'rules' => "required|min_length[6]"],
                'new_password' => ['label' => 'Mật khẩu mới', 'rules' =>  "required|min_length[6]|max_length[24]|valid_password"],
                'new_password_conf' => ['label' => 'Nhập lại mật khẩu mới', 'rules' => 'required|matches[new_password]'],
            ])) {
                $data['validation_errors'] = $validation->getErrors();
            } else if(!password_verify($old_password,$user_details->password)) {
                $data['validation_errors'] = ["Mật khẩu cũ không đúng"];
               
            }

            if($data['validation_errors']) {
                echo view('templates/autopay/header',$data);
                echo view('user/change_password',$data);
                echo view('templates/autopay/footer',$data);
            } else {
                $lastPasswordChanged = date('Y-m-d H:i:s');
                $userModel->set(['password' => password_hash(trim($this->request->getVar('new_password')), PASSWORD_DEFAULT), 'last_password_changed' => $lastPasswordChanged])->where('id',$this->user_details->id)->update();

                $session = session();
                $session_data = $session->get('user_logged_in');
                $session_data['last_password_changed'] = $lastPasswordChanged;
                $session->set('user_logged_in', $session_data);

                try {
                    $userExists = model(UserModel::class)->where('id', $this->user_details->id)->countAllResults();
                    
                    if ($userExists > 0) {
                        model(PersonalAccessTokenModel::class)
                            ->where('user_id', $this->user_details->id)
                            ->where('company_id', $this->user_session['company_id'])
                            ->delete();
                    }
                } catch (\Exception $e) {
                    log_message('error', '[RevokePersonalAccessToken] ' . $e->getMessage() . ' - ' . $e->getTraceAsString());
                }

                add_user_log(array('data_id'=>$this->user_details->id,'company_id' => $this->user_session['company_id'], 'data_type'=>'user_change_password','description'=>'Đổi mật khẩu','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
                
                echo theme_view('templates/autopay/header',$data);
                echo view('user/change_password_success',$data);
                echo theme_view('templates/autopay/footer',$data);
            } 
        } else {
            echo theme_view('templates/autopay/header',$data);
            echo view('user/change_password',$data);
            echo theme_view('templates/autopay/footer',$data);
        }

    }

    public function ajax_get_my_user() {
      
     
        $userModel = model(UserModel::class);
        $user_info = $userModel->select('firstname, lastname')->where(['id' => $this->user_details->id, 'active' => 1])->get()->getRow();

        if(!is_object($user_info))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Không tim thấy thông tin"));
        
         
        return $this->response->setJSON(["status"=>TRUE, "data"=>$user_info]);
         
    }

    public function ajax_my_user_update() {
        if ($this->request->getMethod(true) != 'POST')
            return '';


        $userModel = model(UserModel::class);
        $user_info = $userModel->select('firstname, lastname')->where(['id' => $this->user_details->id, 'active' => 1])->get()->getRow();

        if(!is_object($user_info))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Không tim thấy thông tin người dùng"));
        
        $validation =  \Config\Services::validation();

        if(! $this->validate([
            'firstname' => "required|max_length[100]",
            'lastname' => "required|max_length[200]",
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }

        $userModel->set(['firstname' => xss_clean($this->request->getPost('firstname')),'lastname' => xss_clean($this->request->getPost('lastname'))])->where('id', $this->user_details->id)->update();

        add_user_log(array('data_id'=>$this->user_details->id,'company_id' => $this->user_session['company_id'], 'data_type'=>'user_change_info','description'=>'Đổi thông tin','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
        set_alert('success','Sửa thông tin thành công');

        return $this->response->setJSON(["status"=>TRUE]);

    }

    public function update_theme() {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        $userModel = model(UserModel::class);
        $user_info = $userModel->select('firstname, lastname')->where(['id' => $this->user_details->id, 'active' => 1])->get()->getRow();

        if(!is_object($user_info))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Không tim thấy thông tin người dùng"));
        
        $validation =  \Config\Services::validation();

        if(! $this->validate([
            'theme' => "required|in_list[dark,light,auto]",
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }

        $new_theme = $this->request->getVar('theme');

        $userModel->set(['theme' => $new_theme])->where('id', $this->user_details->id)->update();

        return $this->response->setJSON(["status"=>TRUE]);

    }

    public function sidebar_toggle() {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        $userModel = model(UserModel::class);
        $user_info = $userModel->select('firstname, lastname, sidebar_toggle')->where(['id' => $this->user_details->id, 'active' => 1])->get()->getRow();

        if(!is_object($user_info))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Không tim thấy thông tin người dùng"));
        
        if($user_info->sidebar_toggle == 1)
            $sidebar_toggle = 0;
        else
            $sidebar_toggle = 1;

        $userModel->set(['sidebar_toggle' => $sidebar_toggle])->where('id', $this->user_details->id)->update();

        return $this->response->setJSON(["status"=>TRUE]);

    }

    public function sidebar_behavior() {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        $userModel = model(UserModel::class);
        $user_info = $userModel->select('firstname, lastname')->where(['id' => $this->user_details->id, 'active' => 1])->get()->getRow();

        if(!is_object($user_info))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Không tim thấy thông tin người dùng"));
        
        $validation =  \Config\Services::validation();

        if(! $this->validate([
            'sidebar_behavior' => "required|in_list[compact,fixed,sticky]",
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }

        $sidebar_behavior = $this->request->getVar('sidebar_behavior');

        $userModel->set(['sidebar_behavior' => $sidebar_behavior])->where('id', $this->user_details->id)->update();

        return $this->response->setJSON(["status"=>TRUE]);

    }
 
    
}