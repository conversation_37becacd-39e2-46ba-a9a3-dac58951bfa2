<?php

namespace App\Controllers\Woo;

use CodeIgniter\Controller;
use Config\Services;
use Config\Wc;
use Exception;

class Oauth extends Controller
{
    protected Wc $wcConfig;

    protected $helpers = ['general'];

    public function __construct()
    {
        $this->wcConfig = config(Wc::class);
    }

    public function init()
    {
        helper('general');

        if ($this->request->getMethod(true) !== 'POST') {
            show_404();
        }

        $data = [
            'redirect_uri' => $this->request->getVar('redirect_uri'),
            'state' => $this->request->getVar('state'),
        ];

        if (! $this->validateData($data, ['redirect_uri' => 'required|valid_url'])) {
            return $this->response->setJSON([
                'error' => $this->validator->getErrors(),
            ]);
        }

        Services::cache()->save("wc_oauth_{$data['state']}", $data['redirect_uri']);

        $queryString = http_build_query([
            'response_type' => 'code',
            'client_id' => $this->wcConfig->clientId,
            'state' => $data['state'],
            'scope' => 'profile company bank-account:read webhook:read webhook:write webhook:delete',
            'redirect_uri' => base_url('woo/oauth/callback'),
        ]);

        return $this->response->setJSON([
            'oauth_url' => base_url('oauth/authorize?' . $queryString),
        ]);
    }

    public function callback()
    {
        if ($this->request->getMethod(true) !== 'GET') {
            show_404();
        }

        $code = esc($this->request->getGet('code'));
        $state = esc($this->request->getGet('state'));

        try {
            $redirectUri = Services::cache()->get("wc_oauth_$state");
        } catch (Exception $e) {
            return $this->handleError('Invalid state');
        }

        if (empty($redirectUri)) {
            return $this->handleError('Invalid redirect URI');
        }

        if (empty($code)) {
            return redirect()->to($redirectUri);
        }

        try {
            $response = Services::curlrequest()->post(base_url('oauth/token'), [
                'form_params' => [
                    'grant_type' => 'authorization_code',
                    'code' => $code,
                    'client_id' => $this->wcConfig->clientId,
                    'client_secret' => $this->wcConfig->clientSecret,
                    'redirect_uri' => base_url('woo/oauth/callback'),
                ],
            ]);

            $result = json_decode($response->getBody(), true);

            log_message('error', "Woo issue access token: " . json_encode($result));

            if ($response->getStatusCode() !== 200) {
                throw new Exception($result['error']);
            }

            if (empty($result['access_token']) || empty($result['refresh_token'])) {
                throw new Exception('Invalid token response');
            }

            Services::cache()->delete("wc_oauth_{$state}");

            $params = http_build_query([
                'access_token' => $result['access_token'],
                'refresh_token' => $result['refresh_token'],
                'expires_in' => $result['expires_in'] ?? 3600,
                'state' => $state,
            ]);

            if (strpos($redirectUri, '?') !== false) {
                $redirectUri .= '&' . $params;
            } else {
                $redirectUri .= '?' . $params;
            }

            return redirect()->to($redirectUri);
        } catch (Exception $e) {
            return $this->handleError($e->getMessage());
        }
    }

    public function refresh()
    {
        if ($this->request->getMethod(true) !== 'POST') {
            show_404();
        }

        $data = [
            'refresh_token' => $this->request->getVar('refresh_token'),
        ];

        if (! $this->validateData($data, ['refresh_token' => 'required'])) {
            return $this->response->setJSON([
                'error' => $this->validator->getErrors(),
            ]);
        }

        try {
            $response = Services::curlrequest()->post(base_url('oauth/token'), [
                'form_params' => [
                    'grant_type' => 'refresh_token',
                    'refresh_token' => $data['refresh_token'],
                    'client_id' => $this->wcConfig->clientId,
                    'client_secret' => $this->wcConfig->clientSecret,
                ],
            ]);

            $result = json_decode($response->getBody(), true);
            if ($response->getStatusCode() !== 200) {
                throw new Exception($result['error'] ?? 'Failed to refresh token');
            }

            if (empty($result['access_token']) || empty($result['refresh_token'])) {
                throw new Exception('Invalid token response');
            }

            return $this->response->setJSON([
                'access_token' => $result['access_token'],
                'refresh_token' => $result['refresh_token'],
                'expires_in' => $result['expires_in'] ?? 3600,
            ]);
        } catch (Exception $e) {
            return $this->handleError($e->getMessage());
        }
    }

    protected function handleError(string $message)
    {
        return $this->response->setJSON([
            'error' => $message,
        ]);
    }
}
