<?php

namespace App\Controllers;

use App\Libraries\Tpbank as TpbankService;
use App\Models\BankAccountModel;
use App\Models\BankSubAccountModel;
use App\Models\SimCompanyModel;
use App\Models\TpbankEnterpriseBankAccountModel;
use App\Models\TpbankTraceCompanyMapModel;
use App\Models\TransactionsModel;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use Config\Tpbank as TpbankConfig;
use Psr\Log\LoggerInterface;

class Tpbank extends BaseController
{
    use ResponseTrait;

    protected TpbankService $tpBank;

    protected TpbankConfig $config;

    public function __construct()
    {
        $this->tpBank = new TpbankService();
        $this->config = config(TpbankConfig::class);
    }

    public function initController(RequestInterface $request, ResponseInterface $response, LoggerInterface $logger)
    {
        parent::initController($request, $response, $logger);

        is_bank_supported_by_promotion($this->config->bankId, $this->company_details->company_id);
    }

    public function step1()
    {
        if (! has_permission('BankAccount', 'can_add')) {
            show_404();
        }

        $data = [
            'page_title' => 'Kết nối ngân hàng TPBank',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'is_enterprise' => $this->request->getGet('group') === 'enterprise',
        ];

        $session = session();
        $bankAccountId = $session->get('tpbank_request_switch_api_connection');
        $data['bankAccount'] = null;

        if ($this->config->allowedSwitchApiConnection && $bankAccountId) {
            $data['bankAccount'] = $this->getSwitchableApiConnectionBankAccount($bankAccountId);
        }

        $session->remove('tpbank_request_switch_api_connection');

        echo view('templates/autopay/header', $data);
        echo view('tpbank/step1', $data);
        echo view('templates/autopay/footer', $data);
    }

    public function step2($id)
    {
        $data = [
            'page_title' => 'Cấu hình',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
        ];

        if (! has_permission('BankAccount', 'can_add')) {
            show_404();
        }

        if (! is_numeric($id)) {
            show_404();
        }

        $bankAccount = $this->getBankAccount($id);

        if (! $bankAccount) {
            show_404();
        }

        $data['bankAccount'] = $bankAccount;
        $data['settings'] = bank_account_settings($bankAccount->id);

        echo view('templates/autopay/header', $data);
        echo view('tpbank/step2', $data);
        echo view('templates/autopay/footer', $data);
    }

    public function step3($id)
    {
        $data = [
            'page_title' => 'Thử một giao dịch',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
        ];

        if (! has_permission('BankAccount', 'can_add')) {
            show_404();
        }

        if (! is_numeric($id)) {
            show_404();
        }

        $bankAccount = $this->getBankAccount($id);

        if (! $bankAccount) {
            show_404();
        }

        $data['bankAccount'] = $bankAccount;

        echo view('templates/autopay/header', $data);
        echo view('tpbank/step3', $data);
        echo view('templates/autopay/footer', $data);
    }

    public function ajax_check_trans()
    {
        if ($this->request->getMethod(true) !== 'POST') {
            return '';
        }

        if (! has_permission('BankAccount', 'can_add')) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Bạn không có quyền thực hiện thao tác này',
            ]);
        }

        $bankAccountId = trim(xss_clean($this->request->getVar('bank_account_id')));

        if (! is_numeric($bankAccountId)) {
            show_404();
        }

        $bankAccount = $this->getBankAccount($bankAccountId);

        if (! $bankAccount) {
            show_404();
        }

        $session = session();

        if (! $session->get('checking_transaction_date')) {
            $session->set('checking_transaction_date', date('Y-m-d H:i:s'));
        }

        $checkingTransactionDate = $session->get('checking_transaction_date');

        $lastTransaction = slavable_model(TransactionsModel::class, 'Tpbank')
            ->select('id, amount_in, account_number, transaction_content, transaction_date')
            ->where('deleted_at', null)
            ->where('parser_status', 'Success')
            ->where('bank_account_id', $bankAccountId)
            ->where('source', 'BankAPINotify')
            ->where('transaction_date >=', $checkingTransactionDate)
            ->orderBy('id', 'DESC')
            ->first();

        if (! $lastTransaction) {
            return $this->response->setJSON(['status' => false]);
        }

        $session->remove('checking_transaction_date');

        return $this->response->setJSON([
            'status' => true,
            'data' => $lastTransaction,
        ]);
    }

    public function ajax_request_switch_api_connection()
    {
        if (! $this->config->allowedSwitchApiConnection) {
            return $this->failNotFound();
        }

        if ($this->request->getMethod(true) !== 'POST') {
            return $this->failNotFound();
        }

        if (! has_permission('BankAccount', 'can_edit')) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Bạn chưa được cấp quyền để thực hiện thao tác này',
            ]);
        }

        $bankAccountId = trim(xss_clean($this->request->getPost('bank_account_id')));

        $bankAccount = $this->getSwitchableApiConnectionBankAccount($bankAccountId);

        if (! $bankAccount) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Tài khoản ngân hàng không đủ điều kiện',
            ]);
        }

        session()->set('tpbank_request_switch_api_connection', $bankAccount->id);

        return $this->response->setJSON([
            'status' => true,
            'redirect_to' => base_url('tpbank/step1') . ($this->request->getVar('type') === 'enterprise' ? '?group=enterprise' : ''),
        ]);
    }

    public function webView()
    {
        if ($this->request->getMethod(true) !== 'GET') {
            show_404();
        }

        if (! $this->config->visibility && ! is_admin()) {
            show_404();
        }

        if (! has_permission('BankAccount', 'can_add')) {
            show_404();
        }

        $accountType = strtoupper($this->request->getGet('account_type'));

        if (! $this->validateData(
            ['account_type' => $accountType],
            ['account_type' => 'required|in_list[CB,RB]']
        )) {
            show_404();
        }

        if (! $this->config->allowedEnterpriseConnection) {
            show_404();
        }

        return redirect()->to($this->tpBank->getWebViewUrl($this->user_session['company_id'], $this->company_details->full_name, $accountType));
    }

    public function ajax_check_connection()
    {
        if ($this->request->getMethod(true) !== 'POST') {
            show_404();
        }

        if (! $this->config->visibility && ! is_admin()) {
            show_404();
        }

        if (! has_permission('BankAccount', 'can_add')) {
            show_404();
        }

        $trace = model(TpbankTraceCompanyMapModel::class)
            ->where('company_id', $this->user_session['company_id'])
            ->where('bank_account_id !=', null)
            ->first();

        if (! $trace) {
            return $this->respond([
                'status' => false,
            ]);
        }

        return $this->respond([
            'status' => true,
            'data' => [
                'trace_number' => $trace->trace_number,
                'next_url' => base_url('tpbank/step2/' . $trace->bank_account_id),
            ],
        ]);
    }

    public function ajax_delete_trace()
    {
        if ($this->request->getMethod(true) !== 'POST') {
            show_404();
        }

        if (! has_permission('BankAccount', 'can_add')) {
            show_404();
        }

        $traceNumber = $this->request->getPost('trace_number');

        if (! $this->validateData(
            ['trace_number' => $traceNumber],
            ['trace_number' => 'required']
        )) {
            return $this->respond([
                'status' => false,
                'message' => 'Dữ liệu không hợp lệ',
            ]);
        }

        $model = model(TpbankTraceCompanyMapModel::class);

        $trace = $model
            ->where('trace_number', $traceNumber)
            ->where('company_id', $this->user_session['company_id'])
            ->first();

        if (! $trace) {
            return $this->respond([
                'status' => false,
                'message' => 'Không tìm thấy dữ liệu',
            ]);
        }

        $model->delete($trace->id);

        session()->setFlashdata(
            'alert-success',
            'Liên kết tài khoản ngân hàng thành công! Vui lòng chờ khoảng 1 phút để thực hiện các giao dịch mới trên tài khoản này'
        );

        return $this->respond([
            'status' => true,
            'message' => 'Xóa dữ liệu thành công',
        ]);
    }

    public function details($id)
    {
        $data = [
            'page_title' => 'Ngân hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'allowed_switch_api_connection' => ($this->config->allowedSwitchApiConnection) || is_admin(),
            'allowed_enterprise_connection' => $this->config->allowedEnterpriseConnection,
            'api_connection_visibility' => $this->config->visibility,
        ];

        if (! has_permission('BankAccount', 'can_view_all')) {
            show_404();
        }

        if (! is_numeric($id)) {
            show_404();
        }

        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $transactionsModel = slavable_model(TransactionsModel::class, 'Tpbank');
        $simCompanyModel = model(SimCompanyModel::class);

        $bankAccount = $bankAccountModel
            ->select('tb_autopay_bank_account.id, tb_autopay_bank_account.account_number, tb_autopay_bank_account.label, tb_autopay_bank_account.active, tb_autopay_bank_account.created_at, tb_autopay_bank.full_name, tb_autopay_bank.brand_name, tb_autopay_bank_account.account_number, tb_autopay_bank_account.account_holder_name, tb_autopay_bank.icon_path, tb_autopay_bank.logo_path, tb_autopay_sim.sim_phonenumber, tb_autopay_bank_account.last_transaction, tb_autopay_bank_account.accumulated,tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.bank_id,tb_autopay_bank_account.bank_sms, tb_autopay_bank_account.bank_sms_connected, tb_autopay_bank_account.bank_api, tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.identification_number, tb_autopay_bank_account.phone_number')
            ->join('tb_autopay_bank', 'tb_autopay_bank.id=tb_autopay_bank_account.bank_id')
            ->join('tb_autopay_sim', 'tb_autopay_sim.id=tb_autopay_bank_account.sim_id', 'left')
            ->where([
                'tb_autopay_bank_account.company_id' => $this->user_session['company_id'],
                'tb_autopay_bank_account.id' => $id,
                'tb_autopay_bank_account.bank_id' => 12,
            ])
            ->first();

        if (! is_object($bankAccount)) {
            show_404();
        }

        $data = array_merge($data, [
            'bankAccount' => $bankAccount,
            'is_enterprise_account' => model(TpbankEnterpriseBankAccountModel::class)
                ->where('bank_account_id', $bankAccount->id)
                ->countAllResults(),
            'count_transactions' => $transactionsModel->where(['bank_account_id' => $bankAccount->id, 'deleted_at' => NULL])->countAllResults(),
            'allowed_delete_bank_account' => has_permission('BankAccount', 'can_delete'),
            'waitForPushTransaction' => strtotime($bankAccount->created_at) + 300 > time(), // khoảng 5 phút
            'count_sms_transactions' => $transactionsModel->where(['bank_account_id' => $bankAccount->id, 'deleted_at' => NULL, 'source' => 'SMS'])->countAllResults(),
            'sims' => $simCompanyModel
                ->select('tb_autopay_sim.id, tb_autopay_sim.sim_phonenumber')
                ->join('tb_autopay_sim', 'tb_autopay_sim.id=tb_autopay_sim_company.sim_id')
                ->where(['tb_autopay_sim_company.company_id' => $this->user_session['company_id'], 'tb_autopay_sim.active' => 1])
                ->orderBy('tb_autopay_sim_company.created_at', 'DESC')
                ->get()->getResult(),
            'bank_sms_info' => get_bank_sms_info($bankAccount->brand_name),
        ]);

        $data['count_subs'] = $bankSubAccountModel->where(['bank_account_id' => $id, 'deleted_at' => null])->countAllResults();

        $data['bank_sub_accounts_custom'] = [];
        if (!empty($bankAccount)) {
            $data['bank_sub_accounts_custom'] = $bankSubAccountModel->select("tb_autopay_bank_sub_account.id,tb_autopay_bank_sub_account.acc_type, tb_autopay_bank.brand_name,tb_autopay_bank.icon_path, tb_autopay_bank_account.account_holder_name, tb_autopay_bank_account.account_number, tb_autopay_bank_sub_account.sub_account, tb_autopay_bank_sub_account.label")
                ->join("tb_autopay_bank_account", "tb_autopay_bank_account.id = tb_autopay_bank_sub_account.bank_account_id")
                ->join("tb_autopay_bank", "tb_autopay_bank.id = tb_autopay_bank_account.bank_id", "left")
                ->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id']])
                ->where('tb_autopay_bank_account.id', $bankAccount->id)
                ->get()
                ->getResultArray();
        }

        echo view('templates/autopay/header', $data);
        echo view($data['bankAccount']->bank_sms ? 'tpbank/sms/details' : 'tpbank/details', $data);
        echo view('templates/autopay/footer', $data);
    }

    public function ajax_view_va($id = null)
    {
        if (! has_permission('BankAccount', 'can_edit')) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Bạn không có quyền sửa tài khoản ngân hàng',
            ]);
        }

        if (! is_numeric($id)) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'ID tài khoản ngân ảo hợp lệ',
            ]);
        }

        $bankSubAccountModel = model(BankSubAccountModel::class);
        $transactionsModel = slavable_model(TransactionsModel::class, 'Tpbank');

        $data['va_details'] = $bankSubAccountModel
            ->select("tb_autopay_bank_sub_account.id,tb_autopay_bank_sub_account.sub_account,tb_autopay_bank_sub_account.acc_type,tb_autopay_bank_sub_account.bank_account_id,tb_autopay_bank_sub_account.label,tb_autopay_bank_account.account_number,tb_autopay_bank_account.label,tb_autopay_bank_account.active,tb_autopay_bank_account.created_at,tb_autopay_bank.brand_name, tb_autopay_bank_account.account_number,tb_autopay_bank_account.account_holder_name,tb_autopay_bank_account.bank_api,tb_autopay_bank_account.bank_api_connected,tb_autopay_bank.icon_path,tb_autopay_bank.logo_path,tb_autopay_bank.full_name,tb_autopay_bank_sub_account.va_active, tb_autopay_bank_sub_account.sub_holder_name,tb_autopay_bank_account.bank_id,tb_autopay_bank.code as bank_code")
            ->join("tb_autopay_bank_account", "tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id")
            ->join("tb_autopay_bank", "tb_autopay_bank.id=tb_autopay_bank_account.bank_id")
            ->where(['tb_autopay_bank_sub_account.id' => $id, 'tb_autopay_bank_account.company_id' => $this->user_session['company_id']])
            ->first();

        if (! $data['va_details']) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Không tìm thấy tài khoản ảo này',
            ]);
        }

        $data['count_transactions'] = $transactionsModel
            ->where(['sub_account' => $data['va_details']->sub_account])
            ->countAllResults();

        $data['last_transaction'] = $transactionsModel
            ->where(['sub_account' => $data['va_details']->sub_account, 'accumulated!=' => ''])
            ->orderBy('transaction_date', 'DESC')
            ->first();

        $html = view('banksubaccount/va_view', $data);

        return $this->response->setJSON([
            'status' => true,
            'html' => $html,
        ]);
    }

    /**
     * @deprecated This method is replaced by Bankaccount::settings()
     */
    public function toggleActivation($id)
    {
        if (! has_permission('BankAccount', 'can_delete')) {
            show_404();
        }

        if (! is_numeric($id)) {
            show_404();
        }

        if (! $this->validate([
            'active' => 'required|in_list[0,1]',
        ])) {
            show_404();
        }

        $bankAccountModel = model(BankAccountModel::class);

        $active = (bool) $this->request->getPost('active');

        $bankAccount = $bankAccountModel
            ->where('company_id', $this->user_session['company_id'])
            ->where('id', $id)
            ->where('bank_id', $this->config->bankId)
            ->first();

        if (! $bankAccount) {
            show_404();
        }

        if ($bankAccount->active) {
            show_404();
        }

        $bankAccountModel->update($id, ['active' => $active]);

        session()->setFlashdata(
            'alert-success',
            $active ? 'Kích hoạt lại tài khoản ngân hàng thành công' : 'Tạm dừng tài khoản ngân hàng thành công'
        );

        return $this->respond([
            'status' => true,
        ]);
    }

    protected function getBankAccount($id)
    {
        return model(BankAccountModel::class)
            ->select('tb_autopay_bank_account.id, tb_autopay_bank_account.account_number, tb_autopay_bank_account.label, tb_autopay_bank_account.active, tb_autopay_bank_account.created_at, tb_autopay_bank.full_name, tb_autopay_bank.brand_name, tb_autopay_bank_account.account_number, tb_autopay_bank_account.account_holder_name, tb_autopay_bank.icon_path, tb_autopay_bank.logo_path, tb_autopay_sim.sim_phonenumber, tb_autopay_bank_account.last_transaction, tb_autopay_bank_account.accumulated,tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.bank_id,tb_autopay_bank_account.bank_sms, tb_autopay_bank_account.bank_sms_connected, tb_autopay_bank_account.bank_api, tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.identification_number, tb_autopay_bank_account.phone_number, tb_autopay_bank_account.sim_id, tb_autopay_bank_account.merchant_id')
            ->join('tb_autopay_bank', 'tb_autopay_bank.id=tb_autopay_bank_account.bank_id')
            ->join('tb_autopay_sim', 'tb_autopay_sim.id=tb_autopay_bank_account.sim_id', 'left')
            ->where('tb_autopay_bank_account.company_id', $this->user_session['company_id'])
            ->where('tb_autopay_bank_account.id', $id)
            ->where('tb_autopay_bank_account.bank_id', $this->config->bankId)
            ->first();
    }

    protected function getSwitchableApiConnectionBankAccount($id)
    {
        return model(BankAccountModel::class)
            ->where('company_id', $this->user_session['company_id'])
            ->where('bank_id', $this->config->bankId)
            ->where('bank_sms', true)
            ->where('bank_api', false)
            ->where('bank_api_connected', false)
            ->where('active', true)
            ->find($id);
    }
}
