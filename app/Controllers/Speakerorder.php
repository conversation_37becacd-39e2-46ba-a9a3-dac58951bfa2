<?php

namespace App\Controllers;

use App\Exceptions\InvalidInvoiceDataException;
use App\Models\CompanyModel;
use App\Models\PhysicalInvoiceModel;
use App\Models\PhysicalOrderModel;
use App\Models\PhysicalOrderTrackingModel;
use App\Models\PhysicalProductModel;
use App\Models\TrackingModel;
use App\Services\AddressService;
use App\Services\CreateInvoice;
use App\Services\SpeakerOrderService;
use Config\Billing;
use Config\SpeakerOrder as Config;
use Exception;
use App\Libraries\TaxCodeLookup;
use App\Models\InvoiceModel;
use App\Services\DecalService;

class Speakerorder extends BaseController
{
    protected Config $config;

    protected SpeakerOrderService $speakerOrderService;

    protected AddressService $addressService;

    protected TaxCodeLookup $taxCodeLookup;

    protected DecalService $decalService;

    public function __construct()
    {
        $this->config = config(Config::class);
        $this->speakerOrderService = new SpeakerOrderService();
        $this->addressService = new AddressService();
        $this->taxCodeLookup = new TaxCodeLookup();
        $this->decalService = new DecalService();
    }

    public function index()
    {
        if ($this->request->getMethod(true) !== 'GET') {
            show_404();
        }

        $data = [
            'page_title' => 'Đặt Loa thanh toán',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
        ];

        $data['products'] = model(PhysicalProductModel::class)->getProducts();
        $data['paymentMethods'] = $this->config->paymentMethods;

        echo theme_view('templates/autopay/header', $data);
        echo view('speaker-order/index', $data);
        echo theme_view('templates/autopay/footer', $data);
    }

    public function create()
    {
        if ($this->request->getMethod(true) !== 'POST') {
            show_404();
        }

        $rules = [
            'customer_type' => 'required|in_list[retail,wholesale]',
            'customer_name' => 'required|min_length[3]',
            'customer_phone' => 'required|regex_match[/^[0-9]{10}$/]',
            'customer_email' => 'permit_empty|valid_email',
            'province' => 'required|is_not_unique[tb_autopay_provinces_v2.code]',
            'district' => 'required|is_not_unique[tb_autopay_districts_v2.code]',
            'ward' => 'required|is_not_unique[tb_autopay_wards_v2.code]',
            'address' => 'required|min_length[3]',
            'products' => 'required',
            'products.*' => 'required|is_not_unique[tb_autopay_physical_products.id]',
            'product_qty' => 'required',
            'product_qty.*' => 'required',
            'payment_method' => 'required|in_list[' . implode(',', array_keys($this->config->paymentMethods)) . ']',
            'referral_code' => 'permit_empty|is_not_unique[tb_autopay_partner.referral_code]',
        ];

        if (! $this->validate($rules, [
            'products.*' => [
                'is_not_unique' => 'Sản phẩm không hợp lệ',
            ],
            'referral_code' => [
                'is_not_unique' => 'Mã giới thiệu không hợp lệ',
            ],
        ])) {
            return $this->response
                ->setStatusCode(400)
                ->setJSON([
                    'status' => false,
                    'messages' => $this->validator->getErrors(),
                ]);
        }

        $company = model(CompanyModel::class)->select('tr_gcid')->find($this->company_details->id);

        $data = [
            'customer_type' => $this->request->getPost('customer_type'),
            'customer_name' => $this->request->getPost('customer_name'),
            'customer_phone' => $this->request->getPost('customer_phone'),
            'customer_email' => $this->request->getPost('customer_email') ?: null,
            'province' => $this->request->getPost('province'),
            'district' => $this->request->getPost('district'),
            'ward' => $this->request->getPost('ward'),
            'address' => $this->request->getPost('address'),
            'products' => (array) $this->request->getPost('products'),
            'product_qty' => (array) $this->request->getPost('product_qty'),
            'payment_method' => $this->request->getPost('payment_method'),
            'notes' => $this->request->getPost('notes'),
            'user_id' => $this->user_session['user_id'],
            'referral_code' => $this->request->getPost('referral_code'),
            'tr_gcid' => $company->tr_gcid,
        ];

        try {
            $result = $this->speakerOrderService->createOrder($data);

            $companyTracking = model(TrackingModel::class)
                ->where('company_id', $this->user_session['company_id'])
                ->first();

            if ($companyTracking) {
                model(PhysicalOrderTrackingModel::class)->addTracking($result['order_id'], [
                    'utm_source' => $companyTracking->utm_source,
                    'utm_medium' => $companyTracking->utm_medium,
                    'utm_campaign' => $companyTracking->utm_campaign,
                    'utm_term' => $companyTracking->utm_term,
                    'utm_content' => $companyTracking->utm_content,
                    'referrer' => $companyTracking->referer,
                    'user_agent' => $this->request->getUserAgent(),
                    'ip_address' => $this->request->getIPAddress(),
                ]);
            }

            return $this->response->setJSON([
                'status' => true,
                'message' => $result['message'],
                'redirect_url' => base_url('speakerorder/confirmation/' . $result['order_code']),
            ]);
        } catch (Exception $e) {
            return $this->response->setJSON([
                'status' => false,
                'message' => $e->getMessage(),
            ]);
        }
    }

    public function confirmation($orderCode)
    {
        if ($this->request->getMethod(true) !== 'GET') {
            show_404();
        }

        $orderResult = $this->speakerOrderService->getOrderByCode($orderCode, $this->user_session['user_id']);

        if (! $orderResult) {
            show_404();
        }

        $bankAccount = config(Billing::class)->ownerBankAccount;

        try {
            $qrCode = "https://qr.sepay.vn/img?bank=MBBank&acc=*************&template=&amount=" . intval($orderResult['order']->total_amount) . "&des=" . $orderResult['order']->order_code;
            
        } catch (Exception $e) {
            $qrCode = null;
        }

        $invoice = model(InvoiceModel::class)
            ->select([
                'tb_autopay_physical_invoices.status',
                'tb_autopay_invoice_customer_info.name',
                'tb_autopay_invoice_customer_info.company_name',
                'tb_autopay_invoice_customer_info.tax_code',
                'tb_autopay_invoice_customer_info.address',
                'tb_autopay_invoice_customer_info.phone',
                'tb_autopay_invoice_customer_info.email',
            ])
            ->where('tb_autopay_physical_invoices.order_id', $orderResult['order']->id)
            ->join('tb_autopay_physical_invoices', 'tb_autopay_physical_invoices.id = tb_autopay_invoice.physical_invoice_id')
            ->join('tb_autopay_invoice_customer_info', 'tb_autopay_invoice_customer_info.invoice_id = tb_autopay_invoice.id')
            ->first();

        $invoice->email = json_decode($invoice->email ?: '[]');

        $decals = $this->decalService->getDecalsByOrder($orderResult['order']->id);

        $data = [
            'page_title' => 'Xác nhận đơn hàng #' . $orderCode,
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'order' => $orderResult['order'],
            'orderItems' => $orderResult['items'],
            'decals' => $decals,
            'canEditDecals' => in_array($orderResult['order']->status, ['Pending', 'Confirmed', 'Processing']),
            'bankAccount' => $bankAccount,
            'qrCode' => $qrCode,
            'invoice' => $invoice,
        ];

        echo theme_view('templates/autopay/header', $data);
        echo view('speaker-order/confirmation', $data);
        echo theme_view('templates/autopay/footer', $data);
    }

    public function ajax_get_provinces()
    {
        if ($this->request->getMethod(true) !== 'GET') {
            show_404();
        }

        return $this->response->setJSON([
            'status' => true,
            'data' => $this->addressService->getProvinces(),
        ]);
    }

    public function ajax_get_districts()
    {
        if ($this->request->getMethod(true) !== 'GET') {
            show_404();
        }

        $province = $this->request->getGet('province');

        if (empty($province)) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Vui lòng chọn tỉnh thành phố',
            ]);
        }

        return $this->response->setJSON([
            'status' => true,
            'data' => $this->addressService->getDistrictsByProvince($province),
        ]);
    }

    public function ajax_get_wards()
    {
        if ($this->request->getMethod(true) !== 'GET') {
            show_404();
        }

        $district = $this->request->getGet('district');

        if (empty($district)) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Vui lòng chọn quận huyện',
            ]);
        }

        return $this->response->setJSON([
            'status' => true,
            'data' => $this->addressService->getWardsByDistrict($district),
        ]);
    }

    public function ajax_calculate_shipping_fee()
    {
        if ($this->request->getMethod(true) !== 'POST') {
            show_404();
        }

        $rules = [
            'province' => 'required|is_not_unique[tb_autopay_provinces_v2.code]',
            'district' => 'required|is_not_unique[tb_autopay_districts_v2.code]',
            'ward' => 'required|is_not_unique[tb_autopay_wards_v2.code]',
            'products' => 'required',
            'product_qty' => 'required',
        ];

        if (! $this->validate($rules)) {
            return $this->response->setJSON([
                'status' => false,
                'message' => implode(', ', $this->validator->getErrors()),
            ]);
        }

        $data = $this->request->getPost();

        try {
            $addressInfo = $this->addressService->validateAndGetAddressInfo(
                $data['province'],
                $data['district'],
                $data['ward']
            );

            $productInfo = $this->speakerOrderService->processProducts((array)$data['products'], (array)$data['product_qty']);

            return $this->response->setJSON([
                'status' => true,
                'data' => [
                    'shipping_fee' => $this->speakerOrderService->calculateShippingFee($addressInfo, $productInfo),
                ],
            ]);
        } catch (Exception $e) {
            return $this->response->setJSON([
                'status' => false,
                'message' => $e->getMessage(),
            ]);
        }
    }

    public function check_payment_status($code = null)
    {
        if (! $this->request->isAJAX()) {
            show_404();
        }

        if (empty($code)) {
            show_404();
        }

        $orderModel = model(PhysicalOrderModel::class);
        $order = $orderModel->where('order_code', $code)->first();

        if (! $order) {
            show_404();
        }

        return $this->response->setJSON([
            'status' => true,
            'data' => [
                'order_code' => $order->order_code,
                'payment_status' => $order->payment_status,
                'status' => $order->status,
            ]
        ]);
    }

    public function update_invoice($orderCode = null)
    {
        if (! $this->request->isAJAX()) {
            show_404();
        }

        if (empty($orderCode)) {
            show_404();
        }

        $orderData = $this->speakerOrderService->getOrderByCode($orderCode, $this->user_session['user_id']);

        if (! $orderData) {
            show_404();
        }

        $invoice = model(PhysicalInvoiceModel::class)->where('order_id', $orderData['order']->id)->first();

        if (! $invoice) {
            show_404();
        }

        $data = $this->request->getPost();

        if (! $this->validateData($data, [
            'invoice_type' => 'required|in_list[personal,company]',
            'name' => 'required|min_length[3]',
            'address' => 'required|min_length[10]',
            'company_name' => 'permit_empty|min_length[3]',
            'tax_code' => 'permit_empty|min_length[3]',
            'invoice_email' => 'required',
            'invoice_email.*' => 'valid_email',
        ])) {
            return $this
                ->response
                ->setStatusCode(400)
                ->setJSON(['messages' => $this->validator->getErrors()]);
        }

        $createPhysicalInvoice = new CreateInvoice();

        try {
            $createPhysicalInvoice->validateData($data);

            if (! $this->taxCodeLookup->validateTaxCode($data['tax_code'])) {
                throw InvalidInvoiceDataException::withMessage('tax_code', 'Mã số thuế không hợp lệ');
            }
        } catch (InvalidInvoiceDataException $e) {
            return $this
                ->response
                ->setStatusCode(400)
                ->setJSON(['messages' => $e->getMessages()]);
        }

        if ($data['invoice_type'] === 'personal') {
            $data['company_name'] = null;
        }

        try {
            $createPhysicalInvoice->execute($orderData['order']->id, $data);

            return $this->response->setJSON([
                'status' => true,
                'message' => 'Hóa đơn đã được tạo thành công',
            ]);
        } catch (Exception $e) {
            return $this->response->setJSON([
                'status' => false,
                'message' => $e->getMessage(),
            ]);
        }
    }

    public function lookup_tax_code($taxCode = null)
    {
        if (
            ! $this->request->isAJAX()
            || $this->request->getMethod(true) !== 'POST'
        ) {
            show_404();
        }

        if (empty($taxCode)) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Mã số thuế không hợp lệ',
            ]);
        }

        $data = $this->taxCodeLookup->lookupBusiness($taxCode);

        if (empty($data)) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Mã số thuế không hợp lệ',
            ]);
        }

        return $this->response->setJSON([
            'status' => true,
            'data' => $data,
        ]);
    }

    public function save_decals($code = null)
    {
        if (
            ! $this->request->isAJAX()
            || $this->request->getMethod(true) !== 'POST'
            || empty($code)
        ) {
            show_404();
        }

        if (! $this->validate([
            'decals' => 'permit_empty',
            'decals.*' => 'permit_empty|string',
        ])) {
            return $this->response
                ->setStatusCode(400)
                ->setJSON([
                    'status' => false,
                    'messages' => $this->validator->getErrors(),
                ]);
        }

        $orderData = $this->speakerOrderService->getOrderByCode($code, $this->user_session['user_id']);

        if (! $orderData) {
            show_404();
        }

        $order = $orderData['order'];

        if (! in_array($order->status, ['Pending', 'Confirmed', 'Processing'])) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Đơn hàng đang trong quá trình xử lý, không thể thay đổi cài đặt mã QR.',
            ]);
        }

        try {
            $result = $this->decalService->saveDecalSettings(
                $order,
                $this->request->getPost('decals')
            );

            return $this->response->setJSON($result);
        } catch (Exception $e) {
            return $this->response->setJSON([
                'status' => false,
                'message' => $e->getMessage(),
            ]);
        }
    }
}
