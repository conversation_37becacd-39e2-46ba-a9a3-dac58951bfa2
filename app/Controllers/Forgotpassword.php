<?php

namespace App\Controllers;

use App\Models\CompanyModel;
use App\Models\UserForgotPasswordModel;
use App\Models\UserModel;
use CodeIgniter\Controller;
use App\Libraries\Captcha;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\Response;

class Forgotpassword extends Controller
{
    use ResponseTrait;
    
    public function index()
    {
        $captcha = new Captcha;

        $data['captcha_enabled'] = $captcha->getEnabled();
        $data['captcha_driver'] = $captcha->getDriver();
        $data['captcha_site_key'] = $captcha->getSiteKey();

        helper(['form','general','url','text']);
        
        $data['is_otp_support'] = $this->isOtpSupport();
        
        return view('forgotpassword/index', $data);
    }

    public function do_reset() {
        
        helper(['form','general','url','text']);
        
     
        $request = \Config\Services::request();

        if ($request->getMethod(true) != 'POST')
            return redirect()->to(base_url());
 
        if(check_logged_user()) {
            return redirect()->to(base_url());
        }

        $validation =  \Config\Services::validation();

        $rules = [
            'email' => [
                "label" => "Email", 
                "rules" => "required|valid_email|is_not_unique[tb_autopay_user.email]", 
                "errors" => [
                    'is_not_unique' => 
                    'Email bạn điền không tồn tại trên hệ thống'
                ]
            ],
        ];

        $captcha = new Captcha;

        if ($captcha->getEnabled()) {
            if ($captcha->getDriver() === 'turnstile') {
                $rules['cf-turnstile-response'] = ['label' => 'Captcha', 'rules' => 'required|valid_turnstile'];
            } else if ($captcha->getDriver() === 'recaptcha') {
                $rules['g-recaptcha-response'] = ["label" => "Recaptcha", "rules" => "required|valid_recaptcha"];
            }
        }

        if(! $this->validate($rules)) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode("<br>", $validation->getErrors())));
        } else {
            $userModel = model(UserModel::class);

            $email = $request->getVar('email', FILTER_SANITIZE_EMAIL);
            
            $user_details = $userModel->select(['id','email'])->where(['email' => $email,'active'=>1])->get()->getRow();

            if(!is_object($user_details))
                return $this->response->setJSON(array("status"=>FALSE, "Email không tồn tại trên hệ thống."));


            $userForgotPasswordModel = model(UserForgotPasswordModel::class);


            $user_id = $user_details->id;

            $count_forgots = $userForgotPasswordModel->where(['created_at<' => date("Y-m-d 23:59:59"), 'user_id' => $user_id])->countAllResults();

            if($count_forgots > 10)
                return $this->response->setJSON(array("status"=>FALSE, "message"=> "Bạn đã thực hiện quá nhiều lần trong hôm nay, vui lòng quay lại vào ngày mai hoặc liên hệ SePay để được hỗ trợ!"));

            $token = random_string('alnum',36);
            $token_expired_at = date("Y-m-d H:i:s", strtotime("+10 hours"));


            $token_id = $userForgotPasswordModel->insert(['user_id' => $user_id, 'token' => $token, 'token_expired_at' => $token_expired_at,'mail_sent' => 0]);

            $email_result = $userForgotPasswordModel->sendEmailResetPassword($user_details->email, base_url('forgotpassword/reset?token=' . $token));

            if($email_result)
                return $this->response->setJSON(array("status"=>TRUE,"message" => "Email hướng dẫn đặt lại mật khẩu đã được gửi về mail của quý khách. Vui lòng kiểm tra mail trong Inbox hoặc Spam"));
            else
                return $this->response->setJSON(array("status"=>TRUE,"message" => "Có lỗi xảy ra trong quá trình gửi mail, vui lòng liên hệ Sepay để được hỗ trợ."));


        }
    }

    public function reset() {
        helper(['form','general','url','text']);
        
        if(check_logged_user()) {
            return redirect()->to(base_url());
        }

        $request = \Config\Services::request();
        $token = $request->getGet('token');

        if(!$token && strlen($token) != 36) 
            return view('forgotpassword/error', ['message' => 'Link đặt lại mật khẩu không hợp lệ']);

        $userForgotPasswordModel = model(UserForgotPasswordModel::class);

        $is_valid_token = $userForgotPasswordModel->isValidToken($token);

        if($is_valid_token == FALSE)
            return view('forgotpassword/error', ['message' => 'Link đặt lại mật khẩu không đúng hoặc đã hết hạn']);

        $forgot_details = $userForgotPasswordModel->where(['token' => $token])->get()->getRow();

        if(!is_object($forgot_details))
            return view('forgotpassword/error', ['message' => 'Link đặt lại mật khẩu không đúng hoặc đã hết hạn']);


        $user_id = $forgot_details->user_id;

        if(!is_numeric($user_id))
            return view('forgotpassword/error', ['message' => 'Link đặt lại mật khẩu không hợp lệ']);


        return view('forgotpassword/resset_form', ['token' => $token]);

    }


    public function do_change() {
        helper(['form','general','url','text']);
        
     
        $request = \Config\Services::request();

        if ($request->getMethod(true) != 'POST')
            return redirect()->to(base_url());
 
        if(check_logged_user()) {
            return redirect()->to(base_url());
        }

        $validation =  \Config\Services::validation();

        $token = $request->getVar('token');

        if(!$token && strlen($token) != 36) 
            return view('forgotpassword/error', ['message' => 'Link đặt lại mật khẩu không hợp lệ']);

    
        if(! $this->validate([
            'token' => ["label" => "Token",  "rules" => "required|exact_length[36]"],
            'password' => ['label' => 'Mật khẩu mới', 'rules' =>  "required|min_length[6]|max_length[24]|valid_password"],
            'passwordconf' => ['label' => 'Nhập lại mật khẩu mới', 'rules' => 'required|matches[password]'],
          
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        } else {

            $userForgotPasswordModel = model(UserForgotPasswordModel::class);
            $is_valid_token = $userForgotPasswordModel->isValidToken($token);

            if($is_valid_token == FALSE)
                return $this->response->setJSON(array('status'=>FALSE,'message'=>'Token không hợp lệ'));

            
            $forgot_details = $userForgotPasswordModel->where(['token' => $token])->get()->getRow();

            if(!is_object($forgot_details))
                return $this->response->setJSON(array('status'=>FALSE,'message'=>'Link đặt lại mật khẩu không đúng hoặc đã hết hạn'));    
    
            $user_id = $forgot_details->user_id;
    
            if(!is_numeric($user_id))
                return $this->response->setJSON(array('status'=>FALSE,'message'=>'Link đặt lại mật khẩu không hợp lệ'));    

            $new_password = $this->request->getPost('password');
            $userModel = model(UserModel::class);
            $result = $userModel->set(['password' => password_hash(trim($new_password), PASSWORD_DEFAULT), 'last_password_changed' => date('Y-m-d H:i:s')])->where('id',$user_id)->update();

            if($result) {

                $userForgotPasswordModel->where(['id' => $forgot_details->id])->delete();

                add_user_log(array('data_id'=>$user_id, 'data_type'=>'user_reset_password','description'=>'Reset mật khẩu','user_id'=> $user_id,'ip'=>$request->getIPAddress(), 'user_agent'=>$request->getUserAgent()->getAgentString(),'status'=>'Success'));
                return $this->response->setJSON(array('status'=>TRUE,'message'=>'Mật khẩu đã được đổi thành công. Vui lòng đăng nhập để sử dụng'));

            } else {
                return $this->response->setJSON(array('status'=>FALSE,'message'=>'Có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ'));

            }



        }
    }
    
    public function ajax_request_reset_password_with_otp(): Response
    {
        if ($this->request->getMethod(true) !== 'POST') {
            return $this->failNotFound();
        }
        
        if (!$this->isOtpSupport()) {
            return $this->failNotFound();
        }
        
        if (check_logged_user()) {
            return $this->failNotFound();
        }
        
        $rules = [
            'phone_number' => [
                "label" => "Số điện thoại", 
                "rules" => [
                    "required",
                    "valid_phone_number",
                    "is_not_unique[tb_autopay_user.username]"
                ],
                "errors" => [
                    'is_not_unique' => 
                    'Số điện thoại bạn điền không tồn tại trên hệ thống'
                ]
            ],
        ];
        
        $captcha = new Captcha;
        
        if ($captcha->getEnabled()) {
            if ($captcha->getDriver() === 'turnstile') {
                $rules['cf-turnstile-response'] = ['label' => 'Captcha', 'rules' => ['required', 'valid_turnstile']];
            } else if ($captcha->getDriver() === 'recaptcha') {
                $rules['g-recaptcha-response'] = ["label" => "Recaptcha", "rules" => ["required", "valid_recaptcha"]];
            }
        }
        
        $data = [
            'phone_number' => $this->getVarString('phone_number'),
        ];
        
        if (! $this->validateData($data, $rules)) {
            return $this->fail($this->validator->getErrors());
        }
        
        $userModel = model(UserModel::class);
        $userDetails = $userModel
            ->join('tb_autopay_company_user', 'tb_autopay_company_user.id = tb_autopay_user.id')
            ->where(['username' => $data['phone_number'], 'active' => 1])->first();
        
        if (! $userDetails) {
            return $this->fail(['phone_number' => 'Số điện thoại bạn điền không tồn tại trên hệ thống']);
        }
        
        $billingConfig = config(\Config\Billing::class);
        
        $companyDetails = model(CompanyModel::class)
            ->join('tb_autopay_company_subscription', 'tb_autopay_company_subscription.company_id = tb_autopay_company.id')
            ->where('tb_autopay_company.id', $userDetails->company_id)
            ->whereIn('tb_autopay_company.status', ['Pending', 'Active'])
            ->where('tb_autopay_company_subscription.plan_id', $billingConfig->speakerBillingDefaultProductId)
            ->first();
            
        if (! $companyDetails) {
            return $this->fail(['phone_number' => 'Số điện thoại bạn điền không tồn tại trên hệ thống']);
        }
        
        helper(['text', 'general']);
        
        $requestId = uuid();
        $otp = random_string('numeric', 6);
        $token = $this->generateOtpToken($userDetails->user_id, $otp, $requestId);
        $tokenExpiredAt = date("Y-m-d H:i:s", strtotime("+2 minutes"));
        
        $userForgotPasswordModel = model(UserForgotPasswordModel::class);
        $tokenId = $userForgotPasswordModel->insert([
            'user_id' => $userDetails->user_id,
            'request_id' => $requestId,
            'token' => $token, 
            'token_expired_at' => $tokenExpiredAt, 
        ]);
            
        if (!$tokenId) {
            return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng thử lại.']);
        }
        
        $dinstarConfig = config(\Config\Dinstar::class);
        $canSendOtp = property_exists($dinstarConfig, 'enabled') ? $dinstarConfig->enabled || is_admin() : false;
        
        if (!$canSendOtp) {
            return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ.']);
        }
        
        $throttler = service('throttler');
        
        if ($throttler->check(md5(sprintf('request_otp_time|%s|%s', $userDetails->username, $this->request->getIPAddress())), 1, MINUTE) === false) {
            return $this->respond(['status' => false, 'message' => 'Bạn đang thử quá nhanh, vui lòng thử lại sau ít lát.']);
        }
        
        if ($throttler->check(md5(sprintf('request_otp_count|%s|%s', $userDetails->username, $this->request->getIPAddress())), 3, 3600 * 24) === false) {
            return $this->respond(['status' => false, 'message' => 'Bạn đã thử quá nhiều lần, vui lòng thử lại vào ngày mai hoặc liên hệ SePay để được hỗ trợ.']);
        }
        
        $curl = curl_init();
        
        $dinstarBody = json_encode([
            "text" => "Ma SePay la " . $otp,
            "port" => [11],
            "param" => [
                ["number" => $userDetails->username]
            ]
        ]);
        
        curl_setopt_array($curl, [
            CURLOPT_URL => sprintf('%s/%s', trim($dinstarConfig->host, '/'), trim($dinstarConfig->sendSmsEndpoint, '/')),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_HTTPAUTH => CURLAUTH_ANY,
            CURLOPT_USERPWD => sprintf('%s:%s', $dinstarConfig->username, $dinstarConfig->password),
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => $dinstarBody,
            CURLOPT_HTTPHEADER => [
                "Content-Type: application/json"
            ]
        ]);
        
        $response = curl_exec($curl);
        $error = curl_error($curl);
        
        if ($error) {
            curl_close($curl);
            
            log_message('error', 'Reset password with OTP failed: ' . $error);
            return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng liên hệ với SePay để được hỗ trợ.']);
        } 
        
        curl_close($curl);
        
        try {
            $responseJson = json_decode($response);
        } catch (\Exception $e) {
            log_message('error', 'Reset password with OTP failed: ' . $e->getMessage());
            return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng liên hệ với SePay để được hỗ trợ.']);
        }
        
        log_message('error', 'Dinstar send SMS request: ' . $dinstarBody);
        log_message('error', 'Dinstar send SMS response: ' . $response);
        
        if ($responseJson->error_code != 202) {
            log_message('error', 'Reset password with OTP failed: Error code not equal to 202 - ' . $response);
            return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng liên hệ với SePay để được hỗ trợ.']);
        }
        
        $userForgotPasswordModel->where('id', $tokenId)
            ->set(['sms_sent' => 1])
            ->update();
        
        return $this->respond(['status' => true, 'message' => 'Mã OTP đã được gửi đến số điện thoại của bạn.', 'request_id' => $requestId]);
    }
    
    public function ajax_confirm_reset_password_with_otp(): Response
    {
        if ($this->request->getMethod(true) !== 'POST') {
            return $this->failNotFound();
        }
        
        if (!$this->isOtpSupport()) {
            return $this->failNotFound();
        }
        
        if (check_logged_user()) {
            return $this->failNotFound();
        }
        
        $rules = [
            'phone_number' => [
                "label" => "Số điện thoại", 
                "rules" => [
                    "required",
                    "valid_phone_number",
                    "is_not_unique[tb_autopay_user.username]"
                ],
                "errors" => [
                    'is_not_unique' => 
                    'Số điện thoại bạn điền không tồn tại trên hệ thống'
                ]
            ],
            'otp' => [
                'label' => 'Mã OTP',
                'rules' => [
                    'required',
                    'regex_match[/^[0-9]{6}$/]'
                ],
                'errors' => [
                    'regex_match' => 'Mã OTP không hợp lệ'
                ]
            ],
            'request_id' => [
                'label' => 'Mã yêu cầu',
                'rules' => [
                    'required',
                    'regex_match[/^[0-9a-f\-]{36}$/]'
                ],
                'errors' => [
                    'required' => 'Mã yêu cầu là bắt buộc'
                ]
            ]
        ];
        
        $captcha = new Captcha;
        
        if ($captcha->getEnabled()) {
            if ($captcha->getDriver() === 'turnstile') {
                $rules['cf-turnstile-response'] = ['label' => 'Captcha', 'rules' => ['required', 'valid_turnstile']];
            } else if ($captcha->getDriver() === 'recaptcha') {
                $rules['g-recaptcha-response'] = ["label" => "Recaptcha", "rules" => ["required", "valid_recaptcha"]];
            }
        }
        
        $data = [
            'phone_number' => $this->getVarString('phone_number'),
            'otp' => $this->getVarString('otp'),
            'request_id' => $this->getVarString('request_id')
        ];
        
        if (! $this->validateData($data, $rules)) {
            return $this->fail($this->validator->getErrors());
        }
        
        $userModel = model(UserModel::class);
        $userDetails = $userModel
            ->join('tb_autopay_company_user', 'tb_autopay_company_user.id = tb_autopay_user.id')
            ->where(['username' => $data['phone_number'], 'active' => 1])
            ->first();
        
        if (! $userDetails) {
            return $this->fail(['phone_number' => 'Số điện thoại bạn điền không tồn tại trên hệ thống']);
        }
        
        $billingConfig = config(\Config\Billing::class);
        
        $companyDetails = model(CompanyModel::class)
            ->join('tb_autopay_company_subscription', 'tb_autopay_company_subscription.company_id = tb_autopay_company.id')
            ->where('tb_autopay_company.id', $userDetails->company_id)
            ->whereIn('tb_autopay_company.status', ['Pending', 'Active'])
            ->where('tb_autopay_company_subscription.plan_id', $billingConfig->speakerBillingDefaultProductId)
            ->first();
            
        if (! $companyDetails) {
            return $this->fail(['phone_number' => 'Số điện thoại bạn điền không tồn tại trên hệ thống']);
        }
        
        $userForgotPasswordModel = model(UserForgotPasswordModel::class);
        $userForgotPasswordDetails = $userForgotPasswordModel
            ->where('request_id', $data['request_id'])
            ->where('token_expired_at >', date('Y-m-d H:i:s'))
            ->first();
        
        if (!$userForgotPasswordDetails) {
            return $this->respond(['status' => false, 'message' => 'Yêu cầu không tồn tại hoặc đã hết hạn, vui lòng lấy mã OTP mới.']);
        }
        
        $cache = service('cache');
        $cacheAttemptKey = 'attempt_otp_' . $data['request_id'];
        
        if ($cache->get($cacheAttemptKey) >= 5) {
            $cache->save($cacheAttemptKey, 0);
            $userForgotPasswordModel->where('request_id', $data['request_id'])->delete();
            return $this->respond(['status' => false, 'message' => 'Bạn đã nhập sai quá nhiều lần, vui lòng lấy mã OTP mới.']);
        }
        
        if ($userForgotPasswordDetails->token !== $this->generateOtpToken($userDetails->user_id, $data['otp'], $data['request_id'])) {
            $cache->save($cacheAttemptKey, $cache->get($cacheAttemptKey) + 1);
            
            return $this->respond(['status' => false, 'message' => 'Mã OTP không chính xác.']);
        }
        
        $cache->save($cacheAttemptKey, 0);
        
        return $this->respond(['status' => true, 'token' => $userForgotPasswordDetails->token]);
    }
    
    protected function isFromSpeakerSite(): bool
    {
        if (! service('speakerBillingFeature')->enabled) {
            return false;
        }
        
        helper('cookie');
        
        if ($this->request->getGet('onboarding') === 'loa') {
            set_cookie('onboarding', 'loa', 86400);
            return true;
        }
        
        if (get_cookie('onboarding') === 'loa') {
            return true;
        }
        
        $referer = $this->request->getServer('HTTP_REFERER');
        
        if ($referer && strpos($referer, 'loa.sepay.vn') !== false) {
            return true;
        }
        
        return false;
    }
    
    protected function isOtpSupport(): bool
    {
        $config = config(\Config\Security::class);
        return $this->isFromSpeakerSite() && (property_exists($config, 'enabledResetPasswordWithOtp') ? $config->enabledResetPasswordWithOtp || is_admin() : false);
    }
    
    protected function generateOtpToken(string $userId, string $otp, string $requestId): string
    {
        return 'otp_' . md5(sprintf('%s|%s|%s', $userId, $otp, $requestId));
    }
    
    protected function getVarString($name)
    {
        $value = $this->request->getVar($name) ?? $this->request->getGet($name);
        
        if (is_array($value) && count($value)) {
            return $value[0];
        }
        
        return is_string($value) ? $value : '';
    }
}