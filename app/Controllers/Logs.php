<?php

namespace App\Controllers;
use App\Models\UserLogModel;
use App\Models\UserModel;

use CodeIgniter\Controller;

class Logs extends BaseController
{
    public function index()
    { 
        $data = [
            'page_title' => 'Logs',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        if(!in_array($this->company_details->role,['Admin', 'SuperAdmin']))
            show_404();
      
        echo theme_view('templates/autopay/header',$data);
        echo view('logs/index',$data);
        echo theme_view('templates/autopay/footer',$data);

    }

    public function ajax_logs_list() {

        if(!in_array($this->company_details->role,['Admin', 'SuperAdmin']))
            show_404();
     
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        $userLogModel = model(UserLogModel::class);
     

        $logs = $userLogModel->getDatatables($this->user_session['company_id']);
         
        $data = array();

        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');

        foreach ($logs as $log) {

            $agent = $this->request->getUserAgent();
            if (! empty($log->user_agent)) {
                $agent->parse($log->user_agent);
            }
            $no++;
            $row = array();
            
            $row[] = $no;
            $row[] = esc($log->id);
            $row[] = '# ' . esc($log->user_id . ' '. $log->lastname . ' ' . $log->firstname);
            //$row[] = esc($log->data_type);
            $row[] = esc($log->description);
            if($log->status == "Success")
                $row[] = "<span class='badge bg-success'>" . esc($log->status) . "</span>";
            else
                $row[] = "<span class='badge bg-danger'>" . esc($log->status) . "</span>";
            $row[] = esc($log->ip);
            $row[] = $log->user_agent ? esc($agent->getBrowser()) . " <span data-bs-toggle='tooltip' data-bs-title='" . esc($log->user_agent) . "'><i style='font-size:1.2em' class='bi bi-info-circle'></i></span>" : 'Không xác định';
            $row[] = esc($log->created_at);
            $data[] = $row;
        }
 
        $output = array(
            "draw" => $draw,
            "recordsTotal" => $userLogModel->countAll($this->user_session['company_id']),
            "recordsFiltered" => $userLogModel->countFiltered($this->user_session['company_id']),
            "data" => $data,
        );
        return $this->response->setJSON($output);
    
    
    }
 
    
}