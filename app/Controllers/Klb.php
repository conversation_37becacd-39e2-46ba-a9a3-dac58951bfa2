<?php

namespace App\Controllers;
use App\Models\BankAccountModel;
use App\Models\BankSubAccountModel;
use App\Models\TransactionsModel;
use App\Models\SimModel;
use App\Models\UserModel;
use App\Models\BankModel;
use App\Models\NotificationTelegramModel;
use App\Models\NotificationTelegramQueueModel;
use App\Models\WebhooksModel;
use App\Models\WebhooksLogModel;
use App\Models\NotificationLarkMessengerModel;
use App\Models\NotificationLarkMessengerQueueModel;
use App\Models\SapoModel;
use App\Models\HaravanModel;
use App\Models\UserPermissionBankModel;
use App\Models\UserPermissionBankSubModel;
use App\Models\ConfigurationModel;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use Psr\Log\LoggerInterface;

class Klb extends BaseController
{
    public function initController(RequestInterface $request, ResponseInterface $response, LoggerInterface $logger)
    {
        parent::initController($request, $response, $logger);

        is_bank_supported_by_promotion(17, $this->company_details->company_id);
    }

    public function details($id='')
    { 
        $data = [
            'page_title' => 'Ngân hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        if(!has_permission('BankAccount', 'can_view_all'))
            show_404();

        if(!is_numeric($id))
            show_404();

        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $transactionsModel = slavable_model(TransactionsModel::class, 'KLB');
        $simCompanyModel = model(SimCompanyModel::class);


        $data['bank_account_details'] = $bankAccountModel->select("tb_autopay_bank_account.id,tb_autopay_bank_account.account_number,tb_autopay_bank_account.label,tb_autopay_bank_account.active,tb_autopay_bank_account.created_at,tb_autopay_bank.full_name,tb_autopay_bank.brand_name, tb_autopay_bank_account.account_number,tb_autopay_bank_account.account_holder_name,tb_autopay_bank.icon_path,tb_autopay_bank.logo_path,tb_autopay_sim.sim_phonenumber, tb_autopay_bank_account.last_transaction, tb_autopay_bank_account.accumulated,tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.bank_id,tb_autopay_bank_account.bank_sms, tb_autopay_bank_account.bank_sms_connected, tb_autopay_bank_account.bank_api, tb_autopay_bank_account.bank_api_connected")->join("tb_autopay_bank","tb_autopay_bank.id=tb_autopay_bank_account.bank_id")->join("tb_autopay_sim","tb_autopay_sim.id=tb_autopay_bank_account.sim_id","left")->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id'], 'tb_autopay_bank_account.id' => $id])->get()->getRow();

        if(!is_object($data['bank_account_details']))
            show_404();
        
        $data['count_transactions'] = $transactionsModel->where(['bank_account_id' => $data['bank_account_details']->id, 'deleted_at' => NULL])->countAllResults();


        if($data['bank_account_details']->bank_id == 17 && $data['bank_account_details']->bank_api_connected == 0 && $data['bank_account_details']->bank_api == 1) {
            return redirect()->to(base_url('klb/step2/' . $data['bank_account_details']->id));
        }


        $data['count_subs'] = $bankSubAccountModel->where(['bank_account_id' => $id, 'deleted_at' => NULL])->countAllResults();

        $data['bank_sms_info'] = get_bank_sms_info($data['bank_account_details']->brand_name);

         // check dedicated sim
         $data['sims'] = $simCompanyModel->select("tb_autopay_sim.id, tb_autopay_sim.sim_phonenumber")->join("tb_autopay_sim","tb_autopay_sim.id=tb_autopay_sim_company.sim_id")->where(['tb_autopay_sim_company.company_id' => $this->user_session['company_id'], "tb_autopay_sim.active" => 1])->orderBy('tb_autopay_sim_company.created_at','DESC')->get()->getResult();
             
        $data['canDeleteBankAccount'] = has_permission('BankAccount', 'can_delete') && $data['bank_account_details']->bank_sms;

        //data QR
        $data['bank_sub_accounts_custom']=[];
        if(!empty($data['bank_account_details'])){
            $data['bank_sub_accounts_custom'] = $bankSubAccountModel->select("tb_autopay_bank_sub_account.id,tb_autopay_bank_sub_account.acc_type,tb_autopay_bank_sub_account.acc_type, tb_autopay_bank.brand_name,tb_autopay_bank.icon_path, tb_autopay_bank_account.account_holder_name, tb_autopay_bank_account.account_number, tb_autopay_bank_sub_account.sub_account, tb_autopay_bank_sub_account.label")
                ->join("tb_autopay_bank_account", "tb_autopay_bank_account.id = tb_autopay_bank_sub_account.bank_account_id")
                ->join("tb_autopay_bank", "tb_autopay_bank.id = tb_autopay_bank_account.bank_id", "left")
                ->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id']])
                ->where('tb_autopay_bank_account.id',$data['bank_account_details']->id) 
                ->get()
                ->getResultArray();
        }


        echo view('templates/autopay/header',$data);
        if ($data['bank_account_details']->bank_api == 1)
            echo view('klb/details',$data);
        else
            echo view('bankaccount/details_v2',$data);
        echo view('templates/autopay/footer',$data);

    }
    
    public function step1()
    { 
        $data = [
            'page_title' => 'Ngân hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];


        if(!has_permission('BankAccount', 'can_add'))
            show_404();

        
        echo view('templates/autopay/header',$data);
        echo view('klb/step1',$data);
        echo view('templates/autopay/footer',$data);

    }

    public function step2($id='')
    { 
        $data = [
            'page_title' => 'Ngân hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        if(!has_permission('BankAccount', 'can_add'))
            show_404();

        if(!is_numeric($id))
            show_404();
 
        $bankAccountModel = model(BankAccountModel::class);
        $transactionsModel = slavable_model(TransactionsModel::class, 'KLB');


        $data['bank_account_details'] = $bankAccountModel->select("tb_autopay_bank_account.id,tb_autopay_bank_account.account_number,tb_autopay_bank_account.label,tb_autopay_bank_account.active,tb_autopay_bank_account.created_at,tb_autopay_bank.brand_name, tb_autopay_bank_account.account_number,tb_autopay_bank_account.account_holder_name,tb_autopay_bank.icon_path,tb_autopay_bank.logo_path, tb_autopay_bank_account.last_transaction, tb_autopay_bank_account.accumulated,tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.bank_id")->join("tb_autopay_bank","tb_autopay_bank.id=tb_autopay_bank_account.bank_id")->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id'], 'tb_autopay_bank_account.id' => $id])->get()->getRow();

        if(!is_object($data['bank_account_details']))
            show_404();
        
        $data['count_transactions'] = $transactionsModel->where(['account_number' => $data['bank_account_details']->account_number, 'deleted_at' => NULL])->countAllResults();

        if($data['bank_account_details']->bank_api_connected == 1) {
            set_alert('error','Tài khoản này đã mở API rồi. Bạn không cần phải liên kết lại.');
            return redirect()->to('klb/step3/' . $id);
        }


        
        echo view('templates/autopay/header',$data);
        echo view('klb/step2',$data);
        echo view('templates/autopay/footer',$data);

    }

    public function step3($id='')
    { 
        $data = [
            'page_title' => 'Ngân hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];


        if(!has_permission('BankAccount', 'can_add'))
            show_404();

        if(!is_numeric($id))
            show_404();

        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $transactionsModel = slavable_model(TransactionsModel::class, 'KLB');


        $data['bank_account_details'] = $bankAccountModel->select("tb_autopay_bank_account.id,tb_autopay_bank_account.account_number,tb_autopay_bank_account.label,tb_autopay_bank_account.active,tb_autopay_bank_account.created_at,tb_autopay_bank.brand_name, tb_autopay_bank_account.account_number,tb_autopay_bank_account.account_holder_name,tb_autopay_bank.icon_path,tb_autopay_bank.logo_path, tb_autopay_bank_account.last_transaction, tb_autopay_bank_account.accumulated,tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.bank_id")->join("tb_autopay_bank","tb_autopay_bank.id=tb_autopay_bank_account.bank_id")->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id'], 'tb_autopay_bank_account.id' => $id])->get()->getRow();

        if(!is_object($data['bank_account_details']))
            show_404();

        $count_va = $bankSubAccountModel->where(['bank_account_id' => $id, 'acc_type' => 'Real', 'va_order' >=1])->countAllResults();
        

        if($count_va > 0) {
            set_alert('error','Bạn đã tạo VA rồi');
            return redirect()->to('klb/step4/' . $id);
        }


        
        echo view('templates/autopay/header',$data);
        echo view('klb/step3',$data);
        echo view('templates/autopay/footer',$data);

    }

    public function step4($id='')
    { 
        $data = [
            'page_title' => 'Ngân hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];


        if(!has_permission('BankAccount', 'can_add'))
            show_404();

        if(!is_numeric($id))
            show_404();

        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);

        $transactionsModel = slavable_model(TransactionsModel::class, 'KLB');


        $data['bank_account_details'] = $bankAccountModel->select("tb_autopay_bank_account.id,tb_autopay_bank_account.account_number,tb_autopay_bank_account.label,tb_autopay_bank_account.active,tb_autopay_bank_account.created_at,tb_autopay_bank.brand_name, tb_autopay_bank_account.account_number,tb_autopay_bank_account.account_holder_name,tb_autopay_bank.icon_path,tb_autopay_bank.logo_path, tb_autopay_bank_account.last_transaction, tb_autopay_bank_account.accumulated,tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.bank_id")->join("tb_autopay_bank","tb_autopay_bank.id=tb_autopay_bank_account.bank_id")->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id'], 'tb_autopay_bank_account.id' => $id])->get()->getRow();

        if(!is_object($data['bank_account_details']))
            show_404();
        
        $data['va_details'] =  $bankSubAccountModel->where(['bank_account_id' => $id, 'va_order>=' => 1,'acc_type' => 'Real'])->orderBy('id','DESC')->get()->getRow();
        
        echo view('templates/autopay/header',$data);
        echo view('klb/step4',$data);
        echo view('templates/autopay/footer',$data);

    }

    public function settings($id)
    { 
        $data = [
            'page_title' => 'Cấu hình',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
        ];

        if(! has_permission('BankAccount', 'can_add')) {
            show_404();
        }

        if(! is_numeric($id)) {
            show_404();
        }

        $bankAccount = model(BankAccountModel::class)
            ->select('tb_autopay_bank_account.id,tb_autopay_bank_account.account_number,tb_autopay_bank_account.label,tb_autopay_bank_account.active,tb_autopay_bank_account.created_at,tb_autopay_bank.brand_name, tb_autopay_bank_account.account_number,tb_autopay_bank_account.account_holder_name,tb_autopay_bank.icon_path,tb_autopay_bank.logo_path, tb_autopay_bank_account.last_transaction, tb_autopay_bank_account.accumulated,tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.bank_id')
            ->join('tb_autopay_bank', 'tb_autopay_bank.id = tb_autopay_bank_account.bank_id')
            ->where('tb_autopay_bank_account.company_id', $this->user_session['company_id'])
            ->where('tb_autopay_bank_account.id', $id)
            ->first();

        if(! $bankAccount) {
            show_404();
        }

        $va = model(BankSubAccountModel::class)
            ->where('bank_account_id', $id)
            ->where('va_order >=', 1)
            ->where('acc_type', 'Real')
            ->orderBy('id','DESC')
            ->first();

        if (! $va) {
            return redirect()->to(base_url('klb/step3/' . $bankAccount->id));
        }

        $data['bankAccount'] = $bankAccount;
        $data['settings'] = bank_account_settings($bankAccount->id);
        $data['va_success'] = $this->request->getGet('va_success');

        echo view('templates/autopay/header', $data);
        echo view('klb/settings', $data);
        echo view('templates/autopay/footer', $data);
    }

    public function ajax_otp_get() {
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        if(!has_permission('BankAccount', 'can_add'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn chưa được cấp quyền để thực hiện thao tác này"));
 
        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'id' => ['label' => 'ID','rules' => 'required|integer|is_natural'],
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }  
        $bankAccountModel = model(BankAccountmodel::class);
        $bank_account_id = $this->request->getPost('id');

        $bank_account_details = $bankAccountModel->where(['id'=>$bank_account_id, 'company_id' => $this->user_session['company_id']])->get()->getRow();
        
        if(!is_object($bank_account_details))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy tài khoản ngân hàng này"));

          
        $klb_config = new \Config\Klb();
        $kPayPacker = new \src\KPayPacker(
            $klb_config->KlbClientID,
            $klb_config->KlbEncryptKey,
            $klb_config->KlbSecretKey,
            $klb_config->KlbAcceptTimeDiff,
            $klb_config->KlbHost,
        );
        try {
            $payClient = new \src\KPayClient($kPayPacker);

            $checkRequest = new \src\verifyaccountno\request\LinkAccountRequest($bank_account_details->account_number);
            $response = $payClient->linkAccountNo($checkRequest);

        } catch(\Throwable $e){
            return $this->response->setJSON(array("status"=>false,"message"=>"Tài khoản không tồn tại hoặc không thể gửi mã OTP. Vui lòng liên hệ SePay để được hỗ trợ"));
        }
        
        if(isset($response->sessionId)) {

            add_user_log(array('data_id'=>$bank_account_id,'company_id' => $this->user_session['company_id'], 'data_type'=>'bank_account_get_otp','description'=>'Lấy OTP liên kết tài khoản','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));

            return $this->response->setJSON(array("status"=>true,"session_id" => $response->sessionId));
        
        } else {
            return $this->response->setJSON(array("status"=>false,"message"=>"Tài khoản không tồn tại hoặc không thể gửi mã OTP. Vui lòng liên hệ SePay để được hỗ trợ"));

        }
            

    
    }


    public function ajax_otp_submit() {
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        if(!has_permission('BankAccount', 'can_add'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn chưa được cấp quyền để thực hiện thao tác này"));
 
        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'id' => ['label' => 'ID','rules' => 'required|integer|is_natural'],
            'otp_bank_connect' => ['label' => 'OTP','rules' => "required|max_length[100]"],
            'session_id' => ['label' => 'Session ID','rules' => "required"],
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }  
        $bankAccountModel = model(BankAccountmodel::class);
        $bank_account_id = $this->request->getPost('id');
        $session_id = $this->request->getPost('session_id');
        $otp = trim($this->request->getPost('otp_bank_connect'));

        $bank_account_details = $bankAccountModel->where(['id'=>$bank_account_id, 'company_id' => $this->user_session['company_id']])->get()->getRow();
        
        if(!is_object($bank_account_details))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy tài khoản ngân hàng này"));

          
        $klb_config = new \Config\Klb();
        $kPayPacker = new \src\KPayPacker(
            $klb_config->KlbClientID,
            $klb_config->KlbEncryptKey,
            $klb_config->KlbSecretKey,
            $klb_config->KlbAcceptTimeDiff,
            $klb_config->KlbHost,
        );
        try {
            $payClient = new \src\KPayClient($kPayPacker);

            $checkRequest = new \src\verifyaccountno\request\VerifyLinkAccountRequest($session_id, $bank_account_details->account_number, $otp);

            $response = $payClient->verifyLinkAccountNo($checkRequest);

        } catch(\Throwable $e){
            log_message("error", "KLB API Verify Error : " . json_decode($e->getMessage()) . " - code: " . json_encode($e->getCode()));
            return $this->response->setJSON(array("status"=>false,"message"=>"Không thể kết nối với ngân hàng. Vui lòng liên hệ SePay để được hỗ trợ."));
        }
           
        if(isset($response) && $response->success == true) {
            $bankAccountModel->set(['bank_api_connected' => 1])->update($bank_account_id);
        
        
            set_alert('success',"Liên kết ngân hàng thành công!");
    
            add_user_log(array('data_id'=>$bank_account_id,'company_id' => $this->user_session['company_id'], 'data_type'=>'bank_account_linked','description'=>'Liên kết tài khoản ngân hàng','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));

            $bankSubAccountConfig = get_configuration('BankSubAccount');

            if($bankSubAccountConfig == "off") {
                $configurationModel = model(ConfigurationModel::class);
                $configurationModel->set([
                    'value' => "on",
                ])->where(['company_id' => $this->user_session['company_id'],'setting' => 'BankSubAccount'])->update();
        
            }

            return $this->response->setJSON(array("status"=>true));
        } else {
            return $this->response->setJSON(array("status"=>false, "message" => "OTP bạn điền không chính xác, vui lòng thử lại."));

        }
 
    }

    public function ajax_bank_account_add() {

        if ($this->request->getMethod(true) != 'POST')
            return '';


        if(!has_permission('BankAccount', 'can_add'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền thêm tài khoản ngân hàng"));
 
        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'account_number' => ['label' => 'Số tài khoản', 'rules' => 'integer|greater_than[1]|min_length[3]|max_length[20]|is_natural'],
            'label' => ['label' => 'Tên gợi nhớ', 'rules' => "max_length[100]"],
        ],
        [ 
            'account_number' => [
                'is_unique'=>'Số tài khoản này đã tồn tại trên hệ thống',
            ],
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        } else {
            $bankAccountModel = model(BankAccountModel::class);
            $simModel = model(SimModel::class);
            $simCompanyModel = model(SimCompanyModel::class);

            if ($bankAccountModel->where(['account_number' => trim($this->request->getPost('account_number')), 'bank_id' => 17])->get()->getRow()) {
                return $this->response->setJSON(array('status'=>FALSE,'message'=>'Số tài khoản này đã tồn tại trên hệ thống'));
            }

            $account_number = $this->request->getPost('account_number');         
            $klb_config = new \Config\Klb();
            $kPayPacker = new \src\KPayPacker(
                $klb_config->KlbClientID,
                $klb_config->KlbEncryptKey,
                $klb_config->KlbSecretKey,
                $klb_config->KlbAcceptTimeDiff,
                $klb_config->KlbHost,
            );
            try {
                $payClient = new \src\KPayClient($kPayPacker);
    
                $checkRequest = new \src\verifyaccountno\request\CheckAccountNoRequest($account_number);
                $response = $payClient->checkAccountNo($checkRequest);

                $account_name = $response->getAccountName();
                
                if(!is_object($response) || !$account_name) {    
                    return $this->response->setJSON(array("status"=>false,"message"=>"Có lỗi khi kiểm tra thông tin tài khoản. Vui lòng liên hệ SePay để được hỗ trợ"));
                } 

            } catch(\Throwable $e){
            
                return $this->response->setJSON(array("status"=>false,"message"=>"Có lỗi khi kiểm tra thông tin tài khoản. Vui lòng liên hệ SePay để được hỗ trợ."));
            }

           // $account_holder_name = remove_accents($account_name);

            $data = array(
                'company_id' => $this->user_session['company_id'],
                'account_holder_name' => $account_name,
                'account_number' => $account_number,
                'bank_id' => 17,
                'label' => xss_clean($this->request->getVar('label')),
                'active' => 1,
                'bank_api' =>1,
                'bank_api_connected' =>0,
            );
  
            $result = $bankAccountModel->insert($data);
            
            if($result) {
                add_user_log(array('data_id'=>$result,'company_id' => $this->user_session['company_id'], 'data_type'=>'bank_account_add','description'=>'Thêm tài khoản ngân hàng KLB API','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
 
                return $this->response->setJSON(array("status"=>true,"id" => $result));
            } else {
                add_user_log(array('data_id'=>0,'company_id' => $this->user_session['company_id'], 'data_type'=>'bank_account_add','description'=>'Thêm tài khoản ngân hàng KLB API','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Failed'));
                return $this->response->setJSON(array("status"=>false,"message"=>"Không thể thêm tài khoản ngân hàng. Vui lòng liên hệ SePay để được hỗ trợ."));
            }
                

        }
    }
 


    public function ajax_check_va_trans() {

        if ($this->request->getMethod(true) != 'POST')
            return '';


        if(!has_permission('BankAccount', 'can_add'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền thực hiện thao tác này"));
 
        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'va_id' => ['label' => 'Số tài khoản', 'rules' => 'integer|required'],
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        } else {

            $va_id = $this->request->getVar('va_id');

            if(!is_numeric($va_id))
                return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy VA ID"));


            $bankSubAccountModel = model(BankSubAccountModel::class);
            $transactionsModel = slavable_model(TransactionsModel::class, 'KLB');

            $result =  $bankSubAccountModel->select("tb_autopay_bank_account.account_number, tb_autopay_bank_account.id as `bank_account_id`, tb_autopay_bank_sub_account.sub_account")->join("tb_autopay_bank_account","tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id")->where(['tb_autopay_bank_sub_account.id' => $va_id, "tb_autopay_bank_account.company_id" => $this->user_session['company_id']])->get()->getRow();
            if(!is_object($result))
                return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy VA này"));

            $last_trans = $transactionsModel->select("id, amount_in,account_number,sub_account,transaction_content,transaction_date")->where(['deleted_at' => NULL, 'parser_status' => 'Success', 'bank_account_id' => $result->bank_account_id, 'sub_account' => $result->sub_account])->orderBy('id','DESC')->get()->getRow();

            if(is_object($last_trans))
                return $this->response->setJSON(array("status"=>TRUE, 'last_transaction' => $last_trans));
            else
                return $this->response->setJSON(array("status"=>FALSE));



        }
    }


    public function ajax_create_va() {

        if ($this->request->getMethod(true) != 'POST')
            return '';


        if(!has_permission('BankAccount', 'can_add'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền thêm tài khoản ngân hàng"));
 
        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'bank_account_id' => ['label' => 'Tài khoản chính', 'rules' => 'integer|is_natural'],
            'label' => ['label' => 'Tên gợi nhớ', 'rules' => "max_length[100]"],
          ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }  

        $bankAccountModel = model(BankAccountModel::class);

        $bank_account_id = $this->request->getVar('bank_account_id');
 
        $bank_account_details = $bankAccountModel->where(['company_id' =>$this->user_session['company_id'], 'id' =>$bank_account_id, 'bank_id'=>17])->get()->getRow();

        if(!is_object($bank_account_details))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Tài khoản chính không tồn tại"));
 
 
        $bankSubAccountModel = model(BankSubAccountModel::class);

        $result = $bankSubAccountModel->select("count(id) as `numrows`")->where(['bank_account_id'=>$bank_account_id])->get()->getRow();

        if($result->numrows >= 100)
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn đã tạo quá số lượng VA cho phép. Vui lòng liên hệ SePay để được hỗ trợ"));

        // get order id
        /*
        $count_sub = $bankSubAccountModel->select("count(id) as `numrows`")->where(['bank_account_id' => $bank_account_id])->get()->getRow();
        $order = $count_sub->numrows + 1;
        */
        $configurationModel = model(ConfigurationModel::class);
        $config_result = $configurationModel->where(['setting' => 'KLBVaOrderLastNumber'])->get()->getRow();
        if(is_object($config_result))
            $order = $config_result->value + 1;
        else
            $order = 1;
        
        $klb_config = new \Config\Klb();
        $kPayPacker = new \src\KPayPacker(
            $klb_config->KlbClientID,
            $klb_config->KlbEncryptKey,
            $klb_config->KlbSecretKey,
            $klb_config->KlbAcceptTimeDiff,
            $klb_config->KlbHost,
        );
        try {
            $payClient = new \src\KPayClient($kPayPacker);

          //  $order = 1121;//0-99999
            $timeout = 0;
            $fixAmount = 0;
            $fixContent = ''; 
            $bankAccountNo = $bank_account_details->account_number;
            $checkRequest = new \src\virtualaccount\request\EnableVirtualAccountRequest($order,  $timeout,  $fixAmount,  $fixContent,  $bankAccountNo);
            $response = $payClient->enableVirtualAccount($checkRequest);

            if(is_object($response) && isset($response->virtualAccount)) {
                $result = $bankSubAccountModel->insert(['bank_account_id' => $bank_account_id,'sub_account' => $response->virtualAccount, 'va_order' => $order, 'acc_type' => 'Real', 'sub_holder_name' => $bank_account_details->account_holder_name,'label' => xss_clean($this->request->getVar('label'))]);

                $configurationModel->set(['value' => $order])->where(['setting' => 'KLBVaOrderLastNumber', 'company_id' => 0])->update();

                return $this->response->setJSON(array("status"=>true,'id' => $result));

            } else {
                return $this->response->setJSON(array("status"=>false,"message"=>"Có lỗi khi khởi tạo VA. Vui lòng liên hệ SePay để được hỗ trợ."));
            }

        } catch(\Throwable $e){
            return $this->response->setJSON(array("status"=>false,"message"=>"Có lỗi khi kết nối với ngân hàng. Vui lòng liên hệ SePay để được hỗ trợ."));
        }
 
    
    }


    public function ajax_va_update() {
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        if(!has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền sửa tài khoản phụ này"));
 
        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'id' => 'required|integer|is_natural',
            'label' => ['label' => 'Tên gợi nhớ', 'rules' => "max_length[100]"],
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }   
        $bankSubAccountModel = model(BankSubAccountmodel::class);
        $sub_account_id = $this->request->getPost('id');

        $bankSubAccountModel = model(BankSubAccountmodel::class);
        
        $result = $bankSubAccountModel->select("tb_autopay_bank_sub_account.id,tb_autopay_bank_sub_account.sub_account,tb_autopay_bank_sub_account.acc_type,tb_autopay_bank_sub_account.bank_account_id,tb_autopay_bank_sub_account.label")->join("tb_autopay_bank_account","tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id")->where(['tb_autopay_bank_sub_account.id'=>$sub_account_id, 'tb_autopay_bank_account.company_id' => $this->user_session['company_id']])->get()->getRow();
          
        if(!is_object($result))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy tài khoản phụ này"));

        $data = array(
            'label' => xss_clean($this->request->getVar('label')),
        );
            
        $result = $bankSubAccountModel->set($data)->where("id",$sub_account_id)->update();
        
        if($result) { 
            return $this->response->setJSON(array("status"=>true));
        } else {
            return $this->response->setJSON(array("status"=>false,"message"=>"Không thể cập nhật!"));
        }
            

        
    }

     

    public function ajax_disable_va() {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền sửa tài khoản ngân hàng"));
 
 
        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'id' => 'required|integer|is_natural',
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }
        $sub_account_id = $this->request->getPost('id');

        $bankSubAccountModel = model(BankSubAccountmodel::class);
        $sub_account_details = $bankSubAccountModel->join("tb_autopay_bank_account","tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id")->where(['tb_autopay_bank_sub_account.id'=>$sub_account_id,'tb_autopay_bank_account.company_id'=>$this->user_session['company_id']])->get()->getRow();

        if(!is_object($sub_account_details))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Không tìm thấy tài khoản VA này"));



        $order = $sub_account_details->va_order;
        $klb_config = new \Config\Klb();
        $kPayPacker = new \src\KPayPacker(
            $klb_config->KlbClientID,
            $klb_config->KlbEncryptKey,
            $klb_config->KlbSecretKey,
            $klb_config->KlbAcceptTimeDiff,
            $klb_config->KlbHost,
        );
        try {
            $payClient = new \src\KPayClient($kPayPacker);

            $checkRequest = new \src\virtualaccount\request\DisableVirtualAccountRequest($order);
            $response = $payClient->disableVirtualAccount($checkRequest);
            
            if(is_object($response) && isset($response->success) && $response->success == true) {
                $bankSubAccountModel->set(['va_active' => 0])->where(['id' => $sub_account_id])->update();
    
                return $this->response->setJSON(array("status"=>true));
             
            } else {
                return $this->response->setJSON(array("status"=>false,"message"=>"Có lỗi khi vô hiệu hoá VA. Vui lòng liên hệ SePay để được hỗ trợ."));
            } 

        } catch(\Throwable $e){
            return $this->response->setJSON(array("status"=>false,"message"=>"Có lỗi kết nối khi vô hiệu hoá VA. Vui lòng liên hệ SePay để được hỗ trợ."));
        }

     

    
    }


    public function ajax_enable_va() {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền sửa tài khoản ngân hàng"));
 
 
        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'id' => 'required|integer|is_natural',
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }
        $sub_account_id = $this->request->getPost('id');

        $bankSubAccountModel = model(BankSubAccountmodel::class);
        $sub_account_details = $bankSubAccountModel->select("tb_autopay_bank_sub_account.va_order, tb_autopay_bank_sub_account.bank_account_id, tb_autopay_bank_account.account_number")->join("tb_autopay_bank_account","tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id")->where(['tb_autopay_bank_sub_account.id'=>$sub_account_id,'tb_autopay_bank_account.company_id'=>$this->user_session['company_id']])->get()->getRow();

      
        if(!is_object($sub_account_details))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Không tìm thấy tài khoản VA này"));



        $order = $sub_account_details->va_order;
         
        $klb_config = new \Config\Klb();
        $kPayPacker = new \src\KPayPacker(
            $klb_config->KlbClientID,
            $klb_config->KlbEncryptKey,
            $klb_config->KlbSecretKey,
            $klb_config->KlbAcceptTimeDiff,
            $klb_config->KlbHost,
        );
        try {
            $payClient = new \src\KPayClient($kPayPacker);

            $timeout = 0;
            $fixAmount = 0;
            $fixContent = ''; 
            $bankAccountNo = $sub_account_details->account_number;
            $checkRequest = new \src\virtualaccount\request\EnableVirtualAccountRequest($order,  $timeout,  $fixAmount,  $fixContent,  $bankAccountNo);
            $response = $payClient->enableVirtualAccount($checkRequest);

            if(is_object($response) && isset($response->virtualAccount)) {
                $bankSubAccountModel->set(['va_active' => 1])->where(['id' => $sub_account_id])->update();
    
    
                return $this->response->setJSON(array("status"=>true));
             
            } else {
               
                return $this->response->setJSON(array("status"=>false,"message"=>"Có lỗi khi mở lại VA. Vui lòng liên hệ SePay để được hỗ trợ!."));
            } 

        } catch(\Throwable $e){

            return $this->response->setJSON(array("status"=>false,"message"=>"Có lỗi kết nối khi mở lại VA. Vui lòng liên hệ SePay để được hỗ trợ."));
        }

     

    
    }




    public function ajax_va_list() {

        if(!has_permission('BankAccount', 'can_view_all'))
            show_404();
     
        if ($this->request->getMethod(true) != 'POST')
            return '';

        $parrent_account_id = $this->request->getGet('parrent_id');

        if(!is_numeric($parrent_account_id))
            $parrent_account_id = FALSE;
 
        $bankSubAccountModel = model(BankSubAccountModel::class);

 
        $bank_accounts = $bankSubAccountModel->getDatatables($this->user_session['company_id'], $parrent_account_id);
         
        $data = array();

        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');

        $can_edit = has_permission('BankAccount', 'can_edit');
        $can_delete = has_permission('BankAccount', 'can_delete');
 
        foreach ($bank_accounts as $account) {

            $no++;
            $row = array();

            $actions_btn = '';

            if($can_edit) {
                $actions_btn = "<a href='javascript:;' onclick='edit_va(" . $account->id. ")' class='btn btn-sm btn-outline-warning ms-2 me-2 mt-2'><i class='bi bi-pencil'></i> Sửa</a>";
            
                if($account->va_active == 1)                  
                    $actions_btn = $actions_btn .  "<button type='button' onclick='disable_va(" . $account->id. ")' class='btn btn-sm btn-outline-danger ms-2 mt-2 btn-disable-va-".$account->id."'><i class='bi bi-toggle-off'></i> Vô hiệu hoá VA</button>";
                else 
                    $actions_btn = $actions_btn .  "<button type='button' onclick='enable_va(" . $account->id. ")' class='btn btn-sm btn-outline-primary ms-2 mt-2 btn-enable-va-".$account->id."'><i class='bi bi-toggle-off'></i> Bật lại VA</button>";
            }
                 
            $row[] = $no;
            $row[] = $account->id;
            $row[] = "<a href='javascript:;' onclick='view_va("  . $account->id . ")'>" . esc($account->sub_account) . "</a>";

          
            $row[] = esc($account->sub_holder_name);

            if($account->va_active == 1)
                $row[] = "<span class='text-success'>Hoạt động</span>";
            else
                $row[] = "<span class='text-danger'>Tạm ngưng</span>";
            $row[] = esc($account->label);
           

            $row[] = esc($account->created_at);
            $row[] = $actions_btn;
       
            $data[] = $row;
        }
 
        $output = array(
            "draw" => $draw,
            "recordsTotal" => $bankSubAccountModel->countAll($this->user_session['company_id'], $parrent_account_id),
            "recordsFiltered" => $bankSubAccountModel->countFiltered($this->user_session['company_id'], $parrent_account_id),
            "data" => $data,
        );
        return $this->response->setJSON($output);
    
    
    }


    public function ajax_view_va($id='') {
        
      
        if(!has_permission('BankAccount', 'can_view_all'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền xem tài khoản ngân hàng"));
        
        if(!is_numeric($id)) 
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"ID tài khoản ngân ảo hợp lệ"));

        $bankSubAccountModel = model(BankSubAccountmodel::class);
        $transactionsModel = slavable_model(TransactionsModel::class, 'KLB');

        $data['va_details'] = $bankSubAccountModel->select("tb_autopay_bank_sub_account.id,tb_autopay_bank_sub_account.sub_account,tb_autopay_bank_sub_account.acc_type,tb_autopay_bank_sub_account.bank_account_id,tb_autopay_bank_sub_account.label,tb_autopay_bank_account.account_number,tb_autopay_bank_account.label,tb_autopay_bank_account.active,tb_autopay_bank_account.created_at,tb_autopay_bank.brand_name, tb_autopay_bank_account.account_number,tb_autopay_bank_account.account_holder_name,tb_autopay_bank.icon_path,tb_autopay_bank.logo_path,tb_autopay_bank.full_name, tb_autopay_bank_sub_account.sub_holder_name,tb_autopay_bank_sub_account.va_active")->join("tb_autopay_bank_account","tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id")->join("tb_autopay_bank","tb_autopay_bank.id=tb_autopay_bank_account.bank_id")->where(['tb_autopay_bank_sub_account.id'=>$id, 'tb_autopay_bank_account.company_id' => $this->user_session['company_id'],'tb_autopay_bank_account.bank_id' => 17])->get()->getRow();

        $data['count_transactions'] = $transactionsModel->where(['sub_account' => $data['va_details']->sub_account])->countAllResults();

        $data['last_transaction'] = $transactionsModel->where(['sub_account' => $data['va_details']->sub_account, 'accumulated!=' => ''])->orderBy('transaction_date','DESC')->get()->getRow();
    
        if($data['va_details']) {
            $html = view('klb/va_view', $data);
            return $this->response->setJSON(["status"=>TRUE, "html"=>$html]);
        }
        else
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy tài khoản ảo này"));
    }


    public function ajax_check_account_number() {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền sửa tài khoản ngân hàng"));
 
 
        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            //'account_number' => ['label' => 'Số tài khoản', 'rules' => 'integer|greater_than[1]|min_length[3]|max_length[20]|is_natural|is_unique[tb_autopay_bank_account.account_number]'],
            'account_number' => ['label' => 'Số tài khoản', 'rules' => 'integer|greater_than[1]|min_length[3]|max_length[20]|is_natural'],
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }
        $account_number = $this->request->getPost('account_number');         
        $klb_config = new \Config\Klb();
        $kPayPacker = new \src\KPayPacker(
            $klb_config->KlbClientID,
            $klb_config->KlbEncryptKey,
            $klb_config->KlbSecretKey,
            $klb_config->KlbAcceptTimeDiff,
            $klb_config->KlbHost,
        );
        try {
            $payClient = new \src\KPayClient($kPayPacker);
 
            $checkRequest = new \src\verifyaccountno\request\CheckAccountNoRequest($account_number);
            $response = $payClient->checkAccountNo($checkRequest);

            $account_name = $response->getAccountName();
            
            if(is_object($response) && $account_name) {    
    
                return $this->response->setJSON(array("status"=>true, "account_name" => $account_name, "account_number" => esc($account_number)));
             
            } else {
               
                return $this->response->setJSON(array("status"=>false,"message"=>"Có lỗi khi kiểm tra thông tin tài khoản. Vui lòng liên hệ SePay để được hỗ trợ"));
            } 

        } catch(\Throwable $e){
          
            return $this->response->setJSON(array("status"=>false,"message"=>"Có lỗi khi kiểm tra thông tin tài khoản. Vui lòng liên hệ SePay để được hỗ trợ."));
        }

     

    
    }

    public function ajax_bank_account_update() {
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        if(!has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền sửa tài khoản ngân hàng"));
 
        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'id' => 'required|integer|is_natural',
            'label' => ['label' => 'Tên gợi nhớ', 'rules' => "max_length[100]"],
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }  
        $bankAccountModel = model(BankAccountmodel::class);
        $bank_account_id = $this->request->getPost('id');

        $result = $bankAccountModel->where(['id'=>$bank_account_id, 'company_id' => $this->user_session['company_id']])->get()->getRow();
        
        if(!is_object($result))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy tài khoản ngân hàng này"));

        $data = array(
            'label' => trim(xss_clean($this->request->getVar('label'))),
        );

        
           
        $result = $bankAccountModel->set($data)->where("id",$bank_account_id)->update();
        
        if($result) { 
            add_user_log(array('data_id'=>$bank_account_id,'company_id' => $this->user_session['company_id'], 'data_type'=>'bank_account_update','description'=>'Sửa tài khoản ngân hàng','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
            return $this->response->setJSON(array("status"=>true));
        } else {
            return $this->response->setJSON(array("status"=>false,"message"=>"Không thể cập nhật tài khoản ngân hàng!"));
        }
            

    
    }

  
}