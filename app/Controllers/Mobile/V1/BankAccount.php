<?php

namespace App\Controllers\Mobile\V1;

use App\Models\BankAccountModel;
use App\Controllers\Mobile\V1\BaseController;

class BankAccount extends BaseController
{
    public function index()
    {
        if (!$this->authorizeFeaturePermission($this->user, 'BankAccount', 'can_view_all')) {
            return $this->respond([
                'data' => [],
                'meta' => [
                    'per_page' => 20,
                    'total' => 0,
                    'has_more' => false,
                    'current_page' => 1,
                    'page_count' => 0,
                ]
            ]);
        }

        $filter = $this->request->getGet('filter');
        $sort = $this->request->getGet('sort');
        $per_page = $this->request->getGet('per_page');

        $data = [
            'filter' => [
                'bank_id' => trim(xss_clean($filter['bank_id'] ?? '')),
            ],
            'sort' => [
                'created_at' => $sort['created_at'] ?? ''
            ],
            'per_page' => is_numeric($per_page) && $per_page > 0 ? $per_page : 20,
            'q' => trim(xss_clean($this->request->getGet('q'))),
        ];

        $bankAccountModel = model(BankAccountModel::class);
        $builder = $bankAccountModel->select(['id', 'bank_id', 'account_holder_name', 'account_number', 'accumulated', 'last_transaction', 'label', 'bank_sms', 'bank_sms_connected', 'bank_api', 'bank_api_connected', 'active', 'created_at', 'updated_at'])
            ->where([
                'company_id' => $this->user->company_id,
                'merchant_id' => null,
                'deleted_at' => null,
            ]);

        if (!$this->isAdminUser($this->user)) {
            $builder->whereIn('id', array_map(function($permission) {
                return $permission->bank_account_id;
            }, count($this->user->bank_account_permissions) > 0 ? $this->user->bank_account_permissions : [0]));
        }

        if ($data['filter']['bank_id']) {
            $builder->where('bank_id', $data['filter']['bank_id']);
        }

        if ($data['sort']['created_at']) {
            $builder->orderBy('created_at', $data['sort']['created_at'] === 'asc' ? 'asc' : 'desc');
        }

        if ($data['q']) {
            $builder->groupStart()
                ->like('account_holder_name', $data['q'])
                ->orLike('account_number', $data['q'])
                ->orLike('label', $data['q'])
            ->groupEnd();
        }

        return $this->respond([
            'data' => array_map(function($bankAccount) {
                return $this->resolveBankAccountData($this->user, $bankAccount);
            }, $builder->paginate($data['per_page'])),
            'meta' => [
                'per_page' => $bankAccountModel->pager->getPerPage(),
                'total' => $bankAccountModel->pager->getTotal(),
                'has_more' => $bankAccountModel->pager->hasMore(),
                'current_page' => $bankAccountModel->pager->getCurrentPage(),
                'page_count' => $bankAccountModel->pager->getPageCount(),
            ]
        ]);
    }

    public function details($id = null)
    {
        if (!$id || !is_numeric($id)) {
            return $this->respondNotFoundResponse();
        }

        if (!$this->authorizeFeaturePermission($this->user, 'BankAccount', 'can_view_all')) {
            return $this->respondUnauthorizedResponse();
        }

        $bankAccount = model(BankAccountModel::class)->select(['id', 'bank_id', 'account_holder_name', 'account_number', 'accumulated', 'last_transaction', 'label', 'bank_sms', 'bank_sms_connected', 'bank_api', 'bank_api_connected', 'active', 'created_at', 'updated_at'])
            ->where([
                'id' => $id,
                'company_id' => $this->user->company_id,
                'merchant_id' => null,
                'deleted_at' => null,
            ])->first();

        if (!$bankAccount || !$this->authorizeBankAccountPermission($this->user, $bankAccount->id)) {
            return $this->respondNotFoundResponse(static::BANK_ACCOUNT_ID_NOT_EXIST_MESSAGE);
        }

        return $this->respond([
            'data' => $this->resolveBankAccountData($this->user, $bankAccount)
        ]);
    }

    protected function resolveBankAccountData($user, $bankAccount)
    {
        if ($this->authorizeBankAccountPermission($user, $bankAccount->id, 'hide_accumulated')) {
            $bankAccount->accumulated = '***';
        }

        return $bankAccount;
    }
}
