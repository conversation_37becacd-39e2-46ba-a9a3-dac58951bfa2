<?php

namespace App\Controllers\Mobile\V1;

use Config\Mobile;
use App\Models\UserModel;
use CodeIgniter\Controller;
use Psr\Log\LoggerInterface;
use CodeIgniter\HTTP\CLIRequest;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;
use App\Models\UserPermissionBankModel;
use CodeIgniter\HTTP\ResponseInterface;
use App\Models\PersonalAccessTokenModel;
use App\Models\UserPermissionBankSubModel;
use App\Models\UserPermissionFeatureModel;

abstract class BaseController extends Controller
{
    use ResponseTrait;
    
    /**
     * Instance of the main Request object.]
     *
     * @var CLIRequest|IncomingRequest
     */
    protected $request;

    protected $helpers = ['general'];

    protected $mobileConfig;

    protected $user;

    public const BANK_ACCOUNT_ID_NOT_EXIST_MESSAGE = 'ID tài khoản ngân hàng không tồn tại.';
    public const BANK_SUB_ACCOUNT_ID_NOT_EXIST_MESSAGE = 'ID VA không tồn tại.';
    public const COMPANY_ID_NOT_EXIST_MESSAGE = 'ID công ty không tồn tại.';
    public const TRANSACTION_ID_NOT_EXIST_MESSAGE = 'ID giao dịch không tồn tại.';
  
    public function initController(RequestInterface $request, ResponseInterface $response, LoggerInterface $logger)
    {
        parent::initController($request, $response, $logger);

        $this->uri = new \CodeIgniter\HTTP\URI();
        $this->user = $request->user ?? null;
        $this->mobileConfig = $request->mobileConfig;
    }

    protected function authorizeFeaturePermission($user, $feature, $permission)
    {
        if ($this->isAdminUser($user)) return true;

        return count(array_filter($user->feature_permissions, function($featurePermission) use ($feature, $permission) {
            return $featurePermission->feature_slug == $feature && $featurePermission->{$permission} == 1;
        })) > 0;
    }

    // 'hide_amount_out', 'hide_accumulated', 'hide_reference_number', 'hide_transaction_content'
    protected function authorizeBankAccountPermission($user, $bankAccountId, $permission = null)
    {
        if ($this->isAdminUser($user)) return !$permission;

        return count(array_filter($user->bank_account_permissions, function($bankAccountPermission) use ($bankAccountId, $permission) {
            return $bankAccountPermission->bank_account_id == $bankAccountId && (!$permission || ($permission && $bankAccountPermission->{$permission} == 1));
        })) > 0;
    }

    protected function authorizeBankSubAccountPermission($user, $bankSubAccountId)
    {
        if ($this->isAdminUser($user)) return true;

        return count(array_filter($user->bank_sub_account_permissions, function($bankSubAccountPermission) use ($bankSubAccountId) {
            return $bankSubAccountPermission->sub_account_id == $bankSubAccountId;
        })) > 0;
    }

    protected function respondBadRequestResponse($errors = [], $code = 400, $message = 'Bad request')
    {
        return $this->respond([
            'code' => $code,
            'message' => $message,
            'errors' => $errors,
        ], 400);
    }

    protected function respondNoContentResponse($code = 204)
    {
        return $this->respond(null, $code);
    }

    protected function respondNotFoundResponse($message = 'Not found', $code = 404)
    {
        return $this->respond([
            'code' => $code,
            'message' => $message,
        ], 404);
    }

    protected function respondUnauthenticatedResponse($code = 401, $message = 'Unauthenticated')
    {
        return $this->respond([
            'code' => $code,
            'message' => $message,
        ], 401);
    }

    protected function respondUnauthorizedResponse($code = 403, $message = 'Unauthorized')
    {
        return $this->respond([
            'code' => $code,
            'message' => $message,
        ], 401);
    }

    protected function respondServiceUnavailabelResponse() 
    {
        return $this->respond([
            'code' => 503,
            'message' => 'Service unavailable',
        ], 503);
    }

    protected function determineIfMethodIsNotPost()
    {
        return $this->request->getMethod(true) != 'POST';
    }

    protected function respondCompanyNotExistsResponse()
    {
        return $this->respondBadRequestResponse(['company_id' => 'ID công ty không tồn tại']);
    }

    protected function respondBankAccountNotExistsResponse()
    {
        return $this->respondBadRequestResponse(['bank_account_id' => 'ID tài khoản ngân hàng không tồn tại']);
    }

    protected function isAdminUser($user)
    {
        return in_array($user->role, ['SuperAdmin', 'Admin']);
    }
}
