<?php

namespace App\Controllers\Mobile\V1;

use App\Models\UserModel;
use App\Models\CompanyModel;
use App\Models\PersonalAccessTokenModel;
use App\Controllers\Mobile\V1\BaseController;

class User extends BaseController
{
    public function login()
    {
        if ($this->determineIfMethodIsNotPost()) {
            return $this->respondNotFoundResponse();
        }

        $data = (array) $this->request->getJSON();

        $rules = [
            'email' => ['required', 'string'],
            'password' => ['required', 'string']
        ];

        if (! $this->validateData($data, $rules)) {
            return $this->respondBadRequestResponse($this->validator->getErrors());
        }

        $safeCredentials = [
            'email' => trim($data['email']),
            'password' => $data['password'],
        ];

        $ipAddress = $this->request->getIPAddress();
        $userAgent = $this->request->getUserAgent()->getAgentString();

        $userModel = model(UserModel::class);
        $companyModel = model(CompanyModel::class);
        $user = $userModel->select(['id', 'email', 'password'])
            ->where('email', $safeCredentials['email'])
            ->where('active', 1)
            ->where('deleted_at', null)
            ->get()->getRow();

        if (!$user || ($user && !password_verify($safeCredentials['password'], $user->password))) {
            return $this->respondUnauthenticatedResponse(401, 'Thông tin đăng nhập không chính xác.');
        }

        $company = $companyModel
            ->select(['tb_autopay_company.id', 'tb_autopay_company.merchant_id'])
            ->join('tb_autopay_company_user', 'tb_autopay_company_user.company_id = tb_autopay_company.id')
            ->where([
                'tb_autopay_company_user.user_id' => $user->id,
                'tb_autopay_company.merchant_id' => null,
                'tb_autopay_company.deleted_at' => null,
            ])
            ->get()->getRow();

        if (!$company) {
            return $this->respondUnauthenticatedResponse(401, 'Thông tin đăng nhập không chính xác.');
        }

        $personalAccessTokenModel = model(PersonalAccessTokenModel::class);
        $safePersonalAccessTokenData = [
            'name' => $userAgent,
            'company_id' => $company->id,
            'user_id' => $user->id,
            'access_token' => md5($user->id.$user->email.time().bin2hex(random_bytes(7))),
            'ttl' => $this->mobileConfig->personalAccessTokenTtl,
            'expires_at' => $this->mobileConfig->personalAccessTokenTtl 
                ? date('Y-m-d H:i:s', strtotime("+{$this->mobileConfig->personalAccessTokenTtl} seconds")) 
                : null,
        ];
        $personalAccessTokenModel->insert($safePersonalAccessTokenData);

        add_user_log(['data_id' => $user->id, 'company_id' => $company->id, 'data_type'=>'login', 'description' => 'Đăng nhập', 'user_id' => $user->id, 'ip' => $ipAddress, 'user_agent' => $userAgent, 'status' => 'Success']);

        return $this->respond([
            'code' => 200,
            'message' => 'Đăng nhập thành công.',
            'data' => [
                'access_token' => $safePersonalAccessTokenData['access_token'],
                'ttl' => $safePersonalAccessTokenData['ttl'],
            ]
        ]);
    }

    public function logout()
    {
        if ($this->determineIfMethodIsNotPost()) {
            return $this->respondNotFoundResponse();
        }
        
        $personalAccessToken = explode('Bearer ', $this->request->getHeaderLine('Authorization'))[1] ?? '';

        $personalAccessTokenModel = model(PersonalAccessTokenModel::class);
        $personalAccessTokenModel->where('access_token', $personalAccessToken)->delete();

        return $this->respondNoContentResponse();
    }

    public function profile()
    {
        return $this->respond([
            'data' => [
                'id' => $this->user->id,
                'email' => $this->user->email,
                'first_name' => $this->user->firstname,
                'last_name' => $this->user->lastname,
                'role' => $this->user->role,
                'feature_permissions' => $this->user->feature_permissions,
                'bank_account_permissions' => $this->user->bank_account_permissions,
                'bank_sub_account_permissions' => $this->user->bank_sub_account_permissions,
                'created_at' => $this->user->created_at,
                'updated_at' => $this->user->updated_at
            ]
        ]);
    }

    public function changePassword()
    {
        if ($this->determineIfMethodIsNotPost()) {
            return $this->respondNotFoundResponse();
        }

        $data = (array) $this->request->getJSON();

        $rules = [
            'old_password' => [
                'rules' => ['required', 'string'],
                'label' => 'mật khẩu cũ'
            ],
            'new_password' => [
                'rules' => ['required', 'string', 'min_length[8]'],
                'label' => 'mật khẩu mới'
            ],
            'new_password_confirmation' => [
                'rules' => ['required', 'string', 'matches[new_password]'],
                'label' => 'nhập lại mật khẩu mới'
            ],      
        ];

        if (! $this->validateData($data, $rules)) {
            return $this->respondBadRequestResponse($this->validator->getErrors());
        }

        if (! password_verify($data['old_password'], $this->user->password)) {
            return $this->respondBadRequestResponse(['old_password' => 'Mật khẩu cũ không chính xác']);
        }

        $safeChangePasswordData = [
            'new_password' => $data['new_password'],
        ];

        model(UserModel::class)->set(['password' => password_hash($safeChangePasswordData['new_password'], PASSWORD_DEFAULT)])
            ->where([
                'id' => $this->user->id,
                'deleted_at' => null,
            ])->update();
            
        $accessToken = explode('Bearer ', $this->request->getHeaderLine('Authorization'))[1] ?? '';
        

        try {
            $userExists = model(UserModel::class)->where('id', $this->user->id)->countAllResults();
            
            if ($userExists > 0) {
                model(PersonalAccessTokenModel::class)
                    ->where('user_id', $this->user->id)
                    ->where('company_id', $this->user->company_id)
                    ->where('access_token !=', $accessToken)->delete();
            }
        } catch (\Exception $e) {
            log_message('error', '[RevokePersonalAccessToken] ' . $e->getMessage() . ' - ' . $e->getTraceAsString());
        }
        
        $ipAddress = $this->request->getIPAddress();
        $userAgent = $this->request->getUserAgent()->getAgentString();

        add_user_log(['data_id' => $this->user->id, 'company_id' => $this->user->company_id, 'data_type'=>'user_change_password', 'description' => 'Đổi mật khẩu', 'user_id' => $this->user->id, 'ip' => $ipAddress, 'user_agent' => $userAgent, 'status' => 'Success']);
    
        return $this->respond([
            'message' => 'Đổi mật khẩu thành công.'
        ]);
    }
}
