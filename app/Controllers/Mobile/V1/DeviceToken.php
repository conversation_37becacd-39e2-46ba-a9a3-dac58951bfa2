<?php

namespace App\Controllers\Mobile\V1;

use App\Models\FcmDeviceTokenModel;
use App\Models\UserPermissionFeatureModel;
use App\Controllers\Mobile\V1\BaseController;

class DeviceToken extends BaseController
{
    public function create()
    {
        if ($this->determineIfMethodIsNotPost()) {
            return $this->respondNotFoundResponse();
        }

        $data = (array) $this->request->getJSON();

        $rules = [
            'app_version' => ['required', 'string'],
            'platform' => ['required', 'string'],
            'token' => ['required', 'string'],
            'device_name' => ['permit_empty', 'string'],
        ];

        if (! $this->validateData($data, $rules)) {
            return $this->respondBadRequestResponse($this->validator->getErrors());
        }

        $safeDeviceTokenData = [
            'app_version' => $data['app_version'],
            'platform' => $data['platform'],
            'token' => $data['token'],
            'user_id' => $this->user->id,
            'device_name' => isset($data['device_name']) ? $data['device_name'] : null,
            'active' => 1
        ];

        $fcmDeviceTokenModel = model(FcmDeviceTokenModel::class);
        $existToken = $fcmDeviceTokenModel->where('token', $data['token'])->first();

        if ($existToken) {
            $deviceTokenId = $existToken->id;
            $fcmDeviceTokenModel->where('id', $deviceTokenId)->set($safeDeviceTokenData)->update();
            add_user_log(['data_id' => $deviceTokenId, 'company_id' => $this->user->company_id, 'data_type' => 'device_token_update', 'description' => 'Cập nhật mã thiết bị', 'user_id' => $this->user->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);
        } else {
            $deviceTokenId = $fcmDeviceTokenModel->insert($safeDeviceTokenData);
            add_user_log(['data_id' => $deviceTokenId, 'company_id' => $this->user->company_id, 'data_type' => 'device_token_create', 'description' => 'Thêm mã thiết bị', 'user_id' => $this->user->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);
        }

        $userPermissionFeatureModel = model(UserPermissionFeatureModel::class);
        $pushMobileTransactionNotificationFeaturePermission = $userPermissionFeatureModel->where(['feature_slug' => 'PushMobileTransactionNotification', 'user_id' => $this->user->id])->first();
        $bankAccountFeaturePermission = $userPermissionFeatureModel->where(['feature_slug' => 'BankAccount', 'user_id' => $this->user->id])->first();

        if ($pushMobileTransactionNotificationFeaturePermission->can_view_all == 1) {
            $userPermissionFeatureModel
                ->where(['id' => $bankAccountFeaturePermission->id])
                ->set(['can_view_all' => 1])
                ->update();
        } else if ($bankAccountFeaturePermission->can_view_all == 1) {
            $userPermissionFeatureModel
                ->where(['id' => $pushMobileTransactionNotificationFeaturePermission->id])
                ->set(['can_view_all' => 1])
                ->update();
        }

        if (!$deviceTokenId) {
            return $this->respondServiceUnavailabelResponse();
        }

        return $this->respondNoContentResponse(201);
    }

    public function revoke()
    {
        if ($this->determineIfMethodIsNotPost()) {
            return $this->respondNotFoundResponse();
        }

        $data = (array) $this->request->getJSON();

        $rules = [
            'token' => ['required', 'string']
        ];

        if (! $this->validateData($data, $rules)) {
            return $this->respondBadRequestResponse($this->validator->getErrors());
        }

        $deviceTokenModel = model(FcmDeviceTokenModel::class);
        $deviceToken = $deviceTokenModel->where('token', $data['token'])->delete();

        return $this->respondNoContentResponse();
    }
}
