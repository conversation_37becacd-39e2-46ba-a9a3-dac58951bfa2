<?php

namespace App\Controllers\Mobile\V1;

use App\Models\BankSubAccountModel;
use App\Controllers\Mobile\V1\BaseController;

class VA extends BaseController
{
    public function index()
    {
        if (!$this->authorizeFeaturePermission($this->user, 'BankAccount', 'can_view_all')) {
            return $this->respondUnauthorizedResponse();
        }

        $filter = $this->request->getGet('filter');
        $per_page = $this->request->getGet('per_page');

        $data = [
            'filter' => [
                'bank_account_id' => trim(xss_clean($filter['bank_account_id'] ?? '')),
            ],
            'sort' => [
                'created_at' => $sort['created_at'] ?? ''
            ],
            'per_page' => is_numeric($per_page) && $per_page > 0 ? $per_page : 20,
            'q' => trim(xss_clean($this->request->getGet('q'))),
        ];

        $bankSubAccountModel = model(BankSubAccountModel::class);
        $builder = $bankSubAccountModel->select(['tb_autopay_bank_sub_account.id', 'tb_autopay_bank_sub_account.bank_account_id', 'tb_autopay_bank_sub_account.sub_account', 'tb_autopay_bank_sub_account.label', 'tb_autopay_bank_sub_account.active', 'tb_autopay_bank_sub_account.created_at', 'tb_autopay_bank_sub_account.updated_at']);
        $builder->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id = tb_autopay_bank_sub_account.bank_account_id');
        $builder->where([
            'tb_autopay_bank_account.company_id' => $this->user->company_id,
            'tb_autopay_bank_sub_account.deleted_at' => null, 
            'tb_autopay_bank_sub_account.merchant_id' => null
        ]);
        
        if (!$this->isAdminUser($this->user)) {
            $builder->whereIn('tb_autopay_bank_sub_account.id', array_map(function($permission) {
                return $permission->sub_account_id;
            }, count($this->user->bank_sub_account_permissions) > 0 ? $this->user->bank_sub_account_permissions : [0]));
        }

        if ($data['filter']['bank_account_id']) {
            $builder->where('tb_autopay_bank_sub_account.bank_account_id', $data['filter']['bank_account_id']);
        }

        if ($data['sort']['created_at']) {
            $builder->orderBy('tb_autopay_bank_sub_account.created_at', $data['sort']['created_at'] === 'asc' ? 'asc' : 'desc');
        }

        if ($data['q']) {
            $builder->groupStart()
                ->like('tb_autopay_bank_sub_account.sub_account', $data['q'])
                ->orLike('tb_autopay_bank_sub_account.label', $data['q'])
            ->groupEnd();
        }

        return $this->respond([
            'data' => $builder->paginate($data['per_page']),
            'meta' => [
                'per_page' => $bankSubAccountModel->pager->getPerPage(),
                'total' => $bankSubAccountModel->pager->getTotal(),
                'has_more' => $bankSubAccountModel->pager->hasMore(),
                'current_page' => $bankSubAccountModel->pager->getCurrentPage(),
                'page_count' => $bankSubAccountModel->pager->getPageCount(),
            ]
        ]);
    }
}
