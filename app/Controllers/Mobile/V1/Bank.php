<?php

namespace App\Controllers\Mobile\V1;

use App\Models\BankModel;
use App\Controllers\Mobile\V1\BaseController;

class Bank extends BaseController
{
    public function index()
    {
        $bankModel = model(BankModel::class);
        $banks = $bankModel->select([
            'id', 'brand_name', 'full_name', 'short_name', 'code', 'bin', 'logo_path', 'icon_path', 'active'
        ])->get()->getResult();

        $banks = array_map(function($bank) {
            $bank->logo_path = base_url('assets/images/banklogo/' . $bank->logo_path);
            $bank->icon_path = base_url('assets/images/banklogo/' . $bank->icon_path);
            $bank->support_official_va = in_array($bank->code, ['MB', 'KLB', 'OCB', 'MSB', 'BIDV', 'ACB']);

            return $bank;
        }, $banks);

        return $this->respond(['data' => $banks], 200);    
    }
}
