<?php

namespace App\Controllers\Mobile\V1;

use App\Models\TransactionsModel;
use App\Controllers\Mobile\V1\BaseController;
use App\Actions\Company\GetCompanyConfigurationAction;

class Transaction extends BaseController
{
    public function index()
    {
        if (!$this->authorizeFeaturePermission($this->user, 'Transactions', 'can_view_all')) {
            return $this->respond([
                'data' => [],
                'meta' => [
                    'per_page' => 20,
                    'total' => 0,
                    'has_more' => false,
                    'current_page' => 1,
                    'page_count' => 0,
                ]
            ]);
        }

        $filter = $this->request->getGet('filter');
        $sort = $this->request->getGet('sort');
        $per_page = $this->request->getGet('per_page');

        $data = [
            'filter' => [
                'bank_id' => trim(xss_clean($filter['bank_id'] ?? '')),
                'bank_account_id' => trim(xss_clean($filter['bank_account_id'] ?? '')),
                'va_id' => trim(xss_clean($filter['va_id'] ?? '')),
                'transfer_type' => trim(xss_clean($filter['transfer_type'] ?? '')),
                'transaction_date' => $filter['transaction_date'] ?? '',
                'start_transaction_date' => $filter['start_transaction_date'] ?? '',
                'end_transaction_date' => $filter['end_transaction_date'] ?? '',
            ],
            'sort' => [
                'transaction_date' => $sort['transaction_date'] ?? ''
            ],
            'per_page' => is_numeric($per_page) && $per_page > 0 ? $per_page : 20,
            'q' => trim($this->request->getGet('q')),
        ];

        $transactionModel = slavable_model(TransactionsModel::class, 'Mobileapi');
        $bankSubAccountConfig = GetCompanyConfigurationAction::run($this->user->company_id, 'BankSubAccount', null, 'Mobileapi')->value;
        $db = db_connect();

        $builder = $transactionModel->select(['tb_autopay_sms_parsed.transaction_id', 'tb_autopay_sms_parsed.transaction_date', 'tb_autopay_sms_parsed.accumulated', 'tb_autopay_sms_parsed.bank_account_id', 'tb_autopay_sms_parsed.account_number', 'tb_autopay_bank_account.bank_id', $bankSubAccountConfig == 'on' ? 'tb_autopay_bank_sub_account.id as va_id' : null, $bankSubAccountConfig == 'on' ? 'tb_autopay_sms_parsed.sub_account as va' : null, 'tb_autopay_sms_parsed.reference_number', 'tb_autopay_sms_parsed.amount_in', 'tb_autopay_sms_parsed.amount_out', 'tb_autopay_sms_parsed.transaction_content', 'tb_autopay_sms_parsed.code as payment_code']);
        $builder->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id = tb_autopay_sms_parsed.bank_account_id');
        $builder->join('tb_autopay_bank_sub_account', 'tb_autopay_bank_sub_account.sub_account = tb_autopay_sms_parsed.sub_account', 'left');
        $builder->join('tb_autopay_user_permission_bank', 'tb_autopay_user_permission_bank.bank_account_id = tb_autopay_sms_parsed.bank_account_id and tb_autopay_user_permission_bank.user_id = "' . $db->getConnection()->real_escape_string($this->user->id) . '"', 'left');
        
        $builder->where([
            'tb_autopay_sms_parsed.parser_status' => true,
            'tb_autopay_sms_parsed.deleted_at' => null,
            'tb_autopay_sms_parsed.merchant_id' => null,
            'tb_autopay_bank_account.company_id' => $this->user->company_id
        ]);

        if (!$this->isAdminUser($this->user)) {
            $builder->whereIn('tb_autopay_sms_parsed.bank_account_id', array_map(function($permission) {
                return $permission->bank_account_id ?? 0;
            }, count($this->user->bank_account_permissions) > 0 ? $this->user->bank_account_permissions : [0]));

            $builder->groupStart()
                ->whereIn('tb_autopay_bank_sub_account.id', array_map(function($permission) {
                    return $permission->sub_account_id ?? 0;
                }, count($this->user->bank_sub_account_permissions) > 0 ? $this->user->bank_sub_account_permissions : [0]))
                ->orWhere('tb_autopay_bank_sub_account.id', null)
            ->groupEnd();

            $builder->groupStart()
                ->where([
                    'tb_autopay_user_permission_bank.hide_amount_out' => 1,
                    'tb_autopay_sms_parsed.amount_in >' => 0
                ])
                ->orWhere('tb_autopay_user_permission_bank.hide_amount_out', 0)
            ->groupEnd();
        }

        if ($data['filter']['bank_id']) {
            $builder->where('tb_autopay_bank_account.bank_id', $data['filter']['bank_id']);
        }

        if ($data['filter']['bank_account_id']) {
            $builder->where('tb_autopay_sms_parsed.bank_account_id', $data['filter']['bank_account_id']);
        }

        if ($data['filter']['transaction_date'] && preg_match('/^\d{4}\-\d{2}-\d{2}$/', $data['filter']['transaction_date'])) {
            $builder->where("DATE_FORMAT(tb_autopay_sms_parsed.transaction_date, '%Y-%m-%d')", $data['filter']['transaction_date']);
        }

        if ($data['filter']['transaction_date'] && preg_match('/^\d{4}\-\d{2}-\d{2} \d{2}\:\d{2}\:\d{2}$/', $data['filter']['transaction_date'])) {
            $builder->where("DATE_FORMAT(tb_autopay_sms_parsed.transaction_date, '%Y-%m-%d %H:%i:%s')", $data['filter']['transaction_date']);
        }

        if ($data['filter']['start_transaction_date'] && preg_match('/^\d{4}\-\d{2}-\d{2}$/', $data['filter']['start_transaction_date'])) {
            $builder->where("DATE_FORMAT(tb_autopay_sms_parsed.transaction_date, '%Y-%m-%d') >=", $data['filter']['start_transaction_date']);
        }

        if ($data['filter']['start_transaction_date'] && preg_match('/^\d{4}\-\d{2}-\d{2} \d{2}\:\d{2}\:\d{2}$/', $data['filter']['start_transaction_date'])) {
            $builder->where("DATE_FORMAT(tb_autopay_sms_parsed.transaction_date, '%Y-%m-%d %H:%i:%s') >=", $data['filter']['start_transaction_date']);
        }

        if ($data['filter']['end_transaction_date'] && preg_match('/^\d{4}\-\d{2}-\d{2}$/', $data['filter']['end_transaction_date'])) {
            $builder->where("DATE_FORMAT(tb_autopay_sms_parsed.transaction_date, '%Y-%m-%d') <=", $data['filter']['end_transaction_date']);
        }

        if ($data['filter']['end_transaction_date'] && preg_match('/^\d{4}\-\d{2}-\d{2} \d{2}\:\d{2}\:\d{2}$/', $data['filter']['end_transaction_date'])) {
            $builder->where("DATE_FORMAT(tb_autopay_sms_parsed.transaction_date, '%Y-%m-%d %H:%i:%s') <=", $data['filter']['end_transaction_date']);
        }

        if ($data['filter']['transfer_type'] == 'credit') {
            $builder->where('amount_in > ', 0);
        }

        if ($data['filter']['transfer_type'] == 'debit') {
            $builder->where('amount_out > ', 0);
        }

        if ($data['filter']['va_id']) {
            $builder->where('tb_autopay_bank_sub_account.id', $data['filter']['va_id']);
        }

        if ($data['sort']['transaction_date']) {
            $builder->orderBy('transaction_date', $data['sort']['transaction_date'] === 'asc' ? 'asc' : 'desc');
        }

        if ($data['q']) {
            $builder->groupStart()
                ->like('tb_autopay_sms_parsed.transaction_content', $data['q'])
                ->orLike('tb_autopay_sms_parsed.body', $data['q'])
                ->orLike('tb_autopay_sms_parsed.reference_number', $data['q'])
                ->orLike('tb_autopay_sms_parsed.code', $data['q'])
                ->orLike('tb_autopay_sms_parsed.sub_account', $data['q'])
            ->groupEnd();
        }

        return $this->respond([
            'data' => array_map(function($transaction) {
                return $this->resolveTransactionData($this->user, $transaction);
            }, $builder->paginate($data['per_page'])),
            'meta' => [
                'per_page' => $transactionModel->pager->getPerPage(),
                'total' => $transactionModel->pager->getTotal(),
                'has_more' => $transactionModel->pager->hasMore(),
                'current_page' => $transactionModel->pager->getCurrentPage(),
                'page_count' => $transactionModel->pager->getPageCount(),
            ]
        ]);
    }

    public function details($id = null)
    {
        if (!$id) {
            return $this->respondNotFoundResponse();
        }

        if (!$this->authorizeFeaturePermission($this->user, 'Transactions', 'can_view_all')) {
            return $this->respondUnauthorizedResponse();
        }

        $bankSubAccountConfig = GetCompanyConfigurationAction::run($this->user->company_id, 'BankSubAccount')->value;

        $builder = model(TransactionsModel::class)->select(['tb_autopay_sms_parsed.transaction_id', 'tb_autopay_sms_parsed.transaction_date', 'tb_autopay_sms_parsed.accumulated', 'tb_autopay_sms_parsed.bank_account_id', 'tb_autopay_sms_parsed.account_number', 'tb_autopay_bank_account.bank_id', $bankSubAccountConfig == 'on' ? 'tb_autopay_bank_sub_account.id as va_id' : null, $bankSubAccountConfig == 'on' ? 'tb_autopay_sms_parsed.sub_account as va' : null, 'tb_autopay_sms_parsed.reference_number', 'tb_autopay_sms_parsed.amount_in', 'tb_autopay_sms_parsed.amount_out', 'tb_autopay_sms_parsed.transaction_content', 'tb_autopay_sms_parsed.code as payment_code']);
        $builder->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id = tb_autopay_sms_parsed.bank_account_id');
        $builder->join('tb_autopay_bank_sub_account', 'tb_autopay_bank_sub_account.sub_account = tb_autopay_sms_parsed.sub_account', 'left');
        $builder->where('tb_autopay_sms_parsed.transaction_id', $id);
        $builder->where('tb_autopay_bank_account.company_id', $this->user->company_id);
        $transaction = $builder->first();

        if (!$transaction 
        || !$this->authorizeBankAccountPermission($this->user, $transaction->bank_account_id) 
        || (($transaction->va_id ?? null) && !$this->authorizeBankSubAccountPermission($this->user, $transaction->va_id ?? null))) {
            return $this->respondNotFoundResponse(static::TRANSACTION_ID_NOT_EXIST_MESSAGE);
        }

        if ($transaction->amount_out > 0 && $this->authorizeBankAccountPermission($this->user, $transaction->bank_account_id, 'hide_amount_out')) {
            return $this->respondNotFoundResponse(static::TRANSACTION_ID_NOT_EXIST_MESSAGE);
        }

        return $this->respond([
            'data' => $this->resolveTransactionData($this->user, $transaction)
        ]);
    }

    protected function resolveTransactionData($user, $transaction)
    {
        if ($this->authorizeBankAccountPermission($user, $transaction->bank_account_id, 'hide_reference_number')) {
            $transaction->reference_number = '***';
        }

        if ($transaction->amount_out > 0 && $this->authorizeBankAccountPermission($user, $transaction->bank_account_id, 'hide_amount_out')) {
            $transaction->amount_out = '***';
        }

        if ($this->authorizeBankAccountPermission($user, $transaction->bank_account_id, 'hide_transaction_content')) {
            $transaction->transaction_content = '***';
        }

        if ($this->authorizeBankAccountPermission($user, $transaction->bank_account_id, 'hide_accumulated')) {
            $transaction->accumulated = '***';
        }

        return $transaction;
    }
}
