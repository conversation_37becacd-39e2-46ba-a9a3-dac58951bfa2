<?php

namespace App\Controllers;

use App\Models\UserModel;
use CodeIgniter\Controller;
use CodeIgniter\HTTP\CLIRequest;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use Psr\Log\LoggerInterface;
use App\Models\NotificationUserModel;

/**
 * Class BaseController
 *
 * BaseController provides a convenient place for loading components
 * and performing functions that are needed by all your controllers.
 * Extend this class in any new controllers:
 *     class Home extends BaseController
 *
 * For security be sure to declare any new methods as protected or private.
 */
abstract class BaseController extends Controller
{
    /**
     * Instance of the main Request object.
     *
     * @var CLIRequest|IncomingRequest
     */
    protected $request;

    /**
     * An array of helpers to be loaded automatically upon
     * class instantiation. These helpers will be available
     * to all other controllers that extend BaseController.
     *
     * @var array
     */
    protected $helpers = ['general','form','url','text'];

    protected $user_details;

    protected $company_details;

    protected $user_session;

    protected $shop_billing;

    /**
     * Constructor.
     */
    public function initController(RequestInterface $request, ResponseInterface $response, LoggerInterface $logger)
    {
        // Do Not Edit This Line
        parent::initController($request, $response, $logger);
 
        // Preload any models, libraries, etc, here.

        // E.g.: $this->session = \Config\Services::session();

        $this->uri = new \CodeIgniter\HTTP\URI();

        $session = session();

        $this->user_session = $session->get('user_logged_in');
        $this->channel_partner = $session->get('channel_partner');

        if (auth()->check()) {
            $this->user_details = auth()->user();
            $this->company_details = auth()->company();

            $this->shop_billing = $session->get('shop_billing');
            $this->user_details->unread_noti_count = 0;

            if (!$request->isAJAX()) {
                $this->user_details->showable_popup_notification = $this->user_details->last_show_popup_notification == null
                    || ($this->user_details->last_show_popup_notification && is_tomorrow(date('Y-m-d H:i:s'), $this->user_details->last_show_popup_notification));

                $notificationUserModel = slavable_model(NotificationUserModel::class, 'Notification');

                $this->user_details->unread_noti_count = $notificationUserModel
                    ->join('tb_autopay_notification', 'tb_autopay_notification.id = tb_autopay_notification_user.notification_id')
                    ->where(['tb_autopay_notification_user.user_id' => $this->user_details->id, 'tb_autopay_notification_user.seen_at' => null, 'tb_autopay_notification.hidden' => 0])
                    ->countAllResults();

                $popupNotificationBuilder = $notificationUserModel
                    ->select(['tb_autopay_notification.id', 'tb_autopay_popup_notification.thumbnail_url', 'tb_autopay_popup_notification.cta_url', 'tb_autopay_popup_notification.cta_text', 'tb_autopay_notification.title', 'tb_autopay_notification.description', 'tb_autopay_notification_user.created_at'])
                    ->join('tb_autopay_notification', 'tb_autopay_notification.id = tb_autopay_notification_user.notification_id')
                    ->join('tb_autopay_popup_notification', 'tb_autopay_notification_user.notification_id = tb_autopay_popup_notification.notification_id')
                    ->where([
                        'tb_autopay_notification_user.user_id' => $this->user_details->id,
                        'tb_autopay_notification.hidden' => 0
                    ])
                    ->groupStart()
                    ->where([
                        'tb_autopay_popup_notification.hidden_at >' => date('Y-m-d H:i:s'),
                    ])
                    ->orWhere([
                        'tb_autopay_popup_notification.hidden_at' => null,
                    ])
                    ->groupEnd();

                if ($this->user_details->last_force_hide_popup_notification) {
                    $popupNotificationBuilder->where(['tb_autopay_notification_user.created_at > ' => $this->user_details->last_force_hide_popup_notification]);
                }

                $this->user_details->popup_notifications = $popupNotificationBuilder->orderBy('tb_autopay_popup_notification.pos', 'asc')->orderBy('tb_autopay_notification.sent_at', 'desc')
                    ->get()->getResult();

                $hasNewPopupNotificationAfterLastShow = !$this->user_details->last_show_popup_notification || count(array_filter($this->user_details->popup_notifications, function($noti) {
                        return strtotime($this->user_details->last_show_popup_notification) < strtotime($noti->created_at);
                    })) > 0;

                if ($hasNewPopupNotificationAfterLastShow) {
                    $this->user_details->showable_popup_notification = true;
                }

                if ($this->user_details->showable_popup_notification) {
                    model(UserModel::class)->where('id', $this->user_details->id)->set('last_show_popup_notification', date('Y-m-d H:i:s'))->update();
                }
            }
        }
    }
}