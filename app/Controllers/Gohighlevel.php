<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Actions\PayCodeDetector;
use App\Libraries\GoHighLevelClient;

class Gohighlevel extends BaseController
{
    public function __construct()
    {
        $GHL_config = config(GoHighLevel::class);
        helper('general');
        if($GHL_config->visibility == false && !is_admin()){
                show_404();
        }

        
    }

    public function oauth_code(){

        $GHL_config = config(GoHighLevel::class);


        $client_id = $GHL_config->client_id;
        $redirect_uri = $GHL_config->redirect_uri;
        $scope = $GHL_config->scope;
        
        // Nếu không có mã ủy quyền, tạo URL ủy quyền
        $authUrl = "https://marketplace.leadconnectorhq.com/oauth/chooselocation?" . http_build_query([
            'response_type' => 'code',
            'redirect_uri' => $redirect_uri,
            'client_id' => $client_id,
            'scope' => $scope,
        ]);

        // Trả về URL ủy quyền
        return redirect()->to($authUrl);
    }

   
    public function index()
    { 
        $data = [
            'alert' => session()->getFlashdata('alert'),
            'page_title' => 'GoHighLevel',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        if(!has_permission('Webhooks', 'can_view_all'))
            show_404();

        $bankAccountModel = model(BankAccountModel::class);

        $data['bankAccounts'] = $bankAccountModel->select("tb_autopay_bank_account.id, tb_autopay_bank.brand_name, tb_autopay_bank_account.account_holder_name, tb_autopay_bank_account.account_number")->join("tb_autopay_bank","tb_autopay_bank.id=tb_autopay_bank_account.bank_id","left")->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id']])->get()->getResult();
        $data['paycodes'] = PayCodeDetector::getPayCodeList($this->user_session['company_id']);
        $data['count_integration'] = model(GoHighLevelModel::class)
        ->where(['company_id' => $this->user_session['company_id']])
        ->countAllResults();

        echo view('templates/autopay/header',$data);
        echo view('gohighlevel/index',$data);
        echo view('templates/autopay/footer',$data);
    }
    public function ajax_gohighlevel_list()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!has_permission('Webhooks', 'can_view_all'))
            show_404();
 
        $GoHighLevelModel = model(GoHighLevelModel::class);
        $webhooksLogModel = model(WebhooksLogModel::class);
        $list = $GoHighLevelModel->getDatatables($this->user_session['company_id']);
        $paycodePrefix = model(ConfigurationModel::class)->getFirstPaycodePrefix($this->user_session['company_id']);
        
        $data = array();

        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');

        foreach ($list as $gohighlevel) {
            

            $no++;
            $row = array();
            
            $row[] = $no;
            $row[] = esc($gohighlevel->id);
            $row[] = "<a class='text-decoration-none text-dark' href='javascript:void(0);'>" . esc($gohighlevel->name) . "</a>"
            . "<br><a class=' text-decoration-none text-dark fw-bold mt-1' href='javascript:void(0);'>" 
            . esc($gohighlevel->site_url) . "</a>";


            $row[] = esc($gohighlevel->brand_name) . ' <br> ' . esc($gohighlevel->account_number) . ' <br>' . esc($gohighlevel->account_holder_name);
            $row[] = esc($gohighlevel->paycode_prefix ?: $paycodePrefix);

            
            if($gohighlevel->active == 1)
                $row[] = "<span class='text-success'>Kích hoạt</span>";
            else
                $row[] = "<span class='text-secondary'>Tạm ngưng</span>";
            
            $row[] = esc($gohighlevel->created_at);
            $row[] = "
           
          <a href='javascript:;' onclick='edit_gohighlevel(" . $gohighlevel->id . ")' class='btn btn-sm btn-outline-warning ms-2 mt-2'><i class='bi bi-pencil'></i> Sửa</a> 
          <a href='javascript:;' onclick='delete_gohighlevel(" . $gohighlevel->id . ")' class='btn btn-sm btn-outline-danger ms-2 mt-2'><i class='bi bi-trash3'></i> Xóa</a>
          ";

            $data[] = $row;
        }
 
        $output = array(
            "draw" => $draw,
            "recordsTotal" => $GoHighLevelModel->countAll($this->user_session['company_id']),
            "recordsFiltered" => $GoHighLevelModel->countFiltered($this->user_session['company_id']),
            "data" => $data,
        );

        return $this->response->setJSON($output);
    }

    public function ajax_gohighlevel_add() 
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!has_permission('Webhooks', 'can_add'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền thêm tích hợp"));
 
        $validation = \Config\Services::validation();

        helper('text');

        $paramsRules = [];

       
        $siteUrl = $this->request->getPost('site_url');

        $rules = [
            'bank_account_id' => ['label' => 'Tài khoản ngân hàng', 'rules' => 'required|integer|is_natural'],
            'name' => ['label' => 'Đặt tên tích hợp', 'rules' => "required|max_length[250]"],
            'site_url' => [
                'label' => 'Domain',
                'rules' => $siteUrl == '*' ? 'required|max_length[550]' : 'required|valid_url|max_length[550]'
            ],
            'active' => "required|in_list[0,1]",
            'paycode_prefix' => 'required',
            'code' => 'required',
        ];
        
        
        if(! $this->validate($rules)) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        } 

        $paycodes = PayCodeDetector::getPayCodeList($this->user_session['company_id']);

        if (! in_array($this->request->getVar('paycode_prefix'), $paycodes)) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Mã thanh toán không hợp lệ',
            ]);
        }

        $bank_account_id = $this->request->getPost('bank_account_id');
        $va_id = $this->request->getPost('va_id');

        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $GoHighLevelModel = model(GoHighLevelModel::class);

        // Fetch bank account details
        $bank_account_details = $bankAccountModel
            ->select("tb_autopay_bank_account.bank_api,tb_autopay_bank_account.account_holder_name, tb_autopay_bank_account.account_number, tb_autopay_bank.brand_name, tb_autopay_bank.full_name, tb_autopay_bank.bin, tb_autopay_bank.code, tb_autopay_bank.logo_path, tb_autopay_bank.icon_path,tb_autopay_bank.id bank_id")
            ->join("tb_autopay_bank", "tb_autopay_bank_account.bank_id=tb_autopay_bank.id")
            ->where([
                'tb_autopay_bank_account.id' => $bank_account_id,
                'company_id' => $this->user_session['company_id']
            ])
            ->get()
            ->getRow();
    
       
    
        
        if (!is_object($bank_account_details))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>'Không tìm thấy tài khoản ngân hàng mà bạn chọn'));

        if (is_numeric($va_id) && $va_id > 0) {
            $va_details = $bankSubAccountModel
            ->join("tb_autopay_bank_account", "tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id", "left")
            ->where([
                'tb_autopay_bank_sub_account.id' => $va_id,
                'tb_autopay_bank_sub_account.acc_type' => "Real",
                'tb_autopay_bank_account.company_id' => $this->user_session['company_id']
            ])
            ->get()
            ->getRow();
            if (!is_object($va_details))
                return $this->response->setJSON(array('status'=>FALSE,'message'=>'Không tìm thấy VA mà bạn chọn'));
        } else {
           
            if ($bank_account_details->bank_api == 1 && in_array($bank_account_details->bank_id, [9,10,17,18]))
                return $this->response->setJSON(array('status'=>FALSE, 'message'=> 'Với các ngân hàng kết nối qua API, bạn cần chọn VA để nhận thanh toán. Vui lòng chọn 1 VA'));
            else
                $va_id = 0;
        }

        
        
        

        // get token GHL
        $gohightlevel_client = new GoHighLevelClient();
        $data_token =  $gohightlevel_client->getToken($this->request->getVar('code'));

        if($data_token['status_code'] != 200){

            return $this->response->setJSON(array("status" => FALSE,"message" => "Lỗi hệ thống xác thực GHL, Hãy liên hệ kỹ thuật!","data"=>$data_token));
        }


        // Initialize account details
        $account_number = $bank_account_details->account_number;
        $account_holder_name = $bank_account_details->account_holder_name;
    
        // Check for virtual account details
           
        if (!empty($va_id)) {
            $account_number = $va_details->sub_account;
            $account_holder_name = $bank_account_details->account_holder_name;
        }
    
        $data_json_url = json_encode([
            'bank_bin' => $bank_account_details->bin,
            'bank_code' => $bank_account_details->code,
            'account_number' => $account_number,
            'prefix' => $this->request->getVar('paycode_prefix'),
            'site_url' => $this->request->getVar('site_url'),
            'bank_brand_name' => $bank_account_details->brand_name,
            'account_name' => $account_holder_name,
        ]);
    

        $data = array(
            'name' => xss_clean($this->request->getVar('name')),
            'company_id' => $this->user_session['company_id'],
            'code' => $this->request->getVar('code'),
            'location_id' => $data_token['raw_response']['locationId'] ?? '',
            'site_url' =>rtrim($this->request->getVar('site_url'), '/') ,
            'access_token' => $data_token['raw_response']['access_token'] ?? '',
            'refresh_token' => $data_token['raw_response']['refresh_token'] ?? '',
            'scope' => $data_token['raw_response']['scope'] ?? '',
            'token_type' => $data_token['raw_response']['token_type'] ?? '',
            'user_type' => $data_token['raw_response']['userType'] ?? '',
            'user_id' => $data_token['raw_response']['userId'] ?? '',
            'expires_in' => date('Y-m-d H:i:s', strtotime('+' . ($data_token['raw_response']['expires_in'] ?? 0) . ' seconds')),
            'api_key_method_payment_live' => md5(time()),
            'publishablekey_method_payment_live' => md5(time()+300),
            'api_payment_url' => base_url()."/userapi/transactions/payment_url"."?d=".base64_encode($data_json_url),
            'api_query_url' => base_url()."/userapi/transactions/query_url",
            'integration_payment_id' => null,
            'bank_account_id' => $bank_account_id,
            'va_id' => $va_id,
            'paycode_prefix' => $this->request->getVar('paycode_prefix'),
            'active' => $this->request->getVar('active') ?? 1,
            
        );

        // check location ID

        $location_id_exits = $GoHighLevelModel->where(['location_id'=>$data['location_id']])->get()->getRow();
        if(!empty($location_id_exits)){
            return $this->response->setJSON(array("status" => FALSE,"message" => "Tài khoản này đã tồn tại tích hợp, hãy kiểm tra lại!"));
        }


        $data_add_payment = $gohightlevel_client->addPayment($data);

        if( $data_add_payment['status_code'] > 201){

            return $this->response->setJSON(array("status" => FALSE,"message" => "Lỗi hệ thống tạo payment GHL, Hãy liên hệ kỹ thuật!","data"=>$data_add_payment));
        }

        // active payment GHL
        $data_active_payment = $gohightlevel_client->activePayment($data);

        if( $data_active_payment['status_code'] > 201){

            return $this->response->setJSON(array("status" => FALSE,"message" => "Lỗi hệ thống tạo payment GHL, Hãy liên hệ kỹ thuật!","data"=>$data_add_payment));
        }

        $data['integration_payment_id'] = $data_add_payment['raw_response']['_id'] ?? null;

        $insert = $GoHighLevelModel->insert($data);
        
        if (!$insert) {
            add_user_log(array('data_id'=>$data_add_payment['raw_response']['_id'],'company_id' => $this->user_session['company_id'], 'data_type'=>'gohighlevel_delete','description'=>'Thêm tích hợp gohighlevel','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Failed'));
    
            return $this->response->setJSON([
                "status"  => false,
                "message" => "Tích hợp thất bại!",
                "error"   => $GoHighLevelModel->errors() // Trả về lỗi chi tiết nếu có
            ]);
        }
        add_user_log(array('data_id'=>$data_add_payment['raw_response']['_id'],'company_id' => $this->user_session['company_id'], 'data_type'=>'gohighlevel_delete','description'=>'Thêm tích hợp gohighlevel','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
    
        return $this->response->setJSON([
            "status"  => true,
            "message" => "Tích hợp thành công!",
            "data"    => $data_active_payment
        ]);
        
        
        
    }

    public function ajax_get_gohighlevel($id='')
    {
        if(!has_permission('Webhooks', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền sửa tích hợp"));

        if(!is_numeric($id)) 
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"ID tích hợp không hợp lệ"));
        
        $GoHighLevelModel = model(GoHighLevelModel::class);
        
        $result = $GoHighLevelModel->select('id,name,site_url,paycode_prefix,bank_account_id,va_id,active')->where(['id'=>$id, 'company_id' => $this->user_session['company_id']])->get()->getRow();
        
        $result->paycode_prefix = $result->paycode_prefix ?: model(ConfigurationModel::class)->getFirstPaycodePrefix($this->user_session['company_id']);

       
        
        if($result)
            return $this->response->setJSON(["status"=>TRUE, "data"=>$result]);
        else
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy tích hợp này"));
    }

    public function ajax_gohighlevel_update()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!has_permission('Webhooks', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền sửa tích hợp"));

        $validation =  \Config\Services::validation();

        helper('text');

        
        $siteUrl = $this->request->getPost('site_url');

    
        $rules = [
            'id' => ['label' => 'ID', 'rules' => 'required|integer|is_natural'],
            'bank_account_id' =>['label' => 'Tài khoản ngân hàng', 'rules' => 'required|integer|is_natural'],
            'site_url' => [
                'label' => 'Domain',
                'rules' => $siteUrl == '*' ? 'required|max_length[550]' : 'required|valid_url|max_length[550]'
            ],
           
            'name' => ['label' => 'Đặt tên', 'rules' => "required|max_length[250]"],
            'active' => "required|in_list[0,1]",
            'paycode_prefix' => 'required',
        ]; 
         
        if (! $this->validate($rules)) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        } 

        $paycodes = PayCodeDetector::getPayCodeList($this->user_session['company_id']);

        if (! in_array($this->request->getVar('paycode_prefix'), $paycodes)) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Mã thanh toán không hợp lệ',
            ]);
        }

        $bank_account_id = $this->request->getPost('bank_account_id');
        $va_id = $this->request->getPost('va_id');

        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);

        $GoHighLevelModel = model(GoHighLevelModel::class);
        $gohighlevel_id = $this->request->getVar('id');
        
        $gohighlevel_details = $GoHighLevelModel->where(["id" =>$gohighlevel_id,"company_id"=>$this->user_session['company_id']])->get()->getRow();


        // Fetch bank account details
        $bank_account_details = $bankAccountModel
            ->select("tb_autopay_bank_account.bank_api,tb_autopay_bank_account.account_holder_name, tb_autopay_bank_account.account_number, tb_autopay_bank.brand_name, tb_autopay_bank.full_name, tb_autopay_bank.bin, tb_autopay_bank.code, tb_autopay_bank.logo_path, tb_autopay_bank.icon_path,tb_autopay_bank.id bank_id")
            ->join("tb_autopay_bank", "tb_autopay_bank_account.bank_id=tb_autopay_bank.id")
            ->where([
                'tb_autopay_bank_account.id' => $bank_account_id,
                'company_id' => $this->user_session['company_id']
            ])
            ->get()
            ->getRow();
    
        if (!is_object($bank_account_details))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>'Không tìm thấy tài khoản ngân hàng mà bạn chọn'));

        if (is_numeric($va_id) && $va_id > 0) {
            $va_details = $bankSubAccountModel
            ->join("tb_autopay_bank_account", "tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id", "left")
            ->where([
                'tb_autopay_bank_sub_account.id' => $va_id,
                'tb_autopay_bank_sub_account.acc_type' => "Real",
                'tb_autopay_bank_account.company_id' => $this->user_session['company_id']
            ])
            ->get()
            ->getRow();
            if (!is_object($va_details))
                return $this->response->setJSON(array('status'=>FALSE,'message'=>'Không tìm thấy VA mà bạn chọn'));
        } else {
            if ($bank_account_details->bank_api == 1 && in_array($bank_account_details->bank_id, [9,10,17,18]))
                return $this->response->setJSON(array('status'=>FALSE, 'message'=> 'Với các ngân hàng kết nối qua API, bạn cần chọn VA để nhận thanh toán. Vui lòng chọn 1 VA'));
            else
                $va_id = 0;
        }

       
        $data = array(
            'bank_account_id' => $this->request->getVar('bank_account_id'),
            'site_url' =>rtrim($this->request->getVar('site_url'), '/') ,
            'name' => xss_clean($this->request->getVar('name')),
            'active' => $this->request->getVar('active'),
            'va_id' => $va_id,
            'paycode_prefix' => $this->request->getVar('paycode_prefix'),
        );

         // Initialize account details
        $account_number = $bank_account_details->account_number;
        $account_holder_name = $bank_account_details->account_holder_name;
    
        // Check for virtual account details
           
    
        if (!empty($va_id)) {
            $account_number = $va_details->sub_account;
            $account_holder_name = $bank_account_details->account_holder_name;
        }

        $data_token_gohighlevel = $GoHighLevelModel->where(['id' => $gohighlevel_id])->get()->getRow();
        $current_time = time();
        if($current_time >= strtotime($data_token_gohighlevel->expires_in)){
            log_message("debug","Reset token update");
            $gohightlevel_client = new GoHighLevelClient();
            $data_token =  $gohightlevel_client->getTokenNew($data_token_gohighlevel->refresh_token);
            if($data_token['status_code'] != 200){
                return $this->response->setJSON(array("status" => FALSE,"message" => "Lỗi hệ thống xác thực GHL, Hãy liên hệ kỹ thuật!","data"=>$data_token));
            }
            
            $data['access_token'] = $data_token['raw_response']['access_token'] ?? '';
            $data['refresh_token'] = $data_token['raw_response']['refresh_token'] ?? '';
            $data['scope'] = $data_token['raw_response']['scope'] ?? '';
            $data['token_type'] = $data_token['raw_response']['token_type'] ?? '';
            $data['user_type'] = $data_token['raw_response']['userType'] ?? '';
            $data['user_id'] = $data_token['raw_response']['userId'] ?? '';
            $data['expires_in'] = date('Y-m-d H:i:s', strtotime('+' . ($data_token['raw_response']['expires_in'] ?? 0) . ' seconds'));
        }

            
        $result = $GoHighLevelModel->set($data)->where(["id" =>$gohighlevel_id,"company_id"=>$this->user_session['company_id']])->update();
        
        if ($result) { 
            $update_payment = $this->update_payment_integration($gohighlevel_id);

            if(!empty($update_payment)){

                add_user_log(array('data_id'=>$gohighlevel_id,'company_id' => $this->user_session['company_id'], 'data_type'=>'gohighlevel_update','description'=>'Sửa tích hợp gohighlevel','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
                return $this->response->setJSON(array("status"=>true, 'id' => $gohighlevel_id));
            }else{
                add_user_log(array('data_id'=>$gohighlevel_id,'company_id' => $this->user_session['company_id'], 'data_type'=>'gohighlevel_update','description'=>'Sửa tích hợp cổng thanh toán gohighlevel','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Failed'));
                return $this->response->setJSON(array("status"=>false,"message"=>"Không thể cập nhật tích hợp!"));
            }
        } else {
            add_user_log(array('data_id'=>$gohighlevel_id,'company_id' => $this->user_session['company_id'], 'data_type'=>'gohighlevel_update','description'=>'Sửa tích hợp gohighlevel','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Failed'));
            return $this->response->setJSON(array("status"=>false,"message"=>"Không thể cập nhật tích hợp!"));
        }
    }

    public function ajax_gohighlevel_delete()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!has_permission('Webhooks', 'can_delete'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền xóa tích hợp"));

        $validation = \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'id' => 'required|integer|is_natural',
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }
        $gohighlevel_id = $this->request->getPost('id');

        $GoHighLevelModel = model(GoHighLevelModel::class);
        $gohighlevel_details = $GoHighLevelModel->where(['id'=>$gohighlevel_id,'company_id'=>$this->user_session['company_id']])->get()->getRowArray();

        // check expires_in token
        $current_time = time();
        if($current_time >= strtotime($gohighlevel_details['expires_in'])){
            log_message("debug","Reset token Delete");
            $gohightlevel_client = new GoHighLevelClient();
            $data_token =  $gohightlevel_client->getTokenNew($gohighlevel_details['refresh_token']);
            if($data_token['status_code'] != 200){
                $GoHighLevelModel->delete($gohighlevel_id);
                log_message("error","Data Current Token: ".json_encode($gohighlevel_details));
                log_message("error","Data refresh token GHL error: ".json_encode($data_token));
                add_user_log(array('data_id'=>$gohighlevel_id,'company_id' => $this->user_session['company_id'], 'data_type'=>'gohighlevel_delete','description'=>'Xóa tích hợp gohighlevel phía SePay','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
                return $this->response->setJSON(array("status" => true,"message" => "SePay đã hỗ trợ gỡ tích hợp phía SePay, Quý khách hãy truy cập vào trang quản trị GoHighLevel và gỡ trực tiếp"));

            }
            $data_update = array(
                'access_token' => $data_token['raw_response']['access_token'] ?? '',
                'refresh_token' => $data_token['raw_response']['refresh_token'] ?? '',
                'scope' => $data_token['raw_response']['scope'] ?? '',
                'token_type' => $data_token['raw_response']['token_type'] ?? '',
                'user_type' => $data_token['raw_response']['userType'] ?? '',
                'user_id' => $data_token['raw_response']['userId'] ?? '',
                'expires_in' => date('Y-m-d H:i:s', strtotime('+' . ($data_token['raw_response']['expires_in'] ?? 0) . ' seconds')),

            ); 
            // cập nhật token mới   
            $result_update = $GoHighLevelModel->set($data_update)->where(["id" =>$gohighlevel_id,"company_id"=>$this->user_session['company_id']])->update();
            
            if($result_update){
                $gohighlevel_details = $GoHighLevelModel->where(['id'=>$gohighlevel_id,'company_id'=>$this->user_session['company_id']])->get()->getRowArray();

            }else{
                return $this->response->setJSON(array("status" => FALSE,"message" => "Lỗi hệ thống xóa payment GHL (token hết hạn), Hãy liên hệ kỹ thuật!","data"=>$data_delete_payment));
            }
        }

        // active payment GHL
        $gohightlevel_client = new GoHighLevelClient();
        $data_delete_payment = $gohightlevel_client->deletePayment($gohighlevel_details);

        if( $data_delete_payment['status_code'] > 201){
            $GoHighLevelModel->delete($gohighlevel_id);
            log_message("error","Data Current Token: ".json_encode($gohighlevel_details));
            log_message("error","Delete payment GHL error: ".json_encode($data_delete_payment));
            add_user_log(array('data_id'=>$gohighlevel_id,'company_id' => $this->user_session['company_id'], 'data_type'=>'gohighlevel_delete','description'=>'Xóa tích hợp gohighlevel phía SePay','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
    
            return $this->response->setJSON(array("status" => true,"message" => "SePay đã hỗ trợ gỡ tích hợp phía SePay, Quý khách hãy truy cập vào trang quản trị GoHighLevel và gỡ trực tiếp"));

        }

        $GoHighLevelModel->delete($gohighlevel_id);

        add_user_log(array('data_id'=>$gohighlevel_id,'company_id' => $this->user_session['company_id'], 'data_type'=>'gohighlevel_delete','description'=>'Xóa tích hợp gohighlevel','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
    
        return $this->response->setJSON(array("status"=>true));
    }

    // update payment getway
    protected function update_payment_integration($gohighlevel_id){

        $GoHighLevelModel = model(GoHighLevelModel::class);
        $gohighlevel_details = $GoHighLevelModel->where(['id'=>$gohighlevel_id,'company_id'=>$this->user_session['company_id']])->get()->getRow();

        $bank_account_id = $gohighlevel_details->bank_account_id;
        $va_id = $gohighlevel_details->va_id;

        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $GoHighLevelModel = model(GoHighLevelModel::class);

        // Fetch bank account details
        $bank_account_details = $bankAccountModel
            ->select("tb_autopay_bank_account.bank_api,tb_autopay_bank_account.account_holder_name, tb_autopay_bank_account.account_number, tb_autopay_bank.brand_name, tb_autopay_bank.full_name, tb_autopay_bank.bin, tb_autopay_bank.code, tb_autopay_bank.logo_path, tb_autopay_bank.icon_path,tb_autopay_bank.id bank_id")
            ->join("tb_autopay_bank", "tb_autopay_bank_account.bank_id=tb_autopay_bank.id")
            ->where([
                'tb_autopay_bank_account.id' => $bank_account_id,
                'company_id' => $this->user_session['company_id']
            ])
            ->get()
            ->getRow();
    
       
    
        
        if (!is_object($bank_account_details))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>'Không tìm thấy tài khoản ngân hàng mà bạn chọn'));

        if (is_numeric($va_id) && $va_id > 0) {
            $va_details = $bankSubAccountModel
            ->join("tb_autopay_bank_account", "tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id", "left")
            ->where([
                'tb_autopay_bank_sub_account.id' => $va_id,
                'tb_autopay_bank_sub_account.acc_type' => "Real",
                'tb_autopay_bank_account.company_id' => $this->user_session['company_id']
            ])
            ->get()
            ->getRow();
            if (!is_object($va_details))
                return $this->response->setJSON(array('status'=>FALSE,'message'=>'Không tìm thấy VA mà bạn chọn'));
        } else {
           
            if ($bank_account_details->bank_api == 1 && in_array($bank_account_details->bank_id, [9,10,17,18]))
                return $this->response->setJSON(array('status'=>FALSE, 'message'=> 'Với các ngân hàng kết nối qua API, bạn cần chọn VA để nhận thanh toán. Vui lòng chọn 1 VA'));
            else
                $va_id = 0;
        }

        // Initialize account details
        $account_number = $bank_account_details->account_number;
        $account_holder_name = $bank_account_details->account_holder_name;
    
        // Check for virtual account details
           
        if (!empty($va_id)) {
            $account_number = $va_details->sub_account;
            $account_holder_name = $bank_account_details->account_holder_name;
        }
    
        $data_json_url = json_encode([
            'bank_bin' => $bank_account_details->bin,
            'bank_code' => $bank_account_details->code,
            'account_number' => $account_number,
            'prefix' => $this->request->getVar('paycode_prefix'),
            'site_url' => $this->request->getVar('site_url'),
            'bank_brand_name' => $bank_account_details->brand_name,
            'account_name' => $account_holder_name,
        ]);
        $data = array(
            'name' => $gohighlevel_details->name,
            'location_id' => $gohighlevel_details->location_id,
            'site_url' =>$gohighlevel_details->site_url ,
            'access_token' => $gohighlevel_details->access_token,
            'token_type' => $gohighlevel_details->token_type,
            'api_key_method_payment_live' => md5(time()),
            'publishablekey_method_payment_live' => md5(time()+300),
            'api_payment_url' => base_url()."/userapi/transactions/payment_url"."?d=".base64_encode($data_json_url),
            'api_query_url' => base_url()."/userapi/transactions/query_url",
            
        );
        // add payment gateway
        $gohightlevel_client = new GoHighLevelClient();
        $data_add_payment = $gohightlevel_client->addPayment($data);

        if( $data_add_payment['status_code'] > 201){
            log_message("error","error add payment gateway");
            return false;
        }

        // active payment GHL
        $data_active_payment = $gohightlevel_client->activePayment($data);

        if( $data_active_payment['status_code'] > 201){
            log_message("error","error active payment gateway");
            return false;
        }

        $data['integration_payment_id'] = $data_add_payment['raw_response']['_id'] ?? null;

        $result_update = $GoHighLevelModel->set($data)->where(["id" =>$gohighlevel_id,"company_id"=>$this->user_session['company_id']])->update();
            
        if($result_update){
            return true;
        }else{
            return false;
        }

    }
}
