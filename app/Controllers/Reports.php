<?php

namespace App\Controllers;
use App\Models\BankAccountModel;
use App\Models\BankSubAccountModel;
use App\Models\TransactionsModel;
use App\Models\BankModel;
use App\Models\ShopModel;
use CodeIgniter\Controller;
use App\Models\UserPermissionBankSubModel;

class Reports extends BaseController
{
     

    public function bankaccounts()
    {
        $data = [
            'page_title' => 'Báo cáo',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        if (!has_permission('ReportsAccountBalance', 'can_view_all')) {
            show_404();
        }

        $requestData = $this->request->getGet(['period', 'start_date', 'end_date']);

        if (! $this->validateData($requestData, [
            'period' => 'permit_empty|string|in_list[today,week,month,year,quarter,custom]',
            'start_date' => 'permit_empty|string|valid_date[Y-m-d]',
            'end_date' => 'permit_empty|string|valid_date[Y-m-d]',
        ])) {
            return redirect()->to(base_url('reports/bankaccounts'));
        }

        $data['period'] = $requestData['period'];

        if($data['period'] == 'custom') {
            $input_start = $requestData['start_date'];
            $input_end = $requestData['end_date'];

            if(!$input_end || !$input_start)
                $data['period'] = 'month';
            else {
                $start_date_obj = date_create_from_format('Y-m-d', $input_start);
                $end_date_obj = date_create_from_format('Y-m-d', $input_end);

                if(is_object($start_date_obj) && is_object($end_date_obj)) {
                    $start_date = $start_date_obj->format("Y-m-d 00:00:00");
                    $end_date = $end_date_obj->format("Y-m-d 23:59:59");
                    $data['period_text'] = '';
                    $data['period_days'] = round( (strtotime($end_date) - strtotime($start_date)) / (60 * 60 * 24) );
                    if(strtotime($start_date) > strtotime($end_date))
                        $data['period'] = 'month';
                
                } else
                    $data['period'] = 'month';
            
            }

        }
    

        if(!in_array($data['period'], ['today','month','year','quarter','week','custom']))
            $data['period'] = 'month';

        if($data['period'] == 'month') {
            $start_date = date("Y-m-01 00:00:00");
            $data['period_text'] = 'tháng này';
            $end_date = date('Y-m-d H:i:s');
        } else if($data['period'] == 'today') {
            $start_date = date("Y-m-d 00:00:00");
            $data['period_text'] = 'hôm nay';
            $end_date = date('Y-m-d H:i:s');
        } else if($data['period'] == 'week') {
            $start_date = date('Y-m-d 00:00:00', strtotime('monday this week'));
            $data['period_text'] = 'tuần này';
            $end_date = date('Y-m-d H:i:s');
        } else if($data['period'] == 'quarter') {
            $start_date = firstDayOf('quarter')->format("Y-m-d 00:00:00");
            $data['period_text'] = 'quý này';
        $end_date = date('Y-m-d H:i:s');
        } else if($data['period'] == 'year') {
            $start_date = date("Y-01-01 00:00:00");
            $data['period_text'] = 'năm này';
            
            $end_date = date('Y-m-d H:i:s');
        }

       // $start_date = date("Y-m-d 00:00:00");
       // $end_date = date("Y-m-d 23:59:59");
        $data['start_date'] = $start_date;
        $data['end_date'] = $end_date;

        $data['period_days'] = round( (strtotime($end_date) - strtotime($start_date)) / (60 * 60 * 24) );
        $transactionsModel = model(TransactionsModel::class);
        $bankAccountModel = model(BankAccountModel::class);
        $userPermissionBankModel = model(UserPermissionBankModel::class);
        $bankAccountCashflowModel = slavable_model(BankAccountCashflowModel::class, 'Reports');

        // has_bank_permission

        if(in_array($this->company_details->role,['Admin','SuperAdmin']))
            $data['bank_accounts'] = $bankAccountModel->select("tb_autopay_bank_account.id,tb_autopay_bank.short_name, tb_autopay_bank.brand_name, tb_autopay_bank.logo_path,tb_autopay_bank.icon_path, tb_autopay_bank_account.account_number, tb_autopay_bank_account.label, tb_autopay_bank_account.account_holder_name, tb_autopay_bank_account.accumulated, tb_autopay_bank_account.last_transaction")->join("tb_autopay_bank","tb_autopay_bank.id=tb_autopay_bank_account.bank_id")->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id'],'tb_autopay_bank_account.active' => 1])->orderBy('tb_autopay_bank_account.id', 'asc')->findAll();
        else
            $data['bank_accounts'] = $userPermissionBankModel->get_bank_accounts($this->user_details->id);


        $data['total_balance_begin'] = 0;
        $data['total_balance_end'] = 0;

        $data['total_credit'] = 0;
        $data['total_debit'] = 0;

        $data['account_data'] = [];

        $start_date = date('Y-m-d 00:00:00',  strtotime('-1 day', strtotime($start_date)));

        foreach($data['bank_accounts'] as $bank_account) {
            /*
            $data['account_data'][$bank_account->id]['balance_begin'] = $transactionsModel->get_account_balance($bank_account->id, $start_date);

            $data['account_data'][$bank_account->id]['balance_end'] = $transactionsModel->get_account_balance($bank_account->id, $end_date);

            $data['account_data'][$bank_account->id]['credit'] = $transactionsModel->get_account_credit($bank_account->id, $start_date, $end_date);

            $data['account_data'][$bank_account->id]['debit'] = $transactionsModel->get_account_debit($bank_account->id, $start_date, $end_date);
            */
            $data['account_data'][$bank_account->id]['balance_begin'] = $bankAccountCashflowModel->get_account_balance($bank_account->id, $start_date);

            $data['account_data'][$bank_account->id]['balance_end'] = $bankAccountCashflowModel->get_account_balance($bank_account->id, $end_date);

            $data['account_data'][$bank_account->id]['credit'] = $bankAccountCashflowModel->get_account_credit($bank_account->id, $start_date, $end_date);

            $data['account_data'][$bank_account->id]['debit'] = $bankAccountCashflowModel->get_account_debit($bank_account->id, $start_date, $end_date);

            $data['total_balance_begin'] = $data['total_balance_begin'] + $data['account_data'][$bank_account->id]['balance_begin'];
            $data['total_balance_end'] = $data['total_balance_end'] + $data['account_data'][$bank_account->id]['balance_end'];
            $data['total_credit'] = $data['total_credit'] + $data['account_data'][$bank_account->id]['credit'];
            $data['total_debit'] = $data['total_debit'] + $data['account_data'][$bank_account->id]['debit'];

        }

        if ($this->channel_partner) {
            $shopModel = model(ShopModel::class);
            $data['shops'] = $shopModel
                ->select(['tb_autopay_shop.id', 'tb_autopay_shop.name'])
                ->where(['company_id' => $this->user_session['company_id']])
                ->get()
                ->getResult();
        
            // Initialize totals
            $data['total_balance_begin_shop'] = 0;
            $data['total_balance_end_shop'] = 0;
            $data['total_credit_shop'] = 0;
            $data['total_debit_shop'] = 0;
        
            if (!empty($data['shops'])) {
                foreach ($data['shops'] as $shop) {
                    $result_statistics = $this->getCompanyUserShopStatistics($shop->id, $start_date, $end_date);
                    $balance_begin = $this->getAccountBalanceShop($shop->id, $start_date);
                    $balance_end = $this->getAccountBalanceShop($shop->id, $end_date);

                    $balance_begin_value = is_numeric($balance_begin) ? $balance_begin : 0;
                    $balance_end_value = is_numeric($balance_end) ? $balance_end : 0;
        
                    $credit_value = is_numeric($result_statistics->revenue_in) ? $result_statistics->revenue_in : 0;
                    $debit_value = is_numeric($result_statistics->revenue_out) ? $result_statistics->revenue_out : 0;
        
                    $data['account_data_shop'][$shop->id]['balance_begin'] = $balance_begin_value;
                    $data['account_data_shop'][$shop->id]['balance_end'] = $balance_end_value;
                    $data['account_data_shop'][$shop->id]['credit'] = $credit_value;
                    $data['account_data_shop'][$shop->id]['debit'] = $debit_value;
        
                    $data['total_balance_begin_shop'] += $balance_begin_value;
                    $data['total_balance_end_shop'] += $balance_end_value;
                    $data['total_credit_shop'] += $credit_value;
                    $data['total_debit_shop'] += $debit_value;
                }
            }
        }
                

        echo theme_view('templates/autopay/header', $data);
        echo theme_view('reports/bankaccounts', $data);
        echo theme_view('templates/autopay/footer', $data);
    }


    public function subbankaccounts()
    {
        $data = [
            'page_title' => 'Báo cáo',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        if (!has_permission('ReportsSubBankAccountIncome', 'can_view_all')) {
            show_404();
        }

        $data['period'] = $this->request->getGet('period');

        if($data['period'] == 'custom') {
            $input_start = $this->request->getGet('start_date');
            $input_end = $this->request->getGet('end_date');
            if(is_array($input_start) || is_array($input_end)){
                show_404();
            }
            if(!$input_end || !$input_start)
                $data['period'] = 'month';
            else {
                $start_date_obj = date_create_from_format('Y-m-d', $input_start);
                $end_date_obj = date_create_from_format('Y-m-d', $input_end);

                if(is_object($start_date_obj) && is_object($end_date_obj)) {
                    $start_date = $start_date_obj->format("Y-m-d 00:00:00");
                    $end_date = $end_date_obj->format("Y-m-d 23:59:59");
                    $data['period_text'] = '';
                    $data['period_days'] = round( (strtotime($end_date) - strtotime($start_date)) / (60 * 60 * 24) );
                    if(strtotime($start_date) > strtotime($end_date))
                        $data['period'] = 'month';
                
                } else
                    $data['period'] = 'month';
            
            }

        }
    

        if(!in_array($data['period'], ['today','month','year','quarter','week','custom']))
            $data['period'] = 'month';

        if($data['period'] == 'month') {
            $start_date = date("Y-m-01 00:00:00");
            $data['period_text'] = 'tháng này';
            $end_date = date('Y-m-d H:i:s');
        } else if($data['period'] == 'today') {
            $start_date = date("Y-m-d 00:00:00");
            $data['period_text'] = 'hôm nay';
            $end_date = date('Y-m-d H:i:s');
        } else if($data['period'] == 'week') {
            $start_date = date('Y-m-d 00:00:00', strtotime('monday this week'));
            $data['period_text'] = 'tuần này';
            $end_date = date('Y-m-d H:i:s');
        } else if($data['period'] == 'quarter') {
            $start_date = firstDayOf('quarter')->format("Y-m-d 00:00:00");
            $data['period_text'] = 'quý này';
        $end_date = date('Y-m-d H:i:s');
        } else if($data['period'] == 'year') {
            $start_date = date("Y-01-01 00:00:00");
            $data['period_text'] = 'năm này';
            
            $end_date = date('Y-m-d H:i:s');
        }
 
        $data['start_date'] = $start_date;
        $data['end_date'] = $end_date;

        $data['period_days'] = round( (strtotime($end_date) - strtotime($start_date)) / (60 * 60 * 24) );
        $transactionsModel = slavable_model(TransactionsModel::class, 'Reports');
        $bankAccountModel = model(BankAccountModel::class);
        $userPermissionBankModel = model(UserPermissionBankModel::class);
        $userPermissionBankSubModel = model(UserPermissionBankSubModel::class);

        $permissonBankSubAccounts = $userPermissionBankSubModel->select('sub_account_id')->where('user_id', $this->user_details->id)->where(['deleted_at' => null])->get()->getResult();

        $permissonBankSubAccounts = array_map(function($row) {
            return $row->sub_account_id;
        }, $permissonBankSubAccounts);

        $builder = $transactionsModel->select("sum(tb_autopay_sms_parsed.amount_in) as `income`, tb_autopay_sms_parsed.gateway, tb_autopay_sms_parsed.sub_account, tb_autopay_sms_parsed.account_number,tb_autopay_sms_parsed.transaction_date, count(tb_autopay_sms_parsed.id) as `total`,tb_autopay_bank_sub_account.label sub_label")
            ->join("tb_autopay_bank_account","tb_autopay_sms_parsed.bank_account_id=tb_autopay_bank_account.id")
            ->join("tb_autopay_bank_sub_account","tb_autopay_sms_parsed.sub_account=tb_autopay_bank_sub_account.sub_account")
            ->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id'],'tb_autopay_sms_parsed.amount_in>' =>0, "tb_autopay_sms_parsed.sub_account!=" => NULL, "tb_autopay_sms_parsed.transaction_date>=" => $start_date, "tb_autopay_sms_parsed.transaction_date<=" => $end_date, 'tb_autopay_sms_parsed.deleted_at' => NULL])->groupBy("tb_autopay_sms_parsed.sub_account")
            ->orderBy('income','DESC');

        if (!in_array($this->company_details->role,['Admin','SuperAdmin'])) {
            $builder->whereIn('tb_autopay_bank_sub_account.id', count($permissonBankSubAccounts) ? $permissonBankSubAccounts : [0]);
        }
        
        $data['sub_accounts_income'] = $builder->get()->getResult();

        // hanldle data chart
        $char_data = [];
        if (!empty($data['sub_accounts_income'])) {
            $builder = $transactionsModel->select("tb_autopay_sms_parsed.gateway,tb_autopay_sms_parsed.amount_in income , tb_autopay_sms_parsed.sub_account, tb_autopay_sms_parsed.account_number, tb_autopay_sms_parsed.transaction_date,tb_autopay_bank_sub_account.label sub_label")
                ->join("tb_autopay_bank_account", "tb_autopay_sms_parsed.bank_account_id = tb_autopay_bank_account.id")
                ->join("tb_autopay_bank_sub_account", "tb_autopay_sms_parsed.sub_account = tb_autopay_bank_sub_account.sub_account")
                ->where([
                    'tb_autopay_bank_account.company_id' => $this->user_session['company_id'],
                    'tb_autopay_sms_parsed.amount_in >' => 0,
                    'tb_autopay_sms_parsed.sub_account !=' => NULL,
                    'tb_autopay_sms_parsed.transaction_date >=' => $start_date,
                    'tb_autopay_sms_parsed.transaction_date <=' => $end_date,
                    'tb_autopay_sms_parsed.deleted_at' => NULL
                ])
                ->orderBy('tb_autopay_sms_parsed.transaction_date', 'DESC'); // Sắp xếp theo ngày giao dịch hoặc trường khác nếu cần
                
            if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])) {
                if (!empty($permissonBankSubAccounts)) {
                    $builder->whereIn('tb_autopay_bank_sub_account.id', $permissonBankSubAccounts);
                }
            }

            $data_convert = $builder->get()->getResult();

            foreach ($data_convert as $val) {
                $char_data[] = [ // Thêm mỗi kết quả vào mảng char_data
                    'name' => $val->gateway . " - " . $val->sub_account, // Sử dụng -> để truy cập thuộc tính của đối tượng
                    'income' => (int)$val->income, // Chuyển đổi thu nhập sang số nguyên
                    'time' => strtotime($val->transaction_date),
                    'time-db' => $val->transaction_date 
                ];
            }
        }

        $data['chart_data'] = $char_data;

        
        echo theme_view('templates/autopay/header', $data);
        echo view('reports/banksubaccounts', $data);
        echo theme_view('templates/autopay/footer', $data);
    }

    protected function getCompanyUserShopStatistics($shopId= null, $startDate = null, $endDate = null)
    {
        $builder = slavable_model(TransactionsModel::class, 'Reports')
            ->select([
                'SUM(tb_autopay_sms_parsed.amount_in) as revenue_in',
                'SUM(tb_autopay_sms_parsed.amount_out) as revenue_out'
            ])
            ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id = tb_autopay_sms_parsed.bank_account_id')
            ->join('tb_autopay_bank_sub_account', 'tb_autopay_bank_sub_account.sub_account = tb_autopay_sms_parsed.sub_account')
            ->join('tb_autopay_bank_shop_link', "tb_autopay_bank_shop_link.bank_account_id = tb_autopay_sms_parsed.bank_account_id AND tb_autopay_bank_shop_link.bank_sub_account_id = tb_autopay_bank_sub_account.id")
            ->join('tb_autopay_shop', 'tb_autopay_bank_shop_link.shop_id = tb_autopay_shop.id')
            ->where('tb_autopay_bank_account.company_id', $this->user_session['company_id'])
            ->where('tb_autopay_sms_parsed.parser_status', 'Success');

        if($shopId)
            $builder->where('tb_autopay_bank_shop_link.shop_id', $shopId);

        if ($startDate && $endDate)
            $builder->where([
                'tb_autopay_sms_parsed.transaction_date >=' => $startDate,
                'tb_autopay_sms_parsed.transaction_date <=' => $endDate
            ]);

        return $builder->get()->getRow();
    }

    protected function getAccountBalanceShop($shopId, $datetime = FALSE)
    {
        $builder = slavable_model(TransactionsModel::class, 'Reports')
            ->select([
                'SUM(tb_autopay_sms_parsed.accumulated) as accumulated'
            ])
            ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id = tb_autopay_sms_parsed.bank_account_id')
            ->join('tb_autopay_bank_sub_account', 'tb_autopay_bank_sub_account.sub_account = tb_autopay_sms_parsed.sub_account')
            ->join('tb_autopay_bank_shop_link', "tb_autopay_bank_shop_link.bank_account_id = tb_autopay_sms_parsed.bank_account_id AND tb_autopay_bank_shop_link.bank_sub_account_id = tb_autopay_bank_sub_account.id")
            ->join('tb_autopay_shop', 'tb_autopay_bank_shop_link.shop_id = tb_autopay_shop.id')
            ->where('tb_autopay_bank_account.company_id', $this->user_session['company_id'])
            ->where('tb_autopay_sms_parsed.parser_status', 'Success')
            ->where('tb_autopay_bank_shop_link.shop_id', $shopId);

        if($datetime == FALSE)
            $datetime = date("Y-m-d");
        else 
            $datetime = date('Y-m-d', strtotime($datetime));

        $builder->where([
            'tb_autopay_sms_parsed.transaction_date <=' => $datetime
        ]);

        return $builder->get()->getRow();
    }
}