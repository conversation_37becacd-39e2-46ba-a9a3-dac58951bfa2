<?php

namespace App\Controllers;

use App\Actions\SaveUTMTracking;
use App\Models\UserModel;
use App\Models\CompanyUserModel;
use App\Models\ConfigurationModel;
use CodeIgniter\Controller;
use App\Libraries\Captcha;
use App\Models\ReferralCodeModel;
use CodeIgniter\API\ResponseTrait;
use App\Models\ZNSNotificationModel;

class Register extends Controller
{
    use ResponseTrait;
    
    public function index() {
        $data = [
            'page_title' => 'Đăng ký',
        ];

        $captcha = new Captcha;

        $data['captcha_enabled'] = $captcha->getEnabled();
        $data['captcha_driver'] = $captcha->getDriver();
        $data['captcha_site_key'] = $captcha->getSiteKey();

        helper(['general','form','url','text']);

        //if(isset($_SERVER['REMOTE_ADDR']) && $_SERVER['REMOTE_ADDR'] != "103.255.238.20")
        //    show_404();

        if(check_logged_user()) {
           return redirect()->to(base_url());
        }
                
        $securityConfig = config(\Config\Security::class);
        $data['enabled_login_with_phone_number'] = is_admin() ? true : ($securityConfig->enabledLoginWithPhoneNumber ?? is_admin()); 
        $data['is_speaker_user'] = $this->isFromSpeakerSite();

        echo view('register/index', $data);
    }

    public function do_register() {
        //if(isset($_SERVER['REMOTE_ADDR']) && $_SERVER['REMOTE_ADDR'] != "103.255.238.20")
        //    show_404();
        helper(['general','form','url','text','cookie']);

        if(check_logged_user()) {
            return redirect()->to(base_url());
        }
      
        helper(['general','form','url','text']);
        $request = \Config\Services::request();

        if ($request->getMethod(true) != 'POST')
            return '';
 
        $validation =  \Config\Services::validation();
        
        $securityConfig = config(\Config\Security::class);
        $enabledLoginWithPhoneNumber = is_admin() ? true : ($securityConfig->enabledLoginWithPhoneNumber ?? is_admin());
        
        $rules = [
            'firstname' =>['label' => 'Họ và đệm', 'rules' => "required|max_length[20]"],
            'lastname' => ['label' => 'Tên', 'rules' => "required|max_length[80]"],
            'company_short_name' => ['label' => 'Tên viết tắt', 'rules' => "required|max_length[20]"],
            'company_full_name' => ['label' => 'Tên đầy đủ công ty/ tổ chức', 'rules' => "required|max_length[200]"],
            'phonenumber' => ['label' => 'Số điện thoại', 'rules' => "required|max_length[20]|valid_phone_number"],
            //'email' => ['label' => 'Email', 'rules' => "required|valid_email|is_unique[tb_autopay_user.email]"],
            'email' => ['label' => 'Email', 'rules' => "required|valid_email"],
            'password' => ['label' => 'Mật khẩu', 'rules' => "required|min_length[6]|max_length[1000]"],
            'passconf' => ['label' => 'Nhập lại mật khẩu', 'rules' => 'required|matches[password]'],
           // 'g-recaptcha-response' => ['label' => 'Xác thực robot', 'rules' => "required|valid_recaptcha"]
        ];

        $captcha = new Captcha;

        if ($captcha->getEnabled()) {
            if ($captcha->getDriver() === 'turnstile') {
                $rules['cf-turnstile-response'] = ['label' => 'Captcha', 'rules' => 'required|valid_turnstile'];
            } else if ($captcha->getDriver() === 'recaptcha') {
                $rules['g-recaptcha-response'] = ["label" => "Recaptcha", "rules" => "required|valid_recaptcha"];
            }
        }

        $firstName = mb_convert_case(xss_clean($request->getPost('firstname')), MB_CASE_TITLE, 'UTF-8');
        $lastName = mb_convert_case(xss_clean($request->getPost('lastname')), MB_CASE_TITLE, 'UTF-8');
        $companyFullName = xss_clean($request->getPost('company_full_name'));
        $companyShortName = xss_clean($request->getPost('company_short_name'));
        $email = trim($this->request->getVar('email', FILTER_SANITIZE_EMAIL));
        $phoneNumber = trim(xss_clean($this->request->getVar('phonenumber')));

        $userModel = model(UserModel::class);

        $isFromSpeakerSite = $this->isFromSpeakerSite();

        if ($isFromSpeakerSite) {
            unset($rules['firstname'], $rules['lastname'], $rules['email'], $rules['company_short_name']);
            $rules['fullname'] = ['label' => 'Họ và tên', 'rules' => "required|max_length[100]"];

            $fullName = mb_convert_case(xss_clean($request->getPost('fullname')), MB_CASE_TITLE, 'UTF-8');
            [$firstName, $lastName] = $this->splitFullName($fullName);

            $companyFullName = xss_clean($request->getPost('company_full_name'));
            $companyShortName = $this->generateCompanyShortName($companyFullName);

            $email = $userModel->generateUniqueEmail($phoneNumber);
        }

        if(! $this->validate($rules)) {
            $errors = $validation->getErrors();

            if (isset($errors['cf-turnstile-response']) || isset($errors['g-recaptcha-response'])) {

                $message = '
------------------------------

‼️ Lỗi xác thực captcha khi KH đăng ký SePay:

⏰ Thực hiện lúc: ' . date("Y-m-d H:i:s") . '

💬 Thông báo lỗi: ' . ($errors['cf-turnstile-response'] ?? $errors['g-recaptcha-response']) . '

#️⃣ Thông tin KH đăng ký:

    - Họ và đệm: ' . ($firstName ?? '(trống)') . '
    - Tên: ' . ($lastName ?? '(trống)') . '
    - Số điện thoại: ' . ($request->getPost('phonenumber') ?? '(trống)') . '
    - Email: ' . ($request->getPost('email') ?? '(trống)') . '
    - Tên công ty/tổ chức: ' . ($companyFullName ?? '(trống)') . '
    - Tên viết tắt: ' . ($companyShortName ?? '(trống)') . '

------------------------------
                        ';

                $telegramQueueModel = model(\App\Models\NotificationTelegramQueueModel::class);
                $telegramQueueModel->insert([
                    'chat_id' => $captcha->getConfig()->telegramChatId,
                    'status' => 'Pending',
                    'message' => $message
                ]);
            }

            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode("", $errors)));
        }

        $email_check = $userModel->where(['email' => $email])->get()->getRow();

        if(is_object($email_check))
            return $this->fail(['email' => 'Email bạn nhập đã tồn tại trên hệ thống! Vui lòng chọn email khác']);
            
        $password_text = trim($this->request->getVar('password'));
        $user_data = [
            'firstname' => $firstName,
            'lastname' => $lastName,
            'email' => $email,
            'phonenumber' => $phoneNumber,
            'password' => password_hash($password_text, PASSWORD_DEFAULT),
            'active' => 1,
        ];
                
        if ($enabledLoginWithPhoneNumber) {
            
            if ($userModel->where('username', $phoneNumber)->countAllResults() > 0) {
                return $this->fail(['phonenumber' => 'Số điện thoại bạn nhập đã tồn tại trên hệ thống! Vui lòng chọn số điện thoại khác']);
            }
            
            $user_data['username'] = $phoneNumber;
        }
       
        $companyModel = model(CompanyModel::class);
        $companyUserModel = model(CompanyUserModel::class);

        $user_id = $userModel->insert($user_data);

        //ctype_alpha($string) || is_numeric($string)

        
        if(is_numeric($user_id) && $user_id > 0) {
            $tr_gcid = get_cookie('tr_gcid',true);
            if($tr_gcid == null)
                $tr_gcid = '';

            $company_id = $companyModel->insert(['full_name' => $companyFullName, 'short_name' => $companyShortName, 'tr_gcid' => $tr_gcid]);

            if(is_numeric($company_id) && $company_id > 0) { 
                if (
                    isset($_COOKIE['utm_source'])
                    && isset($_COOKIE['utm_medium'])
                    && isset($_COOKIE['utm_campaign'])
                    && $_COOKIE['utm_source'] === 'INV'
                    && $_COOKIE['utm_medium'] === 'RFTRA'
                ) {
                    $referralCodeModel = model(ReferralCodeModel::class);
    
                    $referralCode = $referralCodeModel
                        ->where('code', esc($_COOKIE['utm_campaign']))
                        ->where('is_active', true)
                        ->first();
    
                    if ($referralCode) {
                        $referralCodeModel->validateAndUseCode($company_id, false, $referralCode->code, 'first_connect_bank');
                    }
                }

                $companyUserModel->insert(['user_id' => $user_id, 'company_id' => $company_id,'role' => 'SuperAdmin']);
                $userPermissionFeatureModel = model(UserPermissionFeatureModel::class);

                $userPermissionFeatureModel->initialize_permission($user_id, $company_id);

                $configurationModel = model(ConfigurationModel::class);
                $configurationModel->initialize_data($company_id);

                SaveUTMTracking::run($company_id);

                // Tạo cấu hình ZNS notification
                $znsNotificationModel = model(ZNSNotificationModel::class);
                $znsNotificationModel->insert([
                    'company_id' => $company_id,
                    'default_phone' => $phoneNumber,
                    'active_phone_type' => 'default'
                ]);

                if ($isFromSpeakerSite) {
                    $this->activateSpeakerBillingProduct($company_id);
                }
            } else {
                return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không thể thêm công ty vào hệ thống"));
            }

        } else {
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không thể thêm người dùng vào hệ thống"));
        }


        $userModel = model(UserModel::class);

        $authen = $userModel->login($email, $password_text, $remember=FALSE);

        if (! $isFromSpeakerSite) {
            set_alert('success','Đăng ký tài khoản thành công. Vui lòng chọn gói dịch vụ phù hợp với bạn');
        }
 
        return $this->response->setJSON([
            'status' => true,
            'redirect_url' => $isFromSpeakerSite ? base_url('outputdevice') : base_url('company/plans'),
        ]);
    }

    protected function isFromSpeakerSite(): bool
    {
        if (! service('speakerBillingFeature')->enabled) {
            return false;
        }

        helper('cookie');

        if ($this->request->getGet('onboarding') === 'loa') {
            set_cookie('onboarding', 'loa', 86400);
            return true;
        }

        if (get_cookie('onboarding') === 'loa') {
            return true;
        }

        $referer = $this->request->getServer('HTTP_REFERER');

        if ($referer && strpos($referer, 'loa.sepay.vn') !== false) {
            return true;
        }

        return false;
    }

    protected function generateCompanyShortName($fullName) {
        $words = preg_split('/\s+/', trim($fullName));
        $shortName = '';

        foreach ($words as $word) {
            if (!empty($word)) {
                $shortName .= mb_substr($word, 0, 1, 'UTF-8');
            }
        }

        $shortName = ! empty($shortName) ? $shortName : substr($fullName, 0, 10);

        return substr($shortName, 0, 20);
    }

    protected function splitFullName($fullName)
    {
        $nameParts = explode(' ', $fullName);
            
        $firstName = array_pop($nameParts);
        $lastName = implode(' ', $nameParts);
        
        return [$firstName, $lastName];
    }

    protected function activateSpeakerBillingProduct($companyId)
    {
        $speakerBillingFeature = service('speakerBillingFeature');

        $speakerBillingFeature->withCompanyContext($companyId);

        if ($speakerBillingFeature->companyContext()->isSubscribedSpeakerBillingProduct()) {
            return;
        }

        if ( ! $speakerBillingFeature->companyContext()->canOrderSpeakerBillingProduct()) {
            return;
        }

        $speakerBillingFeature->withOrderContext();

        try {
            $speakerBillingFeature->orderContext()->checkout();

            set_alert('success', 'Đăng ký tài khoản thành công, hãy bắt đầu thêm loa thanh toán ngay nào!');
        } catch (\Exception $e) {
            log_message('error', $e->getMessage());
        }   
    }
}
