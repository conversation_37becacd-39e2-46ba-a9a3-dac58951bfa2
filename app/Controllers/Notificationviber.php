<?php

namespace App\Controllers;

use App\Features\Store\StoreFeature;
use App\Libraries\ViberChannelClient;
use App\Models\BankAccountModel;
use App\Models\BankSubAccountModel;
use App\Models\NotificationViberModel;
use CodeIgniter\API\ResponseTrait;
use Exception;

class Notificationviber extends BaseController
{
    use ResponseTrait;

    public function __construct()
    {
        try {
            if (!(new \App\Features\Viber\ViberFeature)->enabled) {
                show_404();
            }
        } catch (\Throwable $e) {
            show_404();
        }
    }

    public function index()
    {
        if (! has_permission('NotificationViber', 'can_view_all')) {
            show_404();
        }

        $storeFeature = new StoreFeature();
        $storeFeature->withCompanyContext($this->user_session['company_id']);
        $viber_total = $storeFeature->viberBuilder()->countAllResults();

        return theme_view('viber/index', [
            'page_title' => 'Tích hợp Viber',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'viber_total' => $viber_total,
        ]);
    }

    public function ajax_list()
    {
        if (
            ! has_permission('NotificationViber', 'can_view_all')
            || ! $this->request->isAJAX()
            || $this->request->getMethod(true) !== 'POST'
        ) {
            show_404();
        }

        if (session()->get('shop_billing')) {
            $storeId = $this->request->getVar('store_id');
            $type = $this->request->getVar('type');

            $storeFeature = new StoreFeature;
            $storeFeature->withCompanyContext($this->user_session['company_id']);

            $data = $storeFeature->getStoreVibers($storeId ? [$storeId] : [], $type);

            return $this->respond(['data' => $data]);
        } else {
            $notificationViberModel = model(NotificationViberModel::class);

            $data = [];

            foreach ($notificationViberModel->getDatatables($this->request, $this->company_details->id) as $integration) {
                $row = [];
                $row[] = $integration->id;
                $row[] = sprintf('%s <br> <small class="text-muted">ID: %s</small>', $integration->description, $integration->channel_id);
                $bankText = '<ul class="list-unstyled mb-0">';

                if (! empty($integration->account_number)) {
                    $bankText .= "<li><i class='bi bi-bank text-primary me-2'></i>Tài khoản chính: <strong>" . esc($integration->account_number) . ' - ' . esc($integration->brand_name) . "</strong></li>";
                } else {
                    $bankText .= "<li><i class='bi bi-bank text-primary me-2'></i>Tài khoản chính: <strong>Tất cả ngân hàng</strong></li>";
                }

                if (! empty($integration->sub_account) && ! empty($integration->account_number)) {
                    $bankText .= "<li><i class='bi bi-credit-card text-success me-2'></i>Tài khoản phụ: <strong>" . esc($integration->sub_account) . "</strong></li>";
                } elseif (empty($integration->sub_account) && ! empty($integration->account_number)) {
                    $bankText .= "<li><i class='bi bi-credit-card text-success me-2'></i>Tài khoản phụ: <strong>Không chọn VA</strong></li>";
                } else {
                    $bankText .= "<li><i class='bi bi-credit-card text-success me-2'></i>Tài khoản phụ: <strong>Tất cả VA</strong></li>";
                }
                $bankText .= '</ul>';
                $row[] = $bankText;
                $row[] = sprintf('<span class="badge bg-%s">%s</span>', $integration->active ? 'success' : 'danger', $integration->active ? 'Kích hoạt' : 'Tạm dừng');
                $row[] = date('d/m/Y H:i:s', strtotime($integration->created_at));
                $row[] = (has_permission('NotificationViber', 'can_edit') ? sprintf('<a href="%s" class="btn btn-sm btn-outline-warning ms-2 me-2"><i class="bi bi-pencil"></i> Sửa</a>', base_url('notificationviber/edit/' . $integration->id)) : '')
                    . (has_permission('NotificationViber', 'can_delete') ? sprintf('<button type="button" class="btn btn-sm btn-outline-danger" data-id="%s" data-bs-toggle="modal" data-bs-target="#deleteModal"><i class="bi bi-trash3"></i> Xóa</button>', $integration->id) : '');
                $data[] = $row;
            }

            return $this->respond([
                'draw' => $this->request->getPost('draw'),
                'recordsTotal' => $notificationViberModel->countAll($this->company_details->id),
                'recordsFiltered' => $notificationViberModel->countFiltered($this->request, $this->company_details->id),
                'data' => $data,
            ]);
        }
    }

    public function step1()
    {
        if (! has_permission('NotificationViber', 'can_add')) {
            show_404();
        }

        $bankAccounts = model(BankAccountModel::class)
            ->where('company_id', $this->company_details->id)
            ->where('active', true)
            ->findAll();

        if (empty($bankAccounts)) {
            session()->setFlashdata('alert-error', 'Vui lòng liên kết tài khoản ngân hàng trước khi tích hợp');

            return redirect()->to('bankaccount/connect');
        }

        session()->remove('notification_viber_integration');

        return view('viber/integration/step1', [
            'page_title' => 'Tích hợp Viber - Kết nối kênh',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'default_store_id' => $this->request->getGet('store_id'),
            'step' => 1,
        ]);
    }

    public function ajax_test_auth_token()
    {
        if (
            ! has_permission('NotificationViber', 'can_add')
            || ! $this->request->isAJAX()
            || $this->request->getMethod(true) !== 'POST'
        ) {
            show_404();
        }

        if (! $this->validate([
            'auth_token' => 'required|min_length[30]',
        ], [
            'auth_token' => [
                'required' => 'Auth Token không được để trống',
                'min_length' => 'Auth Token không hợp lệ',
            ],
        ])) {
            return $this->failValidationErrors($this->validator->getErrors());
        }

        $authToken = $this->request->getPost('auth_token');
        $result = $this->testViberAuthToken($authToken);

        if ($result['success']) {
            return $this->respond([
                'status' => true,
                'message' => $result['message'],
                'data' => $result['data'],
            ]);
        }

        return $this->failValidationErrors(['auth_token' => $result['message']]);
    }

    public function ajax_step1()
    {
        if (
            ! has_permission('NotificationViber', 'can_add')
            || ! $this->request->isAJAX()
            || $this->request->getMethod(true) !== 'POST'
        ) {
            show_404();
        }

        $rules = [
            'auth_token' => 'required|min_length[30]',
            'channel_name' => 'required',
            'channel_id' => 'required',
            'sender_id' => 'required',
        ];

        if (session()->get('shop_billing')) {
            $rules['transaction_type'] = 'required|in_list[In_only,Out_only,All]';
        }

        if (! $this->validate($rules)) {
            return $this->failValidationErrors($this->validator->getErrors());
        }

        $data = [
            'auth_token' => trim($this->request->getPost('auth_token')),
            'channel_name' => $this->request->getPost('channel_name'),
            'channel_id' => $this->request->getPost('channel_id'),
            'channel_avatar' => $this->request->getPost('channel_avatar'),
            'members' => $this->request->getPost('members'),
            'sender_id' => $this->request->getPost('sender_id'),
            'transaction_type' => $this->request->getPost('transaction_type'),
            'default_store_id' => $this->request->getPost('default_store_id'),
        ];

        $members = json_decode($data['members'], true);
        if (! empty($members)) {
            $validSenderIds = array_column($members, 'id');
            if (! in_array($data['sender_id'], $validSenderIds)) {
                return $this->failValidationErrors(['sender_id' => 'Người gửi được chọn không hợp lệ']);
            }
        }

        $result = $this->setupViberWebhook($data['auth_token']);

        if (! $result['success']) {
            return $this->respond([
                'status' => false,
                'message' => $result['message'],
            ]);
        }

        session()->set('notification_viber_integration', $data);

        return $this->respond([
            'status' => true,
            'data' => [
                'redirect_url' => base_url('notificationviber/step2' . ($this->request->getPost('default_store_id') ? '?store_id=' . $this->request->getPost('default_store_id') : '')),
            ],
        ]);
    }

    public function step2()
    {
        if (! has_permission('NotificationViber', 'can_add')) {
            show_404();
        }

        $integrationData = session()->get('notification_viber_integration');

        if (! $integrationData) {
            return redirect()->to('notificationviber/step1' . ($this->request->getGet('store_id') ? '?store_id=' . $this->request->getGet('store_id') : ''));
        }

        $data = [
            'page_title' => 'Tích hợp Viber - Chọn tài khoản ngân hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'step' => 2,
            'integration' => $integrationData,
            'default_store_id' => $this->request->getGet('store_id') ?? $integrationData['default_store_id'],
        ];

        if (session()->get('shop_billing')) {
            $storeFeature = new StoreFeature;
            $storeFeature->withCompanyContext($this->user_session['company_id']);

            $data['stores'] = $storeFeature->getLinkableViberStores($integrationData['channel_id'], $integrationData['transaction_type']);

            if ($data['default_store_id']) {
                $stores = $data['stores'];
                $defaultStoreKey = array_search($data['default_store_id'], array_column($stores, 'id'));

                if ($defaultStoreKey !== false) {
                    $defaultStore = $stores[$defaultStoreKey];
                    unset($stores[$defaultStoreKey]);
                    array_unshift($stores, $defaultStore);
                    $data['stores'] = $stores;
                }
            }
        } else {
            $bankAccounts = model(BankAccountModel::class)
                ->select(['tb_autopay_bank_account.id', 'tb_autopay_bank_account.account_number', 'tb_autopay_bank_account.account_holder_name', 'tb_autopay_bank.brand_name', 'tb_autopay_bank.icon_path'])
                ->join('tb_autopay_bank', 'tb_autopay_bank.id = tb_autopay_bank_account.bank_id')
                ->where('tb_autopay_bank_account.company_id', $this->company_details->id)
                ->where('tb_autopay_bank_account.active', true)
                ->findAll();

            if (empty($bankAccounts)) {
                session()->setFlashdata('alert-error', 'Vui lòng liên kết tài khoản ngân hàng trước khi tích hợp');

                return redirect()->to('bankaccount/connect');
            }

            $bankSubAccounts = model(BankSubAccountModel::class)
                ->select(['tb_autopay_bank_sub_account.id', 'tb_autopay_bank_sub_account.bank_account_id', 'tb_autopay_bank_sub_account.sub_account', 'tb_autopay_bank_sub_account.sub_holder_name', 'tb_autopay_bank_sub_account.label'])
                ->whereIn('bank_account_id', array_column($bankAccounts, 'id'))
                ->where('active', true)
                ->where('va_active', true)
                ->findAll();

            $data['bankAccounts'] = $bankAccounts;
            $data['bankSubAccounts'] = $bankSubAccounts;
        }

        return theme_view('viber/integration/step2', $data);
    }

    public function ajax_step2()
    {
        if (! has_permission('NotificationViber', 'can_add')) {
            show_404();
        }

        if (! ($integration = session()->get('notification_viber_integration'))) {
            return $this->respond([
                'status' => false,
                'data' => [
                    'redirect_url' => base_url('notificationviber/step1' . (isset($integration['default_store_id']) ? '?store_id=' . $integration['default_store_id'] : '')),
                ],
            ]);
        }

        if (session()->get('shop_billing')) {
            $storeFeature = new StoreFeature();
            $storeFeature->withCompanyContext($this->user_session['company_id']);

            $storeIds = $this->request->getVar('store_ids');

            if (!is_array($storeIds) || empty($storeIds)) {
                return $this->respond(['status' => false, 'message' => 'Vui lòng chọn ít nhất một cửa hàng']);
            }

            $linkableStores = $storeFeature->getLinkableViberStores($integration['channel_id'], $integration['transaction_type']);

            if (count(array_diff($storeIds, array_column($linkableStores, 'id'))) > 0) {
                return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng tải lại trang']);
            }

            $data = [
                'links' => array_column($linkableStores, 'links'),
                'store_ids' => $storeIds,
            ];
        } else {
            $data = [
                'bank_account_id' => $this->request->getPost('bank_account_id'),
                'sub_account_id' => $this->request->getPost('sub_account_id'),
                'transaction_type' => $this->request->getPost('transaction_type'),
                'webhook_type' => $this->request->getPost('webhook_type'),
                'verify_payment' => $this->request->getPost('verify_payment'),
                'amount_gte' => $this->request->getPost('amount_gte'),
                'amount_lte' => $this->request->getPost('amount_lte'),
                'contains_content' => $this->request->getPost('contains_content'),
                'ignore_phrases' => $this->request->getPost('ignore_phrases'),
            ];

            $rules = [
                'bank_account_id' => ['permit_empty', 'integer'],
                'sub_account_id' => ['permit_empty', 'integer'],
                'transaction_type' => ['required', 'string', 'in_list[All,In_only,Out_only]'],
                'webhook_type' => ['required', 'string', 'in_list[No,All,Success,Failed]'],
                'verify_payment' => ['required', 'string', 'in_list[Skip,Success_Or_Failed,Success,Failed]'],
                'amount_gte' => ['permit_empty', 'integer', 'greater_than[0]'],
                'amount_lte' => ['permit_empty', 'integer', 'greater_than_equal_to[' . $data['amount_gte'] . ']'],
                'contains_content' => ['permit_empty', 'string'],
                'ignore_phrases' => ['permit_empty', 'string'],
            ];

            if (! $this->validateData($data, $rules)) {
                return $this->failValidationErrors($this->validator->getErrors());
            }

            if ($data['bank_account_id']) {
                $bankAccount = model(BankAccountModel::class)
                    ->where('company_id', $this->company_details->id)
                    ->where('active', true)
                    ->find($data['bank_account_id']);

                if (! $bankAccount) {
                    return $this->failValidationErrors(['bank_account_id' => 'Tài khoản ngân hàng không tồn tại']);
                }

                if ($data['sub_account_id']) {
                    $bankSubAccount = model(BankSubAccountModel::class)
                        ->where('bank_account_id', $data['bank_account_id'])
                        ->where('active', true)
                        ->where('va_active', true)
                        ->find($data['sub_account_id']);

                    if (! $bankSubAccount) {
                        return $this->failValidationErrors(['sub_account_id' => 'Tài khoản ngân hàng con không tồn tại']);
                    }
                }
            }
        }

        session()->set('notification_viber_integration', array_merge($integration, $data));

        return $this->respond([
            'status' => true,
            'data' => [
                'redirect_url' => base_url('notificationviber/step3' . (isset($integration['default_store_id']) ? '?store_id=' . $integration['default_store_id'] : '')),
            ],
        ]);
    }

    public function step3()
    {
        if (! has_permission('NotificationViber', 'can_add')) {
            show_404();
        }

        $integration = session()->get('notification_viber_integration');

        if (! $integration) {
            session()->remove('notification_viber_integration');

            return redirect()->to('notificationviber/step2' . (isset($integration['default_store_id']) ? '?store_id=' . $integration['default_store_id'] : ''));
        }

        return view('viber/integration/step3', [
            'page_title' => 'Tích hợp Viber - Cấu hình tin nhắn',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'step' => 3,
            'integration' => session()->get('notification_viber_integration'),
        ]);
    }

    public function ajax_step3()
    {
        if (
            ! has_permission('NotificationViber', 'can_add')
            || ! $this->request->isAJAX()
            || $this->request->getMethod(true) !== 'POST'
        ) {
            show_404();
        }

        $integration = session()->get('notification_viber_integration');

        if (! $integration) {
            session()->remove('notification_viber_integration');

            return $this->failValidationErrors(['error' => 'Session hết hạn. Vui lòng bắt đầu lại']);
        }

        $data = [
            'is_template_custom' => $this->request->getPost('is_template_custom'),
            'template_custom' => trim($this->request->getPost('template_custom')),
            'template_name' => $this->request->getPost('template_name'),
            'hide_accumulated' => $this->request->getPost('hide_accumulated') == 1 ? 1 : 0,
            'hide_details_link' => $this->request->getPost('hide_details_link') == 1 ? 1 : 0,
        ];

        $rules = [
            'is_template_custom' => 'required|in_list[0,1]',
            'template_custom' => 'required_with[is_template_custom]',
            'template_name' => 'required|in_list[template_1,template_2,template_3]',
            'hide_accumulated' => 'required|in_list[0,1]',
            'hide_details_link' => 'required|in_list[0,1]',
        ];

        $messages = [
            'is_template_custom' => [
                'required' => 'Chọn mẫu tin nhắn là bắt buộc',
                'in_list' => 'Chọn mẫu tin nhắn không hợp lệ',
            ],
            'template_custom' => [
                'required_with' => 'Chọn mẫu tin nhắn là bắt buộc',
            ],
            'template_name' => [
                'required' => 'Chọn mẫu tin nhắn là bắt buộc',
                'in_list' => 'Chọn mẫu tin nhắn không hợp lệ',
            ],
            'hide_accumulated' => [
                'required' => 'Chọn ẩn số dư tài khoản là bắt buộc',
                'in_list' => 'Chọn ẩn số dư tài khoản không hợp lệ',
            ],
            'hide_details_link' => [
                'required' => 'Chọn ẩn liên kết chi tiết là bắt buộc',
                'in_list' => 'Chọn ẩn liên kết chi tiết không hợp lệ',
            ],
        ];

        if (! $this->validateData($data, $rules, $messages)) {
            return $this->failValidationErrors($this->validator->getErrors());
        }

        if (! empty($data['template_custom'])) {
            $data['template_custom'] = allowed_tags($data['template_custom']);
        }

        if (session()->get('shop_billing')) {
            $storeFeature = new StoreFeature;
            $storeFeature->withCompanyContext($this->user_session['company_id']);
            $storeFeature->linkStoresToViber($integration['store_ids'], $integration);
        } else {
            $notificationViberId = model(NotificationViberModel::class)->insert([
                'company_id' => $this->company_details->id,
                'bank_account_id' => $integration['bank_account_id'],
                'sub_account_id' => $integration['sub_account_id'],
                'channel_id' => $integration['channel_id'],
                'auth_token' => $integration['auth_token'],
                'sender_id' => $integration['sender_id'],
                'description' => $integration['channel_name'],
                'transaction_type' => $integration['transaction_type'],
                'webhook_type' => $integration['webhook_type'],
                'verify_payment' => $integration['verify_payment'],
                'amount_in_great_than_equal_to' => $integration['amount_gte'],
                'amount_in_less_than_equal_to' => $integration['amount_lte'],
                'contains_content' => $integration['contains_content'],
                'ignore_phrases' => $integration['ignore_phrases'],
                'hide_accumulated' => $data['hide_accumulated'] ? 1 : 0,
                'hide_details_link' => $data['hide_details_link'] ? 1 : 0,
                'is_template_custom' => $data['is_template_custom'] ? 1 : 0,
                'template_custom' => $data['template_custom'],
                'template_name' => $data['template_name'],
                'active' => true,
            ]);
        }

        add_user_log([
            'data_id' => $notificationViberId ?? 0,
            'company_id' => $this->company_details->id,
            'data_type' => 'notification_viber_add',
            'description' => 'Thêm tích hợp thông báo Viber',
            'user_id' => $this->user_details->id,
            'ip' => $this->request->getIPAddress(),
            'user_agent' => $this->request->getUserAgent()->getAgentString(),
        ]);

        return $this->respond(['status' => true]);
    }

    public function ajax_test_message()
    {
        if (
            ! has_permission('NotificationViber', 'can_add')
            || ! $this->request->isAJAX()
            || $this->request->getMethod(true) !== 'POST'
        ) {
            show_404();
        }

        if (! ($integration = session()->get('notification_viber_integration'))) {
            return $this->failValidationErrors(['error' => 'Session hết hạn. Vui lòng bắt đầu lại']);
        }

        $message = $this->request->getPost('message') ?: 'Đây là tin nhắn test từ SePay! 🎉';
        $result = $this->sendTestMessage($integration['auth_token'], $message);

        if ($result['success']) {
            return $this->respond([
                'status' => true,
                'message' => $result['message'],
            ]);
        }

        return $this->failValidationErrors(['error' => $result['message']]);
    }

    public function step4()
    {
        if (! has_permission('NotificationViber', 'can_add')) {
            show_404();
        }

        $integration = session()->get('notification_viber_integration');

        if (! $integration) {
            return redirect()->to('notificationviber/step1');
        }

        session()->remove('notification_viber_integration');

        $storeFeature = new StoreFeature;
        $storeFeature->withCompanyContext($this->user_session['company_id']);

        $data['stores'] = $storeFeature->getLinkedViberStores($integration['channel_id'], $integration['transaction_type']);

        return view('viber/integration/step4', [
            'page_title' => 'Tích hợp Viber - Hoàn tất',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'step' => 4,
            'integration' => $integration,
        ]);
    }

    public function ajax_delete($id = null)
    {
        if (! has_permission('NotificationViber', 'can_delete')) {
            show_404();
        }

        if (! $id) {
            return $this->respond([
                'status' => false,
                'message' => 'Tích hợp không tồn tại',
            ]);
        }

        $storeFeature = new StoreFeature();
        $storeFeature->withCompanyContext($this->user_session['company_id']);

        $viber = $storeFeature->viberBuilder()->where('id', $id)->first();

        if (! $viber) {
            return $this->failNotFound();
        }

        $storeFeature->deleteStoreViber($viber->channel_id, $viber->transaction_type);

        add_user_log([
            'data_id' => $id,
            'company_id' => $this->company_details->id,
            'data_type' => 'notification_viber_delete',
            'description' => 'Xóa tích hợp thông báo Viber',
            'user_id' => $this->user_details->id,
            'ip' => $this->request->getIPAddress(),
            'user_agent' => $this->request->getUserAgent()->getAgentString(),
        ]);

        return $this->respond([
            'status' => true,
            'message' => 'Xóa tích hợp thành công',
        ]);
    }

    public function edit($id = null)
    {
        if (! has_permission('NotificationViber', 'can_edit') || ! $id) {
            show_404();
        }

        $integration = model(NotificationViberModel::class)
            ->where('company_id', $this->company_details->id)
            ->find($id);

        if (! $integration) {
            show_404();
        }

        $data = [
            'page_title' => 'Chỉnh sửa tích hợp Viber',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'integration' => $integration,
            'action' => $this->request->getGet('action') ?: 'basic',
        ];

        switch ($data['action']) {
            case 'basic':
                $viewName = 'viber/edit/basic';
                $bankAccounts = model(BankAccountModel::class)
                    ->select(['tb_autopay_bank_account.id', 'tb_autopay_bank_account.account_number', 'tb_autopay_bank_account.account_holder_name', 'tb_autopay_bank.brand_name', 'tb_autopay_bank.icon_path'])
                    ->join('tb_autopay_bank', 'tb_autopay_bank.id = tb_autopay_bank_account.bank_id')
                    ->where('tb_autopay_bank_account.company_id', $this->company_details->id)
                    ->where('tb_autopay_bank_account.active', true)
                    ->findAll();

                $bankSubAccounts = model(BankSubAccountModel::class)
                    ->select(['tb_autopay_bank_sub_account.id', 'tb_autopay_bank_sub_account.bank_account_id', 'tb_autopay_bank_sub_account.sub_account', 'tb_autopay_bank_sub_account.sub_holder_name', 'tb_autopay_bank_sub_account.label'])
                    ->whereIn('bank_account_id', array_column($bankAccounts, 'id'))
                    ->where('active', true)
                    ->where('va_active', true)
                    ->findAll();

                $data['bankAccounts'] = $bankAccounts;
                $data['bankSubAccounts'] = $bankSubAccounts;
                break;
            case 'store':
                if (! session()->get('shop_billing')) {
                    show_404();
                }

                $storeFeature = new StoreFeature();
                $storeFeature->withCompanyContext($this->user_session['company_id']);

                $data['stores'] = $storeFeature->getLinkedViberStores($integration->channel_id, $integration->transaction_type);
                $data['linkable_stores'] = $storeFeature->getLinkableViberStores($integration->channel_id, $integration->transaction_type);

                $viewName = 'viber/edit/store';
                break;
            case 'advanced':
                if (session()->get('shop_billing')) {
                    show_404();
                }

                $viewName = 'viber/edit/advanced';
                break;
            case 'template':
                $viewName = 'viber/edit/template';
                break;
            default:
                return redirect()->to('notificationviber/edit/' . $id);
        }

        return view($viewName, $data);
    }

    public function ajax_update_basic($id = null)
    {
        if (
            ! has_permission('NotificationViber', 'can_edit')
            || ! $this->request->isAJAX()
            || $this->request->getMethod(true) !== 'POST'
            || ! $id
        ) {
            show_404();
        }

        $integration = model(NotificationViberModel::class)
            ->where('company_id', $this->company_details->id)
            ->find($id);

        if (! $integration) {
            return $this->failValidationErrors(['error' => 'Tích hợp không tồn tại']);
        }

        $data = [];

        if ($this->request->getPost('description')) {
            $data['description'] = trim($this->request->getPost('description'));
        }

        if ($this->request->getPost('active') !== null) {
            $data['active'] = $this->request->getPost('active');
        }

        if (session()->get('shop_billing')) {
            if ($this->request->getPost('transaction_type')) {
                $data['transaction_type'] = $this->request->getPost('transaction_type');
            }
        } else {
            $data['bank_account_id'] = $this->request->getPost('bank_account_id');
            $data['sub_account_id'] = $this->request->getPost('sub_account_id');
        }

        $rules = [];

        if (isset($data['description'])) {
            $rules['description'] = 'required|min_length[3]|max_length[255]';
        }

        if (isset($data['active'])) {
            $rules['active'] = 'required|in_list[0,1]';
        }

        if (session()->get('shop_billing')) {
            if (isset($data['transaction_type'])) {
                $rules['transaction_type'] = 'required|in_list[All,In_only,Out_only]';
            }
        } else {
            $rules['bank_account_id'] = ['permit_empty', 'integer'];
            $rules['sub_account_id'] = ['permit_empty', 'integer'];
        }

        $messages = [
            'description' => [
                'required' => 'Tên mô tả không được để trống',
                'min_length' => 'Tên mô tả tối thiểu 3 ký tự',
                'max_length' => 'Tên mô tả tối đa 255 ký tự',
            ],
            'active' => [
                'required' => 'Chọn trạng thái tích hợp là bắt buộc',
                'in_list' => 'Trạng thái tích hợp không hợp lệ',
            ],
        ];

        if (! $this->validateData($data, $rules, $messages)) {
            return $this->failValidationErrors($this->validator->getErrors());
        }

        $updateData = [
            'description' => $data['description'] ?? $integration->description,
            'active' => $data['active'] ?? $integration->active,
        ];

        if (session()->get('shop_billing')) {
            $updateData['transaction_type'] = $data['transaction_type'] ?? $integration->transaction_type;
        } else {
            if ($data['bank_account_id']) {
                $bankAccount = model(BankAccountModel::class)
                    ->where('company_id', $this->company_details->id)
                    ->where('active', true)
                    ->find($data['bank_account_id']);

                if (! $bankAccount) {
                    return $this->failValidationErrors(['bank_account_id' => 'Tài khoản ngân hàng không tồn tại']);
                }

                if ($data['sub_account_id']) {
                    $bankSubAccount = model(BankSubAccountModel::class)
                        ->where('bank_account_id', $data['bank_account_id'])
                        ->where('active', true)
                        ->where('va_active', true)
                        ->find($data['sub_account_id']);

                    if (! $bankSubAccount) {
                        return $this->failValidationErrors(['sub_account_id' => 'Tài khoản ngân hàng con không tồn tại']);
                    }
                }
            }

            $updateData['bank_account_id'] = $data['bank_account_id'] ?? $integration->bank_account_id;
            $updateData['sub_account_id'] = $data['sub_account_id'] ?? $integration->sub_account_id;
        }

        if (! model(NotificationViberModel::class)->update($id, $updateData)) {
            return $this->failValidationErrors(['error' => 'Lỗi khi cập nhật cấu hình cơ bản. Vui lòng thử lại']);
        }

        add_user_log([
            'data_id' => $id,
            'company_id' => $this->company_details->id,
            'data_type' => 'notification_viber_edit_basic',
            'description' => 'Chỉnh sửa cấu hình cơ bản tích hợp Viber',
            'user_id' => $this->user_details->id,
            'ip' => $this->request->getIPAddress(),
            'user_agent' => $this->request->getUserAgent()->getAgentString(),
        ]);

        return $this->respond([
            'status' => true,
            'message' => 'Cập nhật cấu hình cơ bản thành công',
        ]);
    }

    public function ajax_update_advanced($id = null)
    {
        if (
            ! has_permission('NotificationViber', 'can_edit')
            || ! $this->request->isAJAX()
            || $this->request->getMethod(true) !== 'POST'
            || ! $id
            || session()->get('shop_billing')
        ) {
            show_404();
        }

        $integration = model(NotificationViberModel::class)
            ->where('company_id', $this->company_details->id)
            ->find($id);

        if (! $integration) {
            return $this->failValidationErrors(['error' => 'Tích hợp không tồn tại']);
        }

        $data = [
            'transaction_type' => $this->request->getPost('transaction_type'),
            'webhook_type' => $this->request->getPost('webhook_type'),
            'verify_payment' => $this->request->getPost('verify_payment'),
            'amount_gte' => $this->request->getPost('amount_gte'),
            'amount_lte' => $this->request->getPost('amount_lte'),
            'contains_content' => $this->request->getPost('contains_content'),
            'ignore_phrases' => $this->request->getPost('ignore_phrases'),
        ];

        $rules = [
            'transaction_type' => ['required', 'string', 'in_list[All,In_only,Out_only]'],
            'webhook_type' => ['required', 'string', 'in_list[No,All,Success,Failed]'],
            'verify_payment' => ['required', 'string', 'in_list[Skip,Success_Or_Failed,Success,Failed]'],
            'amount_gte' => ['permit_empty', 'integer', 'greater_than[0]'],
            'amount_lte' => ['permit_empty', 'integer', 'greater_than_equal_to[' . ($data['amount_gte'] ?: '0') . ']'],
            'contains_content' => ['permit_empty', 'string'],
            'ignore_phrases' => ['permit_empty', 'string'],
        ];

        $messages = [
            'transaction_type' => [
                'required' => 'Chọn loại giao dịch là bắt buộc',
                'in_list' => 'Loại giao dịch không hợp lệ',
            ],
            'webhook_type' => [
                'required' => 'Chọn trạng thái webhook là bắt buộc',
                'in_list' => 'Trạng thái webhook không hợp lệ',
            ],
            'verify_payment' => [
                'required' => 'Chọn xác thực thanh toán là bắt buộc',
                'in_list' => 'Xác thực thanh toán không hợp lệ',
            ],
        ];

        if (! $this->validateData($data, $rules, $messages)) {
            return $this->failValidationErrors($this->validator->getErrors());
        }

        $updateData = [
            'transaction_type' => $data['transaction_type'],
            'webhook_type' => $data['webhook_type'],
            'verify_payment' => $data['verify_payment'],
            'amount_in_great_than_equal_to' => $data['amount_gte'] ?: 0,
            'amount_in_less_than_equal_to' => $data['amount_lte'] ?: 0,
            'contains_content' => $data['contains_content'],
            'ignore_phrases' => $data['ignore_phrases'],
        ];

        if (! model(NotificationViberModel::class)->update($id, $updateData)) {
            return $this->failValidationErrors(['error' => 'Lỗi khi cập nhật cấu hình nâng cao. Vui lòng thử lại']);
        }

        add_user_log([
            'data_id' => $id,
            'company_id' => $this->company_details->id,
            'data_type' => 'notification_viber_edit_advanced',
            'description' => 'Chỉnh sửa cấu hình nâng cao tích hợp Viber',
            'user_id' => $this->user_details->id,
            'ip' => $this->request->getIPAddress(),
            'user_agent' => $this->request->getUserAgent()->getAgentString(),
        ]);

        return $this->respond([
            'status' => true,
            'message' => 'Cập nhật cấu hình nâng cao thành công',
        ]);
    }

    public function ajax_update_template($id = null)
    {
        if (
            ! has_permission('NotificationViber', 'can_edit')
            || ! $this->request->isAJAX()
            || $this->request->getMethod(true) !== 'POST'
            || ! $id
        ) {
            show_404();
        }

        $integration = model(NotificationViberModel::class)
            ->where('company_id', $this->company_details->id)
            ->find($id);

        if (! $integration) {
            return $this->failValidationErrors(['error' => 'Tích hợp không tồn tại']);
        }

        $data = [
            'sender_id' => $this->request->getPost('sender_id'),
            'is_template_custom' => $this->request->getPost('is_template_custom'),
            'template_custom' => trim($this->request->getPost('template_custom')),
            'template_name' => $this->request->getPost('template_name'),
            'hide_accumulated' => $this->request->getPost('hide_accumulated') == 1 ? 1 : 0,
            'hide_details_link' => $this->request->getPost('hide_details_link') == 1 ? 1 : 0,
        ];

        $rules = [
            'sender_id' => 'required',
            'is_template_custom' => 'required|in_list[0,1]',
            'template_custom' => 'required_with[is_template_custom]',
            'template_name' => 'required|in_list[template_1,template_2,template_3]',
            'hide_accumulated' => 'required|in_list[0,1]',
            'hide_details_link' => 'required|in_list[0,1]',
        ];

        $messages = [
            'sender_id' => [
                'required' => 'Chọn người gửi tin nhắn là bắt buộc',
            ],
            'is_template_custom' => [
                'required' => 'Chọn mẫu tin nhắn là bắt buộc',
                'in_list' => 'Chọn mẫu tin nhắn không hợp lệ',
            ],
            'template_custom' => [
                'required_with' => 'Nội dung tin nhắn tùy chỉnh là bắt buộc',
            ],
            'template_name' => [
                'required' => 'Chọn mẫu tin nhắn là bắt buộc',
                'in_list' => 'Chọn mẫu tin nhắn không hợp lệ',
            ],
            'hide_accumulated' => [
                'required' => 'Chọn ẩn số dư tài khoản là bắt buộc',
                'in_list' => 'Chọn ẩn số dư tài khoản không hợp lệ',
            ],
            'hide_details_link' => [
                'required' => 'Chọn ẩn liên kết chi tiết là bắt buộc',
                'in_list' => 'Chọn ẩn liên kết chi tiết không hợp lệ',
            ],
        ];

        if (! $this->validateData($data, $rules, $messages)) {
            return $this->failValidationErrors($this->validator->getErrors());
        }

        $authTokenResult = $this->testViberAuthToken($integration->auth_token);
        if ($authTokenResult['success'] && isset($authTokenResult['data']['members'])) {
            $validSenderIds = array_column($authTokenResult['data']['members'], 'id');
            if (! in_array($data['sender_id'], $validSenderIds)) {
                return $this->failValidationErrors(['sender_id' => 'Người gửi được chọn không hợp lệ']);
            }
        } else {
            return $this->failValidationErrors(['sender_id' => 'Không thể xác thực người gửi. Vui lòng thử lại']);
        }

        if (! empty($data['template_custom'])) {
            $data['template_custom'] = allowed_tags($data['template_custom']);
        }

        $updateData = [
            'sender_id' => $data['sender_id'],
            'hide_accumulated' => $data['hide_accumulated'] ? 1 : 0,
            'hide_details_link' => $data['hide_details_link'] ? 1 : 0,
            'is_template_custom' => $data['is_template_custom'] ? 1 : 0,
            'template_custom' => $data['template_custom'],
            'template_name' => $data['template_name'],
        ];

        if (session()->get('shop_billing')) {
            $storeFeature = new StoreFeature();
            $storeFeature->withCompanyContext($this->user_session['company_id']);
            $updated = $storeFeature->editStoreViber($integration->channel_id, $integration->transaction_type, $updateData);
        } else {
            $updated = model(NotificationViberModel::class)->update($id, $updateData);
        }

        if (! $updated) {
            return $this->failValidationErrors(['error' => 'Lỗi khi cập nhật mẫu thông báo. Vui lòng thử lại']);
        }

        add_user_log([
            'data_id' => $id,
            'company_id' => $this->company_details->id,
            'data_type' => 'notification_viber_edit_template',
            'description' => 'Chỉnh sửa mẫu thông báo tích hợp Viber',
            'user_id' => $this->user_details->id,
            'ip' => $this->request->getIPAddress(),
            'user_agent' => $this->request->getUserAgent()->getAgentString(),
        ]);

        return $this->respond([
            'status' => true,
            'message' => 'Cập nhật mẫu thông báo thành công',
        ]);
    }

    public function ajax_link_store(string $storeId = '')
    {
        if (
            $this->request->getMethod(true) != 'POST'
            || ! session()->get('shop_billing')
        ) {
            return $this->failNotFound();
        }

        if (! has_permission('NotificationViber', 'can_edit')) {
            return $this->failNotFound();
        }

        $storeId = trim($storeId);

        if (!$storeId || !is_numeric($storeId)) {
            return $this->failNotFound();
        }

        $viberIds = $this->request->getVar('viber_ids');

        if (!$viberIds || !is_array($viberIds) || empty($viberIds)) {
            return $this->fail('Vui lòng chọn ít nhất một kênh Viber', 400);
        }

        $storeFeature = new StoreFeature();
        $storeFeature->withCompanyContext($this->user_session['company_id']);

        $linkedCount = 0;
        $errors = [];

        foreach ($viberIds as $viberId) {
            $viber = $storeFeature->viberBuilder()->where('id', $viberId)->first();

            if (! $viber) {
                $errors[] = "Không tìm thấy Viber ID: {$viberId}";
                continue;
            }

            try {
                $storeFeature->linkStoresToViber(
                    [$storeId],
                    (array) $viber
                );
                $linkedCount++;
            } catch (\Exception $e) {
                $errors[] = "Lỗi khi liên kết Viber ID {$viberId}: " . $e->getMessage();
            }
        }

        if ($linkedCount === 0) {
            return $this->fail('Không thể liên kết bất kỳ kênh Viber nào: ' . implode(', ', $errors), 400);
        }

        return $this->respond([
            'status' => true,
            'message' => "Đã liên kết {$linkedCount} kênh Viber với cửa hàng",
            'errors' => $errors,
        ]);
    }

    public function ajax_unlink_store(string $viberId = '')
    {
        if (
            $this->request->getMethod(true) != 'POST'
            || ! session()->get('shop_billing')
        ) {
            return $this->failNotFound();
        }

        if (! has_permission('NotificationViber', 'can_edit')) {
            return $this->failNotFound();
        }

        $viberId = trim($viberId);

        if (!is_string($viberId)) {
            return $this->failNotFound();
        }

        $storeId = $this->request->getVar('store_id');

        if (!$storeId || !is_numeric($storeId)) {
            return $this->failNotFound();
        }

        $storeFeature = new StoreFeature;
        $storeFeature->withCompanyContext($this->user_session['company_id']);

        $viber = $storeFeature->viberBuilder()->where('id', $viberId)->first();

        if (!$viber) {
            return $this->failNotFound();
        }

        $storeFeature->unlinkStoresToViber([$storeId], $viber->channel_id, $viber->transaction_type);

        return $this->respond([
            'status' => true,
            'message' => 'Đã gỡ tích hợp Viber khỏi cửa hàng',
        ]);
    }

    public function ajax_sync_store(string $viberId = '')
    {
        if (
            $this->request->getMethod(true) != 'POST'
            || ! session()->get('shop_billing')
        ) {
            return $this->failNotFound();
        }

        if (! has_permission('NotificationViber', 'can_edit')) {
            return $this->failNotFound();
        }

        $viberId = trim($viberId);

        if (!is_string($viberId)) {
            return $this->failNotFound();
        }

        $storeFeature = new StoreFeature;
        $storeFeature->withCompanyContext($this->user_session['company_id']);

        $viber = $storeFeature->viberBuilder()->where('id', $viberId)->first();

        if (! $viber) {
            return $this->failNotFound();
        }

        $linkedStores = $storeFeature->getLinkedViberStores($viber->channel_id, $viber->transaction_type);

        if (!count($linkedStores)) {
            return $this->failNotFound();
        }

        $storeIds = $this->request->getVar('store_ids');

        if (!is_array($storeIds) || empty($storeIds)) {
            return $this->respond(['status' => false, 'message' => 'Vui lòng giữ lại ít nhất một cửa hàng']);
        }

        $linkableStoreIds = array_values(array_diff($storeIds, array_column($linkedStores, 'id')));
        $unlinkableStoreIds = array_values(array_diff(array_column($linkedStores, 'id'), $storeIds));

        $storeFeature = new StoreFeature;
        $storeFeature->withCompanyContext($this->user_session['company_id']);

        if (count($linkableStoreIds)) {
            $storeFeature->linkStoresToViber($linkableStoreIds, (array) $viber);
        }

        if (count($unlinkableStoreIds)) {
            $storeFeature->unlinkStoresToViber($unlinkableStoreIds, $viber->channel_id, $viber->transaction_type);
        }

        if ($storeFeature->viberBuilder()->where('id', $viberId)->countAllResults() === 0) {
            $viber = $storeFeature->viberBuilder()
                ->where('channel_id', $viber->channel_id)
                ->where('transaction_type', $viber->transaction_type)
                ->first();

            if (! $viber) {
                return $this->respond([
                    'status' => false,
                    'message' => 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ'
                ]);
            }

            return $this->respond([
                'status' => true,
                'message' => 'Cập nhật thành thành công',
            ]);
        }

        return $this->respond([
            'status' => true,
            'message' => 'Cập nhật thành thành công',
        ]);
    }

    protected function testViberAuthToken(string $authToken): array
    {
        try {
            $client = new ViberChannelClient();
            $response = $client->getAccountInfo($authToken);

            if ($response->getStatusCode() === 200) {
                $data = json_decode($response->getBody(), true);

                if (isset($data['status']) && $data['status'] === 0) {
                    return [
                        'success' => true,
                        'message' => 'Kết nối thành công với kênh Viber!',
                        'data' => [
                            'name' => $data['name'] ?? 'Unknown',
                            'id' => $data['id'] ?? '',
                            'icon' => $data['icon'] ?? '',
                            'members' => $data['members'] ?? [],
                        ],
                    ];
                }

                return [
                    'success' => false,
                    'message' => 'Auth Token không hợp lệ hoặc kênh không tồn tại',
                ];
            }

            return [
                'success' => false,
                'message' => 'Không thể kết nối tới Viber. Vui lòng thử lại',
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Lỗi kết nối: ' . $e->getMessage(),
            ];
        }
    }

    protected function setupViberWebhook(string $authToken): array
    {
        try {
            $client = new ViberChannelClient();
            $response = $client->setupWebhook($authToken, 'https://quocdat.webdemo.fun/test.php');
            $data = json_decode($response->getBody(), true);

            if ($response->getStatusCode() === 200 && $data['status'] === 0) {
                return [
                    'success' => true,
                    'message' => 'Webhook đã được thiết lập thành công!',
                ];
            }

            return [
                'success' => false,
                'message' => 'Không thể thiết lập webhook',
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Không thể thiết lập webhook: ' . $e->getMessage(),
            ];
        }
    }

    protected function sendTestMessage(string $authToken, string $message): array
    {
        try {
            $client = new ViberChannelClient();
            $response = $client->sendTextMessage($authToken, 'SePay Bot', $message);

            if ($response->getStatusCode() === 200) {
                $data = json_decode($response->getBody(), true);

                if (isset($data['status']) && $data['status'] === 0) {
                    return [
                        'success' => true,
                        'message' => 'Tin nhắn test đã được gửi thành công!',
                    ];
                }
            }

            return [
                'success' => false,
                'message' => 'Không thể gửi tin nhắn test',
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Lỗi gửi tin nhắn: ' . $e->getMessage(),
            ];
        }
    }
}
