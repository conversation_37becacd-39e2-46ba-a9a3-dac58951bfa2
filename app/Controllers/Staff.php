<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Features\Store\StoreFeature;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\Response;
use CodeIgniter\HTTP\ResponseInterface;
use Config\Mobile;

class Staff extends BaseController
{
    use ResponseTrait;
    
    public function index()
    {
        if (! in_array($this->company_details->role,['Admin','SuperAdmin'])) {
            show_404();
        }
        
        $data = [
            'page_title' => 'Nhân viên',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];
        
        $storeFeature = new StoreFeature();
        $storeFeature->withCompanyContext($this->user_session['company_id']);
        
        $data['staff_total'] = $storeFeature->staffBuilder()->countAllResults();
        
        echo theme_view('templates/autopay/header', $data);
        echo theme_view('staff/index', $data);
        echo theme_view('templates/autopay/footer', $data);
    }
    
    public function add(string $staffId = '')
    {
        $data = [
            'page_title' => 'Thêm nhân viên',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'default_store_id' => $this->request->getGet('store_id')
        ];
        
        if (! in_array($this->company_details->role,['Admin','SuperAdmin'])) {
            show_404();
        }
        
        $step = $this->request->getGet('step') ?? 1;
        $fallbackUrl = base_url('staff/add' . ($data['default_store_id'] ? '?store_id=' . $data['default_store_id'] : ''));
        
        if (!is_numeric($step) || $step < 1 || $step > 3) {
            return redirect()->to($fallbackUrl);
        }
        
        if ($step > 1 && !$staffId) {
            return redirect()->to($fallbackUrl);
        }
        
        $data['step'] = (int) $step;
        
        $storeFeature = new StoreFeature;
        $storeFeature->withCompanyContext($this->user_session['company_id']);
        
        if ($step > 1 && $staffId) {
            $data['staff'] = $storeFeature->staffBuilder()->where(['tb_autopay_user.id' => $staffId])->first();
            
            if (! $data['staff']) {
                show_404();
            }
        } else {
            $data['staff'] = null;
        }
        
        echo theme_view('templates/autopay/header', $data);
        
        if ($step == 2) {
            $data['stores'] = $storeFeature->getLinkableStaffStores($staffId);

            if ($data['default_store_id'] ) {
                $stores = $data['stores'];
                $defaultStoreKey = array_search($data['default_store_id'] , array_column($stores, 'id'));

                if ($defaultStoreKey !== false) {
                    $defaultStore = $stores[$defaultStoreKey];
                    unset($stores[$defaultStoreKey]);
                    array_unshift($stores, $defaultStore);
                    $data['stores'] = $stores;
                }
            }

            echo theme_view('staff/add/step2', $data);
        } else if ($step == 3) {
            $data['stores'] = $storeFeature->getLinkedStaffStores((int) $staffId);
            $data['mobile_config'] = config(Mobile::class);
            
            $session = service('session');
            $data['staff_password'] = $session->get('temp_staff_password');
            $session->remove('temp_staff_password');
            echo theme_view('staff/add/step3', $data);
        } else {
            echo theme_view('staff/add/step1', $data);
        }
        
        echo theme_view('templates/autopay/footer', $data);
    }
    
    public function ajax_index(): Response
    {
        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])) {
            return $this->failNotFound();
        }
        
        $storeId = $this->request->getGet('store_id');
        $type = $this->request->getGet('type');

        $storeFeature = new StoreFeature();
        $storeFeature->withCompanyContext($this->user_session['company_id']);
        $staffs = $storeFeature->getStoreStaffs($storeId ? [$storeId] : [], $type);

        return $this->respond([
            'data' => $staffs
        ]);
    }
    
    public function ajax_add(): Response
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }

        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])) {
            return $this->failNotFound();
        }
        
        $data = [
            'fullname' => trim($this->request->getPost('fullname')),
            'email' => trim($this->request->getPost('email')),
            'password' => $this->request->getPost('password'),
        ];

        $rules = [
            'fullname' => [
                'rules' => 'required|max_length[50]',
            ],
            'email' => [
                'rules' => 'required|valid_email|is_unique[tb_autopay_user.email]',
                'errors' => [
                    'is_unique' => 'Địa chỉ e-mail đã tồn tại trong hệ thống'
                ]
            ],
            'password' => [
                'rules' => 'required|min_length[8]',
            ],
        ];

        if (!$this->validate($rules)) {
            return $this->fail($this->validator->getErrors());
        }

        try {
            $storeFeature = new StoreFeature;
            $storeFeature->withCompanyContext($this->user_session['company_id']);
            $userId = $storeFeature->createStaff($data);
            
            $session = service('session');
            $session->set('temp_staff_password', $data['password']);
            
            add_user_log(['data_id' => $userId, 'company_id' => $this->user_session['company_id'], 'data_type' => 'staff_add', 'description' => 'Thêm nhân viên', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);

            return $this->respond([
                'status' => true,
                'message' => 'Thêm nhân viên thành công',
                'id' => $userId
            ]);
        } catch (\Exception $e) {
            log_message('error', $e->getMessage());
            
            return $this->respond([
                'status' => false,
                'message' => 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ'
            ]);
        }
    }
    
    public function ajax_link_stores_to_staff(string $staffId = ''): Response
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }

        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])) {
            return $this->failNotFound();
        }
        
        if (!is_numeric($staffId)) {
            return $this->failNotFound();
        }
        
        $storeFeature = new StoreFeature;
        $storeFeature->withCompanyContext($this->user_session['company_id']);
        
        $staff = $storeFeature->staffBuilder()->where(['tb_autopay_user.id' => $staffId])->first();
        
        if (! $staff) {
            return $this->failNotFound();
        }

        $storeIds = $this->request->getVar('store_ids');

        if (!is_array($storeIds) || empty($storeIds)) {
            return $this->respond(['status' => false, 'message' => 'Vui lòng chọn ít nhất một cửa hàng']);
        }
        
        $linkableStores = $storeFeature->getLinkableStaffStores($staffId);
        
        if (count(array_diff($storeIds, array_column($linkableStores, 'id'))) > 0) {
            return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng tải lại trang']);
        }

        try {
            $storeFeature->linkStoresToStaff($storeIds, $staffId);

            return $this->respond([
                'status' => true,
            ]);
        } catch (\Exception $e) {
            log_message('error', $e->getMessage());
            
            return $this->respond([
                'status' => false,
                'message' => 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ'
            ]);
        }
    }
    
    public function edit(string $staffId = '')
    {
        if (! in_array($this->company_details->role, ['Admin','SuperAdmin'])) {
            show_404();
        }
        
        $data = [
            'page_title' => 'Nhân viên',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];
        
        $staffId = trim($staffId);
        
        if (!is_string($staffId)) {
            return $this->failNotFound();
        }
        
        $tab = $this->request->getGet('tab') ?? '';
        
        if ($tab && !in_array($tab, ['store', 'advanced'])) {
            return redirect()->to(base_url('staff/edit/' . $staffId));
        }
        
        $data['tab'] = $tab;
        
        $storeFeature = new StoreFeature;
        $storeFeature->withCompanyContext($this->user_session['company_id']);
        
        $data['staff'] = $storeFeature->staffBuilder()
            ->where('tb_autopay_user.id', $staffId)
            ->first();
            
        if (!$data['staff']) {
            show_404();
        }
        
        $data['stores'] = $storeFeature->getLinkedStaffStores($staffId);
        
        echo theme_view('templates/autopay/header', $data);
        
        if ($data['tab'] === 'general') {
            echo theme_view('staff/edit/general', $data);
        } else if ($data['tab'] === 'store') {
            $data['linkable_stores'] = $storeFeature->getLinkableStaffStores($staffId);
            echo theme_view('staff/edit/store', $data);
        } else {
            echo theme_view('staff/edit/general', $data);
        }
        
        echo theme_view('templates/autopay/footer', $data);
    }
    
    public function ajax_edit(string $staffId = '')
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }

        $staffId = trim($staffId);
        
        if (!is_string($staffId)) {
            return $this->failNotFound();
        }
        
        if (! in_array($this->company_details->role, ['Admin','SuperAdmin'])) {
            return $this->failNotFound();
        }
        
        $storeFeature = new StoreFeature;
        $storeFeature->withCompanyContext($this->user_session['company_id']);
        
        $staff = $storeFeature->staffBuilder()->where('tb_autopay_user.id', $staffId)->first();
            
        if (!$staff) {
            return $this->failNotFound();
        }
        
        $data = [
            'fullname' => trim($this->request->getPost('fullname')),
            'password' => $this->request->getPost('password'),
            'active' => $this->request->getVar('active')
        ];

        $rules = [
            'fullname' => [
                'rules' => 'permit_empty|max_length[50]',
            ],
            'password' => [
                'rules' => 'permit_empty|min_length[8]',
            ],
        ];
        
        if (! $this->validateData($data, $rules)) {
            return $this->fail($this->validator->getErrors());
        }
       
        $updated = $storeFeature->editStoreStaff($staff->id, $data);
        
        if ($updated) {
            add_user_log(['data_id' => $staff->id, 'company_id' => $this->user_session['company_id'], 'data_type' => 'staff_edit', 'description' => 'Cập nhật nhân viên', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);
        }
        
        return $this->respond(['status' => true, 'message' => 'Cập nhật thành công']);
    }
    
    public function ajax_sync_store_to_staff(string $staffId = '')
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }
        
        if (! in_array($this->company_details->role, ['Admin','SuperAdmin'])) {
            return $this->failNotFound();
        }
        
        $staffId = trim($staffId);
        
        if (!is_string($staffId)) {
            return $this->failNotFound();
        }
        
        $storeFeature = new StoreFeature;
        $storeFeature->withCompanyContext($this->user_session['company_id']);
        
        $staff = $storeFeature->staffBuilder()->where('tb_autopay_user.id', $staffId)->first();
            
        if (!$staff) {
            return $this->failNotFound();
        }
        
        $linkedStores = $storeFeature->getLinkedStaffStores($staff->id);
        
        $storeIds = $this->request->getVar('store_ids');

        if (!is_array($storeIds) || empty($storeIds)) {
            return $this->respond(['status' => false, 'message' => 'Vui lòng chỉ định ít nhất một cửa hàng']);
        }
        
        $linkableStoreIds = array_values(array_diff($storeIds, array_column($linkedStores, 'id')));
        $unlinkableStoreIds = array_values(array_diff(array_column($linkedStores, 'id'), $storeIds));
        
        $storeFeature = new StoreFeature;
        $storeFeature->withCompanyContext($this->user_session['company_id']);
       
        if (count($linkableStoreIds)) {
            $storeFeature->linkStoresToStaff($linkableStoreIds, (int) $staff->id);
        }
        
        if (count($unlinkableStoreIds)) {
            $storeFeature->unlinkStoresToStaff($unlinkableStoreIds, (int) $staff->id);
        }
        
        return $this->respond([
            'status' => true,
            'message' => 'Cập nhật thành thành công',
        ]);
    }
    
    public function ajax_delete(string $staffId = '')
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }
        
        if (! in_array($this->company_details->role, ['Admin','SuperAdmin'])) {
            return $this->failNotFound();
        }
        
        $staffId = trim($staffId);
        
        if (!is_string($staffId)) {
            return $this->failNotFound();
        }
        
        $storeFeature = new StoreFeature;
        $storeFeature->withCompanyContext($this->user_session['company_id']);
        
        $staff = $storeFeature->staffBuilder()->where('tb_autopay_user.id', $staffId)->first();
            
        if (!$staff) {
            return $this->failNotFound();
        }
        
        $storeFeature->deleteStoreStaff($staff->id);
        
        add_user_log(['data_id' => $staff->id, 'company_id' => $this->user_session['company_id'], 'data_type' => 'staff_delete', 'description' => 'Xóa nhân viên', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);
        
        return $this->respond([
            'status' => true,
            'message' => 'Đã xóa tài khoản nhân viên',
        ]);
    }

    public function ajax_unlink_store_to_staff(string $staffId = '')
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }
        
        if (! in_array($this->company_details->role, ['Admin', 'SuperAdmin'])) {
            return $this->failNotFound();
        }
        
        $staffId = trim($staffId);
        
        if (!is_string($staffId)) {
            return $this->failNotFound();
        }
        
        $storeId = $this->request->getVar('store_id');
        
        if (!$storeId || !is_numeric($storeId)) {
            return $this->failNotFound();
        }
        
        $storeFeature = new StoreFeature;
        $storeFeature->withCompanyContext($this->user_session['company_id']);
        
        $staff = $storeFeature->staffBuilder()->where('tb_autopay_user.id', $staffId)->first();
            
        if (!$staff) {
            return $this->failNotFound();
        }
        
        $storeFeature->unlinkStoresToStaff([$storeId], (int) $staffId);
        
        return $this->respond([
            'status' => true,
            'message' => 'Đã gỡ quyền nhân viên khỏi cửa hàng',
        ]);
    }

    public function ajax_link_store_to_staffs(string $storeId = '')
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }
        
        if (! in_array($this->company_details->role, ['Admin', 'SuperAdmin'])) {
            return $this->failNotFound();
        }
        
        $storeId = trim($storeId);
        
        if (!$storeId || !is_numeric($storeId)) {
            return $this->failNotFound();
        }
        
        $staffIds = $this->request->getVar('staff_ids');
        
        if (!$staffIds || !is_array($staffIds) || empty($staffIds)) {
            return $this->fail('Vui lòng chọn ít nhất một nhân viên', 400);
        }
        
        $storeFeature = new StoreFeature;
        $storeFeature->withCompanyContext($this->user_session['company_id']);
        
        $linkedCount = 0;
        $errors = [];
        
        foreach ($staffIds as $staffId) {
            $staff = $storeFeature->staffBuilder()->where('tb_autopay_user.id', $staffId)->first();
                
            try {
                $storeFeature->linkStoresToStaff(
                    [$storeId], 
                    (int) $staffId
                );
                $linkedCount++;
            } catch (\Exception $e) {
            }
        }
        
        if ($linkedCount === 0) {
            return $this->fail('Không thể phân quyền bất kỳ nhân viên nào');
        }
        
        return $this->respond([
            'status' => true,
            'message' => "Đã phân quyền {$linkedCount} nhân viên với cửa hàng",
            'errors' => $errors
        ]);
    }
}
