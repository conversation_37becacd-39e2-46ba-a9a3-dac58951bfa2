<?php

namespace App\Controllers;
use App\Models\FormModel;

use CodeIgniter\Controller;

class Form extends BaseController
{
      

    public function ajax_msb_add() {

        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!has_permission('BankAccount', 'can_add'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn chưa có quyền thực hiện thao tác này."));

 
        $validation =  \Config\Services::validation();

        helper('text');

        $rules = [
            'company_name' =>['label' => 'Tên tổ chức/ Cá nhân muốn mở API tại MSB', 'rules' => 'required'],
            'phonenumber' => ['label' => 'Số điện thoại', 'rules' => "required|max_length[20]"],
            'is_msb_account' => ['label' => 'Bạn đã có tài khoản MSB chưa', 'rules' => "required|in_list[0,1]"],
        ];
        
        if(! $this->validate($rules)) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        } 
         
        $data = array(
            'company_id' => $this->user_details->id,
            'user_id' => $this->company_details->id,
            'title' => "Yêu cầu mở API MSB. Company ID #" . $this->company_details->id,
            'body' => "- Tên tổ chức/ Cá nhân muốn mở API tại MSB: " . $this->request->getVar('company_name') . "
- SĐT liên hệ: ". $this->request->getVar('phonenumber') . "
- Có tài khoản tại MSB: " . $is_msb_account . "
- Ghi chú từ khách hàng: " .  $this->request->getVar('notes'),
        );

        $formModel = model(FormModel::class);
        
        $result = $formModel->insert($data);
        
        if($result) {
            add_user_log(array('data_id'=>$result,'company_id' => $this->user_session['company_id'], 'data_type'=>'form_add','description'=>'Yêu cầu tích hợp MSB API','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
            return $this->response->setJSON(array("status"=>true));
        } else {
            add_user_log(array('data_id'=>0,'company_id' => $this->user_session['company_id'], 'data_type'=>'form_add','description'=>'Yêu cầu tích hợp MSB API','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Failed'));
            return $this->response->setJSON(array("status"=>false,"message"=>"Không thể gửi yêu cầu. Vui lòng liên hệ SePay để được hỗ trợ."));
        }
    
    }

 
}