<?php

namespace App\Controllers;
use App\Models\CompanyApiModel;

use CodeIgniter\Controller;

class Companyapi extends BaseController
{
    public function index()
    { 
        $data = [
            'page_title' => 'API Access',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        if(!in_array($this->company_details->role,['Admin','SuperAdmin']))
            show_404();

        $userModel = model(UserModel::class);
      
        echo theme_view('templates/autopay/header',$data);
        echo view('companyapi/index',$data);
        echo theme_view('templates/autopay/footer',$data);

    }

    public function ajax_api_list() {
        if(!in_array($this->company_details->role,['Admin','SuperAdmin']))
            show_404();
     
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        $companyApiModel = model(CompanyApiModel::class);

        $apis = $companyApiModel->getDatatables($this->user_session['company_id']);
         
        $data = array();

        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');

        foreach ($apis as $api) {

            $no++;
            $row = array();

            $actions_btn = "<a href='javascript:;' onclick='edit_api(" . $api->id. ")' class='btn btn-sm btn-outline-warning ms-2 me-1 mt-2'><i class='bi bi-pencil'></i> Sửa</a><a href='javascript:;' onclick='delete_api(" . $api->id. ")' class='btn btn-sm btn-outline-danger ms-2 mt-2'><i class='bi bi-trash3'></i> Xóa</a>";
          
            $row[] = $no;
            $row[] = esc($api->id);
            $row[] = esc($api->name);
            $row[] = esc($api->api_key);
            if($api->active == 1)
                $row[] = "<span class='badge bg-success'>Hoạt động</span>";
            else
                $row[] = "<span class='badge bg-danger'>Tạm khóa</span>";           
            
            $row[] = $actions_btn;
       
            $data[] = $row;
        }
 
        $output = array(
            "draw" => $draw,
            "recordsTotal" => $companyApiModel->countAll($this->user_session['company_id']),
            "recordsFiltered" => $companyApiModel->countFiltered($this->user_session['company_id']),
            "data" => $data,
        );
        return $this->response->setJSON($output);
    
    
    }

    function ajax_api_add() {
        if(!in_array($this->company_details->role,['Admin','SuperAdmin']))
            show_404();

        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'name' => ['label' => 'Tên', 'rules' =>  "required|max_length[200]"],
            'active' => "required|in_list[0,1]",
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }

        $api_key = strtoupper(random_string('alnum', 64));

        $companyApiModel = model(companyApiModel::class);

        $api_data = [
            'name' => trim(xss_clean($this->request->getVar('name'))),
            'company_id' => $this->user_session['company_id'],
            'api_key' => $api_key,
            'active' => $this->request->getVar('active'),
        ];

        $api_id = $companyApiModel->insert($api_data);

        if(!is_numeric($api_id))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Không thể tạo mới API, vui lòng liên hệ SePay để được hỗ trợ"));

        add_user_log(array('data_id'=>$api_id,'company_id' => $this->user_session['company_id'], 'data_type'=>'admin_create_api','description'=>'Thêm API','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));

        return $this->response->setJSON(array("status"=>true));
        
       
    }

    public function ajax_api_delete() {
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        if(!in_array($this->company_details->role,['Admin','SuperAdmin']))
            show_404();

        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'id' => 'required|integer|is_natural',
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }
        $api_id = $this->request->getPost('id');

        $companyApiModel = model(CompanyApiModel::class);
 
        $company_api = $companyApiModel->where(['company_id' => $this->user_session['company_id'], 'id' => $api_id])->get()->getRow();

        if(!is_object($company_api))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Không tìm thấy API này"));

        // delete api
        $companyApiModel->where(['company_id' => $this->user_session['company_id'], 'id' => $api_id])->delete();

        add_user_log(array('data_id'=>$api_id,'company_id' => $this->user_session['company_id'], 'data_type'=>'admin_delete_api','description'=>'Xoá API','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));


        return $this->response->setJSON(array("status"=>true));
    }

    public function ajax_get_api($api_id='') {
        
        if(!is_numeric($api_id)) 
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"ID API không hợp lệ"));

        if(!in_array($this->company_details->role,['Admin','SuperAdmin']))
            show_404();
        
        $companyApiModel = model(CompanyApiModel::class);
       
        $result = $companyApiModel->select("tb_autopay_company_api.id, tb_autopay_company_api.name,tb_autopay_company_api.api_key,tb_autopay_company_api.active")->where(['tb_autopay_company_api.id'=>$api_id,"tb_autopay_company_api.company_id" => $this->user_session['company_id']])->get()->getRow();
        
        if($result)
            return $this->response->setJSON(["status"=>TRUE, "data"=>$result]);
        else
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy API này"));
    }

    public function ajax_api_update() {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!in_array($this->company_details->role,['Admin','SuperAdmin']))
            show_404();

       
        $validation =  \Config\Services::validation();

        helper('text');

      
        $validate_data = [
            'id' => "required|integer|is_natural",
            'name' => ['label' => 'Tên', 'rules' =>  "required|max_length[200]"],
            'active' => "required|in_list[0,1]"
        ];

        if(! $this->validate($validate_data))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));        

        $api_id = $this->request->getVar('id');

        $api_data = [
            'name' => trim(xss_clean($this->request->getVar('name'))),
            'active' => $this->request->getVar('active'),
        ];

        $companyApiModel = model(CompanyApiModel::class);

        $result = $companyApiModel->set($api_data)->where(["id" => $api_id, "company_id" => $this->user_session['company_id']])->update();

        
        if($result) { 
            add_user_log(array('data_id'=>$api_id,'company_id' => $this->user_session['company_id'], 'data_type'=>'admin_update_api','description'=>'Sửa API','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));

            return $this->response->setJSON(array("status"=>true));
        } else {
            return $this->response->setJSON(array("status"=>false,"message"=>"Không thể cập nhật API!"));
        }
    }


 
}