<?php

namespace App\Models;

use CodeIgniter\Model;

class EmailQueueModel extends Model
{
    protected $table = 'tb_autopay_email_queue';

    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType     = 'object';
    protected $allowedFields = ['data_type','data_id','mail_to', 'title','body','status', 'retry', 'sent_at','is_read','user_id'];

    protected $createdField  = 'created_at';

    protected $useSoftDeletes = false;

    protected $useTimestamps     = false;

    protected $validationRules    = [];

    protected $skipValidation     = false;
    
    protected $order = ['id'=>'DESC'];
 
    protected $builder;

    private function _getDatatablesQuery($user_id) {

        $request = \Config\Services::request();
  
        $column_order = array(null,'id','title','created_at');
        $column_search = array('title','body');
    
        $this->builder = $this->db->table($this->table);

        $this->builder->select("id, title, body, created_at,is_read");
 
        $this->builder->where(['user_id' => $user_id]);

        $i = 0;
     
        foreach ($column_search as $item) {
            if($request->getVar('search')['value'] != NULL) {
                $search_term = trim($request->getVar('search')['value']);
                 
                if($i===0) {
                    $this->builder->groupStart();
                    $this->builder->like($item, $search_term);
                } else {
                    $this->builder->orLike($item, $search_term);
                }
 
                if(count($column_search) - 1 == $i)
                    $this->builder->groupEnd();
            }
            $i++;
        }

        // Per column search
        foreach ($column_search as $key => $item) { 
            
            if($item !=NULL && isset($request->getPost('columns')[$key]['search']['value']) && $request->getPost('columns')[$key]['search']['value'] != "") {
                $this->builder->where($item, $request->getPost('columns')[$key]['search']['value']);
            }
        }
      
        if($request->getVar('order') != NULL && isset($column_order[$request->getVar('order')[0]['column']])  && $column_order[$request->getVar('order')[0]['column']] != NULL) {
            
            $this->builder->orderBy($column_order[$request->getVar('order')[0]['column']], $request->getVar('order')['0']['dir']);

        } else if(isset($this->order)) {

            $order = $this->order;
            $this->builder->orderBy(key($order), $order[key($order)]);

        }
    }

    public function getDatatables($user_id=FALSE) {
        $request = \Config\Services::request();
        $this->_getDatatablesQuery($user_id);
        if($request->getVar('length') != NULL && $request->getVar('length') != -1)
            $this->builder->limit($request->getVar('length'), $request->getVar('start'));
            
        $query = $this->builder->get();

        return $query->getResult();
    }

    public function countAll($user_id) {
     
        $builder = $this->db->table($this->table);
        if(is_numeric($user_id))
            $builder->where(['user_id' => $user_id]);

        return $builder->countAllResults();
    }

    public function countFiltered($user_id) {
        $this->_getDatatablesQuery($user_id);
         
        return $this->builder->countAllResults();
    }

}