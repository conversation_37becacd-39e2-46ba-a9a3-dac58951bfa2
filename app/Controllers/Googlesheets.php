<?php
namespace App\Controllers;
use App\Controllers\BaseController;
use App\Models\GoogleAccountModel;
use App\Libraries\GoogleSheetsClient;
use App\Models\BankAccountModel;
use App\Models\BankGoogleSheetModel;
use App\Models\BankSubAccountModel;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use Psr\Log\LoggerInterface;
class Googlesheets extends BaseController
{
    use ResponseTrait;

    public function __construct()
    {
        helper('general');
        $config = config(\Config\GoogleSheets::class);    
        if (!$config->visibility && !is_admin()) {
            show_404();
        }
                         
    }

    public function index()
    {
        if(!has_permission('NotificationGoogleSheets', 'can_view_all'))
            show_404();

        $data = [
            'page_title' => 'Google Sheets',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        $bankGoogleSheetModel = model(BankGoogleSheetModel::class);

        $google_sheets = $bankGoogleSheetModel->where('company_id', $this->user_session['company_id'])->first();

        $data['google_sheets'] = $google_sheets;

        echo view('templates/autopay/header',$data);
        echo view('googlesheets/index',$data);
        echo view('templates/autopay/footer',$data);
    }
    public function ajax_google_sheets_list()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';
        if (!has_permission('NotificationGoogleSheets', 'can_view_all'))
            show_404();

        $bankGoogleSheetModel = model(BankGoogleSheetModel::class);
        $googleSheets = $bankGoogleSheetModel->getDatatables($this->user_session['company_id']);
       
        $data = [];
        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');
        $autoDeleteMapping = [
            0 => "Không xóa",
            3600 => '60 phút',
            14400 => "4 tiếng",
            28800 => "8 tiếng",
            86400 => "1 ngày"
        ];

        foreach ($googleSheets as $googlesheet) {
            $no++;
            $row = [];
            $google_active = "<span>Email: {$googlesheet->email}</span><br />" .
                ($googlesheet->active == 1 ?
                "<span class='text-success'>Đang liên kết</span>" :
                "<span class='text-danger'>Đã hủy liên kết Google với SePay.</span>
                <p>Nhấn vào <a class='text-primary google-login-btn'>đây</a> để đăng nhập lại</p>");
            $row[] = $no;
            $row[] = $google_active;
            $bank_text = '';
            if ($googlesheet->account_all) $bank_text .= "<span>{$googlesheet->account_all}</span>";
            if ($googlesheet->account_bank) {
                $account_bank = str_replace(',', '<br>', $googlesheet->account_bank);
                $bank_text .= "<p class='bank_account'>Tài khoản ngân hàng: <br/>$account_bank</p>";
            }
            if ($googlesheet->account_va) {
                $account_va = str_replace(',', '<br>', $googlesheet->account_va);
                $bank_text .= "<p class='bank_sub_account'>Tài khoản ảo (VA): <br/>$account_va</p>";
            }
            $row[] = $bank_text;
            $row[] = "<a href='https://docs.google.com/spreadsheets/d/{$googlesheet->file_id}' target='_blank'>{$googlesheet->file_name}</a>";
            $row[] = "<span class='text-success'>{$googlesheet->sheet_name}</span>";

            $autoDeleteText = $autoDeleteMapping[$googlesheet->auto_delete_after] ?? "{$googlesheet->auto_delete_after} giây";
            $transfer_type = $googlesheet->transfer_type == 'in' ? 'Tiền vào' : ($googlesheet->transfer_type == 'out' ? 'Tiền ra' : 'Tất cả');
            $hide_accumulated = $googlesheet->hide_accumulated ? 'Có' : 'Không';

            $row[] = "
                <p class='mb-1'>Xóa giao dịch sau: <b>" . esc($autoDeleteText) . "</b></p>
                <p class='mb-1'>Loại giao dịch: <b>" . esc($transfer_type) . "</b></p>
                <p class='mb-1'>Ẩn số dư giao dịch: <b>" . esc($hide_accumulated) . "</b></p>
            ";

            $action_text = "";
            if (has_permission('NotificationGoogleSheets', 'can_edit'))
                $action_text .= "<a href='" . base_url('googlesheets/edit?file_id='.$googlesheet->file_id.'&sheet_id='.$googlesheet->sheet_id) . "' class='btn btn-sm btn-outline-warning ms-2 me-2 mt-2'><i class='bi bi-pencil'></i> Sửa</a>";
            if (has_permission('NotificationGoogleSheets', 'can_delete'))
                $action_text .= "<a href='javascript:;' onclick='delete_google_sheet({$googlesheet->sheet_id}, \"{$googlesheet->file_id}\", {$googlesheet->google_account_id})' class='btn btn-sm btn-outline-danger ms-2 mt-2'><i class='bi bi-trash3'></i> Xóa</a>";
            $row[] = $action_text;

            $mobile = "
                <div>
                    <p>
                    {$google_active }
                    {$bank_text}
                    <p class='mb-0'>
                        Tên bảng tính: <a href='https://docs.google.com/spreadsheets/d/{$googlesheet->file_id}' target='_blank'>{$googlesheet->file_name}</a>
                    </p>
                    <p>Tên trang tính: <span class='text-success'>{$googlesheet->sheet_name}</span></p>
                    <p>{$action_text}</p>
                </div>";
            $row[] = $mobile;
            $data[] = $row;
        }

        return $this->response->setJSON([
            "draw" => $draw,
            "recordsTotal" => $bankGoogleSheetModel->countAll($this->user_session['company_id']),
            "recordsFiltered" => $bankGoogleSheetModel->countFiltered($this->user_session['company_id']),
            "data" => $data,
        ]);

    }

    public function step1()
    {
        if (!has_permission('NotificationGoogleSheets', 'can_add'))
            show_404();

        $data = [
            'page_title' => 'Google Sheets',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];
        $googleAccountModel = model(GoogleAccountModel::class);
        $google_account = $googleAccountModel->select('id, email, active')->where(['company_id' => $this->user_session['company_id']])->orderBy('updated_at', 'desc')->get()->getResult();
        $data['google_account'] = $google_account;
        echo view('templates/autopay/header',$data);
        echo view('googlesheets/step1',$data);
        echo view('templates/autopay/footer',$data);
    }
    public function ajax_step1()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';
        if (!has_permission('NotificationGoogleSheets', 'can_add'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền thực hiện thao tác này']);
        $data = [
            'google_account_id' => trim($this->request->getPost('google_account_id')),
        ];
        $rules = [
            'google_account_id' => ['required', 'integer', 'is_natural'],
        ];
        if (! $this->validateData($data, $rules)) {
            return $this->fail($this->validator->getErrors());
        }
        $googleAccountModel = model(GoogleAccountModel::class);
        $bankAccountModel = model(BankAccountModel::class);
        $bank_account = $bankAccountModel->select('tb_autopay_bank_account.id, tb_autopay_bank_account.account_holder_name,
        tb_autopay_bank_account.account_number, tb_autopay_bank.brand_name')
            ->join('tb_autopay_bank', 'tb_autopay_bank_account.bank_id = tb_autopay_bank.id')
            ->where([
                'tb_autopay_bank_account.company_id' => $this->user_session['company_id'],
                'tb_autopay_bank_account.active' => 1
            ])
            ->get()->getResult();
        if(!$bank_account)
            return $this->response->setJSON(['status' => false, 'message' => 'Vui lòng liên kết tài khoản ngân hàng để tiếp tục']);
        $google_account = $googleAccountModel->where([
            'id' => $data['google_account_id'],
            'active' => 1,
            'company_id' => $this->user_session['company_id']
        ])->first();
        if(!$google_account){
            return $this->response->setJSON(['status' => false, 'message' => 'Vui lòng đăng nhập lại tài khoản Google trên']);
        }
        $session = service('session');
        $session->set('googlesheets', $data);
        return $this->respond(['status' => true]);
    }
    public function login()
    {
        if (!has_permission('NotificationGoogleSheets', 'can_add'))
            show_404();
        $client = new GoogleSheetsClient;
        $authUrl = "https://accounts.google.com/o/oauth2/v2/auth?" . http_build_query([
                'client_id' => $client->appId,
                'redirect_uri' => $client->redirectUri,
                'response_type' => 'code',
                'scope' => 'https://www.googleapis.com/auth/drive.file https://www.googleapis.com/auth/userinfo.email',
                'access_type' => 'offline',
                'prompt' => 'consent'
            ]);
        return redirect()->to($authUrl);
    }
    public function callback()
    {
        if (!has_permission('NotificationGoogleSheets', 'can_add'))
            show_404();
        $client = new GoogleSheetsClient();
        $code = $this->request->getGet('code');
        if (!$code) {
            return "<script>
            window.close();
              </script>";
        }
        $data = [
            'code' => $code,
            'client_id' => $client->appId,
            'client_secret' => $client->appSecret,
            'redirect_uri' => $client->redirectUri,
            'grant_type' => 'authorization_code',
        ];
        $result = $client->createToken('https://oauth2.googleapis.com/token', $data);
        if (!isset($result['response']) || !$result['response']->getBody()) {
            return "<script>
            window.close();
              </script>";
        }
        $token = json_decode($result['response']->getBody(), true);
        if (!isset($token['access_token']) || !isset($token['scope']) || strpos($token['scope'], "https://www.googleapis.com/auth/drive.file") === false) {
            return "<script>
                window.close();
            </script>";
        }        
        $result = $client->getUserInfo('https://www.googleapis.com/oauth2/v1/userinfo', $token['access_token']);
        if (!isset($result['response']) || !$result['response']->getBody()) {
            return "<script>
            window.close();
              </script>";
        }
        $userInfo = json_decode($result['response']->getBody(), true);
        if (!isset($userInfo['email'])) {
            return "<script>
            window.close();
              </script>";
        }
        $email = $userInfo['email'];
        $googleAccountModel = model(GoogleAccountModel::class);
        $existingUser = $googleAccountModel->where(
            [
                'email' => $email,
                'company_id' => $this->user_session['company_id']
            ]
        )->get()->getResult();
        $baseData = [
            'access_token' => $token['access_token'],
            'refresh_token' => $token['refresh_token'],
            'issued_at' => time() + (7 * 3600),
            'expires_in' => $token['expires_in'],
            'email' => $email,
            'active' => 1,
        ];
        if (!empty($existingUser)) {
            foreach ($existingUser as $e) {
                $data = array_merge($baseData, ['company_id' => $e->company_id]);
                $googleAccountModel->update($e->id, $data);
            }
        } else {
            $data = array_merge($baseData, ['company_id' => $this->user_session['company_id']]);
            $googleAccountModel->insert($data);
        }
        return "<script>
            window.opener.location.reload();
            window.close();
              </script>";
    }
    public function step2()
    {
        if (!has_permission('NotificationGoogleSheets', 'can_add')) show_404();
        $data = [
            'page_title' => 'Google Sheets',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];
        $session = service('session');
        $notificationGoogleSheets = $session->get('googlesheets');
        if (!$notificationGoogleSheets
        || !array_key_exists('google_account_id', $notificationGoogleSheets)){
            return redirect()->to(base_url().'/googlesheets/step1');
        }
        $googleAccountModel = model(GoogleAccountModel::class);
        $google_account = $googleAccountModel->select('id')->where(
            ['company_id' => $this->user_session['company_id'] , 'id' => $notificationGoogleSheets['google_account_id']])->first();

        if(!$google_account){
            return redirect()->to('googlesheets/step1');
        }

        $config = config(\Config\GoogleSheets::class);
        $data['google_account'] = $google_account;
        $data['google_appKey'] = $config->appKey;
        $data['google_projectNumber'] = $config->projectNumber;
        
        echo view('templates/autopay/header',$data);
        echo view('googlesheets/step2',$data);
        echo view('templates/autopay/footer',$data);
    }
    
    public function ajax_create_sheet_in_folder()
    {
        if ($this->request->getMethod(true) !== 'POST' || !has_permission('NotificationGoogleSheets', 'can_add')) {
            return $this->respond(['status' => false, 'message' => 'Bạn không có quyền thực hiện thao tác này']);
        }
        $data = $this->request->getPost(['folder_id', 'file_name', 'google_account_id']);
        $rules = [
            'folder_id' => 'required|string',
            'file_name' => 'required|string',
            'google_account_id' => 'required|integer|is_natural'
        ];
        if (!$this->validate($rules)) {
            return $this->fail($this->validator->getErrors());
        }

        $googleAccountModel = model(GoogleAccountModel::class);
        $google_account = $googleAccountModel->where([
            'company_id' => $this->user_session['company_id'],
            'id' => $data['google_account_id']
        ])->first();
        if (!$google_account) {
            return $this->respond(['status' => false, 'message' => 'Tài khoản này không tồn tại']);
        }

        $client = new GoogleSheetsClient;
        $result = $client->createSheet($data['google_account_id'], $data['folder_id'], $data['file_name']);
        if (!$this->isValidResponse($result)) {
            $this->respond(["status" => FALSE, "message" => "Không thể tạo bảng tính mới!"]);
        }

        $file = json_decode($result['response']->getBody(), true);
        return $this->respond([
            'status' => true,
            'message' => 'Tạo bảng tính thành công',
            'file' => $file
        ]);
    }
    
    public function ajax_step2()
    {
        if ($this->request->getMethod(true) !== 'POST' || !has_permission('NotificationGoogleSheets', 'can_add')) {
            return $this->respond(['status' => false, 'message' => 'Bạn không có quyền thực hiện thao tác này']);
        }
        $data = $this->request->getPost(['file_id', 'file_name', 'position', 'sheet_id', 'auto_delete_after']);
        $rules = [
            'file_id' => 'required',
            'file_name' => 'required',
            'position' => 'required|in_list[start,end]',
            'sheet_id' => 'required|is_natural',
            'auto_delete_after' => 'required|is_natural'
        ];
        if (!$this->validate($rules)) {
            return $this->fail($this->validator->getErrors());
        }
        $bankGoogleSheetModel = model(BankGoogleSheetModel::class);
        $session = service('session');
        $notificationGoogleSheets = $session->get('googlesheets');
        $result = $bankGoogleSheetModel->where([
            'google_account_id' => $notificationGoogleSheets['google_account_id'],
            'company_id' => $this->user_session['company_id'],
            'file_id' => $data['file_id'],
            'sheet_id' => $data['sheet_id']
        ])->countAllResults();
        if($result)
            return $this->respond(['status' => false, 'message' => 'Tích hợp trên đã tồn tại. Vui lòng chọn tích hợp khác']);

        $notificationGoogleSheets = array_merge($notificationGoogleSheets, [
            'file_id' => $data['file_id'],
            'file_name' => $data['file_name'],
            'position' => $data['position'],
            'sheet_id' => $data['sheet_id'],
            'auto_delete_after' => $data['auto_delete_after']
        ]);
        $session->set('googlesheets', $notificationGoogleSheets);
        return $this->respond(['status' => true]);
    }

    public function check_token()
    {
        if (!has_permission('NotificationGoogleSheets', 'can_add')) {
            return $this->respond(['status' => false, 'message' => 'Bạn không có quyền thực hiện thao tác này']);
        }

        if($this->request->getMethod(true) !== 'POST'){
            return $this->respond(['status' => false, 'message' => 'Phương thức không hợp lệ']);
        }

        $session = service('session');
        $notificationGoogleSheets = $session->get('googlesheets');
        if (!$notificationGoogleSheets
        || !array_key_exists('google_account_id', $notificationGoogleSheets)){
            return $this->respond(['status' => false, 'message' => 'Dữ liệu không hợp lệ']);
        }

        $googleAccountModel = model(GoogleAccountModel::class);
        $google_account = $googleAccountModel->select('id ,access_token')->where(
            ['company_id' => $this->user_session['company_id'] , 'id' => $notificationGoogleSheets['google_account_id']])->first();
        if (!$google_account) {
            return $this->respond(['status' => false, 'message' => 'Tài khoản này không tồn tại']);
        }

        $client = new GoogleSheetsClient;
        $token = $client->handleToken($google_account->id);
        if (!is_string($token) || empty($token)) {
            return $this->respond(['status' => false, 'message' => 'Token đã hết hạn hoặc không hợp lệ. Vui lòng đăng nhập lại']);
        }
        return $this->respond([
            'status' => true,
            'message' => 'success',
            'data' => $token
        ]);
    }
    public function ajax_list_sheet()
    {
        if ($this->request->getMethod(true) !== 'POST' || !has_permission('NotificationGoogleSheets', 'can_add')) {
            return $this->respond(['status' => false, 'message' => 'Bạn không có quyền thực hiện thao tác này']);
        }
        $data = $this->request->getPost(['file_id', 'google_account_id']);
        $rules = [
            'file_id' => 'required',
            'google_account_id' => 'required|integer|is_natural'
        ];
        if (!$this->validate($rules)) {
            return $this->fail($this->validator->getErrors());
        }
        $googleAccountModel = model(GoogleAccountModel::class);
        $google_account = $googleAccountModel->where([
            'company_id' => $this->user_session['company_id'],
            'id' => $data['google_account_id']
        ])->first();
        if (!$google_account) {
            return $this->respond(['status' => false, 'message' => 'Tài khoản này không tồn tại']);
        }
        $file_id = $data['file_id'];
        $client = new GoogleSheetsClient;
        $result_list_sheet = $client->getSheet(
            $data['google_account_id'],
            "https://sheets.googleapis.com/v4/spreadsheets/{$file_id}"
        );
        if (!$this->isValidResponse($result_list_sheet)) {
            return $this->respond(["status" => FALSE, "message" => "Không thể lấy danh sách bảng tính. Vui lòng kiểm tra lại!"]);
        }
        $sheets = json_decode($result_list_sheet['response']->getBody(), true);
        if (!isset($sheets['sheets'])) {
            return $this->response->setJSON(["status" => FALSE, "message" => "Không tìm thấy bảng tính nào!"]);
        }
        $sheets = $sheets['sheets'];
        usort($sheets, function($a, $b) {
            return strcmp($b['properties']['index'], $a['properties']['index']);
        });
        return $this->response->setJSON(["status" => TRUE, "data" => $sheets]);
    }
    public function step3()
    {
        if (!has_permission('NotificationGoogleSheets', 'can_add'))
            show_404();
        $data = [
            'page_title' => 'Google Sheets',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];
        $session = service('session');
        $notificationGoogleSheets = $session->get('googlesheets');
        if (!$notificationGoogleSheets
        || !array_key_exists('google_account_id', $notificationGoogleSheets)
        || !array_key_exists('file_id', $notificationGoogleSheets)
        || !array_key_exists('file_name', $notificationGoogleSheets)
        || !array_key_exists('position', $notificationGoogleSheets)
        || !array_key_exists('sheet_id', $notificationGoogleSheets)
        || !array_key_exists('auto_delete_after', $notificationGoogleSheets)
        ){
            return redirect()->to(base_url().'/googlesheets/step2');
        }
        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $bank_accounts = $bankAccountModel->select('
        tb_autopay_bank_account.id, tb_autopay_bank_account.account_holder_name,
        tb_autopay_bank_account.account_number, tb_autopay_bank.brand_name,
        tb_autopay_bank.icon_path
        ')
        ->join('tb_autopay_bank', 'tb_autopay_bank_account.bank_id = tb_autopay_bank.id')
        ->where([
            'tb_autopay_bank_account.company_id' => $this->user_session['company_id'],
            'tb_autopay_bank_account.active' => 1,
            'tb_autopay_bank.active' => 1
        ])
        ->findAll();
        if (!$bank_accounts) {
            show_404();
        }
        foreach ($bank_accounts as &$bank) {
            $bank->sub_accounts = $bankSubAccountModel->select('id, sub_account, sub_holder_name, label')
                ->where(['bank_account_id' => $bank->id, 'active' => 1])
                ->findAll();
        }
        $data['banks'] = $bank_accounts;
        $notificationGoogleSheets['banks'] = $bank_accounts;
        echo view('templates/autopay/header',$data);
        echo view('googlesheets/step3',$data);
        echo view('templates/autopay/footer',$data);
    }
    public function ajax_step3()
    {
        if ($this->request->getMethod(true) !== 'POST' || !has_permission('NotificationGoogleSheets', 'can_add')) {
            return $this->respond(['status' => false, 'message' => 'Bạn không có quyền thực hiện thao tác này']);
        }
        $data = $this->request->getVar(['transfer_type', 'is_custom_bank', 'hide_accumulated', 'banks']);
        $rules = [
            'transfer_type' => 'required|in_list[in,out,all]',
            'is_custom_bank' => 'required|in_list[0, 1]',
            'hide_accumulated' => 'permit_empty',
        ];
        if (!$this->validate($rules)) {
            return $this->fail($this->validator->getErrors());
        }
        $session = service('session');
        $notificationGoogleSheets = $session->get('googlesheets') ?? [];
        if ($data['is_custom_bank'] == 0) {
            $data['banks'] = null;
        }
        if (!is_null($data['banks'])) {
            if (!is_object($data['banks']) || empty($data['banks'])) {
                return $this->respond(['status' => false, 'message' => 'Dữ liệu tài khoản ngân hàng không hợp lệ']);
            }
            foreach ($data['banks'] as $bank_id => $sub_accounts) {
                if (!is_numeric($bank_id) || (!is_array($sub_accounts) && !is_null($sub_accounts))) {
                    return $this->respond(['status' => false ,'message' => 'Dữ liệu tài khoản ngân hàng không hợp lệ']);
                }
            }
        }
        $hide_accumulated = ($data['hide_accumulated'] === "on") ? 1 : 0;
        $notificationGoogleSheets = array_merge($notificationGoogleSheets, [
            'transfer_type' => $data['transfer_type'],
            'is_custom_bank' => $data['is_custom_bank'],
            'hide_accumulated' => $hide_accumulated,
            'banks' => $data['banks'],
        ]);
        $session->set('googlesheets', $notificationGoogleSheets);
        return $this->respond(['status' => true]);
    }
    public function step4()
    {
        if (!has_permission('NotificationGoogleSheets', 'can_add')) show_404();

        $data = [
            'page_title' => 'Google Sheets',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];
        $session = service('session');
        $notificationGoogleSheets = $session->get('googlesheets');

        if (!$notificationGoogleSheets
        || !array_key_exists('google_account_id', $notificationGoogleSheets)
        || !array_key_exists('is_custom_bank', $notificationGoogleSheets)
        || !array_key_exists('transfer_type', $notificationGoogleSheets)
        || !array_key_exists('hide_accumulated', $notificationGoogleSheets)
        || !array_key_exists('banks', $notificationGoogleSheets)
        || !array_key_exists('file_id', $notificationGoogleSheets)
        || !array_key_exists('file_name', $notificationGoogleSheets)
        || !array_key_exists('position', $notificationGoogleSheets)
        || !array_key_exists('sheet_id', $notificationGoogleSheets)
        || !array_key_exists('auto_delete_after', $notificationGoogleSheets)
        ){
            return redirect()->to(base_url().'/googlesheets/step1');
        }

        $data['googlesheets'] = $notificationGoogleSheets;

        echo view('templates/autopay/header',$data);
        echo view('googlesheets/step4',$data);
        echo view('templates/autopay/footer',$data);
    }
    public function ajax_step4()
    {
        if ($this->request->getMethod(true) !== 'POST' || !has_permission('NotificationGoogleSheets', 'can_add')) {
            return $this->respond(['status' => false, 'message' => 'Bạn không có quyền thực hiện thao tác này']);
        }

        $session = service('session');
        $notificationGoogleSheets = $session->get('googlesheets');
        if (!$notificationGoogleSheets || array_diff(['google_account_id', 'is_custom_bank', 'transfer_type', 'hide_accumulated', 'banks', 'file_id', 'position', 'sheet_id', 'auto_delete_after'], array_keys($notificationGoogleSheets))) {
            return $this->response->setJSON(["status" => false, 'message' => 'Thiếu thông tin yêu cầu']);
        }

        if(!in_array($notificationGoogleSheets['position'], ['start', 'end'])){
            return $this->response->setJSON(["status" => false, 'message' => 'Vị trí trang tính không hợp lệ']);
        }

        if(!in_array($notificationGoogleSheets['transfer_type'], ['in', 'out', 'all'])){
            return $this->response->setJSON(["status" => false, 'message' => 'Loại giao dịch không hợp lệ']);
        }
        
        if(!in_array($notificationGoogleSheets['hide_accumulated'], [0, 1])){
            return $this->response->setJSON(["status" => false, 'message' => 'Ẩn lũy kế không hợp lệ']);
        }

        if(!in_array($notificationGoogleSheets['auto_delete_after'], [0, 3600, 14400, 28800, 86400])){
            return $this->response->setJSON(["status" => false, 'message' => 'Thời gian xóa không hợp lệ']);
        }

        $bankGoogleSheetModel = model(BankGoogleSheetModel::class);
        $banks = $notificationGoogleSheets['banks'];
        $google_account_id = $notificationGoogleSheets['google_account_id'];

        $client = new GoogleSheetsClient;
        $fileId = $notificationGoogleSheets['file_id'];
        $googleAccountId = $notificationGoogleSheets['google_account_id'];
        $sheetId = $notificationGoogleSheets['sheet_id'];

        $result = $bankGoogleSheetModel->where([
            'google_account_id' => $googleAccountId,
            'company_id' => $this->user_session['company_id'],
            'file_id' => $fileId,
            'sheet_id' => $sheetId
        ])->countAllResults();

        if($result)
            return $this->respond(['status' => false, 'message' => 'Tích hợp trên đã tồn tại. Vui lòng chọn tích hợp khác']);

        $sheetNameResponse = $client->getSheetNameById($googleAccountId, "https://sheets.googleapis.com/v4/spreadsheets/{$fileId}");

        if (!$this->isValidResponse($sheetNameResponse)) {
            return $this->respond(["status" => FALSE, "message" => "Không tìm thấy trang tính này. Vui lòng kiểm tra lại!"]);
        }
        $sheets = json_decode($sheetNameResponse['response']->getBody(), true)['sheets'] ?? [];
        $sheetTitle = $this->getSheetTitle($sheets, $sheetId);
        if ($sheetTitle === null) {
            return $this->respond(["status" => FALSE, "message" => "Không tìm thấy bảng tính tương ứng. Vui lòng kiểm tra lại!"]);
        }

        $sheetTitleEncoded = urlencode($sheetTitle);
        $sheetDataResponse = $client->getSheetData($googleAccountId, "https://sheets.googleapis.com/v4/spreadsheets/{$fileId}/values/{$sheetTitleEncoded}!A1:J1");
        if (!$this->isValidResponse($sheetDataResponse)) {
            return $this->respond(["status" => FALSE, "message" => "Không thể lấy thông tin từ bảng tính!"]);
        }

        $expectedHeaders = [
            'Ngân hàng',
            'Ngày giao dịch',
            'Số tài khoản',
            'Tài khoản phụ',
            'Code TT',
            'Nội dung thanh toán',
            'Loại',
            'Số tiền',
            'Mã tham chiếu',
            'Lũy kế',
        ];

        $sheetHeaders = json_decode($sheetDataResponse['response']->getBody(), true)['values'][0] ?? [];
        if (!empty(array_diff($expectedHeaders, $sheetHeaders))) {
            $requests = [
                [
                    'insertDimension' => [
                        'range' => [
                            'sheetId' => $sheetId,
                            'dimension' => 'ROWS',
                            'startIndex' => 0,
                            'endIndex' => 1
                        ],
                        'inheritFromBefore' => false
                    ]
                ],
                [
                    'repeatCell' => [
                        'range' => [
                            'sheetId' => $sheetId,
                            'startRowIndex' => 0,
                            'endRowIndex' => 1,
                            'startColumnIndex' => 0,
                            'endColumnIndex' => 10,
                        ],
                        'cell' => [
                            'userEnteredFormat' => [
                                'horizontalAlignment' => 'CENTER',
                                'textFormat' => [
                                    'bold' => true
                                ]
                            ]
                        ],
                        'fields' => 'userEnteredFormat(horizontalAlignment, textFormat)'
                    ]
                ]
            ];
        
            $batchUpdateResponse = $client->batchUpdateToSheet($googleAccountId, "https://sheets.googleapis.com/v4/spreadsheets/{$fileId}:batchUpdate", $requests);
            if (!$this->isValidResponse($batchUpdateResponse)) {
                return $this->respond(["status" => FALSE, "message" => "Không thể thêm dữ liệu vào bảng tính!"]);
            }
        
            $updateResponse = $client->updateSheet(
                $googleAccountId,
                $fileId,
                $sheetTitle,
                'A1:J1',
                $expectedHeaders
            );
        
            if (!$this->isValidResponse($updateResponse)) {
                return $this->respond(["status" => FALSE, "message" => "Không thể thêm dữ liệu vào bảng tính!"]);
            }
        
            $sheetUpdate = json_decode($updateResponse['response']->getBody(), true);
            if ($sheetUpdate['spreadsheetId'] !== $fileId) {
                return $this->respond(["status" => FALSE, "message" => "Không thể thêm dữ liệu vào bảng tính!"]);
            }
        }        
        $sheetNameResponse = $client->getSheetNameById($google_account_id, "https://sheets.googleapis.com/v4/spreadsheets/{$notificationGoogleSheets['file_id']}");
        if (!$this->isValidResponse($sheetNameResponse)) {
            return $this->respond(["status" => FALSE, "message" => "Không thể lấy tên bảng tính!"]);
        }
        $sheets = json_decode($sheetNameResponse['response']->getBody(), true)['sheets'] ?? [];
        $sheetTitle = $this->getSheetTitle($sheets, $notificationGoogleSheets['sheet_id']);
        if ($sheetTitle === null) {
            return $this->respond(["status" => FALSE, "message" => "Không tìm thấy bảng tính tương ứng!"]);
        }
        $sheet_name = $sheetTitle;
        if (!is_null($banks)) {
            if (!is_object($banks) || empty((array) $banks)) {
                return $this->respond(['status' => false, 'message' => 'Dữ liệu tài khoản ngân hàng không hợp lệ']);
            }
            foreach ($banks as $bank_id => $sub_accounts) {
                if (!is_numeric($bank_id) || (!is_array($sub_accounts) && !is_null($sub_accounts))) {
                    return $this->respond(['status' => false, 'message' => 'Dữ liệu tài khoản ngân hàng không hợp lệ']);
                }
                $insertData = [
                    'google_account_id' => $google_account_id,
                    'company_id' => $this->user_session['company_id'],
                    'transfer_type' => $notificationGoogleSheets['transfer_type'],
                    'bank_account_id' => $bank_id,
                    'file_id' => $notificationGoogleSheets['file_id'],
                    'file_name' => $notificationGoogleSheets['file_name'],
                    'position' => $notificationGoogleSheets['position'],
                    'hide_accumulated' => $notificationGoogleSheets['hide_accumulated'],
                    'sheet_id' => $notificationGoogleSheets['sheet_id'],
                    'sheet_name' => $sheet_name,
                    'auto_delete_after' => $notificationGoogleSheets['auto_delete_after']
                ];
                if (is_array($sub_accounts) && !empty($sub_accounts)) {
                    foreach ($sub_accounts as $sub_account) {
                        $insertData['bank_sub_account_id'] = $sub_account;
                        $result = $bankGoogleSheetModel->insert($insertData);
                    }
                } else {
                    $insertData['bank_sub_account_id'] = null;
                    $result = $bankGoogleSheetModel->insert($insertData);
                }
            }
            if($result){
                add_user_log([
                    'data_id' => $result,
                    'company_id' => $this->user_session['company_id'],
                    'data_type' => 'notification_google_sheet_add',
                    'description' => 'Thêm tích hợp thông báo Google sheets thành công',
                    'user_id' => $this->user_details->id,
                    'ip' => $this->request->getIPAddress(),
                    'user_agent' => $this->request->getUserAgent()->getAgentString(),
                    'status' => 'Success'
                ]);
            }else{
                add_user_log([
                    'data_id' => $result,
                    'company_id' => $this->user_session['company_id'],
                    'data_type' => 'notification_google_sheet_add',
                    'description' => 'Thêm tích hợp thông báo Google sheets thất bại',
                    'user_id' => $this->user_details->id,
                    'ip' => $this->request->getIPAddress(),
                    'user_agent' => $this->request->getUserAgent()->getAgentString(),
                    'status' => 'Failed'
                ]);
            }
        } else {
            $insertData = [
                'google_account_id' => $google_account_id,
                'company_id' => $this->user_session['company_id'],
                'transfer_type' => $notificationGoogleSheets['transfer_type'],
                'bank_account_id' => null,
                'bank_sub_account_id' => null,
                'file_id' => $notificationGoogleSheets['file_id'],
                'file_name' => $notificationGoogleSheets['file_name'],
                'position' => $notificationGoogleSheets['position'],
                'hide_accumulated' => $notificationGoogleSheets['hide_accumulated'],
                'sheet_id' => $notificationGoogleSheets['sheet_id'],
                'sheet_name' => $sheet_name,
                'auto_delete_after' => $notificationGoogleSheets['auto_delete_after']
            ];
            $result = $bankGoogleSheetModel->insert($insertData);
            if($result){
                add_user_log([
                    'data_id' => $result,
                    'company_id' => $this->user_session['company_id'],
                    'data_type' => 'notification_google_sheet_add',
                    'description' => 'Thêm tích hợp thông báo google sheets thành công',
                    'user_id' => $this->user_details->id,
                    'ip' => $this->request->getIPAddress(),
                    'user_agent' => $this->request->getUserAgent()->getAgentString(),
                    'status' => 'Success'
                ]);
            }else{
                add_user_log([
                    'data_id' => $result,
                    'company_id' => $this->user_session['company_id'],
                    'data_type' => 'notification_google_sheet_add',
                    'description' => 'Thêm tích hợp thông báo google sheets thất bại',
                    'user_id' => $this->user_details->id,
                    'ip' => $this->request->getIPAddress(),
                    'user_agent' => $this->request->getUserAgent()->getAgentString(),
                    'status' => 'Failed'
                ]);
            }
        }
        $session->remove('googlesheets');
        return $this->respond(['status' => true, 'message' => 'Lưu tích hợp thành công']);
    }

    public function ajax_delete()
    {
        if ($this->request->getMethod(true) !== 'POST' || !has_permission('NotificationGoogleSheets', 'can_delete')) {
            return $this->respond(['status' => false, 'message' => 'Bạn không có quyền thực hiện thao tác này']);
        }
        $data = [
            'sheet_id' => trim($this->request->getPost('sheet_id')),
            'file_id' => trim($this->request->getPost('file_id')),
            'google_account_id' => trim($this->request->getPost('google_account_id')),
        ];
        $rules = [
            'sheet_id' => 'required|integer|is_natural',
            'file_id' => 'required',
            'google_account_id' => 'required|integer|is_natural',
        ];
        if (! $this->validateData($data, $rules)) {
            return $this->fail($this->validator->getErrors());
        }
        $bankGoogleSheetModel = model(BankGoogleSheetModel::class);
        $bankGoogleSheetDetails = $bankGoogleSheetModel->where([
            'company_id' => $this->user_session['company_id'],
            'sheet_id' => $data['sheet_id'],
            'file_id' => $data['file_id'],
            'google_account_id' => $data['google_account_id']
        ])->get()->getResult();
        if(empty($bankGoogleSheetDetails))
            return $this->response->setJSON(['status' => false, 'message' => 'Không tìm thấy tích hợp này']);
        foreach($bankGoogleSheetDetails as $b)
        {
            $result = $bankGoogleSheetModel->delete($b->id);
        }
        if ($result) {
            add_user_log(array('data_id' => $b->id, 'company_id' => $this->user_session['company_id'], 'data_type' => 'notification_google_sheet_delete', 'description' => 'Xóa tích hợp thông báo Goolge Sheets', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success'));
        } else {
            add_user_log(array('data_id' => $b->id, 'company_id' => $this->user_session['company_id'], 'data_type' => 'notification_google_sheet_delete', 'description' => 'Xóa tích hợp thông báo Goolge Sheets thất bại', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Failed'));
        }
        return $this->respond(['status' => true, 'message' => "Xóa tích hợp thành công"]);
    }

    public function edit()
    {
        if (!has_permission('NotificationGoogleSheets', 'can_edit')) {
            show_404();
        }

        $data = [
            'page_title' => 'Google Sheets',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];
        
        $value = [
            'sheet_id' => $this->request->getGet('sheet_id'),
            'file_id' => $this->request->getGet('file_id'),
        ];

        if (is_array($value['sheet_id']) || is_array($value['file_id'])) {
            show_404();
        }

        $value = [
            'sheet_id' => trim($this->xss_clean_for_sheet($value['sheet_id'])),
            'file_id' => trim($this->xss_clean_for_sheet($value['file_id'])),
        ];

        $rules = [
            'sheet_id' => 'required|string',
            'file_id' => 'required|string',
        ];

        if (! $this->validateData($value, $rules)) {
            return $this->fail($this->validator->getErrors());
        }
        
        if (!is_string($value['file_id']) || !is_string($value['sheet_id'])) {
            show_404();
        }

        $file_id = $value['file_id'];
        $sheet_id = $value['sheet_id'];

        $bankGoogleSheetModel = model(BankGoogleSheetModel::class);
        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);

        $bankGoogleSheetDetails = $bankGoogleSheetModel
            ->select(['tb_autopay_google_account.email', 'tb_autopay_bank_google_sheet.*'])
            ->join('tb_autopay_google_account', 'tb_autopay_google_account.id = tb_autopay_bank_google_sheet.google_account_id')
            ->where([
                'tb_autopay_bank_google_sheet.file_id' => $file_id,
                'tb_autopay_bank_google_sheet.sheet_id' => $sheet_id,
                'tb_autopay_bank_google_sheet.company_id' => $this->user_session['company_id'],
                'tb_autopay_google_account.company_id' => $this->user_session['company_id']
            ])
            ->first();

        if (!is_object($bankGoogleSheetDetails)) {
            show_404();
        }

        $data['bank_google_sheet_details'] = $bankGoogleSheetDetails;
        $bank_accounts = $bankAccountModel->select('
            tb_autopay_bank_account.id, tb_autopay_bank_account.account_holder_name,
            tb_autopay_bank_account.account_number, tb_autopay_bank.brand_name,
            tb_autopay_bank.icon_path
        ')
        ->join('tb_autopay_bank', 'tb_autopay_bank_account.bank_id = tb_autopay_bank.id')
        ->where([
            'tb_autopay_bank_account.company_id' => $this->user_session['company_id'],
            'tb_autopay_bank_account.active' => 1,
            'tb_autopay_bank.active' => 1
        ])
        ->findAll();

        if (!$bank_accounts) {
            show_404();
        }

        foreach ($bank_accounts as &$bank) {
            $bank->sub_accounts = $bankSubAccountModel->select('id, sub_account, sub_holder_name, label')
                ->where(['bank_account_id' => $bank->id, 'active' => 1])
                ->findAll();
        }

        $data['banks'] = $bank_accounts;
        $bank_accounts_old = $bankGoogleSheetModel
            ->select(['bank_account_id', 'bank_sub_account_id'])
            ->where([
                'google_account_id' => $bankGoogleSheetDetails->google_account_id,
                'company_id' => $this->user_session['company_id'],
                'file_id' => $bankGoogleSheetDetails->file_id,
                'sheet_id' => $bankGoogleSheetDetails->sheet_id
            ])->findAll();

        $selected_banks_with_sub = [];
        foreach ($bank_accounts_old as $old_bank) {
            $bank_id = $old_bank->bank_account_id;
            $sub_account_id = $old_bank->bank_sub_account_id;
            if (!isset($selected_banks_with_sub[$bank_id])) {
                $selected_banks_with_sub[$bank_id] = [];
            }
            $selected_banks_with_sub[$bank_id][] = $sub_account_id;
        }
        $data['selected_banks_with_sub'] = $selected_banks_with_sub;

        echo view('templates/autopay/header',$data);
        echo view('googlesheets/edit',$data);
        echo view('templates/autopay/footer',$data);
    }

    public function ajax_edit_step3()
    {
        if ($this->request->getMethod(true) !== 'POST' || !has_permission('NotificationGoogleSheets', 'can_edit')) {
            return $this->respond(['status' => false, 'message' => 'Bạn không có quyền thực hiện thao tác này']);
        }

        $data = $this->request->getVar(['is_custom_bank', 'transfer_type', 'banks', 'file_id', 'sheet_id', 'google_account_id', 'hide_accumulated']);

        $rules = [
            'is_custom_bank' => 'required|in_list[0,1]',
            'banks' => 'permit_empty',
            'transfer_type' => 'required|in_list[in,out,all]',
            'google_account_id' => 'required|string',
            'hide_accumulated' => 'permit_empty',
            'sheet_id' => 'required|string',
            'file_id' => 'required|string',
        ];

        if (!$this->validate($rules)) {
            return $this->fail($this->validator->getErrors());
        }

        $file_id = $data['file_id'];
        $sheet_id = $data['sheet_id'];
        $google_account_id = $data['google_account_id'];
        $hide_accumulated = ($data['hide_accumulated'] === "on") ? 1 : 0;
        $banks = $data['banks'];
        $bankGoogleSheetModel = model(BankGoogleSheetModel::class);
        $bankAccountModel = model(BankAccountModel::class);

        $bankGoogleSheetDetails = $bankGoogleSheetModel->where(
            [
            'file_id' => $file_id,
            'sheet_id' => $sheet_id,
            'google_account_id' => $google_account_id,
            'company_id' => $this->user_session['company_id']
            ]
        )->first();

        if(!is_object($bankGoogleSheetDetails))
            return $this->response->setJSON(['status' => false, 'message' => 'Không tìm thấy tích hợp này']);

        if (!is_null($banks)) {
            if (!is_object($banks) || empty((array) $banks)) {
                return $this->respond(['status' => false, 'message' => 'Dữ liệu tài khoản ngân hàng không hợp lệ']);
            }
            $bankGoogleSheetModel->where([
                    'company_id' => $this->user_session['company_id'],
                    'sheet_id' => $bankGoogleSheetDetails->sheet_id,
                    'file_id' => $bankGoogleSheetDetails->file_id,
                    'google_account_id' => $google_account_id
            ])->delete();

            foreach ($banks as $bank_id => $sub_accounts) {
                if (!is_numeric($bank_id) || (!is_array($sub_accounts) && !is_null($sub_accounts))) {
                    return $this->respond(['status' => false, 'message' => 'Dữ liệu tài khoản ngân hàng không hợp lệ']);
                }

                $bankExists = $bankAccountModel->where([
                    'id' => $bank_id,
                    'company_id' => $this->user_session['company_id'],
                    'active' => true
                ])->countAllResults();
    
                if (!$bankExists) {
                    return $this->respond(['status' => false, 'message' => 'Dữ liệu tài khoản ngân hàng không hợp lệ']);
                }

                $insertData = [
                    'google_account_id' => $google_account_id,
                    'company_id' => $this->user_session['company_id'],
                    'transfer_type' => $data['transfer_type'],
                    'bank_account_id' => $bank_id,
                    'file_id' => $bankGoogleSheetDetails->file_id,
                    'file_name' => $bankGoogleSheetDetails->file_name,
                    'position' => $bankGoogleSheetDetails->position,
                    'hide_accumulated' => $hide_accumulated,
                    'sheet_id' => $bankGoogleSheetDetails->sheet_id,
                    'sheet_name' => $bankGoogleSheetDetails->sheet_name,
                    'auto_delete_after' => $bankGoogleSheetDetails->auto_delete_after
                ];
                if (is_array($sub_accounts) && !empty($sub_accounts)) {
                    foreach ($sub_accounts as $sub_account) {
                        $insertData['bank_sub_account_id'] = $sub_account;
                        $result = $bankGoogleSheetModel->insert($insertData);
                    }
                } else {
                    $insertData['bank_sub_account_id'] = null;
                    $result = $bankGoogleSheetModel->insert($insertData);
                }
            }
            if($result){
                add_user_log([
                    'data_id' => $result,
                    'company_id' => $this->user_session['company_id'],
                    'data_type' => 'notification_google_sheet_add',
                    'description' => 'Cập nhật tích hợp Google Sheets thành công',
                    'user_id' => $this->user_details->id,
                    'ip' => $this->request->getIPAddress(),
                    'user_agent' => $this->request->getUserAgent()->getAgentString(),
                    'status' => 'Success'
                ]);
            }else{
                add_user_log([
                    'data_id' => $result,
                    'company_id' => $this->user_session['company_id'],
                    'data_type' => 'notification_google_sheet_add',
                    'description' => 'Cập nhật tích hợp Google Sheets thất bại',
                    'user_id' => $this->user_details->id,
                    'ip' => $this->request->getIPAddress(),
                    'user_agent' => $this->request->getUserAgent()->getAgentString(),
                    'status' => 'Failed'
                ]);
            }
        } else {
            $bankGoogleSheetModel->where([
                'company_id' => $this->user_session['company_id'],
                'sheet_id' => $bankGoogleSheetDetails->sheet_id,
                'file_id' => $bankGoogleSheetDetails->file_id,
                'google_account_id' => $google_account_id
            ])->delete();
            
            $insertData = [
                'google_account_id' => $google_account_id,
                'company_id' => $this->user_session['company_id'],
                'transfer_type' => $data['transfer_type'],
                'bank_account_id' => null,
                'bank_sub_account_id' => null,
                'file_id' => $bankGoogleSheetDetails->file_id,
                'file_name' => $bankGoogleSheetDetails->file_name,
                'position' => $bankGoogleSheetDetails->position,
                'hide_accumulated' => $hide_accumulated,
                'sheet_id' => $bankGoogleSheetDetails->sheet_id,
                'sheet_name' => $bankGoogleSheetDetails->sheet_name,
                'auto_delete_after' => $bankGoogleSheetDetails->auto_delete_after
            ];
            $result = $bankGoogleSheetModel->insert($insertData);
            if($result){
                add_user_log([
                    'data_id' => $result,
                    'company_id' => $this->user_session['company_id'],
                    'data_type' => 'notification_google_sheet_add',
                    'description' => 'Cập nhật tích hợp Google Sheets thành công',
                    'user_id' => $this->user_details->id,
                    'ip' => $this->request->getIPAddress(),
                    'user_agent' => $this->request->getUserAgent()->getAgentString(),
                    'status' => 'Success'
                ]);
            }else{
                add_user_log([
                    'data_id' => $result,
                    'company_id' => $this->user_session['company_id'],
                    'data_type' => 'notification_google_sheet_add',
                    'description' => 'Cập nhật tích hợp Google Sheets thất bại',
                    'user_id' => $this->user_details->id,
                    'ip' => $this->request->getIPAddress(),
                    'user_agent' => $this->request->getUserAgent()->getAgentString(),
                    'status' => 'Failed'
                ]);
            }
        }
        return $this->respond(['status' => true, 'message' => "Cập nhật tích hợp Google Sheets thành công"]);
    }
    public function ajax_edit_step2()
    {
        if ($this->request->getMethod(true) !== 'POST' || !has_permission('NotificationGoogleSheets', 'can_edit')) {
            return $this->respond(['status' => false, 'message' => 'Bạn không có quyền thực hiện thao tác này']);
        }

        $data = $this->request->getPost(['file_id', 'position', 'sheet_id', 'google_account_id', 'auto_delete_after']);

        $rules = [
            'file_id' => 'required|string',
            'position' => 'required|in_list[start,end]',
            'sheet_id' => 'required|string',
            'google_account_id' => 'required|integer|is_natural',
            'auto_delete_after' => 'required|integer|is_natural|in_list[0,3600,14400,28800,86400]'
        ];

        if (!$this->validate($rules)) {
            return $this->fail($this->validator->getErrors());
        }

        $file_id = $data['file_id'];
        $sheet_id = $data['sheet_id'];
        $google_account_id = $data['google_account_id'];
        $bankGoogleSheetModel = model(BankGoogleSheetModel::class);
        $bankGoogleSheetDetails = $bankGoogleSheetModel->where(
            [
                'file_id' => $file_id,
                'sheet_id' => $sheet_id,
                'google_account_id' => $google_account_id,
                'company_id' => $this->user_session['company_id']
                ]
        )->first();

        if(!is_object($bankGoogleSheetDetails))
            return $this->response->setJSON(['status' => false, 'message' => 'Không tìm thấy tích hợp này']);
        $bankGoogleSheetsExist = $bankGoogleSheetModel->where([
            'google_account_id' => $google_account_id,
            'company_id' => $this->user_session['company_id'],
            'file_id' => $data['file_id'],
            'sheet_id' => $data['sheet_id']
        ])->get()->getResult();

        if(!empty($bankGoogleSheetsExist)){
            foreach ($bankGoogleSheetsExist as $b){
                $result = $bankGoogleSheetModel->set($data)->where(
                    [
                        "id" => $b->id,
                        "company_id" => $this->user_session['company_id'],
                        "google_account_id" => $google_account_id
                    ]
                    )->update();
            }
            if($result) {
                add_user_log(array('data_id'=>$google_account_id,'company_id' => $this->user_session['company_id'], 'data_type'=>'notification_google_sheet_update','description'=>'Cập nhật tích hợp Google Sheets thành công','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
                return $this->response->setJSON(array("status"=>true,"message"=>"Cập nhật tích hợp Google Sheets thành công"));
            } else {
                add_user_log(array('data_id'=>$google_account_id,'company_id' => $this->user_session['company_id'], 'data_type'=>'notification_google_sheet_update','description'=>'Cập nhật tích hợp Google Sheets thất bại','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Failed'));
                return $this->response->setJSON(array("status"=>false,"message"=>"Không thể cập nhật dữ liệu này!"));
            }
        }else{
            return $this->response->setJSON(['status' => false, 'message' => 'Không tìm thấy tích hợp này']);
        }

    }
    private function isDuplicateIntegration($google_account_id, $company_id, $file_id, $sheet_id) {
        $bankGoogleSheetModel = model(BankGoogleSheetModel::class);
        return $bankGoogleSheetModel->where([
            'google_account_id' => $google_account_id,
            'company_id' => $company_id,
            'file_id' => $file_id,
            'sheet_id' => $sheet_id
        ])->countAllResults() > 0;
    }
    private function isValidResponse($response)
    {
        return isset($response['response']) && $response['response']->getStatusCode() === 200;
    }
    private function getSheetTitle($sheets, $sheetId)
    {
        foreach ($sheets as $sheet) {
            if ($sheet['properties']['sheetId'] == $sheetId) {
                return $sheet['properties']['title'];
            }
        }
        return null;
    }

    private function xss_clean_for_sheet($str){
        $str = preg_replace('/[^a-zA-Z0-9-._,\/àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴÈÉẸẺẼÊỀẾỆỂỄÌÍỊỈĨÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠÙÚỤỦŨƯỪỨỰỬỮỲÝỴỶỸĐ\s]/', '', $str);
        return $str;
    }
}