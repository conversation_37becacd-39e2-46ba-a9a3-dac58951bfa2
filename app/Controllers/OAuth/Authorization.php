<?php

namespace App\Controllers\OAuth;

use App\Controllers\BaseController;
use App\Libraries\OAuthServer\Entities\ScopeEntity;
use App\Libraries\OAuthServer\Entities\UserEntity;
use App\Libraries\OAuthServer\Traits\HandlesOAuthErrors;
use App\Libraries\Psr7\PsrHttpFactory;
use App\Models\OAuthClientModel;
use App\Models\OAuthTokenModel;
use CodeIgniter\HTTP\RedirectResponse;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use CodeIgniter\HTTP\URI;
use GuzzleHttp\Psr7\Response;
use League\OAuth2\Server\Exception\OAuthServerException;
use League\OAuth2\Server\RequestTypes\AuthorizationRequest;
use Nyholm\Psr7\Factory\Psr17Factory;
use Psr\Log\LoggerInterface;

class Authorization extends BaseController
{
    use HandlesOAuthErrors;

    public function initController(RequestInterface $request, ResponseInterface $response, LoggerInterface $logger)
    {
        parent::initController($request, $response, $logger);

        if (! check_oauth2_allowed_ip()) {
            show_404();
        }
    }

    public function authorize()
    {
        return $this->withErrorHandling(function () {
            $request = $this->request;
            $psr17Factory = new Psr17Factory();
            $psrHttpFactory = new PsrHttpFactory($psr17Factory, $psr17Factory);
            $authRequest = oauth2()->authorizationServer()->validateAuthorizationRequest($psrHttpFactory->createRequest($request));

            if (! auth()->check()) {
                return $request->getGet('prompt') === 'none' ? $this->denyRequest($authRequest) : $this->promptForLogin();
            }

            if ($request->getGet('prompt') === 'login' && ! session()->get('promptedForLogin')) {
                auth()->logout();

                return $this->promptForLogin();
            }

            session()->remove('promptedForLogin');
            $scopes = $this->parseScopes($authRequest);

            $client = model(OAuthClientModel::class)
                ->where('client_id', $authRequest->getClient()->getIdentifier())
                ->where('revoked', false)
                ->first();

            if (! $client) {
                throw OAuthServerException::invalidClient($psrHttpFactory->createRequest($request));
            }

            if ($request->getGet('prompt') !== 'consent' && $this->hasValidToken($scopes)) {
                return $this->approveRequest($authRequest);
            }

            if ($request->getGet('prompt') === 'none') {
                return $this->denyRequest($authRequest);
            }

            session()->set('authToken', $authToken = random_string('alnum', 40));
            session()->set('authRequest', $authRequest);

            return view('oauth/authorize', compact('client', 'request', 'scopes', 'authToken'));
        });
    }

    public function deny()
    {
        return $this->withErrorHandling(function () {
            $this->assertValidAuthToken();

            return $this->denyRequest($this->getAuthRequestFromSession());
        });
    }

    public function approve()
    {
        return $this->withErrorHandling(function () {
            $this->assertValidAuthToken();

            return $this->approveRequest($this->getAuthRequestFromSession());
        });
    }

    protected function assertValidAuthToken()
    {
        if (! session()->get('authToken') || $this->request->getPost('auth_token') !== session()->get('authToken')) {
            session()->remove(['authToken', 'authRequest']);
            throw OAuthServerException::invalidRequest('authToken');
        }
    }

    protected function getAuthRequestFromSession(): AuthorizationRequest
    {
        /** @var AuthorizationRequest $authRequest */
        $authRequest = session()->get('authRequest');

        if (! $authRequest) {
            throw OAuthServerException::invalidRequest('authRequest');
        }

        $authRequest->setUser(new UserEntity($this->user_details->id));

        return $authRequest;
    }

    protected function denyRequest(AuthorizationRequest $authRequest)
    {
        if (! isset($this->user_details)) {
            $uri = $authRequest->getRedirectUri() ?? (is_array($authRequest->getClient()->getRedirectUri())
                ? $authRequest->getClient()->getRedirectUri()[0]
                : $authRequest->getClient()->getRedirectUri());

            $separator = $authRequest->getGrantTypeId() === 'implicit' ? '#' : '?';
            $uri .= (strpos($uri, $separator) !== false ? '&' : $separator) . 'state=' . $authRequest->getState();

            throw OAuthServerException::accessDenied('Unauthenticated', $uri);
        }

        $authRequest->setUser(new UserEntity($this->user_details->id));
        $authRequest->setAuthorizationApproved(false);

        return $this->withErrorHandling(function () use ($authRequest) {
            return $this->convertResponse(
                oauth2()->authorizationServer()->completeAuthorizationRequest($authRequest, new Response())
            );
        });
    }

    protected function promptForLogin(): RedirectResponse
    {
        session()->set('promptedForLogin', true);
        session()->set('_previous.url', URI::createURIString(current_url(true)->getScheme(), current_url(true)->getAuthority(), current_url(true)->getPath(), current_url(true)->getQuery()));
        return redirect()->to('/login');
    }

    protected function parseScopes(AuthorizationRequest $authRequest): array
    {
        return oauth2()->scopesFor(array_map(fn(ScopeEntity $scope) => $scope->getIdentifier(), $authRequest->getScopes()));
    }

    protected function approveRequest(AuthorizationRequest $authRequest)
    {
        return $this->withErrorHandling(function () use ($authRequest) {
            $authRequest->setUser(new UserEntity($this->user_details->id));
            $authRequest->setAuthorizationApproved(true);

            return $this->convertResponse(
                oauth2()->authorizationServer()->completeAuthorizationRequest($authRequest, new Response())
            );
        });
    }

    protected function hasValidToken($scopes): bool
    {
        $token = model(OAuthTokenModel::class)
            ->where('user_id', $this->user_details->id)
            ->where('revoked', false)
            ->first();

        if (! $token) {
            return false;
        }

        return json_decode($token->scopes, true) === array_column($scopes, 'id');
    }
}
