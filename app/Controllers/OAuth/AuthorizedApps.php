<?php

namespace App\Controllers\OAuth;

use App\Controllers\BaseController;
use App\Models\OAuthRefreshTokenModel;
use App\Models\OAuthTokenModel;
use CodeIgniter\Database\RawSql;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use Psr\Log\LoggerInterface;

class AuthorizedApps extends BaseController
{
    public function initController(RequestInterface $request, ResponseInterface $response, LoggerInterface $logger)
    {
        parent::initController($request, $response, $logger);

        if (! check_oauth2_allowed_ip()) {
            show_404();
        }
    }

    public function index()
    {
        $data = [
            'page_title' => 'Ứng dụng đã ủy quyền',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'authorizedApps' => model(OAuthTokenModel::class)
                ->select([
                    'tb_autopay_oauth_access_tokens.id',
                    'tb_autopay_oauth_access_tokens.user_id',
                    'tb_autopay_oauth_access_tokens.client_id',
                    'tb_autopay_oauth_access_tokens.created_at',
                    'tb_autopay_oauth_access_tokens.scopes',
                    'tb_autopay_oauth_access_tokens.last_used_at',
                    'tb_autopay_oauth_clients.name',
                ])
                ->join('tb_autopay_oauth_clients', 'tb_autopay_oauth_clients.id = tb_autopay_oauth_access_tokens.client_id')
                ->join(new RawSql('(SELECT client_id, MAX(last_used_at) as max_last_used 
                    FROM tb_autopay_oauth_access_tokens 
                    WHERE user_id = ' . $this->user_details->id . ' 
                    AND revoked = false 
                    GROUP BY client_id) latest'), 'latest.client_id = tb_autopay_oauth_access_tokens.client_id AND latest.max_last_used = tb_autopay_oauth_access_tokens.last_used_at')
                ->where('tb_autopay_oauth_access_tokens.user_id', $this->user_details->id)
                ->where('tb_autopay_oauth_access_tokens.revoked', false)
                ->findAll(),
        ];

        echo theme_view('templates/autopay/header', $data);
        echo view('oauth/authorized-apps/index', $data);
        echo theme_view('templates/autopay/footer', $data);
    }

    public function revoke($tokenId)
    {
        $tokenModel = model(OAuthTokenModel::class);

        $token = $tokenModel
            ->select('id, client_id')
            ->where('user_id', $this->user_details->id)
            ->where('id', $tokenId)
            ->where('revoked', false)
            ->first();

        if (! $token) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Không tìm thấy ứng dụng OAuth đã ủy quyền',
            ]);
        }

        $tokenIds = model(OAuthTokenModel::class)
            ->select('id')
            ->where('client_id', $token->client_id)
            ->where('user_id', $this->user_details->id)
            ->where('revoked', false)
            ->findAll();

        $tokenIds = array_column($tokenIds, 'id');

        $tokenModel->update($tokenIds, ['revoked' => true]);

        model(OAuthRefreshTokenModel::class)
            ->whereIn('access_token_id', $tokenIds)
            ->set('revoked', true)
            ->update();

        add_user_log([
            'data_id' => $token->client_id,
            'company_id' => $this->user_session['company_id'],
            'data_type' => 'oauth_client_revoke',
            'description' => 'Thu hồi ứng dụng OAuth',
            'user_id' => auth()->id(),
            'ip' => $this->request->getIPAddress(),
            'user_agent' => $this->request->getUserAgent()->getAgentString(),
            'status' => 'Success',
        ]);

        session()->setFlashdata('alert-success', 'Ứng dụng đã được thu hồi thành công');

        return $this->response->setJSON(['success' => true]);
    }

    public function revokeAll()
    {
        $tokenModel = model(OAuthTokenModel::class);

        $tokens = $tokenModel
            ->select('id')
            ->where('user_id', $this->user_details->id)
            ->where('revoked', false)
            ->findAll();

        if (empty($tokens)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Không có ứng dụng OAuth nào đã được ủy quyền',
            ]);
        }

        $tokenIds = array_column($tokens, 'id');

        $tokenModel->update($tokenIds, ['revoked' => true]);

        model(OAuthRefreshTokenModel::class)
            ->whereIn('access_token_id', $tokenIds)
            ->set('revoked', true)
            ->update();

        add_user_log([
            'data_id' => $this->user_details->id,
            'company_id' => $this->user_session['company_id'],
            'data_type' => 'oauth_client_revoke_all',
            'description' => 'Thu hồi tất cả ứng dụng OAuth',
            'user_id' => auth()->id(),
            'ip' => $this->request->getIPAddress(),
            'user_agent' => $this->request->getUserAgent()->getAgentString(),
            'status' => 'Success',
        ]);

        session()->setFlashdata('alert-success', 'Tất cả ứng dụng đã được thu hồi thành công');

        return $this->response->setJSON(['success' => true]);
    }
}
