<?php

namespace App\Controllers\OAuth;

use App\Libraries\OAuthServer\Traits\HandlesOAuthErrors;
use App\Libraries\Psr7\PsrHttpFactory;
use CodeIgniter\Controller;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use <PERSON><PERSON>holm\Psr7\Factory\Psr17Factory;
use Nyholm\Psr7\Response;
use Psr\Log\LoggerInterface;

class AccessToken extends Controller
{
    use HandlesOAuthErrors;

    protected $helpers = ['general'];

    public function initController(RequestInterface $request, ResponseInterface $response, LoggerInterface $logger)
    {
        parent::initController($request, $response, $logger);

        if (! check_oauth2_allowed_ip()) {
            show_404();
        }
    }

    public function issueToken()
    {
        return $this->withErrorHandling(function () {
            $psr17Factory = new Psr17Factory();
            $psrHttpFactory = new PsrHttpFactory($psr17Factory, $psr17Factory);

            $response = oauth2()->authorizationServer()->respondToAccessTokenRequest(
                $psrHttpFactory->createRequest($this->request),
                new Response()
            );

            return $this->convertResponse($response);
        });
    }
}
