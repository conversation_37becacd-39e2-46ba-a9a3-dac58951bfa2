<?php

namespace App\Controllers\OAuth;

use App\Controllers\BaseController;
use App\Models\OAuthClientModel;
use App\Models\OAuthTokenModel;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use Config\OAuth2;
use Psr\Log\LoggerInterface;

class Clients extends BaseController
{
    protected OAuth2 $config;

    public function initController(RequestInterface $request, ResponseInterface $response, LoggerInterface $logger)
    {
        parent::initController($request, $response, $logger);

        $this->config = config(OAuth2::class);

        if (! in_array($this->company_details->role, ['Admin', 'SuperAdmin']) || ! is_admin()) {
            show_404();
        }
    }

    public function index()
    {
        $data = [
            'page_title' => 'Ứng dụng OAuth',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'clients' => model(OAuthClientModel::class)
                ->where('user_id', auth()->id())
                ->where('revoked', false)
                ->orderBy('id', 'desc')
                ->findAll(),
            'clientId' => session()->getFlashdata('client_id'),
            'clientSecret' => session()->getFlashdata('client_secret'),
        ];

        echo theme_view('templates/autopay/header', $data);
        echo view('oauth/clients/index', $data);
        echo theme_view('templates/autopay/footer', $data);
    }

    public function new()
    {
        $data = [
            'page_title' => 'Tạo ứng dụng OAuth',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'defaultScope' => explode(' ', $this->config->defaultScope),
            'scopes' => oauth2()->scopes(),
        ];

        echo theme_view('templates/autopay/header', $data);
        echo view('oauth/clients/create', $data);
        echo theme_view('templates/autopay/footer', $data);
    }

    public function create()
    {
        $data = $this->request->getPost(['name', 'redirect_uri', 'scopes', 'terms']);

        if (! $this->validateData($data, [
            'name' => ['label' => 'Tên ứng dụng', 'rules' => 'required|max_length[100]'],
            'redirect_uri' => ['label' => 'Địa chỉ URL chuyển hướng', 'rules' => 'required|valid_url_strict'],
            'scopes' => ['label' => 'Quyền truy cập', 'rules' => 'required'],
            'terms' => ['label' => 'Điều khoản', 'rules' => 'required'],
        ])) {
            return $this->response
                ->setStatusCode(400)
                ->setJSON([
                    'error' => 400,
                    'messages' => $this->validator->getErrors(),
                    'status' => 400,
                ]);
        }

        $scopes = $this->request->getPost('scopes') ?? [];
        $availableScopes = array_column(oauth2()->scopes(), 'id');

        foreach ($scopes as $scope) {
            if (! in_array($scope, $availableScopes)) {
                return $this->response->setJSON([
                    'status' => false,
                    'message' => 'Quyền truy cập đã chọn không hợp lệ.',
                ]);
            }
        }

        $clientId = sprintf(
            '%s-%s',
            to_slug(mb_substr($this->request->getPost('name'), 0, 20)),
            random_string()
        );

        $clientSecret = bin2hex(random_bytes(32));

        $id = model(OAuthClientModel::class)->insert([
            'id' => uuid(),
            'user_id' => auth()->id(),
            'name' => esc($this->request->getPost('name')),
            'client_id' => $clientId,
            'scopes' => json_encode($scopes),
            'client_secret' => $clientSecret,
            'redirect_uri' => $this->request->getPost('redirect_uri'),
            'revoked' => false,
        ]);

        add_user_log([
            'data_id' => $id,
            'company_id' => $this->user_session['company_id'],
            'data_type' => 'oauth_client_add',
            'description' => 'Tạo ứng dụng OAuth mới',
            'user_id' => auth()->id(),
            'ip' => $this->request->getIPAddress(),
            'user_agent' => $this->request->getUserAgent()->getAgentString(),
            'status' => 'Success',
        ]);

        session()->setFlashdata('client_id', $clientId);
        session()->setFlashdata('client_secret', $clientSecret);

        return $this->response->setJSON([
            'status' => true,
            'redirect_url' => base_url('oauth/apps'),
        ]);
    }

    public function edit($id)
    {
        $client = model(OAuthClientModel::class)
            ->where('id', $id)
            ->where('user_id', auth()->id())
            ->first();

        if (! $client) {
            show_404();
        }

        $data = [
            'page_title' => "Ứng dụng OAuth {$client->name}",
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'client' => $client,
            'defaultScope' => explode(' ', $this->config->defaultScope),
            'scopes' => oauth2()->scopes(),
        ];

        $client->scopes = json_decode($client->scopes, true);

        echo theme_view('templates/autopay/header', $data);
        echo view('oauth/clients/edit', $data);
        echo theme_view('templates/autopay/footer', $data);
    }

    public function update($id)
    {
        $client = model(OAuthClientModel::class)
            ->where('id', $id)
            ->where('user_id', auth()->id())
            ->first();

        if (! $client) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Không tìm thấy ứng dụng OAuth.',
            ]);
        }

        if (! $this->validate([
            'name' => ['label' => 'Tên ứng dụng', 'rules' => 'required|max_length[100]'],
            'redirect_uri' => ['label' => 'Địa chỉ URL chuyển hướng', 'rules' => 'required|valid_url_strict'],
            'scopes' => ['label' => 'Quyền truy cập', 'rules' => 'required'],
        ])) {
            return $this->response
                ->setStatusCode(400)
                ->setJSON([
                    'error' => 400,
                    'messages' => $this->validator->getErrors(),
                    'status' => 400,
                ]);
        }

        $scopes = $this->request->getPost('scopes') ?? [];
        $availableScopes = array_column(oauth2()->scopes(), 'id');

        foreach ($scopes as $scope) {
            if (! in_array($scope, $availableScopes)) {
                return $this->response->setJSON([
                    'status' => false,
                    'message' => 'Quyền truy cập đã chọn không hợp lệ.',
                ]);
            }
        }

        model(OAuthClientModel::class)->update($id, [
            'name' => esc($this->request->getPost('name')),
            'scopes' => json_encode($scopes),
            'redirect_uri' => $this->request->getPost('redirect_uri'),
        ]);

        add_user_log([
            'data_id' => $id,
            'company_id' => $this->user_session['company_id'],
            'data_type' => 'oauth_client_edit',
            'description' => 'Chỉnh sửa ứng dụng OAuth',
            'user_id' => auth()->id(),
            'ip' => $this->request->getIPAddress(),
            'user_agent' => $this->request->getUserAgent()->getAgentString(),
            'status' => 'Success',
        ]);

        return $this->response->setJSON([
            'status' => true,
            'message' => 'Đã cập nhật ứng dụng OAuth.',
        ]);
    }

    public function delete($id)
    {
        $client = model(OAuthClientModel::class)
            ->where('id', $id)
            ->where('user_id', auth()->id())
            ->first();

        if (! $client) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Không tìm thấy ứng dụng OAuth.',
            ]);
        }

        model(OAuthClientModel::class)->update($id, ['revoked' => true]);
        model(OAuthTokenModel::class)
            ->where('client_id', $id)
            ->set('revoked', true)
            ->update();

        add_user_log([
            'data_id' => $id,
            'company_id' => $this->user_session['company_id'],
            'data_type' => 'oauth_client_delete',
            'description' => 'Xóa ứng dụng OAuth',
            'user_id' => auth()->id(),
            'ip' => $this->request->getIPAddress(),
            'user_agent' => $this->request->getUserAgent()->getAgentString(),
            'status' => 'Success',
        ]);

        session()->setFlashdata('alert-success', 'Đã xóa ứng dụng OAuth.');

        return $this->response->setJSON(['status' => true]);
    }
}
