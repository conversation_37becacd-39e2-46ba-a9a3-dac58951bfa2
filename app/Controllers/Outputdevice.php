<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\BankAccountModel;
use App\Models\BankIntegrationOutputDeviceModel;
use App\Models\OutputDeviceModel;
use App\Models\OutputDeviceReplayMessageQueueModel;
use CodeIgniter\API\ResponseTrait;
use App\Actions\OutputDevice\ExecuteOutputDevice;
use App\Libraries\FlametechvnClient;
use App\Models\AcbEnterpriseAccountModel;
use App\Models\BankSubAccountModel;
use App\Models\BidvEnterpriseAccountModel;
use App\Models\OcbEnterpriseAccountModel;
use App\Models\OcbEnterpriseAppModel;
use App\Models\UserPermissionBankModel;
use App\Models\OutputDeviceLogModel;
use App\Models\TransactionsModel;
use App\Models\UserPermissionBankSubModel;
use Config\Acb;
use Config\Bidv;
use Config\OutputDevices;
use Config\Flametechvn;
use Config\Ocb;
use PharIo\Manifest\Library;
use phpseclib3\Crypt\DES;
use App\Models\BankModel;
use App\Features\BankAccount\BankAccountFeature;
use App\Features\Store\StoreFeature;
use App\Models\MbbEnterpriseAccountModel;
use App\Models\OutputDeviceDecalModel;
use App\Models\MbbMmsTerminalModel;
use App\Libraries\TomtomClient;
use App\Models\AbbankLinkedAccountModel;
use App\Models\BankShopLinkModel;
use App\Models\ProvinceModel;
use App\Models\DistrictModel;
use App\Models\ShopModel;
use App\Models\WardModel;
use Exception;

class Outputdevice extends BaseController
{
    use ResponseTrait;
    
    protected $config_output_device;

    public function __construct()
    {
        

        $config_output_device = config(OutputDevices::class);
        helper('general');
        if($config_output_device->enabled == false && !is_admin()){
            if (ENVIRONMENT == 'production') {
                show_404();
            }
        }

        
    }

    protected function responseCustom($code, $message, $data = 0)
    {
        $response = service('response');

        $data_res = [
            'code' => $code,
            'message' => $message,
        ];

        if ($data != 0 || $data == []) {
            $data_res['data'] = $data;
        }

        $csrfToken = csrf_hash(); 

        $response->setStatusCode($code);
        $response->setJSON($data_res);

        $response->setHeader('X-CSRF-TOKEN', $csrfToken);

        return $response;
    }

    public function index()
    { 
        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])){
            show_404();
        }
        $data = [
            'page_title' => 'Loa',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        $model_seach = $this->request->getGet("model") ??"";
        $condition = [];
        if(!empty($model_seach)){
            $condition = ['tb_autopay_output_device.model' => xss_clean($model_seach)];
        }

        $OutputDeviceModel = model(OutputDeviceModel::class);
        $OutputDeviceReplayMessageQueueModel = slavable_model(OutputDeviceReplayMessageQueueModel::class, 'Transactions');

        $integrationData = $OutputDeviceModel->select("
            tb_autopay_output_device.id,
            tb_autopay_output_device.imei,
            tb_autopay_output_device.name,
            tb_autopay_output_device.active,
            tb_autopay_output_device.active_date,
            tb_autopay_output_device.serial_number,
            tb_autopay_output_device.model,
            tb_autopay_output_device.company_id,
            tb_autopay_output_device.vendor,
            tb_autopay_output_device.online_status,
            tb_autopay_output_device.external_device_id,
            tb_autopay_output_device.created_at,
            tb_autopay_output_device.bank_id,
            tb_autopay_output_device.shop_id,
            tb_autopay_bank.brand_name,
            COUNT(tb_autopay_bank_integration_output_device.bank_account_id) AS bank_account_count,
            COUNT(CASE 
            WHEN tb_autopay_bank_integration_output_device.bank_account_id IS NOT NULL 
            AND tb_autopay_bank_integration_output_device.sub_account_id IS NULL 
            THEN 1 
            END) AS main_account_count,
            COUNT(CASE 
            WHEN tb_autopay_bank_integration_output_device.sub_account_id IS NOT NULL 
            THEN 1 
            END) AS va_account_count
        ")
        ->where($condition)
        ->where(['tb_autopay_output_device.company_id' => $this->company_details->company_id])
        ->join('tb_autopay_bank_integration_output_device', 'tb_autopay_output_device.id = tb_autopay_bank_integration_output_device.output_device_id', 'left')
        ->join('tb_autopay_bank', 'tb_autopay_output_device.bank_id = tb_autopay_bank.id', 'left')
        ->orderBy('tb_autopay_bank_integration_output_device.created_at', 'DESC')
        ->groupBy('tb_autopay_output_device.id')
        ->get()
        ->getResultArray();

        if (!empty($integrationData)) {
            foreach ($integrationData as &$val) {
                $val['count_transaction'] = $OutputDeviceReplayMessageQueueModel->where(['company_id' => $this->company_details->company_id,'output_device_id' => $val['id']])->countAllResults();
                $val['created_day'] = date('d/m/Y', strtotime($val['created_at']));
                
                
                $firstIntegration = model(BankIntegrationOutputDeviceModel::class)->where(['output_device_id' => $val['id']])->orderBy('id', 'DESC')->first();
                
                if (!$firstIntegration) continue;
                
                if ($firstIntegration['sub_account_id']) {
                    $val['first_qr'] = (array) model(BankSubAccountModel::class)
                    ->select('tb_autopay_bank_account.*, tb_autopay_bank.brand_name')
                    ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id = tb_autopay_bank_sub_account.bank_account_id', 'left')
                    ->join('tb_autopay_bank', 'tb_autopay_bank.id = tb_autopay_bank_account.bank_id', 'left')
                    ->where(['tb_autopay_bank_sub_account.id' => $firstIntegration['sub_account_id']])
                    ->first();
                } else {
                    $val['first_qr'] = (array) model(BankAccountModel::class)
                    ->select('tb_autopay_bank_account.*, tb_autopay_bank.brand_name')
                    ->join('tb_autopay_bank', 'tb_autopay_bank.id = tb_autopay_bank_account.bank_id', 'left')
                    ->where(['tb_autopay_bank_account.id' => $firstIntegration['bank_account_id']])
                    ->first();
                }
            }
            unset($val);
        }

        
        $data['alert'] = session()->getFlashdata('alert');

        $data['outputdevice_data'] = $integrationData;
        
        if (is_shop_billing_subscription()) {
            $shopIds = array_column($data['outputdevice_data'], 'shop_id');
            
            $stores = count($shopIds) > 0 ? model(ShopModel::class)->where('company_id', $this->company_details->id)
                ->whereIn('id', array_column($data['outputdevice_data'], 'shop_id'))
                ->findAll() : [];
                    
            foreach ($data['outputdevice_data'] as &$row) {
                $row['store'] = array_filter($stores, fn ($store) => $store->id == $row['shop_id'])[0] ?? null;
            }
        }

        echo theme_view('templates/autopay/header',$data);
        echo view('outputdevice/index',$data);
        echo theme_view('templates/autopay/footer',$data);
    }

    // kích hoạt thiết bị Flametechvn Aisino Old
    public function verify_otp() {
        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])) {
            show_404();
        }
    
        $data = $this->request->getPost();
        
        $session = service('session');
        $data_integration = $session->get('output_device_integration');
        $device_id = $data_integration['id'] ?? "";
        if(empty($data_integration)){
            return $this->responseCustom(422,"Lỗi lưu thông tin, hãy tích hợp lại");
        }      
        $data_integration['otp'] = xss_clean($data['otp']);
        // Load validation service
        $validation = service("validation");
        $validationRules = [
            "otp" => [
                "label" => "otp",
                "rules" => "required|regex_match[/^\d{6}$/]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "regex_match" => "{field} phải gồm chính xác 6 chữ số.",
                ],
            ],
            
            "vendor" => [
                "label" => "Vendor",
                "rules" => "required|in_list[Flametechvn]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "in_list" => "{field} phải là 'Aisino' hoặc 'Flametechvn'",
                ],
            ],
            "model" => [
                "label" => "Model",
                "rules" => "required|in_list[S003,S16,Q181,Q190]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "valid_vendor" => "{field} phải là 'Aisino' hoặc 'Flametechvn'.",
                ],
            ],
        ];
    

        // check ID thiết bị
        if(empty($device_id)){
            $validationRules['serial_number'] = [
                "label" => "Serial Number",
                "rules" => "required|is_unique[tb_autopay_output_device.serial_number]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "is_unique" => "{field} đã tồn tại trong hệ thống",
                ],
            ];
        }
        // Set validation rules
        $validation->setRules($validationRules);
    
        // Check data
        if (!$validation->run($data_integration)) {
            return $this->response->setStatusCode(422)->setJSON(array(
                'status' => False,
                'message' => implode(". ", $validation->getErrors())
            ));
        }

        // xác thực OTP
        $FlametechvnClient =  new FlametechvnClient();
        $status_verify = $FlametechvnClient->verifyOTP($data_integration['serial_number'],$data_integration['session_id'],$data_integration['otp']);

        if($status_verify['status_code'] == 200 ){

            $data_integration['external_device_token'] = $status_verify['device_token'];
            $data_integration['active'] = 1;
            $data_integration['online_status'] = !empty($status_verify['online'])?'on':'off';

            // filter data
            unset($data_integration['session_id']);
            unset($data_integration['otp']);

            

            // check device
            $OutputDeviceModel = model(OutputDeviceModel::class);
            $BankIntegrationOutputDeviceModel = model(BankIntegrationOutputDeviceModel::class);

            // check ID thiết bị
             // update thông tin tích hợp trước đó Flametechvn ID
            if (!empty($device_id)) {
                unset($data_integration['id']);
                $status_update = $OutputDeviceModel->set($data_integration)->where(['vendor' => 'Flametechvn','id' =>$device_id])->update();
                if (!$status_update) {
                    return $this->responseCustom(422, "Không thể kích hoạt thiết bị!");
                }

                $session->setFlashdata('alert', [
                    'type' => 'success',
                    'message' => 'Liên kết thiết bị thành công',
                ]);

                // Trả về phản hồi thành công với URL
                return $this->responseCustom(200, "Liên kết thành công!", ['url' => base_url('/outputdevice')]);
            }

            
      

            $check_exits = $OutputDeviceModel->where(['serial_number'=>$data_integration['serial_number']])->first();
            if(!empty($check_exits)){
                return $this->responseCustom(422,"Thiết bị đã tồn tại trên hệ thống!");
            }

            //add thông tin
            $status_insert = $OutputDeviceModel->insert($data_integration);
            // Lấy ID vừa insert
            $inserted_id = $OutputDeviceModel->insertID(); 
            if (!$inserted_id) {
                return $this->responseCustom(422, "Thêm Loa thất bại, ID không hợp lệ.");
            }

            // Kiểm tra kết quả
            if ($status_insert) {

                $record_integration_bank = array(
                    'output_device_id' =>$inserted_id,
                    'company_id' => $this->user_session['company_id'],
                );

                $status_insert_bank =  $BankIntegrationOutputDeviceModel->insert($record_integration_bank);
                if($status_insert_bank){
                    $session->setFlashdata('alert', [
                        'type' => 'success', 
                        'message' => 'Thêm thiết bị thành công',
                    ]);
                    return $this->responseCustom(200, "Xác thực OTP thành công",['url'=>base_url().'/outputdevice']);
                }else{
                    return $this->responseCustom(422, "Thêm tích hợp cấu hình loa thất bại,Hãy liên hệ kỹ thuật");
                }
            } else {
                return $this->responseCustom(422, "Thêm Loa thất bại,Hãy liên hệ kỹ thuật");
            }
                    
            
            return $this->responseCustom(423,"Lỗi, hãy liên hệ kỹ thuật",$data_integration);
        }
        log_message("error","API verify OTP: ".json_encode($status_verify));
        if($status_verify['status_code'] == 404){
            return $this->responseCustom(422,"Không tìm thấy thông tin thiết bị");
        }

        if($status_verify['status_code'] == 423){
            return $this->responseCustom(422,"OTP đã quá hạn, Hãy thử lại!");
        }

        if($status_verify['status_code'] == 424){
            return $this->responseCustom(422,"OTP không khớp, Hãy thử lại!");
        }


        return $this->responseCustom(422,"Lỗi, Hãy liên hệ kỹ thuật để kiểm tra",$status_verify);
    }
    
    // Aisino
    public function config_admin($id="")
    { 

        if(!is_admin()){
            if (ENVIRONMENT == 'production') {
                show_404();
            }
        }

        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])){
            show_404();
        }

        // check device
        $OutputDeviceModel = model(OutputDeviceModel::class);
        $check_device = $OutputDeviceModel->where(['id'=>$id,'company_id' =>$this->company_details->company_id])->first();
        if(empty($check_device)){
            return $this->responseCustom(404,"Thiết bị không tồn tại!");
        }
       
        $data = [
            'page_title' => 'Loa',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];
        $BankIntegrationOutputDeviceModel = model(BankIntegrationOutputDeviceModel::class);
        $OutputDeviceReplayMessageQueueModel = slavable_model(OutputDeviceReplayMessageQueueModel::class, 'Transactions');
        $OutputDeviceModel = model(OutputDeviceModel::class);
        $BankAccoutModel = model(BankAccountModel::class);
        $userPermissionBankSubModel = model(UserPermissionBankSubModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);

        // custom QR LCD
        $data['role'] = $this->company_details->role;
        if(in_array($data['role'],['Admin','SuperAdmin'])){

            $data['bankAccounts'] = $BankAccoutModel->select("
                    tb_autopay_bank_account.id,tb_autopay_bank_account.bank_id,tb_autopay_bank_account.account_holder_name,tb_autopay_bank_account.bank_sms_connected, 
                    tb_autopay_bank_account.bank_api_connected,tb_autopay_bank_account.account_number,tb_autopay_bank_account.label,tb_autopay_bank.brand_name,tb_autopay_bank.short_name,tb_autopay_bank.brand_name,tb_autopay_bank.logo_path,tb_autopay_bank.icon_path")->join("tb_autopay_bank","tb_autopay_bank.id=tb_autopay_bank_account.bank_id")->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id']])->orderBy('tb_autopay_bank_account.id','asc')->get()->getResult();
        }
        else{

            $data['bankAccounts'] = $userPermissionBankSubModel->get_bank_accounts($this->user_details->id);
        }

        $bank_accounts = array_map(function($obj) {
            return get_object_vars($obj);
        }, $data['bankAccounts']);    
        $list_id_bank_account = array_column($bank_accounts, "id");
        $data['bank_sub_accounts_custom']=[];
        if(!empty($list_id_bank_account)){    
            $query = $bankSubAccountModel->select(
                "tb_autopay_bank_sub_account.id, 
                    tb_autopay_bank.brand_name,
                    tb_autopay_bank.logo_path, 
                    tb_autopay_bank.icon_path, 
                    tb_autopay_bank_account.account_holder_name, 
                    tb_autopay_bank_account.account_number, 
                    tb_autopay_bank_account.bank_sms_connected, 
                    tb_autopay_bank_account.bank_api_connected, 
                    tb_autopay_bank_sub_account.sub_account, 
                    tb_autopay_bank_sub_account.acc_type, 
                    tb_autopay_bank_sub_account.label",
                )
                ->join("tb_autopay_bank_account", "tb_autopay_bank_account.id = tb_autopay_bank_sub_account.bank_account_id")
                ->join("tb_autopay_bank", "tb_autopay_bank.id = tb_autopay_bank_account.bank_id", "left")
                ->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id']])
                ->whereIn('tb_autopay_bank_account.id', $list_id_bank_account);                     
            if (in_array($this->company_details->role, ['Admin', 'SuperAdmin'])) {
                $data['bank_sub_accounts_custom']  = $query->get()->getResultArray();
            } else {
                $data['bank_sub_accounts_custom']  = $query
                ->join("tb_autopay_user_permission_bank_sub", "tb_autopay_user_permission_bank_sub.sub_account_id = tb_autopay_bank_sub_account.id", "left")
                ->where("tb_autopay_user_permission_bank_sub.user_id", $this->user_session['user_id'])
                ->get()
                ->getResultArray();
            }    
        }    

        // check thông tin tích hợp thiết bị công ty
        $checkExits = $OutputDeviceModel->where(['id'=>$id,'company_id'=>$this->company_details->company_id])->findAll();
        if(empty($checkExits)){
            show_404();
        }

        // Check thông tin thiết bị
        $outputDeviceData = $OutputDeviceModel->where(['id'=>$id])->get()->getRowArray();
        $data['outputdevice_data'] = $outputDeviceData;

        $bankDataAccount = $BankAccoutModel->select("
        tb_autopay_bank_account.id bank_account_id,
        tb_autopay_bank_account.company_id,
        tb_autopay_bank_account.bank_id,
        tb_autopay_bank_account.account_holder_name,
        tb_autopay_bank_account.account_number account_number_bank,
        tb_autopay_bank_account.label bank_label,
        tb_autopay_bank_account.bank_sms,
        tb_autopay_bank_account.bank_sms_connected,
        tb_autopay_bank_account.bank_api,
        tb_autopay_bank_account.bank_api_connected,
    
        tb_autopay_bank_sub_account.id sub_account_id,
        tb_autopay_bank_sub_account.sub_account,
        tb_autopay_bank_sub_account.acc_type,
        tb_autopay_bank_sub_account.sub_holder_name,
        tb_autopay_bank_sub_account.label sub_account_label,
    
        tb_autopay_bank.brand_name brand_name_bank,
        tb_autopay_bank.short_name short_name_bank,
        tb_autopay_bank.full_name full_name_bank,
        tb_autopay_bank.bin bin_bank,
        tb_autopay_bank.code code_bank,
        tb_autopay_bank.logo_path logo_path_bank,
        tb_autopay_bank.icon_path icon_path_bank
        ")

       
        ->groupStart() // Bắt đầu nhóm điều kiện
            ->where(['tb_autopay_bank_account.company_id' => $this->company_details->company_id])
            ->where('tb_autopay_bank_account.bank_api_connected', 1)
            ->where('tb_autopay_bank_account.bank_sms_connected', 0)
        ->groupEnd() // Kết thúc nhóm điều kiện
        ->orGroupStart() // Bắt đầu nhóm điều kiện OR
            ->where(['tb_autopay_bank_account.company_id' => $this->company_details->company_id])
            ->where('tb_autopay_bank_account.bank_api_connected', 0)
            ->where('tb_autopay_bank_account.bank_sms_connected', 1)
        ->groupEnd() // Kết thúc nhóm điều kiện OR
        
        ->join("tb_autopay_bank_sub_account", "tb_autopay_bank_sub_account.bank_account_id = tb_autopay_bank_account.id", "left")
        ->join("tb_autopay_bank", "tb_autopay_bank.id = tb_autopay_bank_account.bank_id", "left")
        ->get()
        ->getResultArray();

        // group dữ liệu theo id ngân hàng group VA
        $groupedArray = [];
        foreach ($bankDataAccount as $item) {
            $bank_account_id = $item['bank_account_id'];
    
            // Nếu chưa tồn tại nhóm cho bank_id này, khởi tạo nhóm với thông tin ngân hàng
            if (!isset($groupedArray[$bank_account_id])) {
                $groupedArray[$bank_account_id] = [
                    'bank_id' => $item['bank_id'],
                    'bank_account_id' => $item['bank_account_id'],
                    'brand_name_bank' => $item['brand_name_bank'],
                    'account_number_bank' => $item['account_number_bank'],
                    'account_holder_name_bank' => $item['account_holder_name'],
                    'short_name_bank' => $item['short_name_bank'],
                    'full_name_bank' => $item['full_name_bank'],
                    'bin_bank' => $item['bin_bank'],
                    'code_bank' => $item['code_bank'],
                    'logo_path_bank' => $item['logo_path_bank'],
                    'icon_path_bank' => $item['icon_path_bank'],
                    'method_integration' => $item['bank_sms']=="1"?"sms":"api",
                    'sub_accounts' => []  
                ];
            }

            // Thêm phần tử sub-account vào nhóm tương ứng
            $groupedArray[$item['bank_account_id']]['sub_accounts'][] = $item;
        }
        //  lọc ngân hàng kết nối api và kiểm tra VA đã tích hợp
        $filteredArray = [];
        foreach($groupedArray as $key => $item){
            if($item['method_integration'] == "api" && count($item['sub_accounts']) == 1 && empty($item['sub_accounts'][0]['sub_account_id'])){
                // Không thêm phần tử này vào mảng tạm nếu không thỏa mãn điều kiện
                $filteredArray[$key] = $item;
                continue;
            }
            $filteredArray[$key] = $item;  // Chỉ thêm phần tử hợp lệ
        }

        // In ra kết quả
        $data['integration_data'] = $filteredArray;

        //
        $data_missed = $OutputDeviceReplayMessageQueueModel->where(['output_device_id'=>$id,'company_id'=>$this->company_details->company_id,'status'=>'Pending'])->get()->getResultArray();
        // Danh sách tài khoản bank
        
        $current_time = time();
        $array_time_filter = [
            '1h' => HOUR, // timestamp
            '1d' => DAY, // timestamp
            '1w' => WEEK, // timestamp
        ];

        // Lọc dữ liệu theo các khoảng thời gian
        $filtered_data = [
            '1h' => [],
            '1d' => [],
            '1w' => [],
        ];

        foreach ($data_missed as $record) {
            // Chuyển đổi thời gian trong record thành timestamp
            $record_time = strtotime($record['created_at']); // assuming 'timestamp' is the key
        
            // Lọc theo 1 giờ
            if (($current_time - $record_time) <= $array_time_filter['1h']) {
                $filtered_data['1h'][] = $record;
            }
        
            // Lọc theo 1 ngày
            if (($current_time - $record_time) <= $array_time_filter['1d']) {
                $filtered_data['1d'][] = $record;
            }
        
            // Lọc theo 1 tuần
            if (($current_time - $record_time) <= $array_time_filter['1w']) {
                $filtered_data['1w'][] = $record;
            }
        }

        $data['filtered_data'] = $filtered_data;
        $data['count_message_missed'] = count($data_missed);

        echo theme_view('templates/autopay/header',$data);
        echo view('outputdevice/config_admin',$data);
        echo theme_view('templates/autopay/footer',$data);
    }

    // Aisino
    public function updateintegration($device_id){

        if(!is_admin()){
            if (ENVIRONMENT == 'production') {
                show_404();
            }
        }
        
        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])){
            show_404();
        }
        $data_post = $this->request->getBody();
        $data_input = json_decode($data_post,true);

        // check device
        $OutputDeviceModel = model(OutputDeviceModel::class);
        $check_device = $OutputDeviceModel->where(['id'=>$device_id,'company_id' =>$this->company_details->company_id])->first();
        if(empty($check_device)){
            return $this->responseCustom(404,"Thiết bị không tồn tại!");
        }

        // Kiểm tra lỗi giải mã JSON
        if (json_last_error() !== JSON_ERROR_NONE) {
            return $this->responseCustom(422,"Không thể chuyển đổi thông tin!");
        }
        $red_check = 0;
        
        foreach($data_input as $item){

            // Check if main bank is selected but no sub accounts
            if ($item['check_main_bank'] == 1 && count($item['sub_account_ids']) == 0) {
                $red_check = 1;
            }
        
            // Check if no main bank selected but sub accounts exist
            if ($item['check_main_bank'] == 0 && count($item['sub_account_ids']) > 0) {
                $red_check = 1;
            }
        
            // Check if main bank is selected but there are sub accounts
            if ($item['check_main_bank'] == 1 && count($item['sub_account_ids']) > 0) {
                $red_check = 1;
            }
            
        }

        if(empty($red_check)){
            return $this->responseCustom(422,"Không nhận dạng được thông tin!");
        }

        $BankIntegrationOutputDeviceModel = model(BankIntegrationOutputDeviceModel::class);

        if(empty($data_input)){
            return $this->responseCustom(404,"Không thấy dữ liệu gửi lên!");
        }

        $array_data_merga = array();

        foreach ($data_input as $val) {
            if ($val['method_integration'] == "api" && $val['device_id'] == $device_id) {
                // Chỉ xử lý khi bank_account_id không nằm trong danh sách [9,10,17,18] ( id bank không thể nhận tiền vào qua tài khoản chính)
                if (!in_array($val['bank_id'], [9, 10, 17, 18])) {
                
                    // Tạo mảng cơ bản
                    $array_bank = array(
                        'bank_account_id' => $val['bank_account_id'],
                        'output_device_id' => $val['device_id'],
                        'company_id' => $this->company_details->company_id,
                        'sub_account_id' => null, // Mặc định là null
                    );

                    // Thêm mảng cơ bản ( tài khoản chính) vào mảng merge
                    if($val['check_main_bank']==1){

                        $array_data_merga[] = $array_bank;
                    }
                
                    // Nếu có sub_account_ids thì xử lý từng giá trị
                    if (!empty($val['sub_account_ids'])) {
                        foreach ($val['sub_account_ids'] as $item) {
                            // Tạo một mảng mới với sub_account_id cụ thể
                            $array_bank_item = array(
                                'bank_account_id' => $val['bank_account_id'],
                                'output_device_id' => $val['device_id'],
                                'company_id' => $this->company_details->company_id,
                                'sub_account_id' => $item,
                            );
                            // Thêm mảng chi tiết vào mảng tổng
                            $array_data_merga[] = $array_bank_item;
                        }
                    }
                } else {
                    if (!empty($val['sub_account_ids'])) {
                        foreach ($val['sub_account_ids'] as $item) {
                            // Tạo một mảng mới với sub_account_id cụ thể
                            $array_bank_item = array(
                                'bank_account_id' => $val['bank_account_id'],
                                'output_device_id' => $val['device_id'],
                                'company_id' => $this->company_details->company_id,
                                'sub_account_id' => $item,
                            );
                            // Thêm mảng chi tiết vào mảng tổng
                            $array_data_merga[] = $array_bank_item;
                        }
                    }
                }
            }
            if ($val['method_integration'] == "sms" && $val['device_id'] == $device_id) {
                
                // Tạo mảng cơ bản
                $array_bank = array(
                    'bank_account_id' => $val['bank_account_id'],
                    'output_device_id' => $val['device_id'],
                    'company_id' => $this->company_details->company_id,
                    'sub_account_id' => null, // Mặc định là null
                );

                // Thêm mảng cơ bản ( tài khoản chính) vào mảng merge
                if($val['check_main_bank']==1){
                    $array_data_merga[] = $array_bank;                
                }
                // Nếu có sub_account_ids thì xử lý từng giá trị
                if (!empty($val['sub_account_ids'])) {
                    foreach ($val['sub_account_ids'] as $item) {
                        // Tạo một mảng mới với sub_account_id cụ thể
                        $array_bank_item = array(
                            'bank_account_id' => $val['bank_account_id'],
                            'company_id' => $this->company_details->company_id,
                            'output_device_id' => $val['device_id'],
                            'sub_account_id' => $item,
                        );
                        // Thêm mảng chi tiết vào mảng tổng
                        $array_data_merga[] = $array_bank_item;
                    }
                }
                
            }
        }        

        foreach ($array_data_merga as &$item) {
            // Áp dụng XSS filter cho từng giá trị
            $item['bank_account_id'] = xss_clean($item['bank_account_id']);
            $item['output_device_id'] = xss_clean($item['output_device_id']);
            $item['company_id'] = xss_clean($item['company_id']);
            // sub_account_id có thể là null, kiểm tra trước khi lọc
            if (!is_null($item['sub_account_id'])) {
                $item['sub_account_id'] = xss_clean($item['sub_account_id']);
            }
        }
        unset($item);

        
        if(empty($array_data_merga)){
            return $this->responseCustom(404,"Ngân hàng cấu hình không khớp, hãy kiểm tra lại!");
        }


         $bankAccountIds = array_unique(array_column($array_data_merga, 'bank_account_id'));
         $bankSubAccountIds = array_unique(array_filter(array_column($array_data_merga, 'sub_account_id'))); // Lọc null
         
         if (!empty($bankAccountIds)) {
             // Lấy danh sách ID bank_account tồn tại
             $existingIds = array_column(
                 model(BankAccountModel::class)
                     ->select('id')
                     ->whereIn('id', $bankAccountIds)
                     ->where('company_id', $this->company_details->company_id)
                     ->findAll(),
                 'id'
             );
         
             // Tìm ID không tồn tại
             $missingBankIds = array_diff($bankAccountIds, $existingIds);
             
             if (!empty($missingBankIds)) {
                 log_message('error', 'Missing Bank Account IDs: ' . json_encode($missingBankIds));
                 return $this->responseCustom(404, "Tài khoản ngân hàng không hợp lệ: " . implode(', ', $missingBankIds));
             }
         }
         
         if (!empty($bankSubAccountIds)) {
             // Lấy danh sách ID sub_account tồn tại với điều kiện bank_account_id hợp lệ
             $existingSubIds = array_column(
                 model(BankSubAccountModel::class)
                     ->select('id')
                     ->whereIn('id', $bankSubAccountIds)
                     ->whereIn('bank_account_id', $bankAccountIds)
                     ->findAll(),
                 'id'
             );
         
             // Tìm ID không tồn tại
             $missingSubIds = array_diff($bankSubAccountIds, $existingSubIds);
             
             if (!empty($missingSubIds)) {
                 log_message('error', 'Missing Sub Account IDs: ' . json_encode($missingSubIds));
                 return $this->responseCustom(404, "Thông tin VA không hợp lệ: " . implode(', ', $missingSubIds));
             }
         }
         

        $resultDel = $BankIntegrationOutputDeviceModel->deleteData(['output_device_id'=>$device_id]);
        if(empty($resultDel['status'])){
            return $this->responseCustom(404,"Hệ thống lỗi, xóa dữ liệu!");
        }
       
        $resultInsertBatch = $BankIntegrationOutputDeviceModel->insertBatchData($array_data_merga);
        if(empty($resultInsertBatch['status'])){
            add_user_log(array('data_id'=>$device_id,'company_id' => $this->user_session['company_id'], 'data_type'=>'notification_output_device_update','description'=>'Cập nhật tích hợp ngân hàng vào thiết bị thất bại','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Failed'));
            return $this->responseCustom(404,"Hệ thống lỗi thêm dữ liệu, Hãy liên hệ kĩ thuật!");
        }
        add_user_log(array('data_id'=>$device_id,'company_id' => $this->user_session['company_id'], 'data_type'=>'notification_output_device_update','description'=>'Cập nhật tích hợp ngân hàng thiết bị thành công','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
        return $this->responseCustom(200,"Cập nhật thành công!",$array_data_merga);
    }
    
    // Aisino & Flametechvn
    public function addconfig($device_id){
        
        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])){
            show_404();
        }
        $data_input = $this->request->getPost();
        $data_input['id'] = $device_id;
        
        $BankIntegrationOutputDeviceModel = model(BankIntegrationOutputDeviceModel::class);
        $company_id = $this->company_details->company_id;
        $check_exits = $BankIntegrationOutputDeviceModel->where(['output_device_id'=>$device_id,'company_id'=>$company_id])->findAll();
        if(empty($check_exits)){
            return $this->responseCustom(422,"Không được phép cấu hình thiết bị!");
        }

        $data_add = array(
            "id" => $data_input['id'],
            "min_amount" => $data_input['min_amount'] ?? null,
            "max_amount" => $data_input['max_amount'] ?? null,
            "required_content" => $data_input['required_content'] ?? null,
        );

        $data_filtered = array_map(function($value) {
            return empty($value) ? null : xss_clean($value);
        }, $data_add); // Loại bỏ các giá trị rỗng

        

        $min_amount = $data_filtered['min_amount']??"";
        $validationRules = [
            "id" => [
                "label" => "ID thiết bị",
                "rules" => "required|is_not_unique[tb_autopay_output_device.id]",
                "errors" => [
                    "required" => "{field} Bắt buộc.",
                    "is_not_unique" => "{field} không tồn tại trong hệ thống.",
                ],
            ],
            "min_amount" => [
                "label" => "Số tiền tối thiểu",
                "rules" => "permit_empty|numeric|greater_than[1000]", // Chấp nhận rỗng
                "errors" => [
                    "numeric" => "{field} phải là một số.",
                    "greater_than" => "{field} phải lớn hơn 1.000."
                ],
            ],
            "max_amount" => [
                "label" => "Số tiền tối đa",
                "rules" => "permit_empty|numeric|greater_than[10000]|max_length[9]|greater_than_min_amount[$min_amount]", // Chấp nhận rỗng
                "errors" => [
                    "numeric" => "{field} phải là một số.",
                    "greater_than" => "{field} phải lớn hơn 10.000.", // Sửa thành > 10.000
                    "max_length" => "{field} không được quá 11 ký tự.",
                    "greater_than_min_amount" => "Giá trị min không được lớn hơn {field}" // Có thể cải thiện thông báo này
                ],
            ],

            "required_content" => [
                "label" => "Nội dung bắt buộc",
                "rules" => "permit_empty|max_length[55]", // Chấp nhận rỗng
                "errors" => [
                    "max_length" => "{field} không được quá 55 ký tự."
                ],
            ],
        ];
        
        
         // Load validation service
         $validation = service("validation");

         // Set validation rules
         $validation->setRules($validationRules);
 
         // Check data
         if (!$validation->run($data_filtered)) {
 
             $errors = $validation->getErrors();
 
             return $this->responseCustom(
                 422,
                 "Lỗi dữ liệu!",
                 $errors
             );
         }

         $OutputDeviceModel = model(OutputDeviceModel::class);
         $result = $OutputDeviceModel->set($data_filtered)->where(['id'=>$data_filtered['id']])->update();
         if(empty($result)){
            add_user_log(array('data_id'=>$device_id,'company_id' => $this->user_session['company_id'], 'data_type'=>'notification_output_device_update','description'=>'Cập nhật cấu hình nâng cao thiết bị thất bại','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Failed'));
            return $this->responseCustom(500,"Lỗi hệ thống cập nhật cấu hình!");
         }
         add_user_log(array('data_id'=>$device_id,'company_id' => $this->user_session['company_id'], 'data_type'=>'notification_output_device_update','description'=>'Cập nhật cấu hình nâng cao thiết bị thành công','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
        return $this->responseCustom(200,"Cập nhật thành công!");
    }

    // Aisino
    public function infointegration(){

        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])){
            show_404();
        }
        $device_id = $this->request->getGet("device_id")??"";
        if(empty($device_id)){
            return $this->responseCustom(404,"Không tìm thấy thông tin thiết bị!");
        }
        $BankIntegrationOutputDeviceModel = model(BankIntegrationOutputDeviceModel::class);
        $integrationData = $BankIntegrationOutputDeviceModel->select("
            tb_autopay_bank_integration_output_device.output_device_id,
            tb_autopay_bank_integration_output_device.company_id,
            tb_autopay_bank_integration_output_device.bank_account_id,
            tb_autopay_bank_integration_output_device.sub_account_id,
            ")
            ->where([
            'tb_autopay_bank_integration_output_device.company_id' => $this->company_details->company_id,
            'tb_autopay_bank_integration_output_device.output_device_id' => $device_id
            ])
            ->get()
            ->getResultArray();
        if(empty($integrationData)){
            return $this->responseCustom(404,"Không tìm thấy thông tin tích hợp!");
        }

        return $this->responseCustom(200,"Thông tin đã tích hợp!",$integrationData);

    }

 
    // Aisino
    public function pushmessagemissedbyid(){
        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])){
            show_404();
        }
        $data_input = $this->request->getPost();
        $OutputDeviceReplayMessageQueueModel = slavable_model(OutputDeviceReplayMessageQueueModel::class, 'Transactions');
        $OutputDeviceModel = model(OutputDeviceModel::class);

        $data_missed = $OutputDeviceReplayMessageQueueModel->where([
            'id' => $data_input['message_id'],
            'output_device_id' => $data_input['device_id'],
            'company_id' => $this->company_details->company_id,
            'status' => 'Pending',
        ])->get()->getRowArray();

        if(empty($data_missed)){
            return $this->responseCustom(422,"Không tìm thấy message, hãy tải lại trang!");
        }

        $data_device = $OutputDeviceModel->where(['id'=>$data_input['device_id']])->get()->getRowArray();
        if($data_device['vendor'] != "Aisino"){
            return $this->responseCustom(423,"Thiết bị này không hỗ trợ!");
        }

        if($data_device['online_status'] != "on"){
            return $this->responseCustom(423,"Thiết bị đang bị tắt, Hãy kiểm tra lại!");
        }

  
        $result_push = ExecuteOutputDevice::pushMessageByDeviceID($data_input['device_id'],$data_missed['transaction_id'], 1);

        if($result_push == "Not find data interation!" ){
            return $this->responseCustom(421,"Bạn đã xóa tích hợp ngân hàng trước đó!");
        }  

        if ($result_push == "Handle case success!") {
            // Cập nhật trạng thái thành công
            $OutputDeviceReplayMessageQueueModel->set(['status' => "Success"])->where(['id' => $data_missed['id']])->update();
        }else {
            log_message("debug", "Lỗi xử lí dữ liệu phát lại");
            return $this->responseCustom(502,"Lỗi hệ thống, Hãy liên hệ SePay!");
        }
        return $this->responseCustom(200,"OK");
    }

    // Aisino
    public function add_config_lcd_qr($device_id)
    {
        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])){
            show_404();
        }

        $data_validate = [
            'id' => $device_id,
            'qr_content' => $this->request->getPost('qr_content') ?? "",
            'qr_bank_name' => $this->request->getPost('qr_bank_name') ?? "",
            'qr_bank_account' => $this->request->getPost('qr_bank_account') ?? "",
            'qr_amount' => $this->request->getPost('qr_amount') ?? NULL,
            'qr_des' => $this->request->getPost('qr_des') ?? NULL,
        ];
        
        $validationRules = [
            "id" => [
                "label" => "ID thiết bị",
                "rules" => "required|is_not_unique[tb_autopay_output_device.id]",
                "errors" => [
                    "required" => "{field} Bắt buộc",
                    "is_not_unique" => "{field} không tồn tại trong hệ thống",
                ],
            ],
            "qr_content" => [
                "label" => "Nội dung QR",
                "rules" => "required|max_length[255]",
                "errors" => [
                    "required" => "{field} Bắt buộc",
                    "max_length" => "{field} không được quá 555 ký tự"
                ],
            ],
            "qr_bank_name" => [
                "label" => "Tên ngân hàng",
                "rules" => "required|max_length[55]",
                "errors" => [
                    "required" => "{field} Bắt buộc",
                    "max_length" => "{field} không được quá 55 ký tự"
                ],
            ],
            "qr_bank_account" => [
                "label" => "Số tài khoản",
                "rules" => "required|max_length[55]",
                "errors" => [
                    "required" => "{field} Bắt buộc",
                    "max_length" => "{field} không được quá 55 ký tự"
                ],
            ],
            "qr_amount" => [
                "label" => "Số tiền tối thiểu",
                "rules" => "permit_empty|numeric|greater_than[1000]",
                "errors" => [
                    "numeric" => "{field} phải là một số",
                    "greater_than" => "{field} phải lớn hơn 1.000"
                ],
            ],
            "qr_des" => [
                "label" => "Nội dung",
                "rules" => "permit_empty|max_length[255]",
                "errors" => [
                    "max_length" => "{field} không được quá 255 ký tự"
                ],
            ],
        ];
        
        // Load validation service
        $validation = service("validation");
        
        // Set validation rules
        $validation->setRules($validationRules);
        
        // Check data
        if (!$validation->run($data_validate)) {
            $errors = $validation->getErrors();

            $errorMessage = implode(', ', $errors); 
        
            return $this->responseCustom(
                422,
                $errorMessage, 
                $errors
            );
        }

        $BankIntegrationOutputDeviceModel = model(BankIntegrationOutputDeviceModel::class);
        $model = model(OutputDeviceModel::class);


        $result_integration_va = $BankIntegrationOutputDeviceModel
        ->join("tb_autopay_bank_sub_account", "tb_autopay_bank_sub_account.id=tb_autopay_bank_integration_output_device.sub_account_id")
        ->where([
        'tb_autopay_bank_sub_account.sub_account' => $this->request->getPost('qr_bank_account'),
        'tb_autopay_bank_integration_output_device.company_id' => $this->user_session['company_id'],
        ])
        ->get()->getRow();

        $result_integration_accbank = $BankIntegrationOutputDeviceModel
        ->join('tb_autopay_bank_account','tb_autopay_bank_account.id=tb_autopay_bank_integration_output_device.bank_account_id')
        ->where([
        'tb_autopay_bank_account.account_number' => $this->request->getPost('qr_bank_account'),
        'tb_autopay_bank_integration_output_device.sub_account_id' => null,
        'tb_autopay_bank_integration_output_device.company_id' => $this->user_session['company_id'],
        ])
        ->get()->getRow();
        
        if (strpos($data_validate['qr_des'], 'TKP') === false) {
            // Không chứa ký tự 'TKP'
            log_message("debug","Không chứa TKP");
        
            if (!is_object($result_integration_va) && !is_object($result_integration_accbank)) { 
                $model->set([
                    'qr_content' => null,
                    'qr_bank_name' => null,
                    'qr_bank_account' => null,
                    'qr_amount' => null,
                    'qr_des' => null
                ])->where(['id' => $device_id])->update();
        
                return $this->responseCustom(423, "Ngân hàng chưa được tích hợp, không thể tạo QR!");
            }
        } else {
            // Có chứa 'TKP', bỏ qua hành động này
            log_message("debug","Chứa TKP, bỏ qua");
        }
        
        
        // check device
        // Xử lý dữ liệu (Ví dụ: lưu vào cơ sở dữ liệu)
     
        $data_device = $model->where(['id'=>$device_id])->get()->getRowArray();
        if($data_device['model'] != "Q190"){
            return $this->responseCustom(422,"Thiết bị này không hỗ trợ màn LCD");
        }
       
        // Cập nhật hoặc thêm mới dữ liệu vào bảng
        $update = $model->update($device_id, $data_validate);

        if ($update) {
            $check = ExecuteOutputDevice::pushMessageQRMqtt($device_id);
            if(empty($check)){
                log_message("debug","Hệ thống đang offline");
            }
            add_user_log(array('data_id'=>$device_id,'company_id' => $this->user_session['company_id'], 'data_type'=>'notification_output_device_update','description'=>'Cập nhật tích hợp tùy chỉnh QR thiết bị thành công','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
            return $this->responseCustom(200,"Cập nhật cấu hình thành công!");
        } else {
            add_user_log(array('data_id'=>$device_id,'company_id' => $this->user_session['company_id'], 'data_type'=>'notification_output_device_update','description'=>'Cập nhật tích hợp tùy chỉnh QR thiết bị thất bại','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Failed'));
            return $this->responseCustom(500,"Lỗi hệ thống, hãy liên hệ kỹ thuật!");
        }
    }

    // custom ui new Flameteachvn

    public function step1(){


        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])){
            show_404();
        }

        $data = [
            'page_title' => 'Loa',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];
        $data['alert'] = session()->getFlashdata('alert');
        $session = service('session');
        $session->remove('output_device_integration');

        echo theme_view('templates/autopay/header',$data);
        echo view('outputdevice/integration/step1',$data);
        echo theme_view('templates/autopay/footer',$data);
    }

    public function ajax_step1(){


        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])){
            show_404();
        }
        
        $data_input = $this->request->getPost();
        $data_input['vendor'] = in_array($data_input['model'],["S003"]) ? "Flametechvn":"" ;
        
        $validation = service("validation");
        $validationRules = [
           
            "vendor" => [
                "label" => "Vendor",
                "rules" => "required|in_list[Flametechvn]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "in_list" => "{field} phải là 'Flametechvn'",
                ],
            ],
            "model" => [
                "label" => "Model",
                "rules" => "required|in_list[S003]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "valid_vendor" => "{field} phải là 'S003'.",
                ],
            ],
        ];
        // Set validation rules
        $validation->setRules($validationRules);
    
        // Check data
        if (!$validation->run($data_input)) {
            return $this->responseCustom(422,implode(". ", $validation->getErrors()));
        }

        $session = service('session');
        $session->set('output_device_integration',['vendor' => $data_input['vendor'],'model' => $data_input['model']]);
       

        return $this->responseCustom(200,"Thành công");
    }

    public function step2(){


        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])){
            show_404();
        }

        // QR code by ID bank account
        $data['role'] = $this->company_details->role;


        $data['bank_account_id'] = $this->request->getGet('account_id');

        $bankAccountModel = model(BankAccountModel::class);


        if(!is_numeric($data['bank_account_id']))
            $data['bank_account_id'] = FALSE;
        else {
            $bank_account_details = $bankAccountModel->where(['id' => $data['bank_account_id'], 'company_id' => $this->user_session['company_id']])->get()->getRow();
            if(!is_object($bank_account_details))
                show_404();

            if(!has_bank_permission($data['bank_account_id']))
               show_404();
        }


        $userPermissionBankModel = model(UserPermissionBankModel::class);
        $userPermissionBankSubModel = model(UserPermissionBankSubModel::class);

        $bankSubAccountModel = model(BankSubAccountModel::class);


        if(in_array($data['role'],['Admin','SuperAdmin']))
            $data['bankAccounts'] = $bankAccountModel->select("
                tb_autopay_bank_account.id ,tb_autopay_bank_account.bank_id,tb_autopay_bank_account.account_holder_name,
                tb_autopay_bank_account.bank_sms_connected,tb_autopay_bank_account.bank_api_connected,tb_autopay_bank_account.account_number,tb_autopay_bank_account.label,tb_autopay_bank.brand_name,tb_autopay_bank.short_name,tb_autopay_bank.brand_name,tb_autopay_bank.logo_path,tb_autopay_bank.icon_path")->join("tb_autopay_bank","tb_autopay_bank.id=tb_autopay_bank_account.bank_id")->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id']])->orderBy('tb_autopay_bank_account.id','asc')->get()->getResult();
        else
            $data['bankAccounts'] = $userPermissionBankModel->get_bank_accounts($this->user_details->id);

        $data['configBankSubAccount'] = get_configuration('BankSubAccount');

        if($data['configBankSubAccount'] == 'on' && $data['bank_account_id']) {
            if(in_array($data['role'],['Admin','SuperAdmin']))
                $data['bank_sub_accounts'] = $bankSubAccountModel->where(['bank_account_id' => $data['bank_account_id']])->orderBy('id','ASC')->get()->getResult();
            else
                $data['bank_sub_accounts'] = $userPermissionBankSubModel->where(['tb_autopay_user_permission_bank_sub.user_id' => $this->user_details->id, 'tb_autopay_bank_sub_account.bank_account_id' => $data['bank_account_id']])->join("tb_autopay_bank_sub_account","tb_autopay_bank_sub_account.id=tb_autopay_user_permission_bank_sub.sub_account_id")->orderBy('tb_autopay_bank_sub_account.id','ASC')->get()->getResult();
        } else {
            $data['bank_sub_accounts'] = [];
        }

        // convert to array
        $bank_accounts = array_map(function($obj) {
            return get_object_vars($obj);
        }, $data['bankAccounts']);

        if(empty($bank_accounts)){
            // return redirect()->to(base_url("/bankaccount/connect"));
        }

        $data = [
            'page_title' => 'Loa',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'shop_billing' => $this->shop_billing,
        ];

        $session = service('session');
        $session->remove('output_device_integration');
        $data_session = $session->get('output_device_integration') ?? []; // Đảm bảo luôn là mảng
        
        // Thêm giá trị mặc định nếu chưa có
        if (empty($data_session['vendor'])) {
            $data_session['vendor'] = "Flametechvn";
        }
        if (empty($data_session['model'])) {
            $data_session['model'] = "S003";
        }
        
        // Cập nhật lại session
        $session->set('output_device_integration', $data_session);

      

        $data_input = $data_session;
        
        $validation = service("validation");
        $validationRules = [
           
            "vendor" => [
                "label" => "Vendor",
                "rules" => "required|in_list[Flametechvn]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "in_list" => "{field} phải là 'Flametechvn'",
                ],
            ],
            "model" => [
                "label" => "Model",
                "rules" => "required|in_list[S003]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "valid_vendor" => "{field} phải là 'S003'.",
                ],
            ],
        ];
        // Set validation rules
        $validation->setRules($validationRules);
    
        // Check data
        if (!$validation->run($data_input)) {
           return redirect()->to(base_url("/outputdevice/step2"));
        }

        echo theme_view('templates/autopay/header',$data);
        echo view('outputdevice/integration/step2',$data);
        echo theme_view('templates/autopay/footer',$data);
    }

    public function request_otp(){

        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])){
            show_404();
        }

        $serial_number = $this->request->getPost("serial_number");
        $session = service('session');
        $data_session =  $session->get('output_device_integration');
        $data_session['serial_number'] = $serial_number;

        $data_input = $data_session;
        
        $validation = service("validation");
        $validationRules = [
           
            "vendor" => [
                "label" => "Vendor",
                "rules" => "required|in_list[Flametechvn]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "in_list" => "{field} phải là 'Flametechvn'",
                ],
            ],
            "model" => [
                "label" => "Model",
                "rules" => "required|in_list[S003]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "valid_vendor" => "{field} phải là 'S003'.",
                ],
            ],
            "serial_number" => [
                "label" => "Serial Number",
                "rules" => "required|is_not_unique[tb_autopay_output_device.serial_number]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "is_not_unique" => "{field} không tồn tại trong hệ thống",
                ],
            ],
        ];
        // Set validation rules
        $validation->setRules($validationRules);
    
        // Check data
        if (!$validation->run($data_input)) {
            return $this->response->setStatusCode(422)->setJSON(array(
                'status' => False,
                'message' => implode(". ", $validation->getErrors())
            ));
        }

        $check_device_active = model(OutputDeviceModel::class)
            ->where(['serial_number' => $data_input['serial_number']])
            ->where('company_id >', 0)
            ->get()
            ->getRowArray();

        if (!empty($check_device_active)) {
            return $this->responseCustom(423, 'Thiết bị đã được kích hoạt');
        }

        $FlametechvnClient =  new FlametechvnClient();
        $status_devices = $FlametechvnClient->checkDeviceInfo($data_input['serial_number']);       
        if($status_devices['status_code'] == 200 ){

            // check model
            if($status_devices['model'] != $data_input['model'] ){

                return $this->responseCustom(422,"Model thiết bị không khớp, hãy kiểm tra lại");
            }

            // check trạng thái online
            if(empty($status_devices['online'])){

                return $this->responseCustom(422,"Thiết bị đang tắt, hãy kiểm tra lại");
            }

            
            // send OTP
            $status_otp = $FlametechvnClient->sendOTP($data_input['serial_number']);
            if($status_otp['status_code'] == 400){

                return $this->responseCustom(422,"Hãy thử lấy OTP lại sau ít phút");
            }

            $outputDeviceData = [
                'model' => $data_input['model'],
                'serial_number' => $data_input['serial_number'],
                'external_device_id' => $status_otp['device_id'],
                'session_id' => $status_otp['session_id'],
                'vendor' => $data_input['vendor'],
            ];
            $session->set('output_device_integration', $outputDeviceData);


            return $this->responseCustom(200,"Gửi OTP thành công");
        }


        if($status_devices['status_code'] == 404){
            return $this->responseCustom(422,"Không tìm thấy thông tin thiết bị");
        }

        return $this->responseCustom(500,"Lỗi gửi OTP, hãy liên hệ kỹ thuật!");
    }


    public function ajax_step2(){

        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])){
            show_404();
        }
        
        $data = $this->request->getPost();
        $session = service('session');
        $data_session =  $session->get('output_device_integration');

        $data_input = array_merge($data_session, $data);
        
        $validationRules = [
            "vendor" => [
                "label" => "Vendor",
                "rules" => "required|in_list[Flametechvn]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "in_list" => "{field} phải là 'Flametechvn'",
                ],
            ],
            "name" => [
                "label" => "Tên",
                "rules" => "required",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                ],
            ],
            "otp" => [
                "label" => "OTP",
                "rules" => "required",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                ],
            ],
            "model" => [
                "label" => "Model",
                "rules" => "required|in_list[S003]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "valid_vendor" => "{field} phải là 'S003'.",
                ],
            ],
            "serial_number" => [
                "label" => "Serial Number",
                "rules" => "required|is_not_unique[tb_autopay_output_device.serial_number]",
                "errors" => [
                    "required" => "Hãy nhấn OTP để tiếp tục.",
                    "is_not_unique" => "{field} không tồn tại trong hệ thống",
                ],
            ],

        ];
    
        if (! $this->validateData($data_input, $validationRules)) {
            return $this->response
                ->setStatusCode(422)
                ->setJSON([
                    'status' => false,
                    'messages' => $this->validator->getErrors(),
                ]);
        } 

        $data_input['name'] = xss_clean($data['name']);

        $FlametechvnClient =  new FlametechvnClient();
        $status_verify = $FlametechvnClient->verifyOTP($data_input['serial_number'],$data_input['session_id'],$data_input['otp']);
        if($status_verify['status_code'] == 200 ){

            $session = service('session');
            $outputDeviceData = $session->get('output_device_integration');
            $outputDeviceData['online_status'] =  !empty($status_verify['online'])?'on':'off';
            $outputDeviceData['external_device_token'] =  $status_verify['device_token'];
            $outputDeviceData['name'] =  xss_clean($data_input['name']);

            $outputDevice = model(OutputDeviceModel::class)->where([
                'serial_number' => $data_input['serial_number'], 
                'model' => $data_input['model'], 
                'vendor' => $data_input['vendor']
            ])->first();
            
            if ($outputDevice) {
                $outputDeviceData['output_device_id'] = $outputDevice['id'];

                $hasAbbOutputDevicePrevious = model(OutputDeviceModel::class)
                    ->where('bank_id', 19)
                    ->where('company_id', $this->user_session['company_id'])
                    ->countAllResults();

                    if ($hasAbbOutputDevicePrevious > 0 && $outputDevice['bank_id'] != 19) {
                        return $this->response->setJSON([
                            'status' => false,
                            'message' => 'Tài khoản SePay của bạn chỉ có thể liên kết được với loa ngân hàng ABBANK',
                        ]);
                    }
            }

            $outputDeviceDecal = $outputDevice 
                ? model(OutputDeviceDecalModel::class)
                    ->select([
                        'tb_autopay_bank_account.id as bank_account_id',
                        'tb_autopay_bank_sub_account.id as bank_sub_account_id',
                        'tb_autopay_bank_account.bank_id as bank_id',
                        'tb_autopay_output_device_decal.account_number as account_number',
                        'tb_autopay_output_device_decal.virtual_account_number as virtual_account_number'
                    ])
                    ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.account_number = tb_autopay_output_device_decal.account_number and tb_autopay_bank_account.bank_id = tb_autopay_output_device_decal.bank_id', 'left')
                    ->join('tb_autopay_bank_sub_account', 'tb_autopay_bank_sub_account.sub_account = tb_autopay_output_device_decal.virtual_account_number and tb_autopay_bank_sub_account.bank_account_id = tb_autopay_bank_account.id', 'left')
                    ->where('tb_autopay_output_device_decal.output_device_id', $outputDevice['id'])->first() 
                : null;
                        
            if ($outputDeviceDecal) {
                $outputDeviceData['decal'] = [
                    'bank_account_id' => $outputDeviceDecal->bank_account_id,
                    'bank_sub_account_id' => $outputDeviceDecal->bank_sub_account_id,
                    'bank_id' => $outputDeviceDecal->bank_id,
                    'account_number' => $outputDeviceDecal->account_number,
                    'virtual_account_number' => $outputDeviceDecal->virtual_account_number
                ];
            }
            
            $session->set('output_device_integration', $outputDeviceData);

            return $this->responseCustom(200,"Xác thực thành công!");
            
           
        }

        if($status_verify['status_code'] == 404){
            return $this->responseCustom(422,"Không tìm thấy thông tin thiết bị");
        }

        if($status_verify['status_code'] == 423){
            return $this->responseCustom(422,"OTP đã quá hạn, Hãy thử lại!");
        }

        if($status_verify['status_code'] == 424){
            return $this->responseCustom(423,"OTP không khớp, Hãy thử lại!");
        }

        return $this->responseCustom(423,"Lỗi, Hãy liên hệ kỹ thuật để kiểm tra",$status_verify);

       
        
    }

    public function step3(){


        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])){
            show_404();
        }

        $data = [
            'page_title' => 'Loa',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        $session = service('session');
        $data_session =  $session->get('output_device_integration');

        $data_input = $data_session;
        
        $validation = service("validation");
        $validationRules = [
           
            "vendor" => [
                "label" => "Vendor",
                "rules" => "required|in_list[Flametechvn]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "in_list" => "{field} phải là 'Flametechvn'",
                ],
            ],
            "name" => [
                "label" => "Tên",
                "rules" => "required",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                ],
            ],
            
            "model" => [
                "label" => "Model",
                "rules" => "required|in_list[S003]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "valid_vendor" => "{field} phải là 'S003'.",
                ],
            ],
            "serial_number" => [
                "label" => "Serial Number",
                "rules" => "required|is_not_unique[tb_autopay_output_device.serial_number]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "is_not_unique" => "{field} không tồn tại trong hệ thống",
                ],
            ],

            "external_device_token" => [
                "label" => "external_device_token",
                "rules" => "required",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                ],
            ],
            "external_device_id" => [
                "label" => "external_device_id",
                "rules" => "required",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                ],
            ],

        ];
        // Set validation rules
        $validation->setRules($validationRules);
    
        // Check data
        if (!$validation->run($data_input)) {
           return redirect()->to(base_url("/outputdevice/step2"));
        }

        $bank_default = model(OutputDeviceModel::class)->select('tb_autopay_bank.brand_name')->join('tb_autopay_bank', 'tb_autopay_bank.id = tb_autopay_output_device.bank_id')->where(['tb_autopay_output_device.serial_number' => $data_input['serial_number'], 'tb_autopay_output_device.company_id' => null])->first();

        if(!empty($bank_default)){
            $img_bank_path = FCPATH . 'assets/images/outputdevice/step-' . $bank_default['brand_name'] . '.png';
            $img_bank = file_exists($img_bank_path) ? base_url('assets/images/outputdevice/step-' . $bank_default['brand_name'] . '.png') : base_url('assets/images/outputdevice/step-SePay.png');
        }else{
            $img_bank = base_url('assets/images/outputdevice/step-SePay.png');
        }
        if ($img_bank) {
            $data['img_bank_default'] = $img_bank;
        }

        $bankAccounts = model(BankAccountModel::class)->where('tb_autopay_bank_account.company_id', $this->user_session['company_id']);
        $outputDevice = model(OutputDeviceModel::class)
            ->where('serial_number', $data_input['serial_number'])
            ->first();

            if ($outputDevice['bank_id'] == 19) {
            $bankAccounts = $bankAccounts
                ->join('tb_autopay_abbank_linked_accounts', 'tb_autopay_abbank_linked_accounts.bank_account_id = tb_autopay_bank_account.id', 'left')
                ->where('tb_autopay_abbank_linked_accounts.bank_account_id IS NULL');
        }

        
        $data['data_device'] = $outputDevice;
        $data['hasBankAccounts'] = !! $bankAccounts->countAllResults();

        echo theme_view('templates/autopay/header',$data);
        echo view('outputdevice/integration/step3',$data);
        echo theme_view('templates/autopay/footer',$data);
    }
    
    public function ajax_step3() {
        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])) {
            show_404();
        }
    
        $data_input = $this->request->getPost();
        
        $data_filtered = [];
        foreach ($data_input as $key => $value) {
            if ($key === 'url_qr') {
                $data_filtered[$key] = empty($value) ? null : $value;
            } else {
                $data_filtered[$key] = empty($value) ? null : xss_clean($value);
            }
        }
        
        $session = service('session');
        $data_session = $session->get('output_device_integration') ?? [];
    
        // Load validation service
        $validation = service("validation");
        $min_amount = $data_filtered['min_amount']??"";
        // Định nghĩa rules
        $validationRules = [
            "bank_account_id" => [
                "label" => "Ngân hàng",
                "rules" => "required|numeric|greater_than[0]|is_not_unique[tb_autopay_bank_account.id]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "numeric" => "{field} phải là một số.",
                    "greater_than" => "{field} phải lớn hơn 0",
                    "is_not_unique" => "{field} không tồn tại trong hệ thống",
                ],
            ],

            "sub_account_id" => [
                "label" => "VA",
                "rules" => isset($data_session['decal']) && isset($data_input['sub_account_id']) && $data_input['sub_account_id'] === 0 && !is_speaker_billing_subscription() ? "" : "permit_empty|numeric|greater_than[0]|is_not_unique[tb_autopay_bank_sub_account.id]",
                "errors" => [
                    "numeric" => "{field} phải là một số",
                    "greater_than" => "{field} phải lớn hơn 0",
                    "is_not_unique" => "{field} không tồn tại trong hệ thống",
                ],
            ],
        
            "url_qr" => [
                "label" => "URL QR",
                "rules" => "required|valid_url",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "valid_url" => "{field} không hợp lệ. Vui lòng nhập đúng định dạng URL.",
                ],
            ],

            "min_amount" => [
                "label" => "Số tiền tối thiểu",
                "rules" => "permit_empty|numeric|greater_than[1000]", 
                "errors" => [
                    "numeric" => "{field} phải là một số.",
                    "greater_than" => "{field} phải lớn hơn 1.000"
                ],
            ],
            "max_amount" => [
                "label" => "Số tiền tối đa",
                "rules" => "permit_empty|numeric|greater_than[10000]|max_length[9]|greater_than_min_amount[$min_amount]", 
                "errors" => [
                    "numeric" => "{field} phải là một số.",
                    "greater_than" => "{field} phải lớn hơn 10.000",
                    "max_length" => "{field} không được quá 11 ký tự.",
                    "greater_than_min_amount" => "Giá trị $min_amount không được lớn hơn {field}" 
                ],
            ],

            "required_content" => [
                "label" => "Nội dung bắt buộc",
                "rules" => "permit_empty|max_length[55]", 
                "errors" => [
                    "max_length" => "{field} không được quá 55 ký tự."
                ],
            ],
        ];
        
    
        // Áp dụng rules và kiểm tra dữ liệu đã lọc
        $validation->setRules($validationRules);
    
        if (!$validation->run($data_filtered)) {  // ✅ Dùng $data_filtered thay vì $data_input
            return $this->responseCustom(422, implode(". ", $validation->getErrors()));
        }
        
        // check QR
        if (isset($data_filtered['sub_account_id']) && $data_filtered['sub_account_id']) {
            $check_account = model(BankSubAccountModel::class)
                ->select('tb_autopay_bank_sub_account.id as sub_account_id, tb_autopay_bank_account.id as bank_account_id')
                ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id = tb_autopay_bank_sub_account.bank_account_id')
                ->where([
                    'tb_autopay_bank_sub_account.id' => $data_filtered['sub_account_id'],
                    'tb_autopay_bank_sub_account.bank_account_id' => $data_filtered['bank_account_id'],
                    'tb_autopay_bank_account.company_id' => $this->user_session['company_id']
                ])
                ->get()
                ->getRowArray();
                
            if (empty($check_account)) {
                return $this->responseCustom(423, "Không tìm thấy ngân hàng hoặc VA");
            }
        } else {
            $data_filtered['sub_account_id'] = isset($data_filtered['sub_account_id']) ? $data_filtered['sub_account_id'] : null;
        }
    
        // Gán dữ liệu vào session, dùng `??` để tránh lỗi `Undefined index`
        $data_session['bank_account_id'] = $data_filtered['bank_account_id'] ?? null;
        $data_session['sub_account_id'] = $data_filtered['sub_account_id'] ?? null;
        $data_session['min_amount'] = $data_filtered['min_amount'] ?? null;
        $data_session['max_amount'] = $data_filtered['max_amount'] ?? null;
        $data_session['required_content'] = $data_filtered['required_content'] ?? null;
        $data_session['url_qr'] = $data_filtered['url_qr'] ?? null;
    
        $session->set('output_device_integration', $data_session);

        // insert Data

        $OutputDeviceModel = model(OutputDeviceModel::class);
        $check_device = $OutputDeviceModel->select()
        ->where([
            'serial_number' => $data_session['serial_number'],
            'model' => $data_session['model'],
            'active' => 0
        ])
        ->groupStart()
            ->where('company_id IS NULL', null, false) // Kiểm tra NULL
            ->orWhere('company_id', '') // Kiểm tra rỗng
        ->groupEnd()
        ->get()
        ->getRowArray();
    

        if(empty($check_device)){
            return $this->responseCustom(423,"Thiết bị đã được tích hợp trước đó, Hãy liên hệ kỹ thuật!");
        }

        $responseData = [];
        
        if (isset($data_session['output_device_id']) && isset($data_session['decal']) && isset($data_input['sub_account_id']) && $data_input['sub_account_id'] == 0) {
            $outputDeviceDecalModel = model(OutputDeviceDecalModel::class);
            
            $outputDeviceDecal = $outputDeviceDecalModel->where('output_device_id', $data_session['output_device_id'])->first();
            
            if (!$outputDeviceDecal) {
                return $this->responseCustom(500,'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ');
            }
            
            $data_session['sub_account_id'] = $this->createVaFromOutputDeviceDecal(
                $outputDeviceDecal->bank_id, 
                $outputDeviceDecal->output_device_id, 
                $data_filtered['bank_account_id'], 
                $outputDeviceDecal->virtual_account_number
            );
            
            $session->set('output_device_integration', $data_session);
        }

        if (isset($data_session['decal']) && ! empty($data_session['decal']['virtual_account_number'])) {
            $responseData['redirect_url'] = base_url("/outputdevice/detail/" . $check_device['id']);
            $session->remove(['output_device_integration']);
        }

        $data_insert_device = [
            'serial_number'      => $data_session['serial_number'],
            'model'              => $data_session['model'],
            'vendor'              => $data_session['vendor'],
            'online_status'      => $data_session['online_status'],
            'min_amount'         => $data_session['min_amount'] ?? null,
            'max_amount'         => $data_session['max_amount'] ?? null,
            'required_content'   => $data_session['required_content'] ?? null,
            'external_device_token'   => $data_session['external_device_token'] ?? null,
            'external_device_id'   => $data_session['external_device_id'] ?? null,
            'name'         => $data_session['name'] ?? null,
            'company_id'         => $this->user_session['company_id'] ?? null,
            'active'        => 1, 
            'active_date'        => empty($check_device['active_date']) ? date('Y-m-d H:i:s') : $check_device['active_date'],
        ];

        $status_insert_device = $OutputDeviceModel->set($data_insert_device)->update($check_device['id']);
        if(empty($status_insert_device)){
            return $this->responseCustom(423,"Lỗi hệ thống tích hợp thiết bị, Hãy liên hệ kỹ thuật!");
        }
        $data_insert_integration = array(
            'output_device_id'      => $check_device['id'],
            'bank_account_id'      => $data_session['bank_account_id'] ?? null,
            'sub_account_id'              => $data_session['sub_account_id'],
            'company_id'         => $this->user_session['company_id'] ?? null,
        );

        $BankIntegrationOutputDeviceModel = model(BankIntegrationOutputDeviceModel::class);
        $BankIntegrationOutputDeviceModel->where(['output_device_id' => $check_device['id']])->delete();
        $status_insert_integration = $BankIntegrationOutputDeviceModel->insert($data_insert_integration);
        if(empty($status_insert_integration)){
            add_user_log(array('data_id'=>$check_device['id'],'company_id' => $this->user_session['company_id'], 'data_type'=>'notification_output_device_update','description'=>'Thêm tích hợp loa thất bại','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Failed'));
            return $this->responseCustom(423,"Lỗi hệ thống thêm tích hợp ngân hàng, Hãy liên hệ kỹ thuật!");
        }
        add_user_log(array('data_id'=>$check_device['id'],'company_id' => $this->user_session['company_id'], 'data_type'=>'notification_output_device_update','description'=>'Thêm tích hợp loa thành công','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
        
        return $this->responseCustom(200, "OK", $responseData); // ✅ Dùng $data_filtered
    }
    
    public function step4(){
        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])){
            show_404();
        }

        $data = [
            'page_title' => 'Loa',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

       
        $session = service('session');
        $data_session =  $session->get('output_device_integration');
        
        if (!$data_session){
            return redirect()->to(base_url("/outputdevice"));
        }

        $data_input = $data_session;
        
        $validation = service("validation");
        $validationRules = [
           
            "vendor" => [
                "label" => "Vendor",
                "rules" => "required|in_list[Flametechvn]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "in_list" => "{field} phải là 'Flametechvn'",
                ],
            ],
            "name" => [
                "label" => "Tên",
                "rules" => "required",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                ],
            ],
            
            "model" => [
                "label" => "Model",
                "rules" => "required|in_list[S003]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "valid_vendor" => "{field} phải là 'S003'.",
                ],
            ],
            "serial_number" => [
                "label" => "Serial Number",
                "rules" => "required|is_not_unique[tb_autopay_output_device.serial_number]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "is_not_unique" => "{field} không tồn tại trong hệ thống",
                ],
            ],

            "external_device_token" => [
                "label" => "external_device_token",
                "rules" => "required",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                ],
            ],
            "external_device_id" => [
                "label" => "external_device_id",
                "rules" => "required",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                ],
            ],

            "bank_account_id" => [
                "label" => "Ngân hàng",
                "rules" => "required",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                ],
            ],

            "sub_account_id" => [
                "label" => "VA",
                "rules" => "permit_empty",
                "errors" => [],
            ],
            "max_amount" => [
                "label" => "Max Amount",
                "rules" => "permit_empty",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                ],
            ],
            "min_amount" => [
                "label" => "Max Amount",
                "rules" => "permit_empty",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                ],
            ],
            "required_content" => [
                "label" => "required Content",
                "rules" => "permit_empty",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                ],
            ],

        ];
        // Set validation rules
        $validation->setRules($validationRules);
    
        // Check data
        if (!$validation->run($data_input)) {
           return redirect()->to(base_url("/outputdevice/step2"));
        }

        $bank_default = model(BankAccountModel::class)->select('tb_autopay_bank.brand_name')->join('tb_autopay_bank', 'tb_autopay_bank.id = tb_autopay_bank_account.bank_id', 'left')->where(['tb_autopay_bank_account.id' => $data_session['bank_account_id'], 'tb_autopay_bank_account.company_id' => $this->company_details->company_id])->first();

        if($bank_default){
            $img_bank_path = FCPATH . 'assets/images/outputdevice/step-' . $bank_default->brand_name . '.png';
            $img_bank = file_exists($img_bank_path) ? base_url('assets/images/outputdevice/step-' . $bank_default->brand_name . '.png') : base_url('assets/images/outputdevice/step-SePay.png');
        }else{
            $img_bank = base_url('assets/images/outputdevice/step-SePay.png');
        }
        
        if ($img_bank) {
            $data['img_bank_default'] = $img_bank;
        }
        $data['brand_name'] = !empty($bank_default->brand_name) ? $bank_default->brand_name : 'SePay';
        $data['qrcode_url'] = $this->generatePayQrcodeUrl($data_session['bank_account_id'], $data_session['sub_account_id']);

        $data['output_device_id'] = $data_session['output_device_id'];
        
        $session->remove(['output_device_integration']);
        
        echo theme_view('templates/autopay/header',$data);
        echo view('outputdevice/integration/step4',$data);
        echo theme_view('templates/autopay/footer',$data);
    }

    public function detail($id=""){

        if(empty($id)){
            show_404();
        }

        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])){
            show_404();
        }

        if (! $this->validate([
            'period' => 'permit_empty|string|in_list[today,week,month,year]',
        ])) {
            return redirect()->back();
        }

        $startDate = null;
        $endDate = null;
        $period = xss_clean($this->request->getGet('period') ?? 'today');

        if ($period == 'today') {
            $startDate = date('Y-m-d 00:00:00');
            $endDate = date('Y-m-d 23:59:59');
        } else if($period == 'week') {
            $startDate = date('Y-m-d 00:00:00', strtotime('monday this week'));
            $endDate = date('Y-m-d 23:59:59');
        }else if($period == 'month') {
            $startDate = date("Y-m-01 00:00:00");
            $endDate = date('Y-m-d 23:59:59');
        } else if($period == 'year') {
            $startDate = date("Y-01-01 00:00:00");
            $endDate = date('Y-m-d 23:59:59');
        }


        $check_exits = model(OutputDeviceModel::class)
            ->select('tb_autopay_output_device.*, tb_autopay_bank.brand_name')
            ->join('tb_autopay_bank', 'tb_autopay_output_device.bank_id = tb_autopay_bank.id', 'left')
            ->where(['tb_autopay_output_device.id' => $id, 'tb_autopay_output_device.company_id' => $this->company_details->company_id])
            ->get()
            ->getRowArray();
        if(empty($check_exits)){
            show_404();
        }
        
        if (is_shop_billing_subscription() && !$check_exits['shop_id']) {
            set_alert('error', 'Vui lòng chọn một cửa hàng cho loa thanh toán trước');
            return redirect()->to(base_url('outputdevice/store/' . $check_exits['id']));
        }

        $data_integration_bank = model(BankIntegrationOutputDeviceModel::class)
        ->where([
            'output_device_id' => $id,
            'company_id' => $this->user_session['company_id']
        ])
        ->countAllResults(); 
    
        $model = slavable_model(OutputDeviceReplayMessageQueueModel::class, 'Transactions');
        $TransactionModel = slavable_model(TransactionsModel::class, 'Transactions');

        // Lấy số lượng trạng thái Success và Pending và Failed
        $query = $model->select('status, COUNT(*) as count')
            ->where('output_device_id', $id)
            ->where('company_id', $this->company_details->company_id)
            ->where('created_at >=', $startDate)
            ->where('created_at <=', $endDate)
            ->groupBy('status')
            ->findAll();

        $counts = ['Success' => 0, 'Pending' => 0]; // Mặc định 0 nếu không có kết quả

        foreach ($query as $row) {
            $counts[$row['status']] = $row['count'];
        }

        $data_retry_success = $counts['Success'];
        $data_retry_pending = $counts['Pending'];

        // Lấy danh sách transaction_id của Success, Pending, Failed
        $query_transactions = $model->select('transaction_id, status')
        ->where('output_device_id', $id)
        ->where('company_id', $this->company_details->company_id)
        ->where('created_at >=', $startDate)
        ->where('created_at <=', $endDate)
        ->findAll();

        $total_transaction = 0;

        // Kiểm tra xem danh sách transaction có dữ liệu không
        $transaction_ids = array_column($query_transactions, 'transaction_id');
        if (!empty($transaction_ids)) {
            $total_transaction = $TransactionModel->select('SUM(amount_in) as total')
                ->whereIn('id', $transaction_ids)
                ->where('transaction_date >=', $startDate)
                ->where('transaction_date <=', $endDate)
                ->get()
                ->getRowArray();
        }

        // Lấy tổng amount
        $total_amount = $total_transaction['total'] ?? 0;


        // Mảng chứa danh sách transaction_id theo trạng thái
        $transactions = [
            'Success' => [],
            'Pending' => [],
            'Failed' => [],
        ];

        // Lặp qua kết quả để nhóm transaction_id theo status
        foreach ($query_transactions as $row) {
            $transactions[$row['status']][] = $row['transaction_id'];
        }

        $integrationData = model(BankIntegrationOutputDeviceModel::class)
        ->select('
            tb_autopay_bank_integration_output_device.id as integration_id,
            tb_autopay_bank_integration_output_device.output_device_id,
            tb_autopay_bank_sub_account.sub_account,
            tb_autopay_bank_sub_account.acc_type, 
            tb_autopay_bank_sub_account.label, 
            tb_autopay_bank_sub_account.id as sub_account_id, 
            tb_autopay_bank_sub_account.created_at,
            tb_autopay_bank_account.account_holder_name, 
            tb_autopay_bank_account.account_number, 
            tb_autopay_bank_account.bank_sms_connected, 
            tb_autopay_bank_account.bank_api_connected, 
            tb_autopay_bank.brand_name, 
            tb_autopay_bank.short_name, 
            tb_autopay_bank.brand_name, 
            tb_autopay_bank.logo_path, 
            tb_autopay_bank.id as bank_id, 
            tb_autopay_bank.icon_path')
        ->join('tb_autopay_bank_sub_account', 'tb_autopay_bank_sub_account.id = tb_autopay_bank_integration_output_device.sub_account_id', 'left')
        ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id = tb_autopay_bank_integration_output_device.bank_account_id')
        ->join('tb_autopay_bank', 'tb_autopay_bank.id = tb_autopay_bank_account.bank_id')
        ->join('tb_autopay_output_device', 'tb_autopay_output_device.id = tb_autopay_bank_integration_output_device.output_device_id')
        ->where(['tb_autopay_bank_integration_output_device.output_device_id' => $id, 'tb_autopay_bank_integration_output_device.company_id' => $this->company_details->company_id])
        ->orderBy('tb_autopay_bank_integration_output_device.created_at', 'DESC')
        ->get()
        ->getResultArray();

        if (!empty($integrationData)) {
            $qr_url = "https://qr.sepay.vn/img?template=" . ($integrationData[0]['bank_id'] == 19 ? '' : 'qronly');
            $bank = $integrationData[0]['brand_name'];
            $des = "";
            $acc = $integrationData[0]['account_number'];
            if (!empty($integrationData[0]['sub_account_id'])) {

                // VA Real
                if ($integrationData[0]['acc_type'] == 'Real') {
                    $acc = $integrationData[0]['sub_account'];
                }
                // swap brand name
                if ($integrationData[0]['brand_name'] == "LPBank") {
                    $bank = "LienVietPostBank";
                }
                if ($integrationData[0]['brand_name'] == "BVBank") {
                    $bank = "VietCapitalBank";
                }

                // VA Virtual API
                if ($integrationData[0]['acc_type'] == 'Virtual' && $integrationData[0]['bank_api_connected'] == 1) {
                    $acc = $integrationData[0]['account_number'];
                    $des = "TKP" . $integrationData[0]['sub_account'];
                }

                // VA Virtual SMS
                if ($integrationData[0]['acc_type'] == 'Virtual' && $integrationData[0]['bank_sms_connected'] == 1) {
                    $acc = $integrationData[0]['account_number'];
                    $des = "TKP" . $integrationData[0]['sub_account'];
                }

                // VietinBank
                if ($integrationData[0]['brand_name'] == 'VietinBank') {
                    $acc = $integrationData[0]['account_number'];
                    $des = ($integrationData[0]['bank_api_connected'] == 1) ? "SEVQR TKP".$integrationData[0]['sub_account'] : "TKP" .$integrationData[0]['sub_account'];
                }

                if ($integrationData[0]['brand_name'] == 'ABBANK') {
                    $acc = $integrationData[0]['account_number'];
                    $des = ($integrationData[0]['bank_api_connected'] == 1) ? "LocVang TKP".$integrationData[0]['sub_account'] : "TKP" .$integrationData[0]['sub_account'];
                }

                $qr_url = $qr_url . "&bank=" . $bank . "&acc=" . $acc . "&des=" . $des;
            } else {
                $acc = $integrationData[0]['account_number'];
                if ($integrationData[0]['brand_name'] == 'VietinBank') {
                    $des = ($integrationData[0]['bank_api_connected'] == 1) ? "SEVQR " : "";
                }

                if ($integrationData[0]['brand_name'] == 'ABBANK') {
                    $des = ($integrationData[0]['bank_api_connected'] == 1) ? "LocVang " : "";
                }

                $qr_url = $qr_url . "&bank=" . $bank . "&acc=" . $acc . "&des=" . $des;
            }
        }

        $data = [
            'data_retry_success' => $data_retry_success,
            'data_retry_pending' => $data_retry_pending,
            'data_device' => $check_exits,
            'total_amount' => number_format((int) $total_amount) . ' đ',
            'data_integration_bank' => $data_integration_bank,
            'list_transaction_success' => $transactions['Success'], // Danh sách transaction_id của Success
            'list_transaction_spending' => $transactions['Pending'],   // Danh sách transaction_id của Failed
            'list_transaction_failed' => $transactions['Failed'],   // Danh sách transaction_id của Failed
            'page_title' => 'Loa',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'qr_url' => $qr_url ?? null,
            'new_va' => $integrationData[0] ?? null,
            'period' => $period,
        ];

        if (is_numeric($data['data_device']['shop_id'])) {
            $storeFeature = new StoreFeature($data['data_device']['shop_id']);
            $storeFeature->withCompanyContext($this->user_session['company_id']);
            
            $data['store'] = $storeFeature->store;
        }

        echo theme_view('templates/autopay/header',$data);
        echo view('outputdevice/detail',$data);
        echo theme_view('templates/autopay/footer',$data);
        
    }

    public function ajax_change_name(){

        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])){
            show_404();
        }

        $data = $this->request->getPost();

        $data_input = array(
            'id' => xss_clean($data['id']),
            'name' => xss_clean($data['name']),
        );
        
        $service = service('validation');
        $service->setRules([
            'id' => 'required',
            'name' => 'required|max_length[255]',
        ]);

        if(!$service->run($data_input)){
            return $this->responseCustom(422,implode(". ", $service->getErrors()));
        }

        $check_exits = model(OutputDeviceModel::class)->where(['id' => $data_input['id'],'company_id' => $this->company_details->company_id])->get()->getRowArray();
        if(empty($check_exits)){
            return $this->responseCustom(423,"Không tìm thấy thiết bị");
        }

        $OutputDeviceModel = model(OutputDeviceModel::class);
        $status_update = $OutputDeviceModel->set($data_input)->update($data_input['id']);
        if($status_update){
             add_user_log(array('data_id'=>$data_input['id'],'company_id' => $this->user_session['company_id'], 'data_type'=>'notification_output_device_update','description'=>'Sửa tên tích hợp loa thành công','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
            return $this->responseCustom(200,"Cập nhật tên thành công");
        }
        add_user_log(array('data_id'=>$data_input['id'],'company_id' => $this->user_session['company_id'], 'data_type'=>'notification_output_device_update','description'=>'Sửa tên tích hợp loa thất bại','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Failed'));
    
        return $this->responseCustom(422,"Cập nhật tên thất bại");
    }

    public function ajax_delete_device(){
        
        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])){
            show_404();
        }

        $data = $this->request->getPost();

        $data_input = array(
            'id' => xss_clean($data['id']),
        );
        
        $service = service('validation');
        $service->setRules([
            "id" => [
                "label" => "ID",
                "rules" => "required|is_not_unique[tb_autopay_output_device.id]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "is_not_unique" => "{field} không tồn tại trong hệ thống",
                ],
            ],
        ]);

        if(!$service->run($data_input)){
            return $this->responseCustom(422,implode(". ", $service->getErrors()));
        }

        $check_exits = model(OutputDeviceModel::class)->where(['id' => $data_input['id'],'active' => 1,'company_id' => $this->company_details->company_id])->get()->getRowArray();
        if(empty($check_exits)){
            return $this->responseCustom(423,"Không tìm thấy thiết bị");
        }
        if($check_exits['vendor'] != 'Flametechvn'){
            return $this->responseCustom(423,"Không thể xóa thiết bị này");
        }

        if ($check_exits['bank_id'] == 19) {
            $linkedDevices = model(AbbankLinkedAccountModel::class)
                ->where('output_device_id', $data_input['id'])
                ->countAllResults();

            if ($linkedDevices > 0) {
                return $this->responseCustom(423, 'Vui lòng hủy liên kết tài khoản ngân hàng trước khi xóa thiết bị');
            }
        }

        $FlametechvnClient =  new FlametechvnClient();
        $status_delete = $FlametechvnClient->removeDevice($check_exits['external_device_token']);
        // Xử lý lỗi từ API
        if ($status_delete['status_code'] != 200) {
            
            // Trả về thành công nếu xóa thành công
            log_message('error', 'Error API Remove Device - ' . json_encode($status_delete));
            return $this->responseCustom(423, "Lỗi gỡ thiết bị, Hãy liên hệ kỹ thuật để kiểm tra");
        }
        $OutputDeviceModel = model(OutputDeviceModel::class);
        $BankIntegrationOutputDeviceModel = model(BankIntegrationOutputDeviceModel::class);
        $status_delete = $BankIntegrationOutputDeviceModel->where(['output_device_id' => $data_input['id'],'company_id' => $this->user_session['company_id']])->delete();
        if (!empty($data_input['id']) && !empty($this->user_session['company_id'])){
            model(OutputDeviceReplayMessageQueueModel::class)->where(['output_device_id' => $data_input['id'],'company_id' => $this->user_session['company_id']])->delete();
        }
        if ($status_delete) {
            $OutputDeviceModel->set([
            'external_device_id' => null,
            'external_device_token' => null,
            'name' => null,
            'min_amount' => null,
            'max_amount' => null,
            'required_content' => null,
            'company_id' => null,
            'active' => 0,
            'shop_id' => null
            ])->update(['id' => $data_input['id']]);

            session()->setFlashdata('alert', [
                'type' => 'success', 
                'message' => 'Gỡ thiết bị thành công',
            ]);
            add_user_log(array('data_id'=>$data_input['id'],'company_id' => $this->user_session['company_id'], 'data_type'=>'notification_output_device_update','description'=>'Gỡ tích hợp loa thành công','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));

            return $this->responseCustom(200, "Gỡ thiết bị thành công");
        }
        add_user_log(array('data_id'=>$data_input['id'],'company_id' => $this->user_session['company_id'], 'data_type'=>'notification_output_device_update','description'=>'Gỡ tích hợp loa thất bại','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Failed'));

        return $this->responseCustom(422,"Gỡ thiết bị thất bại");
    }

    public function qrcode($id=""){
        if(empty($id)){
            show_404();
        }

        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])){
            show_404();
        }

        $check_exits = model(OutputDeviceModel::class)
            ->select('tb_autopay_output_device.*, tb_autopay_bank.brand_name')
            ->join('tb_autopay_bank', 'tb_autopay_output_device.bank_id = tb_autopay_bank.id', 'left')
            ->where(['tb_autopay_output_device.id' => $id, 'tb_autopay_output_device.company_id' => $this->company_details->company_id])
            ->get()
            ->getRowArray();

        if(empty($check_exits)){
            show_404();
        }
        
        if (is_shop_billing_subscription() && !$check_exits['shop_id']) {
            set_alert('error', 'Vui lòng chọn một cửa hàng cho loa thanh toán trước');
            return redirect()->to(base_url('outputdevice/store/' . $check_exits['id']));
        }

        $integrationData = model(BankIntegrationOutputDeviceModel::class)
            ->select('
             tb_autopay_bank_integration_output_device.id as integration_id,
             tb_autopay_bank_integration_output_device.output_device_id,
             tb_autopay_bank_sub_account.sub_account,
             tb_autopay_bank_sub_account.acc_type, 
             tb_autopay_bank_sub_account.label, 
             tb_autopay_bank_sub_account.id as sub_account_id, 
             
             tb_autopay_bank_account.account_holder_name, 
             tb_autopay_bank_account.account_number, 
             tb_autopay_bank_account.bank_sms_connected, 
             tb_autopay_bank_account.bank_api_connected, 

             tb_autopay_bank.brand_name, 
             tb_autopay_bank.short_name, 
             tb_autopay_bank.brand_name, 
             tb_autopay_bank.logo_path, 
             tb_autopay_bank.id as bank_id, 
             tb_autopay_bank.icon_path')

            ->join('tb_autopay_bank_sub_account', 'tb_autopay_bank_sub_account.id = tb_autopay_bank_integration_output_device.sub_account_id', 'left')
            ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id = tb_autopay_bank_integration_output_device.bank_account_id')
            ->join('tb_autopay_bank', 'tb_autopay_bank.id = tb_autopay_bank_account.bank_id')
            ->join('tb_autopay_output_device', 'tb_autopay_output_device.id = tb_autopay_bank_integration_output_device.output_device_id')
            ->where(['tb_autopay_bank_integration_output_device.output_device_id' => $id, 'tb_autopay_bank_integration_output_device.company_id' => $this->company_details->company_id])
            ->groupBy(['tb_autopay_bank_integration_output_device.sub_account_id', 'tb_autopay_bank_integration_output_device.bank_account_id'])
            ->orderBy('tb_autopay_bank_integration_output_device.created_at', 'DESC')
            ->findAll();

        // render QR url
        
        if (!empty($integrationData)) {
            foreach ($integrationData as &$value) {
                $qr_url = "https://qr.sepay.vn/img?template=" . ($value['bank_id'] == 19 ? '' : 'qronly');
                $bank = $value['brand_name'];
                $des = "";
                $acc = $value['account_number'];
                if (!empty($value['sub_account_id'])) {

                     // VA Real
                    if ($value['acc_type'] == 'Real') {
                        $acc = $value['sub_account'];
                    }
                    // swap brand name
                    if ($value['brand_name'] == "LPBank") {
                        $bank = "LienVietPostBank";
                    }
                    if ($value['brand_name'] == "BVBank") {
                        $bank = "VietCapitalBank";
                    }

                    // VA Virtual API
                    if ($value['acc_type'] == 'Virtual' && $value['bank_api_connected'] == 1) {
                        $acc = $value['account_number'];
                        $des = "TKP" . $value['sub_account'];
                    }

                    // VA Virtual SMS
                    if ($value['acc_type'] == 'Virtual' && $value['bank_sms_connected'] == 1) {
                        $acc = $value['account_number'];
                        $des = "TKP" . $value['sub_account'];
                    }
                   
                    // VietinBank
                    if ($value['brand_name'] == 'VietinBank') {
                        $acc = $value['account_number'];
                        $des = ($value['bank_api_connected'] == 1) ? "SEVQR TKP".$value['sub_account'] : "TKP" .$value['sub_account'];
                    }

                    if ($value['brand_name'] == 'ABBANK') {
                        $acc = $value['account_number'];
                        $des = ($value['bank_api_connected'] == 1) ? "LocVang TKP".$value['sub_account'] : "TKP" .$value['sub_account'];
                    }

                    $value['qr_url'] = $qr_url . "&bank=" . $bank . "&acc=" . $acc . "&des=" . $des;
                } else {
                    $acc = $value['account_number'];
                    if ($value['brand_name'] == 'VietinBank') {
                        $des = ($value['bank_api_connected'] == 1) ? "SEVQR " : "";
                    }
                    if ($value['brand_name'] == 'ABBANK') {
                        $des = ($value['bank_api_connected'] == 1) ? "LocVang " : "";
                    }

                    $value['qr_url'] = $qr_url . "&bank=" . $bank . "&acc=" . $acc . "&des=" . $des;
                }
            }
            unset($value);
        }

        $data = [
            
            'data_device' => $check_exits,
            'data_integration_bank' => count($integrationData),
            'data_detail_integration_bank' => $integrationData,
            'page_title' => 'Loa',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'new_va' => $integrationData[0] ?? null,
        ];

        if (is_numeric($data['data_device']['shop_id'])) {
            $storeFeature = new StoreFeature($data['data_device']['shop_id']);
            $storeFeature->withCompanyContext($this->user_session['company_id']);
            
            $data['store'] = $storeFeature->store;
        }
        
        $data['alert'] = session()->getFlashdata('alert');

        echo theme_view('templates/autopay/header',$data);
        echo view('outputdevice/qrcode/index',$data);
        echo theme_view('templates/autopay/footer',$data);
       
    }

    public function config($id=""){
        if(empty($id)){
            show_404();
        }

        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])){
            show_404();
        }

        $check_exits = model(OutputDeviceModel::class)
            ->select('tb_autopay_output_device.*, tb_autopay_bank.brand_name')
            ->join('tb_autopay_bank', 'tb_autopay_output_device.bank_id = tb_autopay_bank.id', 'left')
            ->where(['tb_autopay_output_device.id' => $id, 'tb_autopay_output_device.company_id' => $this->company_details->company_id])
            ->get()
            ->getRowArray();
        if(empty($check_exits)){
            show_404();
        }

        $data_integration_bank = model(BankIntegrationOutputDeviceModel::class)
        ->where([
            'output_device_id' => $id,
            'company_id' => $this->user_session['company_id']
        ])
        ->countAllResults();
        
        $integrationData = model(BankIntegrationOutputDeviceModel::class)
            ->select('
             tb_autopay_bank_integration_output_device.id as integration_id,
             tb_autopay_bank_integration_output_device.output_device_id,
             tb_autopay_bank_sub_account.sub_account,
             tb_autopay_bank_sub_account.acc_type, 
             tb_autopay_bank_sub_account.label, 
             tb_autopay_bank_sub_account.id as sub_account_id, 
             
             tb_autopay_bank_account.account_holder_name, 
             tb_autopay_bank_account.account_number, 
             tb_autopay_bank_account.bank_sms_connected, 
             tb_autopay_bank_account.bank_api_connected, 

             tb_autopay_bank.brand_name, 
             tb_autopay_bank.short_name, 
             tb_autopay_bank.brand_name, 
             tb_autopay_bank.logo_path, 
             tb_autopay_bank.icon_path')

            ->join('tb_autopay_bank_sub_account', 'tb_autopay_bank_sub_account.id = tb_autopay_bank_integration_output_device.sub_account_id')
            ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id = tb_autopay_bank_sub_account.bank_account_id')
            ->join('tb_autopay_bank', 'tb_autopay_bank.id = tb_autopay_bank_account.bank_id')
            ->join('tb_autopay_output_device', 'tb_autopay_output_device.id = tb_autopay_bank_integration_output_device.output_device_id')
            ->where(['tb_autopay_bank_integration_output_device.output_device_id' => $id, 'tb_autopay_bank_integration_output_device.company_id' => $this->company_details->company_id])
            ->orderBy('tb_autopay_bank_integration_output_device.created_at', 'DESC')
            ->get()
            ->getResultArray();

        
        $data = [

            'data_device' => $check_exits,
            'data_integration_bank' => $data_integration_bank,
            'page_title' => 'Loa',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'new_va' =>  $integrationData[0] ?? null,
        ];

        echo theme_view('templates/autopay/header',$data);
        echo view('outputdevice/config',$data);
        echo theme_view('templates/autopay/footer',$data);
       ;
    }

    public function createqr($id = ""){
        if(empty($id)){
            show_404();
        }

        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])){
            show_404();
        }

        $check_exits = model(OutputDeviceModel::class)
        ->select('tb_autopay_output_device.*, tb_autopay_bank.brand_name')
        ->join('tb_autopay_bank', 'tb_autopay_output_device.bank_id = tb_autopay_bank.id', 'left')
        ->where(['tb_autopay_output_device.id' => $id, 'tb_autopay_output_device.company_id' => $this->company_details->company_id])
        ->first();
        if(empty($check_exits)){
            show_404();
        }

        $canContinueConnectBanks = true;

        if ($check_exits['bank_id'] == 19) {
            $canContinueConnectBanks = ! model(AbbankLinkedAccountModel::class)
                ->where('output_device_id', $id)
                ->first();
        }

        $data = [
            'data_device' => $check_exits,
            'page_title' => 'Loa',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'canContinueConnectBanks' => $canContinueConnectBanks,
        ];

        echo theme_view('templates/autopay/header',$data);
        echo view('outputdevice/qrcode/create',$data);
        echo theme_view('templates/autopay/footer',$data);
    }

    public function ajax_get_create_va_modal($id)
    {
        $bankAccount = model(BankAccountModel::class)
            ->select([
                'tb_autopay_bank_account.id',
                'tb_autopay_bank_account.account_number',
                'tb_autopay_bank_account.bank_id',
                'tb_autopay_bank_account.account_holder_name',
                'tb_autopay_bank_account.bank_sms_connected',
                'tb_autopay_bank_account.identification_number',
                'tb_autopay_bank_account.phone_number',
                'tb_autopay_bank.brand_name',
            ])
            ->join('tb_autopay_bank', 'tb_autopay_bank.id = tb_autopay_bank_account.bank_id')
            ->where('tb_autopay_bank_account.company_id', $this->user_session['company_id'])
            ->find($id);

        if (! $bankAccount) {
            show_404();
        }

        $data['bankAccount'] = $bankAccount;

        $realVaSupported = ['OCB', 'BIDV', 'MBBank', 'ACB', 'KienLongBank'];

        switch ($bankAccount->brand_name) {
            case 'ACB':
                $enterpriseAccount = model(AcbEnterpriseAccountModel::class)
                    ->where('bank_account_id', $bankAccount->id)
                    ->first();

                $isEnterpriseAccount = !! $enterpriseAccount;

                if ($isEnterpriseAccount) {
                    $config = config(Acb::class);
                    $data['vac'] = $enterpriseAccount->vac;
                    $data['va_minlength'] = $config->enterpriseVaMinlength ?? 4;
                    $data['va_maxlength'] = $config->enterpriseVaMaxlength ?? 18;
                }

                break;
            case 'BIDV':
                $enterpriseAccount = model(BidvEnterpriseAccountModel::class)
                    ->where('bank_account_id', $bankAccount->id)
                    ->first();
                
                $isEnterpriseAccount = !! $enterpriseAccount;
                $config = config(Bidv::class);
                
                if ($isEnterpriseAccount) {
                    $vaPrefix = $enterpriseAccount->va_prefix ?? $config->vaPrefix;

                    $data['prefixId'] = $vaPrefix . strlen($enterpriseAccount->prefix_id) . $enterpriseAccount->prefix_id . ($vaPrefix == '96' ? '88' : 'VA');
                    $data['vaSuffixMaxLength'] = $config->vaMaxlen - strlen($data['prefixId']);
                    $data['vaNameMinLength'] = $config->vaNameMinlen;
                    $data['vaNameMaxLength'] = $config->vaNameMaxlen;
                    $data['customVaName'] = $enterpriseAccount->custom_va_name ?? false;
                } else {
                    $data['vaPrefix'] = $config->personalVaPrefix;
                    $data['vaSuffixMaxLength'] = $config->personalVaMaxlen - strlen($data['vaPrefix']);
                    $data['vaCharType'] = $config->personalVaCharType;
                    $data['vaMaxLength'] = $config->personalVaMaxlen;
                }

                break;
            case 'OCB':
                $enterpriseAccount = model(OcbEnterpriseAccountModel::class)
                    ->where('bank_account_id', $bankAccount->id)
                    ->first();

                $isEnterpriseAccount = !! $enterpriseAccount;
                $config = config(Ocb::class);
                $data['vaOnlyNumber'] = $config->OcbVAOnlyNumber;
                $data['vaNumberMinLength'] = $config->OcbDefaultVANumberMinLength;

                if ($isEnterpriseAccount) {
                    $data['enterpriseAccount'] = $enterpriseAccount;
                    $data['apps'] = model(OcbEnterpriseAppModel::class)
                        ->where('bank_account_id', $bankAccount->id)
                        ->findAll();
                } else {
                    $data['vaPrefix'] = $config->OcbPrefixVA;
                }
                break;
            default:
                $isEnterpriseAccount = false;
                break;
        }

        if ($bankAccount->bank_sms_connected || ! in_array($bankAccount->brand_name, $realVaSupported)) {
            return $this->response->setJSON([
                'status' => true,
                'data' => view('outputdevice/bank-sub-account/virual-account', $data),
            ]);
        }

        $viewName = strtolower($bankAccount->brand_name);

        if ($isEnterpriseAccount) {
            $viewName = 'enterprise/' . $viewName;
        }

        $viewPath = 'outputdevice/bank-sub-account/' . $viewName;

        if (! file_exists(APPPATH . 'Views/' . $viewPath . '.php')) {
            show_404();
        }
        
        $session = service('session');
        
        try {
            if (isset($session->get('output_device_integration')['output_device_id'])) {
                $outputDeviceDecal = model(OutputDeviceDecalModel::class)
                    ->where('output_device_id', $session->get('output_device_integration')['output_device_id'])
                    ->where('bank_id', $bankAccount->bank_id)
                    ->where('virtual_account_number !=', null)
                    ->where('account_number', $bankAccount->account_number)
                    ->first();
                    
                if (!$outputDeviceDecal) {
                    throw new \Exception('Output device decal not found');
                }
            } else {
                $outputDeviceDecal = model(OutputDeviceDecalModel::class)
                    ->join('tb_autopay_output_device', 'tb_autopay_output_device.id = tb_autopay_output_device_decal.output_device_id')
                    ->join('tb_autopay_bank_sub_account', 'tb_autopay_bank_sub_account.sub_account = tb_autopay_output_device_decal.virtual_account_number', 'left')
                    ->where('tb_autopay_output_device_decal.bank_id', $bankAccount->bank_id)
                    ->where('tb_autopay_output_device_decal.virtual_account_number !=', null)
                    ->where('tb_autopay_output_device_decal.account_number', $bankAccount->account_number)
                    ->where('tb_autopay_output_device.company_id', $this->user_session['company_id'])
                    ->where('tb_autopay_bank_sub_account.id', null)
                    ->first();
            }
            
            $data['default_sub_account'] = $outputDeviceDecal ? str_replace($data['vaPrefix'], '', $outputDeviceDecal->virtual_account_number) : null;
        } catch (\Exception $e) {
            $data['default_sub_account'] = null;
        }

        return $this->response->setJSON([
            'status' => true,
            'data' => view($viewPath, $data),
        ]);
    }

    public function ajax_create_qr(){
        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])) {
            show_404();
        }

        $data_input = $this->request->getPost();
        
        $outputDeviceDecalModel = model(OutputDeviceDecalModel::class);
        
        $outputDeviceDecal = $outputDeviceDecalModel
            ->select(['tb_autopay_output_device_decal.id', 'tb_autopay_output_device_decal.virtual_account_number', 'tb_autopay_output_device_decal.bank_id', 'tb_autopay_output_device_decal.output_device_id'])
            ->join('tb_autopay_output_device', 'tb_autopay_output_device.id = tb_autopay_output_device_decal.output_device_id')
            ->join('tb_autopay_bank_sub_account', 'tb_autopay_bank_sub_account.sub_account = tb_autopay_output_device_decal.virtual_account_number', 'left')
            ->where('tb_autopay_output_device_decal.output_device_id', $data_input['output_device_id'])
            ->where('tb_autopay_output_device.company_id', $this->user_session['company_id'])
            ->where('tb_autopay_output_device_decal.virtual_account_number !=', null)
            ->where('tb_autopay_bank_sub_account.id', null)
            ->first();
        
        $outputDeviceDecalModel = model(OutputDeviceDecalModel::class);
    
        // Load validation service
        $validation = service("validation");
    
        // Định nghĩa rules
        $validationRules = [
            "output_device_id" => [
                "label" => "Thiết bị",
                "rules" => "required|numeric|greater_than[0]|is_not_unique[tb_autopay_output_device.id]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "numeric" => "{field} phải là số.",
                    "greater_than" => "{field} phải lớn hơn 0.",
                    "is_not_unique" => "{field} không tồn tại trong hệ thống",
                ],
            ],
            "bank_account_id" => [
                "label" => "Ngân hàng",
                "rules" => "required|numeric|greater_than[0]|is_not_unique[tb_autopay_bank_account.id]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "numeric" => "{field} phải là số.",
                    "greater_than" => "{field} phải lớn hơn 0.",
                    "is_not_unique" => "{field} không tồn tại trong hệ thống",
                ],
            ],

            "sub_account_id" => [
                "label" => "VA",
                "rules" => $outputDeviceDecal && (!isset($data_input['sub_account_id']) || $data_input['sub_account_id'] < 1) ? ['permit_empty', 'in_list[0]'] : "permit_empty|numeric|greater_than[0]|is_not_unique[tb_autopay_bank_sub_account.id]",
                "errors" => [
                    "numeric" => "{field} phải là số.",
                    "greater_than" => "{field} phải lớn hơn 0.",
                    "is_not_unique" => "{field} không tồn tại trong hệ thống",
                ],
            ],
        
           
        ];
        
    
        // Áp dụng rules và kiểm tra dữ liệu đã lọc
        $validation->setRules($validationRules);
    
        if (!$validation->run($data_input)) { 
            return $this->responseCustom(422, implode(". ", $validation->getErrors()));
        }
        
        // check QR
        if (isset($data_input['sub_account_id']) && $data_input['sub_account_id']) {
            $check_account = model(BankSubAccountModel::class)
                ->select('tb_autopay_bank_sub_account.id as sub_account_id, tb_autopay_bank_account.id as bank_account_id')
                ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id = tb_autopay_bank_sub_account.bank_account_id')
                ->where([
                    'tb_autopay_bank_sub_account.id' => $data_input['sub_account_id'],
                    'tb_autopay_bank_sub_account.bank_account_id' => $data_input['bank_account_id'],
                    'tb_autopay_bank_account.company_id' => $this->user_session['company_id']
                ])
                ->get()
                ->getRowArray();
                
            if (empty($check_account)) {
                return $this->responseCustom(423, "Không tìm thấy ngân hàng hoặc VA");
            }
        } else {
            $data_input['sub_account_id'] = isset($data_input['sub_account_id']) ? $data_input['sub_account_id'] : null;
        }

        // check Device 
        $check_device = model(OutputDeviceModel::class)
            ->where(['id' => $data_input['output_device_id'],'company_id' => $this->user_session['company_id']])
            ->get()
            ->getRowArray();
        if(empty($check_device)){
            return $this->responseCustom(423,"Không tìm thấy thiết bị");
        }

        if ($outputDeviceDecal && ($data_input['sub_account_id'] === '0' || $data_input['sub_account_id'] === 0)) {
            $data_input['sub_account_id'] = $this->createVaFromOutputDeviceDecal(
                $outputDeviceDecal->bank_id,
                $outputDeviceDecal->output_device_id,
                $data_input['bank_account_id'],
                $outputDeviceDecal->virtual_account_number
            );
        }

        // check tích hợp
        $check_integration = model(BankIntegrationOutputDeviceModel::class)
            ->where(['output_device_id'=>$data_input['output_device_id'],'bank_account_id' => $data_input['bank_account_id'],'sub_account_id'=>$data_input['sub_account_id'], 'company_id' => $this->user_session['company_id']])
            ->get()
            ->getRowArray();
        if(!empty($check_integration)){
            return $this->responseCustom(423,"QR này đã được tích hợp trước đó");   
        }
        
        if (is_shop_billing_subscription()) {
            if (!$check_device['shop_id']) {
                return $this->responseCustom(423, "Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ");   
            }
            
            try {
                $duplicateStoreQrcode = model(BankShopLinkModel::class)
                    ->where('shop_id !=', $check_device['shop_id'])
                    ->where('bank_account_id', $data_input['bank_account_id'])
                    ->where('bank_sub_account_id', $data_input['sub_account_id'] ? $data_input['sub_account_id'] : null)
                    ->countAllResults();
                    
                if ($duplicateStoreQrcode) {
                    return $this->responseCustom(423,"Mã QR nhận thanh toán này đang thuộc sở hữu của cửa hàng khác");
                }
                
                $storeFeature = new StoreFeature($check_device['shop_id']);
                $storeFeature->withCompanyContext($this->user_session['company_id']);
                $storeFeature->withBankAccountContext($data_input['bank_account_id']);
                $storeFeature->bankAccountContext()->linkToStore($data_input['sub_account_id']);
            } catch (\Exception $e) {
                if (!$e->getCode() === 409) {
                    log_message('error', '[StoreFeature] Failed to create QR for speaker: ' . $e->getMessage());
                    return $this->responseCustom(423, "Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ");   
                }
            }
        }

        $data_insert = array(
            'output_device_id'      => $data_input['output_device_id'],
            'bank_account_id'      => $data_input['bank_account_id'],
            'sub_account_id'              => $data_input['sub_account_id'],
            'company_id'         => $this->user_session['company_id'],
        );

        $status_insert = model(BankIntegrationOutputDeviceModel::class)->insert($data_insert);
        if(empty($status_insert)){
            add_user_log(array('data_id'=>$data_input['output_device_id'],'company_id' => $this->user_session['company_id'], 'data_type'=>'notification_output_device_update','description'=>'Thêm QR tích hợp loa thất bại','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Failed'));

            return $this->responseCustom(423,"Lỗi hệ thống thêm tích hợp ngân hàng, Hãy liên hệ kỹ thuật!");
        }

        session()->setFlashdata('alert', [
            'type' => 'success', 
            'message' => 'Thêm QR thành công',
        ]);
        add_user_log(array('data_id'=>$data_input['output_device_id'],'company_id' => $this->user_session['company_id'], 'data_type'=>'notification_output_device_update','description'=>'Thêm QR tích hợp loa thành công','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));

        return $this->responseCustom(200,"Tạo QR thành công!");
    }

    public function ajax_delete_integration($id_intergarion = ""){


        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])) {
            show_404();
        }

        $data_input = array(
            'id' => xss_clean($id_intergarion),
        );
        
        $service = service('validation');
        $service->setRules([
            "id" => [
                "label" => "ID",
                "rules" => "required|is_not_unique[tb_autopay_bank_integration_output_device.id]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "is_not_unique" => "{field} không tồn tại trong hệ thống",
                ],
            ],
        ]);

        if(!$service->run($data_input)){
            return $this->responseCustom(422,implode(". ", $service->getErrors()));
        }

        $check_integration = model(BankIntegrationOutputDeviceModel::class)
            ->where(['id'=>$data_input['id'],'company_id' => $this->user_session['company_id']])
            ->get()
            ->getRowArray();
        if(empty($check_integration)){
            return $this->responseCustom(423,"Không tìm thấy tích hợp QR");
        }

        $status_delete = model(BankIntegrationOutputDeviceModel::class)->where(['id' => $data_input['id'],'company_id' => $this->user_session['company_id']])->delete();
        if(empty($status_delete)){
            add_user_log(array('data_id'=>$data_input['id'],'company_id' => $this->user_session['company_id'], 'data_type'=>'notification_output_device_update','description'=>'Xóa QR tích hợp loa thất bại','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Failed'));

            return $this->responseCustom(423,"Lỗi hệ thống xóa tích hợp QR, Hãy liên hệ kỹ thuật!");
        }
        add_user_log(array('data_id'=>$data_input['id'],'company_id' => $this->user_session['company_id'], 'data_type'=>'notification_output_device_update','description'=>'Xóa QR tích hợp loa thành công','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));

       
        return $this->responseCustom(200,"Xóa QR thành công!");
    }

    public function bankaccount($id)
    {
        if (! in_array($this->company_details->role, ['Admin', 'SuperAdmin'])) {
            show_404();
        }

        if ($this->request->getMethod(true) !== 'GET') {
            show_404();
        }

        $device = model(OutputDeviceModel::class)
            ->select('tb_autopay_output_device.*, tb_autopay_bank.brand_name')
            ->join('tb_autopay_bank', 'tb_autopay_output_device.bank_id = tb_autopay_bank.id', 'left')
            ->where(['tb_autopay_output_device.id' => $id, 'tb_autopay_output_device.company_id' => $this->company_details->company_id])
            ->first();

        if (! $device) {
            show_404();
        }

        if (!is_speaker_billing_subscription()) {
            show_404();
        }

        $data_integration_bank = model(BankIntegrationOutputDeviceModel::class)
            ->where('output_device_id', $id)
            ->where('company_id', $this->user_session['company_id'])
            ->countAllResults();

        if ($device['bank_id'] == 19) {
            $bankAccounts = model(AbbankLinkedAccountModel::class)
                ->select('
                    tb_autopay_bank_account.id,
                    tb_autopay_bank_account.label,
                    tb_autopay_bank_account.account_holder_name,
                    tb_autopay_bank_account.account_number,
                    tb_autopay_bank.brand_name,
                    tb_autopay_bank.short_name,
                    tb_autopay_bank.logo_path,
                    tb_autopay_bank.icon_path,
                    tb_autopay_abbank_linked_accounts.id as integration_id,
                    (
                        SELECT COUNT(DISTINCT output_device_id) FROM tb_autopay_abbank_linked_accounts
                        WHERE tb_autopay_abbank_linked_accounts.bank_account_id = tb_autopay_bank_account.id
                    ) as devices_count
                ')
                ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id = tb_autopay_abbank_linked_accounts.bank_account_id')
                ->join('tb_autopay_bank', 'tb_autopay_bank.id = tb_autopay_bank_account.bank_id')
                ->where('tb_autopay_abbank_linked_accounts.output_device_id', $id)
                ->where('tb_autopay_bank_account.company_id', $this->user_session['company_id'])
                ->findAll();
        } else {
            $bankAccounts = model(BankAccountModel::class)
                ->select('
                    tb_autopay_bank_account.id,
                    tb_autopay_bank_account.label,
                    tb_autopay_bank_account.account_holder_name,
                    tb_autopay_bank_account.account_number,
                    tb_autopay_bank.brand_name,
                    tb_autopay_bank.short_name,
                    tb_autopay_bank.logo_path,
                    tb_autopay_bank.icon_path,
                    tb_autopay_bank_integration_output_device.id as integration_id,
                    (
                        SELECT COUNT(DISTINCT output_device_id) FROM tb_autopay_bank_integration_output_device
                        WHERE tb_autopay_bank_integration_output_device.bank_account_id = tb_autopay_bank_account.id
                        ) as devices_count
                ')
                ->join('tb_autopay_bank', 'tb_autopay_bank.id = tb_autopay_bank_account.bank_id')
                ->join('tb_autopay_bank_integration_output_device', 'tb_autopay_bank_integration_output_device.bank_account_id = tb_autopay_bank_account.id', 'left')
                ->where('tb_autopay_bank_account.company_id', $this->user_session['company_id'])
                ->groupBy('tb_autopay_bank_account.id')
                ->orderBy('devices_count', 'asc')
                ->findAll();
        }
        

            $integrationData = model(BankIntegrationOutputDeviceModel::class)
            ->select('
             tb_autopay_bank_integration_output_device.id as integration_id,
             tb_autopay_bank_integration_output_device.output_device_id,
             tb_autopay_bank_sub_account.sub_account,
             tb_autopay_bank_sub_account.acc_type, 
             tb_autopay_bank_sub_account.label, 
             tb_autopay_bank_sub_account.id as sub_account_id, 
             
             tb_autopay_bank_account.account_holder_name, 
             tb_autopay_bank_account.account_number, 
             tb_autopay_bank_account.bank_sms_connected, 
             tb_autopay_bank_account.bank_api_connected, 

             tb_autopay_bank.brand_name, 
             tb_autopay_bank.short_name, 
             tb_autopay_bank.brand_name, 
             tb_autopay_bank.logo_path, 
             tb_autopay_bank.icon_path')

            ->join('tb_autopay_bank_sub_account', 'tb_autopay_bank_sub_account.id = tb_autopay_bank_integration_output_device.sub_account_id')
            ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id = tb_autopay_bank_sub_account.bank_account_id')
            ->join('tb_autopay_bank', 'tb_autopay_bank.id = tb_autopay_bank_account.bank_id')
            ->join('tb_autopay_output_device', 'tb_autopay_output_device.id = tb_autopay_bank_integration_output_device.output_device_id')
            ->where(['tb_autopay_bank_integration_output_device.output_device_id' => $id, 'tb_autopay_bank_integration_output_device.company_id' => $this->company_details->company_id])
            ->orderBy('tb_autopay_bank_integration_output_device.created_at', 'DESC')
            ->get()
            ->getResultArray();

            
        $data = [
            'data_device' => $device,
            'page_title' => 'Loa',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'data_integration_bank' => $data_integration_bank,
            'bankAccounts' => $bankAccounts,
            'unlinkableBankAccounts' => $this->getUnlinkableBankAccounts(),
            'new_va' => $integrationData[0] ?? null,
        ];

        echo theme_view('templates/autopay/header', $data);
        echo view('outputdevice/bankaccount', $data);
        echo theme_view('templates/autopay/footer', $data);
    }

    public function ajax_get_bank_account_delete_modal($id)
    {
        if (! in_array($this->company_details->role, ['Admin', 'SuperAdmin'])) {
            show_404();
        }

        if ($this->request->getMethod(true) !== 'GET') {
            show_404();
        }

        $bankAccount = model(BankAccountModel::class)
            ->select('
                tb_autopay_bank_account.id,
                tb_autopay_bank_account.label,
                tb_autopay_bank_account.account_holder_name,
                tb_autopay_bank_account.account_number,
                tb_autopay_bank.brand_name,
                tb_autopay_bank.short_name,
                tb_autopay_bank.logo_path,
                tb_autopay_bank.icon_path,
                (SELECT COUNT(*) FROM tb_autopay_bank_integration_output_device WHERE 
                 tb_autopay_bank_integration_output_device.bank_account_id = tb_autopay_bank_account.id) as devices_count
            ')
            ->where('company_id', $this->user_session['company_id'])
            ->join('tb_autopay_bank', 'tb_autopay_bank.id = tb_autopay_bank_account.bank_id')
            ->find($id);

        if (! $bankAccount) {
            show_404();
        }
        
        if (!is_speaker_billing_subscription()) {
            return $this->failNotFound();
        }

        if (! in_array($bankAccount->short_name, $this->getUnlinkableBankAccounts())) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Tài khoản ngân hàng này không thể xóa.',
            ]);
        }

        if ($bankAccount->devices_count > 1 && $bankAccount->short_name !== 'ABBANK') {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Tài khoản ngân hàng này đang được sử dụng trong các thiết bị khác. Vui lòng gỡ tài khoản ngân hàng này khỏi các thiết bị khác trước khi xóa.',
            ]);
        }

        switch ($bankAccount->short_name) {
            case 'ACB':
                $isEnterpriseAccount = !! model(AcbEnterpriseAccountModel::class)
                    ->where('bank_account_id', $bankAccount->id)
                    ->first();
                break;
            default:
                $isEnterpriseAccount = false;
                break;
        }

        return $this->response->setJSON([
            'status' => true,
            'data' => view('outputdevice/delete-modal/' . strtolower($bankAccount->short_name), [
                'bankAccount' => $bankAccount,
                'isEnterpriseAccount' => $isEnterpriseAccount,
                'outputDeviceId' => $this->request->getGet('output_device_id'),
            ]),
        ]);
    }

    protected function getUnlinkableBankAccounts()
    {
        return ['ACB', 'BIDV', 'MBBank', 'VPB', 'VietinBank', 'ABBANK'];
    }

    public function ajax_get_bank_sub_accounts($id = ''){

        if (! in_array($this->company_details->role, ['Admin', 'SuperAdmin'])) {
            return $this->responseCustom(403, 'Không có quyền truy cập');
        }

        if ($this->request->getMethod(true) !== 'GET') {
            return $this->responseCustom(405, 'Method not allowed');
        }

        if(!is_numeric($id))
            return $this->responseCustom(422, 'ID không hợp lệ');

        $bankAccount = model(BankAccountModel::class)
        ->select('
            tb_autopay_bank_account.id,
            tb_autopay_bank_account.bank_id,
            tb_autopay_bank_account.bank_sms_connected, 
            tb_autopay_bank_account.bank_api_connected,
            tb_autopay_bank_account.label,
            tb_autopay_bank_account.account_holder_name,
            tb_autopay_bank_account.account_number,
            tb_autopay_bank.brand_name,
            tb_autopay_bank.short_name,
            tb_autopay_bank.logo_path,
            tb_autopay_bank.icon_path,
            (SELECT COUNT(*) FROM tb_autopay_bank_integration_output_device WHERE 
                tb_autopay_bank_integration_output_device.bank_account_id = tb_autopay_bank_account.id) as devices_count
        ')
        ->where('company_id', $this->user_session['company_id'])
        ->join('tb_autopay_bank', 'tb_autopay_bank.id = tb_autopay_bank_account.bank_id')
        ->find($id);

        if(!is_object($bankAccount))
            return $this->responseCustom(404, 'Không tìm thấy tài khoản ngân hàng');

        $bankSubAccountModel = model(BankSubAccountModel::class);

        $query = $bankSubAccountModel->select(
            "tb_autopay_bank_sub_account.id, 
             tb_autopay_bank.brand_name,
             tb_autopay_bank.logo_path, 
             tb_autopay_bank.icon_path, 
             tb_autopay_bank_account.account_holder_name, 
             tb_autopay_bank_account.account_number, 
             tb_autopay_bank_account.bank_sms_connected, 
             tb_autopay_bank_account.bank_api_connected, 
             tb_autopay_bank_sub_account.sub_account, 
             tb_autopay_bank_sub_account.acc_type, 
             tb_autopay_bank_sub_account.label",
            )
            ->join("tb_autopay_bank_account", "tb_autopay_bank_account.id = tb_autopay_bank_sub_account.bank_account_id")
            ->join("tb_autopay_bank", "tb_autopay_bank.id = tb_autopay_bank_account.bank_id", "left")
            ->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id']])
            ->where(['tb_autopay_bank_sub_account.active' => 1, 'tb_autopay_bank_sub_account.va_active' => 1])
            ->where('tb_autopay_bank_account.id', $id)
            ->orderBy('tb_autopay_bank_sub_account.created_at', 'DESC')
            ->get()->getResultArray(); 

        $outputDeviceDecalModel = model(OutputDeviceDecalModel::class);

        $outputDeviceDecals = $outputDeviceDecalModel->where([
            'account_number' => $bankAccount->account_number,
            'bank_id' => $bankAccount->bank_id,
            'virtual_account_number !=' => null,
            'output_device_id !=' => null
        ])->get()->getResult();

        $outputDeviceId = session()->get('output_device_integration')['output_device_id'] ?? $this->request->getGet('output_device_id');

        if (count($outputDeviceDecals) === 0) {
            $outputDeviceDecal = null;
        } else if (count($outputDeviceDecals) === 1) {
            $outputDeviceDecal = $outputDeviceDecals[0];
        } else if (count($outputDeviceDecals) > 1 && $outputDeviceId) {
            $outputDeviceDecal = null;
            foreach ($outputDeviceDecals as $decal) {
                if ($decal->output_device_id == $outputDeviceId) {
                    $outputDeviceDecal = $decal;
                    break;
                }
            }
        } else {
            $outputDeviceDecal = null;
        }
        
        $virtualAccountAvailable = $outputDeviceDecal && $outputDeviceDecal->bank_id == 8 ? model(MbbMmsTerminalModel::class)->where([
            'output_device_id' => $outputDeviceDecal->output_device_id,
        ])->whereIn('bank_account_id', [0, $bankAccount->id])
        ->countAllResults() : true;
        
        if ($outputDeviceDecal 
        && $outputDeviceDecal->bank_id == $bankAccount->bank_id 
        && $outputDeviceDecal->account_number == $bankAccount->account_number
        && model(BankSubAccountModel::class)->where(['sub_account' => $outputDeviceDecal->virtual_account_number])->countAllResults() === 0
        && $virtualAccountAvailable) {
            array_unshift($query, [
                'id' => 0,
                'brand_name' => $bankAccount->brand_name,
                'bank_id' => $bankAccount->bank_id,
                'logo_path' => $bankAccount->logo_path,
                'icon_path' => $bankAccount->icon_path,
                'account_number' => $bankAccount->account_number,
                'account_holder_name' => $bankAccount->account_holder_name,
                'bank_sms_connected' => $bankAccount->bank_sms_connected,
                'bank_api_connected' => $bankAccount->bank_api_connected,
                'acc_type' => in_array($bankAccount->brand_name, ['VPBank', 'TPBank', 'VietinBank', 'ACB']) ? 'Virtual' : 'Real',
                'sub_account' => $outputDeviceDecal->virtual_account_number,
                'is_decal' => true,
                'label' => 'in sẵn trên loa'
            ]);
        }
        
            return $this->response->setJSON([
                'status' => true,
                'data' => $query,
                'outputDeviceDecal' => $outputDeviceDecal,
                'outputDeviceId' => $outputDeviceId,
                'virtualAccountAvailable' => $virtualAccountAvailable,
            ]);
    }

    public function ajax_get_bank_accounts($outputDeviceId = null)
    {
        if (! in_array($this->company_details->role, ['Admin', 'SuperAdmin'])) {
            return $this->responseCustom(403, 'Không có quyền truy cập');
        }

        if ($this->request->getMethod(true) !== 'GET') {
            return $this->responseCustom(405, 'Method not allowed');
        }

        $bankAccounts = model(BankAccountModel::class)
            ->select('
                tb_autopay_bank_account.id,
                tb_autopay_bank_account.label,
                tb_autopay_bank_account.account_holder_name,
                tb_autopay_bank_account.account_number,
                tb_autopay_bank_account.bank_sms_connected,
                tb_autopay_bank_account.bank_api_connected,
                tb_autopay_bank.brand_name,
                tb_autopay_bank.short_name,
                tb_autopay_bank.logo_path,
                tb_autopay_bank.icon_path
            ')
            ->join('tb_autopay_bank', 'tb_autopay_bank.id = tb_autopay_bank_account.bank_id')
            ->where('tb_autopay_bank_account.company_id', $this->user_session['company_id'])
            ->groupStart()
                ->where('tb_autopay_bank_account.bank_sms_connected', 1)
                ->orWhere('tb_autopay_bank_account.bank_api_connected', 1)
            ->groupEnd();

        if ($outputDeviceId) {
            $outputDevice = model(OutputDeviceModel::class)->find($outputDeviceId);

            if ($outputDevice && $outputDevice['bank_id'] == 19) {
                $bankAccounts = $bankAccounts
                    ->join('tb_autopay_abbank_linked_accounts', 'tb_autopay_abbank_linked_accounts.bank_account_id = tb_autopay_bank_account.id', 'left')
                    ->where('tb_autopay_abbank_linked_accounts.output_device_id', $outputDeviceId);
            }
        }

        $bankAccounts = $bankAccounts
            ->orderBy('tb_autopay_bank_account.created_at', 'DESC')
            ->findAll();

        return $this->response->setJSON([
            'status' => true,
            'data' => $bankAccounts,
        ]);   
    }
    
    protected function generatePayQrcodeUrl(int $bankAccountId, ?int $bankSubAccountId = null): string
    {
        $bankModel = model(BankModel::class);
        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        
        $bankAccount = $bankAccountModel->find($bankAccountId);
        $bankSubAccount = $bankSubAccountId ? $bankSubAccountModel->find($bankSubAccountId) : null;
        $bank = $bankModel->find($bankAccount->bank_id);
        
        $accountNumber = $bankSubAccount && $bankSubAccount->acc_type === 'Real' ? $bankSubAccount->sub_account : $bankAccount->account_number;
        
        $remark = '';
        
        if ($bank->brand_name == 'VietinBank') {
            $remark .= 'SEVQR ';
        }

        if ($bank->brand_name == 'ABBANK') {
            $remark .= 'LocVang ';
        }
        
        if ($bankSubAccount && $bankSubAccount->acc_type === 'Virtual') {
            $remark .=  'TKP' . $bankSubAccount->sub_account;
        }

        $template = $bank->id == 19 ? '' : 'qronly';

        return sprintf('https://qr.sepay.vn/img?bank=%s&acc=%s&template=%s&des=%s', $bank->bin, $accountNumber, $template, $remark);
    }
    
    protected function createVaFromOutputDeviceDecal(int $bankId, int $outputDeviceId, int $bankAccountId, string $virtualAccountNumber): int
    {
        $bankAccountFeature = new BankAccountFeature;
        $bankAccountFeature->withCompanyContext($this->user_session['company_id']);
        $bankAccount = model(BankAccountModel::class)->find($bankAccountId);
        
        if (!$bankAccount) {
            throw new \Exception('Bank account not found');
        }
        
        if ($bankId === 8) {
            model(MbbMmsTerminalModel::class)->where('output_device_id', $outputDeviceId)->set(['bank_account_id' => $bankAccount->id])->update();
            
            $isEnterprise = model(MbbEnterpriseAccountModel::class)->where('bank_account_id', $bankAccount->id)->first();
            
            $bankAccountFeature->withBankAccountConnectContext('MBBank', $isEnterprise ? 'enterprise' : 'individual');
            /** @var MBBankBankAccountConnectContext $bankAccountConnectContext */
            $bankAccountConnectContext = $bankAccountFeature->bankAccountConnectContext();
            $bankAccountConnectContext->bankAccountId = $bankAccount->id;
            $bankAccountConnectContext->accountHolderName = $bankAccount->account_holder_name;
            $bankAccountConnectContext->outputDeviceDecal = model(OutputDeviceDecalModel::class)->where([
                'output_device_id' => $outputDeviceId,
                'bank_id' => 8,
                'account_number' => $bankAccount->account_number,
                'virtual_account_number !=' => null,
            ])->first();
            
            $bankAccountConnectContext->requestCreateIndividualOfficialVa();
            $bankAccountConnectContext->confirmCreateIndividualOfficialVa();
            
            if (!$bankAccountConnectContext->firstVaId) {
                throw new \Exception('Failed to create virtual account');
            }
            
            return (int) $bankAccountConnectContext->firstVaId;
        }
        
        $bankSubAccountId = model(BankSubAccountModel::class)->insert([
            'bank_account_id' => $bankAccount->id,
            'sub_account' => $virtualAccountNumber,
            'acc_type' => 'Virtual',
        ]);
        
        if (!$bankSubAccountId) {
            throw new \Exception('Failed to create virtual account');
        }
        
        return (int) $bankSubAccountId;
    }
    
    public function ajax_reverse_geocode()
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }
        
        $lat = $this->request->getPost('lat');
        $lon = $this->request->getPost('lon');
        
        if (!is_numeric($lat) || !is_numeric($lon)) {
            return $this->fail(['status' => false, 'message' => 'Dữ liệu tọa độ không hợp lệ.']);
        }
        
        $tomtomClient = new TomtomClient;
        
        try {
            $address = $tomtomClient->reverseGeocode($lat, $lon);
        } catch (\Exception $e) {
            return $this->fail(['status' => false, 'message' => $e->getMessage()]);
        }
        
        $province = model(ProvinceModel::class)->like('name', $address->address->countrySubdivision)->first();
        
        if (! $province) {
            return $this->fail(['status' => false, 'message' => 'Không xác định được địa chỉ']);
        }
        
        $district = model(DistrictModel::class)
            ->where('province_code', $province->code)
            ->like('name', $address->address->municipalitySubdivision)
            ->first();
            
        if (! $district) {
            return $this->fail(['status' => false, 'message' => 'Không xác định được địa chỉ']);
        }
            
        $ward = model(WardModel::class)
            ->where('district_code', $district->code)
            ->like('name', $address->address->municipalitySecondarySubdivision)
            ->first();
        
        if (! $ward) {
            return $this->fail(['status' => false, 'message' => 'Không xác định được địa chỉ']);
        }
        
        return $this->respond([
            'status' => true,
            'province_code' => $province->code,
            'district_code' => $district->code,
            'ward_code' => $ward->code,
            'address' => $address->address->streetNameAndNumber,
            'full_address' => implode(', ', [$address->address->streetNameAndNumber, $ward->full_name, $district->full_name, $province->full_name])
        ]);
    }
    
    public function store(string $speakerId = '')
    {
        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])){
            show_404();
        }
        
        if (!is_shop_billing_subscription()) {
            show_404();
        }

        $data = [
            'page_title' => 'Loa',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'default_store_id' => $this->request->getVar('store_id')
        ];
        
        $fallbackUrl = base_url('outputdevice/step1' . ($data['default_store_id'] ? '?store_id=' . $data['default_store_id'] : ''));
        
        if (!is_numeric($speakerId)) {
            return redirect()->to($fallbackUrl);
        }
        
        $speakerModel = model(OutputDeviceModel::class);
        $data['speaker'] = $speakerModel
            ->where('id', $speakerId)
            ->where('company_id', $this->user_session['company_id'])
            ->where('active', 1)
            ->first();
        
        if (!$data['speaker']) {
            return redirect()->to($fallbackUrl);
        }
        
        $storeFeature = new StoreFeature;
        $storeFeature->withCompanyContext($this->user_session['company_id']);
        
        $data['stores'] = $storeFeature->getStores();
        $data['fixed_store'] = false;
        
        if ($data['speaker']['shop_id']) {
            $data['default_store_id'] = $data['speaker']['shop_id'];
        } else {
            $qrcodeModel = model(BankIntegrationOutputDeviceModel::class);
            $qrcode = $qrcodeModel
                ->where('output_device_id', $speakerId)
                ->where('company_id', $this->user_session['company_id'])
                ->first();
            
            if ($qrcode) {
                $storeQrcode = model(BankShopLinkModel::class)
                    ->where('bank_account_id', $qrcode['bank_account_id'])
                    ->where('bank_sub_account_id', $qrcode['sub_account_id'] ? $qrcode['sub_account_id'] : null)
                    ->first();
                    
                if ($storeQrcode) {
                    $data['default_store_id'] = $storeQrcode['shop_id'];
                    $data['fixed_store'] = true;
                }
            }
        }
        
        if ($data['default_store_id'] ) {
            $stores = $data['stores'];
            $defaultStoreKey = array_search($data['default_store_id'] , array_column($stores, 'id'));

            if ($defaultStoreKey !== false) {
                $defaultStore = $stores[$defaultStoreKey];
                unset($stores[$defaultStoreKey]);
                array_unshift($stores, $defaultStore);
                $data['stores'] = $stores;
            }
        }
        
        echo theme_view('templates/autopay/header', $data);
        echo theme_view('speaker/add/store', $data);
        echo theme_view('templates/autopay/footer', $data);
    }
    
    public function ajax_change_store(string $speakerId = '')
    {
        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])){
            show_404();
        }
        
        if (!is_shop_billing_subscription()) {
            return $this->failNotFound();
        }
        
        if (!is_numeric($speakerId)) {
            return $this->failNotFound();
        }
        
        $speakerModel = model(OutputDeviceModel::class);
        $speaker = $speakerModel
            ->where('id', $speakerId)
            ->where('company_id', $this->user_session['company_id'])
            ->where('active', 1)
            ->first();
        
        if (!$speaker) {
            return $this->failNotFound();
        }
        
        $storeId = $this->request->getPost('store_id');
        
        if (!is_numeric($storeId)) {
            return $this->failNotFound();
        }
        
        if ($storeId == $speaker['shop_id']) {
            return $this->respond(['status' => true, 'redirect_url' => base_url('outputdevice/detail/' . $speaker['id'])]);
        }
        
        try {
            $storeFeature = new StoreFeature($storeId);
            $storeFeature->withCompanyContext($this->user_session['company_id']);
            
            if ($speaker['shop_id']) {
                model(BankIntegrationOutputDeviceModel::class)
                    ->where('output_device_id', $speaker['id'])
                    ->where('company_id', $this->user_session['company_id'])
                    ->delete();
            } else {
                $qrcodeModel = model(BankIntegrationOutputDeviceModel::class);
                $qrcode = $qrcodeModel
                    ->where('output_device_id', $speaker['id'])
                    ->where('company_id', $this->user_session['company_id'])
                    ->first();
                
                if ($qrcode) {
                    $storeQrcode = model(BankShopLinkModel::class)
                        ->where('bank_account_id', $qrcode['bank_account_id'])
                        ->where('bank_sub_account_id', $qrcode['sub_account_id'] ? $qrcode['sub_account_id'] : null)
                        ->first();
                        
                    if ($storeQrcode && $storeQrcode['shop_id'] != $storeId) {
                        model(BankIntegrationOutputDeviceModel::class)
                            ->where('output_device_id', $speaker['id'])
                            ->where('company_id', $this->user_session['company_id'])
                            ->delete();
                    }
                    
                    try {
                        if (!$storeQrcode || ($storeQrcode && $storeQrcode['shop_id'] != $storeId)) {
                            model(BankShopLinkModel::class)->insert([
                                'shop_id' => $storeId,
                                'bank_account_id' => $qrcode['bank_account_id'],
                                'bank_sub_account_id' => $qrcode['sub_account_id'] ? $qrcode['sub_account_id'] : null
                            ]);
                        }
                    } catch (\Exception $e) {
                        
                    }
                }
            }
            
            $storeFeature->linkStoreToSpeaker($speaker['id']);
                
            if ($speaker['shop_id']) {
                set_alert('success', 'Chuyển đổi cửa hàng thành công, vui lòng cấu hình lại mã QR nhận thanh toán cho loa');
                return $this->respond(['status' => true, 'redirect_url' => base_url('outputdevice/detail/' . $speaker['id'])]);
            }
            
            return $this->respond(['status' => true, 'redirect_url' => base_url('outputdevice')]);
        } catch (\Exception $e) {
            log_message('error', '[StoreFeature] Failed to change store for speaker: ' . $e->getMessage());
            return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ']);
        }
    }
}
