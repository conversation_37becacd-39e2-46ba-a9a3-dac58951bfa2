<?php

namespace App\Controllers\Publicapi;

use App\Controllers\BaseController;

class Viber extends BaseController
{
    public function webhook()
    {
        helper('general');

        if ($this->request->getMethod(true) !== 'POST') {
            show_404();
        }

        $data = $this->request->getJSON(true);
        log_message('error', '[VIBER WEBHOOK] ' . json_encode($data));

        return $this->response->setJSON([
            'status' => true,
            'message' => 'Success',
        ]);
    }
}
