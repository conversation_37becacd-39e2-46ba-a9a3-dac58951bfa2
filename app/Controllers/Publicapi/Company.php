<?php

namespace App\Controllers\Publicapi;
use CodeIgniter\Controller;
use App\Models\ProductModel;
use App\Models\BankModel;
use Config\Billing;

class Company extends Controller{
    public function getPlan()
    {
        $bankModel = model(BankModel::class);
        $productModel = model(ProductModel::class);

        $data = [];

        $apiBrandNames = ['VPBank', 'TPBank', 'ACB', 'Vietinbank', 'MBBANK', 'OCB', 'KienLongBank', 'BIDV', 'MSB'];
        $data['bank_sms']= $bankModel->whereNotIn('brand_name', $apiBrandNames)->get()->getResult();
        $data['bank_api'] = $bankModel->whereIn('brand_name', $apiBrandNames)->get()->getResult();
        
        $data['plans_free'] = $productModel->getFreePlan();
        $data['plans_api'] = $productModel->getApiPlans();
        $data['plans_sms'] = $productModel->getSmsPlans();
        $data['plans_promotion'] = $productModel->getPromotionPlans();

        return $this->response->setJSON(["status"=>TRUE , "data" => $data]);
    }
}