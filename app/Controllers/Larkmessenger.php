<?php

namespace App\Controllers;

use CodeIgniter\API\ResponseTrait;
use App\Controllers\BaseController;
use App\Features\Store\StoreFeature;
use App\Models\NotificationLarkMessengerModel;
use App\Models\NotificationLarkMessengerQueueModel;

class Larkmessenger extends BaseController
{
    use ResponseTrait;

    public function index()
    {
        if (!has_permission('NotificationLarkMessenger', 'can_view_all')) {
            show_404();
        }
        
        $data = [
            'page_title' => 'Lark Messenger',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];
        
        $storeFeature = new StoreFeature;
        $storeFeature->withCompanyContext($this->user_session['company_id']);
        
        $data['lark_messenger_total'] = $storeFeature->larkMessengerBuilder()->countAllResults();
        
        echo theme_view('templates/autopay/header', $data);
        echo theme_view('lark-messenger/index', $data);
        echo theme_view('templates/autopay/footer', $data);
    }
    
    public function ajax_index_lark_messenger()
    {
        $storeId = $this->request->getGet('store_id');
        $type = $this->request->getGet('type');

        $storeFeature = new StoreFeature;
        $storeFeature->withCompanyContext($this->user_session['company_id']);
        
        $data = $storeFeature->getStoreLarkMessengers($storeId ? [$storeId] : [], $type);
        
        return $this->respond([
            'data' => $data
        ]);
    }
    
    public function add()
    {
        if (!has_permission('NotificationLarkMessenger', 'can_add')) {
            show_404();
        }
 
        $session = service('session');
        $integrationData = $session->get('lark_messenger_integration');

        $data = [
            'page_title' => 'Lark Messenger',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'default_store_id' => $this->request->getGet('store_id')
        ];
        
        $step = $this->request->getGet('step') ?? 1;
        $fallbackUrl = base_url('larkmessenger/add' . ($data['default_store_id'] ? '?store_id=' . $data['default_store_id'] : ''));
        
        if (!is_numeric($step) || $step < 1 || $step > 4) {
            return redirect()->to($fallbackUrl);
        }
        
        if ($step > 1 && (!is_array($integrationData) || !$integrationData['is_connected'])) {
            return redirect()->to($fallbackUrl);
        }
        
        $data['step'] = (int) $step;

        echo theme_view('templates/autopay/header', $data);

        $storeFeature = new StoreFeature;
        $storeFeature->withCompanyContext($this->user_session['company_id']);
        
        $data['integration'] = $integrationData;
        
        if ($data['step'] === 2) {
            $data['stores'] = $storeFeature->getLinkableLarkMessengerStores($integrationData['bot_webhook_url'], $integrationData['transaction_type']);

            if ($data['default_store_id'] ) {
                $stores = $data['stores'];
                $defaultStoreKey = array_search($data['default_store_id'] , array_column($stores, 'id'));

                if ($defaultStoreKey !== false) {
                    $defaultStore = $stores[$defaultStoreKey];
                    unset($stores[$defaultStoreKey]);
                    array_unshift($stores, $defaultStore);
                    $data['stores'] = $stores;
                }
            }

            echo theme_view('lark-messenger/add/step2', $data);
        } else if ($data['step'] === 3) {
            echo theme_view('lark-messenger/add/step3', $data);
        } else if ($data['step'] === 4) {
            $session->remove('lark_messenger_integration');
            $data['stores'] = $storeFeature->getLinkedLarkMessengerStores($integrationData['bot_webhook_url'], $integrationData['transaction_type']);
            echo theme_view('lark-messenger/add/step4', $data);
        } else {
            $session->remove('lark_messenger_integration');
            echo theme_view('lark-messenger/add/step1', $data);
        }

        echo theme_view('templates/autopay/footer', $data);
    }
    
    public function ajax_add_step_1()
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }

        if (! has_permission('NotificationLarkMessenger', 'can_add')) {
            return $this->failNotFound();
        }

        $data = [
            'bot_webhook_url' => trim($this->request->getPost('bot_webhook_url')),
            'description' => trim($this->request->getPost('description')),
            'transaction_type' => trim($this->request->getPost('transaction_type'))
        ];

        $rules = [
            'bot_webhook_url' => ['required', 'regex_match[/^https:\/\/open\.larksuite\.com\/open-apis\/bot\/v2\/hook\/[a-zA-Z0-9\-]{36}$/]'],
            'description' => ['required', 'max_length[100]'],
            'transaction_type' => ['required','in_list[All,In_only,Out_only]'],
        ];

        if (! $this->validateData($data, $rules)) {
            return $this->fail($this->validator->getErrors());
        }
        
        $storeFeature = new StoreFeature;
        $storeFeature->withCompanyContext($this->user_session['company_id']);
        
        if ($storeFeature->larkMessengerBuilder()->where('bot_webhook_url', $data['bot_webhook_url'])->where('transaction_type', $data['transaction_type'])->countAllResults() > 0) {
            return $this->respond(['status' => false, 'message' => 'Tích hợp Lark Messenger đã tồn tại, vui lòng kiểm tra lại.']);
        }
        
        $session = service('session');
        $session->set('lark_messenger_integration', array_merge($session->get('lark_messenger_integration') ?? [], $data));

        return $this->respond(['status' => true]);
    }
    
    public function ajax_add_step_2()
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }

        if (! has_permission('NotificationLarkMessenger', 'can_add')) {
            return $this->failNotFound();
        }
        
        $session = service('session');
        $integrationData = $session->get('lark_messenger_integration');
        
        if (! $integrationData || !$integrationData['is_connected']) {
            return $this->respond(['status' => false, 'redirect_to' => base_url('larkmessenger/add')]);
        }
        
        $storeIds = $this->request->getVar('store_ids');

        if (!is_array($storeIds) || empty($storeIds)) {
            return $this->respond(['status' => false, 'message' => 'Vui lòng chọn ít nhất một cửa hàng']);
        }
        
        $storeFeature = new StoreFeature;
        $storeFeature->withCompanyContext($this->user_session['company_id']);
        $linkableStores = $storeFeature->getLinkableLarkMessengerStores($integrationData['bot_webhook_url'], $integrationData['transaction_type']);
        
        if (count(array_diff($storeIds, array_column($linkableStores, 'id'))) > 0) {
            return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng tải lại trang']);
        }
        
        $session->set('lark_messenger_integration', array_merge($integrationData, [
            'links' => array_column($linkableStores, 'links'),
            'store_ids' => $storeIds,
        ]));
        
        return $this->respond(['status' => true]);
    }
    
    public function ajax_add_step_3()
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }

        if (! has_permission('NotificationLarkMessenger', 'can_add')) {
            return $this->failNotFound();
        }
        
        $session = service('session');
        $integrationData = $session->get('lark_messenger_integration');
        
        if (! $session->get('lark_messenger_integration')) {
            return $this->respond(['status' => false, 'redirect_to' => base_url('larkmessenger/add')]);
        }
        
        $data = [
            'is_template_custom' => $this->request->getVar('is_template_custom'),
            'template_custom' => $this->request->getVar('template_custom'),
            'template_name' => $this->request->getVar('template_name'),
            'hide_accumulated' => $this->request->getVar('hide_accumulated'),
            'hide_details_link' => $this->request->getVar('hide_details_link')
        ];

        if (!in_array($data['template_name'], ['template_1', 'template_2', 'template_3'])) {
            return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng tải lại trang']);
        }

        if ($data['is_template_custom'] && !trim($data['template_custom'])) {
            return $this->fail(['template_custom' => 'Nội dung tin nhắn là bắt buộc']);
        }

        if (!$integrationData
        || !array_key_exists('bot_webhook_url', $integrationData)
        || !array_key_exists('description', $integrationData)
        || !array_key_exists('transaction_type', $integrationData)
        || !array_key_exists('links', $integrationData)
        || !array_key_exists('store_ids', $integrationData)) {
            return redirect()->to('lark-messenger/add?step=1');
        }

        $data = array_merge($data, $integrationData);
        
        $storeFeature = new StoreFeature;
        $storeFeature->withCompanyContext($this->user_session['company_id']);

        $storeFeature->linkStoresToLarkMessenger($data['store_ids'], $data);
        
        add_user_log(['data_id' => 0, 'company_id' => $this->user_session['company_id'], 'data_type' => 'notification_lark_messenger_add', 'description' => 'Thêm tích hợp thông báo Lark Messenger', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);
        
        return $this->respond(['status' => true]);
    }
    
    public function edit(string $larkMessengerId = '')
    {
        if (! has_permission('NotificationLarkMessenger', 'can_edit')) {
            show_404();
        }
        
        $data = [
            'page_title' => 'Lark Messenger',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];
        
        $larkMessengerId = trim($larkMessengerId);
        
        if (!is_string($larkMessengerId)) {
            return $this->failNotFound();
        }
        
        $tab = $this->request->getGet('tab') ?? '';
        
        if ($tab && !in_array($tab, ['store', 'custom-message'])) {
            return redirect()->to(base_url('larkmessenger/edit/' . $larkMessengerId));
        }
        
        $data['tab'] = $tab;
        
        $storeFeature = new StoreFeature;
        $storeFeature->withCompanyContext($this->user_session['company_id']);
        
        $data['lark_messenger'] = $storeFeature->larkMessengerBuilder()
            ->where('id', $larkMessengerId)
            ->first();
            
        if (!$data['lark_messenger']) {
            show_404();
        }
        
        $data['stores'] = $storeFeature->getLinkedLarkMessengerStores($data['lark_messenger']->bot_webhook_url, $data['lark_messenger']->transaction_type);
        
        if (!count($data['stores'])) {
            return $this->failNotFound();
        }
        
        echo theme_view('templates/autopay/header', $data);
        
        if ($data['tab'] === 'general') {
            echo theme_view('lark-messenger/edit/general', $data);
        } else if ($data['tab'] === 'store') {
            $data['linkable_stores'] = $storeFeature->getLinkableLarkMessengerStores($data['lark_messenger']->bot_webhook_url, $data['lark_messenger']->transaction_type);
            echo theme_view('lark-messenger/edit/store', $data);
        } else if ($data['tab'] === 'custom-message') {
            echo theme_view('lark-messenger/edit/custom-message', $data);
        } else {
            echo theme_view('lark-messenger/edit/general', $data);
        }
        
        echo theme_view('templates/autopay/footer', $data);
    }
    
    public function ajax_edit(string $larkMessengerId = '')
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }

        $larkMessengerId = trim($larkMessengerId);
        
        if (!is_string($larkMessengerId)) {
            return $this->failNotFound();
        }
        
        if (! has_permission('NotificationLarkMessenger', 'can_edit')) {
            return $this->failNotFound();
        }
        
        $storeFeature = new StoreFeature;
        $storeFeature->withCompanyContext($this->user_session['company_id']);
        
        $larkMessenger = $storeFeature->larkMessengerBuilder()->where('id', $larkMessengerId)->first();
            
        if (!$larkMessenger) {
            return $this->failNotFound();
        }
        
        $stores = $storeFeature->getLinkedLarkMessengerStores($larkMessenger->bot_webhook_url, $larkMessenger->transaction_type);
        
        if (!count($stores)) {
            return $this->failNotFound();
        }
        
        $data = [
            'bot_webhook_url' => trim($this->request->getPost('bot_webhook_url')),
            'description' => trim($this->request->getPost('description')),
            'transaction_type' => trim($this->request->getPost('transaction_type')),
            'is_template_custom' => $this->request->getVar('is_template_custom'),
            'template_custom' => $this->request->getVar('template_custom'),
            'template_name' => $this->request->getVar('template_name'),
            'hide_accumulated' => $this->request->getVar('hide_accumulated'),
            'hide_details_link' => $this->request->getVar('hide_details_link'),
            'active' => $this->request->getVar('active')
        ];

        $rules = [
            'bot_webhook_url' => ['permit_empty', 'regex_match[/^https:\/\/open\.larksuite\.com\/open-apis\/bot\/v2\/hook\/[a-zA-Z0-9\-]{36}$/]'],
            'description' => ['permit_empty', 'max_length[100]'],
            'transaction_type' => ['permit_empty','in_list[All,In_only,Out_only]'],
        ];
        
        if (! $this->validateData($data, $rules)) {
            return $this->fail($this->validator->getErrors());
        }
        
        if (($larkMessenger->bot_webhook_url != $data['bot_webhook_url'] || $larkMessenger->transaction_type != $data['transaction_type']) && $storeFeature->larkMessengerBuilder()->where('bot_webhook_url', $data['bot_webhook_url'])->where('transaction_type', $data['transaction_type'])->countAllResults() > 0) {
            return $this->respond(['status' => false, 'message' => 'Tích hợp Lark Messenger đã tồn tại, vui lòng kiểm tra lại.']);
        }
        
        if ($data['bot_webhook_url'] && $larkMessenger->bot_webhook_url != $data['bot_webhook_url']) {
            $session = service('session');
            $integrationData = $session->get('lark_messenger_integration');
            
            $connected = isset($integrationData) && $integrationData['is_connected'] && $integrationData['bot_webhook_url'] == $data['bot_webhook_url'];
            
            if (!$connected) {
                return $this->respond(['status' => false, 'message' => 'Bot Webhook URL chưa được đảm bảo kết nối, vui lòng thử kết nối trước khi thay đổi.']);
            }
        }
        
        if ($data['template_name'] && !in_array($data['template_name'], ['template_1', 'template_2', 'template_3'])) {
            return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng tải lại trang']);
        }

        if ($data['is_template_custom'] && !trim($data['template_custom'])) {
            return $this->fail(['template_custom' => 'Nội dung tin nhắn là bắt buộc']);
        }
        
        $updated = $storeFeature->editStoreLarkMessenger($larkMessenger->bot_webhook_url, $larkMessenger->transaction_type, $data);
        
        if ($updated) {
            add_user_log(['data_id' => 0, 'company_id' => $this->user_session['company_id'], 'data_type' => 'notification_lark_messenger_add', 'description' => 'Cập nhật tích hợp thông báo Lark Messenger', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);
        }
        
        return $this->respond(['status' => true, 'message' => 'Cập nhật thành công']);
    }

    public function ajax_test_lark_messenger_connection()
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }

        if (! has_permission('NotificaitonLarkMessenger', 'can_add')) {
            return $this->failNotFound();
        }

        $data = [
            'bot_webhook_url' => trim($this->request->getVar('bot_webhook_url')),
        ];
        
        $rules = [
            'bot_webhook_url' => ['required', 'regex_match[/^https:\/\/open\.larksuite\.com\/open-apis\/bot\/v2\/hook\/[a-zA-Z0-9\-]{36}$/]'],
        ];

        if (! $this->validateData($data, $rules)) {
            return $this->fail($this->validator->getErrors());
        }

        return $this->testLarkMessengerConnection($data['bot_webhook_url']);
    }
    
    public function ajax_test_lark_messenger_message(string $larkMessengerId = '')
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }

        if (!has_permission('NotificationLarkMessenger', 'can_add')) {
            return $this->failNotFound();
        }

        $data = [
            'template_custom' => $this->request->getVar('template_custom'),
            'is_template_custom' => $this->request->getVar('is_template_custom'),
            'hide_accumulated' => $this->request->getVar('hide_accumulated'),
            'hide_details_link' => $this->request->getVar('hide_details_link')
        ];

        if ($data['is_template_custom'] && !trim($data['template_custom'])) {
            return $this->fail(['template_custom' => 'Nội dung tin nhắn là bắt buộc']);
        }

        if ($larkMessengerId) {
            $storeFeature = new StoreFeature;
            $storeFeature->withCompanyContext($this->user_session['company_id']);
            
            $integrationData = $storeFeature->larkMessengerBuilder()->where('id', $larkMessengerId)->first();
                
            if (!$integrationData) {
                return $this->failNotFound();
            }
            
            $integrationData = (array) $integrationData;
        } else {
            $session = service('session');
    
            $integrationData = $session->get('lark_messenger_integration');
            
            if (!is_array($integrationData) 
            || !array_key_exists('bot_webhook_url', $integrationData) 
            || !array_key_exists('description', $integrationData) 
            || !array_key_exists('transaction_type', $integrationData)
            || !array_key_exists('store_ids', $integrationData)) {
                return $this->respond(['status' => false, 'redirect_to' => base_url('lark-messenger/add')]);
            }
        }

        $transactionDetails = (object) [
            'id' => '103896',
            'accumulated' => '********',
            'amount_in' => '********',
            'amount_out' => 0,
            'account_number' => '*************',
            'brand_name' => 'Vietcombank',
            'transaction_content' => 'TRAN ANH DUONG chuyen tien',
            'code' => 'HD1029148',
            'reference_number' => '171298.050723.020002',
            'transaction_date' => '2023-07-05 13:59:48',
            'sub_account' => null,
        ];

        $larkMessengerDetails = (object) [
            'template_custom' => $data['template_custom']
        ];

        if (!$data['is_template_custom']) {
            $accumulated = "

⛳️ Số dư: " . number_format($transactionDetails->accumulated) . ' đ
';
            $detailsLink = '
            
Xem chi tiết tại https://my.sepay.vn
';

            if ($data['hide_accumulated'])
                $accumulated = "
";

            if ($data['hide_details_link'])
                $detailsLink = "
";

            $message = "--------- - ID: " . $transactionDetails->id . " ---------
Có giao dịch mới:

✳️ Tiền vào: " . number_format($transactionDetails->amount_in) . " đ

➖ Tiền ra: " . number_format($transactionDetails->amount_out) . " đ" . $accumulated . 
"
⛪️ Tài khoản chính: " . $transactionDetails->account_number . " " . $transactionDetails->brand_name . "

ℹ️ Nội dung thanh toán: " . $transactionDetails->transaction_content . "

#️⃣ Mã code thanh toán: " . $transactionDetails->code . "

⚓️ Mã tham chiếu: " . $transactionDetails->reference_number . "

⏰ Giao dịch lúc: " . $transactionDetails->transaction_date . "" .
$detailsLink .
"
------------------------------";
        } else {
            $message = model(NotificationLarkMessengerModel::class)->getMessage($transactionDetails, $larkMessengerDetails);
        }

        return $this->testLarkMessengerConnection($integrationData['bot_webhook_url'], $message);
    }

    protected function testLarkMessengerConnection($botWebhookUrl, $message = null)
    {
        $notificationLarkMessengerQueueModel = model(NotificationLarkMessengerQueueModel::class);
        $message = $message ?? "✅ Kết nối thành công!";
        $result = $notificationLarkMessengerQueueModel->sendMessage($message, $botWebhookUrl, 'html');

        if (isset($result['response'])) {
            $response = json_decode($result['response']);

            $session = service("session");
            $session_data_integration = $session->get('lark_messenger_integration');
            if (is_object($response) && isset($response->StatusMessage) && $response->StatusMessage == "success") {

                $session_data_integration['is_connected'] = 1;
                $session_data_integration['bot_webhook_url'] = $botWebhookUrl;
                
                $session->set('lark_messenger_integration', $session_data_integration);

                return $this->respond(['status' => true]);
            } else {
                $session_data_integration['is_connected'] = 0;
                $session_data_integration['bot_webhook_url'] = $botWebhookUrl;
                $session->set('lark_messenger_integration', $session_data_integration);

                $message = 'Không thể kết nối tới nhóm Lark Messenger, vui lòng kiểm tra';

                return $this->respond([
                    'status' => false, 
                    'message' => $message
                ]);
            }
        }

        return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ']);
    }
    
    public function ajax_sync_store_to_lark_messenger(string $larkMessengerId = '')
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }
        
        if (! has_permission('NotificationLarkMessenger', 'can_edit')) {
            return $this->failNotFound();
        }
        
        $larkMessengerId = trim($larkMessengerId);
        
        if (!is_string($larkMessengerId)) {
            return $this->failNotFound();
        }
        
        $storeFeature = new StoreFeature;
        $storeFeature->withCompanyContext($this->user_session['company_id']);
        
        $larkMessenger = $storeFeature->larkMessengerBuilder()->where('id', $larkMessengerId)->first();
            
        if (!$larkMessenger) {
            return $this->failNotFound();
        }
        
        $linkedStores = $storeFeature->getLinkedLarkMessengerStores($larkMessenger->bot_webhook_url, $larkMessenger->transaction_type);
        
        if (!count($linkedStores)) {
            return $this->failNotFound();
        }
        
        $storeIds = $this->request->getVar('store_ids');

        if (!is_array($storeIds) || empty($storeIds)) {
            return $this->respond(['status' => false, 'message' => 'Vui lòng giữ lại ít nhất một cửa hàng']);
        }
        
        $linkableStoreIds = array_values(array_diff($storeIds, array_column($linkedStores, 'id')));
        $unlinkableStoreIds = array_values(array_diff(array_column($linkedStores, 'id'), $storeIds));
        
        $storeFeature = new StoreFeature;
        $storeFeature->withCompanyContext($this->user_session['company_id']);
       
        if (count($linkableStoreIds)) {
            $storeFeature->linkStoresToLarkMessenger($linkableStoreIds, (array) $larkMessenger);
        }
        
        if (count($unlinkableStoreIds)) {
            $storeFeature->unlinkStoresToLarkMessenger($unlinkableStoreIds, $larkMessenger->bot_webhook_url, $larkMessenger->transaction_type);
        }
        
        if ($storeFeature->larkMessengerBuilder()->where('id', $larkMessengerId)->countAllResults() === 0) {
            $larkMessenger = $storeFeature->larkMessengerBuilder()
                ->where('bot_webhook_url', $larkMessenger->bot_webhook_url)
                ->where('transaction_type', $larkMessenger->transaction_type)
                ->first();
                
            if (!$larkMessenger) {
                return $this->respond([
                    'status' => false,
                    'message' => 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ'
                ]);
            }
            
            return $this->respond([
                'status' => true,
                'message' => 'Cập nhật thành thành công',
                'redirect_url' => base_url('larkmessenger/edit/' . $larkMessenger->id)
            ]);
        }
        
        return $this->respond([
            'status' => true,
            'message' => 'Cập nhật thành thành công',
        ]);
    }
    
    public function ajax_delete(string $larkMessengerId = '')
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }
        
        if (! has_permission('NotificationLarkMessenger', 'can_edit')) {
            return $this->failNotFound();
        }
        
        $larkMessengerId = trim($larkMessengerId);
        
        if (!is_string($larkMessengerId)) {
            return $this->failNotFound();
        }
        
        $storeFeature = new StoreFeature;
        $storeFeature->withCompanyContext($this->user_session['company_id']);
        
        $larkMessenger = $storeFeature->larkMessengerBuilder()->where('id', $larkMessengerId)->first();
            
        if (!$larkMessenger) {
            return $this->failNotFound();
        }
        
        $storeFeature->deleteStoreLarkMessenger($larkMessenger->bot_webhook_url, $larkMessenger->transaction_type);
        
        add_user_log(['data_id' => 0, 'company_id' => $this->user_session['company_id'], 'data_type' => 'notification_lark_delete', 'description' => 'Gỡ tích hợp thông báo Lark Messenger', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);
        
        return $this->respond([
            'status' => true,
            'message' => 'Đã xóa tích hợp Lark Messenger',
        ]);
    }

    public function ajax_unlink_store_to_lark_messenger(string $larkMessengerId = '')
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }
        
        if (! has_permission('NotificationLarkMessenger', 'can_edit')) {
            return $this->failNotFound();
        }
        
        $larkMessengerId = trim($larkMessengerId);
        
        if (!is_string($larkMessengerId)) {
            return $this->failNotFound();
        }
        
        $storeId = $this->request->getVar('store_id');
        
        if (!$storeId || !is_numeric($storeId)) {
            return $this->failNotFound();
        }
        
        $storeFeature = new StoreFeature;
        $storeFeature->withCompanyContext($this->user_session['company_id']);
        
        $larkMessenger = $storeFeature->larkMessengerBuilder()->where('id', $larkMessengerId)->first();
            
        if (!$larkMessenger) {
            return $this->failNotFound();
        }
        
        $storeFeature->unlinkStoresToLarkMessenger([$storeId], $larkMessenger->bot_webhook_url, $larkMessenger->transaction_type);
        
        return $this->respond([
            'status' => true,
            'message' => 'Đã gỡ tích hợp Lark Messenger khỏi cửa hàng',
        ]);
    }

    public function ajax_link_store_to_lark_messengers(string $storeId = '')
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }
        
        if (! has_permission('NotificationLarkMessenger', 'can_edit')) {
            return $this->failNotFound();
        }
        
        $storeId = trim($storeId);
        
        if (!$storeId || !is_numeric($storeId)) {
            return $this->failNotFound();
        }
        
        $larkMessengerIds = $this->request->getVar('lark_messenger_ids');
        
        if (!$larkMessengerIds || !is_array($larkMessengerIds) || empty($larkMessengerIds)) {
            return $this->fail('Vui lòng chọn ít nhất một nhóm Lark Messenger', 400);
        }
        
        $storeFeature = new StoreFeature;
        $storeFeature->withCompanyContext($this->user_session['company_id']);
        
        $linkedCount = 0;
        $errors = [];
        
        foreach ($larkMessengerIds as $larkMessengerId) {
            $larkMessenger = $storeFeature->larkMessengerBuilder()->where('id', $larkMessengerId)->first();
                
            try {
                $storeFeature->linkStoresToLarkMessenger(
                    [$storeId], 
                    (array) $larkMessenger
                );
                $linkedCount++;
            } catch (\Exception $e) {
            }
        }
        
        if ($linkedCount === 0) {
            return $this->fail('Không thể liên kết bất kỳ nhóm Lark Messenger nào');
        }
        
        return $this->respond([
            'status' => true,
            'message' => "Đã liên kết {$linkedCount} nhóm Lark Messenger với cửa hàng",
            'errors' => $errors
        ]);
    }
}
