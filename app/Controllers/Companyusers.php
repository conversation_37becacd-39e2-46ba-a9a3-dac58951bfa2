<?php

namespace App\Controllers;
use App\Models\BankAccountModel;
use App\Models\TransactionsModel;
use App\Models\CompanyUserModel;
use App\Models\PersonalAccessTokenModel;
use App\Models\UserModel;
use App\Models\UserPermissionFeatureModel;

use CodeIgniter\Controller;

class Companyusers extends BaseController
{
    public function index()
    { 
        $data = [
            'page_title' => 'Người dùng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        if(!in_array($this->company_details->role,['Admin','SuperAdmin']))
            show_404();

        $userModel = model(UserModel::class);
      
        echo theme_view('templates/autopay/header',$data);
        echo view('companyusers/index',$data);
        echo theme_view('templates/autopay/footer',$data);

    }

    public function ajax_users_list() {
        if(!in_array($this->company_details->role,['Admin','SuperAdmin']))
            show_404();
     
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        $companyUserModel = model(CompanyUserModel::class);

        $users = $companyUserModel->getDatatables($this->user_session['company_id']);
         
        $data = array();

        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');

        foreach ($users as $user) {

            $no++;
            $row = array();
            
            $actions_btn = '';


            if(!in_array($user->role,['Admin','SuperAdmin']))
                $actions_btn = "<a href='" . base_url('userpermission/permission/' . $user->id) ."' class='btn btn-sm btn-outline-primary ms-2 me-1 mt-2'><i class='bi bi-key'></i> Phân quyền</a>";

            if($user->role != 'SuperAdmin' && $this->user_details->id != $user->id) {
                $actions_btn = $actions_btn . "<a href='javascript:;' onclick='edit_user(" . $user->id. ")' class='btn btn-sm btn-outline-warning ms-2 me-1 mt-2'><i class='bi bi-pencil'></i> Sửa</a>";
                $actions_btn = $actions_btn .  "<a href='javascript:;' onclick='delete_user(" . $user->id. ")' class='btn btn-sm btn-outline-danger ms-2 mt-2'><i class='bi bi-trash3'></i> Xóa</a>";
            }                
            
            if($user->role == "Admin") 
                $admin_badge = "<div class='mt-1 text-end'><span data-bs-toggle='tooltip' data-bs-title='Admin có toàn quyền, trừ quyền chỉnh sửa Super Admin' class='badge bg-primary'><i class='bi bi-star'></i> Admin</span></div>";
            else if($user->role == "SuperAdmin") 
                $admin_badge = "<div class='mt-1 text-end'><span  data-bs-toggle='tooltip' data-bs-title='Super Admin có toàn quyền' class='badge bg-danger'><i class='bi bi-star'></i> Super Admin</span></div>"; 
            else
                $admin_badge = '';
            
              
            
            $row[] = $no;
            $row[] = esc($user->id);
            $row[] = "<img src='" . get_gravatar($user->email,32) . "' class='avatar img-fluid rounded-circle me-1' alt=''> " . esc( $user->lastname . ' ' . $user->firstname) . $admin_badge;
            $row[] = esc($user->email);
            $row[] = esc($user->full_name . ' (' . $user->short_name . ')');
            if($user->active == 1)
                $row[] = "<span class='badge bg-success'>Hoạt động</span>";
            else
                $row[] = "<span class='badge bg-danger'>Tạm khóa</span>";
            $row[] = esc($user->created_at);
            $row[] = $actions_btn;
       
            $data[] = $row;
        }
 
        $output = array(
            "draw" => $draw,
            "recordsTotal" => $companyUserModel->countAll($this->user_session['company_id']),
            "recordsFiltered" => $companyUserModel->countFiltered($this->user_session['company_id']),
            "data" => $data,
        );
        return $this->response->setJSON($output);
    
    
    }

    function ajax_user_add() {
        if(!in_array($this->company_details->role,['Admin','SuperAdmin']))
            show_404();

        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        if($this->user_session['company_id'] == 5) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Tính năng không khả dụng ở tài khoản Demo"));
        }
    
        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'firstname' => ['label' => 'Tên', 'rules' =>  "required|max_length[50]"],
            'lastname' => ['label' => 'Họ và đệm', 'rules' => "required|max_length[100]"],
            'password' => ['label' => 'Mật khẩu', 'rules' => "required|min_length[6]|max_length[50]"],
            'email' => ['label' => 'Email', 'rules' => "required|valid_email|is_unique[tb_autopay_user.email]"],
            'active' => "required|in_list[0,1]",
            'role' => "required|in_list[User,Admin]",

        ],[
            "email" => ['is_unique' => 'Email bạn điền đã tồn tại trên hệ thống']
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }

        $companyUserModel = model(CompanyUserModel::class);
        $company = $companyUserModel->where(['user_id' => $this->user_details->id])->first();
        if(!is_object($company))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Không tìm thấy công ty liên kết với tài khoản của bạn"));
        

        $model = model(UserModel::class);

        $user_data = [
            'firstname' => mb_convert_case(xss_clean($this->request->getVar('firstname')), MB_CASE_TITLE, "UTF-8"),
            'lastname' => mb_convert_case(xss_clean($this->request->getVar('lastname')), MB_CASE_TITLE, "UTF-8"),
            'email' => $this->request->getVar('email'),
            'password' => password_hash(trim($this->request->getVar('password')), PASSWORD_DEFAULT),
            'active' => $this->request->getVar('active'),
        ];

        $new_user_id = $model->insert($user_data);

        if(!is_numeric($new_user_id))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Không thể thêm mới người dùng do lỗi hệ thống"));

        add_user_log(array('data_id'=>$new_user_id,'company_id' => $this->user_session['company_id'], 'data_type'=>'admin_create_user','description'=>'Thêm tài khoản người dùng','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));

        $company_user_data = ['company_id' => $this->user_session['company_id'], 'user_id' => $new_user_id,'role' => $this->request->getVar('role')];
        $company_user_result = $companyUserModel->insert($company_user_data);

        $userPermissionFeatureModel = model(UserPermissionFeatureModel::class);

        $userPermissionFeatureModel->initialize_permission($new_user_id, $this->user_session['company_id']);
        
        if($company_user_result) {

            set_alert('success','Thêm người dùng thành công');
            
            return $this->response->setJSON(array("status"=>true,"user_id" => $new_user_id));
        } else {
            return $this->response->setJSON(array("status"=>false,"message"=>"Người dùng đã được khởi tạo nhưng không thể thêm vào công ty của bạn do lỗi hệ thống."));
        }
    }


    public function ajax_user_update() {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!in_array($this->company_details->role,['Admin','SuperAdmin']))
            show_404();

       
        $validation =  \Config\Services::validation();

        helper('text');

      
        $validate_data = [
            'id' => "required|integer|is_natural",
            'firstname' => ['label' => 'Tên', 'rules' =>  "required|max_length[50]"],
            'lastname' => ['label' => 'Họ và đệm', 'rules' => "required|max_length[100]"],
            'active' => "required|in_list[0,1]",
            'role' => "required|in_list[User,Admin]",
        ];


        $password = $this->request->getVar('password');
        if($password != "")
            $validate_data['password'] = "required|min_length[6]|max_length[200]";

        if(! $this->validate($validate_data))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));        


        $user_id = $this->request->getPost('id');

        $userModel = model(UserModel::class);
        $companyModel = model(CompanyModel::class);
        $company = $companyModel->find($this->user_session['company_id']);

        $companyUserModel = model(CompanyUserModel::class);
        $company_user = $companyUserModel->where(['company_id' => $this->user_session['company_id'], 'user_id' => $user_id])->get()->getRow();

        if(!is_object($company_user))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Người dùng này không thuộc công ty bạn quản lý"));

        if($company_user->role == "SuperAdmin")
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Bạn không thể chỉnh sửa Super Admin"));

        $user_data = [
            'firstname' => mb_convert_case(xss_clean($this->request->getVar('firstname')), MB_CASE_TITLE, "UTF-8"),
            'lastname' => mb_convert_case(xss_clean($this->request->getVar('lastname')), MB_CASE_TITLE, "UTF-8"),
            'active' => $this->request->getVar('active'),
        ];

        if($user_id == $this->user_details->id && $user_data['active'] == 0) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Bạn không thể khóa tài khoản của chính mình"));
        }

        if ($password != "") {
            $user_data['password'] = password_hash(trim($this->request->getVar('password')), PASSWORD_DEFAULT);
            $user_data['last_password_changed'] = date('Y-m-d H:i:s');
            
            try {
                $userExists = model(UserModel::class)->where('id', $user_id)->countAllResults();
                
                if ($userExists > 0) {
                    model(PersonalAccessTokenModel::class)
                        ->where('user_id', $user_id)
                        ->where('company_id', $this->user_session['company_id'])
                        ->delete();
                }
            } catch (\Exception $e) {
                log_message('error', '[RevokePersonalAccessToken] ' . $e->getMessage() . ' - ' . $e->getTraceAsString());
            }
        }


        $result = $userModel->set($user_data)->where("id",$user_id)->update();

        $role = $this->request->getVar('role');

        if($company_user->role != $role) {
            $companyUserModel->set(['role' => $role])->where(['company_id' => $this->user_session['company_id'], 'user_id' => $user_id])->update();
        }
        
        if($result) { 
            return $this->response->setJSON(array("status"=>true));
        } else {
            return $this->response->setJSON(array("status"=>false,"message"=>"Không thể cập nhật nguời dùng!"));
        }
    }

    public function ajax_get_user($user_id='') {
        
        if(!is_numeric($user_id)) 
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"ID người dùng không hợp lệ"));

        if(!in_array($this->company_details->role,['Admin','SuperAdmin']))
            show_404();
        
        $userModel = model(UserModel::class);
        $companyModel = model(CompanyModel::class);
        $company = $companyModel->find($this->user_session['company_id']);

        $companyUserModel = model(CompanyUserModel::class);
        $company_user = $companyUserModel->where(['company_id' => $this->user_session['company_id'], 'user_id' => $user_id])->get()->getRow();

        if(!is_object($company_user))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Người dùng này không thuộc công ty bạn quản lý"));

        if($company_user->role == "SuperAdmin")
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Bạn không thể chỉnh sửa Super Admin"));

        $result = $userModel->select("tb_autopay_user.id, tb_autopay_user.email,tb_autopay_user.firstname,tb_autopay_user.lastname,tb_autopay_user.active,tb_autopay_company_user.role")->join("tb_autopay_company_user","tb_autopay_company_user.user_id=tb_autopay_user.id")->where(['tb_autopay_user.id'=>$user_id,"tb_autopay_company_user.company_id" => $this->user_session['company_id']])->get()->getRow();
        
        if($result)
            return $this->response->setJSON(["status"=>TRUE, "data"=>$result]);
        else
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy người dùng này"));
    }

    public function ajax_user_delete() {
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        if(!in_array($this->company_details->role,['Admin','SuperAdmin']))
            show_404();

        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'id' => 'required|integer|is_natural',
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }
        $user_id = $this->request->getPost('id');

        $userModel = model(UserModel::class);
        $companyModel = model(CompanyModel::class);
        $company = $companyModel->find($this->user_session['company_id']);


        if($user_id == $this->user_details->id) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Bạn không thể xóa tài khoản của chính mình"));
        }

        $companyUserModel = model(CompanyUserModel::class);
        $company_user = $companyUserModel->where(['company_id' => $this->user_session['company_id'], 'user_id' => $user_id])->get()->getRow();

        if(!is_object($company_user))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Người dùng này không thuộc công ty bạn quản lý"));

        if($company_user->role == "SuperAdmin")
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Bạn không thể xóa Super Admin"));

        // delete user company
        $companyUserModel->where(['user_id' => $user_id, 'company_id' => $this->user_session['company_id']])->delete();

        try {
            $userExists = model(UserModel::class)->where('id', $user_id)->countAllResults();
            
            if ($userExists > 0) {
                model(PersonalAccessTokenModel::class)
                    ->where('user_id', $user_id)
                    ->where('company_id', $this->user_session['company_id'])
                    ->delete();
            }
        } catch (\Exception $e) {
            log_message('error', '[RevokePersonalAccessToken] ' . $e->getMessage() . ' - ' . $e->getTraceAsString());
        }
        
        // delete user
        $userModel->delete(['id' => $user_id]);
    
        return $this->response->setJSON(array("status"=>true));
    }

 
}