<?php

namespace App\Controllers\Coreapi;

use App\Actions\BankAccount\FilterTransactionAction;
use Config\Bidv;
use Exception;
use App\Models\CompanyModel;
use App\Models\SmsParserModel;
use App\Models\BankAccountModel;
use App\Models\TransactionsModel;
use CodeIgniter\API\ResponseTrait;
use App\Models\BankSubAccountModel;
use PhpAmqpLib\Message\AMQPMessage;
use App\Models\BidvNotificationModel;
use App\Models\BankAccountCashflowModel;
use App\Models\BidvNotificationRawModel;
use App\Models\NotificationTelegramModel;
use App\Models\NotificationLarkMessengerModel;
use App\Actions\Shopify\ExecuteShopifyWebhooksAction;
use App\Actions\GoogleSheets\ExecuteGoogleSheetsWebhooksAction;
use App\Actions\Mobile\PushMobileTransactionNotificationQueueAction;
use CodeIgniter\RESTful\ResourceController;
use App\Actions\PayCodeDetector;

use App\Actions\OutputDevice\ExecuteOutputDevice;

class BidvPers extends ResourceController
{
    use ResponseTrait;

    protected Bidv $config;

    protected $debug;

    private $accessCode;

    protected $vaPrefix;

    protected $enableWebhookOffload;

    protected $helpers = ['general'];

    public function __construct()
    {
        $config = config(Bidv::class);
        $this->config = $config;
        $this->debug = $config->personalNotifyDebug ?? false;
        $this->accessCode = $config->personalNotifyAccessCode;
        $this->vaPrefix = $config->personalVaPrefix;
        $this->enableWebhookOffload = $config->enableBidvWebhookOffload;
    }

    public function getbill()
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }

        $rawData = [
            'customer_id' => $this->request->getVar('customer_id'),
            'checksum' => $this->request->getVar('checksum'),
        ];

        $rules = [
            'customer_id' => ['required', 'string', "regex_match[/^{$this->vaPrefix}[0-9A-Z]+$/]"],
            'checksum' => ['required', 'string'],
        ];

        if (! $this->validateData($rawData, $rules)) {
            if ($this->debug) {
                log_message('error', '[BIDV personal getbill] Bad request: ' . json_encode($this->validator->getErrors()) . ' - ' . json_encode($rawData));
            }

            return $this->respondStandardResponse(200, '001', implode(', ', $this->validator->getErrors()));
        }

        $validChecksum = hash('sha256', "{$this->accessCode}|{$rawData['customer_id']}");

        if ($rawData['checksum'] !== $validChecksum) {
            return $this->respondStandardResponse(200, '004', 'Checksum is invalid');
        }

        $bankSubAccountModel = model(BankSubAccountModel::class);
        $bankSubAccount = $bankSubAccountModel
            ->select(['tb_autopay_bank_sub_account.sub_account as sub_account', 'tb_autopay_bank_sub_account.sub_holder_name as sub_holder_name', 'tb_autopay_bank_sub_account.va_active as va_active', 'tb_autopay_bank_sub_account.bank_account_id'])
            ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id = tb_autopay_bank_sub_account.bank_account_id')
            ->join('tb_autopay_bidv_enterprise_account', 'tb_autopay_bidv_enterprise_account.bank_account_id = tb_autopay_bank_account.id', 'left')
            ->where([
                'tb_autopay_bidv_enterprise_account.id' => null,
                'tb_autopay_bank_account.bank_id' => 9,
                'tb_autopay_bank_account.bank_api' => 1,
                'tb_autopay_bank_account.bank_api_connected' => 1,
                'tb_autopay_bank_account.merchant_id' => null,
                'tb_autopay_bank_sub_account.va_active' => 1,
                'tb_autopay_bank_sub_account.sub_account' => $rawData['customer_id'],
            ])
            ->get()->getRow();

        if (!$bankSubAccount) {
            return $this->respondStandardResponse(200, '011', 'Customer ID is invalid / not exist');
        }

        // SIMULATE BILL ID
        $lastTransaction = slavable_model(TransactionsModel::class, 'Coreapi')->where(['bank_account_id' => $bankSubAccount->bank_account_id])->orderBy('id', 'DESC')->first();
        // SIMULATE BILL ID

        return $this->respondStandardResponse(200, '000', 'success', [
            'customer_id' => $bankSubAccount->sub_account,
            'customer_name' => $bankSubAccount->sub_holder_name,
            'code' => is_object($lastTransaction) ? $lastTransaction->id : '1',
        ]);
    }

    public function paybill()
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }
        
        $rawData = [
            'trans_id' => trim($this->request->getVar('trans_id')),
            'bill_id' => trim($this->request->getVar('bill_id')),
            'code' => trim($this->request->getVar('code')),
            'trans_date' => trim($this->request->getVar('trans_date')),
            'customer_id' => trim($this->request->getVar('customer_id')),
            'amount' => trim($this->request->getVar('amount')),
            'remark' => trim($this->request->getVar('remark')),
            'checksum' => trim($this->request->getVar('checksum')),
        ];

        $rules = [
            'trans_id' => ['required', 'string'],
            'bill_id' => ['required', 'string'],
            'code' => ['permit_empty', 'string'],
            'trans_date' => ['required', 'string', 'regex_match[/^\d{14}$/]'],
            'customer_id' => ['required', 'string', 'regex_match[/^[0-9A-Z]+$/]'],
            'amount' => ['required', 'integer', 'greater_than[0]'],
            'remark' => ['required', 'string'],
            'checksum' => ['required', 'string'],
        ];

        if (! $this->validateData($rawData, $rules)) {
            if ($this->debug) {
                log_message('error', '[BIDV personal getbill] Bad request: ' . json_encode($this->validator->getErrors()) . ' - ' . json_encode($rawData));
            }

            return $this->respondStandardResponse(200, '001', implode(', ', $this->validator->getErrors()));
        }

        $validChecksum = hash('sha256', "{$this->accessCode}|{$rawData['trans_id']}|{$rawData['trans_date']}|{$rawData['customer_id']}|{$rawData['amount']}");

        if ($rawData['checksum'] !== $validChecksum) {
            return $this->respondStandardResponse(200, '004', 'Checksum is invalid');
        }

        $bidvNotificationRawModel = model(BidvNotificationRawModel::class);
        $bidvNotificationModel = model(BidvNotificationModel::class);
        $bidvNotificationRawId = $bidvNotificationRawModel->insert(['payload' => json_encode($rawData), 'ip' =>$this->request->getIPAddress()]);

        $duplicatedTransaction = $bidvNotificationModel->where('trans_id', $rawData['trans_id'])->countAllResults();
        if ($duplicatedTransaction) {
            if ($this->debug) log_message('error', "BIDV Notification Error: Transaction Duplicated - Raw #" . $bidvNotificationRawId);

            return $this->respondStandardResponse(200, '023', 'Transaction is exist');
        }

        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $bankSubAccountDetails = $bankSubAccountModel
            ->select(['tb_autopay_bank_sub_account.id', 'tb_autopay_bank_sub_account.sub_account as sub_account', 'tb_autopay_bank_sub_account.sub_holder_name as sub_holder_name', 'tb_autopay_bank_sub_account.va_active as va_active', 'tb_autopay_bank_account.company_id as company_id', 'tb_autopay_bank_account.account_number as account_number', 'tb_autopay_bank_account.merchant_id as merchant_id', 'tb_autopay_bank_account.id as bank_account_id', 'tb_autopay_bank_account.bank_id as bank_id', 'tb_autopay_bank_account.bank_sms as bank_sms'])
            ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id = tb_autopay_bank_sub_account.bank_account_id')
            ->join('tb_autopay_bidv_enterprise_account', 'tb_autopay_bidv_enterprise_account.bank_account_id = tb_autopay_bank_account.id', 'left')
            ->where([
                'tb_autopay_bidv_enterprise_account.id' => null,
                'tb_autopay_bank_account.bank_id' => 9,
                'tb_autopay_bank_account.merchant_id' => null,
                'tb_autopay_bank_sub_account.sub_account' => $rawData['customer_id'],
                'tb_autopay_bank_sub_account.active' => 1,
            ])
            ->get()->getRow();

        if (!$bankSubAccountDetails) {
            return $this->respondStandardResponse(200, '011', 'Customer ID is invalid / not exist');
        }

        $bankAccountDetails = $bankAccountModel->where('id', $bankSubAccountDetails->bank_account_id)->where('active', true)->first();

        if (!$bankAccountDetails) {
            return $this->respondStandardResponse(200, '011', 'Customer ID is invalid / not exist');
        }

        // SIMULATE BILL ID
        // $lastTransaction = slavable_model(TransactionsModel::class, 'Coreapi')->where(['bank_account_id' => $bankAccountDetails->id])->orderBy('id', 'desc')->first();
        // if ($lastTransaction && $lastTransaction->id != $rawData['code']) {
        //     return $this->respondStandardResponse(200, '021', 'Bill ID is invalid / not exist');
        // }

        // if (!$lastTransaction && $rawData['code'] != 1) {
        //     return $this->respondStandardResponse(200, '021', 'Bill ID is invalid / not exist');
        // }
        // SIMULATE BILL ID

        // Skip Bank API notification when switch to SMS Banking
        if ($bankSubAccountDetails->bank_id == 9 && $bankSubAccountDetails->bank_sms) {
            if ($this->debug) {
                log_message('error', 'Skip Bank API notification when BIDV account switch to SMS connection - ' . json_encode($rawData));
            }

            return $this->respondStandardResponse(200, '000', 'success');
        }

        $bidvNotificationId = $bidvNotificationModel->insert([
            'raw_id' => $bidvNotificationRawId,
            'trans_id' => $rawData['trans_id'],
            'trans_date' => $rawData['trans_date'],
            'customer_id' => $rawData['customer_id'],
            'amount' => $rawData['amount'],
            'remark' => $rawData['remark'],
            'checksum' => $rawData['checksum']
        ]);

        if (($this->config->enableFilterTransaction ?? false) && ! $bankAccountDetails->merchant_id) {
            $filterTransaction = FilterTransactionAction::forBankAccount($bankAccountDetails->id)
                ->amountIn(true)
                ->description($rawData['remark'])
                ->payload($rawData)
                ->filter();

            if ($filterTransaction['isRejected']) {
                return $this->respondStandardResponse(200, '000', 'success');
            }
        }

        $smsParserModel = model(SmsParserModel::class);
        $companyModel = model(CompanyModel::class);
        $transactionsModel = model(TransactionsModel::class);

        $payCodeRegex = false;
        $code = null;

        if (is_object($bankSubAccountDetails)) {
            $code = PayCodeDetector::getCode($rawData['remark'], $bankSubAccountDetails->company_id);
        }

        $parserId = $smsParserModel->insert([
            'sms_id' => $bidvNotificationId,
            'reference_number' => $rawData['trans_id'],
            'gateway' => 'BIDV',
            'transaction_date' => date('Y-m-d H:i:s', strtotime($rawData['trans_date'])),
            'account_number' => $bankSubAccountDetails->account_number,
            'sub_account' => $bankSubAccountDetails->sub_account,
            'amount_in' => $rawData['amount'],
            'code' => $code,
            'transaction_content' => $rawData['remark'],
            'source' => 'BankAPINotify',
            'body' => 'BankAPINotify ' . $rawData['remark'],
            'parser_status' => 'Success',
            'bank_account_id' => $bankSubAccountDetails->bank_account_id,
            'merchant_id' => $bankSubAccountDetails->merchant_id,
        ]);

        if (!$parserId) {
            $bidvNotificationModel->where('id', $bidvNotificationId)->delete();
            log_message('error', 'BIDV Notification Error: SMSParserModel insert failed - Raw #' . $bidvNotificationRawId);

            return $this->respondStandardResponse(200, '031', 'Service unavailable');
        }

        $transactionDetails = $smsParserModel->find($parserId);

        if (is_object($transactionDetails) && is_numeric($transactionDetails->account_number) && !$bankSubAccountDetails->merchant_id) {
            try {
                PushMobileTransactionNotificationQueueAction::run($transactionDetails, $bankAccountDetails, $bankSubAccountDetails);
            } catch (Exception $e) {
                log_message('error', 'BIDV PushMobileTransactionNotificationQueueAction failed: ' . $e->getMessage() . ' - ' . $e->getTraceAsString() . ' - ' . json_encode($transactionDetails));
            }
            
            $companyCheck = $companyModel->select("tb_autopay_company.id")
                ->join("tb_autopay_company_subscription", "tb_autopay_company.id=tb_autopay_company_subscription.company_id")
                ->join("tb_autopay_bank_account", "tb_autopay_bank_account.company_id=tb_autopay_company.id")
                ->where([
                    'tb_autopay_bank_account.id' => $bankSubAccountDetails->bank_account_id, 
                    'tb_autopay_company_subscription.status' => 'Active',
                    'tb_autopay_company.status' => 'Active', 'tb_autopay_company.active' => 1
                ])->get()->getResult();
            
            if (count($companyCheck) == 1) {
                $webhookQueuable = $this->enableWebhookOffload ?? false;
                $forceWebhookManually = ['webhook' => false, 'sapo' => false, 'haravan' => false, 'shopify' => false, 'googlesheet' => false,'output_device' => false, 'viber' => false];
                $rabbitmq = null;

                if ($webhookQueuable) {
                    $rabbitmq = new \App\Libraries\RabbitMQClient;
                    $webhookQueuable = $rabbitmq->connect();
                }

                if ($rabbitmq && $webhookQueuable) {
                    $msg = new AMQPMessage(
                        json_encode(['account_number' => $transactionDetails->account_number, 'parser_id' => $parserId]),
                        array('delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT)
                    );

                    // webhooks
                    try {
                        $rabbitmq->queueDeclare('webhook');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('webhook', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'BIDV API webhook queue failed: ' . $e->getMessage());
                        $forceWebhookManually['webhook'] = true;
                    }
                    
                    // sapo
                    try {
                        $rabbitmq->queueDeclare('sapo');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('sapo', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'BIDV API Sapo queue failed: ' . $e->getMessage());
                        $forceWebhookManually['sapo'] = true;
                    }

                    // haravan
                    try {
                        $rabbitmq->queueDeclare('haravan');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('haravan', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'BIDV API Haravan queue failed: ' . $e->getMessage());
                        $forceWebhookManually['haravan'] = true;
                    } 

                    // shopify
                    try {
                        $rabbitmq->queueDeclare('shopify');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('shopify', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'BIDV API Shopify queue failed: ' . $e->getMessage());
                        $forceWebhookManually['shopify'] = true;
                    } 

                    // googlesheet
                    try {
                        $rabbitmq->queueDeclare('googlesheet');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('googlesheet', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'BIDV API GoogleSheet queue failed: ' . $e->getMessage());
                        $forceWebhookManually['googlesheet'] = true;
                    } 

                     // output device
                     try {
                        $rabbitmq->queueDeclare('output_device');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('output_device', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'BIDVPers API output device queue failed: ' . $e->getMessage());
                        $forceWebhookManually['output_device'] = true;
                    } 
                    
                    // viber
                    try {
                        $rabbitmq->queueDeclare('viber');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('viber', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'BIDV Pers API Viber queue failed: ' . $e->getMessage());
                        $forceWebhookManually['viber'] = true;
                    }

                    $rabbitmq->close();
                } 
                
                if (!$webhookQueuable || $forceWebhookManually['webhook']) {
                    // webhooks
                    $webhooksModel = model(WebhooksModel::class);
                    $webhooksModel->doWebhooks($transactionDetails->account_number, $parserId);
                }

                if (!$webhookQueuable || $forceWebhookManually['sapo']) {
                    // sapo
                    $sapoModel = model(SapoModel::class);
                    $sapoModel->doWebhooks($transactionDetails->account_number, $parserId);
                }

                if (!$webhookQueuable || $forceWebhookManually['haravan']) {
                    // haravan
                    $haravanModel = model(HaravanModel::class);
                    $haravanModel->doWebhooks($transactionDetails->account_number, $parserId);
                }

                if (!$webhookQueuable || $forceWebhookManually['shopify']) {
                    // shopify
                    ExecuteShopifyWebhooksAction::run($parserId);
                }

                if (!$webhookQueuable || $forceWebhookManually['googlesheet']) {
                    // googlesheet
                    ExecuteGoogleSheetsWebhooksAction::run($parserId);
                }
                
                if (!$webhookQueuable || $forceWebhookManually['webhook']) {
                    // telegram queue
                    $notificationTelegramModel = model(NotificationTelegramModel::class);
                    $notificationTelegramModel->checkAndAddQueue($parserId);
                    
                    // lark messenger queue
                    $notificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);
                    $notificationLarkMessengerModel->checkAndAddQueue($parserId);
                }


                // output device
                if (!$webhookQueuable || $forceWebhookManually['output_device']) {
                    // output device queue
                    ExecuteOutputDevice::run($parserId,0);
                }
                
                if (!$webhookQueuable || $forceWebhookManually['viber']) {
                    // viber
                    (new \App\Features\Viber\ViberFeature)->sendNotificationByTransactionId($parserId);
                }

                //counter
                $counterModel = model(CounterModel::class);
                $counterModel->transaction($companyCheck[0]->id, false, $transactionDetails->amount_in, $transactionDetails->amount_out);

                // cashflow
                $bankAccountCashFlowModel = model(BankAccountCashflowModel::class);
                $bankAccountCashFlowModel->amount_update($companyCheck[0]->id, date('Y-m-d', strtotime($transactionDetails->transaction_date)), $transactionDetails->bank_account_id, $transactionDetails->amount_in, $transactionDetails->amount_out, $transactionDetails->sub_account, $transactionDetails->accumulated);

                $lastTransaction = $transactionDetails;

                if (is_object($lastTransaction))
                    $bankAccountModel->set(['last_transaction' => $lastTransaction->transaction_date, 'accumulated' => $lastTransaction->accumulated])->where(['id' => $bankSubAccountDetails->bank_account_id])->update();
            }
        }
 
        return $this->respondStandardResponse(200, '000', 'success');
    }

    protected function respondStandardResponse($statusCode, $resultCode, $description, $data = [])
    {
        return $this->respond(array_merge([
            'result_code' => $resultCode,
            'result_desc' => $description
        ], $data), $statusCode);
    }
}
