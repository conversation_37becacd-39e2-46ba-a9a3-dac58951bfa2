<?php

namespace App\Controllers\Coreapi;

use App\Actions\BankAccount\FilterTransactionAction;
use Exception;
use App\Models\SapoModel;
use CodeIgniter\Controller;
use App\Models\CompanyModel;
use App\Models\CounterModel;
use App\Models\HaravanModel;
use App\Models\WebhooksModel;
use App\Models\SmsParserModel;
use \src\security\SecurityUtil;
use App\Models\BankAccountModel;
use App\Models\TransactionsModel;
use App\Models\ConfigurationModel;
use CodeIgniter\API\ResponseTrait;
use App\Models\BankSubAccountModel;
use PhpAmqpLib\Message\AMQPMessage;
use App\Models\KlbNotificationModel;
use App\Models\BankAccountCashflowModel;
use App\Models\NotificationTelegramModel;
use CodeIgniter\RESTful\ResourceController;
use App\Models\NotificationLarkMessengerModel;
use App\Models\NotificationTelegramQueueModel;
use App\Actions\Shopify\ExecuteShopifyWebhooksAction;
use App\Actions\GoogleSheets\ExecuteGoogleSheetsWebhooksAction;
use App\Actions\Mobile\PushMobileTransactionNotificationQueueAction;
use App\Actions\PayCodeDetector;

use App\Actions\OutputDevice\ExecuteOutputDevice;

use App\Actions\Transaction\CreateTransactionQueueAction;
use App\Actions\Merchant\PushMerchantTransactionNotificationAction;

class Klb extends ResourceController
{
    use ResponseTrait;
 
    public function notifyTransaction() {

        $request = \Config\Services::request();
        
        if ($this->request->getMethod(true) != 'POST')
            return $this->failNotFound();

        $validation =  \Config\Services::validation();
 
        if(! $this->validate(
            [
                'data'=>'required',
            ]
        ))
            return $this->failValidationError(implode(". ", $validation->getErrors()));
 
        $klbNotification = model(KlbNotificationModel::class);
        $klbNotificationRaw = model(KlbNotificationRawModel::class);

        $input =  $request->getJSON();

        $raw_data = $input->data;

        $raw_id = $klbNotificationRaw->insert(['data' => $raw_data, 'ip' =>$this->request->getIPAddress()]);

        $klb_config = new \Config\Klb();
        $klb_config->enableKlbWebhookOffload = $klb_config->enableKlbWebhookOffload ?? false;
       
        try {
            $decrypt_data = SecurityUtil::decryptAES($raw_data, $klb_config->KlbEncryptKey);

        } catch(\Throwable $e){

        }
     
        $decrypt_data_array = json_decode($decrypt_data);

        if(is_object($decrypt_data_array)) {
            $transactionId = "";
            $virtualAccount = "";
            $actualAccount = "";
            $fromBin = "";
            $fromAccount = "";
            $success = "";
            $amount = "";
            $txnNumber = "";
            $transferDesc = "";
            $time = "";

            if(isset($decrypt_data_array->transactionId))
                $transactionId = $decrypt_data_array->transactionId;
            if(isset($decrypt_data_array->virtualAccount))
                $virtualAccount = $decrypt_data_array->virtualAccount;
            if(isset($decrypt_data_array->actualAccount))
                $actualAccount = $decrypt_data_array->actualAccount;
            if(isset($decrypt_data_array->fromBin))
                $fromBin = $decrypt_data_array->fromBin;
            if(isset($decrypt_data_array->fromAccount))
                $fromAccount = $decrypt_data_array->fromAccount;
            if(isset($decrypt_data_array->fromAccount))
                $fromAccount = $decrypt_data_array->fromAccount;
            if(isset($decrypt_data_array->success))
                $success = $decrypt_data_array->success;
            if(isset($decrypt_data_array->amount))
                $amount = $decrypt_data_array->amount;
            if(isset($decrypt_data_array->txnNumber))
                $txnNumber = $decrypt_data_array->txnNumber;

            if(isset($decrypt_data_array->transferDesc))
                $transferDesc = $decrypt_data_array->transferDesc;
            if(isset($decrypt_data_array->time))
                $time = $decrypt_data_array->time;


            $klb_data = [
                'transactionId' => $transactionId,
                'virtualAccount' => $virtualAccount,
                'actualAccount' => $actualAccount,
                'fromBin' => $fromBin,
                'fromAccount' => $fromAccount,
                'success' => $success,
                'amount' => $amount,
                'txnNumber' => $txnNumber,
                'transferDesc' => $transferDesc,
                'time' => $time,
                'raw_id' => $raw_id,
            ];

            $duplicatedTransaction = $klbNotification->where('transactionId', $klb_data['transactionId'])->countAllResults();

            if ($duplicatedTransaction) {
                return $this->response->setJSON(['data' => SecurityUtil::encryptAES(json_encode(['status' => true]), $klb_config->KlbEncryptKey)]);
            }

            $klb_id = $klbNotification->insert($klb_data);
            if(!is_numeric($klb_id))
                return $this->response->setJSON(['data' => SecurityUtil::encryptAES(json_encode(['status' => true]), $klb_config->KlbEncryptKey)]);
        } else 
            return $this->response->setJSON(['data' => SecurityUtil::encryptAES(json_encode(['status' => true]), $klb_config->KlbEncryptKey)]);

        $smsParserModel = model(SmsParserModel::class);
        $companyModel = model(CompanyModel::class);
        $transactionsModel = model(TransactionsModel::class);
        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);

        $bank_account_details = $bankAccountModel
            ->select('id,account_number,company_id,bank_id,merchant_id')
            ->where([
                'account_number' => $klb_data['actualAccount'],
                'bank_id' => 17,
                'active' => true
            ])->first();
        //helper('filesystem');
        //write_file('klb.log', serialize($bank_account_details));
        if(!is_object($bank_account_details))
            return $this->response->setJSON(['data' => SecurityUtil::encryptAES(json_encode(['status' => true]), $klb_config->KlbEncryptKey)]);

        $canSyncAccumlated = true;

        if (($klb_config->enableFilterTransaction ?? false) && ! $bank_account_details->merchant_id) {
            $filterTransaction = FilterTransactionAction::forBankAccount($bank_account_details->id)
                ->amountIn(true)
                ->description($klb_data['transferDesc'])
                ->payload($klb_data)
                ->filter();

            if ($filterTransaction['isRejected']) {
                return $this->response->setJSON(['data' => SecurityUtil::encryptAES(json_encode(['status' => true]), $klb_config->KlbEncryptKey)]);
            }

            $canSyncAccumlated = $filterTransaction['canSyncAccumulated'];
        }

        if(strtotime($klb_data['time'])) {
            $transaction_date = $klb_data['time'];
        } else 
            return $this->response->setJSON(['data' => SecurityUtil::encryptAES(json_encode(['status' => true]), $klb_config->KlbEncryptKey)]);

        $account_number = $bank_account_details->account_number;

        $transaction_content = $klb_data['transferDesc'];
        // payment code
        $code = NULL;

        if(is_object($bank_account_details)) {
            $code = PayCodeDetector::getCode($klb_data['transferDesc'], $bank_account_details->company_id);
        }

        $transactionsModel = model(TransactionsModel::class);

        $accumulated = 0;

        $cache = service('cache');
        $lastAccumulatedKey = 'sepay_last_accumulated_bank_account_' . $bank_account_details->id;
        $lastAccumulated = $cache->get($lastAccumulatedKey);

        if ($lastAccumulated === 0) {
            $accumulated = 0;
        } else if (!is_null($lastAccumulated)) {
            $accumulated = $lastAccumulated + $klb_data['amount'];
        } else {
            $last_trans = $transactionsModel->select(['accumulated'])->where([
                'account_number' => $account_number, 
                'accumulated!=' => '',
                'bank_account_id' => $bank_account_details->id,
            ])->orderBy('transaction_date', 'desc')->first();
            
            if (is_object($last_trans) && $last_trans->accumulated > 0) {
                $accumulated = $last_trans->accumulated + $klb_data['amount'];
            }
        }

        $transaction_data = [
            "sms_id" => $klb_id,
            "gateway" => "KienLongBank",
            "transaction_date" => $transaction_date,
            "account_number" => $account_number,
            "sub_account" => $klb_data['virtualAccount'],
            "amount_in" => $klb_data['amount'],
            "amount_out" => 0,
            "accumulated" => $canSyncAccumlated ? $accumulated : 0,
            "code" => $code,
            "transaction_content" => $transaction_content,
            "reference_number" => $klb_data['transactionId'],
            "source" => "BankAPINotify",
            "body" => "BankAPINotify " . $transaction_content,
            "parser_status" => "Success",
            "from_bin" => $fromBin,
            "from_account_number" => $fromAccount,
            'bank_account_id' => $bank_account_details->id,
            'merchant_id' => $bank_account_details->merchant_id,
            'transaction_id' => uuid()
        ];

        $merchantConfig = config(\App\Config\Merchant::class);
        $merchantConfig->enabledTransactionInsertionOffload = $merchantConfig->enabledTransactionInsertionOffload ?? false; 
        $merchantConfig->enabledPushTransactionNotificationOffload = $merchantConfig->enabledPushTransactionNotificationOffload ?? true;

        $queueTransactionId = null;
        $parser_result = null; // primary id of tb_autopay_sms_parsed table
        $transaction_details = null;

        if ($bank_account_details->merchant_id && $merchantConfig->enabledTransactionInsertionOffload) {
            $queueTransactionId = CreateTransactionQueueAction::run((object) $transaction_data, $bank_account_details);
        }

        if (!$queueTransactionId) {
            $parser_result = $smsParserModel->insert($transaction_data);
            $transaction_details = $smsParserModel->find($parser_result);
        }

        $cache->save($lastAccumulatedKey, $accumulated, 0);

        if (!$parser_result && !$queueTransactionId) {
            $klbNotification->where('id', $klb_id)->delete();
            log_message('error', "KLB Notification Error: SMSParserModel insert failed - Raw #" . $raw_id);

            return $this->response->setJSON(['data' => SecurityUtil::encryptAES(json_encode(['status' => true]), $klb_config->KlbEncryptKey)]);
        }

        if (is_object($transaction_details) && is_numeric($transaction_details->account_number) && $bank_account_details->merchant_id && !$queueTransactionId) {
            try {
                PushMerchantTransactionNotificationAction::run(
                    $bank_account_details, 
                    $transaction_details,
                    $merchantConfig->enabledPushTransactionNotificationOffload
                );
            } catch (Exception $e) {
                log_message('error', '[PushableMerchantTransactionNotificationAction] ' . $e->getMessage() . ' - ' . $e->getTraceAsString());
            }
        }

        if(is_object($transaction_details) && is_numeric($transaction_details->account_number) && !$bank_account_details->merchant_id) {
            try {
                PushMobileTransactionNotificationQueueAction::run($transaction_details, $bank_account_details);
            } catch (Exception $e) {
                log_message('error', 'KLB PushMobileTransactionNotificationQueueAction failed: ' . $e->getMessage() . ' - ' . $e->getTraceAsString() . ' - ' . json_encode($transaction_details));
            }
            // check company and subscription status
            $company_check = $companyModel->select("tb_autopay_company.id")
                ->join("tb_autopay_company_subscription","tb_autopay_company.id=tb_autopay_company_subscription.company_id")
                ->join("tb_autopay_bank_account","tb_autopay_bank_account.company_id=tb_autopay_company.id")
                ->where(['tb_autopay_bank_account.id' => $bank_account_details->id, 'tb_autopay_company_subscription.status'=>'Active','tb_autopay_company.status' => 'Active', 'tb_autopay_company.active' => 1])->get()->getResult();

            if(count($company_check) == 1) {
                $webhookQueuable = $klb_config->enableKlbWebhookOffload;
                $forceWebhookManually = ['webhook' => false, 'sapo' => false, 'haravan' => false, 'shopify' => false, 'googlesheet' => false,'output_device' => false, 'viber' => false];
                $rabbitmq = null;

                if ($webhookQueuable) {
                    $rabbitmq = new \App\Libraries\RabbitMQClient;
                    $webhookQueuable = $rabbitmq->connect();
                }

                if ($rabbitmq && $webhookQueuable) {
                    $msg = new AMQPMessage(
                        json_encode(['account_number' => $transaction_details->account_number, 'parser_id' => $parser_result]),
                        array('delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT)
                    );

                    // webhooks
                    try {
                        $rabbitmq->queueDeclare('webhook');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('webhook', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'KLB API webhook queue failed: ' . $e->getMessage());
                        $forceWebhookManually['webhook'] = true;
                    }
                    
                    // sapo
                    try {
                        $rabbitmq->queueDeclare('sapo');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('sapo', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'KLB API Sapo queue failed: ' . $e->getMessage());
                        $forceWebhookManually['sapo'] = true;
                    }

                    // haravan
                    try {
                        $rabbitmq->queueDeclare('haravan');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('haravan', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'KLB API Haravan queue failed: ' . $e->getMessage());
                        $forceWebhookManually['haravan'] = true;
                    } 

                    // shopify
                    try {
                        $rabbitmq->queueDeclare('shopify');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('shopify', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'KLB API Shopify queue failed: ' . $e->getMessage());
                        $forceWebhookManually['shopify'] = true;
                    } 

                    //googlesheet
                    try {
                        $rabbitmq->queueDeclare('googlesheet');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('googlesheet', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'KLB API GoogleSheet queue failed: ' . $e->getMessage());
                        $forceWebhookManually['googlesheet'] = true;
                    }

                    // output device
                    try {
                        $rabbitmq->queueDeclare('output_device');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('output_device', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'Klb API output device queue failed: ' . $e->getMessage());
                        $forceWebhookManually['output_device'] = true;
                    } 
                    
                    // viber
                    try {
                        $rabbitmq->queueDeclare('viber');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('viber', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'KLB API Viber queue failed: ' . $e->getMessage());
                        $forceWebhookManually['viber'] = true;
                    }

                    $rabbitmq->close();
                } 
                
                if (!$webhookQueuable || $forceWebhookManually['webhook']) {
                    // webhooks
                    $webhooksModel = model(WebhooksModel::class);
                    $webhooksModel->doWebhooks($transaction_details->account_number, $parser_result);
                }

                if (!$webhookQueuable || $forceWebhookManually['sapo']) {
                    // sapo
                    $sapoModel = model(SapoModel::class);
                    $sapoModel->doWebhooks($transaction_details->account_number, $parser_result);
                }

                if (!$webhookQueuable || $forceWebhookManually['haravan']) {
                    // haravan
                    $haravanModel = model(HaravanModel::class);
                    $haravanModel->doWebhooks($transaction_details->account_number, $parser_result);
                }

                if (!$webhookQueuable || $forceWebhookManually['shopify']) {
                    // shopify
                    ExecuteShopifyWebhooksAction::run($parser_result);
                }

                if (!$webhookQueuable || $forceWebhookManually['googlesheet']) {
                    // googlesheet
                    ExecuteGoogleSheetsWebhooksAction::run($parser_result);
                }

                if (!$webhookQueuable || $forceWebhookManually['webhook']) {
                    // telegram queue
                    $notificationTelegramModel = model(NotificationTelegramModel::class);
                    $notificationTelegramModel->checkAndAddQueue($parser_result);
                    
                    // lark messenger queue
                    $notificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);
                    $notificationLarkMessengerModel->checkAndAddQueue($parser_result);
                }

                // output device
                if (!$webhookQueuable || $forceWebhookManually['output_device']) {
                    // output device queue
                    ExecuteOutputDevice::run($parser_result,0);
                }
                
                if (!$webhookQueuable || $forceWebhookManually['viber']) {
                    // viber
                    (new \App\Features\Viber\ViberFeature)->sendNotificationByTransactionId($parser_result);
                }

                //counter
                $counterModel = model(CounterModel::class);
                $counterModel->transaction($company_check[0]->id,FALSE,$transaction_details->amount_in, $transaction_details->amount_out);
                
                // cashflow
                $bankAccountCashFlowModel = model(BankAccountCashflowModel::class);
                $bankAccountCashFlowModel->amount_update($company_check[0]->id, date('Y-m-d', strtotime($transaction_details->transaction_date)), $transaction_details->bank_account_id, $transaction_details->amount_in, $transaction_details->amount_out, $transaction_details->sub_account,$transaction_details->accumulated);

                $last_trans = $transaction_details;

                if(is_object($last_trans))
                    $bankAccountModel->set(['last_transaction' => $last_trans->transaction_date, 'accumulated' => $last_trans->accumulated])->where(['id' => $bank_account_details->id])->update();
            }
        }
 
        return $this->response->setJSON(['data' => SecurityUtil::encryptAES(json_encode(['status' => true]), $klb_config->KlbEncryptKey)]);
    } 
}