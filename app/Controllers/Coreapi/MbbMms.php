<?php

namespace App\Controllers\Coreapi;

use App\Actions\BankAccount\FilterTransactionAction;
use Exception;
use App\Models\SapoModel;
use CodeIgniter\Controller;
use App\Models\CompanyModel;
use App\Models\CounterModel;
use App\Models\HaravanModel;
use App\Models\MerchantModel;
use App\Models\WebhooksModel;
use App\Models\SmsParserModel;
use App\Models\BankAccountModel;
use App\Models\TransactionsModel;
use App\Models\ConfigurationModel;
use CodeIgniter\API\ResponseTrait;
use App\Models\BankSubAccountModel;
use PhpAmqpLib\Message\AMQPMessage;
use App\Models\MbbNotificationModel;
use CodeIgniter\HTTP\IncomingRequest;
use App\Models\MbbMmsNotificationModel;
use App\Models\MbbNotificationRawModel;
use App\Models\BankAccountCashflowModel;
use App\Models\MbbNotificationTokenModel;
use App\Models\NotificationTelegramModel;
use App\Models\BankSubAccountMetadataModel;
use CodeIgniter\RESTful\ResourceController;
use App\Models\NotificationLarkMessengerModel;
use App\Models\NotificationTelegramQueueModel;
use App\Actions\Shopify\ExecuteShopifyWebhooksAction;
use App\Actions\GoogleSheets\ExecuteGoogleSheetsWebhooksAction;
use App\Actions\Transaction\CreateTransactionQueueAction;
use App\Actions\Merchant\PushMerchantTransactionNotificationAction;
use App\Actions\Mobile\PushMobileTransactionNotificationQueueAction;
use App\Actions\PayCodeDetector;

use App\Actions\OutputDevice\ExecuteOutputDevice;
use Config\MbbMms as MbbMmsConfig;

/**
 * Note: 
 * `traceTransfer` is VA number
 * `debitAmount` is transaction amount
 */
class MbbMms extends ResourceController
{
    use ResponseTrait;

    protected $helpers = ['general'];

    protected MbbMmsConfig $config;

    const BANK_ID = 8;

    public function __construct()
    {
        $this->config = config(MbbMmsConfig::class);
        $this->config->notifyDebug = $this->config->notifyDebug ?? true;
        $this->config->notifyWebhookOffload = $this->config->notifyWebhookOffload ?? false;
    }

    protected function respondStandardResponse($code, $desc)
    {
        return $this->respond(['resCode' => $code, 'resDesc' => $desc], 200);
    }
 
    public function notifyTransaction()
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }

        $rawData = (array) $this->request->getJSON();

        $data = [
            'traceTransfer' => $this->request->getVar('traceTransfer'),
            'storeLabel' => $this->request->getVar('storeLabel'),
            'terminalLabel' => $this->request->getVar('terminalLabel'),
            'debitAmount' => $this->request->getVar('debitAmount'),
            'realAmount' => $this->request->getVar('realAmount'),
            'payDate' => $this->request->getVar('payDate'),
            'respCode' => $this->request->getVar('respCode'),
            'respDesc' => $this->request->getVar('respDesc'),
            'checkSum' => $this->request->getVar('checkSum'),
            'rate' => $this->request->getVar('rate'),
            'billNumber' => $this->request->getVar('billNumber'),
            'consumerLabelTerm' => $this->request->getVar('consumerLabelTerm'),
            'referenceLabelCode' => $this->request->getVar('referenceLabelCode'),
            'referenceLabel' => $this->request->getVar('referenceLabel'),
            'userName' => $this->request->getVar('userName'),
            'ftCode' => $this->request->getVar('ftCode'),
            'endPointUrl' => $this->request->getVar('endPointUrl'),
            'additionalData' => json_decode(json_encode($this->request->getVar('additionalData')), true),
        ];

        $rules = [
            'traceTransfer' => ['required', 'string'],
            'storeLabel' => ['permit_empty', 'string'],
            'terminalLabel' => ['permit_empty', 'string'],
            'debitAmount' => ['required', 'integer', 'greater_than[0]'],
            'realAmount' => ['required', 'integer'],
            'payDate' => ['required', 'string', 'regex_match[/\d{14}/]'],
            'respCode' => ['required', 'string'],
            'respDesc' => ['required', 'string'],
            'checkSum' => ['required', 'string'],
            'rate' => ['permit_empty', 'string'],
            'billNumber' => ['permit_empty', 'string'],
            'consumerLabelTerm' => ['permit_empty', 'string'],
            'referenceLabelCode' => ['permit_empty', 'string'],
            'referenceLabel' => ['permit_empty'],
            'referenceLabel.*' => ['permit_empty', 'string'],
            'userName' => ['permit_empty', 'string'],
            'ftCode' => ['permit_empty', 'string'],
            'endPointUrl' => ['permit_empty', 'string'],
            'additionalData' => ['permit_empty'],
            'additionalData.*.name' => ['permit_empty', 'in_list[customerAccount,customerName,bankCode,paymentDetail,partnerType,sourceApp,t24ProcessingDate]'],
        ];

        if (! $this->validateData($data, $rules)) {
            log_message('error', 'MBB MMS Notification Error: Bad request: ' . json_encode($this->validator->getErrors()) . ' - ' . json_encode($rawData));

            return $this->respondStandardResponse('02', 'Bad request: ' . implode(' ', $this->validator->getErrors()));
        }

        $validChecksum = md5($data['traceTransfer'] . $data['billNumber'] . $data['payDate'] . $data['debitAmount'] . $this->config->clientAccessKey);

        if ($data['checkSum'] !== $validChecksum) {
            return $this->respondStandardResponse('02', 'Checksum invalid');
        }

        $mbbNotificationRawModel = model(MbbNotificationRawModel::class);
        $mbbMmsNotificationModel = model(MbbMmsNotificationModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $bankSubAccountMetadataModel = model(BankSubAccountMetadataModel::class);
        $bankAccountModel = model(BankAccountModel::class);

        if ($this->config->notifyDebug) {
            $mbbNotificationRawId = $mbbNotificationRawModel->insert(['action' => 'MbbMms', 'payload' => json_encode($data), 'ip' =>$this->request->getIPAddress()]);
        } else {
            $mbbNotificationRawId = 0;
        }

        $duplicatedTransaction = $mbbMmsNotificationModel->where(['ftCode' => $data['ftCode']])->countAllResults();
        if ($duplicatedTransaction) {
            if ($this->config->notifyDebug) log_message('error', 'MBB MMS Notification Error: Transaction Duplicated - Raw #' . $mbbNotificationRawId);

            return $this->respondStandardResponse('02', 'Duplicated transaction');
        }

        $additionalData = [];
        $amount = $data['debitAmount'];
        $allowedAdditionaFields = ['customerAccount', 'bankCode', 'paymentDetail', 'partnerType'];

        foreach ($data['additionalData'] as $item) {
            if (!isset($item['name']) || !in_array($item['name'], $allowedAdditionaFields)) continue;

            $additionalData[$item['name']] = $item['value'];
        }

        $bankSubAccountDetails = $bankSubAccountModel
            ->select(['tb_autopay_bank_sub_account.id', 'tb_autopay_bank_sub_account.bank_account_id', 'tb_autopay_bank_sub_account.va_type', 'tb_autopay_bank_sub_account.sub_account', 'tb_autopay_bank_sub_account.sub_holder_name', 'tb_autopay_bank_sub_account.content', 'tb_autopay_bank_sub_account_metadata.bill_id', 'tb_autopay_bank_sub_account_metadata.amount', 'tb_autopay_bank_sub_account_metadata.expires_at', 'tb_autopay_bank_sub_account_metadata.paid_amount', 'tb_autopay_bank_sub_account.label', 'tb_autopay_bank_sub_account.active', 'tb_autopay_bank_sub_account.created_at', 'tb_autopay_bank_sub_account.updated_at'])
            ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id = tb_autopay_bank_sub_account.bank_account_id')
            ->join('tb_autopay_bank_sub_account_metadata', 'tb_autopay_bank_sub_account_metadata.bank_sub_account_id = tb_autopay_bank_sub_account.id', 'left')
            ->where([
                'tb_autopay_bank_sub_account.sub_account' => $this->config->prefixVa . $data['traceTransfer'], 
                'tb_autopay_bank_sub_account.va_active' => 1, 
                'tb_autopay_bank_sub_account.active' => 1,
                'tb_autopay_bank_sub_account.acc_type' => 'Real',
                'tb_autopay_bank_account.bank_id' => self::BANK_ID, 
                'tb_autopay_bank_sub_account_metadata.paid_amount' => null,
            ])
            ->groupStart()
                ->where('tb_autopay_bank_sub_account_metadata.expires_at', null)
                ->orWhere('tb_autopay_bank_sub_account_metadata.expires_at > ', date('Y-m-d H:i:s'))
            ->groupEnd()->first();

        if (!$bankSubAccountDetails) {
            if ($this->config->notifyDebug) log_message('error', 'MBB MMS Notification Error: VA does not exists - Raw #' . $mbbNotificationRawId);

            return $this->respondStandardResponse('02', 'QR does not exist in SePay');
        }
        
        $bankAccountDetails = $bankAccountModel->where([
            'id' => $bankSubAccountDetails->bank_account_id,
            'bank_id' => self::BANK_ID,
            'active' => true,
        ])->first();

        if (! $bankAccountDetails) {
            if ($this->config->notifyDebug) log_message('error', 'MBB MMS Notification Error: Account number does not exists - Raw #' . $mbbNotificationRawId);

            return $this->respondStandardResponse('02', 'QR does not exist in SePay');
        }
        
        $canSyncTransaction = $bankAccountDetails->sync_transactions ?? false;

        if ($bankSubAccountDetails->va_type === 'Dynamic' && $data['referenceLabelCode'] && $data['referenceLabelCode'] !== $bankSubAccountDetails->bill_id) {
            return $this->respondStandardResponse('02', 'Bill ID does not exist in SePay');
        }

        if ($bankSubAccountDetails->va_type === 'Dynamic' && $bankSubAccountDetails->amount && $amount != $bankSubAccountDetails->amount) {
            return $this->respondStandardResponse('02', 'Debit amount does not match');
        }

        $mbbMmsNotificationId = $mbbMmsNotificationModel->insert([
            'traceTransfer' => $data['traceTransfer'],
            'storeLabel' => $data['storeLabel'] ?? null,
            'terminalLabel' => $data['terminalLabel'] ?? null,
            'debitAmount' => $data['debitAmount'],
            'realAmount' => $data['realAmount'],
            'payDate' => $data['payDate'],
            'respCode' => $data['respCode'],
            'respDesc' => $data['respDesc'],
            'rate' => $data['rate'] ?? null,
            'billNumber' => $data['billNumber'] ?? null,
            'consumerLabelTerm' => $data['consumerLabelTerm'] ?? null,
            'referenceLabelCode' => $data['referenceLabelCode'] ?? null,
            'referenceLabel' => $data['referenceLabel'] ? json_encode($data['referenceLabel']) : null,
            'userName' => $data['userName'] ?? null,
            'ftCode' => $data['ftCode'] ?? null,
            'endPointUrl' => $data['endPointUrl'] ?? null,
            'customerAccount' => $additionalData['customerAccount'] ?? null,
            'customerName' => $additionalData['customerName'] ?? null,
            'paymentDetail' => $additionalData['paymentDetail'] ?? null,
            'partnerType' => $additionalData['partnerType'] ?? null,
        ]);

        if (($this->config->enableFilterTransaction ?? false) && ! $bankAccountDetails->merchant_id) {
            $filterTransaction = FilterTransactionAction::forBankAccount($bankAccountDetails->id)
                ->amountIn(true)
                ->description($additionalData['paymentDetail'])
                ->payload($data)
                ->filter();

            if ($filterTransaction['isRejected']) {
                return $this->respondStandardResponse('00', 'Success');
            }

            $canSyncTransaction = $filterTransaction['canSyncAccumulated'];
        }

        $smsParserModel = model(SmsParserModel::class);
        $companyModel = model(CompanyModel::class);
        $transactionsModel = model(TransactionsModel::class);

        $cache = service('cache');
        $lastAccumulatedKey = 'sepay_last_accumulated_bank_account_' . $bankAccountDetails->id;
        $lastAccumulated = $cache->get($lastAccumulatedKey);
        $accumulated = 0;

        if ($lastAccumulated === 0) {
            $accumulated = 0;
        } else if (!is_null($lastAccumulated)) {
            $accumulated = $lastAccumulated + $amount;
        } else {
            $lastTransaction = $transactionsModel->select(['accumulated'])->where([
                'accumulated!=' => '',
                'bank_account_id' => $bankAccountDetails->id,
            ])->orderBy('transaction_date', 'desc')->first();
            
            if (is_object($lastTransaction)) {
                $accumulated += $amount;
            }
        }

        $code = null;

        if (is_object($bankAccountDetails)) {
            $code = PayCodeDetector::getCode($additionalData['paymentDetail'], $bankAccountDetails->company_id);
        }

        $parserData = [
            'sms_id' => $mbbMmsNotificationId,
            'gateway' => 'MBBank',
            'transaction_date' => date('Y-m-d H:i:s', strtotime($data['payDate'])),
            'account_number' => $bankAccountDetails->account_number,
            'sub_account' => $this->config->prefixVa . $data['traceTransfer'],
            'amount_in' => $amount,
            'accumulated' => $canSyncTransaction ? $accumulated : 0,
            'code' => $code,
            'transaction_content' => $additionalData['paymentDetail'],
            'reference_number' => $data['ftCode'],
            'source' => 'BankAPINotify',
            'body' => 'BankAPINotify ' . $additionalData['paymentDetail'],
            'parser_status' => 'Success',
            'from_account_number' => $additionalData['customerAccount'] ?? '',
            'from_bin' => $additionalData['bankCode'] ?? '',
            'bank_account_id' => $bankAccountDetails->id,
            'merchant_id' => $bankAccountDetails->merchant_id,
            'transaction_id' => uuid()
        ];

        $parserId = $smsParserModel->insert($parserData);
        
        if (!$parserId) {
            $mbbMmsNotificationModel->where('id', $mbbMmsNotificationId)->delete();
            log_message('error', "MBB MMS Notification Error: SMSParserModel insert failed - Raw #" . $mbbMmsNotificationId);

            return $this->respondStandardResponse('99', 'Service unavailable, please retry');
        }

        $transactionDetails = $smsParserModel->find($parserId);
        
        if (is_object($transactionDetails) && is_numeric($transactionDetails->account_number) && !$bankAccountDetails->merchant_id) {
            try {
                PushMobileTransactionNotificationQueueAction::run($transactionDetails, $bankAccountDetails, $bankSubAccountDetails);
            } catch (Exception $e) {
                log_message('error', 'MBB MMS PushMobileTransactionNotificationQueueAction failed: ' . $e->getMessage() . ' - ' . $e->getTraceAsString() . ' - ' . json_encode($transactionDetails));
            }

            $companyCheck = $companyModel->select("tb_autopay_company.id")
                ->join("tb_autopay_company_subscription", "tb_autopay_company.id=tb_autopay_company_subscription.company_id")
                ->join("tb_autopay_bank_account", "tb_autopay_bank_account.company_id=tb_autopay_company.id")
                ->where([
                    'tb_autopay_bank_account.id' => $bankAccountDetails->id, 
                    'tb_autopay_company_subscription.status' => 'Active',
                    'tb_autopay_company.status' => 'Active', 'tb_autopay_company.active' => 1
                ])->get()->getResult();
            
            if (count($companyCheck) == 1) {
                $webhookQueuable = $this->config->notifyWebhookOffload ?? false;
                $forceWebhookManually = ['webhook' => false, 'sapo' => false, 'haravan' => false, 'shopify' => false, 'googlesheet' => false,'output_device' => false, 'viber' => false];
                $rabbitmq = null;

                if ($webhookQueuable) {
                    $rabbitmq = new \App\Libraries\RabbitMQClient;
                    $webhookQueuable = $rabbitmq->connect();
                }

                if ($rabbitmq && $webhookQueuable) {
                    $msg = new AMQPMessage(
                        json_encode(['account_number' => $transactionDetails->account_number, 'parser_id' => $parserId]),
                        array('delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT)
                    );

                    // webhooks
                    try {
                        $rabbitmq->queueDeclare('webhook');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('webhook', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'MBB API webhook queue failed: ' . $e->getMessage());
                        $forceWebhookManually['webhook'] = true;
                    }
                    
                    // sapo
                    try {
                        $rabbitmq->queueDeclare('sapo');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('sapo', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'MBB API Sapo queue failed: ' . $e->getMessage());
                        $forceWebhookManually['sapo'] = true;
                    }

                    // haravan
                    try {
                        $rabbitmq->queueDeclare('haravan');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('haravan', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'MBB API Haravan queue failed: ' . $e->getMessage());
                        $forceWebhookManually['haravan'] = true;
                    } 

                    // shopify
                    try {
                        $rabbitmq->queueDeclare('shopify');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('shopify', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'MBB API Shopify queue failed: ' . $e->getMessage());
                        $forceWebhookManually['shopify'] = true;
                    } 

                    //googlesheet
                    try {
                        $rabbitmq->queueDeclare('googlesheet');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('googlesheet', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'MBB API GoogleSheet queue failed: ' . $e->getMessage());
                        $forceWebhookManually['googlesheet'] = true;
                    }
                    
                    // output device
                    try {
                        $rabbitmq->queueDeclare('output_device');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('output_device', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'MBBMMS API output device queue failed: ' . $e->getMessage());
                        $forceWebhookManually['output_device'] = true;
                    }
                    
                    // viber
                    try {
                        $rabbitmq->queueDeclare('viber');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('viber', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'MBB MMS API Viber queue failed: ' . $e->getMessage());
                        $forceWebhookManually['viber'] = true;
                    }

                    $rabbitmq->close();
                } 
                
                if (!$webhookQueuable || $forceWebhookManually['webhook']) {
                    // webhooks
                    $webhooksModel = model(WebhooksModel::class);
                    $webhooksModel->doWebhooks($transactionDetails->account_number, $parserId);
                }

                if (!$webhookQueuable || $forceWebhookManually['sapo']) {
                    // sapo
                    $sapoModel = model(SapoModel::class);
                    $sapoModel->doWebhooks($transactionDetails->account_number, $parserId);
                }

                if (!$webhookQueuable || $forceWebhookManually['haravan']) {
                    // haravan
                    $haravanModel = model(HaravanModel::class);
                    $haravanModel->doWebhooks($transactionDetails->account_number, $parserId);
                }

                if (!$webhookQueuable || $forceWebhookManually['shopify']) {
                    // shopify
                    ExecuteShopifyWebhooksAction::run($parserId);
                }
                
                if (!$webhookQueuable || $forceWebhookManually['googlesheet']) {
                    // googlesheet
                    ExecuteGoogleSheetsWebhooksAction::run($parserId);
                }

                if (!$webhookQueuable || $forceWebhookManually['webhook']) {
                    // telegram queue
                    $notificationTelegramModel = model(NotificationTelegramModel::class);
                    $notificationTelegramModel->checkAndAddQueue($parserId);
                    
                    // lark messenger queue
                    $notificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);
                    $notificationLarkMessengerModel->checkAndAddQueue($parserId);
                }

                // output device
                if (!$webhookQueuable || $forceWebhookManually['output_device']) {
                    // output device queue
                    ExecuteOutputDevice::run($parserId,0);
                }
                
                if (!$webhookQueuable || $forceWebhookManually['viber']) {
                    // viber
                    (new \App\Features\Viber\ViberFeature)->sendNotificationByTransactionId($parserId);
                }

                //counter
                $counterModel = model(CounterModel::class);
                $counterModel->transaction($companyCheck[0]->id, false, $transactionDetails->amount_in, $transactionDetails->amount_out);

                // cashflow
                $bankAccountCashFlowModel = model(BankAccountCashflowModel::class);
                $bankAccountCashFlowModel->amount_update($companyCheck[0]->id, date('Y-m-d', strtotime($transactionDetails->transaction_date)), $transactionDetails->bank_account_id, $transactionDetails->amount_in, $transactionDetails->amount_out, $transactionDetails->sub_account, $transactionDetails->accumulated);

                $lastTransaction = $transactionDetails;

                if (is_object($lastTransaction))
                    $bankAccountModel->set(['last_transaction' => $lastTransaction->transaction_date, 'accumulated' => $lastTransaction->accumulated])->where(['id' => $bankAccountDetails->id])->update();
            }

            if ($bankSubAccountDetails->va_type === 'Dynamic') {
                $bankSubAccountModel->where('id', $bankSubAccountDetails->id)->set(['va_active' => 0, 'active' => 0])->update();
                $bankSubAccountMetadataModel->where('bank_sub_account_id', $bankSubAccountDetails->id)->set(['paid_amount' => $amount])->update();
            }
        }
 
        return $this->respondStandardResponse('00', 'Success');
    }
}