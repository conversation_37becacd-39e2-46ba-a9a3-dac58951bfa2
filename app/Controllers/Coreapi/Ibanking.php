<?php

namespace App\Controllers\Coreapi;

use App\Actions\BankAccount\FilterTransactionAction;
use Exception;
use App\Models\SmsModel;
use App\Models\SapoModel;
use CodeIgniter\Controller;
use App\Models\CompanyModel;
use App\Models\CounterModel;
use App\Models\HaravanModel;
use App\Models\IbankingModel;
use App\Models\WebhooksModel;
use App\Models\SmsParserModel;
use App\Models\BankAccountModel;
use App\Models\TransactionsModel;
use App\Models\ConfigurationModel;
use CodeIgniter\API\ResponseTrait;
use App\Models\BankSubAccountModel;
use PhpAmqpLib\Message\AMQPMessage;
use Config\IBanking as IBankingConfig;
use App\Models\BankAccountCashflowModel;
use App\Models\NotificationTelegramModel;
use CodeIgniter\RESTful\ResourceController;
use App\Models\NotificationLarkMessengerModel;
use App\Models\NotificationTelegramQueueModel;
use App\Actions\Shopify\ExecuteShopifyWebhooksAction;
use App\Actions\GoogleSheets\ExecuteGoogleSheetsWebhooksAction;
use App\Actions\Mobile\PushMobileTransactionNotificationQueueAction;
use App\Actions\PayCodeDetector;

use App\Actions\OutputDevice\ExecuteOutputDevice;

class Ibanking extends ResourceController
{
    use ResponseTrait;

    protected $helpers = ['general'];
 
    public function create() {

        $request = \Config\Services::request();

        $config = config(IBankingConfig::class);
        $config->enableIBankingWebhookOffload = $config->enableIBankingWebhookOffload ?? false;

        $client_ip = $request->getIPAddress();

        if(!in_array($client_ip, ['**************', '**************']) && ! in_testing())
            return $this->failNotFound();
        
        if ($this->request->getMethod(true) != 'POST')
            return $this->failNotFound();
        $ibankingModel = new IbankingModel();

        $validation =  \Config\Services::validation();
 
        if(! $this->validate(
            [
                'gateway'=>'required|in_list[Vietcombank]',
                'source'=>'required',
                'transaction_date'=>'required',
                'account_number'=>'required',
                'amount'=>'required|integer',
                'type' =>'required|in_list[credit,debit]',
                'uniq_id'=>'required|is_unique[tb_autopay_ibanking.uniq_id]',
            ]
        ))
            return $this->failValidationError(implode(". ", $validation->getErrors()));

        // check bug day in feature of VCB
        $date = date("Y-m-d", strtotime($this->request->getVar('transaction_date')));
        $days_between = ceil(abs(strtotime($date) - strtotime(date("Y-m-d"))) / 86400);

        if($days_between == 1)
            $transaction_date = date("Y-m-d") . " " . date("H:i:s");
        else
            $transaction_date = date("Y-m-d", strtotime($this->request->getVar('transaction_date'))) . " " . date("H:i:s");

        $transaction_content = preg_replace('!\s+!', ' ', $this->request->getVar('transaction_content'));

        $account_number = $this->request->getVar('account_number');
        $type = $this->request->getVar('type');
        $amount = $this->request->getVar('amount');

        $transactionsModel = model(TransactionsModel::class);
        $bankAccountModel = model(BankAccountModel::class);

        $bank_account_details = $bankAccountModel
            ->select('tb_autopay_bank.brand_name, tb_autopay_bank_account.id, tb_autopay_bank_account.account_number, tb_autopay_bank_account.company_id, tb_autopay_bank_account.bank_sms')
            ->join('tb_autopay_bank', 'tb_autopay_bank.id=tb_autopay_bank_account.bank_id')
            ->where([
                'account_number' => $account_number,
                'tb_autopay_bank.brand_name' => $this->request->getVar('gateway'),
                'tb_autopay_bank_account.active' => true,
            ])->first();

        if ($bank_account_details) {
            $last_trans = $transactionsModel->where([
                'account_number' => $account_number, 
                'accumulated!=' => '',
                'bank_account_id' => $bank_account_details->id
            ])->orderBy('transaction_date', 'desc')->first();
        } else {
            $last_trans = null;
        }

        $accumulated = 0;

        if(is_object($last_trans) && $last_trans->accumulated >0) {
            if($type == "credit")
                $accumulated = $last_trans->accumulated + $amount;
            else if($last_trans->accumulated >= $amount)
                $accumulated = $last_trans->accumulated - $amount;
        }

        // sub account
        $sub_account = NULL;
        preg_match('/TKP#[A-Z0-9]{13}/', $transaction_content, $matches, PREG_OFFSET_CAPTURE);
        if(is_array($matches) && count($matches) > 0 && isset($matches[0][1])) {
            $sub_account = substr($transaction_content, $matches[0][1] + 4,13);
        } else {
            preg_match('/TKP[A-Z0-9]{13}/', $transaction_content, $matches, PREG_OFFSET_CAPTURE);
            if(is_array($matches) && count($matches) > 0 && isset($matches[0][1])) {
                $sub_account = substr($transaction_content, $matches[0][1] + 3,13);
            }
        }

        // payment code
        $code = NULL;
        $canSyncAccumulated = true;

        if(is_object($bank_account_details)) {
            if ($config->enableFilterTransaction ?? false) {
                $filterTransaction = FilterTransactionAction::forBankAccount($bank_account_details->id)
                    ->amountIn($type === 'credit')
                    ->description($transaction_content)
                    ->payload((array) $this->request->getVar())
                    ->filter();

                if ($filterTransaction['isRejected']) {
                    return $this->respondCreated([
                        'status' => 201,
                        'error' => null,
                        'messages' => [
                            'success' => 'Data Saved',
                        ],
                        'webhooks_result' => '',
                    ]);
                }

                $canSyncAccumulated = $filterTransaction['canSyncAccumulated'];
            }

            $code = PayCodeDetector::getCode($transaction_content, $bank_account_details->company_id);
        }


        $is_existed_transaction = $transactionsModel->getExitingSmsTransactionVcb($account_number, $transaction_content, $transaction_date, $amount, $type);

        if($is_existed_transaction === FALSE)
            $is_primary = 1;
        else
            $is_primary = 0;


        $data = [
            'uniq_id' => $this->request->getVar('uniq_id'),
            'gateway' => $this->request->getVar('gateway'),
            'source' => $this->request->getVar('source'),
            'transaction_date' => $transaction_date,
            'account_number' => $account_number,
            'amount' => $amount,
            'accumulated' => $canSyncAccumulated ? $accumulated : 0,
            'type' => $type,
            'transaction_content' => $transaction_content,
            'reference_number' => $this->request->getVar('reference_number'),
            'sub_account' => $sub_account,
            'code' => $code,
            'is_primary' => $is_primary
        ];

        $result = $ibankingModel->insert($data);
         
        if(is_numeric($result) && $result > 0) {
            $response = [
                'status'   => 201,
                'error'    => null,
                'messages' => [
                    'success' => 'Data Saved'
                ],
                'webhooks_result'    => ''
            ];
            
            $smsParserModel = model(SmsParserModel::class);
            $companyModel = model(CompanyModel::class);
            $transactionsModel = model(TransactionsModel::class);
 
            $amount_in = 0;
            $amount_out = 0;
            if($type == "credit")
                $amount_in = $data['amount'];
            else
                $amount_out = $data['amount'];
            $transaction_data = [
                "sms_id" => $result,
                "gateway" => $data['gateway'],
                "transaction_date" => $data['transaction_date'],
                "account_number" => $data['account_number'],
                "sub_account" => $data['sub_account'],
                "amount_in" => $amount_in,
                "amount_out" => $amount_out,
                "accumulated" => $data['accumulated'],
                "code" => $data['code'],
                "transaction_content" => $data['transaction_content'],
                "reference_number" => $data['reference_number'],
                "source" => "IBanking",
                "body" => "IBanking " . $data['transaction_content'],
                "parser_status" => "Success",
                'bank_account_id' => is_object($bank_account_details) ? $bank_account_details->id : null
            ];
            $parser_result = FALSE;
            if($is_existed_transaction === FALSE) {
                $parser_result = $smsParserModel->insert($transaction_data);

                
                // check to avoid duplicate transaction between sms and ibanking if they come same time
                usleep(100000);

                $is_existed_transaction2 = $transactionsModel->getExitingSmsTransactionVcb($account_number, $transaction_content, $transaction_date, $amount, $type);

                if($is_existed_transaction2) {
                    $smsParserModel->where(['id' => $parser_result])->delete();
                    helper('general');
                    add_system_log(['company_id' => 0, 'data_type' => 'duplicate_transaction_detected', 'description' => "Duplicate transaction, IBanking ID " . $result,'level' => 'Warning', 'by' => 'Ibanking Controller']);
                    return $this->respondCreated($response);
                } 
            }

             
            // webhooks & telegram queue
            if(is_numeric($parser_result) && is_object($bank_account_details)) {

                $transaction_details = $smsParserModel->find($parser_result);
                if(is_object($transaction_details) && is_numeric($transaction_details->account_number)) {
                    if ($transaction_details->sub_account) {
                        $bankSubAccountDetails = model(BankSubAccountModel::class)->where([
                            'sub_account' => $transaction_details->sub_account,
                            'bank_account_id' => $bank_account_details->id,
                            'va_active' => 1,
                            'active' => 1,
                            'merchant_id' => null,
                            'deleted_at' => null,
                        ])->first();
                    } else {
                        $bankSubAccountDetails = null;
                    }

                    try {
                        PushMobileTransactionNotificationQueueAction::run($transaction_details, $bank_account_details, $bankSubAccountDetails);
                    } catch (Exception $e) {
                        log_message('error', 'IBanking PushMobileTransactionNotificationQueueAction failed: ' . $e->getMessage() . ' - ' . $e->getTraceAsString() . ' - ' . json_encode($transaction_details));
                    }

                    // check company and subscription status
                    $company_check = $companyModel->select("tb_autopay_company.id")
                        ->join("tb_autopay_company_subscription","tb_autopay_company.id=tb_autopay_company_subscription.company_id")
                        ->join("tb_autopay_bank_account","tb_autopay_bank_account.company_id=tb_autopay_company.id")
                        ->where(['tb_autopay_bank_account.id' => $bank_account_details->id, 'tb_autopay_company_subscription.status'=>'Active','tb_autopay_company.status' => 'Active', 'tb_autopay_company.active' => 1])->get()->getResult();
                    
                    if(count($company_check) == 1) {
                        $webhookQueuable = $config->enableIBankingWebhookOffload;
                        $forceWebhookManually = ['webhook' => false, 'sapo' => false, 'haravan' => false, 'shopify' => false, 'googlesheet' => false,'output_device' => false, 'viber' => false];
                        $rabbitmq = null;

                        if ($webhookQueuable) {
                            $rabbitmq = new \App\Libraries\RabbitMQClient;
                            $webhookQueuable = $rabbitmq->connect();
                        }

                        if ($rabbitmq && $webhookQueuable) {
                            $msg = new AMQPMessage(
                                json_encode(['account_number' => $transaction_details->account_number, 'parser_id' => $parser_result]),
                                array('delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT)
                            );
        
                            // webhooks
                            try {
                                $rabbitmq->queueDeclare('webhook');
                                $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('webhook', 'name'));
                            } catch (\Exception $e) {
                                log_message('error', 'SMS webhook queue failed: ' . $e->getMessage());
                                $forceWebhookManually['webhook'] = true;
                            }
                            
                            // sapo
                            try {
                                $rabbitmq->queueDeclare('sapo');
                                $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('sapo', 'name'));
                            } catch (\Exception $e) {
                                log_message('error', 'SMS Sapo queue failed: ' . $e->getMessage());
                                $forceWebhookManually['sapo'] = true;
                            }
        
                            // haravan
                            try {
                                $rabbitmq->queueDeclare('haravan');
                                $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('haravan', 'name'));
                            } catch (\Exception $e) {
                                log_message('error', 'SMS Haravan queue failed: ' . $e->getMessage());
                                $forceWebhookManually['haravan'] = true;
                            } 

                            // shopify
                            try {
                                $rabbitmq->queueDeclare('shopify');
                                $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('shopify', 'name'));
                            } catch (\Exception $e) {
                                log_message('error', 'IBanking Shopify queue failed: ' . $e->getMessage());
                                $forceWebhookManually['shopify'] = true;
                            } 

                            // googlesheet
                            try {
                                $rabbitmq->queueDeclare('googlesheet');
                                $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('googlesheet', 'name'));
                            } catch (\Exception $e) {
                                log_message('error', 'IBanking API GoogleSheet queue failed: ' . $e->getMessage());
                                $forceWebhookManually['googlesheet'] = true;
                            } 

                            // output device
                            try {
                                $rabbitmq->queueDeclare('output_device');
                                $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('output_device', 'name'));
                            } catch (\Exception $e) {
                                log_message('error', 'Ibanking API output device queue failed: ' . $e->getMessage());
                                $forceWebhookManually['output_device'] = true;
                            } 
                            
                            // viber
                            try {
                                $rabbitmq->queueDeclare('viber');
                                $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('viber', 'name'));
                            } catch (\Exception $e) {
                                log_message('error', 'IBanking API Viber queue failed: ' . $e->getMessage());
                                $forceWebhookManually['viber'] = true;
                            }
        
                            $rabbitmq->close();
                        } 
                        
                        if (!$webhookQueuable || $forceWebhookManually['webhook']) {
                            // webhooks
                            $webhooksModel = model(WebhooksModel::class);
                            $webhooksModel->doWebhooks($transaction_details->account_number, $parser_result);
                        }
        
                        if (!$webhookQueuable || $forceWebhookManually['sapo']) {
                            // sapo
                            $sapoModel = model(SapoModel::class);
                            $sapoModel->doWebhooks($transaction_details->account_number, $parser_result);
                        }
        
                        if (!$webhookQueuable || $forceWebhookManually['haravan']) {
                            // haravan
                            $haravanModel = model(HaravanModel::class);
                            $haravanModel->doWebhooks($transaction_details->account_number, $parser_result);
                        }

                        if (!$webhookQueuable || $forceWebhookManually['shopify']) {
                            // shopify
                            ExecuteShopifyWebhooksAction::run($parser_result);
                        }

                        if (!$webhookQueuable || $forceWebhookManually['googlesheet']) {
                            // googlesheet
                            ExecuteGoogleSheetsWebhooksAction::run($parser_result);
                        }

                        if (!$webhookQueuable || $forceWebhookManually['webhook']) {
                            // telegram queue
                            $notificationTelegramModel = model(NotificationTelegramModel::class);
                            $notificationTelegramModel->checkAndAddQueue($parser_result);
                            
                            // lark messenger queue
                            $notificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);
                            $notificationLarkMessengerModel->checkAndAddQueue($parser_result);
                        }

                          // output device
                        if (!$webhookQueuable || $forceWebhookManually['output_device']) {
                            // output device queue
                            ExecuteOutputDevice::run($parser_result,0);
                        }
                        
                        if (!$webhookQueuable || $forceWebhookManually['viber']) {
                            // viber
                            (new \App\Features\Viber\ViberFeature)->sendNotificationByTransactionId($parser_result);
                        }

                        //counter
                        $counterModel = model(CounterModel::class);
                        $counterModel->transaction($company_check[0]->id,FALSE,$transaction_details->amount_in, $transaction_details->amount_out);
 
                        // cashflow
                        $bankAccountCashFlowModel = model(BankAccountCashflowModel::class);
                        $bankAccountCashFlowModel->amount_update($company_check[0]->id, date('Y-m-d', strtotime($transaction_details->transaction_date)), $transaction_details->bank_account_id, $transaction_details->amount_in, $transaction_details->amount_out, $transaction_details->sub_account,$transaction_details->accumulated);

                        $last_trans = $transaction_details;

                        if(is_object($last_trans))
                            $bankAccountModel->set(['last_transaction' => $last_trans->transaction_date, 'accumulated' => $last_trans->accumulated])->where(['id' => $bank_account_details->id])->update();
                    }
                }
            }
             
            return $this->respondCreated($response);
        } else {
            $this->fail("Cannot insert data", 400);
        }
    }
}