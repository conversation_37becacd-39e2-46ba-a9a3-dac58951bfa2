<?php

namespace App\Controllers\Coreapi;

use App\Actions\BankAccount\FilterTransactionAction;
use Exception;
use App\Models\SapoModel;
use CodeIgniter\Controller;
use App\Models\CompanyModel;
use App\Models\CounterModel;
use App\Models\HaravanModel;
use App\Models\MerchantModel;
use App\Models\WebhooksModel;
use App\Models\SmsParserModel;
use App\Models\BankAccountModel;
use App\Models\TransactionsModel;
use App\Models\ConfigurationModel;
use CodeIgniter\API\ResponseTrait;
use App\Models\BankSubAccountModel;
use PhpAmqpLib\Message\AMQPMessage;
use App\Models\AcbNotificationModel;
use CodeIgniter\HTTP\IncomingRequest;
use App\Models\AcbNotificationRawModel;
use App\Models\BankAccountCashflowModel;
use App\Models\NotificationTelegramModel;
use App\Models\VietinbankNotificationModel;
use CodeIgniter\RESTful\ResourceController;
use App\Models\NotificationLarkMessengerModel;
use App\Models\NotificationTelegramQueueModel;
use App\Models\VietinbankNotificationRawModel;
use App\Actions\Shopify\ExecuteShopifyWebhooksAction;
use App\Actions\GoogleSheets\ExecuteGoogleSheetsWebhooksAction;
use App\Actions\Transaction\CreateTransactionQueueAction;
use App\Actions\Merchant\PushMerchantTransactionNotificationAction;
use App\Actions\Mobile\PushMobileTransactionNotificationQueueAction;
use App\Actions\PayCodeDetector;

use App\Actions\OutputDevice\ExecuteOutputDevice;

class Acb extends ResourceController
{
    use ResponseTrait;

    protected $helpers = ['general'];

    protected $config;

    const BANK_ID = 3;

    public function __construct()
    {
        $this->config = config(\Config\Acb::class);
        $this->config->notifyDebug = $this->config->notifyDebug ?? false; 
        $this->config->notifyEnabledWebhookOffload = $this->config->notifyEnabledWebhookOffload ?? false; 
    }

    protected function standardResponse($message, $statusCode, $responseCode, $referenceCode = null)
    {
        return $this->respond([
            'timestamp' => date('Y-m-d\TH:i:s\Z'),
            'responseCode' => $responseCode,
            'message' => $message,
            'responseBody' => [
                'index' => 1,
                'referenceCode' => $referenceCode 
            ]
        ], $statusCode);
    }

    protected function authorize()
    {
        $key = explode('Basic ', $this->request->getHeaderLine('Authorization'))[1] ?? '';

        return $key && $key === base64_encode("{$this->config->notifyAuthUser}:{$this->config->notifyAuthPassword}");
    }

    public function notifyPrimaryTransaction()
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }

        if (!$this->authorize()) {
            return $this->standardResponse('Invalid token', 401, '********');
        }

        $json = json_decode(json_encode($this->request->getJSON()), true);

        log_message('error', 'ACB payload: ' . json_encode($json));

        $data = [
            'masterMeta' => $json['masterMeta'] ?? [],
            'requests' => $json['requests'] ?? [],
        ];

        $rules = [
            'masterMeta' => ['required'],
            'masterMeta.clientId' => ['required', 'regex_match[/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i]'],
            'masterMeta.clientRequestId' => ['required', 'regex_match[/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i]'],
            'requests' => ['required'],
            'requests.0.requestMeta' => ['required'],
            'requests.0.requestMeta.requestType' => ['required', 'in_list[NOTIFICATION]'],
            'requests.0.requestMeta.requestCode' => ['required', 'in_list[TRANSACTION_UPDATE,TRANSACTION_HISTORY]'],
            'requests.0.requestParams' => ['required'],
            'requests.0.requestParams.transactions' => ['required'],
            'requests.0.requestParams.transactions.*.transactionStatus' => ['required', 'in_list[COMPLETED,ERRORCORRECTED]'],
            'requests.0.requestParams.transactions.*.transactionChannel' => ['permit_empty'],
            'requests.0.requestParams.transactions.*.transactionCode' => ['required'],
            'requests.0.requestParams.transactions.*.accountNumber' => ['required'],
            'requests.0.requestParams.transactions.*.transactionDate' => ['required', 'valid_date'],
            'requests.0.requestParams.transactions.*.effectiveDate' => ['required', 'valid_date'],
            'requests.0.requestParams.transactions.*.debitOrCredit' => ['required', 'in_list[debit,credit]'],
            'requests.0.requestParams.transactions.*.amount' => ['required'],
            'requests.0.requestParams.transactions.*.transactionContent' => ['required'],
            'requests.0.requestParams.transactions.*.virtualAccountInfo' => ['permit_empty'],
            'requests.0.requestParams.transactions.*.virtualAccountInfo.virtualAccountPrefix' => ['permit_empty'],
            'requests.0.requestParams.transactions.*.virtualAccountInfo.virtualAccount' => ['permit_empty'],
            'requests.0.requestParams.transactions.*.transactionEntityAttribute' => ['permit_empty'],
            'requests.0.requestParams.transactions.*.transactionEntityAttribute.traceNumber' => ['permit_empty'],
            'requests.0.requestParams.transactions.*.transactionEntityAttribute.beneficiaryName' => ['permit_empty'],
            'requests.0.requestParams.transactions.*.transactionEntityAttribute.beneficiaryAccountNumber' => ['permit_empty'],
            'requests.0.requestParams.transactions.*.transactionEntityAttribute.receiverBankName' => ['permit_empty'],
            'requests.0.requestParams.transactions.*.transactionEntityAttribute.remitterName' => ['permit_empty'],
            'requests.0.requestParams.transactions.*.transactionEntityAttribute.remitterAccountNumber' => ['permit_empty'],
            'requests.0.requestParams.transactions.*.transactionEntityAttribute.issuerBankName' => ['permit_empty'],
            'requests.0.requestParams.transactions.*.transactionEntityAttribute.virtualAccount' => ['permit_empty'],
            'requests.0.requestParams.transactions.*.transactionEntityAttribute.referenceNumber' => ['permit_empty'],
            'requests.0.requestParams.transactions.*.transactionEntityAttribute.partnerCustomerCode' => ['permit_empty'],
            'requests.0.requestParams.transactions.*.transactionEntityAttribute.partnerCustomerName' => ['permit_empty'],
            'requests.0.requestParams.transactions.*.transactionEntityAttribute.partnerCustomerType' => ['permit_empty'],
            'requests.0.requestParams.pagination' => ['required'],
            'requests.0.requestParams.pagination.page' => ['required'],
            'requests.0.requestParams.pagination.pageSize' => ['required'],
            'requests.0.requestParams.pagination.totalPage' => ['required'],
        ];

        if (! $this->validateData($data, $rules)) {
            if ($this->config->notifyDebug)
                log_message('error', "ACB Notification Error: Bad request " . json_encode($this->validator->getErrors()) . ' - '. json_encode($data));

            return $this->standardResponse('Invalid payload: ' . implode(', ', $this->validator->getErrors()), 400, '********');
        }

        $acbNotificationRawModel = model(AcbNotificationRawModel::class);
        $acbNotificationModel = model(AcbNotificationModel::class);
        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);

        if ($this->config->notifyDebug) {
            $acbNotificationRawId = $acbNotificationRawModel->insert(['action' => 'notifyPrimaryTransaction', 'payload' => json_encode($data), 'ip' => $this->request->getIPAddress()]);
        } else {
            $acbNotificationRawId = 0;
        }

        foreach ($data['requests'][0]['requestParams']['transactions'] as $index => $transaction) {
            $duplicatedTransaction = $acbNotificationModel->where([
                'transactionCode' => $transaction['transactionCode'],
                'accountNumber' => $transaction['accountNumber'],
            ])->countAllResults();
            if ($duplicatedTransaction) {
                if ($this->config->notifyDebug) log_message('error', "ACB Notification Error: Transaction Duplicated - Raw #" . $acbNotificationRawId);
    
                return $this->standardResponse('Duplicated transaction', 409, '********');
            }

            $bankAccountDetails = $bankAccountModel->where(['account_number' => $transaction['accountNumber'], 'bank_id' => Acb::BANK_ID, 'active' => true])->first();
    
            if (!is_object($bankAccountDetails)) {
                log_message('error', "ACB Notification Error: Account number does not exist - Raw #" . $acbNotificationRawId);
    
                return $this->standardResponse('Account number does not exist', 400, '********');
            }            

            if ($bankAccountDetails->bank_id == Acb::BANK_ID && $bankAccountDetails->bank_sms) {
                if ($this->config->notifyDebug) {
                    log_message('error', 'Skip Bank API notification when ACB account switch to SMS connection');
                    log_message('error', json_encode($data));
                }
                
                continue;
            }

            $bankSubAccountDetails = null;

            if (isset($transaction['virtualAccountInfo']) && isset($transaction['virtualAccountInfo']['vaNbr'])) {
                $bankSubAccountDetails = $bankSubAccountModel->where([
                    'sub_account' => $transaction['virtualAccountInfo']['vaNbr'],
                    'bank_account_id' => $bankAccountDetails->id,
                    'va_active' => 1,
                    'active' => 1,
                    'deleted_at' => null,
                ])->first();
            } else {
                preg_match('/TKP[A-Z0-9]{2,16}/', $transaction['transactionContent'], $matches, PREG_OFFSET_CAPTURE);
                if (is_array($matches) && count($matches) > 0 && isset($matches[0][0])) {
                    $contentVa = str_replace('TKP', '', $matches[0][0]);

                    $bankSubAccountDetails = $bankSubAccountModel->where([
                        'sub_account' => $contentVa,
                        'bank_account_id' => $bankAccountDetails->id,
                        'va_active' => 1,
                        'active' => 1,
                        'deleted_at' => null,
                    ])->first();
                }
            }

            $handled = $this->handleIncomingTransaction($transaction, $acbNotificationRawId, $bankAccountDetails, $bankSubAccountDetails);

            if (!$handled) 
                return $this->standardResponse('Service unavailable, please retry', 500, '********');
        }

        return $this->standardResponse('Success', 200, '********');
    }

    public function notifyVaTransaction()
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }

        if (!$this->authorize()) {
            return $this->standardResponse('Invalid token', 401, '********');
        }

        $json = json_decode(json_encode($this->request->getJSON()), true);

        log_message('error', 'ACB payload: ' . json_encode($json));

        $data = [
            'requestTrace' => $json['requestTrace'] ?? [],
            'requestDateTime' => $json['requestDateTime'] ?? [],
            'requestParameters' => $json['requestParameters'] ?? [],
        ];

        $rules = [
            'requestTrace' => ['required', 'regex_match[/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i]'],
            'requestDateTime' => ['required', 'valid_date'],
            'requestParameters' => ['required'],
            'requestParameters.masterMeta' => ['required'],
            'requestParameters.masterMeta.clientId' => ['required', 'regex_match[/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i]'],
            'requestParameters.masterMeta.clientRequestId' => ['required', 'regex_match[/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i]'],
            'requestParameters.request' => ['required'],
            'requestParameters.request.requestMeta' => ['required'],
            'requestParameters.request.requestMeta.requestType' => ['required', 'in_list[NOTIFICATION]'],
            'requestParameters.request.requestMeta.requestCode' => ['required', 'in_list[TRANSACTION_UPDATE,TRANSACTION_HISTORY]'],
            'requestParameters.request.requestParams' => ['required'],
            'requestParameters.request.requestParams.transactions' => ['required'],
            'requestParameters.request.requestParams.transactions.*.transactionStatus' => ['required', 'in_list[COMPLETED,ERRORCORRECTED]'],
            'requestParameters.request.requestParams.transactions.*.transactionChannel' => ['permit_empty'],
            'requestParameters.request.requestParams.transactions.*.transactionDate' => ['required', 'valid_date'],
            'requestParameters.request.requestParams.transactions.*.effectiveDate' => ['required', 'valid_date'],
            'requestParameters.request.requestParams.transactions.*.debitOrCredit' => ['required', 'in_list[debit,credit]'],
            'requestParameters.request.requestParams.transactions.*.amount' => ['required'],
            'requestParameters.request.requestParams.transactions.*.transactionContent' => ['required'],
            'requestParameters.request.requestParams.transactions.*.virtualAccountInfo' => ['permit_empty'],
            'requestParameters.request.requestParams.transactions.*.virtualAccountInfo.virtualAccountPrefix' => ['permit_empty'],
            'requestParameters.request.requestParams.transactions.*.virtualAccountInfo.virtualAccount' => ['permit_empty'],
            'requestParameters.request.requestParams.transactions.*.transactionEntityAttribute' => ['permit_empty'],
            'requestParameters.request.requestParams.transactions.*.transactionEntityAttribute.traceNumber' => ['permit_empty'],
            'requestParameters.request.requestParams.transactions.*.transactionEntityAttribute.beneficiaryName' => ['permit_empty'],
            'requestParameters.request.requestParams.transactions.*.transactionEntityAttribute.beneficiaryAccountNumber' => ['permit_empty'],
            'requestParameters.request.requestParams.transactions.*.transactionEntityAttribute.receiverBankName' => ['permit_empty'],
            'requestParameters.request.requestParams.transactions.*.transactionEntityAttribute.remitterName' => ['permit_empty'],
            'requestParameters.request.requestParams.transactions.*.transactionEntityAttribute.remitterAccountNumber' => ['permit_empty'],
            'requestParameters.request.requestParams.transactions.*.transactionEntityAttribute.issuerBankName' => ['permit_empty'],
            'requestParameters.request.requestParams.transactions.*.transactionEntityAttribute.virtualAccount' => ['permit_empty'],
            'requestParameters.request.requestParams.transactions.*.transactionEntityAttribute.referenceNumber' => ['permit_empty'],
            'requestParameters.request.requestParams.transactions.*.transactionEntityAttribute.partnerCustomerCode' => ['permit_empty'],
            'requestParameters.request.requestParams.transactions.*.transactionEntityAttribute.partnerCustomerName' => ['permit_empty'],
            'requestParameters.request.requestParams.transactions.*.transactionEntityAttribute.partnerCustomerType' => ['permit_empty'],
            'requestParameters.request.requestParams.pagination' => ['required'],
            'requestParameters.request.requestParams.pagination.page' => ['required'],
            'requestParameters.request.requestParams.pagination.pageSize' => ['required'],
            'requestParameters.request.requestParams.pagination.totalPage' => ['required'],
        ];

        if (! $this->validateData($data, $rules)) {
            if ($this->config->notifyDebug)
                log_message('error', "ACB Notification Error: Bad request " . json_encode($this->validator->getErrors()) . ' - '. json_encode($data));

            return $this->standardResponse('Invalid payload: ' . implode(', ', $this->validator->getErrors()), 400, '********');
        }

        
        $acbNotificationRawModel = model(AcbNotificationRawModel::class);
        $bankAccountModel = model(BankAccountModel::class);
        $acbNotificationModel = model(AcbNotificationModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);

        if ($this->config->notifyDebug) {
            $acbNotificationRawId = $acbNotificationRawModel->insert(['action' => 'notifyVaTransaction', 'payload' => json_encode($data), 'ip' => $this->request->getIPAddress()]);
        } else {
            $acbNotificationRawId = 0;
        }

        foreach ($data['requestParameters']['request']['requestParams']['transactions'] as $index => $transaction) {
            $duplicatedTransaction = $acbNotificationModel->where([
                'tea_virtualAccount' => $transaction['transactionEntityAttribute']['virtualAccount'],
                'amount' => $transaction['amount'],
                'debitOrCredit' => $transaction['debitOrCredit'],
                'transactionContent' => $transaction['transactionContent'],
                'transactionDate' => $transaction['transactionDate'],
            ])->countAllResults();
            if ($duplicatedTransaction) {
                if ($this->config->notifyDebug) log_message('error', "ACB Notification Error: Transaction Duplicated - Raw #" . $acbNotificationRawId);

                return $this->standardResponse('Duplicated transaction', 409, '********');
            }

            $bankSubAccountDetails = $bankSubAccountModel->where([
                'sub_account' => $transaction['transactionEntityAttribute']['virtualAccount'], 
                'va_active' => 1, 
                'active ' => 1, 
                'deleted_at' => null
            ])->first();
            
            $bankAccountDetails = $bankAccountModel
                ->select('tb_autopay_bank_account.id, tb_autopay_bank_account.account_number, tb_autopay_bank_account.label, tb_autopay_bank_account.active, tb_autopay_bank_account.created_at, tb_autopay_bank_account.account_number, tb_autopay_bank_account.account_holder_name, tb_autopay_bank_account.last_transaction, tb_autopay_bank_account.accumulated,tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.bank_id,tb_autopay_bank_account.bank_sms, tb_autopay_bank_account.bank_sms_connected, tb_autopay_bank_account.bank_api, tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.identification_number, tb_autopay_bank_account.phone_number, tb_autopay_bank_account.company_id, tb_autopay_bank_account.sim_id, tb_autopay_acb_bank_account_metadata.realtime_transfer_type, tb_autopay_bank_account.merchant_id')
                ->join('tb_autopay_acb_bank_account_metadata', 'tb_autopay_acb_bank_account_metadata.bank_account_id = tb_autopay_bank_account.id')
                ->where(['tb_autopay_bank_account.id' => $bankSubAccountDetails->bank_account_id, 'tb_autopay_bank_account.active' => true])->first();

            if (!is_object($bankSubAccountDetails) || !is_object($bankAccountDetails)) {
                log_message('error', "ACB Notification Error: VA number does not exist - Raw #" . $acbNotificationRawId);
    
                return $this->standardResponse('VA number does not exist', 400, '********');
            }

            if (in_array($bankAccountDetails->realtime_transfer_type, ['ALL', 'CREDIT'])) {
                if ($this->config->notifyDebug) {
                    log_message('error', 'Skip VA notification when ACB primary account was registered ALL or CREDIT');
                    log_message('error', json_encode($data));
                }

                continue;
            }

            if ($bankAccountDetails->bank_id == Acb::BANK_ID && $bankAccountDetails->bank_sms) {
                if ($this->config->notifyDebug) {
                    log_message('error', 'Skip Bank API notification when ACB account switch to SMS connection');
                    log_message('error', json_encode($data));
                }
    
                continue;
            }

            $transaction['accountNumber'] = $bankAccountDetails->account_number;
            $handled = $this->handleIncomingTransaction($transaction, $acbNotificationRawId, $bankAccountDetails, $bankSubAccountDetails);

            if (!$handled) 
                return $this->standardResponse('Service unavailable, please retry', 500, '********');
        } 

        return $this->standardResponse('Success', 200, '********');
    }

    protected function handleIncomingTransaction($transaction, $acbNotificationRawId, $bankAccountDetails, $bankSubAccountDetails = null)
    {
        $merchantConfig = config(\Config\Merchant::class);

        if (!isset($transaction['accountNumber'])) {
            log_message('error', 'ACB Notification Error: Missing accountNumber in VA notification - ' . json_encode($transaction));
            return false;
        }

        $acbNotificationModel = model(AcbNotificationModel::class);
        $bankAccountModel = model(BankAccountModel::class);
        $smsParserModel = model(SmsParserModel::class);
        $companyModel = model(CompanyModel::class);
        $transactionsModel = model(TransactionsModel::class);
        $configurationModel = model(ConfigurationModel::class);

        $safeAcbNotificationData = [
            'raw_id' => $acbNotificationRawId,
            'transactionStatus' => $transaction['transactionStatus'],
            'transactionChannel' => $transaction['transactionChannel'] ?? '',
            'transactionCode' => $transaction['transactionCode'] ?? null,
            'accountNumber' => $transaction['accountNumber'] ?? null,
            'transactionDate' => $transaction['transactionDate'] ?? null,
            'effectiveDate' => $transaction['effectiveDate'] ?? null,
            'debitOrCredit' => $transaction['debitOrCredit'],
            'amount' => $transaction['amount'],
            'transactionContent' => $transaction['transactionContent'],
            'vai_vaPrefixCd' => $transaction['virtualAccountInfo']['vaPrefixCd'] ?? null,
            'vai_vaNbr' => $transaction['virtualAccountInfo']['vai_vaNbr'] ?? null,
            'tea_traceNumber' => $transaction['transactionEntityAttribute']['traceNumber'] ?? null,
            'tea_beneficiaryName' => $transaction['transactionEntityAttribute']['beneficiaryName'] ?? null,
            'tea_beneficiaryAccountNumber' => $transaction['transactionEntityAttribute']['beneficiaryAccountNumber'] ?? null,
            'tea_receiverBankName' => $transaction['transactionEntityAttribute']['receiverBankName'] ?? null,
            'tea_remitterName' => $transaction['transactionEntityAttribute']['remitterName'] ?? null,
            'tea_remitterAccountNumber' => $transaction['transactionEntityAttribute']['remitterAccountNumber'] ?? null,
            'tea_issuerBankName' => $transaction['transactionEntityAttribute']['issuerBankName'] ?? null,
            'tea_virtualAccount' => $transaction['transactionEntityAttribute']['virtualAccount'] ?? null,
            'tea_referenceNumber' => $transaction['transactionEntityAttribute']['referenceNumber'] ?? null,
            'tea_partnerCustomerCode' => $transaction['transactionEntityAttribute']['partnerCustomerCode'] ?? null,
            'tea_partnerCustomerName' => $transaction['transactionEntityAttribute']['partnerCustomerName'] ?? null,
            'tea_partnerCustomerType' => $transaction['transactionEntityAttribute']['partnerCustomerType'] ?? null,
        ];
        $acbNotificationId = $acbNotificationModel->insert($safeAcbNotificationData);

        if (!$acbNotificationId) {
            log_message('error', 'ACB Notification Error: ACBNotificationModel insert failed - ' . json_encode($transaction));
            return false;
        }

        if (($this->config->enableFilterTransaction ?? false) && ! $bankAccountDetails->merchant_id) {
            $filterTransaction = FilterTransactionAction::forBankAccount($bankAccountDetails->id)
                ->amountIn($transaction['debitOrCredit'] === 'credit')
                ->description($transaction['transactionContent'])
                ->payload($transaction)
                ->filter();

            if ($filterTransaction['isRejected']) {
                return true;
            }
        }

        $code = null;

        if (is_object($bankAccountDetails)) {
            $code = PayCodeDetector::getCode($transaction['transactionContent'], $bankAccountDetails->company_id);
        }

        $safeTransactionData = [
            'sms_id' => $acbNotificationId,
            'gateway' => 'ACB',
            'transaction_date' => date("Y-m-d H:i:s", strtotime($safeAcbNotificationData['transactionDate'])),
            'account_number' => $safeAcbNotificationData['accountNumber'],
            'sub_account' => $bankSubAccountDetails->sub_account ?? null,
            'amount_out' => $safeAcbNotificationData['debitOrCredit'] === 'debit' ? $safeAcbNotificationData['amount'] : 0,
            'amount_in' => $safeAcbNotificationData['debitOrCredit'] === 'credit' ? $safeAcbNotificationData['amount'] : 0,
            'accumulated' => 0,
            'code' => $code,
            'transaction_content' => $safeAcbNotificationData['transactionContent'],
            'reference_number' => $safeAcbNotificationData['transactionCode'],
            'source' => 'BankAPINotify',
            'body' => 'BankAPINotify ' . $safeAcbNotificationData['transactionContent'],
            'parser_status' => 'Success',
            'from_account_number' => $safeAcbNotificationData['tea_remitterAccountNumber'] ?? '',
            'from_bin' => '',
            'bank_account_id' => $bankAccountDetails->id,
            'merchant_id' => $bankAccountDetails->merchant_id,
            'transaction_id' => uuid()
        ];

        $queueTransactionId = null;
        $transactionId = null; // primary id of tb_autopay_sms_parsed table
        $transactionDetails = null;

        if ($bankAccountDetails->merchant_id && $merchantConfig->enabledTransactionInsertionOffload) {
            $queueTransactionId = CreateTransactionQueueAction::run((object) $safeTransactionData, $bankAccountDetails);
        }

        if (!$queueTransactionId) {
            $transactionId = $smsParserModel->insert($safeTransactionData);
            $transactionDetails = $smsParserModel->find($transactionId);
        }

        if (!$transactionId && !$queueTransactionId) {
            $acbNotificationModel->where('id', $acbNotificationId)->delete();
            log_message('error', 'ACB Notification Error: SMSParserModel insert failed - Raw #' . $acbNotificationRawId);
            return false;
        }

        if (is_object($transactionDetails) && is_numeric($transactionDetails->account_number) && $bankAccountDetails->merchant_id && !$queueTransactionId) {
            try {
                PushMerchantTransactionNotificationAction::run(
                    $bankAccountDetails, 
                    $transactionDetails,
                    $merchantConfig->enabledPushTransactionNotificationOffload
                );
            } catch (Exception $e) {
                log_message('error', '[PushableMerchantTransactionNotificationAction] ' . $e->getMessage() . ' - ' . $e->getTraceAsString());
            }
        }

        if (is_object($transactionDetails) && is_numeric($transactionDetails->account_number) && !$bankAccountDetails->merchant_id) {
            try {
                PushMobileTransactionNotificationQueueAction::run($transactionDetails, $bankAccountDetails, $bankSubAccountDetails);
            } catch (Exception $e) {
                log_message('error', 'ACB PushMobileTransactionNotificationQueueAction failed: ' . $e->getMessage() . ' - ' . $e->getTraceAsString() . ' - ' . json_encode($transactionDetails));
            }
            
            $companyCheck = $companyModel->select("tb_autopay_company.id")
                ->join("tb_autopay_company_subscription", "tb_autopay_company.id=tb_autopay_company_subscription.company_id")
                ->join("tb_autopay_bank_account", "tb_autopay_bank_account.company_id=tb_autopay_company.id")
                ->where([
                    'tb_autopay_bank_account.id' => $bankAccountDetails->id, 
                    'tb_autopay_company_subscription.status' => 'Active',
                    'tb_autopay_company.status' => 'Active', 'tb_autopay_company.active' => 1
                ])->get()->getResult();
            
            if (count($companyCheck) == 1) {
                $webhookQueuable = $this->config->notifyEnabledWebhookOffload ?? false;
                $forceWebhookManually = ['webhook' => false, 'sapo' => false, 'haravan' => false, 'shopify' => false, 'googlesheet' => false,'output_device' => false, 'viber' => false];
                $rabbitmq = null;

                if ($webhookQueuable) {
                    $rabbitmq = new \App\Libraries\RabbitMQClient;
                    $webhookQueuable = $rabbitmq->connect();
                }

                if ($rabbitmq && $webhookQueuable) {
                    $msg = new AMQPMessage(
                        json_encode(['account_number' => $transactionDetails->account_number, 'parser_id' => $transactionId]),
                        array('delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT)
                    );

                    // webhooks
                    try {
                        $rabbitmq->queueDeclare('webhook');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('webhook', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'Vietinbank API webhook queue failed: ' . $e->getMessage());
                        $forceWebhookManually['webhook'] = true;
                    }
                    
                    // sapo
                    try {
                        $rabbitmq->queueDeclare('sapo');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('sapo', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'Vietinbank API Sapo queue failed: ' . $e->getMessage());
                        $forceWebhookManually['sapo'] = true;
                    }

                    // haravan
                    try {
                        $rabbitmq->queueDeclare('haravan');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('haravan', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'Vietinbank API Haravan queue failed: ' . $e->getMessage());
                        $forceWebhookManually['haravan'] = true;
                    } 

                    // shopify
                    try {
                        $rabbitmq->queueDeclare('shopify');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('shopify', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'Vietinbank API Shopify queue failed: ' . $e->getMessage());
                        $forceWebhookManually['shopify'] = true;
                    }

                    // googlesheet
                    try {
                        $rabbitmq->queueDeclare('googlesheet');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('googlesheet', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'ACB API GoogleSheet queue failed: ' . $e->getMessage());
                        $forceWebhookManually['googlesheet'] = true;
                    } 

                    // output device
                    try {
                        $rabbitmq->queueDeclare('output_device');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('output_device', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'ACB API output device queue failed: ' . $e->getMessage());
                        $forceWebhookManually['output_device'] = true;
                    } 
                    
                    // viber
                    try {
                        $rabbitmq->queueDeclare('viber');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('viber', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'ACB API Viber queue failed: ' . $e->getMessage());
                        $forceWebhookManually['viber'] = true;
                    }

                    $rabbitmq->close();
                } 
                
                if (!$webhookQueuable || $forceWebhookManually['webhook']) {
                    // webhooks
                    $webhooksModel = model(WebhooksModel::class);
                    $webhooksModel->doWebhooks($transactionDetails->account_number, $transactionId);
                }

                if (!$webhookQueuable || $forceWebhookManually['sapo']) {
                    // sapo
                    $sapoModel = model(SapoModel::class);
                    $sapoModel->doWebhooks($transactionDetails->account_number, $transactionId);
                }

                if (!$webhookQueuable || $forceWebhookManually['haravan']) {
                    // haravan
                    $haravanModel = model(HaravanModel::class);
                    $haravanModel->doWebhooks($transactionDetails->account_number, $transactionId);
                }

                if (!$webhookQueuable || $forceWebhookManually['shopify']) {
                    // shopify
                    ExecuteShopifyWebhooksAction::run($transactionId);
                }

                if (!$webhookQueuable || $forceWebhookManually['googlesheet']) {
                    // googlesheet
                    ExecuteGoogleSheetsWebhooksAction::run($transactionId);
                }
                
                if (!$webhookQueuable || $forceWebhookManually['webhook']) {
                    // telegram queue
                    $notificationTelegramModel = model(NotificationTelegramModel::class);
                    $notificationTelegramModel->checkAndAddQueue($transactionId);
                    
                    // lark messenger queue
                    $notificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);
                    $notificationLarkMessengerModel->checkAndAddQueue($transactionId);
                }

                 // output device
                 if (!$webhookQueuable || $forceWebhookManually['output_device']) {
                    // output device queue
                    ExecuteOutputDevice::run($transactionId,0);
                }
                
                if (!$webhookQueuable || $forceWebhookManually['viber']) {
                    // viber
                    (new \App\Features\Viber\ViberFeature)->sendNotificationByTransactionId($transactionId);
                }

                //counter
                $counterModel = model(CounterModel::class);
                $counterModel->transaction($companyCheck[0]->id, false, $transactionDetails->amount_in, $transactionDetails->amount_out);

                // cashflow
                $bankAccountCashFlowModel = model(BankAccountCashflowModel::class);
                $bankAccountCashFlowModel->amount_update($companyCheck[0]->id, date('Y-m-d', strtotime($transactionDetails->transaction_date)), $transactionDetails->bank_account_id, $transactionDetails->amount_in, $transactionDetails->amount_out, $transactionDetails->sub_account, $transactionDetails->accumulated);

                $lastTransaction = $transactionDetails;

                if (is_object($lastTransaction))
                    $bankAccountModel->set(['last_transaction' => $lastTransaction->transaction_date, 'accumulated' => $lastTransaction->accumulated])->where(['id' => $bankAccountDetails->id])->update();
            }
        }

        return true;
    }
}