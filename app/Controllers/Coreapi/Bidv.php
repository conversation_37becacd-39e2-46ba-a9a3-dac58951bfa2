<?php

namespace App\Controllers\Coreapi;

use App\Actions\BankAccount\FilterTransactionAction;
use Exception;
use App\Models\CompanyModel;
use App\Models\SmsParserModel;
use App\Actions\PayCodeDetector;
use App\Models\BankAccountModel;
use App\Models\TransactionsModel;
use CodeIgniter\API\ResponseTrait;
use App\Models\BankSubAccountModel;
use PhpAmqpLib\Message\AMQPMessage;
use App\Models\BidvNotificationModel;
use App\Models\BankAccountCashflowModel;
use App\Models\BidvNotificationRawModel;
use App\Models\NotificationTelegramModel;
use CodeIgniter\RESTful\ResourceController;
use App\Models\NotificationLarkMessengerModel;
use App\Actions\Shopify\ExecuteShopifyWebhooksAction;
use App\Actions\GoogleSheets\ExecuteGoogleSheetsWebhooksAction;
use App\Actions\IsBidvVaOrderNumber;
use App\Actions\Mobile\PushMobileTransactionNotificationQueueAction;
use App\Models\VAModel;
use App\Models\VAOrderModel;
use Config\Services;
use App\Actions\OutputDevice\ExecuteOutputDevice;

class Bidv extends ResourceController
{
    use ResponseTrait;

    protected IsBidvVaOrderNumber $isBidvVaOrderNumber;

    public function __construct()
    {
        $this->config = config(\App\Config\Bidv::class);
        $this->config->debug = $this->config->debug ?? false;
        $this->isBidvVaOrderNumber = new IsBidvVaOrderNumber();
    }

    public function getbill()
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }

        $rawData = [
            'customer_id' => $this->request->getVar('customer_id'),
            'checksum' => $this->request->getVar('checksum'),
        ];

        $rules = [
            'customer_id' => ['required', 'string', 'regex_match[/^[0-9A-Z]+$/]'],
            'checksum' => ['required', 'string'],
        ];

        if (! $this->validateData($rawData, $rules)) {
            if ($this->config->debug) {
                log_message('error', 'BIDV Lookup VA Error: Bad request ' . json_encode($this->validator->getErrors()));
                log_message('error', 'BIDV Lookup VA Payload: ' . json_encode($rawData));
            }

            return $this->respondStandardResponse(400, '001', implode(', ', $this->validator->getErrors()));
        }

        $validChecksum = hash('sha256', "{$this->config->accessCode}|{$rawData['customer_id']}");

        if ($rawData['checksum'] !== $validChecksum) {
            return $this->respondStandardResponse(403, '004', 'Checksum is invalid');
        }

        log_message('error', 'BIDV VA getbill request: ' . json_encode($rawData));

        // Only tesing
        if (getenv('CI_ENVIRONMENT') == 'development' || getenv('CI_ENVIRONMENT') == 'production') {
            $dynamicVAList = ['V3SEPABC', 'V3SEPXYZ', 'V3SEPV1', 'V3SEPVSEP', 'V3SEPV00000', 'V3DDVABC', 'V3DDVXYZ', 'V3DDVV1', 'V3DDVVDDV', 'V3DDVV00000'];

            if (in_array($rawData['customer_id'], $dynamicVAList)) {
                log_message('error', 'BIDV dynamic VA getbill response: Success ' . json_encode([
                    'customer_id' => $rawData['customer_id'],
                    'customer_name' => 'DYNAMIC VA ' . $rawData['customer_id'],
                    'amount' => 3000,
                    'active' => 1,
                    'type' => 1,
                ]));

                return $this->respondStandardResponse(200, '000', 'success', [
                    'customer_id' => $rawData['customer_id'],
                    'customer_name' => 'DYNAMIC VA ' . $rawData['customer_id'],
                    'amount' => 3000,
                    'active' => 1,
                    'type' => 1,
                ]);
            }
        }
        // Only tesing

        try {
            if ($this->isBidvVaOrderNumber->execute($rawData['customer_id'])) {
                $va = Services::cache()->get("bidv_va_{$rawData['customer_id']}");

                if (! $va) {
                    $va = $this->getDynamicVa($rawData['customer_id']);
                }
    
                if (
                    ! $va
                    || ($va->expired_at !== null && $va->expired_at < date('Y-m-d H:i:s'))
                    || $va->status != 'Unpaid'
                    || ! in_array($va->order_status, ['Pending', 'Partially'])
                ) {
                    return $this->respondStandardResponse(400, '011', 'Customer ID is invalid / not exist');
                }

                $responseData = [
                    'customer_id' => $va->va_number,
                    'customer_name' => $va->va_holder_name ?: $va->account_holder_name,
                    'active' => 1,
                    'type' => $va->amount ? 1 : 0,
                ];

                if ($va->amount) {
                    $responseData['amount'] = floatval($va->amount);
                }
    
                log_message('error', 'BIDV dynamic VA getbill response: Success ' . json_encode($responseData));
    
                return $this->respondStandardResponse(200, '000', 'success', $responseData);
            }
        } catch (\Exception $e) {
            log_message('error', 'BIDV dynamic VA getbill response: ' . $e->getMessage());
        }

        $bankSubAccountModel = model(BankSubAccountModel::class);
        $bankSubAccount = $bankSubAccountModel
            ->select(['tb_autopay_bank_sub_account.sub_account as sub_account', 'tb_autopay_bank_sub_account.sub_holder_name as sub_holder_name', 'tb_autopay_bank_sub_account.va_active as va_active'])
            ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id = tb_autopay_bank_sub_account.bank_account_id')
            ->where([
                'tb_autopay_bank_account.bank_id' => 9,
                'tb_autopay_bank_account.bank_api' => 1,
                'tb_autopay_bank_account.bank_api_connected' => 1,
                'tb_autopay_bank_account.merchant_id' => null,
                'tb_autopay_bank_sub_account.sub_account' => $rawData['customer_id'],
            ])
            ->get()->getRow();

        if (!$bankSubAccount) {
            return $this->respondStandardResponse(400, '011', 'Customer ID is invalid / not exist');
        }

        log_message('error', 'BIDV static VA getbill response: Success ' . json_encode([
            'customer_id' => $bankSubAccount->sub_account,
            'customer_name' => $bankSubAccount->sub_holder_name,
            'active' => (int) $bankSubAccount->va_active,
            'type' => 0,
        ]));

        return $this->respondStandardResponse(200, '000', 'success', [
            'customer_id' => $bankSubAccount->sub_account,
            'customer_name' => $bankSubAccount->sub_holder_name,
            'active' => (int) $bankSubAccount->va_active,
            'type' => 0,
        ]);
    }

    public function paybill()
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }
        
        $rawData = [
            'trans_id' => $this->request->getVar('trans_id'),
            'trans_date' => $this->request->getVar('trans_date'),
            'customer_id' => $this->request->getVar('customer_id'),
            'amount' => $this->request->getVar('amount'),
            'remark' => $this->request->getVar('remark'),
            'checksum' => $this->request->getVar('checksum'),
        ];

        $rules = [
            'trans_id' => ['required', 'string'],
            'trans_date' => ['required', 'string', 'regex_match[/^\d{14}$/]'],
            'customer_id' => ['required', 'string', 'regex_match[/^[0-9A-Z]+$/]'],
            'amount' => ['required', 'integer'],
            'remark' => ['required', 'string'],
            'checksum' => ['required', 'string']
        ];

        if (! $this->validateData($rawData, $rules)) {
            if ($this->config->debug) {
                log_message('error', 'BIDV Notification Error: Bad request ' . json_encode($this->validator->getErrors()));
                log_message('error', 'BIDV Notification Payload: ' . json_encode($rawData));
            }

            return $this->respondStandardResponse(400, '001', implode(', ', $this->validator->getErrors()));
        }

        $validChecksum = hash('sha256', "{$this->config->accessCode}|{$rawData['trans_id']}|{$rawData['trans_date']}|{$rawData['customer_id']}|{$rawData['amount']}");

        if ($rawData['checksum'] !== $validChecksum) {
            return $this->respondStandardResponse(403, '004', 'Checksum is invalid');
        }

        $bidvNotificationRawModel = model(BidvNotificationRawModel::class);
        $bidvNotificationModel = model(BidvNotificationModel::class);
        $bidvNotificationRawId = $bidvNotificationRawModel->insert(['payload' => json_encode($rawData), 'ip' =>$this->request->getIPAddress()]);

        $duplicatedTransaction = $bidvNotificationModel->where('trans_id', $rawData['trans_id'])->countAllResults();
        if ($duplicatedTransaction) {
            if ($this->config->debug) log_message('error', "BIDV Notification Error: Transaction Duplicated - Raw #" . $bidvNotificationRawId);

            return $this->respondStandardResponse(400, '023', 'Transaction is exist');
        }

        // Only tesing
        if (getenv('CI_ENVIRONMENT') == 'development' || getenv('CI_ENVIRONMENT') == 'production') {
            $dynamicVAList = ['V3SEPABC', 'V3SEPXYZ', 'V3SEPV1', 'V3SEPVSEP', 'V3SEPV00000', 'V3DDVABC', 'V3DDVXYZ', 'V3DDVV1', 'V3DDVVDDV', 'V3DDVV00000'];

            if (in_array($rawData['customer_id'], $dynamicVAList)) {
                if ($rawData['amount'] == 3000) {
                    log_message('error', 'BIDV dynamic VA paybill: Success ' . json_encode($rawData));
                    return $this->respondStandardResponse(200, '000', 'success');
                } else {
                    log_message('error', 'BIDV dynamic VA paybill: Amount does not match ' . json_encode($rawData));
                    return $this->respondStandardResponse(400, '022', 'Amount does not match the invoice');
                }
            }
        }
        // Only tesing

        $vaModel = model(VAModel::class);
        $bankSubAccountDetails = null;

        if ($this->isBidvVaOrderNumber->execute($rawData['customer_id'])) {
            $va = $this->getDynamicVa($rawData['customer_id']);


            if (
                $va
                && ($va->amount == null || $va->amount == $rawData['amount'] || $rawData['amount'] <= $va->amount)
                && ($va->expired_at == null || $va->expired_at > date('Y-m-d H:i:s'))
                && $va->status == 'Unpaid'
                && in_array($va->order_status, ['Pending', 'Partially'])
            ) {
                $bankSubAccountDetails = (object) [
                    'bank_account_id' => $va->bank_account_id,
                    'bank_id' => $va->bank_id,
                    'bank_sms' => $va->bank_sms,
                    'company_id' => $va->company_id,
                    'account_number' => $va->account_number,
                    'sub_account' => $va->va_number,
                    'merchant_id' => $va->merchant_id,
                ];
            }
        }

        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);

        if (! $bankSubAccountDetails) {
            $bankSubAccountDetails = $bankSubAccountModel
                ->select(['tb_autopay_bank_sub_account.id', 'tb_autopay_bank_sub_account.sub_account as sub_account', 'tb_autopay_bank_sub_account.sub_holder_name as sub_holder_name', 'tb_autopay_bank_sub_account.va_active as va_active', 'tb_autopay_bank_account.company_id as company_id', 'tb_autopay_bank_account.account_number as account_number', 'tb_autopay_bank_account.merchant_id as merchant_id', 'tb_autopay_bank_account.id as bank_account_id', 'tb_autopay_bank_account.bank_id as bank_id', 'tb_autopay_bank_account.bank_sms as bank_sms'])
                ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id = tb_autopay_bank_sub_account.bank_account_id')
                ->where([
                    'tb_autopay_bank_account.bank_id' => 9,
                    'tb_autopay_bank_account.merchant_id' => null,
                    'tb_autopay_bank_account.active' => true,
                    'tb_autopay_bank_sub_account.sub_account' => $rawData['customer_id'],
                    'tb_autopay_bank_sub_account.va_active' => 1,
                    'tb_autopay_bank_sub_account.active' => 1,
                ])
                ->get()->getRow();
        }

        if (!$bankSubAccountDetails) {
            return $this->respondStandardResponse(400, '011', 'Customer ID is invalid / not exist');
        }

        $bankAccountDetails = $bankAccountModel->where('id', $bankSubAccountDetails->bank_account_id)->where('active', true)->first();

        if (!$bankAccountDetails) {
            return $this->respondStandardResponse(400, '011', 'Customer ID is invalid / not exist');
        }

        // Skip Bank API notification when switch to SMS Banking
        if ($bankSubAccountDetails->bank_id == 9 && $bankSubAccountDetails->bank_sms) {
            if ($this->config->debug) {
                log_message('error', 'Skip Bank API notification when BIDV account switch to SMS connection');
                log_message('error', json_encode($rawData));
            }

            return $this->respondStandardResponse(200, '000', 'success');
        }

        $bidvNotificationId = $bidvNotificationModel->insert([
            'raw_id' => $bidvNotificationRawId,
            'trans_id' => $rawData['trans_id'],
            'trans_date' => $rawData['trans_date'],
            'customer_id' => $rawData['customer_id'],
            'amount' => $rawData['amount'],
            'remark' => $rawData['remark'],
            'checksum' => $rawData['checksum']
        ]);

        if (($this->config->enableFilterTransaction ?? false) && ! $bankAccountDetails->merchant_id) {
            $filterTransaction = FilterTransactionAction::forBankAccount($bankAccountDetails->id)
                ->amountIn(true)
                ->description($rawData['remark'])
                ->payload($rawData)
                ->filter();

            if ($filterTransaction['isRejected']) {
                return $this->respondStandardResponse(200, '000', 'success');
            }
        }

        $smsParserModel = model(SmsParserModel::class);
        $companyModel = model(CompanyModel::class);
        $transactionsModel = model(TransactionsModel::class);

        $code = null;

        if (is_object($bankSubAccountDetails)) {
            $code = PayCodeDetector::getCode($rawData['remark'], $bankSubAccountDetails->company_id);
        }
        
        if (isset($va)) {
            $code = $va->order_code;
        }

        $parserId = $smsParserModel->insert([
            'sms_id' => $bidvNotificationId,
            'reference_number' => $rawData['trans_id'],
            'gateway' => 'BIDV',
            'transaction_date' => date('Y-m-d H:i:s', strtotime($rawData['trans_date'])),
            'account_number' => $bankSubAccountDetails->account_number,
            'sub_account' => $bankSubAccountDetails->sub_account,
            'amount_in' => $rawData['amount'],
            'code' => $code,
            'transaction_content' => $rawData['remark'],
            'source' => 'BankAPINotify',
            'body' => 'BankAPINotify ' . $rawData['remark'],
            'parser_status' => 'Success',
            'bank_account_id' => $bankSubAccountDetails->bank_account_id,
            'merchant_id' => $bankSubAccountDetails->merchant_id,
        ]);

        if (!$parserId) {
            $bidvNotificationModel->where('id', $bidvNotificationId)->delete();
            log_message('error', 'BIDV Notification Error: SMSParserModel insert failed - Raw #' . $bidvNotificationRawId);

            return $this->respondStandardResponse(503, '031', 'Service unavailabel');
        }

        $transactionDetails = $smsParserModel->find($parserId);

        if (isset($va)) {
            $currentOrder = model(VAOrderModel::class)->find($va->order_id);
            $newPaidAmount = $currentOrder->paid_amount + $rawData['amount'];

            $vaOrderData = [
                'paid_amount' => $newPaidAmount,
                'status' => ($newPaidAmount == $currentOrder->amount) ? 'Paid' : 'Partially',
            ];

            model(VAOrderModel::class)->update($va->order_id, $vaOrderData);

            if ($va->amount == $rawData['amount']) {
                $vaModel->update($va->id, ['status' => 'Paid', 'paid_at' => date('Y-m-d H:i:s')]);
                Services::cache()->delete("bidv_va_{$va->va_number}");
            }
        }

        if (is_object($transactionDetails) && is_numeric($transactionDetails->account_number) && !$bankSubAccountDetails->merchant_id) {
            try {
                PushMobileTransactionNotificationQueueAction::run($transactionDetails, $bankAccountDetails, $bankSubAccountDetails);
            } catch (Exception $e) {
                log_message('error', 'BIDV PushMobileTransactionNotificationQueueAction failed: ' . $e->getMessage() . ' - ' . $e->getTraceAsString() . ' - ' . json_encode($transactionDetails));
            }
            
            $companyCheck = $companyModel->select("tb_autopay_company.id")
                ->join("tb_autopay_company_subscription", "tb_autopay_company.id=tb_autopay_company_subscription.company_id")
                ->join("tb_autopay_bank_account", "tb_autopay_bank_account.company_id=tb_autopay_company.id")
                ->where([
                    'tb_autopay_bank_account.id' => $bankSubAccountDetails->bank_account_id, 
                    'tb_autopay_company_subscription.status' => 'Active',
                    'tb_autopay_company.status' => 'Active', 'tb_autopay_company.active' => 1
                ])->get()->getResult();
            
            if (count($companyCheck) == 1) {
                $webhookQueuable = $this->config->enableBidvWebhookOffload ?? false;
                $forceWebhookManually = ['webhook' => false, 'sapo' => false, 'haravan' => false, 'shopify' => false, 'googlesheet' => false,'output_device' => false, 'viber' => false];
                $rabbitmq = null;

                if ($webhookQueuable) {
                    $rabbitmq = new \App\Libraries\RabbitMQClient;
                    $webhookQueuable = $rabbitmq->connect();
                }

                if ($rabbitmq && $webhookQueuable) {
                    $msg = new AMQPMessage(
                        json_encode(['account_number' => $transactionDetails->account_number, 'parser_id' => $parserId]),
                        array('delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT)
                    );

                    // webhooks
                    try {
                        $rabbitmq->queueDeclare('webhook');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('webhook', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'BIDV API webhook queue failed: ' . $e->getMessage());
                        $forceWebhookManually['webhook'] = true;
                    }
                    
                    // sapo
                    try {
                        $rabbitmq->queueDeclare('sapo');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('sapo', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'BIDV API Sapo queue failed: ' . $e->getMessage());
                        $forceWebhookManually['sapo'] = true;
                    }

                    // haravan
                    try {
                        $rabbitmq->queueDeclare('haravan');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('haravan', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'BIDV API Haravan queue failed: ' . $e->getMessage());
                        $forceWebhookManually['haravan'] = true;
                    } 

                    // shopify
                    try {
                        $rabbitmq->queueDeclare('shopify');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('shopify', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'BIDV API Shopify queue failed: ' . $e->getMessage());
                        $forceWebhookManually['shopify'] = true;
                    } 

                    // googlesheet
                    try {
                        $rabbitmq->queueDeclare('googlesheet');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('googlesheet', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'BIDV API GoogleSheet queue failed: ' . $e->getMessage());
                        $forceWebhookManually['googlesheet'] = true;
                    } 

                    // output device
                    try {
                        $rabbitmq->queueDeclare('output_device');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('output_device', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'BIDV API output device queue failed: ' . $e->getMessage());
                        $forceWebhookManually['output_device'] = true;
                    } 
                    
                    // viber
                    try {
                        $rabbitmq->queueDeclare('viber');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('viber', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'BIDV API Viber queue failed: ' . $e->getMessage());
                        $forceWebhookManually['viber'] = true;
                    }

                    $rabbitmq->close();
                } 
                
                if (!$webhookQueuable || $forceWebhookManually['webhook']) {
                    // webhooks
                    $webhooksModel = model(WebhooksModel::class);
                    $webhooksModel->doWebhooks($transactionDetails->account_number, $parserId);
                }

                if (!$webhookQueuable || $forceWebhookManually['sapo']) {
                    // sapo
                    $sapoModel = model(SapoModel::class);
                    $sapoModel->doWebhooks($transactionDetails->account_number, $parserId);
                }

                if (!$webhookQueuable || $forceWebhookManually['haravan']) {
                    // haravan
                    $haravanModel = model(HaravanModel::class);
                    $haravanModel->doWebhooks($transactionDetails->account_number, $parserId);
                }

                if (!$webhookQueuable || $forceWebhookManually['shopify']) {
                    // shopify
                    ExecuteShopifyWebhooksAction::run($parserId);
                }

                if (!$webhookQueuable || $forceWebhookManually['googlesheet']) {
                    // googlesheet
                    ExecuteGoogleSheetsWebhooksAction::run($parserId);
                }
                
                if (!$webhookQueuable || $forceWebhookManually['webhook']) {
                    // telegram queue
                    $notificationTelegramModel = model(NotificationTelegramModel::class);
                    $notificationTelegramModel->checkAndAddQueue($parserId);
                    
                    // lark messenger queue
                    $notificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);
                    $notificationLarkMessengerModel->checkAndAddQueue($parserId);
                }

                // output device
                if (!$webhookQueuable || $forceWebhookManually['output_device']) {
                    // output device queue
                    ExecuteOutputDevice::run($parserId,0);
                }

                if (!$webhookQueuable || $forceWebhookManually['viber']) {
                    // viber
                    (new \App\Features\Viber\ViberFeature)->sendNotificationByTransactionId($parserId);
                }
                

                //counter
                $counterModel = model(CounterModel::class);
                $counterModel->transaction($companyCheck[0]->id, false, $transactionDetails->amount_in, $transactionDetails->amount_out);

                // cashflow
                $bankAccountCashFlowModel = model(BankAccountCashflowModel::class);
                $bankAccountCashFlowModel->amount_update($companyCheck[0]->id, date('Y-m-d', strtotime($transactionDetails->transaction_date)), $transactionDetails->bank_account_id, $transactionDetails->amount_in, $transactionDetails->amount_out, $transactionDetails->sub_account, $transactionDetails->accumulated);

                $lastTransaction = $transactionDetails;

                if (is_object($lastTransaction))
                    $bankAccountModel->set(['last_transaction' => $lastTransaction->transaction_date, 'accumulated' => $lastTransaction->accumulated])->where(['id' => $bankSubAccountDetails->bank_account_id])->update();
            }
        }
 
        return $this->respondStandardResponse(200, '000', 'success');
    }

    protected function respondStandardResponse($statusCode, $resultCode, $description, $data = [])
    {
        return $this->respond(array_merge([
            'result_code' => $resultCode,
            'result_desc' => $description
        ], $data), $statusCode);
    }

    protected function getDynamicVa($customerId)
    {
        return model(VAModel::class)
            ->select([
                'tb_autopay_va.id',
                'tb_autopay_va.order_id',
                'tb_autopay_va.va_number',
                'tb_autopay_va.va_holder_name',
                'tb_autopay_va.amount',
                'tb_autopay_va.expired_at',
                'tb_autopay_va.status',
                'tb_autopay_va_order.order_code',
                'tb_autopay_va_order.bank_account_id',
                'tb_autopay_va_order.status as order_status',
                'tb_autopay_bank_account.bank_id',
                'tb_autopay_bank_account.bank_sms',
                'tb_autopay_bank_account.company_id',
                'tb_autopay_bank_account.account_number',
                'tb_autopay_bank_account.merchant_id',
                'tb_autopay_bank_account.account_holder_name',
            ])
            ->join('tb_autopay_va_order', 'tb_autopay_va_order.id = tb_autopay_va.order_id')
            ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id = tb_autopay_va_order.bank_account_id')
            ->where('tb_autopay_va.va_number', $customerId)
            ->first();
    }
}
