<?php

namespace App\Controllers\Coreapi;

use App\Actions\GoogleSheets\ExecuteGoogleSheetsWebhooksAction;
use Exception;
use App\Models\SapoModel;
use CodeIgniter\Controller;
use App\Models\CompanyModel;
use App\Models\CounterModel;
use App\Models\HaravanModel;
use App\Models\MerchantModel;
use App\Models\WebhooksModel;
use App\Models\SmsParserModel;
use App\Actions\PayCodeDetector;
use App\Models\BankAccountModel;
use App\Models\TransactionsModel;
use App\Models\ConfigurationModel;
use CodeIgniter\API\ResponseTrait;
use App\Libraries\VietinbankClient;
use App\Models\BankSubAccountModel;
use PhpAmqpLib\Message\AMQPMessage;
use CodeIgniter\HTTP\IncomingRequest;
use App\Models\BankAccountCashflowModel;
use App\Models\NotificationTelegramModel;
use App\Models\VietinbankNotificationModel;
use CodeIgniter\RESTful\ResourceController;
use App\Models\NotificationLarkMessengerModel;
use App\Models\NotificationTelegramQueueModel;
use App\Models\VietinbankNotificationRawModel;
use App\Actions\Shopify\ExecuteShopifyWebhooksAction;
use App\Models\VietinbankEnterpriseNotificationModel;
use App\Actions\Transaction\CreateTransactionQueueAction;
use App\Actions\Merchant\PushMerchantTransactionNotificationAction;
use App\Actions\Mobile\PushMobileTransactionNotificationQueueAction;
use App\Actions\OutputDevice\ExecuteOutputDevice;

class VietinbankOrg extends ResourceController
{
    use ResponseTrait;

    protected $helpers = ['general'];

    const BANK_ID = 6;

    protected $vietinBankPublicKey;

    public function __construct()
    {
        $config = config(\Config\Vietinbank::class);
        $this->vietinBankPublicKey = openssl_pkey_get_public(file_get_contents($config->notifyVietinbankOrgPublicKeyPath));
    }

    public function inqCustomer()
    {
        $json = json_decode(json_encode($this->request->getJSON()), true) ?? [];

        $data = [
            'header' => $json['header'] ?? [],
            'data' => $json['data'] ?? [],
        ];

        $rules = [
            'header' => ['required'],
            'header.msgId' => ['required', 'string'],
            'header.msgType' => ['required', 'string'],
            'header.providerId' => ['required', 'string'],
            'header.timestamp' => ['required', 'regex_match[/^[0-9]{14}$/]'],
            'header.signature' => ['required', 'string'],

            'data' => ['required'],
            'data.records' => ['required'],
            'data.records.*.transId' => ['required', 'string'],
            'data.records.*.transTime' => ['required', 'regex_match[/^[0-9]{14}$/]'],
            'data.records.*.custCode' => ['required', 'string'],
            'data.records.*.serviceType' => ['permit_empty', 'string'],
        ];

        $responseHeader = $data['header'];
        $responseHeader['merchantId'] = null;
        $responseHeader['productId'] = null;
        $responseHeader['recordNum'] = 1;

        if (! $this->validateData($data, $rules)) {
            $validationErrors = $this->validator->getErrors();
            $records = $data['data']['records'] ?? [];

            log_message('error', 'VietinBank Org Inquire Customer Error: Bad request ' . json_encode($validationErrors) . ' - ' . json_encode($data));

            return $this->standardInquireCustomerResponse($responseHeader, [
                'errors' => [
                    'errorCode' => '99',
                    'errorDesc' => 'Several records invalid: ' . implode(', ', $this->validator->getErrors()),
                ],
                'records' => array_map(function($record, $index) use ($validationErrors) {
                    $validationErrorKeys = array_keys($validationErrors);
                    $recordValidationErrors = array_filter($validationErrorKeys, function($key) use ($index) { return strpos($key, 'data.records.'.$index) > -1; });
                    
                    if (count($recordValidationErrors)) {
                        return $this->resolveInquireCustomerResponseRecord($record, '99', implode(', ', $recordValidationErrors));
                    }

                    return $this->resolveInquireCustomerResponseRecord($record, '00', 'Record valid');
                }, $records, array_keys($records)),
            ]);
        }

        try {
            $isValidSignature = openssl_verify(
                $data['data']['records'][0]['transId'] . $data['data']['records'][0]['transTime'] . $data['data']['records'][0]['custCode'],
                base64_decode($data['header']['signature']), 
                $this->vietinBankPublicKey, 
                OPENSSL_ALGO_SHA256
            );
        } catch (Exception $e) {
            $isValidSignature = false;
        }

        if (getenv('CI_ENVIRONMENT') === 'development') {
            $isValidSignature = true;
        }

        if (!$isValidSignature) {
            log_message('error', 'VietinBank Org Inquire Customer Error: Invalid signature: ' . ($data['header']['signature'] ?? ''));

            return $this->standardInquireCustomerResponse($data['header'], [
                'errors' => [
                    'errorCode' => '01',
                    'errorDesc' => 'Verify signature failed',
                ],
                'records' => array_map(function($record) {
                    return $this->resolveInquireCustomerResponseRecord($record, '01', 'Verify signature failed');
                }, $data['data']['records'] ?? []),
            ]);
        }

        foreach ($data['data']['records'] as &$record) {
            // Determine if bank sub account exist
            $bankSubAccountModel = model(BankSubAccountModel::class);
            $bankSubAccountDetails = $bankSubAccountModel
                ->select(['tb_autopay_bank_sub_account.id', 'tb_autopay_bank_sub_account.sub_account', 'tb_autopay_bank_sub_account.sub_holder_name'])
                ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id = tb_autopay_bank_sub_account.bank_account_id')
                ->where([
                    'tb_autopay_bank_account.bank_id' => VietinbankOrg::BANK_ID,
                    'tb_autopay_bank_account.merchant_id' => null,
                    'tb_autopay_bank_sub_account.sub_account' => $record['custCode'],
                    'tb_autopay_bank_sub_account.va_active' => 1,
                    'tb_autopay_bank_sub_account.active' => 1,
                ])
                ->first();

            if (!is_object($bankSubAccountDetails)) {
                $record = $this->resolveInquireCustomerResponseRecord($record, '02', 'Customer code does not exists in SePay');
                continue;
            }

            $record['custName'] = $bankSubAccountDetails->sub_holder_name;

            $record = $this->resolveInquireCustomerResponseRecord($record, '00', 'Successful');
        }

        $errors = count(array_filter($data['data']['records'], function($record) {
            return $record['status']['code'] != '00' && $record['status']['code'] != '49';
        })) ? [
            'errorCode' => '99',
            'errorDesc' => 'Several records inquire failed',
        ] : [
            'errorCode' => '00',
            'errorDesc' => 'Successful',
        ];

        return $this->standardInquireCustomerResponse($data['header'], [
            'errors' => $errors,
            'records' => $data['data']['records'],
        ]);
    }
 
    public function notifyTransaction()
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }

        $config = config(\Config\Vietinbank::class);

        $data = json_decode(json_encode($this->request->getJSON()), true) ?? [];

        $rules = [
            'msgId' => ['required'],
            'providerId' => ['required'],
            'transId' => ['required'],
            'transTime' => ['required', 'regex_match[/^[0-9]{14}$/]'],
            'transType' => ['required'],
            'custCode' => ['required'],
            'sendBankId' => ['permit_empty'],
            'sendBranchId' => ['permit_empty'],
            'sendAcctId' => ['permit_empty'],
            'sendAcctName' => ['permit_empty'],
            'recvAcctId' => ['permit_empty'],
            'recvAcctName' => ['permit_empty'],
            'recvVirtualAcctId' => ['permit_empty'],
            'recvVirtualAcctName' => ['permit_empty'],
            'bankTransId' => ['required'],
            'amount' => ['required'],
            'remark' => ['permit_empty'],
            'currencyCode' => ['required'],
            'signature' => ['required']
        ];

        if (! $this->validateData($data, $rules)) {
            $validationErrors = $this->validator->getErrors();
            log_message('error', 'VietinBank Org Notification Error: Bad request ' . json_encode($validationErrors) . ' - ' . json_encode($data));

            return $this->standardNotifyTransactionResponse($data, '99', 'Several fields invalid: ' . implode(' ', $validationErrors));
        }

        $vietinbankNotificationRawModel = model(VietinbankNotificationRawModel::class);
        $vietinbankNotificationRawId = $vietinbankNotificationRawModel->insert(['payload' => json_encode($data), 'ip' => $this->request->getIPAddress(), 'source' => 'Org']);

        // Verify signature
        try {
            $isValidSignature = openssl_verify(
                $data['transId'] . $data['transTime'] . $data['custCode'] . $data['amount'] . $data['bankTransId'] . $data['remark'],
                base64_decode($data['signature']), 
                $this->vietinBankPublicKey, 
                OPENSSL_ALGO_SHA256
            );
        } catch (Exception $e) {
            $isValidSignature = false;
        }

        if (getenv('CI_ENVIRONMENT') === 'development') {
            $isValidSignature = true;
        }

        if (!$isValidSignature) {
            return $this->standardNotifyTransactionResponse($data, '01', 'Verify signature failed');
        }

        // Determine if transaction duplicated
        $vietinbankEnterpriseNotificationModel = model(VietinbankEnterpriseNotificationModel::class);
        $duplicatedTransaction = $vietinbankEnterpriseNotificationModel->where('transId', $data['transId'])->countAllResults();
        if ($duplicatedTransaction) {
            if ($config->notifyDebug) log_message('error', "Vietinbank Org Notification Error: Transaction Duplicated - Raw #" . $vietinbankNotificationRawId);

            return $this->standardNotifyTransactionResponse($data, '05', 'Transaction ID duplicated');
        }

        $vaNumber = $data['custCode'] ?? $data['recvVirtualAcctId'];
        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);

        // Determine if bank sub account exist
        if ($data['transType'] == '3') {
            if (!$vaNumber) {
                return $this->standardNotifyTransactionResponse($data, '02', 'Customer code does not exists in SePay');
            }

            $bankSubAccountDetails = $bankSubAccountModel
                ->select(['tb_autopay_bank_sub_account.id', 'tb_autopay_bank_sub_account.sub_account', 'tb_autopay_bank_sub_account.sub_holder_name', 'tb_autopay_bank_sub_account.bank_account_id'])
                ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id = tb_autopay_bank_sub_account.bank_account_id')
                ->where([
                    'tb_autopay_bank_account.bank_id' => VietinbankOrg::BANK_ID,
                    'tb_autopay_bank_account.merchant_id' => null,
                    'tb_autopay_bank_sub_account.sub_account' => $vaNumber,
                    'tb_autopay_bank_sub_account.va_active' => 1,
                    'tb_autopay_bank_sub_account.active' => 1,
                ])
                ->first();

            if (!is_object($bankSubAccountDetails)) {
                return $this->standardNotifyTransactionResponse($data, '02', 'Customer code does not exists in SePay');
            }

            $bankAccountDetails = $bankAccountModel->where(['id' => $bankSubAccountDetails->bank_account_id, 'bank_id' => VietinbankOrg::BANK_ID])->first();
        } else {
            $bankSubAccountDetails = null;
            $bankAccountDetails = $bankAccountModel->where(['account_number' => $data['recvAcctId'], 'bank_id' => VietinbankOrg::BANK_ID])->first();
        }

        if (!is_object($bankAccountDetails)) {
            log_message('error', 'Vietinbank Org Notification Error: Account number does not exists - Raw #' . $vietinbankNotificationRawId);

            return $this->standardNotifyTransactionResponse($data, '02', 'Customer code does not exists in SePay');
        }

        // Skip Bank API notification when switch to SMS Banking
        if ($bankAccountDetails->bank_id == VietinbankOrg::BANK_ID && $bankAccountDetails->bank_sms) {
            if ($config->notifyDebug) {
                log_message('error', 'Skip Bank API notification when Vietinbank Org account switch to SMS connection: ' . json_encode($data));
            }

            return $this->standardNotifyTransactionResponse($data, '00', 'Successful');
        }

        $data['remark'] = $data['remark'] ?? '';

        $vietinbankNotificationId = $vietinbankEnterpriseNotificationModel->insert([
            'raw_id' => $vietinbankNotificationRawId,
            'msgId' => $data['msgId'],
            'providerId' => $data['providerId'],
            'transId' => $data['transId'],
            'transTime' => $data['transTime'],
            'transType' => $data['transType'],
            'custCode' => $data['custCode'],
            'sendBankId' => $data['sendBankId'] ?? null,
            'sendBranchId' => $data['sendBranchId'] ?? null,
            'sendAcctId' => $data['sendAcctId'] ?? null,
            'sendAcctName' => $data['sendAcctName'] ?? null,
            'recvAcctId' => $data['recvAcctId'] ?? null,
            'recvAcctName' => $data['recvAcctName'] ?? null,
            'recvVirtualAcctId' => $data['recvVirtualAcctId'] ?? null,
            'recvVirtualAcctName' => $data['recvVirtualAcctName'] ?? null,
            'bankTransId' => $data['bankTransId'],
            'amount' => $data['amount'],
            'remark' => $data['remark'],
            'currencyCode' => $data['currencyCode'],
        ]);

        $smsParserModel = model(SmsParserModel::class);
        $companyModel = model(CompanyModel::class);
        $transactionsModel = model(TransactionsModel::class);

        $code = null;

        if (is_object($bankAccountDetails)) {
            $code = PayCodeDetector::getCode($data['remark'], $bankAccountDetails->company_id);
        }

        $parserData = [
            'sms_id' => $vietinbankNotificationId,
            'reference_number' => $data['bankTransId'],
            'gateway' => 'VietinBank',
            'transaction_date' => date('Y-m-d H:i:s', strtotime($data['transTime'])),
            'account_number' => $bankAccountDetails->account_number,
            'sub_account' => $bankSubAccountDetails->sub_account ?? null,
            'amount_in' => $data['amount'],
            'code' => $code,
            'transaction_content' => $data['remark'],
            'source' => 'BankAPINotify',
            'body' => 'BankAPINotify ' . $data['remark'],
            'parser_status' => 'Success',
            'bank_account_id' => $bankAccountDetails->id,
            'transaction_id' => uuid()
        ];

        $parserId = $smsParserModel->insert($parserData);

        if (!$parserId) {
            $vietinbankEnterpriseNotificationModel->where('id', $vietinbankNotificationId)->delete();
            log_message('error', 'Vietinbank Org Notification Error: SMSParserModel insert failed - Raw #' . $vietinbankNotificationRawId);

            return $this->standardNotifyTransactionResponse($data, '99', 'Record insert failed, please retry');
        }

        $transactionDetails = $smsParserModel->find($parserId);

        if (is_object($transactionDetails) && is_numeric($transactionDetails->account_number) && !$bankAccountDetails->merchant_id) {
            try {
                PushMobileTransactionNotificationQueueAction::run($transactionDetails, $bankAccountDetails, $bankSubAccountDetails);
            } catch (Exception $e) {
                log_message('error', 'Vietinbank Org PushMobileTransactionNotificationQueueAction failed: ' . $e->getMessage() . ' - ' . $e->getTraceAsString() . ' - ' . json_encode($transactionDetails));
            }
            
            $companyCheck = $companyModel->select("tb_autopay_company.id")
                ->join("tb_autopay_company_subscription", "tb_autopay_company.id=tb_autopay_company_subscription.company_id")
                ->join("tb_autopay_bank_account", "tb_autopay_bank_account.company_id=tb_autopay_company.id")
                ->where([
                    'tb_autopay_bank_account.id' => $bankAccountDetails->id, 
                    'tb_autopay_company_subscription.status' => 'Active',
                    'tb_autopay_company.status' => 'Active', 'tb_autopay_company.active' => 1
                ])->get()->getResult();
            
            if (count($companyCheck) == 1) {
                $webhookQueuable = $config->enableVietinbankWebhookOffload ?? false;
                $forceWebhookManually = ['webhook' => false, 'sapo' => false, 'haravan' => false, 'shopify' => false, 'googlesheet' => false, 'output_device' => false, 'viber' => false];
                $rabbitmq = null;

                if ($webhookQueuable) {
                    $rabbitmq = new \App\Libraries\RabbitMQClient;
                    $webhookQueuable = $rabbitmq->connect();
                }

                if ($rabbitmq && $webhookQueuable) {
                    $msg = new AMQPMessage(
                        json_encode(['account_number' => $transactionDetails->account_number, 'parser_id' => $parserId]),
                        array('delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT)
                    );

                    // webhooks
                    try {
                        $rabbitmq->queueDeclare('webhook');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('webhook', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'Vietinbank ORG API webhook queue failed: ' . $e->getMessage());
                        $forceWebhookManually['webhook'] = true;
                    }
                    
                    // sapo
                    try {
                        $rabbitmq->queueDeclare('sapo');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('sapo', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'Vietinbank ORG API Sapo queue failed: ' . $e->getMessage());
                        $forceWebhookManually['sapo'] = true;
                    }

                    // haravan
                    try {
                        $rabbitmq->queueDeclare('haravan');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('haravan', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'Vietinbank API Haravan queue failed: ' . $e->getMessage());
                        $forceWebhookManually['haravan'] = true;
                    } 

                    // shopify
                    try {
                        $rabbitmq->queueDeclare('shopify');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('shopify', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'Vietinbank ORG API Shopify queue failed: ' . $e->getMessage());
                        $forceWebhookManually['shopify'] = true;
                    }
                    
                    //googlesheet
                    try {
                        $rabbitmq->queueDeclare('googlesheet');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('googlesheet', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'Vietinbank ORG API GoogleSheet queue failed: ' . $e->getMessage());
                        $forceWebhookManually['googlesheet'] = true;
                    }

                     // output device
                    try {
                        $rabbitmq->queueDeclare('output_device');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('output_device', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'Vietinbank ORG API output device queue failed: ' . $e->getMessage());
                        $forceWebhookManually['output_device'] = true;
                    }
                    
                    // viber
                    try {
                        $rabbitmq->queueDeclare('viber');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('viber', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'VietinBank ORG API Viber queue failed: ' . $e->getMessage());
                        $forceWebhookManually['viber'] = true;
                    }

                    $rabbitmq->close();
                } 
                
                if (!$webhookQueuable || $forceWebhookManually['webhook']) {
                    // webhooks
                    $webhooksModel = model(WebhooksModel::class);
                    $webhooksModel->doWebhooks($transactionDetails->account_number, $parserId);
                }

                if (!$webhookQueuable || $forceWebhookManually['sapo']) {
                    // sapo
                    $sapoModel = model(SapoModel::class);
                    $sapoModel->doWebhooks($transactionDetails->account_number, $parserId);
                }

                if (!$webhookQueuable || $forceWebhookManually['haravan']) {
                    // haravan
                    $haravanModel = model(HaravanModel::class);
                    $haravanModel->doWebhooks($transactionDetails->account_number, $parserId);
                }

                if (!$webhookQueuable || $forceWebhookManually['shopify']) {
                    // shopify
                    ExecuteShopifyWebhooksAction::run($parserId);
                }
                
                if (!$webhookQueuable || $forceWebhookManually['webhook']) {
                    // telegram queue
                    $notificationTelegramModel = model(NotificationTelegramModel::class);
                    $notificationTelegramModel->checkAndAddQueue($parserId);
                    
                    // lark messenger queue
                    $notificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);
                    $notificationLarkMessengerModel->checkAndAddQueue($parserId);
                }
                
                if (!$webhookQueuable || $forceWebhookManually['googlesheet']) {
                    // googlesheet
                    ExecuteGoogleSheetsWebhooksAction::run($parserId);
                }
                
                // output device
                if (!$webhookQueuable || $forceWebhookManually['output_device']) {
                    // output device queue
                    ExecuteOutputDevice::run($parserId, 0);
                }
                
                if (!$webhookQueuable || $forceWebhookManually['viber']) {
                    // viber
                    (new \App\Features\Viber\ViberFeature)->sendNotificationByTransactionId($parserId);
                }

                //counter
                $counterModel = model(CounterModel::class);
                $counterModel->transaction($companyCheck[0]->id, false, $transactionDetails->amount_in, $transactionDetails->amount_out);

                // cashflow
                $bankAccountCashFlowModel = model(BankAccountCashflowModel::class);
                $bankAccountCashFlowModel->amount_update($companyCheck[0]->id, date('Y-m-d', strtotime($transactionDetails->transaction_date)), $transactionDetails->bank_account_id, $transactionDetails->amount_in, $transactionDetails->amount_out, $transactionDetails->sub_account, $transactionDetails->accumulated);

                $lastTransaction = $transactionDetails;

                if (is_object($lastTransaction))
                    $bankAccountModel->set(['last_transaction' => $lastTransaction->transaction_date, 'accumulated' => $lastTransaction->accumulated])->where(['id' => $bankAccountDetails->id])->update();
            }
        }

        return $this->standardNotifyTransactionResponse($data, '00', 'Successful');
    }

    protected function standardInquireCustomerResponse($header, $data)
    {
        $client = new VietinbankClient;

        $signPayload = [
            'errors.errorCode' => $data['errors']['errorCode'] ?? null,
            'transId' => $data['records'][0]['transId'] ?? null,
            'transTime' => $data['records'][0]['transTime'] ?? null,
            'custCode' => $data['records'][0]['custCode'] ?? null,
            'custName' => $data['records'][0]['custName'] ?? null,
            'status' => $data['records'][0]['status']['code'] ?? null
        ];

        return $this->respond([
            'header' => array_merge($header, ['signature' => $client->sign($signPayload, array_keys($signPayload))]),
            'data' => $data,
        ]);
    }

    protected function standardNotifyTransactionResponse($data, $code, $message)
    {
        $client = new VietinbankClient;

        $signPayload = [
            'transId' => $data['transId'] ?? null,
            'errorCode' => $code,
            'message' => $message
        ];

        return $this->respond([
            'transId' => $data['transId'] ?? null,
            'providerId' => $data['providerId'] ?? null,
            'errorCode' => $code,
            'errorDesc' => $message,
            'signature' => $client->sign($signPayload, array_keys($signPayload))
        ]);
    }

    protected function resolveInquireCustomerResponseRecord($record, $code = null, $message = null)
    {
        return [
            'transId' => $record['transId'],
            'channelId' => $record['channelId'] ?? null,
            'transTime' => $record['transTime'],
            'transType' => $record['transType'] ?? null,
            'serviceType' => $record['serviceType'] ?? null,
            'paymentType' => $record['paymentType'] ?? null,
            'paymentMethod' => $record['paymentMethod'] ?? null,
            'custCode' => $record['custCode'] ?? null,
            'custName' => $record['custName'] ?? null,
            'custAcct' => null,
            'amount' => null,
            'currencyCode' => 'VND',
            'birthDate' => null,
            'gender' => null,
            'idCard' => null,
            'idCardType' => null,
            'accNo' => null,
            'phoneNo' => null,
            'email' => null,
            'status' => [
                'code' => $code,
                'message' => $message,
            ],
            'addInfo' => isset($record['addInfo']) ? (object) $record['addInfo'] : (object) [],
            'preseve1' => null,
            'preseve2' => null,
            'preseve3' => null,
        ];
    }
}