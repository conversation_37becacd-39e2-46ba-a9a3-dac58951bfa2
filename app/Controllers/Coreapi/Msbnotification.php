<?php

namespace App\Controllers\Coreapi;

use App\Actions\BankAccount\FilterTransactionAction;
use Exception;
use Config\Msb;
use App\Models\SapoModel;
use CodeIgniter\Controller;
use App\Models\CompanyModel;
use App\Models\CounterModel;
use App\Models\HaravanModel;
use App\Models\WebhooksModel;
use App\Models\SmsParserModel;
use App\Models\BankAccountModel;
use App\Models\TransactionsModel;
use App\Models\ConfigurationModel;
use CodeIgniter\API\ResponseTrait;
use App\Models\BankSubAccountModel;
use PhpAmqpLib\Message\AMQPMessage;
use App\Models\MsbNotificationModel;

use App\Models\BankAccountCashflowModel;
use App\Models\NotificationTelegramModel;
use CodeIgniter\RESTful\ResourceController;
use App\Models\NotificationLarkMessengerModel;
use App\Models\NotificationTelegramQueueModel;
use App\Actions\Shopify\ExecuteShopifyWebhooksAction;
use App\Actions\GoogleSheets\ExecuteGoogleSheetsWebhooksAction;
use App\Actions\Mobile\PushMobileTransactionNotificationQueueAction;
use App\Actions\PayCodeDetector;

use App\Actions\OutputDevice\ExecuteOutputDevice;

class Msbnotification extends ResourceController
{
    use ResponseTrait;
 
    public function create() {

        $request = \Config\Services::request();

        $config = config(Msb::class);
        $config->enableMsbWebhookOffload = $config->enableMsbWebhookOffload ?? false;
        
        if ($this->request->getMethod(true) != 'POST')
            return $this->failNotFound();
        

        $validation =  \Config\Services::validation();
 
        if(! $this->validate(
            [
                'tranSeq'=>'required|is_unique[tb_autopay_msb_notification.tranSeq]',
                'vaCode'=>'required',
                'vaNumber'=>'required',
                'vaName'=>'required',
                'toAccountNumber'=>'required',
                'tranAmount'=>'required',
                'tranDate'=>'required',
                'signature'=>'required',
            ]
        )) {
            $message = implode(". ", $validation->getErrors());
            return $this->failValidationError($message);
        }
 
        
        $msbNotification = model(MsbNotificationModel::class);
 
        $data = [
            'tranSeq' => $this->request->getVar('tranSeq'),
            'vaCode' => $this->request->getVar('vaCode'),
            'vaNumber' => $this->request->getVar('vaNumber'),
            'vaName' => $this->request->getVar('vaName'),
            'toAccountName' => $this->request->getVar('toAccountName'),
            'toAccountNumber' => $this->request->getVar('toAccountNumber'),
            'tranAmount' => $this->request->getVar('tranAmount'),
            'tranDate' => $this->request->getVar('tranDate'),
            'signature' => $this->request->getVar('signature'),
            'tranRemark' => $this->request->getVar('tranRemark'),
            'fromAccountName' => $this->request->getVar('fromAccountName'),
            'fromAccountNumber' => $this->request->getVar('fromAccountNumber'),
        ];
        
        $msb_id = $msbNotification->insert($data);
         
        if(!is_numeric($msb_id)) {
            log_message('error', "MSB Notification Error: Cannot insert data: " . $msb_id);
            $this->fail("Cannot insert data", 400);
        }
            


        $smsParserModel = model(SmsParserModel::class);
        $companyModel = model(CompanyModel::class);
        $transactionsModel = model(TransactionsModel::class);
        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);

        $vaNumber = $data['vaNumber'];

        $sub_account_details = $bankSubAccountModel->select("tb_autopay_bank_sub_account.id, tb_autopay_bank_sub_account.sub_account, tb_autopay_bank_sub_account.bank_account_id, tb_autopay_bank_account.company_id")->join("tb_autopay_bank_account","tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id")->where(['tb_autopay_bank_account.bank_id' => 10, 'tb_autopay_bank_sub_account.sub_account' => $vaNumber])->get()->getRow();

        if(!is_object($sub_account_details)) {
            log_message('error', "MSB Notification Error: Bank sub account not found. Raw ID: " . $msb_id);
            return $this->fail("Bank sub account not found", 400);
        }

        $bank_account_details = $bankAccountModel->where('id', $sub_account_details->bank_account_id)->where('active', true)->get()->getRow();

        if(!is_object($bank_account_details)) {
            log_message('error', "MSB Notification Error: Bank account not found. Raw ID: " . $msb_id);
            return $this->fail("Bank account not found", 400);
        }

        if(!$bank_account_details->access_code) {
            log_message('error', "MSB Notification Error: Access code not found. Raw ID: " . $msb_id);
            return $this->fail("Access code not found", 400);
        }

        $access_code = $bank_account_details->access_code;
       
        $str = $access_code . $data['tranSeq']  . $data['tranDate'] . $data['vaNumber'] . $data['tranAmount'] . $data['fromAccountNumber']. $data['toAccountNumber'];
        
        $input_signature = hash('sha256', $str);
        if($input_signature != $data['signature']) {
            log_message('error', "MSB Notification Error: Invalid singature. Raw ID: " . $msb_id);
            return $this->fail("Invalid singature", 400);
        }
         
        $account_number = $bank_account_details->account_number;

        // transaction date yyMMddhhssmm ex ************
        $transaction_date = date_create_from_format("ymdHis", $data['tranDate']);
        
        if(!$transaction_date) {
            log_message('error', "MSB Notification Error: Transaction date not found. Raw ID: " . $msb_id);
            return $this->fail("Transaction date not found", 400);
        }

        // Convert to mysql format
        $transaction_date = date_format($transaction_date,'Y-m-d H:i:s');

        $canSyncAccumulated = true;

        if (($config->enableFilterTransaction ?? false) && ! $bank_account_details->merchant_id) {
            $filterTransaction = FilterTransactionAction::forBankAccount($bank_account_details->id)
                ->amountIn(true)
                ->description($data['tranRemark'])
                ->payload($data)
                ->filter();

            if ($filterTransaction['isRejected']) {
                return $this->respondCreated([
                    'status' => 201,
                    'error' => null,
                    'messages' => [
                        'success' => 'Data Saved',
                    ],
                ]);
            }

            $canSyncAccumulated = $filterTransaction['canSyncAccumulated'];
        }

        // payment code
        $code = NULL;


        if(is_object($bank_account_details)) {
            $code = PayCodeDetector::getCode($data['tranRemark'], $bank_account_details->company_id);
        }

        $transactionsModel = model(TransactionsModel::class);

        $cache = service('cache');
        $lastAccumulatedKey = 'sepay_last_accumulated_bank_account_' . $bank_account_details->id;
        $lastAccumulated = $cache->get($lastAccumulatedKey);
        $accumulated = 0;

        if ($lastAccumulated === 0) {
            $accumulated = 0;
        } else if (!is_null($lastAccumulated)) {
            $accumulated = $lastAccumulated + $data['tranAmount'];
        } else {
            $last_trans = $transactionsModel->select(['accumulated'])->where([
                'account_number' => $account_number, 
                'accumulated!=' => '',
                'bank_account_id' => $bank_account_details->id,
            ])->orderBy('transaction_date', 'desc')->first();
            
            if (is_object($last_trans) && $last_trans->accumulated >0) {
                $accumulated = $last_trans->accumulated + $data['tranAmount'];
            }
        }

	$transaction_content = $data['tranRemark'];

        $regex_1 = '/^-[0-9]+-/';
        $regex_2 = '/_[0-9a-zA-Z]+$/';
        $transaction_content = preg_replace($regex_1, '', $transaction_content);
        $transaction_content = preg_replace($regex_2, '', $transaction_content);
	
        $transaction_data = [
            "sms_id" => $msb_id,
            "gateway" => "MSB",
            "transaction_date" => $transaction_date,
            "account_number" => $account_number,
            "sub_account" => $data['vaNumber'],
            "amount_in" => $data['tranAmount'],
            "accumulated" => $canSyncAccumulated ? $accumulated : 0,
            "code" => $code,
            "transaction_content" => $transaction_content,
            "reference_number" => $data['tranSeq'],
            "source" => "BankAPINotify",
            "body" => "BankAPINotify " . $data['tranRemark'],
            "parser_status" => "Success",
            "from_account_name" => $data['fromAccountName'],
            "from_account_number" => $data['fromAccountNumber'],
            "bank_account_id" => $bank_account_details->id
        ];

        $parser_result = $smsParserModel->insert($transaction_data);
        $cache->save($lastAccumulatedKey, $accumulated, 0);
            
        
        if(!is_numeric($parser_result)) {
            log_message('error', "MSB Notification Error: Cannot insert data. Raw ID: " . $msb_id);
            return $this->fail("Cannot insert data", 400);
        }

        $transaction_details = $smsParserModel->find($parser_result);
        if(is_object($transaction_details) && is_numeric($transaction_details->account_number)) {
            try {
                PushMobileTransactionNotificationQueueAction::run($transaction_details, $bank_account_details, $sub_account_details);
            } catch (Exception $e) {
                log_message('error', 'MSB PushMobileTransactionNotificationQueueAction failed: ' . $e->getMessage() . ' - ' . $e->getTraceAsString() . ' - ' . json_encode($transaction_details));
            }

            // check company and subscription status
            $company_check = $companyModel->select("tb_autopay_company.id")
                ->join("tb_autopay_company_subscription","tb_autopay_company.id=tb_autopay_company_subscription.company_id")
                ->join("tb_autopay_bank_account","tb_autopay_bank_account.company_id=tb_autopay_company.id")
                ->where(['tb_autopay_bank_account.id' => $bank_account_details->id, 'tb_autopay_company_subscription.status'=>'Active','tb_autopay_company.status' => 'Active', 'tb_autopay_company.active' => 1])->get()->getResult();
            
            if(count($company_check) == 1) {
                $webhookQueuable = $config->enableMsbWebhookOffload;
                $forceWebhookManually = ['webhook' => false, 'sapo' => false, 'haravan' => false, 'shopify' => false, 'googlesheet' => false,'output_device' => false, 'viber' => false];
                $rabbitmq = null;

                if ($webhookQueuable) {
                    $rabbitmq = new \App\Libraries\RabbitMQClient;
                    $webhookQueuable = $rabbitmq->connect();
                }

                if ($rabbitmq && $webhookQueuable) {
                    $msg = new AMQPMessage(
                        json_encode(['account_number' => $transaction_details->account_number, 'parser_id' => $parser_result]),
                        array('delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT)
                    );

                    // webhooks
                    try {
                        $rabbitmq->queueDeclare('webhook');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('webhook', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'MSB webhook queue failed: ' . $e->getMessage());
                        $forceWebhookManually['webhook'] = true;
                    }
                    
                    // sapo
                    try {
                        $rabbitmq->queueDeclare('sapo');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('sapo', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'MSB Sapo queue failed: ' . $e->getMessage());
                        $forceWebhookManually['sapo'] = true;
                    }

                    // haravan
                    try {
                        $rabbitmq->queueDeclare('haravan');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('haravan', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'MSB Haravan queue failed: ' . $e->getMessage());
                        $forceWebhookManually['haravan'] = true;
                    } 

                    // shopify
                    try {
                        $rabbitmq->queueDeclare('shopify');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('shopify', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'MSB API Shopify queue failed: ' . $e->getMessage());
                        $forceWebhookManually['shopify'] = true;
                    } 

                    //googlesheet
                    try {
                        $rabbitmq->queueDeclare('googlesheet');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('googlesheet', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'MSB GoogleSheet queue failed: ' . $e->getMessage());
                        $forceWebhookManually['googlesheet'] = true;
                    }

                    // output device
                    try {
                        $rabbitmq->queueDeclare('output_device');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('output_device', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'Msbnotification API output device queue failed: ' . $e->getMessage());
                        $forceWebhookManually['output_device'] = true;
                    } 
                    
                    // viber
                    try {
                        $rabbitmq->queueDeclare('viber');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('viber', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'MSB API Viber queue failed: ' . $e->getMessage());
                        $forceWebhookManually['viber'] = true;
                    }

                    $rabbitmq->close();
                } 
                
                if (!$webhookQueuable || $forceWebhookManually['webhook']) {
                    // webhooks
                    $webhooksModel = model(WebhooksModel::class);
                    $webhooksModel->doWebhooks($transaction_details->account_number, $parser_result);
                }

                if (!$webhookQueuable || $forceWebhookManually['sapo']) {
                    // sapo
                    $sapoModel = model(SapoModel::class);
                    $sapoModel->doWebhooks($transaction_details->account_number, $parser_result);
                }

                if (!$webhookQueuable || $forceWebhookManually['haravan']) {
                    // haravan
                    $haravanModel = model(HaravanModel::class);
                    $haravanModel->doWebhooks($transaction_details->account_number, $parser_result);
                }

                if (!$webhookQueuable || $forceWebhookManually['shopify']) {
                    // shopify
                    ExecuteShopifyWebhooksAction::run($parser_result);
                }

                if (!$webhookQueuable || $forceWebhookManually['googlesheet']) {
                    // googlesheet
                    ExecuteGoogleSheetsWebhooksAction::run($parser_result);
                }

                // output device
                if (!$webhookQueuable || $forceWebhookManually['output_device']) {
                    // output device queue
                    ExecuteOutputDevice::run($parser_result,0);
                }
                
                if (!$webhookQueuable || $forceWebhookManually['viber']) {
                    // viber
                    (new \App\Features\Viber\ViberFeature)->sendNotificationByTransactionId($parser_result);
                }

                if (!$webhookQueuable || $forceWebhookManually['webhook']) {
                    // telegram queue
                    $notificationTelegramModel = model(NotificationTelegramModel::class);
                    $notificationTelegramModel->checkAndAddQueue($parser_result);
                    
                    // lark messenger queue
                    $notificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);
                    $notificationLarkMessengerModel->checkAndAddQueue($parser_result);
                }

                //counter
                $counterModel = model(CounterModel::class);
                $counterModel->transaction($company_check[0]->id,FALSE,$transaction_details->amount_in, $transaction_details->amount_out);

                // cashflow
                $bankAccountCashFlowModel = model(BankAccountCashflowModel::class);
                $bankAccountCashFlowModel->amount_update($company_check[0]->id, date('Y-m-d', strtotime($transaction_details->transaction_date)), $transaction_details->bank_account_id, $transaction_details->amount_in, $transaction_details->amount_out, $transaction_details->sub_account,$transaction_details->accumulated);

                $last_trans = $transaction_details;

                if(is_object($last_trans))
                    $bankAccountModel->set(['last_transaction' => $last_trans->transaction_date, 'accumulated' => $last_trans->accumulated])->where(['id' => $bank_account_details->id])->update();
            }
        }

        $response = [
            'status'   => 201,
            'error'    => null,
            'messages' => [
                'success' => 'Data Saved'
            ]
        ];
         
        return $this->respondCreated($response);
    }
}
