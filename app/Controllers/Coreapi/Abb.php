<?php

namespace App\Controllers\Coreapi;

use App\Actions\BankAccount\FilterTransactionAction;
use App\Actions\GoogleSheets\ExecuteGoogleSheetsWebhooksAction;
use App\Actions\Merchant\PushMerchantTransactionNotificationAction;
use App\Actions\Mobile\PushMobileTransactionNotificationQueueAction;
use App\Actions\OutputDevice\ExecuteOutputDevice;
use App\Actions\PayCodeDetector;
use App\Actions\Shopify\ExecuteShopifyWebhooksAction;
use App\Actions\Transaction\CreateTransactionQueueAction;
use App\Libraries\RabbitMQClient;
use App\Models\AbbankNotificationModel;
use App\Models\BankAccountCashflowModel;
use App\Models\BankAccountModel;
use App\Models\BankNotificationRawModel;
use App\Models\BankSubAccountModel;
use App\Models\CompanyModel;
use App\Models\CounterModel;
use App\Models\HaravanModel;
use App\Models\NotificationLarkMessengerModel;
use App\Models\NotificationTelegramModel;
use App\Models\SapoModel;
use App\Models\SmsParserModel;
use App\Models\WebhooksModel;
use CodeIgniter\HTTP\Response;
use CodeIgniter\RESTful\ResourceController;
use Config\Abbank as Config;
use Config\Merchant;
use Exception;
use PhpAmqpLib\Message\AMQPMessage;

class Abb extends ResourceController
{
    protected $helpers = ['general'];

    protected Config $config;

    protected Merchant $merchantConfig;

    public function __construct()
    {
        $this->config = config(Config::class);
        $this->merchantConfig = config(Merchant::class);
    }

    public function notify(): Response
    {
        if ($this->request->getMethod(true) !== 'POST') {
            show_404();
        }

        if (! $this->authorize()) {
            return $this->respond(['code' => 401, 'message' => 'Unauthorized'], 401);
        }

        $data = $this->request->getJSON(true);

        if (empty($data)) {
            return $this->respond(['code' => 400, 'message' => 'Bad Request'], 400);
        }

        if ($this->config->debug) {
            log_message('error', 'ABBANK payload: ' . json_encode($data));
        }

        $rules = [
            'providerId' => "required|string|in_list[{$this->config->providerId}]",
            'requestId' => 'required|string',
            'timestamp' => 'required|string|regex_match[/^\d{14}$/]|exact_length[14]',
            'data' => 'required',
            'data.*.referenceNumber' => 'required|string',
            'data.*.transactionId' => 'required|string',
            'data.*.transactionTime' => 'required|integer',
            'data.*.transactionType' => 'required|string|in_list[C,D]',
            'data.*.transactionDesc' => 'required|string',
            'data.*.accountNumber' => 'required|string',
            'data.*.amount' => 'required|numeric',
            'data.*.balance' => 'permit_empty|numeric',
            'data.*.virtualAccount' => 'permit_empty|string',
        ];

        if (! $this->validateData($data, $rules)) {
            if ($this->config->debug) {
                log_message('error', 'ABBANK validation errors: ' . json_encode($this->validator->getErrors()));
            }

            return $this->respond(['code' => 400, 'message' => 'Bad Request', 'errors' => $this->validator->getErrors()], 400);
        }

        $notifiationRawId = model(BankNotificationRawModel::class)->insert([
            'bank_id' => $this->config->bankId,
            'payload' => json_encode($data),
            'ip' => $this->request->getIPAddress(),
        ]);

        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $abbankNotificationModel = model(AbbankNotificationModel::class);
        $smsParserModel = model(SmsParserModel::class);
        $companyModel = model(CompanyModel::class);

        $transactions = [];

        foreach ($data['data'] as $transaction) {
            $abbankNotifications = $abbankNotificationModel
                ->where('transaction_id', $transaction['transactionId'])
                ->countAllResults();

            if ($abbankNotifications > 0) {
                if ($this->config->debug) {
                    log_message('error', 'ABBANK transaction already existed: ' . $transaction['transactionId']);
                }

                $transactions[] = [
                    'transactionId' => $transaction['transactionId'],
                    'code' => '03',
                    'description' => 'Transaction already existed',
                ];

                continue;
            }

            $bankAccount = $bankAccountModel
                ->where('account_number', $transaction['accountNumber'])
                ->where('bank_id', $this->config->bankId)
                ->where('active', true)
                ->first();

            if (! $bankAccount) {
                if ($this->config->debug) {
                    log_message('error', 'ABBANK account number not found: ' . $transaction['accountNumber']);
                }

                $transactions[] = [
                    'transactionId' => $transaction['transactionId'],
                    'code' => '04',
                    'description' => 'Account number not found',
                ];

                continue;
            }

            if ($bankAccount->bank_id == $this->config->bankId && $bankAccount->bank_sms) {
                $transactions[] = [
                    'transactionId' => $transaction['transactionId'],
                    'code' => '00',
                    'description' => 'Success',
                ];

                continue;
            }

            $notificationId = model(AbbankNotificationModel::class)->insert([
                'raw_id' => $notifiationRawId,
                'request_id' => $data['requestId'],
                'timestamp' => $data['timestamp'],
                'reference_number' => $transaction['referenceNumber'],
                'transaction_id' => $transaction['transactionId'],
                'transaction_time' => $transaction['transactionTime'],
                'transaction_type' => $transaction['transactionType'],
                'transaction_desc' => $transaction['transactionDesc'],
                'account_number' => $transaction['accountNumber'],
                'amount' => $transaction['amount'],
                'virtual_account' => $transaction['virtualAccount'] ?? null,
            ]);
            
            $transactionTime = $transaction['transactionTime'] / 1000;

            $currentTime = time();
            $timeDiff = abs($transactionTime - $currentTime);

            if ($timeDiff > 21600) {
                $transactionTime = $transactionTime - (7 * 3600);
            }

            if (! $bankAccount->merchant_id) {
                $filterTransaction = FilterTransactionAction::forBankAccount($bankAccount->id)
                    ->amountIn($transaction['transactionType'] === 'C')
                    ->description($transaction['transactionDesc'])
                    ->payload($transaction)
                    ->filter();

                if ($filterTransaction['isRejected']) {
                    if ($this->config->debug) {
                        log_message('error', 'ABBANK transaction rejected: ' . $filterTransaction['message']);
                    }

                    $transactions[] = [
                        'transactionId' => $transaction['transactionId'],
                        'code' => '00',
                        'description' => $filterTransaction['message'],
                    ];

                    continue;
                }
            }

            $bankSubAccount = null;
            $code = null;

            if (preg_match('/(?:(L\s*O\s*C\s*V\s*A\s*N\s*G)|(S\s*E\s*V\s*Q\s*R))\s+(T\s*K\s*P(?:\s?[A-Z0-9]){2,5})/', strtoupper($transaction['transactionDesc']), $matches)) {
                $vaNumber = str_replace(' ', '', $matches[3]);
                $vaNumber = str_replace('TKP', '', $vaNumber);

                if ($vaNumber) {
                    $bankSubAccount = $bankSubAccountModel
                        ->where('sub_account', $vaNumber)
                        ->where('bank_account_id', $bankAccount->id)
                        ->where('va_active', true)
                        ->where('active', true)
                        ->where('deleted_at', null)
                        ->first();
                }
            }

            if ($transaction['transactionDesc']) {
                $code = PayCodeDetector::getCode($transaction['transactionDesc'], $bankAccount->company_id);
            }

            $parserData = [
                'sms_id' => $notificationId,
                'reference_number' => $transaction['referenceNumber'],
                'gateway' => 'ABBANK',
                'transaction_date' => date('Y-m-d H:i:s', $transactionTime),
                'account_number' => $bankAccount->account_number,
                'sub_account' => $bankSubAccount->sub_account ?? null,
                'amount_in' => $transaction['transactionType'] === 'C' ? $transaction['amount'] : 0,
                'amount_out' => $transaction['transactionType'] === 'D' ? $transaction['amount'] : 0,
                'accumulated' => 0,
                'code' => $code,
                'transaction_content' => $transaction['transactionDesc'],
                'source' => 'BankAPINotify',
                'body' => 'BankAPINotify ' . $transaction['transactionDesc'],
                'parser_status' => 'Success',
                'bank_account_id' => $bankAccount->id,
                'merchant_id' => $bankAccount->merchant_id,
                'transaction_id' => uuid(),
            ];

            $queueTransactionId = null;

            if ($bankAccount->merchant_id && $this->merchantConfig->enabledTransactionInsertionOffload) {
                $queueTransactionId = CreateTransactionQueueAction::run((object) $parserData, $bankAccount);
            }

            if (! $queueTransactionId) {
                $parserId = $smsParserModel->insert($parserData);
                $transactionDetails = $smsParserModel->find($parserId);
            }

            if (! $parserId && ! $queueTransactionId) {
                $abbankNotificationModel->delete($notificationId);

                if ($this->config->debug) {
                    log_message('error', 'ABBANK transaction not inserted: ' . $transaction['transactionId']);
                }

                $transactions[] = [
                    'transactionId' => $transaction['transactionId'],
                    'code' => '99',
                    'description' => 'Service unavailable',
                ];

                continue;
            }

            if (! $transactionDetails || ! $transactionDetails->account_number || $bankAccount->merchant_id) {
                if ($this->config->debug) {
                    log_message('error', 'ABBANK transaction not inserted: ' . $transaction['transactionId']);
                }

                $transactions[] = [
                    'transactionId' => $transaction['transactionId'],
                    'code' => '99',
                    'description' => 'Service unavailable',
                ];

                continue;
            }

            try {
                PushMerchantTransactionNotificationAction::run(
                    $bankAccount,
                    $transactionDetails,
                    $this->merchantConfig->enabledPushTransactionNotificationOffload
                );
            } catch (Exception $e) {
                log_message('error', '[PushableMerchantTransactionNotificationAction] ' . $e->getMessage() . ' - ' . $e->getTraceAsString());
            }

            try {
                PushMobileTransactionNotificationQueueAction::run($transactionDetails, $bankAccount, $bankSubAccount);
            } catch (Exception $e) {
                log_message('error', 'ABBANK PushMobileTransactionNotificationQueueAction failed: ' . $e->getMessage() . ' - ' . $e->getTraceAsString() . ' - ' . json_encode($transactionDetails));
            }

            $company = $companyModel
                ->select('tb_autopay_company.id')
                ->join('tb_autopay_company_subscription', 'tb_autopay_company.id=tb_autopay_company_subscription.company_id')
                ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.company_id=tb_autopay_company.id')
                ->where('tb_autopay_bank_account.id', $bankAccount->id)
                ->where('tb_autopay_company_subscription.status', 'Active')
                ->where('tb_autopay_company.status', 'Active')
                ->where('tb_autopay_company.active', true)
                ->first();

            if (! $company) {
                $transactions[] = [
                    'transactionId' => $transaction['transactionId'],
                    'code' => '00',
                    'description' => 'Success',
                ];

                continue;
            }

            $forceWebhookManually = ['webhook' => false, 'sapo' => false, 'haravan' => false, 'shopify' => false, 'googlesheet' => false, 'output_device' => false];
            $rabbitmq = null;

            if ($this->config->webhookOffloadEnabled) {
                $rabbitmq = new RabbitMQClient();
                $webhookQueuable = $rabbitmq->connect();
            }

            if ($rabbitmq && $webhookQueuable) {
                $msg = new AMQPMessage(
                    json_encode(['account_number' => $transactionDetails->account_number, 'parser_id' => $parserId]),
                    ['delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT]
                );

                try {
                    $rabbitmq->queueDeclare('webhook');
                    $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('webhook', 'name'));
                } catch (Exception $e) {
                    log_message('error', 'ABBANK webhook queue failed: ' . $e->getMessage());
                    $forceWebhookManually['webhook'] = true;
                }

                try {
                    $rabbitmq->queueDeclare('sapo');
                    $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('sapo', 'name'));
                } catch (Exception $e) {
                    log_message('error', 'ABBANK Sapo queue failed: ' . $e->getMessage());
                    $forceWebhookManually['sapo'] = true;
                }

                try {
                    $rabbitmq->queueDeclare('haravan');
                    $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('haravan', 'name'));
                } catch (Exception $e) {
                    log_message('error', 'ABBANK Haravan queue failed: ' . $e->getMessage());
                    $forceWebhookManually['haravan'] = true;
                }

                try {
                    $rabbitmq->queueDeclare('shopify');
                    $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('shopify', 'name'));
                } catch (Exception $e) {
                    log_message('error', 'ABBANK Shopify queue failed: ' . $e->getMessage());
                    $forceWebhookManually['shopify'] = true;
                }

                try {
                    $rabbitmq->queueDeclare('googlesheet');
                    $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('googlesheet', 'name'));
                } catch (Exception $e) {
                    log_message('error', 'ABBANK GoogleSheet queue failed: ' . $e->getMessage());
                    $forceWebhookManually['googlesheet'] = true;
                }

                try {
                    $rabbitmq->queueDeclare('output_device');
                    $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('output_device', 'name'));
                } catch (Exception $e) {
                    log_message('error', 'ABBANK output device queue failed: ' . $e->getMessage());
                    $forceWebhookManually['output_device'] = true;
                }

                $rabbitmq->close();
            }

            if (! $webhookQueuable || $forceWebhookManually['webhook']) {
                model(WebhooksModel::class)->doWebhooks($transactionDetails->account_number, $parserId);
                model(NotificationTelegramModel::class)->checkAndAddQueue($parserId);
                model(NotificationLarkMessengerModel::class)->checkAndAddQueue($parserId);
            }

            if (! $webhookQueuable || $forceWebhookManually['sapo']) {
                $sapoModel = model(SapoModel::class);
                $sapoModel->doWebhooks($transactionDetails->account_number, $parserId);
            }

            if (! $webhookQueuable || $forceWebhookManually['haravan']) {
                $haravanModel = model(HaravanModel::class);
                $haravanModel->doWebhooks($transactionDetails->account_number, $parserId);
            }

            if (! $webhookQueuable || $forceWebhookManually['shopify']) {
                ExecuteShopifyWebhooksAction::run($parserId);
            }

            if (! $webhookQueuable || $forceWebhookManually['googlesheet']) {
                ExecuteGoogleSheetsWebhooksAction::run($parserId);
            }

            if (! $webhookQueuable || $forceWebhookManually['output_device']) {
                ExecuteOutputDevice::run($parserId, 0);
            }

            model(CounterModel::class)->transaction($company->id, false, $transactionDetails->amount_in, $transactionDetails->amount_out);

            model(BankAccountCashflowModel::class)->amount_update(
                $company->id,
                date('Y-m-d', strtotime($transactionDetails->transaction_date)),
                $transactionDetails->bank_account_id,
                $transactionDetails->amount_in,
                $transactionDetails->amount_out,
                $transactionDetails->sub_account,
                $transactionDetails->accumulated
            );

            $bankAccountModel->update($transactionDetails->bank_account_id, [
                'last_transaction' => $transactionDetails->transaction_date,
                'accumulated' => $transactionDetails->accumulated,
            ]);

            $transactions[] = [
                'transactionId' => $transaction['transactionId'],
                'code' => '00',
                'description' => 'Success',
            ];
        }

        $hasError = false;

        foreach ($transactions as $transaction) {
            if ($transaction['code'] !== '00') {
                $hasError = true;
                break;
            }
        }

        return $this->respond([
            'code' => $hasError ? 400 : 200,
            'message' => $hasError ? 'Some transactions failed' : 'Success',
            'requestId' => $data['requestId'],
            'results' => $transactions,
        ], $hasError ? 400 : 200);
    }

    protected function authorize(): bool
    {
        $key = explode('Basic ', $this->request->getHeaderLine('Authorization'))[1] ?? '';

        return $key && $key === base64_encode("{$this->config->basicAuthUsername}:{$this->config->basicAuthPassword}");
    }
}
