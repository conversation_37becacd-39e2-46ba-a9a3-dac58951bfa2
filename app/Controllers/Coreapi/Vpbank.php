<?php

namespace App\Controllers\Coreapi;

use App\Actions\BankAccount\FilterTransactionAction;
use Exception;
use App\Models\SapoModel;
use phpseclib3\Crypt\RSA;
use App\Models\CompanyModel;
use App\Models\CounterModel;
use App\Models\HaravanModel;
use App\Models\WebhooksModel;
use App\Models\SmsParserModel;
use App\Actions\PayCodeDetector;
use App\Models\BankAccountModel;
use App\Models\TransactionsModel;
use CodeIgniter\API\ResponseTrait;
use App\Models\BankSubAccountModel;
use PhpAmqpLib\Message\AMQPMessage;
use phpseclib3\Crypt\PublicKeyLoader;
use App\Models\VpbankNotificationModel;
use App\Models\BankAccountCashflowModel;
use App\Models\NotificationTelegramModel;
use App\Models\VpbankNotificationRawModel;
use CodeIgniter\RESTful\ResourceController;
use App\Models\NotificationLarkMessengerModel;
use App\Actions\OutputDevice\ExecuteOutputDevice;
use App\Actions\Shopify\ExecuteShopifyWebhooksAction;
use App\Actions\Transaction\CreateTransactionQueueAction;
use App\Actions\GoogleSheets\ExecuteGoogleSheetsWebhooksAction;
use App\Actions\Merchant\PushMerchantTransactionNotificationAction;
use App\Actions\Mobile\PushMobileTransactionNotificationQueueAction;

class Vpbank extends ResourceController
{
    use ResponseTrait;

    const BANK_ID = 2;

    protected $helpers = ['general'];

    protected $debug;

    protected $authUsername;

    protected $authPassword;

    protected $vpbankCertPath;

    protected $privateKeyPath;

    protected $webhookOffload;
    protected $enableFilterTransaction;

    public function __construct()
    {
        $config = config(\Config\Vpbank::class);

        $this->authUsername = $config->notifyAuthUsername;
        $this->authPassword = $config->notifyAuthPassword;
        $this->vpbankCertPath = $config->notifyVpbankCertPath;
        $this->privateKeyPath = $config->notifyPrivateKeyPath;
        $this->debug = $config->notifyDebug ?? false;
        $this->webhookOffload = $config->notifyWebhookOffload ?? false;
        $this->enableFilterTransaction = $config->enableFilterTransaction ?? false;
    }
                                            
    public function notifyTransaction()             
    {
        $this->debugTime('Captured');

        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }

        $signature = $this->request->getHeaderLine('Signature');
        $authorizationToken = trim(str_replace('Basic', '', $this->request->getHeaderLine('Authorization')));

        $encryptedData = [
            'requestId' => trim($this->request->getVar('requestId')),
            'accountNumber' => trim($this->request->getVar('accountNumber')),
            'amount' => trim($this->request->getVar('amount')),
            'currency' => trim($this->request->getVar('currency')),
            'dateTime' => trim($this->request->getVar('dateTime')),
            'newMemo' => trim($this->request->getVar('newMemo')),
            'transactionId' => trim($this->request->getVar('transactionId')),
        ];

        $encryptedRules = [
            'requestId' => ['required', 'string'],
            'accountNumber' => ['required', 'string'],
            'amount' => ['required', 'string'],
            'currency' => ['required', 'string'],
            'dateTime' => ['required', 'string'],
            'newMemo' => ['permit_empty', 'string'],
            'transactionId' => ['required', 'string'],
        ];

        if (! $this->validateData($encryptedData, $encryptedRules)) {
            log_message('error', 'VPbank Notification Error: Bad encrypted request - ' . json_encode($this->validator->getErrors()) . ' - ' . json_encode($encryptedData));

            return $this->respond([
                'error_code' => 400,
                'message' => 'Bad request',
                'errors' => $this->validator->getErrors()
            ], 400);
        }

        if (!$authorizationToken || $authorizationToken !== base64_encode(sprintf('%s:%s', $this->authUsername, $this->authPassword))) {
            return $this->respond([
                'error_code' => 401,
                'message' => 'Unauthenticated',
            ], 401);
        }

        $privateKey = PublicKeyLoader::loadPrivateKey(file_get_contents($this->privateKeyPath));

        $this->debugTime('Private key loaded');

        $rawData = [
            'requestId' => $this->decryptData($encryptedData['requestId'], $privateKey),
            'accountNumber' => $this->decryptData($encryptedData['accountNumber'], $privateKey),
            'amount' => $this->decryptData($encryptedData['amount'], $privateKey),
            'currency' => $this->decryptData($encryptedData['currency'], $privateKey),
            'dateTime' => $this->decryptData($encryptedData['dateTime'], $privateKey),
            'newMemo' => $this->decryptData($encryptedData['newMemo'], $privateKey),
            'transactionId' => $this->decryptData($encryptedData['transactionId'], $privateKey),
        ];

        $this->debugTime('Data decrypted');

        $rules = [
            'requestId' => ['required', 'string'],
            'accountNumber' => ['required', 'string'],
            'amount' => ['required', 'string'],
            'currency' => ['required', 'string'],
            'dateTime' => ['required', 'string', 'regex_match[/^[0-9]{4}\-[0-9]{2}\-[0-9]{2}\s[0-9]{2}\:[0-9]{2}\:[0-9]{2}$/]'],
            'newMemo' => ['permit_empty', 'string'],
            'transactionId' => ['required', 'string'],
        ];

        if (! $this->validateData($rawData, $rules)) {
            log_message('error', 'VPbank Notification Error: Bad request - ' . json_encode($this->validator->getErrors()) . ' - ' . json_encode($encryptedData));

            return $this->respond([
                'error_code' => 400,
                'message' => 'Bad request',
                'errors' => $this->validator->getErrors()
            ], 400);
        }

        $this->debugTime('Payload validated');

        $plainSignature = sprintf('%s%s%s%s%s%s', $rawData['accountNumber'], $rawData['amount'], $rawData['currency'], $rawData['dateTime'], $rawData['newMemo'], $rawData['transactionId']);

        $vpbankPublicKey = PublicKeyLoader::loadPublicKey(file_get_contents($this->vpbankCertPath));

        $this->debugTime('Public key loaded');

        $signatureIsValid = $vpbankPublicKey
            ->withPadding(RSA::ENCRYPTION_OAEP)
            ->withHash('sha256')
            ->withMGFHash('sha256')
            ->verify($plainSignature, base64_decode($signature));

        if (!$signatureIsValid && ENVIRONMENT !== 'development') {
            log_message('error', 'VPbank Notification Error: Signature invalid - ' . $signature);

            return $this->respond([
                'error_code' => 400,
                'message' => 'Singature invalid',
            ], 400);
        }

        $this->debugTime('Signature validated');

        $vpbankNotificationRawModel = model(VpbankNotificationRawModel::class);
        $vpbankNotificationModel = model(VpbankNotificationModel::class);
        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);

        $rawNotificationId = $vpbankNotificationRawModel->insert(['payload' => json_encode($encryptedData), 'ip' =>$this->request->getIPAddress()]);

        $this->debugTime('Raw notification inserted');

        if (!$rawNotificationId) {
            log_message('error', 'VPbank Notification Error: Raw Notification insert failed');

            return $this->respond([
                'error_code' => 503,
                'message' => 'Service unavailable',
            ], 503);
        }

        $duplicatedTransaction = $vpbankNotificationModel->where(['transactionId' => $rawData['transactionId']])->countAllResults();
        if ($duplicatedTransaction) {
            log_message('error', 'VPBank Notification Error: Transaction Duplicated - Raw #' . $rawNotificationId);

            return $this->respond([
                'error_code' => 409,
                'message' => 'Transaction duplicated',
            ], 409);
        }

        $this->debugTime('No duplicated');

        // Determine if bank account exist
        $bankAccountDetails = $bankAccountModel->where([
            'account_number' => $rawData['accountNumber'],
            'bank_id' => Vpbank::BANK_ID,
            'active' => true,
        ])->first();

        if (!is_object($bankAccountDetails)) {
            log_message('error', 'VPBank Notification Error: Account number does not exists - Raw #' . $rawNotificationId);

            return $this->respond([
                'error_code' => 4040,
                'message' => 'Account number not exist',
            ], 404);
        }

        $this->debugTime('Found bank account');

        // Skip Bank API notification when switch to SMS Banking
        if ($bankAccountDetails->bank_id == VPBank::BANK_ID && $bankAccountDetails->bank_sms) {
            if ($this->debug) {
                log_message('error', 'VPBank Notification Error: Skip Bank API notification when VPBank account switch to SMS connection');
            }

            return $this->respond([
                'error_code' => 0,
                'message' => 'OK',
            ]);
        }

        // Determine if bank sub account exist
        $va = '';
        $bankSubAccountDetails = null;

        preg_match('/TKP[A-Z0-9]{2,16}/', $rawData['newMemo'], $matches, PREG_OFFSET_CAPTURE);
        if (is_array($matches) && count($matches) > 0 && isset($matches[0][0])) {
            $va = str_replace('TKP', '', $matches[0][0]);
        }

        if ($va) {
            $bankSubAccountDetails = $bankSubAccountModel->where([
                'sub_account' => $va,
                'bank_account_id' => $bankAccountDetails->id,
                'va_active' => 1,
                'active' => 1,
                'deleted_at' => null,
            ])->first();
        }

        $this->debugTime('VA searched');

        $notificationId = $vpbankNotificationModel->insert([
            'raw_id' => $rawNotificationId,
            'requestId' => $rawData['requestId'],
            'accountNumber' => $rawData['accountNumber'],
            'amount' => $rawData['amount'],
            'currency' => $rawData['currency'],
            'dateTime' => $rawData['dateTime'],
            'newMemo' => $rawData['newMemo'],
            'transactionId' => $rawData['transactionId'],
        ]);

        $this->debugTime('Notification inserted');

        if (!$notificationId) {
            log_message('error', 'VPbank Notification Error: Notification insert failed');

            return $this->respond([
                'error_code' => 503,
                'message' => 'Service unavailable',
            ], 503);
        }

        if ($this->enableFilterTransaction && ! $bankAccountDetails->merchant_id) {
            $filterTransaction = FilterTransactionAction::forBankAccount($bankAccountDetails->id)
                ->amountIn(true)
                ->description($rawData['newMemo'])
                ->payload($rawData)
                ->filter();

            if ($filterTransaction['isRejected']) {
                return $this->respond([
                    'error_code' => 0,
                    'message' => 'OK',
                ]);
            }
        }

        $smsParserModel = model(SmsParserModel::class);
        $companyModel = model(CompanyModel::class);
        $transactionsModel = model(TransactionsModel::class);

        $code = null;

        if (is_object($bankAccountDetails)) {
            $code = PayCodeDetector::getCode($rawData['newMemo'] ?? '', $bankAccountDetails->company_id);
        }

        $this->debugTime('Paycode searched');

        $parserData = [
            'sms_id' => $notificationId,
            'reference_number' => $rawData['transactionId'],
            'gateway' => 'VPBank',
            'transaction_date' => date('Y-m-d H:i:s', strtotime($rawData['dateTime'])),
            'account_number' => $bankAccountDetails->account_number,
            'sub_account' => $bankSubAccountDetails->sub_account ?? null,
            'amount_in' => $rawData['amount'],
            'amount_out' => 0,
            'accumulated' => 0,
            'code' => $code,
            'transaction_content' => $rawData['newMemo'],
            'source' => 'BankAPINotify',
            'body' => 'BankAPINotify ' . $rawData['newMemo'],
            'parser_status' => 'Success',
            'bank_account_id' => $bankAccountDetails->id,
            'merchant_id' => $bankAccountDetails->merchant_id,
            'transaction_id' => uuid()
        ];

        $queueTransactionId = null;
        $parserId = null; // primary id of tb_autopay_sms_parsed table
        $transactionDetails = null;

        $merchantConfig = config(\Config\Merchant::class);

        if ($bankAccountDetails->merchant_id && $merchantConfig->enabledTransactionInsertionOffload) {
            $queueTransactionId = CreateTransactionQueueAction::run((object) $parserData, $bankAccountDetails);
        }

        if (!$queueTransactionId) {
            $parserId = $smsParserModel->insert($parserData);
            $transactionDetails = $smsParserModel->find($parserId);
        }

        $this->debugTime('Sms parser inserted');

        if (!$parserId && !$queueTransactionId) {
            $vpbankNotificationModel->where('id', $notificationId)->delete();
            log_message('error', 'VPBank Notification Error: SMSParserModel insert failed - Raw #' . $rawNotificationId);

            return $this->respond([
                'error_code' => 503,
                'message' => 'Service unavailable',
            ], 503);
        }

        if (is_object($transactionDetails) && is_numeric($transactionDetails->account_number) && $bankAccountDetails->merchant_id && !$queueTransactionId) {
            try {
                PushMerchantTransactionNotificationAction::run(
                    $bankAccountDetails, 
                    $transactionDetails,
                    $merchantConfig->enabledPushTransactionNotificationOffload
                );
            } catch (Exception $e) {
                log_message('error', '[PushableMerchantTransactionNotificationAction] ' . $e->getMessage() . ' - ' . $e->getTraceAsString());
            }
        }

        if (is_object($transactionDetails) && is_numeric($transactionDetails->account_number) && !$bankAccountDetails->merchant_id) {
            try {
                PushMobileTransactionNotificationQueueAction::run($transactionDetails, $bankAccountDetails, $bankSubAccountDetails);
            } catch (Exception $e) {
                log_message('error', 'VPBank PushMobileTransactionNotificationQueueAction failed: ' . $e->getMessage() . ' - ' . $e->getTraceAsString() . ' - ' . json_encode($transactionDetails));
            }

            $this->debugTime('Mobile transaction notification pushed');
            
            $companyCheck = $companyModel->select("tb_autopay_company.id")
                ->join("tb_autopay_company_subscription", "tb_autopay_company.id=tb_autopay_company_subscription.company_id")
                ->join("tb_autopay_bank_account", "tb_autopay_bank_account.company_id=tb_autopay_company.id")
                ->where([
                    'tb_autopay_bank_account.id' => $bankAccountDetails->id, 
                    'tb_autopay_company_subscription.status' => 'Active',
                    'tb_autopay_company.status' => 'Active', 'tb_autopay_company.active' => 1
                ])->get()->getResult();

            $this->debugTime('Company checked');
            
            if (count($companyCheck) == 1) {
                $webhookQueuable = $this->webhookOffload;
                $forceWebhookManually = ['webhook' => false, 'sapo' => false, 'haravan' => false, 'shopify' => false, 'googlesheet' => false, 'output_device' => false, 'viber' => false];
                $rabbitmq = null;

                if ($webhookQueuable) {
                    $rabbitmq = new \App\Libraries\RabbitMQClient;
                    $webhookQueuable = $rabbitmq->connect();
                }

                if ($rabbitmq && $webhookQueuable) {
                    $this->debugTime('RabbitMQ connected');

                    $msg = new AMQPMessage(
                        json_encode(['account_number' => $transactionDetails->account_number, 'parser_id' => $parserId]),
                        array('delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT)
                    );

                    // webhooks
                    try {
                        $rabbitmq->queueDeclare('webhook');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('webhook', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'VPBank API webhook queue failed: ' . $e->getMessage());
                        $forceWebhookManually['webhook'] = true;
                    }

                    $this->debugTime('Webhook queued');
                    
                    // sapo
                    try {
                        $rabbitmq->queueDeclare('sapo');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('sapo', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'VPBank API Sapo queue failed: ' . $e->getMessage());
                        $forceWebhookManually['sapo'] = true;
                    }

                    $this->debugTime('Sapo queued');

                    // haravan
                    try {
                        $rabbitmq->queueDeclare('haravan');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('haravan', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'VPBank API Haravan queue failed: ' . $e->getMessage());
                        $forceWebhookManually['haravan'] = true;
                    } 

                    $this->debugTime('Haravan queued');

                    // shopify
                    try {
                        $rabbitmq->queueDeclare('shopify');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('shopify', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'VPBank API Shopify queue failed: ' . $e->getMessage());
                        $forceWebhookManually['shopify'] = true;
                    }

                    $this->debugTime('Shopify queued');
                    
                    // googlesheet
                    try {
                        $rabbitmq->queueDeclare('googlesheet');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('googlesheet', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'VPBank API GoogleSheet queue failed: ' . $e->getMessage());
                        $forceWebhookManually['googlesheet'] = true;
                    }

                    $this->debugTime('GoogleSheet queued');

                    // output device
                    try {
                        $rabbitmq->queueDeclare('output_device');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('output_device', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'VPBanl API output device queue failed: ' . $e->getMessage());
                        $forceWebhookManually['output_device'] = true;
                    }

                    $this->debugTime('Output device queued');
                    
                    // viber
                    try {
                        $rabbitmq->queueDeclare('viber');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('viber', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'VPBank API Viber queue failed: ' . $e->getMessage());
                        $forceWebhookManually['viber'] = true;
                    }

                    $this->debugTime('Viber queued');

                    $rabbitmq->close();
                } 
                
                if (!$webhookQueuable || $forceWebhookManually['webhook']) {
                    // webhooks
                    $webhooksModel = model(WebhooksModel::class);
                    $webhooksModel->doWebhooks($transactionDetails->account_number, $parserId);
                    $this->debugTime('Webhook force executed');
                }

                if (!$webhookQueuable || $forceWebhookManually['sapo']) {
                    // sapo
                    $sapoModel = model(SapoModel::class);
                    $sapoModel->doWebhooks($transactionDetails->account_number, $parserId);
                    $this->debugTime('Sapo force executed');
                }

                if (!$webhookQueuable || $forceWebhookManually['haravan']) {
                    // haravan
                    $haravanModel = model(HaravanModel::class);
                    $haravanModel->doWebhooks($transactionDetails->account_number, $parserId);
                    $this->debugTime('Haravan force executed');
                }

                if (!$webhookQueuable || $forceWebhookManually['shopify']) {
                    // shopify
                    ExecuteShopifyWebhooksAction::run($parserId);
                    $this->debugTime('Shopify force executed');
                }

                if (!$webhookQueuable || $forceWebhookManually['googlesheet']) {
                    // googlesheet
                    ExecuteGoogleSheetsWebhooksAction::run($parserId);
                }

                if (!$webhookQueuable || $forceWebhookManually['output_device']) {
                    // output device
                    ExecuteOutputDevice::run($parserId, 0);
                    $this->debugTime('Output device force executed');
                }
                
                if (!$webhookQueuable || $forceWebhookManually['viber']) {
                    // viber
                    (new \App\Features\Viber\ViberFeature)->sendNotificationByTransactionId($parserId);
                    $this->debugTime('Viber force executed');
                }
                
                if (!$webhookQueuable || $forceWebhookManually['webhook']) {
                    // telegram queue
                    $notificationTelegramModel = model(NotificationTelegramModel::class);
                    $notificationTelegramModel->checkAndAddQueue($parserId);
                    
                    // lark messenger queue
                    $notificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);
                    $notificationLarkMessengerModel->checkAndAddQueue($parserId);

                    $this->debugTime('Telegram and Lark Messenger force executed');
                }

                //counter
                $counterModel = model(CounterModel::class);
                $counterModel->transaction($companyCheck[0]->id, false, $transactionDetails->amount_in, $transactionDetails->amount_out);

                $this->debugTime('Counter updated');

                // cashflow
                $bankAccountCashFlowModel = model(BankAccountCashflowModel::class);
                $bankAccountCashFlowModel->amount_update($companyCheck[0]->id, date('Y-m-d', strtotime($transactionDetails->transaction_date)), $transactionDetails->bank_account_id, $transactionDetails->amount_in, $transactionDetails->amount_out, $transactionDetails->sub_account, $transactionDetails->accumulated);

                $this->debugTime('Bank account cashflow updated');

                $lastTransaction = $transactionDetails;

                if (is_object($lastTransaction))
                    $bankAccountModel->set(['last_transaction' => $lastTransaction->transaction_date, 'accumulated' => $lastTransaction->accumulated])->where(['id' => $bankAccountDetails->id])->update();
            }
        }

        $this->debugTime('All done');

        return $this->respond([
            'error_code' => 0,
            'message' => 'OK',
        ]);
    }

    protected function decryptData($encryptedData, $privateKey)
    {
        return $privateKey->withPadding(RSA::ENCRYPTION_OAEP)
            ->withHash('sha256')
            ->withMGFHash('sha256')
            ->decrypt(base64_decode($encryptedData));
    }

    protected function debugTime($message)
    {
        if ($this->debug) {
            $microtime = microtime(true);
            $datetime = \DateTime::createFromFormat('U.u', sprintf('%.6f', $microtime));
            log_message('error', sprintf('[VPB_NOTIFY_DEBUG_TIME] %s: %s', $message, $datetime->format('Y-m-d H:i:s.u')));
        }
    }
}
