<?php

namespace App\Controllers\Coreapi;

use App\Actions\BankAccount\FilterTransactionAction;
use Exception;
use App\Models\SapoModel;
use CodeIgniter\Controller;
use App\Models\CompanyModel;
use App\Models\CounterModel;
use App\Models\HaravanModel;
use App\Models\MerchantModel;
use App\Models\WebhooksModel;
use App\Models\SmsParserModel;
use App\Models\BankAccountModel;
use App\Models\TransactionsModel;
use App\Models\ConfigurationModel;
use CodeIgniter\API\ResponseTrait;
use App\Models\BankSubAccountModel;
use PhpAmqpLib\Message\AMQPMessage;
use CodeIgniter\HTTP\IncomingRequest;
use App\Models\BankAccountCashflowModel;
use App\Models\NotificationTelegramModel;
use App\Models\VietinbankNotificationModel;
use CodeIgniter\RESTful\ResourceController;
use App\Models\NotificationLarkMessengerModel;
use App\Models\NotificationTelegramQueueModel;
use App\Models\VietinbankNotificationRawModel;
use App\Actions\Shopify\ExecuteShopifyWebhooksAction;
use App\Actions\GoogleSheets\ExecuteGoogleSheetsWebhooksAction;
use App\Actions\Transaction\CreateTransactionQueueAction;
use App\Actions\Merchant\PushMerchantTransactionNotificationAction;
use App\Actions\Mobile\PushMobileTransactionNotificationQueueAction;
use App\Actions\PayCodeDetector;

use App\Actions\OutputDevice\ExecuteOutputDevice;

class Vietinbank extends ResourceController
{
    use ResponseTrait;

    protected $header = [];

    public const RESPONSE_SUCCESSFUL_ERR_CODE = '00';
    public const RESPONSE_SEVERAL_RECORD_INSERT_FAILED_ERR_CODE = '99';

    protected function standardResponse($data)
    {
        return $this->respond([
            'header' => $this->header,
            'data' => $data,
        ]);
    }

    protected function resolveResponseRecord($record, $code = null, $message = null)
    {
        return [
            'transId' => $record['transId'],
            'recordNo' => $record['recordNo'],
            'transTime' => $record['transTime'],
            'phoneNo' => $record['phoneNo'],
            'custAcct' => $record['custAcct'],
            'status' => [
                'code' => $code,
                'message' => $message,
            ],
            'addInfo' => isset($record['addInfo']) ? (object) $record['addInfo'] : (object) [],
            'preseve1' => $record['preseve1'],
            'preseve2' => $record['preseve2'],
            'preseve3' => $record['preseve3'],
        ];
    }
 
    public function notifyTransaction()
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }

        helper(['general']);
        
        $config = config(\Config\Vietinbank::class);
        $merchantConfig = config(\Config\Merchant::class);

        $json = json_decode(json_encode($this->request->getJSON()), true);

        $data = [
            'header' => $json['header'] ?? [],
            'data' => $json['data'] ?? [],
        ];

        $this->header = $data['header'];

        $rules = [
            'header' => ['required'],
            'header.msgId' => ['required', 'string'],
            'header.msgType' => ['required', 'string'],
            'header.channelId' => ['required', 'string'],
            'header.gatewayId' => ['required', 'string'],
            'header.providerId' => ['required', 'string'],
            'header.merchantId' => ['required', 'string'],
            'header.productId' => ['required', 'string'],
            'header.version' => ['required', 'string'],
            'header.version' => ['required', 'string'],
            'header.language' => ['required', 'string'],
            'header.timestamp' => ['required', 'regex_match[/^[0-9]{14}$/]'],
            'header.recordNum' => ['required', 'string'],
            'header.clientIP' => ['required', 'string'],
            'header.username' => ['required', 'string'],
            'header.password' => ['required', 'string'],
            'header.token' => ['required', 'string'],
            'header.preseve1' => ['permit_empty', 'string'],
            'header.preseve2' => ['permit_empty', 'string'],
            'header.preseve3' => ['permit_empty', 'string'],
            'header.signature' => ['required', 'string'],
            'header.encrypt' => ['permit_empty', 'string'],

            'data' => ['required'],
            'data.records' => ['required'],
            'data.records.*.transId' => ['required', 'string'],
            'data.records.*.recordNo' => ['required', 'string'],
            'data.records.*.transTime' => ['required', 'regex_match[/^[0-9]{14}$/]'],
            'data.records.*.phoneNo' => ['required', 'string'],
            'data.records.*.content' => ['required', 'string'],
            'data.records.*.custAcct' => ['permit_empty', 'string'],
            'data.records.*.sendBankId' => ['permit_empty', 'string'],
            'data.records.*.sendBranchId' => ['permit_empty', 'string'],
            'data.records.*.sendAcctId' => ['permit_empty', 'string'],
            'data.records.*.sendmAcctName' => ['permit_empty', 'string'],
            'data.records.*.recvBankId' => ['permit_empty', 'string'],
            'data.records.*.recvBranchId' => ['permit_empty', 'string'],
            'data.records.*.recvAcctId' => ['permit_empty', 'string'],
            'data.records.*.recvAcctName' => ['permit_empty', 'string'],
            'data.records.*.amount' => ['required', 'string'],
            'data.records.*.balance' => ['required', 'string'],
            'data.records.*.remark' => ['required', 'string'],
            'data.records.*.addInfo' => ['permit_empty'],
            'data.records.*.preseve1' => ['required', 'string', 'in_list[CREDIT,DEBIT]'],
            'data.records.*.preseve2' => ['permit_empty', 'string'],
            'data.records.*.preseve3' => ['permit_empty', 'string'],
        ];

        if (! $this->validateData($data, $rules)) {
            $validationErrors = $this->validator->getErrors();
            $records = $data['data']['records'] ?? [];

            log_message('error', "VietinBank Notification Error: Bad request " . json_encode($validationErrors));
            log_message('error', "VietinBank Notification Payload: " . json_encode($data));

            return $this->standardResponse([
                'errors' => [
                    'errorCode' => '40',
                    'errorDesc' => 'Several records invalid: ' . implode(', ', $this->validator->getErrors()),
                    'errorCodeVPG' => '01',
			        'errorDescVPG' => 'Failed'
                ],
                'records' => array_map(function($record, $index) use ($validationErrors) {
                    $validationErrorKeys = array_keys($validationErrors);
                    $recordValidationErrors = array_filter($validationErrorKeys, function($key) use ($index) { return strpos($key, 'data.records.'.$index) > -1; });
                    
                    if (count($recordValidationErrors)) {
                        return $this->resolveResponseRecord($record, '40', implode(', ', $recordValidationErrors));
                    }

                    return $this->resolveResponseRecord($record, '20', 'Record valid');
                }, $records, array_keys($records)),
            ], 400);
        }

        $vietinbankNotificationRawModel = model(VietinbankNotificationRawModel::class);
        $vietinbankNotificationModel = model(VietinbankNotificationModel::class);

        $vietinbankNotificationRawId = $vietinbankNotificationRawModel->insert(['payload' => json_encode($data), 'ip' =>$this->request->getIPAddress()]);

        // Verify signature
        $vietinbankPublicKey = openssl_pkey_get_public(file_get_contents($config->notifyVietinbankPublicKeyPath));

        try {
            $isValidSignature = openssl_verify(
                $data['header']['msgId'] . $data['header']['gatewayId'] . $data['header']['providerId'], 
                base64_decode($data['header']['signature']), 
                $vietinbankPublicKey, 
                OPENSSL_ALGO_SHA256
            );
        } catch (Exception $e) {
            $isValidSignature = false;
        }

        if (getenv('CI_ENVIRONMENT') === 'development') {
            $isValidSignature = true;
        }

        if (!$isValidSignature) {
            return $this->standardResponse([
                'errors' => [
                    'errorCode' => '93',
                    'errorDesc' => 'Verify signature failed',
                    'errorCodeVPG' => '01',
			        'errorDescVPG' => 'Failed'
                ],
                'records' => array_map(function($record) {
                    return $this->resolveResponseRecord($record, '93', 'Verify signature failed');
                }, $data['data']['records'] ?? []),
            ], 400);
        }

        foreach ($data['data']['records'] as &$record) {
            $duplicatedTransaction = $vietinbankNotificationModel->where(['transId' => $record['transId'], 'preseve1' => $record['preseve1']])->countAllResults();
            if ($duplicatedTransaction) {
                if ($config->notifyDebug) log_message('error', "Vietinbank Notification Error: Transaction Duplicated - Raw #" . $vietinbankNotificationRawId);

                $record = $this->resolveResponseRecord($record, '49', 'Transaction ID duplicated');
                continue;
            }

            // Determine if bank account exist
            $bankAccountModel = model(BankAccountModel::class);
            $bankAccountDetails = $bankAccountModel->where([
                'account_number' => $record['custAcct'],
                'bank_id' => 6,
                'active' => true,
            ])->get()->getRow();

            if (!is_object($bankAccountDetails)) {
                log_message('error', "Vietinbank Notification Error: Account number does not exists - Raw #" . $vietinbankNotificationRawId);
    
                $record = $this->resolveResponseRecord($record, '40', 'Account number does not exists in SePay');
                continue;
            }

            // Skip Bank API notification when switch to SMS Banking
            if ($bankAccountDetails->bank_id == 6 && $bankAccountDetails->bank_sms) {
                if ($config->notifyDebug) {
                    log_message('error', 'Skip Bank API notification when Vietinbank account switch to SMS connection');
                    log_message('error', json_encode($record));
                }

                $record = $this->resolveResponseRecord($record, '00', 'Successful');
                continue;
            }

            // Determine if bank sub account exist
            $bankSubAccountModel = model(BankSubAccountModel::class);

            if (isset($record['va'])) {
                $bankSubAccountDetails = $bankSubAccountModel->where([
                    'sub_account' => $record['va'],
                    'bank_account_id' => $bankAccountDetails->id,
                ])->get()->getRow();
    
                if (!is_object($bankSubAccountDetails)) {
                    log_message('error', "Vietinbank Notification Error: VA number does not exists - Raw #" . $vietinbankNotificationRawId);
    
                    $record = $this->resolveResponseRecord($record, '40', 'VA number does not exists in SePay');
                    continue;
                }
            } else {
                preg_match('/TKP[A-Z0-9]{2,16}/', $record['remark'], $matches, PREG_OFFSET_CAPTURE);
                if (is_array($matches) && count($matches) > 0 && isset($matches[0][0])) {
                    $record['va'] = str_replace("TKP","",$matches[0][0]);
                }
            }
    
            if (isset($record['va'])) {
                $bankSubAccountDetails = $bankSubAccountModel->where([
                    'sub_account' => $record['va'],
                    'bank_account_id' => $bankAccountDetails->id,
                    'va_active' => 1,
                    'active' => 1,
                    'deleted_at' => null,
                ])->get()->getRow();
            } else {
                $bankSubAccountDetails = null;
            }

            $vietinbankNotificationId = $vietinbankNotificationModel->insert([
                'raw_id' => $vietinbankNotificationRawId,
                'transId' => $record['transId'],
                'recordNo' => $record['recordNo'],
                'transTime' => $record['transTime'],
                'phoneNo' => $record['phoneNo'],
                'content' => $record['content'],
                'custAcct' => $record['custAcct'],
                'sendBankId' => $record['sendBankId'] ?? null,
                'sendBranchId' => $record['sendBranchId'] ?? null,
                'sendAcctId' => $record['sendAcctId'] ?? null,
                'sendmAcctName' => $record['sendmAcctName'] ?? null,
                'recvBankId' => $record['recvBankId'] ?? null,
                'recvBranchId' => $record['recvBranchId'] ?? null,
                'recvAcctName' => $record['recvAcctName'] ?? null,
                'amount' => $record['amount'],
                'balance' => $record['balance'],
                'remark' => $record['remark'],
                'addInfo' => isset($record['addInfo']) && is_array($record['addInfo']) ? json_encode($record['addInfo']) : null,
                'preseve1' => $record['preseve1'] ?? null,
                'preseve2' => $record['preseve2'] ?? null,
                'preseve3' => $record['preseve3'] ?? null,
            ]);

            $canSyncAccumulated = true;

            if (($config->enableFilterTransaction ?? false) && ! $bankAccountDetails->merchant_id) {
                $filterTransaction = FilterTransactionAction::forBankAccount($bankAccountDetails->id)
                    ->amountIn($record['preseve1'] === 'CREDIT')
                    ->description($record['remark'])
                    ->payload($record)
                    ->filter();

                if ($filterTransaction['isRejected']) {
                    $record = $this->resolveResponseRecord($record, '00', 'Successful');
                    continue;
                }

                $canSyncAccumulated = $filterTransaction['canSyncAccumulated'];
            }

            $smsParserModel = model(SmsParserModel::class);
            $companyModel = model(CompanyModel::class);
            $transactionsModel = model(TransactionsModel::class);

            $code = null;

            if (is_object($bankAccountDetails)) {
                $code = PayCodeDetector::getCode($record['remark'], $bankAccountDetails->company_id);
            }

            $tranferType = $record['preseve1'];

            $parserData = [
                'sms_id' => $vietinbankNotificationId,
                'reference_number' => $record['transId'],
                'gateway' => 'VietinBank',
                'transaction_date' => date('Y-m-d H:i:s', strtotime($record['transTime'])),
                'account_number' => $bankAccountDetails->account_number,
                'sub_account' => $bankSubAccountDetails->sub_account ?? null,
                'amount_in' => $tranferType === 'CREDIT' ?  $record['amount'] : 0,
                'amount_out' => $tranferType === 'DEBIT' ? $record['amount'] : 0,
                'accumulated' => $canSyncAccumulated ? $record['balance'] : 0,
                'code' => $code,
                'transaction_content' => $record['remark'],
                'source' => 'BankAPINotify',
                'body' => 'BankAPINotify ' . $record['remark'],
                'parser_status' => 'Success',
                'bank_account_id' => $bankAccountDetails->id,
                'merchant_id' => $bankAccountDetails->merchant_id,
                'transaction_id' => uuid()
            ];

            $queueTransactionId = null;
            $parserId = null; // primary id of tb_autopay_sms_parsed table
            $transactionDetails = null;

            if ($bankAccountDetails->merchant_id && $merchantConfig->enabledTransactionInsertionOffload) {
                $queueTransactionId = CreateTransactionQueueAction::run((object) $parserData, $bankAccountDetails);
            }

            if (!$queueTransactionId) {
                $parserId = $smsParserModel->insert($parserData);
                $transactionDetails = $smsParserModel->find($parserId);
            }

            if (!$parserId && !$queueTransactionId) {
                $vietinbankNotificationModel->where('id', $vietinbankNotificationId)->delete();
                log_message('error', 'Vietinbank Notification Error: SMSParserModel insert failed - Raw #' . $vietinbankNotificationRawId);

                $record = $this->resolveResponseRecord($record, '99', 'Several records insert failed');
                continue;
            }

            if (is_object($transactionDetails) && is_numeric($transactionDetails->account_number) && $bankAccountDetails->merchant_id && !$queueTransactionId) {
                log_message('error', 'im here');
                try {
                    PushMerchantTransactionNotificationAction::run(
                        $bankAccountDetails, 
                        $transactionDetails,
                        $merchantConfig->enabledPushTransactionNotificationOffload
                    );
                } catch (Exception $e) {
                    log_message('error', '[PushableMerchantTransactionNotificationAction] ' . $e->getMessage() . ' - ' . $e->getTraceAsString());
                }
            }

            if (is_object($transactionDetails) && is_numeric($transactionDetails->account_number) && !$bankAccountDetails->merchant_id) {
                try {
                    PushMobileTransactionNotificationQueueAction::run($transactionDetails, $bankAccountDetails, $bankSubAccountDetails);
                } catch (Exception $e) {
                    log_message('error', 'Vietinbank PushMobileTransactionNotificationQueueAction failed: ' . $e->getMessage() . ' - ' . $e->getTraceAsString() . ' - ' . json_encode($transactionDetails));
                }
                
                $companyCheck = $companyModel->select("tb_autopay_company.id")
                    ->join("tb_autopay_company_subscription", "tb_autopay_company.id=tb_autopay_company_subscription.company_id")
                    ->join("tb_autopay_bank_account", "tb_autopay_bank_account.company_id=tb_autopay_company.id")
                    ->where([
                        'tb_autopay_bank_account.id' => $bankAccountDetails->id, 
                        'tb_autopay_company_subscription.status' => 'Active',
                        'tb_autopay_company.status' => 'Active', 'tb_autopay_company.active' => 1
                    ])->get()->getResult();
                
                if (count($companyCheck) == 1) {
                    $webhookQueuable = $config->enableVietinbankWebhookOffload ?? false;
                    $forceWebhookManually = ['webhook' => false, 'sapo' => false, 'haravan' => false, 'shopify' => false, 'googlesheet' => false,'output_device' => false, 'viber' => false];
                    $rabbitmq = null;

                    if ($webhookQueuable) {
                        $rabbitmq = new \App\Libraries\RabbitMQClient;
                        $webhookQueuable = $rabbitmq->connect();
                    }

                    if ($rabbitmq && $webhookQueuable) {
                        $msg = new AMQPMessage(
                            json_encode(['account_number' => $transactionDetails->account_number, 'parser_id' => $parserId]),
                            array('delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT)
                        );

                        // webhooks
                        try {
                            $rabbitmq->queueDeclare('webhook');
                            $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('webhook', 'name'));
                        } catch (\Exception $e) {
                            log_message('error', 'Vietinbank API webhook queue failed: ' . $e->getMessage());
                            $forceWebhookManually['webhook'] = true;
                        }
                        
                        // sapo
                        try {
                            $rabbitmq->queueDeclare('sapo');
                            $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('sapo', 'name'));
                        } catch (\Exception $e) {
                            log_message('error', 'Vietinbank API Sapo queue failed: ' . $e->getMessage());
                            $forceWebhookManually['sapo'] = true;
                        }

                        // haravan
                        try {
                            $rabbitmq->queueDeclare('haravan');
                            $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('haravan', 'name'));
                        } catch (\Exception $e) {
                            log_message('error', 'Vietinbank API Haravan queue failed: ' . $e->getMessage());
                            $forceWebhookManually['haravan'] = true;
                        } 

                        // shopify
                        try {
                            $rabbitmq->queueDeclare('shopify');
                            $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('shopify', 'name'));
                        } catch (\Exception $e) {
                            log_message('error', 'Vietinbank API Shopify queue failed: ' . $e->getMessage());
                            $forceWebhookManually['shopify'] = true;
                        } 

                        //googlesheet
                        try {
                            $rabbitmq->queueDeclare('googlesheet');
                            $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('googlesheet', 'name'));
                        } catch (\Exception $e) {
                            log_message('error', 'Vietinbank API GoogleSheet queue failed: ' . $e->getMessage());
                            $forceWebhookManually['googlesheet'] = true;
                        }

                         // output device
                        try {
                            $rabbitmq->queueDeclare('output_device');
                            $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('output_device', 'name'));
                        } catch (\Exception $e) {
                            log_message('error', 'Vietinbank API output device queue failed: ' . $e->getMessage());
                            $forceWebhookManually['output_device'] = true;
                        } 
                        
                        // viber
                        try {
                            $rabbitmq->queueDeclare('viber');
                            $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('viber', 'name'));
                        } catch (\Exception $e) {
                            log_message('error', 'VietinBank API Viber queue failed: ' . $e->getMessage());
                            $forceWebhookManually['viber'] = true;
                        }

                        $rabbitmq->close();
                    } 
                    
                    if (!$webhookQueuable || $forceWebhookManually['webhook']) {
                        // webhooks
                        $webhooksModel = model(WebhooksModel::class);
                        $webhooksModel->doWebhooks($transactionDetails->account_number, $parserId);
                    }

                    if (!$webhookQueuable || $forceWebhookManually['sapo']) {
                        // sapo
                        $sapoModel = model(SapoModel::class);
                        $sapoModel->doWebhooks($transactionDetails->account_number, $parserId);
                    }

                    if (!$webhookQueuable || $forceWebhookManually['haravan']) {
                        // haravan
                        $haravanModel = model(HaravanModel::class);
                        $haravanModel->doWebhooks($transactionDetails->account_number, $parserId);
                    }

                    if (!$webhookQueuable || $forceWebhookManually['shopify']) {
                        // shopify
                        ExecuteShopifyWebhooksAction::run($parserId);
                    }

                    if (!$webhookQueuable || $forceWebhookManually['googlesheet']) {
                        // googlesheet
                        ExecuteGoogleSheetsWebhooksAction::run($parserId);
                    }
                    
                    if (!$webhookQueuable || $forceWebhookManually['webhook']) {
                        // telegram queue
                        $notificationTelegramModel = model(NotificationTelegramModel::class);
                        $notificationTelegramModel->checkAndAddQueue($parserId);
                        
                        // lark messenger queue
                        $notificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);
                        $notificationLarkMessengerModel->checkAndAddQueue($parserId);
                    }

                    // output device
                    if (!$webhookQueuable || $forceWebhookManually['output_device']) {
                        // output device queue
                        ExecuteOutputDevice::run($parserId,0);
                    }
                    
                    if (!$webhookQueuable || $forceWebhookManually['viber']) {
                        // viber
                        (new \App\Features\Viber\ViberFeature)->sendNotificationByTransactionId($parserId);
                    }
                    
                    //counter
                    $counterModel = model(CounterModel::class);
                    $counterModel->transaction($companyCheck[0]->id, false, $transactionDetails->amount_in, $transactionDetails->amount_out);

                    // cashflow
                    $bankAccountCashFlowModel = model(BankAccountCashflowModel::class);
                    $bankAccountCashFlowModel->amount_update($companyCheck[0]->id, date('Y-m-d', strtotime($transactionDetails->transaction_date)), $transactionDetails->bank_account_id, $transactionDetails->amount_in, $transactionDetails->amount_out, $transactionDetails->sub_account, $transactionDetails->accumulated);

                    $lastTransaction = $transactionDetails;

                    if (is_object($lastTransaction))
                        $bankAccountModel->set(['last_transaction' => $lastTransaction->transaction_date, 'accumulated' => $lastTransaction->accumulated])->where(['id' => $bankAccountDetails->id])->update();
                }
            }
 
            $record = $this->resolveResponseRecord($record, '00', 'Successful');
        }

        $errors = count(array_filter($data['data']['records'], function($record) {
            return $record['status']['code'] != '00' && $record['status']['code'] != '49';
        })) ? [
            'errorCode' => '99',
            'errorDesc' => 'Several records insert failed',
            'errorCodeVPG' => '01',
            'errorDescVPG' => 'Failed'
        ] : [
            'errorCode' => '00',
            'errorDesc' => 'Successful',
            'errorCodeVPG' => '00',
            'errorDescVPG' => 'Successful'
        ];

        return $this->standardResponse([
            'errors' => $errors,
            'records' => $data['data']['records'],
        ], 200);
    }
}