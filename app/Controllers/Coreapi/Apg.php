<?php

namespace App\Controllers\Coreapi;

use Config\Services;
use App\Config\Invoice;
use App\Features\PaymentGateway\NapasVietQrProfile;
use Config\NapasConfig;
use CodeIgniter\Controller;
use App\Models\CompanyModel;
use App\Models\InvoiceModel;
use App\Models\PgNapasVietqrProfileModel;
use App\Models\PgOrderModel;
use CodeIgniter\API\ResponseTrait;
use App\Models\PgProfileModel;

class Apg extends Controller
{
    use ResponseTrait;

    /** @var NapasConfig */
    protected $napasConfig;

    public function __construct()
    {
        $this->napasConfig = config(NapasConfig::class);
    }
    
    public function token()
    {
        if ($this->request->getMethod(true) !== 'POST') {
            return $this->respond([
                'message' => 'failure',
                'description' => 'Method not allowed'
            ], 405);
        }
        
        if (stripos($this->request->getHeaderLine('Content-Type'), 'application/x-www-form-urlencoded') === false) {
            return $this->respond([
                'message' => 'failure',
                'description' => 'Content-Type must be application/x-www-form-urlencoded'
            ], 415);
        }
        
        $clientId = $this->request->getPost('client_id');
        $clientSecret = $this->request->getPost('client_secret');
        
        /** @var NapasConfig $napasConfig */
        $napasConfig = config(\Config\NapasConfig::class);

        if (!$clientId || !$clientSecret || $clientId !== $napasConfig->notifyAuthClientId || $clientSecret !== $napasConfig->notifyAuthClientSecret) {
            return $this->respond([
                'message' => 'failure',
                'description' => 'Invalid client ID/client secret'
            ], 400);
        }
        
        $ttl = $this->napasConfig->notifyTokenTtl;

        $payload = [
            'iss' => base_url(),
            'aud' => base_url('coreapi/apg'),
            'iat' => time(),
            'exp' => time() + $ttl,
            'client_id' => $clientId
        ];

        $header = [
            'alg' => 'HS256',
            'typ' => 'JWT'
        ];

        $base64UrlHeader = rtrim(strtr(base64_encode(json_encode($header)), '+/', '-_'), '=');
        $base64UrlPayload = rtrim(strtr(base64_encode(json_encode($payload)), '+/', '-_'), '=');

        $signature = hash_hmac('sha256', $base64UrlHeader . "." . $base64UrlPayload, $clientSecret, true);
        $base64UrlSignature = rtrim(strtr(base64_encode($signature), '+/', '-_'), '=');

        $jwt = $base64UrlHeader . "." . $base64UrlPayload . "." . $base64UrlSignature;

        return $this->respond([
            'token_type' => 'Bearer',
            'access_token' => $jwt,
            'expires_in' => $ttl
        ]);
    }
    
    public function notification()
    {
        if ($this->request->getMethod(true) !== 'POST') {
            return $this->respond([
                'code' => '405',
                'message' => 'Method not allowed'
            ], 405);
        }

        log_message('error', '[NAPAS_NOTIFICATION] BODY: ' . json_encode($this->request->getJSON()));
        
        if (!$this->authorizeToken()) {
            return $this->respond([
                'code' => '401',
                'message' => 'Token invalid or expired'
            ], 401);
        }
        
        if (!$this->validateSignature()) {
            return $this->respond([
                'code' => '300',
                'message' => 'Signature is invalid'
            ], 400);
        }

        $header = $this->request->getJsonVar('header', true);
        $payload = $this->request->getJsonVar('payload', true);
        
        $headerRules = [
            'messageIdentifier' => 'required|string',
            'senderReference' => 'required|string',
            'creationDateTime' => 'required|valid_date[Y-m-d\TH:i:sP]',
            'senderId' => 'required|numeric|exact_length[6]',
            'receiverId' => 'required|numeric|exact_length[6]',
            'signature' => 'required|string',
        ];

        $rules = [
            'status' => 'required|alpha',
            'amount' => 'required|numeric',
            'transDateTime' => 'required|valid_date[Y-m-d\TH:i:sP]',
            'id' => 'required|string',
            'issueBank' => 'required|numeric|exact_length[6]',
            'beneficiaryBank' => 'required|numeric|exact_length[6]',
            'realMerchantAccount' => 'required|string',
            'mcc' => 'required|numeric|exact_length[4]',
            'sourceAccount' => 'required|string',
            'systemTrace' => 'required|string',
            'localTime' => 'required|string|exact_length[6]',
            'localDate' => 'required|string|exact_length[4]',
            'terminalId' => 'required|string',
            'refId' => 'required|string',
            'caseId' => 'required|string',
            'creationDateTime' => 'required|valid_date[Y-m-d\TH:i:sP]',
        ];
        
        if (!$this->validateData($header, $headerRules)) {
            log_message('error', sprintf('[Apg->notification] Invalid header format: %s | Raw header: %s', json_encode($this->validator->getErrors()), json_encode($header)));
            return $this->respond([
                'code' => '400',
                'message' => 'Invalid header format'
            ], 400);
        }

        if (!$this->validateData($payload, $rules)) {
            log_message('error', sprintf('[Apg->notification] Invalid payload format: %s | Raw payload: %s', json_encode($this->validator->getErrors()), $this->request->getBody()));
            return $this->respond([
                'code' => '400',
                'message' => 'Invalid payload format'
            ], 400);
        }

        if ($payload['status'] !== 'ACSP') {
            log_message('error', sprintf('[Apg->notification] Transaction failed with status: %s | Raw payload: %s', $payload['status'], $this->request->getBody()));
            return $this->respond([
                'code' => '400',
                'message' => 'Transaction failed with status: ' . $payload['status']
            ], 400);
        }

        $pgProfileModel = model(PgProfileModel::class);
        $pgOrderModel = model(PgOrderModel::class);
        $companyModel = model(CompanyModel::class);

        try {
            $orderId = ltrim(substr($payload['id'], -7), '0');

            if (!preg_match('/^[A-Z0-9]{7}$/', $orderId)) {
                throw new \Exception('Invalid order ID format');
            }
        } catch (\Throwable $e) {
            log_message('error', sprintf('[Apg->notification] Error extracting order ID: %s | Raw payload: %s', $e->getMessage(), $this->request->getBody()));
            return $this->respond([
                'code' => '400',
                'message' => 'Invalid order ID format'
            ], 400);
        }

        $vac = str_replace($orderId, '', $payload['id']);

        $pgProfile = $pgProfileModel
            ->join('tb_autopay_pg_merchant', 'tb_autopay_pg_profile.id=tb_autopay_pg_merchant.pg_profile_id')
            ->join('tb_autopay_pg_napas_vietqr_profile', 'tb_autopay_pg_profile.id=tb_autopay_pg_napas_vietqr_profile.pg_profile_id')
            ->where('tb_autopay_pg_merchant.active', 1)
            ->where('tb_autopay_pg_profile.active', 1)
            ->where('tb_autopay_pg_napas_vietqr_profile.vac', $vac)
            ->first();

        if (!$pgProfile) {
            log_message('error', sprintf('[Apg->notification] NAPAS_VIETQR profile not found for VAC: %s | Raw payload: %s', $vac, $this->request->getBody()));
            return $this->respond([
                'code' => '404',
                'message' => 'Merchant not found'
            ], 404);
        }

        $pgOrder = $pgOrderModel
            ->where('order_id', $orderId)
            ->where('order_status', 'AUTHENTICATION_NOT_NEEDED')
            ->where('order_amount', $payload['amount'])
            ->first();

        if (!$pgOrder) {
            log_message('error', sprintf('[Apg->notification] Order not found for order ID: %s | Raw payload: %s', $orderId, $this->request->getBody()));
            return $this->respond([
                'code' => '404',
                'message' => 'Order not found'
            ], 404);
        }
        

        $company = $companyModel->select(['*'])
            ->join('tb_autopay_company_subscription', 'tb_autopay_company.id=tb_autopay_company_subscription.company_id')
            ->where([
                'tb_autopay_company_subscription.status' => 'Active',
                'tb_autopay_company.status' => 'Active', 
                'tb_autopay_company.active' => 1
            ])->first();

        if (is_object($company)) {
            $napasVietQrProfile = new NapasVietQrProfile($pgProfile->id);
            $napasVietQrProfile->pushIpn('ORDER_PAID', $pgOrder->id, $payload['id']);
        }

        return $this->respond([
            'code' => 'success',
            'message' => 'Message is successful'
        ]);
    }
    
    public function reconciliation()
    {
        if ($this->request->getMethod(true) !== 'POST') {
            return $this->respond([
                'code' => '405',
                'message' => 'Method not allowed'
            ], 405);
        }
   
        log_message('error', '[NAPAS_RECONCILIATION] BODY: ' . json_encode($this->request->getJSON()));

        if (!$this->authorizeToken()) {
            return $this->respond([
                'code' => '401',
                'message' => 'Token invalid or expired'
            ], 401);
        }
        
        if (!$this->validateSignature()) {
            return $this->respond([
                'code' => '300',
                'message' => 'Signature is invalid'
            ], 400);
        }
        
        return $this->respond([
            'code' => 'success',
            'message' => 'Message is successfully'
        ], 200);
    }
    
    protected function authorizeToken(): bool
    {
        $authHeader = $this->request->getHeaderLine('Authorization');
        if (!$authHeader || strpos($authHeader, 'Bearer ') !== 0) {
            return false;
        }

        $jwt = substr($authHeader, 7);
        $parts = explode('.', $jwt);

        if (count($parts) !== 3) {
            return false;
        }

        [$base64UrlHeader, $base64UrlPayload, $base64UrlSignature] = $parts;

        $payload = json_decode(base64_decode(strtr($base64UrlPayload, '-_', '+/')), true);

        if (!$payload || !isset($payload['exp'], $payload['client_id']) || time() > $payload['exp']) {
            return false;
        }

        if ($payload['client_id'] !== $this->napasConfig->notifyAuthClientId) {
            return false;
        }

        $signature = hash_hmac('sha256', $base64UrlHeader . "." . $base64UrlPayload, $this->napasConfig->notifyAuthClientSecret, true);
        $expectedSignature = rtrim(strtr(base64_encode($signature), '+/', '-_'), '=');

        return hash_equals($expectedSignature, $base64UrlSignature);
    }
    
    protected function validateSignature(): bool
    {
        $signatureHeader = $this->request->getJsonVar('header')->signature ?? null;
        $publicKey = file_get_contents($this->napasConfig->notifyNapasCertPath);
        $data = json_encode($this->request->getJsonVar('payload'), JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);

        if (!$signatureHeader || !$publicKey || !$data) {
            return false;
        }
        
        try {
            $rsa = openssl_pkey_get_public($publicKey);

            if (!$rsa) {
                return false;
            }

            $isValid = openssl_verify($data, base64_decode($signatureHeader), $rsa, OPENSSL_ALGO_SHA256);

            return $isValid === 1;
        } catch (\Exception $e) {
            return false;
        }
    }
}
