<?php

namespace App\Controllers\Coreapi;

use App\Actions\GoogleSheets\ExecuteGoogleSheetsWebhooksAction;
use App\Actions\Merchant\PushMerchantTransactionNotificationAction;
use App\Actions\Mobile\PushMobileTransactionNotificationQueueAction;
use App\Actions\OutputDevice\ExecuteOutputDevice;
use App\Actions\PayCodeDetector;
use App\Actions\Shopify\ExecuteShopifyWebhooksAction;
use App\Actions\Transaction\CreateTransactionQueueAction;
use App\Controllers\BaseController;
use App\Exceptions\PaymentGatewayException;
use App\Features\PaymentGateway\MpgsProfile;
use App\Features\PaymentGateway\PaymentGatewayFeature;
use App\Models\BankAccountCashflowModel;
use App\Models\BankAccountModel;
use App\Models\BankModel;
use App\Models\CompanyModel;
use App\Models\CounterModel;
use App\Models\CybersourceNotificationModel;
use App\Models\CybersourceProfileModel;
use App\Models\HaravanModel;
use App\Models\NotificationLarkMessengerModel;
use App\Models\NotificationTelegramModel;
use App\Models\SapoModel;
use App\Models\SmsParserModel;
use App\Models\TransactionsModel;
use App\Models\UserModel;
use App\Models\WebhooksModel;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\Controller;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\Response;
use CodeIgniter\HTTP\ResponseInterface;
use CodeIgniter\RESTful\ResourceController;
use PhpAmqpLib\Message\AMQPMessage;
use Psr\Log\LoggerInterface;

/**
 * @property IncomingRequest $request
 */
class Mpgs extends Controller
{
    use ResponseTrait;

    protected PaymentGatewayFeature $paymentGatewayFeature;

    protected ?object $merchant = null;
    
    /**
     * @return Response|void
     */
    public function initController(RequestInterface $request, ResponseInterface $response, LoggerInterface $logger)
    {
        parent::initController($request, $response, $logger);
        
        $this->paymentGatewayFeature = new PaymentGatewayFeature;

        try {
            $this->merchant = $this->getMerchant();
        } catch (PaymentGatewayException $e) {
            $this->merchant = null;
        }
    }
    
    public function confirmOrder(): Response
    {
        if (! $this->merchant) {
            return $this->failNotFound();
        }
        
        $jsonBody = $this->request->getJSON();
        
        $rules = [
            'order_id' => ['required'],
        ];
        
        if (! $this->validateData((array) $jsonBody, $rules)) {
            return $this->fail($this->validator->getErrors());
        }
        
        $orderId = trim($jsonBody->order_id);
        
        try {
            /** @var MpgsProfile $profile */
            $profile = $this->paymentGatewayFeature->getAvailablePaymentGatewayProfile((int) $this->merchant->id, MpgsProfile::class);
            
            if (!$profile) {
                return $this->failNotFound();
            }
            
            $order = $profile->retrieveOrder($orderId);
            
            if (!$order) {
                return $this->fail(['message' => 'Order not found'], 404);
            }
            
            $transactions = $order->transaction ?? [];
            
            $paymentTransaction = array_values(array_filter(
                $transactions, 
                fn ($transaction) => $transaction->transaction->type == 'PAYMENT' 
            ))[0] ?? null;
            
            $isPaidOrder = $paymentTransaction
            && $order->totalAuthorizedAmount === $order->totalCapturedAmount
            && $order->totalCapturedAmount > 0
            && $paymentTransaction->transaction->amount >= $order->totalCapturedAmount;
            
            if (!$isPaidOrder) {
                return $this->fail(['message' => 'Order not paid']);
            }
            
            $bankAccountDetails = model(BankAccountModel::class)
                ->where('id', $profile->profile->bank_account_id)->where('active', 1)
                ->where('bank_pg', 1)
                ->where('bank_pg_connected', 1)
                ->first();
            
            if (!$bankAccountDetails) {
                return $this->fail(['message' => 'Acquirer bank account not found'], 503);
            }
            
            $bankDetails = model(BankModel::class)->where('id', $bankAccountDetails->bank_id)->first();
            
            $code = null;
            
            if (is_object($bankAccountDetails)) {
                $code = PayCodeDetector::getCode($order->id, $bankAccountDetails->company_id);
            }
            
            if (slavable_model(TransactionsModel::class, 'Coreapi')->where([
                'reference_number' => $paymentTransaction->transaction->id,
                'bank_account_id' => $bankAccountDetails->id,
                'gateway' => $bankDetails->brand_name,
                'source' => 'Mpgs',
            ])->first()) {
                return $this->respond(['message' => 'Order paid']);
            }
            
            $safeTransactionData = [
                'sms_id' => 0,
                'gateway' => $bankDetails->brand_name,
                'transaction_date' => date('Y-m-d H:i:s', strtotime($paymentTransaction->timeOfRecord)),
                'account_number' => $bankAccountDetails->account_number,
                'sub_account' => null,
                'amount_out' => 0,
                'amount_in' => 	$paymentTransaction->transaction->amount,
                'accumulated' => 0,
                'code' => $code,
                'body' => 'Thanh toan qua cong thanh toan',
                'transaction_content' => 'Thanh toan qua cong thanh toan',
                'reference_number' => $paymentTransaction->transaction->id,
                'source' => 'Mpgs',
                'parser_status' => 'Success',
                'currency' => $paymentTransaction->transaction->currency,
                'bank_account_id' => $bankAccountDetails->id,
                'merchant_id' => null,
                'transaction_id' => uuid()
            ];
            
            if ($paymentTransaction->sourceOfFunds->type == 'CARD') {
                $safeTransactionData['from_bin'] = $paymentTransaction->sourceOfFunds->provided->card->brand;
                $safeTransactionData['from_account_name'] = $paymentTransaction->sourceOfFunds->provided->card->nameOnCard;
                $safeTransactionData['from_account_number'] = $paymentTransaction->sourceOfFunds->provided->card->number;
            }
            
            $this->handleIncomingTransaction($bankAccountDetails, $safeTransactionData);
            
            return $this->respond(['message' => 'Order paid']);
        } catch (\Exception $e) {
            if ($e instanceof PaymentGatewayException) {
                $e->forceLogging();
            } else {
                log_message('error', '[MPGS_CONFIRM_ORDER] ' . $e->getMessage() . ' | ' . $e->getTraceAsString());
            }
            
            return $this->failServerError('Đã có lỗi xảy ra, vui lòng liên hệ với chúng tôi', $e->getCode());
        } 
    }
    
    protected function handleIncomingTransaction($bankAccountDetails, $safeTransactionData)
    {
        $smsParserModel = model(SmsParserModel::class);
        $companyModel = model(CompanyModel::class);
        $bankAccountModel = model(BankAccountModel::class);
        
        $queueTransactionId = null;
        $transactionId = null; // primary id of tb_autopay_sms_parsed table
        $transactionDetails = null;

        $mpgsConfig = config(\Config\MpgsConfig::class);
        $merchantConfig = config(\Config\Merchant::class);
        
        if ($bankAccountDetails->merchant_id && $merchantConfig->enabledTransactionInsertionOffload) {
            $queueTransactionId = CreateTransactionQueueAction::run((object) $safeTransactionData, $bankAccountDetails);
        }

        if (!$queueTransactionId) {
            $transactionId = $smsParserModel->insert($safeTransactionData);
            $transactionDetails = $smsParserModel->find($transactionId);
        }

        if (!$transactionId && !$queueTransactionId) {
            log_message('error', '[MPGS_IPN] Failed to create transaction');
            throw new \Exception('Failed to create transaction', 503);
        }

        if (is_object($transactionDetails) && is_numeric($transactionDetails->account_number) && $bankAccountDetails->merchant_id && !$queueTransactionId) {
            try {
                PushMerchantTransactionNotificationAction::run(
                    $bankAccountDetails, 
                    $transactionDetails,
                    $merchantConfig->enabledPushTransactionNotificationOffload
                );
            } catch (\Exception $e) {
                log_message('error', '[MPGS_IPN] PushMerchantTransactionNotificationAction failed: ' . $e->getMessage() . ' - ' . $e->getTraceAsString());
            }
        }

        if (is_object($transactionDetails) && is_numeric($transactionDetails->account_number) && !$bankAccountDetails->merchant_id) {
            try {
                PushMobileTransactionNotificationQueueAction::run($transactionDetails, $bankAccountDetails, null);
            } catch (\Exception $e) {
                log_message('error', '[MPGS_IPN] PushMobileTransactionNotificationQueueAction failed: ' . $e->getMessage() . ' - ' . $e->getTraceAsString() . ' - ' . json_encode($transactionDetails));
            }
        }
        
        $companyCheck = $companyModel->select("tb_autopay_company.id")
            ->join("tb_autopay_company_subscription", "tb_autopay_company.id=tb_autopay_company_subscription.company_id")
            ->join("tb_autopay_bank_account", "tb_autopay_bank_account.company_id=tb_autopay_company.id")
            ->where([
                'tb_autopay_bank_account.id' => $bankAccountDetails->id, 
                'tb_autopay_company_subscription.status' => 'Active',
                'tb_autopay_company.status' => 'Active', 'tb_autopay_company.active' => 1
            ])->get()->getResult();
        
        if (count($companyCheck) == 1) {
            $webhookQueuable = $mpgsConfig->notifyEnabledWebhookOffload ?? false;
            $forceWebhookManually = ['webhook' => false, 'sapo' => false, 'haravan' => false, 'shopify' => false, 'googlesheet' => false,'output_device' => false];
            $rabbitmq = null;

            if ($webhookQueuable) {
                $rabbitmq = new \App\Libraries\RabbitMQClient;
                $webhookQueuable = $rabbitmq->connect();
            }

            if ($rabbitmq && $webhookQueuable) {
                $msg = new AMQPMessage(
                    json_encode(['account_number' => $transactionDetails->account_number, 'parser_id' => $transactionId]),
                    array('delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT)
                );

                // webhooks
                try {
                    $rabbitmq->queueDeclare('webhook');
                    $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('webhook', 'name'));
                } catch (\Exception $e) {
                    log_message('error', '[Cybersource IPN] Webhook queue failed: ' . $e->getMessage());
                    $forceWebhookManually['webhook'] = true;
                }
                
                // sapo
                try {
                    $rabbitmq->queueDeclare('sapo');
                    $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('sapo', 'name'));
                } catch (\Exception $e) {
                    log_message('error', '[Cybersource IPN] Sapo queue failed: ' . $e->getMessage());
                    $forceWebhookManually['sapo'] = true;
                }

                // haravan
                try {
                    $rabbitmq->queueDeclare('haravan');
                    $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('haravan', 'name'));
                } catch (\Exception $e) {
                    log_message('error', '[Cybersource IPN] Haravan queue failed: ' . $e->getMessage());
                    $forceWebhookManually['haravan'] = true;
                } 

                // shopify
                try {
                    $rabbitmq->queueDeclare('shopify');
                    $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('shopify', 'name'));
                } catch (\Exception $e) {
                    log_message('error', '[Cybersource IPN] Shopify queue failed: ' . $e->getMessage());
                    $forceWebhookManually['shopify'] = true;
                }

                // googlesheet
                try {
                    $rabbitmq->queueDeclare('googlesheet');
                    $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('googlesheet', 'name'));
                } catch (\Exception $e) {
                    log_message('error', '[Cybersource IPN] GoogleSheet queue failed: ' . $e->getMessage());
                    $forceWebhookManually['googlesheet'] = true;
                } 

                // output device
                try {
                    $rabbitmq->queueDeclare('output_device');
                    $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('output_device', 'name'));
                } catch (\Exception $e) {
                    log_message('error', '[Cybersource IPN] output device queue failed: ' . $e->getMessage());
                    $forceWebhookManually['output_device'] = true;
                } 

                $rabbitmq->close();
            } 
            
            if (!$webhookQueuable || $forceWebhookManually['webhook']) {
                // webhooks
                $webhooksModel = model(WebhooksModel::class);
                $webhooksModel->doWebhooks($transactionDetails->account_number, $transactionId);
            }

            if (!$webhookQueuable || $forceWebhookManually['sapo']) {
                // sapo
                $sapoModel = model(SapoModel::class);
                $sapoModel->doWebhooks($transactionDetails->account_number, $transactionId);
            }

            if (!$webhookQueuable || $forceWebhookManually['haravan']) {
                // haravan
                $haravanModel = model(HaravanModel::class);
                $haravanModel->doWebhooks($transactionDetails->account_number, $transactionId);
            }

            if (!$webhookQueuable || $forceWebhookManually['shopify']) {
                // shopify
                ExecuteShopifyWebhooksAction::run($transactionId);
            }

            if (!$webhookQueuable || $forceWebhookManually['googlesheet']) {
                // googlesheet
                ExecuteGoogleSheetsWebhooksAction::run($transactionId);
            }
            
            if (!$webhookQueuable || $forceWebhookManually['webhook']) {
                // telegram queue
                $notificationTelegramModel = model(NotificationTelegramModel::class);
                $notificationTelegramModel->checkAndAddQueue($transactionId);
                
                // lark messenger queue
                $notificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);
                $notificationLarkMessengerModel->checkAndAddQueue($transactionId);
            }

             // output device
             if (!$webhookQueuable || $forceWebhookManually['output_device']) {
                // output device queue
                ExecuteOutputDevice::run($transactionId,0);
            }

            //counter
            $counterModel = model(CounterModel::class);
            $counterModel->transaction($companyCheck[0]->id, false, $transactionDetails->amount_in, $transactionDetails->amount_out);

            // cashflow
            $bankAccountCashFlowModel = model(BankAccountCashflowModel::class);
            $bankAccountCashFlowModel->amount_update($companyCheck[0]->id, date('Y-m-d', strtotime($transactionDetails->transaction_date)), $transactionDetails->bank_account_id, $transactionDetails->amount_in, $transactionDetails->amount_out, $transactionDetails->sub_account, $transactionDetails->accumulated);

            $lastTransaction = $transactionDetails;

            if (is_object($lastTransaction))
                $bankAccountModel
                    ->set(['last_transaction' => $lastTransaction->transaction_date, 'accumulated' => $lastTransaction->accumulated])
                    ->where(['id' => $bankAccountDetails->id])
                    ->update();
        }
    }
    
    protected function handleStorePaymentCard(MpgsProfile $profile, string $sessionId)
    {
        $profile->setSessionId($sessionId);
        $profile->storePaymentCard();
    }
    
    /** 
     * @throws PaymentGatewayException
     */
    protected function getMerchant(): object
    {
        $merchantId = trim(xss_clean($this->request->getGet('merchant')));
        
        $merchant = $this->paymentGatewayFeature->getPaymentGatewayMerchantById($merchantId);
        
        if (!$merchant) {
            throw new PaymentGatewayException('Merchant not found', 404, $merchantId);
        }
        
        if (!$merchant->active) {
            throw new PaymentGatewayException('Merchant is not active', 404, $merchantId);
        }
        
        return $merchant;
    }
    
    protected function logConfirmOrder(string $orderId, string $message): void
    {
        log_message('info', sprintf('[MPGS_CONFIRM_ORDER#%s] %s', $orderId, $message));
    }
}