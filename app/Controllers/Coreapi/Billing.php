<?php

namespace App\Controllers\Coreapi;
use App\Models\SmsParserModel;
use App\Models\HaravanModel;
use App\Models\SapoModel;

use CodeIgniter\RESTful\ResourceController;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\Controller;

class Billing extends ResourceController
{
    use ResponseTrait;
 
    public function addPayment() {

        helper('general');
        $billingConfig = config(\App\Config\Billing::class);
        $request = \Config\Services::request();

        $client_ip = $request->getIPAddress();

        if(!in_array($client_ip, ['*************', '**************','**************']))
            return $this->failNotFound();
        
        if ($this->request->getMethod(true) != 'POST')
            return $this->failNotFound();

        $stransactionModel = model(StransactionModel::class);
        $invoiceModel = model(InvoiceModel::class);
        $orderModel = model(OrderModel::class);
        $companySubscriptionModel = model(CompanySubscriptionModel::class);

        $validation =  \Config\Services::validation();
 
        if(! $this->validate(
            [
                'gateway'=>'required',
                'transactionDate'=>'required',
                'accountNumber'=>'required',
                'transferAmount' => 'required|integer',
                'code'=>'required',
                'content'=>'required',
                'transferType' =>'required|in_list[in,out]',
                'description'=>'required',
            ]
        ))
            return $this->failValidationError(implode(". ", $validation->getErrors()));

        // Kiểm tra có hóa đơn tương ứng với code không
        $data_invoice = [
            "code" => $this->request->getVar('code'),
            "total" => $this->request->getVar('transferAmount'),
            "type" => $this->request->getVar('transferType'),
            "description" => $this->request->getVar('description'),
        ];
 
        if($data_invoice['type'] != 'in')
            return $this->fail("Transfer type must be in", 400);


        $invoice_id = str_replace("SEP", "",$data_invoice['code']);
        $invoice_id = intval($invoice_id);
        

        if(!is_numeric($invoice_id) || $invoice_id == 0)
            return $this->fail("Cannot find invoice id with code " . $data_invoice['code'], 400);

        $invoice_details = $invoiceModel->where(['id' => $invoice_id])->get()->getRow();

        if(!is_object($invoice_details))
            return $this->fail("Cannot find invoice id with id " . $invoice_id, 400);

        $company_id = $invoice_details->company_id;

        if($invoice_details->status != "Unpaid")
            return $this->fail("Invoice status needs to be unpaid", 400);
        

        if($invoice_details->total != $data_invoice['total'])
            return $this->fail("Invoice amount is different from the transfer amount", 400);

        // Thêm transaction
        $data_transaction = [
            "invoice_id" => $invoice_id,
            "company_id" => $company_id,
            "date" => $this->request->getVar('transactionDate'),
            "description" => $this->request->getVar('description'),
            "in" => $this->request->getVar('transferAmount'),
            "type" => $this->request->getVar('type'),
        ];
        $transaction_id =  $stransactionModel->insert($data_transaction);

        if(!is_numeric($transaction_id)) {
            return $this->fail("Cannot insert stransaction", 400);
        }
        // Cập nhật trạng thái hóa đơn
        $invoiceModel->set(['status' => 'Paid', 'datepaid' => $data_transaction['date']])->where(['id' => $invoice_id])->update();

        $message = "Invoice ID: " . $invoice_id;

        // Nếu hóa đơn liên quan đến order: cập nhật trạng thái order
        $order_details = $orderModel->where(['invoice_id' => $invoice_id, "company_id" => $invoice_details->company_id])->get()->getRow();

        if(is_object($order_details)) {

            $message = $message . " Order ID: " . $order_details->id;

            // Nếu order liên quan đến subscription: cập nhật trạng thái subscription
            $companySubscriptionModel = model(CompanySubscriptionModel::class);
            $companySubscriptionChangeModel = model(CompanySubscriptionChangeModel::class);
            $companyModel = model(CompanyModel::class);

            $subscription_details = $companySubscriptionModel->where(["company_id" => $invoice_details->company_id])->get()->getRow();
            $subscriptionChangeDetails = $companySubscriptionChangeModel->where(['order_id' => $order_details->id,"company_id" => $invoice_details->company_id])->get()->getRow();

            if (is_object($subscription_details) && is_object($subscriptionChangeDetails)) {
                $companySubscriptionModel->set([
                    'status' => 'Active',
                    'order_id' => $order_details->id,
                    'plan_id' => $subscriptionChangeDetails->plan_id,
                    'begin_date' => $subscriptionChangeDetails->begin_date,
                    'end_date' => $subscriptionChangeDetails->end_date,
                    'first_payment' => $subscriptionChangeDetails->first_payment,
                    'recurring_payment' => $subscriptionChangeDetails->recurring_payment,
                    'billing_cycle' => $subscriptionChangeDetails->billing_cycle,
                    'monthly_transaction_limit' => $subscriptionChangeDetails->monthly_transaction_limit,
                    'bank_account_limit' => $subscriptionChangeDetails->bank_account_limit,
                    'telegram_intergration_limit' => $subscriptionChangeDetails->telegram_intergration_limit,
                    'webhook_intergration_limit' => $subscriptionChangeDetails->webhook_intergration_limit,
                    'monthly_telegram_send_limit' => $subscriptionChangeDetails->monthly_telegram_send_limit,
                    'monthly_webhooks_send_limit' => $subscriptionChangeDetails->monthly_webhooks_send_limit
                ])->where(['id' => $subscription_details->id])->update();
                $companySubscriptionChangeModel->where(['id' => $subscriptionChangeDetails->id])->delete();
                $companyModel->set(['status' => 'Active'])->where(['id' => $invoice_details->company_id])->update();
                $orderModel->set(['status' => 'Active'])->where(['id' => $order_details->id])->update();

                $productModel = model(ProductModel::class);
                $productDetails = $productModel->where(['id' => $subscriptionChangeDetails->plan_id])->get()->getRow();

                $simModel = model(SimModel::class);
                $simCompanyModel = model(SimCompanyModel::class);

                $companyDetails = $companyModel->where(['id' => $invoice_details->company_id])->get()->getRow();
                $userModel = model(\App\Models\UserModel::class);
                $userDetails = $userModel->join('tb_autopay_company_user', 'tb_autopay_company_user.user_id=tb_autopay_user.id')->where(['tb_autopay_company_user.company_id' => $companyDetails->id, 'role' => 'SuperAdmin'])->get()->getRow();

                if(is_object($productDetails) && $productDetails->dedicated_sim == 1) {
                    if ($billingConfig->enabledAutoSimConfigForSubscriptionChange) {
                        $sims = $simModel->select("tb_autopay_sim.id ,tb_autopay_sim.sim_phonenumber, tb_autopay_sim.description")->join("tb_autopay_sim_company","tb_autopay_sim_company.sim_id=tb_autopay_sim.id","left")->where("tb_autopay_sim_company.sim_id IS NULL AND tb_autopay_sim.is_shared=0 AND tb_autopay_sim.active=1")->get()->getResult();

                        $result = $simCompanyModel->where(['company_id' => $invoice_details->company_id])->get()->getRow();

                        if(!is_object($result) && isset($sims[0]) && is_object($sims[0])) {
                            $simCompanyModel->insert([
                                'company_id' => $company_id,
                                'sim_id' => $sims[0]->id
                            ]);

                            $message = $message . " Dedicated SIM ID: " . $sims[0]->id;
                        } else if (!is_object($result) && count($sims) == 0) {
                            notify_sim_config('assign', $userDetails, $companyDetails, $productDetails->name, $subscriptionChangeDetails->billing_cycle);
                        }
                    } else {
                        notify_sim_config('assign', $userDetails, $companyDetails, $productDetails->name, $subscriptionChangeDetails->billing_cycle);
                    }
                } else if (is_object($productDetails) && $productDetails->dedicated_sim != 1) {
                    if ($billingConfig->enabledAutoSimConfigForSubscriptionChange) {
                        $simCompanyModel->where(['company_id' => $invoice_details->company_id])->delete();
                    } else {
                        notify_sim_config('unassign', $userDetails, $companyDetails, $productDetails->name, $subscriptionChangeDetails->billing_cycle);
                    }
                }

                add_system_log(['company_id' => $companyDetails->id, 'data_type' => 'change_subscription_by_user', 'description' => "Change subscription by user from plan #{$subscription_details->plan_id} to #{$subscriptionChangeDetails->plan_id}",'level' => 'Info', 'by' => 'Billing Controller']);    
            } else if(is_object($subscription_details) && !is_object($subscriptionChangeDetails)) {
                $companySubscriptionModel->set(['status' => 'Active'])->where(['id' => $subscription_details->id])->update();
                
                $companyModel->set(['status' => 'Active'])->where(['id' => $invoice_details->company_id])->update();

                $orderModel->set(['status' => 'Active'])->where(['id' => $order_details->id])->update();

                $message = $message . " Subscription ID: " . $subscription_details->id;

                // dedicated sim
                // chỉ cấp sim nếu subscription_details chưa active
                $simModel = model(SimModel::class);
                $simCompanyModel = model(SimCompanyModel::class);

                $productModel = model(ProductModel::class);
                $product_details = $productModel->where(['id' => $subscription_details->plan_id])->get()->getRow();

                if(is_object($product_details) && $product_details->dedicated_sim == 1 && $subscription_details->status == 'Pending') {
                    $sims = $simModel->select("tb_autopay_sim.id ,tb_autopay_sim.sim_phonenumber, tb_autopay_sim.description")->join("tb_autopay_sim_company","tb_autopay_sim_company.sim_id=tb_autopay_sim.id","left")->where("tb_autopay_sim_company.sim_id IS NULL AND tb_autopay_sim.is_shared=0 AND tb_autopay_sim.active=1")->get()->getResult();

                    // Nếu đã cấp sim rồi (ví dụ cấp dùng thử) thì không cấp nữa
                    $result = $simCompanyModel->where(['company_id' => $invoice_details->company_id])->get()->getRow();

                    if(!is_object($result) && isset($sims[0]) && is_object($sims[0])) {
                        $simCompanyModel->insert([
                            'company_id' => $company_id,
                            'sim_id' => $sims[0]->id
                        ]);

                        $message = $message . " Dedicated SIM ID: " . $sims[0]->id;

                    }
                }

            }
        }
        
        $response = [
            'status'   => 201,
            'error'    => null,
            'success' => TRUE,
            'messages' => [
                'success' => 'Data Saved ' . $message
            ]
        ];
         
        return $this->respondCreated($response);

       
    }

    /*
    public function check_transaction_status() {

        $request = \Config\Services::request();

        $this->response->setHeader('access-control-allow-origin', '*');

        if ($this->request->getMethod(true) != 'POST')
            return $this->failNotFound();

        $model = new SmsParserModel();
        $haravanModel = new HaravanModel();
        $sapoModel = new SapoModel();

        $validation =  \Config\Services::validation();
 
        if(! $this->validate(
            [
            'account_number'=>'required|alpha_numeric|max_length[20]|min_length[3]',
            'amount'=>'required|integer|is_natural',
            'code'=>'required|alpha_numeric|max_length[20]|min_length[3]'
        ]))
            return $this->failValidationError(implode(". ", $validation->getErrors()));

        $account_number = $this->request->getVar('account_number');

        $result_haravan = $haravanModel->join('tb_autopay_bank_account','tb_autopay_bank_account.id=tb_autopay_haravan.bank_account_id')->where(['tb_autopay_bank_account.account_number' => $account_number])->get()->getRow();
        $result_sapo = $sapoModel->join('tb_autopay_bank_account','tb_autopay_bank_account.id=tb_autopay_sapo.bank_account_id')->where(['tb_autopay_bank_account.account_number' => $account_number])->get()->getRow();


        if(!$result_haravan && !$result_sapo)
            return $this->failNotFound();    

        $where = [
            'account_number' => $this->request->getVar('account_number'),
            'amount_in' => $this->request->getVar('amount'),
           // 'code' => $this->request->getVar('code')
        ];
        $code = $this->request->getVar('code');
        $result = $model->where($where)->like('transaction_content', $code)->get()->getRow();

        
        if(is_object($result)) {
            $data = [
                'status' => 200,
                'error' => null,
                'pay_status' => TRUE,
            ];
            return $this->respond($data, 200);
            
        } else {
            $data = [
                'status' => 200,
                'error' => null,
                'pay_status' => FALSE,
            ];
            return $this->respond($data, 200);
        }

       
    } */

}