<?php

namespace App\Controllers\Coreapi;

use App\Actions\BankAccount\FilterTransactionAction;
use Exception;
use App\Models\SapoModel;
use App\Models\CompanyModel;
use App\Models\CounterModel;
use App\Models\HaravanModel;
use App\Models\WebhooksModel;
use App\Models\SmsParserModel;
use App\Actions\PayCodeDetector;
use App\Models\BankAccountModel;
use CodeIgniter\API\ResponseTrait;
use App\Models\BankSubAccountModel;
use PhpAmqpLib\Message\AMQPMessage;
use App\Models\BankAccountCashflowModel;
use App\Models\NotificationTelegramModel;
use CodeIgniter\RESTful\ResourceController;
use App\Models\NotificationLarkMessengerModel;
use App\Actions\OutputDevice\ExecuteOutputDevice;
use App\Actions\Shopify\ExecuteShopifyWebhooksAction;
use App\Actions\Transaction\CreateTransactionQueueAction;
use App\Actions\GoogleSheets\ExecuteGoogleSheetsWebhooksAction;
use App\Actions\Merchant\PushMerchantTransactionNotificationAction;
use App\Actions\Mobile\PushMobileTransactionNotificationQueueAction;
use App\Models\VpbankBankAccountMetadataModel;
use App\Models\VpbankEnterpriseAccountModel;
use App\Models\VpbankOrgNotificationModel;
use App\Models\VpbankOrgNotificationRawModel;
use CodeIgniter\HTTP\Response;

class Vpbankorg extends ResourceController
{
    use ResponseTrait;

    const BANK_ID = 2;

    protected $helpers = ['general'];

    protected $debug;

    protected $authUsername;

    protected $authPassword;

    protected $vpbankCertPath;
    
    protected $vpbankRegisterCertPath;

    protected $webhookOffload;
    
    protected $enableFilterTransaction;

    public function __construct()
    {
        /** @var \Config\Vpbank $config */
        $config = config(\Config\Vpbank::class);

        $this->authUsername = $config->orgNotifyAuthUsername;
        $this->authPassword = $config->orgNotifyAuthPassword;
        $this->vpbankCertPath = $config->orgNotifyVpbankCertPath;
        $this->vpbankRegisterCertPath = $config->orgNotifyVpbankRegisterCertPath;
        $this->debug = $config->orgNotifyDebug ?? false;
        $this->webhookOffload = $config->orgNotifyWebhookOffload ?? false;
        $this->enableFilterTransaction = $config->enableFilterTransaction ?? false;
    }
    
    public function notifyTransaction(): Response         
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }

        $signature = $this->request->getHeaderLine('Signature');
        $authorizationToken = trim(str_replace('Basic', '', $this->request->getHeaderLine('Authorization')));

        $data = $this->request->getJSON(true);

        $rules = [
            'masterAccountNumber' => ['required', 'string'],
            'virtualAccountNumber' => ['permit_empty', 'string'],
            'virtualName' => ['permit_empty', 'string'],
            'virtualAlkey' => ['permit_empty', 'string'],
            'amount' => ['required', 'numeric'],
            'bookingDate' => ['required', 'regex_match[/^\d{4}\d{2}\d{2}$/]'],
            'transactionDate' => ['required', 'regex_match[/^\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2}$/]'],
            'transactionId' => ['required', 'string'],
            'currency' => ['permit_empty', 'string'],
            'remark' => ['permit_empty', 'string']
        ];

        if (! $this->validateData($data, $rules)) {
            log_message('error', '[VPBANK_ORG_IPN] Bad request: ' . json_encode($this->validator->getErrors()) . ' | Raw data: ' . json_encode($data));

            return $this->respondStandard(
                '400', 
                '4000', 
                sprintf('Trường thông tin bị thiếu hoặc không hợp lệ: %s', implode(' ', $this->validator->getErrors())), 
                $data['transactionId'] ?? null
            );
        }
        
        if (!$authorizationToken || $authorizationToken !== base64_encode(sprintf('%s:%s', $this->authUsername, $this->authPassword))) {
            return $this->respondStandard('401', '4010', 'Xác thực thất bại', $data['transactionId']);
        }

        $plainSignature = sprintf('%s%s%s%s', $data['transactionId'], $data['masterAccountNumber'], $data['amount'], $data['transactionDate']);

        $publicKey = openssl_pkey_get_public(file_get_contents($this->vpbankCertPath));
        
        $signatureIsValid = openssl_verify(base64_encode($plainSignature), base64_decode($signature), $publicKey, OPENSSL_ALGO_SHA256);
        
        if (!$signatureIsValid) {
            log_message('error', '[VPBANK_ORG_IPN] Signature invalid: ' . $signature . ' | Raw data: ' . json_encode($data));

            return $this->respondStandard('400', '4001', 'Chữ ký không hợp lệ', $data['transactionId']);
        }

        $vpbankOrgNotificationRawModel = model(VpbankOrgNotificationRawModel::class);
        $vpbankOrgNotificationModel = model(VpbankOrgNotificationModel::class);
        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);

        $rawNotificationId = $vpbankOrgNotificationRawModel->insert(['payload' => json_encode($data), 'ip' =>$this->request->getIPAddress()]);

        if (!$rawNotificationId) {
            log_message('error', '[VPBANK_ORG_IPN] Raw notification insert failed | Raw data: ' . json_encode($data));

            return $this->respondStandard('503', '5030', 'Dịch vụ chưa sẵn sàng', $data['transactionId']);
        }

        $duplicatedTransaction = $vpbankOrgNotificationModel->where(['transactionId' => $data['transactionId']])->countAllResults();
        if ($duplicatedTransaction) {
            log_message('error', '[VPBANK_ORG_IPN] Transaction duplicated | Raw data: ' . json_encode($data));
            
            return $this->respondStandard('409', '4090', 'Giao dịch bị trùng lặp', $data['transactionId']);
        }

        // Determine if bank account exist
        $bankAccountDetails = $bankAccountModel->where([
            'account_number' => $data['masterAccountNumber'],
            'bank_id' => Vpbank::BANK_ID,
            'active' => true,
        ])->first();

        if (!is_object($bankAccountDetails)) {
            log_message('error', '[VPBANK_ORG_IPN] Bank account not found | Raw data: ' . json_encode($data));

            return $this->respondStandard('404', '4040', 'Tài khoản Master không tồn tại ở hệ thống SePay', $data['transactionId']);
        }

        // Skip Bank API notification when switch to SMS Banking
        if ($bankAccountDetails->bank_sms) {
            if ($this->debug) {
                log_message('error', '[VPBANK_ORG_IPN] Skip notification | Raw data: ' . json_encode($data));
            }

            return $this->respondStandard('200', '0', 'Thành công', $data['transactionId']);
        }

        // Determine if bank sub account exist
        $bankSubAccountDetails = null;

        if ($data['virtualAccountNumber']) {
            $bankSubAccountDetails = $bankSubAccountModel->where([
                'sub_account' => $data['virtualAccountNumber'],
                'bank_account_id' => $bankAccountDetails->id,
                'va_active' => 1,
                'active' => 1,
            ])->first();
            
            if (!is_object($bankSubAccountDetails)) {
                log_message('error', '[VPBANK_ORG_IPN] Bank sub account not found | Raw data: ' . json_encode($data));
    
                return $this->respondStandard('404', '4040', 'Số VAN không tồn tại ở hệ thống SePay', $data['transactionId']);
            }
        }

        $notificationId = $vpbankOrgNotificationModel->insert([
            'raw_id' => $rawNotificationId,
            'masterAccountNumber' => $data['masterAccountNumber'],
            'virtualAccountNumber' => $data['virtualAccountNumber'] ?? null,
            'virtualName' => $data['virtualName'] ?? null,
            'virtualAlkey' => $data['virtualAlkey'] ?? null,
            'amount' => $data['amount'],
            'bookingDate' => $data['bookingDate'],
            'transactionDate' => $data['transactionDate'],
            'transactionId' => $data['transactionId'],
            'currency' => $data['currency'] ?? null,
            'remark' => $data['remark'] ?? null,
        ]);

        if (!$notificationId) {
            log_message('error', '[VPBANK_ORG_IPN] Notification insert failed | Raw data: ' . json_encode($data));

            return $this->respondStandard('503', '5030', 'Dịch vụ chưa sẵn sàng', $data['transactionId']);
        }

        if ($this->enableFilterTransaction && ! $bankAccountDetails->merchant_id) {
            $filterTransaction = FilterTransactionAction::forBankAccount($bankAccountDetails->id)
                ->amountIn(true)
                ->description($data['remark'])
                ->payload($data)
                ->filter();

            if ($filterTransaction['isRejected']) {
                return $this->respondStandard('200', '0', 'Thành công', $data['transactionId']);
            }
        }

        $smsParserModel = model(SmsParserModel::class);
        $companyModel = model(CompanyModel::class);

        $code = null;

        if (is_object($bankAccountDetails)) {
            $code = PayCodeDetector::getCode($data['remark'] ?? '', $bankAccountDetails->company_id);
        }

        $parserData = [
            'sms_id' => $notificationId,
            'reference_number' => $data['transactionId'],
            'gateway' => 'VPBank',
            'transaction_date' => date('Y-m-d H:i:s', strtotime($data['transactionDate'])),
            'account_number' => $bankAccountDetails->account_number,
            'sub_account' => $bankSubAccountDetails->sub_account ?? null,
            'accumulated' => 0,
            'code' => $code,
            'transaction_content' => $data['remark'],
            'source' => 'BankAPINotify',
            'body' => 'BankAPINotify ' . $data['remark'],
            'parser_status' => 'Success',
            'bank_account_id' => $bankAccountDetails->id,
            'merchant_id' => $bankAccountDetails->merchant_id,
            'transaction_id' => uuid()
        ];
        
        if ($data['amount'] > 0) {
            $parserData['amount_in'] = $data['amount'];
            $parserData['amount_out'] = 0;
        } else {
            $parserData['amount_in'] = 0;
            $parserData['amount_out'] = abs($data['amount']);
        }

        $queueTransactionId = null;
        $parserId = null; // primary id of tb_autopay_sms_parsed table
        $transactionDetails = null;

        $merchantConfig = config(\Config\Merchant::class);

        if ($bankAccountDetails->merchant_id && $merchantConfig->enabledTransactionInsertionOffload) {
            $queueTransactionId = CreateTransactionQueueAction::run((object) $parserData, $bankAccountDetails);
        }

        if (!$queueTransactionId) {
            $parserId = $smsParserModel->insert($parserData);
            $transactionDetails = $smsParserModel->find($parserId);
        }

        if (!$parserId && !$queueTransactionId) {
            $vpbankOrgNotificationModel->where('id', $notificationId)->delete();
            
            log_message('error', '[VPBANK_ORG_IPN] Transaction insert failed | Raw data: ' . json_encode($data));

            return $this->respondStandard('503', '5030', 'Dịch vụ chưa sẵn sàng', $data['transactionId']);
        }

        if (is_object($transactionDetails) && is_numeric($transactionDetails->account_number) && $bankAccountDetails->merchant_id && !$queueTransactionId) {
            try {
                PushMerchantTransactionNotificationAction::run(
                    $bankAccountDetails, 
                    $transactionDetails,
                    $merchantConfig->enabledPushTransactionNotificationOffload
                );
            } catch (Exception $e) {
                log_message('error', '[VPBANK_ORG_IPN] PushableMerchantTransactionNotificationAction: ' . $e->getMessage() . ' | ' . $e->getTraceAsString());
            }
        }

        if (is_object($transactionDetails) && is_numeric($transactionDetails->account_number) && !$bankAccountDetails->merchant_id) {
            try {
                PushMobileTransactionNotificationQueueAction::run($transactionDetails, $bankAccountDetails, $bankSubAccountDetails);
            } catch (Exception $e) {
                log_message('error', '[VPBANK_ORG_IPN] PushMobileTransactionNotificationQueueAction: ' . $e->getMessage() . ' | ' . $e->getTraceAsString());
            }
            
            $companyCheck = $companyModel->select("tb_autopay_company.id")
                ->join("tb_autopay_company_subscription", "tb_autopay_company.id=tb_autopay_company_subscription.company_id")
                ->join("tb_autopay_bank_account", "tb_autopay_bank_account.company_id=tb_autopay_company.id")
                ->where([
                    'tb_autopay_bank_account.id' => $bankAccountDetails->id, 
                    'tb_autopay_company_subscription.status' => 'Active',
                    'tb_autopay_company.status' => 'Active', 'tb_autopay_company.active' => 1
                ])->get()->getResult();
            
            if (count($companyCheck) == 1) {
                $webhookQueuable = $this->webhookOffload;
                $forceWebhookManually = ['webhook' => false, 'sapo' => false, 'haravan' => false, 'shopify' => false, 'googlesheet' => false, 'output_device' => false];
                $rabbitmq = null;

                if ($webhookQueuable) {
                    $rabbitmq = new \App\Libraries\RabbitMQClient;
                    $webhookQueuable = $rabbitmq->connect();
                }

                if ($rabbitmq && $webhookQueuable) {
                    $msg = new AMQPMessage(
                        json_encode(['account_number' => $transactionDetails->account_number, 'parser_id' => $parserId]),
                        array('delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT)
                    );

                    // webhooks
                    try {
                        $rabbitmq->queueDeclare('webhook');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('webhook', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', '[VPBANK_ORG_IPN] Webhook queue failed: ' . $e->getMessage());
                        $forceWebhookManually['webhook'] = true;
                    }
                    
                    // sapo
                    try {
                        $rabbitmq->queueDeclare('sapo');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('sapo', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', '[VPBANK_ORG_IPN] Sapo queue failed: ' . $e->getMessage());
                        $forceWebhookManually['sapo'] = true;
                    }

                    // haravan
                    try {
                        $rabbitmq->queueDeclare('haravan');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('haravan', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', '[VPBANK_ORG_IPN] Haravan queue failed: ' . $e->getMessage());
                        $forceWebhookManually['haravan'] = true;
                    } 

                    // shopify
                    try {
                        $rabbitmq->queueDeclare('shopify');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('shopify', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', '[VPBANK_ORG_IPN] Shopify queue failed: ' . $e->getMessage());
                        $forceWebhookManually['shopify'] = true;
                    }
                    
                    // googlesheet
                    try {
                        $rabbitmq->queueDeclare('googlesheet');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('googlesheet', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', '[VPBANK_ORG_IPN] GoogleSheet queue failed: ' . $e->getMessage());
                        $forceWebhookManually['googlesheet'] = true;
                    }

                    // output device
                    try {
                        $rabbitmq->queueDeclare('output_device');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('output_device', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', '[VPBANK_ORG_IPN] Output device queue failed: ' . $e->getMessage());
                        $forceWebhookManually['output_device'] = true;
                    }

                    $rabbitmq->close();
                } 
                
                if (!$webhookQueuable || $forceWebhookManually['webhook']) {
                    // webhooks
                    $webhooksModel = model(WebhooksModel::class);
                    $webhooksModel->doWebhooks($transactionDetails->account_number, $parserId);
                }

                if (!$webhookQueuable || $forceWebhookManually['sapo']) {
                    // sapo
                    $sapoModel = model(SapoModel::class);
                    $sapoModel->doWebhooks($transactionDetails->account_number, $parserId);
                }

                if (!$webhookQueuable || $forceWebhookManually['haravan']) {
                    // haravan
                    $haravanModel = model(HaravanModel::class);
                    $haravanModel->doWebhooks($transactionDetails->account_number, $parserId);
                }

                if (!$webhookQueuable || $forceWebhookManually['shopify']) {
                    // shopify
                    ExecuteShopifyWebhooksAction::run($parserId);
                }

                if (!$webhookQueuable || $forceWebhookManually['googlesheet']) {
                    // googlesheet
                    ExecuteGoogleSheetsWebhooksAction::run($parserId);
                }

                if (!$webhookQueuable || $forceWebhookManually['output_device']) {
                    // output device
                    ExecuteOutputDevice::run($parserId, 0);
                }
                
                if (!$webhookQueuable || $forceWebhookManually['webhook']) {
                    // telegram queue
                    $notificationTelegramModel = model(NotificationTelegramModel::class);
                    $notificationTelegramModel->checkAndAddQueue($parserId);
                    
                    // lark messenger queue
                    $notificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);
                    $notificationLarkMessengerModel->checkAndAddQueue($parserId);
                }

                //counter
                $counterModel = model(CounterModel::class);
                $counterModel->transaction($companyCheck[0]->id, false, $transactionDetails->amount_in, $transactionDetails->amount_out);

                // cashflow
                $bankAccountCashFlowModel = model(BankAccountCashflowModel::class);
                $bankAccountCashFlowModel->amount_update($companyCheck[0]->id, date('Y-m-d', strtotime($transactionDetails->transaction_date)), $transactionDetails->bank_account_id, $transactionDetails->amount_in, $transactionDetails->amount_out, $transactionDetails->sub_account, $transactionDetails->accumulated);

                $lastTransaction = $transactionDetails;

                if (is_object($lastTransaction)) {
                    $bankAccountModel->set([
                        'last_transaction' => $lastTransaction->transaction_date,
                        'accumulated' => $lastTransaction->accumulated
                    ])->where(['id' => $bankAccountDetails->id])->update();
                }
            }
        }

        return $this->respondStandard('200', '0', 'Thành công', $data['transactionId']);
    }
    
    public function registerCallback(): Response         
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }

        $data = $this->request->getJSON(true) ?? [];
        
        if ($this->debug) {
            log_message('error', '[VPBANK_ORG_REGISTER_CALLBACK] Payload: ' . json_encode($data));
        }

        $rules = [
            'data' => ['required'],
            'data.*.AccountNo' => ['required', 'string'],
            'data.*.TransactionId' => ['permit_empty', 'string'],
            'data.*.RequestType' => ['required', 'in_list[BOTH,CREDIT,DEBIT]'],
            'data.*.Cif' => ['required', 'string'],
            'data.*.Partner' => ['required', 'string'],
            'data.*.PartnerRefernceNumber' => ['required', 'string'],
            'data.*.T24Code' => ['permit_empty', 'string'],
            'data.*.T24Mesage' => ['permit_empty', 'string'],
            'data.*.AccountRegisterId' => ['required', 'string'],
            'sign' => ['required', 'string'],
        ];

        if (! $this->validateData($data, $rules)) {
            log_message('error', '[VPBANK_ORG_REGISTER_CALLBACK] Bad request: ' . json_encode($this->validator->getErrors()) . ' | Raw data: ' . json_encode($data));

            return $this->respondStandard(
                '400',
                '4000', 
                sprintf('Trường thông tin bị thiếu hoặc không hợp lệ: %s', implode(' ', $this->validator->getErrors())), 
            );
        }
 
        $signature = trim($data['sign']);
        $plainSignature = json_encode($data['data']);
        
        $publicKey = openssl_pkey_get_public(file_get_contents($this->vpbankRegisterCertPath));
        
        $signatureIsValid = openssl_verify($plainSignature, base64_decode($signature), $publicKey, OPENSSL_ALGO_SHA256);
        
        if (!$signatureIsValid) {
            log_message('error', '[VPBANK_ORG_REGISTER_CALLBACK] Signature invalid: ' . $signature);

            return $this->respondStandard('400', '4001', 'Chữ ký không hợp lệ');
        }
        
        $bankAccountModel = model(BankAccountModel::class);
        $vpbankEnterpriseAccountModel = model(VpbankEnterpriseAccountModel::class);
        $vpbankBankAccountMetadataModel = model(VpbankBankAccountMetadataModel::class);
        $errors = [];
        
        foreach ($data['data'] as $record) {
            if (!$record['AccountRegisterId']) {
                log_message('error', sprintf('[VPBANK_ORG_REGISTER_CALLBACK] AccountRegisterId is missing for account: %s | Raw body: %s', $record['AccountNo'], json_encode($data)));
                continue;
            }
            
            $bankAccount = $bankAccountModel
                ->where('bank_id', Vpbank::BANK_ID)
                ->where('account_number', $record['AccountNo'])
                ->where('bank_api', 1)
                ->first();
            
            if (!is_object($bankAccount)) {
                log_message('error', sprintf('[VPBANK_ORG_REGISTER_CALLBACK] Bank account not found: %s | Raw body', $record['AccountNo'], json_encode($data)));
                
                $errors[] = sprintf('Tài khoản ngân hàng không tồn tại trên SePay: %s', $record['AccountNo']);

                continue;
            }
            
            if ($bankAccount->bank_api_connected) {
                log_message('error', sprintf('[VPBANK_ORG_REGISTER_CALLBACK] Bank account already connected: %s | Raw body: %s', $record['AccountNo'], json_encode($data)));
                
                $errors[] = sprintf('Tài khoản ngân hàng đã liên kết trước đó: %s', $record['AccountNo']);

                continue;
            }
            
            $vpbankEnterpriseAccount = $vpbankEnterpriseAccountModel->where([
                'bank_account_id' => $bankAccount->id,
                'partner_reference_number' => $record['PartnerRefernceNumber'],
            ])->first();
            
            if (!is_object($vpbankEnterpriseAccount)) {
                log_message('error', sprintf('[VPBANK_ORG_REGISTER_CALLBACK] VPBank enterprise account not found: %s | Raw body: %s', $record['AccountNo'], json_encode($data)));
                
                $errors[] = sprintf('Yêu cầu không tồn tại trên SePay: %s', $record['AccountNo']);

                continue;
            }
            
            $vpbankBankAccountMetadataId = $vpbankBankAccountMetadataModel->insert([
                'bank_account_id' => $bankAccount->id,
                'register_id' => $record['AccountRegisterId'],
            ]);
            
            if (!$vpbankBankAccountMetadataId) {
                log_message('error', sprintf('[VPBANK_ORG_REGISTER_CALLBACK] VPBank bank account metadata insert failed: %s | Raw body: %s', $record['AccountNo'], json_encode($data)));
                
                $errors[] = sprintf('Không thể tạo thông tin cho tài khoản ngân hàng: %s', $record['AccountNo']);
            }
            
            $connected = $bankAccountModel->set([
                'bank_api_connected' => 1,
                'pending_api_connection' => 0,
            ])->where('id', $bankAccount->id)->update();
            
            if (!$connected) {
                log_message('error', sprintf('[VPBANK_ORG_REGISTER_CALLBACK] Bank account connection update failed: %s | Raw body: %s', $record->AccountNo, json_encode($data)));
                
                $errors[] = sprintf('Không thể cập nhật liên kết cho tài khoản ngân hàng: %s', $record['AccountNo']);
            }
        }

        if (count($errors)) {
            return $this->respondStandard('500', '5000', implode(', ', $errors));
        }
        
        return $this->respondStandard('200', '0', 'Thành công');
    }

    protected function respondStandard($status, $errorCode, $errorMessage, $transactionId = null): Response
    {
        return $this->respond([
            'status' => $status,
            'errorCode' => $errorCode,
            'errorMessage' => $errorMessage,
            'transactionId' => $transactionId,
        ], $status);
    }
}