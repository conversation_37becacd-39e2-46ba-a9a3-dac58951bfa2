<?php

namespace App\Controllers\Coreapi;

use App\Actions\BankAccount\FilterTransactionAction;
use App\Actions\PayCodeDetector;
use Exception;
use App\Models\SapoModel;
use CodeIgniter\Controller;
use App\Models\CompanyModel;
use App\Models\CounterModel;
use App\Models\HaravanModel;
use App\Models\MerchantModel;
use App\Models\WebhooksModel;
use App\Models\SmsParserModel;
use App\Models\BankAccountModel;
use App\Models\TransactionsModel;
use App\Models\ConfigurationModel;
use CodeIgniter\API\ResponseTrait;
use App\Models\BankSubAccountModel;
use PhpAmqpLib\Message\AMQPMessage;
use App\Models\MbbNotificationModel;
use CodeIgniter\HTTP\IncomingRequest;
use App\Models\MbbNotificationRawModel;
use App\Models\BankAccountCashflowModel;
use App\Models\MbbNotificationTokenModel;
use App\Models\NotificationTelegramModel;
use CodeIgniter\RESTful\ResourceController;
use App\Models\NotificationLarkMessengerModel;
use App\Models\NotificationTelegramQueueModel;
use App\Actions\Shopify\ExecuteShopifyWebhooksAction;
use App\Actions\GoogleSheets\ExecuteGoogleSheetsWebhooksAction;
use App\Actions\Transaction\CreateTransactionQueueAction;
use App\Actions\Merchant\PushMerchantTransactionNotificationAction;
use App\Actions\Mobile\PushMobileTransactionNotificationQueueAction;

use App\Actions\OutputDevice\ExecuteOutputDevice;
use Config\Mbb as MbbConfig;

class Mbb extends ResourceController
{
    use ResponseTrait;

    protected MbbConfig $config;

    public function __construct()
    {
        $this->config = config(MbbConfig::class);
        $this->config->notifyAuthType = $this->config->notifyAuthType ?? 'token';
        $this->config->notifyAuthStaticKey = $this->config->notifyAuthStaticKey ?? null;
        $this->config->notificationTokenTtl = $this->config->notificationTokenTtl ?? 60;
    }

    protected function authorize($request)
    {
        $token = explode('Bearer ', $request->getHeaderLine('Authorization'))[1] ?? '';

        if ($this->config->notifyAuthType === 'static_key' && $this->config->notifyAuthStaticKey && $token === $this->config->notifyAuthStaticKey) {
            return true;
        }

        $authorized = model(MbbNotificationTokenModel::class)->where([
            'token' => $token,
            'expires_at >' => date('Y-m-d H:i:s')
        ])->countAllResults() === 1;

        if (!$authorized) {
            $tokenCount = model(MbbNotificationTokenModel::class)->where(['token' => $token])->countAllResults();

            log_message('error', 'MBB Token Debug: ' . json_encode(['token' => $token, 'count' => $tokenCount]));
        }

        return $authorized;
    }

    public function generateToken()
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }

        $ttl = $this->config->notificationTokenTtl;

        $key = explode('Basic ', $this->request->getHeaderLine('Authorization'))[1] ?? '';

        if (!$key || $key !== base64_encode("{$this->config->notificationTokenUsername}:{$this->config->notificationTokenPassword}")) {
            return $this->respond([
                'code' => 401,
                'message' => 'Unauthenticated'
            ], 401); 
        }

        if ($this->config->notifyAuthType === 'static_key' && $this->config->notifyAuthStaticKey) {
            return $this->respond([
                'access_token' => $this->config->notifyAuthStaticKey,
                'token_type' => 'bearer',
                'expires_in' => $ttl,
            ]);
        }

        $token = md5($key . time() . bin2hex(random_bytes(7)));

        try {
            model(MbbNotificationTokenModel::class)->insert([
                'token' => $token,
                'ttl' => $ttl,
                'expires_at' => date('Y-m-d H:i:s', strtotime("+$ttl seconds"))
            ]);
        } catch (\Exception $e) {
            log_message('error', 'MBB Notification Error: Generate token failed ' . $e->getMessage());

            return $this->respond([
                'code' => 503,
                'message' => 'Service unvailable, please retry',
            ], 503);
        }

        return $this->respond([
            'access_token' => $token,
            'token_type' => 'bearer',
            'expires_in' => $ttl,
        ]);
    }
 
    public function notifyTransaction()
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }

        helper(['general']);
        
        $config = config(\App\Config\Mbb::class);
        $mbbMmsConfig = config(\App\Config\MbbMms::class);
        $config->notifyDebug = $config->notifyDebug ?? false; 
        $merchantConfig = config(\App\Config\Merchant::class);
        $merchantConfig->enabledTransactionInsertionOffload = $merchantConfig->enabledTransactionInsertionOffload ?? false; 
        $merchantConfig->enabledPushTransactionNotificationOffload = $merchantConfig->enabledPushTransactionNotificationOffload ?? true; 

        $data = [
            'transactionid' => $this->request->getVar('transactionid'),
            'transactiontime' => $this->request->getVar('transactiontime'),
            'referencenumber' => $this->request->getVar('referencenumber'),
            'amount' => $this->request->getVar('amount'),
            'content' => $this->request->getVar('content'),
            'bankaccount' => $this->request->getVar('bankaccount'),
            'transType' => $this->request->getVar('transType'),
            'va' => $this->request->getVar('va'),
            'valueDate' => $this->request->getVar('valueDate'),
            'reciprocalAccount' => $this->request->getVar('reciprocalAccount'),
            'reciprocalBankCode' => $this->request->getVar('reciprocalBankCode'),
        ];

        $rules = [
            'transactionid' => ['required', 'string'],
            'transactiontime' => ['required', 'regex_match[/^[0-9]{13}$/]'],
            'referencenumber' => ['required', 'string'],
            'amount' => ['required', 'integer'],
            'content' => ['required', 'string'],
            'bankaccount' => ['required', 'string'],
            'transType' => ['permit_empty', 'in_list[D,C]'],
            'va' => ['permit_empty', 'string'],
            'valueDate' => ['permit_empty', 'regex_match[/^[0-9]{13}$/]'],
            'reciprocalAccount' => ['permit_empty', 'string'],
            'reciprocalBankCode' => ['permit_empty', 'string'],
        ];

        if (! $this->validateData($data, $rules)) {
            log_message('error', "MBB Notification Error: Bad request " . json_encode($this->validator->getErrors()));
            log_message('error', "MBB Notification Payload: " . json_encode($data));

            return $this->respond([
                'transactionid' => $data['transactionid'],
                'code' => 400,
                'message' => 'Bad request',
                'errors' => $this->validator->getErrors()
            ], 400);
        }

        $mbbNotificationRawModel = model(MbbNotificationRawModel::class);
        $mbbNotificationModel = model(MbbNotificationModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);

        if ($config->notifyDebug) {
            $mbbNotificationRawId = $mbbNotificationRawModel->insert(['payload' => json_encode($data), 'ip' =>$this->request->getIPAddress()]);
        } else {
            $mbbNotificationRawId = 0;
        }

        if (!$this->authorize($this->request)) {
            log_message('error', "MBB Notification Error: Unauthorized");
            
            return $this->respond([
                'transactionid' => $data['transactionid'],
                'code' => 403,
                'message' => 'Unauthorized'
            ], 403);
        }

        $duplicatedTransaction = $mbbNotificationModel->where('transactionid', $data['transactionid'])->countAllResults();
        if ($duplicatedTransaction) {
            if ($config->notifyDebug) log_message('error', "MBB Notification Error: Transaction Duplicated - Raw #" . $mbbNotificationRawId);

            return $this->respond([
                'transactionid' => $data['transactionid'],
                'code' => 4090,
                'message' => 'Transaction ID duplicated'
            ], 409);
        }

        $duplicatedReferenceNumber = $mbbNotificationModel->where([
            'referencenumber' => $data['referencenumber'],
            'transType' => $data['transType'],
            'amount' => $data['amount']
        ])->countAllResults();
        if ($duplicatedReferenceNumber) {
            if ($config->notifyDebug) log_message('error', "MBB Notification Error: Reference number Duplicated - Raw #" . $mbbNotificationRawId);

            return $this->respond([
                'transactionid' => $data['transactionid'],
                'code' => 4091,
                'message' => 'Reference number duplicated'
            ], 409);
        }

        $bankAccountModel = model(BankAccountModel::class);
        $bankAccountDetails = $bankAccountModel->where([
            'account_number' => $data['bankaccount'],
            'bank_id' => 8,
            'active' => true,
        ])->get()->getRow();

        if(!is_object($bankAccountDetails)) {
            log_message('error', "MBB Notification Error: Account number does not exists - Raw #" . $mbbNotificationRawId);

            return $this->respond([
                'transactionid' => $data['transactionid'],
                'code' => 4040,
                'message' => 'Account number does not exists'
            ], 404);
        }

        $canSyncAccumulated = true;

        // Skip transaction if it is VA transaction
        $contentFirstWord = explode(' ', $data['content'])[0] ?? '';
        $contentFirstWordIsVaNumber = $bankSubAccountModel->where([
            'sub_account' => $mbbMmsConfig->prefixVa . $contentFirstWord, 
            'bank_account_id' => $bankAccountDetails->id, 
            'acc_type' => 'Real'
        ])->countAllResults();

        if ($contentFirstWordIsVaNumber && $data['transType'] == 'C') {
            if ($config->notifyDebug) {
                log_message('error', 'Skip Bank API notification when VA transaction');
                log_message('error', json_encode($data));
            }

            return $this->respond([
                'transactionid' => $data['transactionid'],
                'code' => 200,
                'message' => 'OK'
            ]);
        }

        // Skip Bank API notification when switch to SMS Banking
        if ($bankAccountDetails->bank_id == 8 && $bankAccountDetails->bank_sms) {
            if ($config->notifyDebug) {
                log_message('error', 'Skip Bank API notification when MB account switch to SMS connection');
                log_message('error', json_encode($data));
            }

            return $this->respond([
                'transactionid' => $data['transactionid'],
                'code' => 200,
                'message' => 'OK'
            ]);
        }

        if ($data['va']) {
            $bankSubAccountDetails = $bankSubAccountModel->where([
                'sub_account' => $data['va'],
                'bank_account_id' => $bankAccountDetails->id,
            ])->get()->getRow();

            if(!is_object($bankSubAccountDetails)) {
                log_message('error', "MBB Notification Error: VA number does not exists - Raw #" . $mbbNotificationRawId);

                return $this->respond([
                    'transactionid' => $data['transactionid'],
                    'code' => 4041,
                    'message' => 'VA number does not exists'
                ], 404);
            }
        } else {
            preg_match('/TKP[A-Z0-9]{2,3}/', $data['content'], $matches, PREG_OFFSET_CAPTURE);
            if (is_array($matches) && count($matches) > 0 && isset($matches[0][0])) {
                $data['va'] = str_replace("TKP","",$matches[0][0]);
            }
        }

        if ($data['va']) {
            $bankSubAccountDetails = $bankSubAccountModel->where([
                'sub_account' => $data['va'],
                'bank_account_id' => $bankAccountDetails->id,
                'va_active' => 1,
                'active' => 1,
                'deleted_at' => null,
            ])->get()->getRow();
        } else {
            $bankSubAccountDetails = null;
        }

        // Skip transaction follow by merchant transaction type setting
        if ($bankAccountDetails->merchant_id) {
            $merchant = model(MerchantModel::class)->where(['id' => $bankAccountDetails->merchant_id])->get()->getRow();
            
            if (($merchant->trans_type == 'C' && $data['transType'] == 'D') 
            || ($merchant->trans_type == 'D' && $data['transType'] == 'C')) {
                return $this->respond([
                    'transactionid' => $data['transactionid'],
                    'code' => 200,
                    'message' => 'OK'
                ]);
            }
        }

        try {
            $mbbNotificationId = $mbbNotificationModel->insert([
                'raw_id' => $mbbNotificationRawId,
                'transactionid' => $data['transactionid'],
                'transactiontime' => $data['transactiontime'],
                'referencenumber' => $data['referencenumber'],
                'amount' => $data['amount'],
                'content' => $data['content'],
                'bankaccount' => $data['bankaccount'],
                'transType' => $data['transType'],
                'valueDate' => $data['valueDate'],
                'va' => $data['va'],
                'reciprocalAccount' => $data['reciprocalAccount'],
                'reciprocalBankCode' => $data['reciprocalBankCode'],
            ]);

            if (!$mbbNotificationId) {
                throw new \Exception('No inserted ID');
            }
        } catch (\Exception $e) {
            log_message('error', 'MBB Notification Error: Notification Inserted failed - ' . $e->getMessage());

            if ($e->getCode() == 1062) {
                return $this->respond([
                    'transactionid' => $data['transactionid'],
                    'code' => 200,
                    'message' => 'OK'
                ]);
            }

            return $this->respond([
                'transactionid' => $data['transactionid'],
                'code' => 503,
                'message' => 'Service unavailable, please retry'
            ], 503);
        }

        if (($this->config->enableFilterTransaction ?? false) && ! $bankAccountDetails->merchant_id) {
            $filterTransaction = FilterTransactionAction::forBankAccount($bankAccountDetails->id)
                ->amountIn($data['transType'] === 'C')
                ->description($data['content'])
                ->payload($data)
                ->filter();

            if ($filterTransaction['isRejected']) {
                return $this->respond([
                    'transactionid' => $data['transactionid'],
                    'code' => 200,
                    'message' => 'OK',
                ]);
            }

            $canSyncAccumulated = $filterTransaction['canSyncAccumulated'];
        }

        $smsParserModel = model(SmsParserModel::class);
        $companyModel = model(CompanyModel::class);
        $transactionsModel = model(TransactionsModel::class);

        $code = null;

        if (is_object($bankAccountDetails)) {
            $code = PayCodeDetector::getCode($data['content'], $bankAccountDetails->company_id);
        }

        $transactionsModel = model(TransactionsModel::class);
        
        $cache = service('cache');
        $lastAccumulatedKey = 'sepay_last_accumulated_bank_account_' . $bankAccountDetails->id;
        $lastAccumulated = $cache->get($lastAccumulatedKey);
        $accumulated = 0;

        if ($lastAccumulated === 0) {
            $accumulated = 0;
        } else if (!is_null($lastAccumulated)) {
            if ($data['transType'] === 'D') {
                $accumulated = $lastAccumulated - $data['amount'];
            } else if ($data['transType'] === 'C') {
                $accumulated = $lastAccumulated + $data['amount'];
            }
        } else {
            $lastTransaction = $transactionsModel->select(['accumulated'])->where([
                'accumulated!=' => '',
                'bank_account_id' => $bankAccountDetails->id,
            ])->orderBy('transaction_date', 'desc')->first();
            
            if (is_object($lastTransaction)) {
                if ($data['transType'] === 'D') {
                    $accumulated -= $data['amount'];
                } else if ($data['transType'] === 'C') {
                    $accumulated += $data['amount'];
                }
            }
        }

        $parserData = [
            "sms_id" => $mbbNotificationId,
            "gateway" => "MBBank",
            "transaction_date" => date("Y-m-d H:i:s", $data['transactiontime'] / 1000),
            "account_number" => $data['bankaccount'],
            "sub_account" => $data['va'],
            "amount_out" => $data['transType'] === 'D' ? $data['amount'] : 0,
            "amount_in" => $data['transType'] === 'C' ? $data['amount'] : 0,
            "accumulated" => $canSyncAccumulated ? $accumulated : 0,
            "code" => $code,
            "transaction_content" => $data['content'],
            "reference_number" => $data['referencenumber'],
            "source" => "BankAPINotify",
            "body" => "BankAPINotify " . $data['content'],
            "parser_status" => "Success",
            "from_account_number" => $data['reciprocalAccount'],
            "from_bin" => $data['reciprocalBankCode'],
            'bank_account_id' => $bankAccountDetails->id,
            'merchant_id' => $bankAccountDetails->merchant_id,
            'transaction_id' => uuid()
        ];

        $queueTransactionId = null;
        $parserId = null; // primary id of tb_autopay_sms_parsed table
        $transactionDetails = null;

        if ($bankAccountDetails->merchant_id && $merchantConfig->enabledTransactionInsertionOffload) {
            $queueTransactionId = CreateTransactionQueueAction::run((object) $parserData, $bankAccountDetails);
        }

        if (!$queueTransactionId) {
            $parserId = $smsParserModel->insert($parserData);
            $transactionDetails = $smsParserModel->find($parserId);
        }

        $cache->save($lastAccumulatedKey, $accumulated, 0);

        if (!$parserId && !$queueTransactionId) {
            $mbbNotificationModel->where('id', $mbbNotificationId)->delete();
            log_message('error', "MBB Notification Error: SMSParserModel insert failed - Raw #" . $mbbNotificationRawId);

            return $this->respond([
                'transactionid' => $data['transactionid'],
                'code' => 503,
                'message' => 'Service unavailable, please retry'
            ], 503);
        }

        if (is_object($transactionDetails) && is_numeric($transactionDetails->account_number) && $bankAccountDetails->merchant_id && !$queueTransactionId) {
            try {
                PushMerchantTransactionNotificationAction::run(
                    $bankAccountDetails, 
                    $transactionDetails,
                    $merchantConfig->enabledPushTransactionNotificationOffload
                );
            } catch (Exception $e) {
                log_message('error', '[PushableMerchantTransactionNotificationAction] ' . $e->getMessage() . ' - ' . $e->getTraceAsString());
            }
        }

        if (is_object($transactionDetails) && is_numeric($transactionDetails->account_number) && !$bankAccountDetails->merchant_id) {
            try {
                PushMobileTransactionNotificationQueueAction::run($transactionDetails, $bankAccountDetails, $bankSubAccountDetails);
            } catch (Exception $e) {
                log_message('error', 'MBB PushMobileTransactionNotificationQueueAction failed: ' . $e->getMessage() . ' - ' . $e->getTraceAsString() . ' - ' . json_encode($transactionDetails));
            }

            $companyCheck = $companyModel->select("tb_autopay_company.id")
                ->join("tb_autopay_company_subscription", "tb_autopay_company.id=tb_autopay_company_subscription.company_id")
                ->join("tb_autopay_bank_account", "tb_autopay_bank_account.company_id=tb_autopay_company.id")
                ->where([
                    'tb_autopay_bank_account.id' => $bankAccountDetails->id, 
                    'tb_autopay_company_subscription.status' => 'Active',
                    'tb_autopay_company.status' => 'Active', 'tb_autopay_company.active' => 1
                ])->get()->getResult();
            
            if (count($companyCheck) == 1) {
                // Update API connection status for enterprise account
                if ($bankAccountDetails->bank_api_connected == 0) {
                    $bankAccountModel->set(['bank_api_connected' => 1])->update($bankAccountDetails->id);
                }

                $webhookQueuable = $config->enableMbbWebhookOffload ?? false;
                $forceWebhookManually = ['webhook' => false, 'sapo' => false, 'haravan' => false, 'shopify' => false, 'googlesheet' => false,'output_device'=>false, 'viber' => false];
                $rabbitmq = null;

                if ($webhookQueuable) {
                    $rabbitmq = new \App\Libraries\RabbitMQClient;
                    $webhookQueuable = $rabbitmq->connect();
                }

                if ($rabbitmq && $webhookQueuable) {
                    $msg = new AMQPMessage(
                        json_encode(['account_number' => $transactionDetails->account_number, 'parser_id' => $parserId]),
                        array('delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT)
                    );

                    // webhooks
                    try {
                        $rabbitmq->queueDeclare('webhook');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('webhook', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'MBB API webhook queue failed: ' . $e->getMessage());
                        $forceWebhookManually['webhook'] = true;
                    }
                    
                    // sapo
                    try {
                        $rabbitmq->queueDeclare('sapo');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('sapo', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'MBB API Sapo queue failed: ' . $e->getMessage());
                        $forceWebhookManually['sapo'] = true;
                    }

                    // haravan
                    try {
                        $rabbitmq->queueDeclare('haravan');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('haravan', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'MBB API Haravan queue failed: ' . $e->getMessage());
                        $forceWebhookManually['haravan'] = true;
                    } 

                    // shopify
                    try {
                        $rabbitmq->queueDeclare('shopify');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('shopify', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'MBB API Shopify queue failed: ' . $e->getMessage());
                        $forceWebhookManually['shopify'] = true;
                    } 

                    // googlesheet
                    try {
                        $rabbitmq->queueDeclare('googlesheet');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('googlesheet', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'MBB API GoogleSheet queue failed: ' . $e->getMessage());
                        $forceWebhookManually['googlesheet'] = true;
                    }

                    // output device
                    try {
                        $rabbitmq->queueDeclare('output_device');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('output_device', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'MBB API output device queue failed: ' . $e->getMessage());
                        $forceWebhookManually['output_device'] = true;
                    } 
                    
                    // viber
                    try {
                        $rabbitmq->queueDeclare('viber');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('viber', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'MBB API Viber queue failed: ' . $e->getMessage());
                        $forceWebhookManually['viber'] = true;
                    }

                    $rabbitmq->close();
                } 
                
                if (!$webhookQueuable || $forceWebhookManually['webhook']) {
                    // webhooks
                    $webhooksModel = model(WebhooksModel::class);
                    $webhooksModel->doWebhooks($transactionDetails->account_number, $parserId);
                }

                if (!$webhookQueuable || $forceWebhookManually['sapo']) {
                    // sapo
                    $sapoModel = model(SapoModel::class);
                    $sapoModel->doWebhooks($transactionDetails->account_number, $parserId);
                }

                if (!$webhookQueuable || $forceWebhookManually['haravan']) {
                    // haravan
                    $haravanModel = model(HaravanModel::class);
                    $haravanModel->doWebhooks($transactionDetails->account_number, $parserId);
                }

                if (!$webhookQueuable || $forceWebhookManually['shopify']) {
                    // shopify
                    ExecuteShopifyWebhooksAction::run($parserId);
                }

                if (!$webhookQueuable || $forceWebhookManually['googlesheet']) {
                    // googlesheet
                    ExecuteGoogleSheetsWebhooksAction::run($parserId);
                }
                
                if (!$webhookQueuable || $forceWebhookManually['webhook']) {
                    // telegram queue
                    $notificationTelegramModel = model(NotificationTelegramModel::class);
                    $notificationTelegramModel->checkAndAddQueue($parserId);
                    
                    // lark messenger queue
                    $notificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);
                    $notificationLarkMessengerModel->checkAndAddQueue($parserId);
                }

                // output device
                if (!$webhookQueuable || $forceWebhookManually['output_device']) {
                    // output device queue
                    ExecuteOutputDevice::run($parserId,0);
                }
                
                if (!$webhookQueuable || $forceWebhookManually['viber']) {
                    // viber
                    (new \App\Features\Viber\ViberFeature)->sendNotificationByTransactionId($parserId);
                }

                //counter
                $counterModel = model(CounterModel::class);
                $counterModel->transaction($companyCheck[0]->id, false, $transactionDetails->amount_in, $transactionDetails->amount_out);

                // cashflow
                $bankAccountCashFlowModel = model(BankAccountCashflowModel::class);
                $bankAccountCashFlowModel->amount_update($companyCheck[0]->id, date('Y-m-d', strtotime($transactionDetails->transaction_date)), $transactionDetails->bank_account_id, $transactionDetails->amount_in, $transactionDetails->amount_out, $transactionDetails->sub_account, $transactionDetails->accumulated);

                $lastTransaction = $transactionDetails;

                if(is_object($lastTransaction))
                    $bankAccountModel->set(['last_transaction' => $lastTransaction->transaction_date, 'accumulated' => $lastTransaction->accumulated])->where(['id' => $bankAccountDetails->id])->update();
            }
        }
 
        return $this->respond([
            'transactionid' => $data['transactionid'],
            'code' => 200,
            'message' => 'OK'
        ]);
    }
}