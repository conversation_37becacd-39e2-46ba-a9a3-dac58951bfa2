<?php

namespace App\Controllers\Coreapi;

use App\Actions\BankAccount\FilterTransactionAction;
use App\Actions\GoogleSheets\ExecuteGoogleSheetsWebhooksAction;
use Exception;
use Config\Sms;
use App\Models\SmsModel;
use App\Models\SapoModel;
use CodeIgniter\Controller;
use App\Models\CounterModel;
use App\Models\HaravanModel;
use App\Models\WebhooksModel;
use App\Models\SmsParserModel;
use App\Models\BankAccountModel;
use App\Models\TransactionsModel;
use CodeIgniter\API\ResponseTrait;
use App\Models\BankSubAccountModel;
use PhpAmqpLib\Message\AMQPMessage;
use App\Models\BankAccountCashflowModel;
use App\Models\NotificationTelegramModel;
use CodeIgniter\RESTful\ResourceController;
use App\Models\NotificationLarkMessengerModel;
use App\Models\NotificationTelegramQueueModel;
use App\Actions\Shopify\ExecuteShopifyWebhooksAction;
use App\Actions\Mobile\PushMobileTransactionNotificationQueueAction;

use App\Actions\OutputDevice\ExecuteOutputDevice;

class Dinstar extends ResourceController
{
    use ResponseTrait;

    protected $helpers = ['general'];
 
    public function create() {

        $request = \Config\Services::request();

        $client_ip = $request->getIPAddress();

        $config = config(Sms::class);
        $config->enableSmsWebhookOffload = $config->enableSmsWebhookOffload ?? false;

        if(!in_array($client_ip, ['*************', '*************','**************','***************','**************']) && ! in_testing())
            return $this->failNotFound();
 
        
        if ($this->request->getMethod(true) != 'POST')
            return $this->failNotFound();

        $json = $this->request->getJSON();

        try {
            log_message('error', 'Debug dinstar raw body content: ' . file_get_contents('php://input'));
            log_message('error', 'Debug dinstar json: ' . json_encode($json));
        } catch (\Exception $e) {

        }

        //log_message('error', "Dinstar SMS Log: " . serialize($json));

        $smsModel = new SmsModel();
        $simModel = model(SimModel::class);

        if(!is_object($json)) {
            log_message('error', "Dinstar SMS Error: Invalid json format: " . serialize($json));
            return $this->fail("Dinstar SMS Error: Invalid json format", 400);
        }

        if(!isset($json->sms) || !is_array($json->sms)) {
            log_message('error', "Dinstar SMS Error: Invalid json SMS format");
            return $this->fail("Dinstar SMS Error: Invalid json SMS format", 400);
        }

        foreach($json->sms as $sms) {

            // {"sn":"dbd1-0920-9066-0042","sms":[{"incoming_sms_id":2,"port":0,"number":"+***********","smsc":"+***********","timestamp":"2023-11-13 15:23:07","text":"Keke","imsi":"***************"}]}
            // number: nguoi goi
            // imsi: nguoi nhan

            $custom_timestamp = null;
            if($sms->number == "Techcombank"){

                try {
                    $custom_timestamp = isset($sms->timestamp) ? trim($sms->timestamp) : null;
    
                    if ($custom_timestamp) {
                        // Parse timestamp thành DateTime object theo định dạng cố định
                        $dt = \DateTime::createFromFormat('Y-m-d H:i:s', $custom_timestamp);
    
                        if (!$dt || $dt->format('Y-m-d H:i:s') !== $custom_timestamp) {
                            // Timestamp không hợp lệ
                            log_message('error', "Dinstar SMS Error: Invalid timestamp format: " . serialize($sms->timestamp));
                            $custom_timestamp = null; // reset lại nếu sai
                        }
                    }
                } catch (\Exception $e) {
                    log_message('error', "Dinstar SMS Error: Invalid timestamp format: " . serialize($sms->timestamp));
                    $custom_timestamp = null;
                }
            }

           

            $dinstar_imsi = $sms->imsi;

            $sim_details = $simModel->where(['dinstar_imsi' => $dinstar_imsi])->get()->getRow();


            if(!is_object($sim_details)) {
                log_message('error', "Dinstar SMS Error: Cannot find sim with IMSI: " . $dinstar_imsi);
                return $this->fail("Cannot find sim with your IMSI", 400);

            }

            $to = $sim_details->sim_phonenumber;

            $data = [
                'from' => $sms->number,
                'body' => $sms->text,
                'to' => $to,
                'dinstar_imsi' => $dinstar_imsi,
                'sms_source' => 'Dinstar'
            ];

            $result = $smsModel->insert($data);

            
            if(is_numeric($result) && $result > 0) {
                $response = [
                    'status'   => 201,
                    'error'    => null,
                    'messages' => [
                        'success' => 'Data Saved'
                    ],
                    'webhooks_result'    => ''
                ];
                
                $smsParserModel = model(SmsParserModel::class);
                $companyModel = model(CompanyModel::class);
                $transactionsModel = model(TransactionsModel::class);
                $bankAccountModel = model(BankAccountModel::class);

                //$parser_result = $smsParserModel->smsBankingParser($result);
                $parser_data = $smsParserModel->smsBankingParser($result,$custom_timestamp);
                //$parser_result = $smsParserModel->insert($parser_data);


                $parser_result = FALSE;

                if ($parser_data['account_number']) {
                    $bankAccountDetails = $bankAccountModel
                        ->select('tb_autopay_bank.brand_name, tb_autopay_bank_account.id, tb_autopay_bank_account.account_number, tb_autopay_bank_account.bank_api, tb_autopay_bank_account.bank_sms, tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.bank_id, tb_autopay_bank_account.company_id')
                        ->join('tb_autopay_bank', 'tb_autopay_bank.id=tb_autopay_bank_account.bank_id')
                        ->where([
                            'account_number' => $parser_data['account_number'],
                            'tb_autopay_bank.brand_name' => $parser_data['gateway'],
                            'tb_autopay_bank_account.active' => true,
                        ])->first();

                    if (is_object($bankAccountDetails)) {
                        $parser_data['bank_account_id'] = $bankAccountDetails->id;
                    }
                } else {
                    $bankAccountDetails = null;
                }

                if ($bankAccountDetails && ($config->enableFilterTransaction ?? false)) {
                    $filterTransaction = FilterTransactionAction::forBankAccount($bankAccountDetails->id)
                        ->amountIn($parser_data['amount_in'] != 0)
                        ->description($parser_data['transaction_content'] ?? null)
                        ->payload($parser_data)
                        ->filter();
        
                    if ($filterTransaction['isRejected']) {
                        return $this->respondCreated($response);
                    }

                    $parser_data['accumulated'] = $filterTransaction['canSyncAccumulated'] ? $parser_data['accumulated'] : 0;
                }

                // For DDV Only
                if($parser_data['account_number'] == "**********" && $parser_data['gateway'] == "Vietcombank" && $parser_data['parser_status'] == "Success") {
                    $is_existed_transaction = $transactionsModel->getExitingIbankingTransactionVcb($parser_data['account_number'], $parser_data['transaction_content'], $parser_data['transaction_date'], $parser_data['amount_in'], $parser_data['amount_out']);
                    $skip_sms = FALSE;

                    if(strlen($parser_data['transaction_content']) < 20 && substr($parser_data['transaction_content'],-3) == "..." && substr($parser_data['transaction_content'],0,5) == "QR - ")
                        $skip_sms = TRUE;

                    if($is_existed_transaction === FALSE && $skip_sms === FALSE) {
                        $parser_result = $smsParserModel->insert($parser_data);
                    }
                } else if($parser_data['parser_status'] == "Success") {
                    // Skip SMS when switch to API connection for MB, BIDV, ACB, VPBank, TPBank, VietinBank
                    if ($bankAccountDetails && in_array($bankAccountDetails->bank_id, [8, 9, 3, 2, 12, 6]) && $bankAccountDetails->bank_api && $bankAccountDetails->bank_api_connected) {
                        log_message('error', 'Skip SMS when account switch to API connection');
                        log_message('error', json_encode($parser_data));
                        $parser_result = false;
                    } else {
                        $parser_result = $smsParserModel->insert($parser_data);
                    }
                }
            
                // webhooks & telegram queue
                if(is_numeric($parser_result) && $parser_data['parser_status'] == "Success" && is_object($bankAccountDetails)) {

                    $transaction_details = $smsParserModel->find($parser_result);
                    if(is_object($transaction_details) && is_numeric($transaction_details->account_number)) {
                        if ($transaction_details->sub_account) {
                            $bankSubAccountDetails = model(BankSubAccountModel::class)->where([
                                'sub_account' => $transaction_details->sub_account,
                                'bank_account_id' => $bankAccountDetails->id,
                                'va_active' => 1,
                                'active' => 1,
                                'merchant_id' => null,
                                'deleted_at' => null,
                            ])->first();
                        } else {
                            $bankSubAccountDetails = null;
                        }

                        try {
                            PushMobileTransactionNotificationQueueAction::run($transaction_details, $bankAccountDetails, $bankSubAccountDetails);
                        } catch (Exception $e) {
                            log_message('error', 'Dinstar PushMobileTransactionNotificationQueueAction failed: ' . $e->getMessage() . ' - ' . $e->getTraceAsString() . ' - ' . json_encode($transaction_details));
                        }

                        // check company and subscription status
                        $company_check = $companyModel->select("tb_autopay_company.id")
                            ->join("tb_autopay_company_subscription","tb_autopay_company.id=tb_autopay_company_subscription.company_id")
                            ->join("tb_autopay_bank_account","tb_autopay_bank_account.company_id=tb_autopay_company.id")
                            ->where(['tb_autopay_bank_account.id' => $bankAccountDetails->id, 'tb_autopay_company_subscription.status'=>'Active','tb_autopay_company.status' => 'Active', 'tb_autopay_company.active' => 1])->get()->getResult();
                        
                        if(count($company_check) == 1) {
                            $webhookQueuable = $config->enableSmsWebhookOffload;
                            $forceWebhookManually = ['webhook' => false, 'sapo' => false, 'haravan' => false, 'shopify' => false, 'googlesheet' => false,'output_device' => false, 'viber' => false];
                            $rabbitmq = null;

                            if ($webhookQueuable) {
                                $rabbitmq = new \App\Libraries\RabbitMQClient;
                                $webhookQueuable = $rabbitmq->connect();
                            }

                            if ($rabbitmq && $webhookQueuable) {
                                $msg = new AMQPMessage(
                                    json_encode(['account_number' => $transaction_details->account_number, 'parser_id' => $parser_result]),
                                    array('delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT)
                                );
            
                                // webhooks
                                try {
                                    $rabbitmq->queueDeclare('webhook');
                                    $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('webhook', 'name'));
                                } catch (\Exception $e) {
                                    log_message('error', 'SMS webhook queue failed: ' . $e->getMessage());
                                    $forceWebhookManually['webhook'] = true;
                                }
                                
                                // sapo
                                try {
                                    $rabbitmq->queueDeclare('sapo');
                                    $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('sapo', 'name'));
                                } catch (\Exception $e) {
                                    log_message('error', 'SMS Sapo queue failed: ' . $e->getMessage());
                                    $forceWebhookManually['sapo'] = true;
                                }
            
                                // haravan
                                try {
                                    $rabbitmq->queueDeclare('haravan');
                                    $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('haravan', 'name'));
                                } catch (\Exception $e) {
                                    log_message('error', 'SMS Haravan queue failed: ' . $e->getMessage());
                                    $forceWebhookManually['haravan'] = true;
                                } 

                                // shopify
                                try {
                                    $rabbitmq->queueDeclare('shopify');
                                    $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('shopify', 'name'));
                                } catch (\Exception $e) {
                                    log_message('error', 'Dinstar Shopify queue failed: ' . $e->getMessage());
                                    $forceWebhookManually['shopify'] = true;
                                } 

                                // googlesheet
                                try {
                                    $rabbitmq->queueDeclare('googlesheet');
                                    $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('googlesheet', 'name'));
                                } catch (\Exception $e) {
                                    log_message('error', 'Dinstar API GoogleSheet queue failed: ' . $e->getMessage());
                                    $forceWebhookManually['googlesheet'] = true;
                                } 

                                // output device
                                try {
                                    $rabbitmq->queueDeclare('output_device');
                                    $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('output_device', 'name'));
                                } catch (\Exception $e) {
                                    log_message('error', 'Dinstar API output device queue failed: ' . $e->getMessage());
                                    $forceWebhookManually['output_device'] = true;
                                } 
                                
                                // viber
                                try {
                                    $rabbitmq->queueDeclare('viber');
                                    $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('viber', 'name'));
                                } catch (\Exception $e) {
                                    log_message('error', 'Dinstar API Viber queue failed: ' . $e->getMessage());
                                    $forceWebhookManually['viber'] = true;
                                }
            
                                $rabbitmq->close();
                            } 
                            
                            if (!$webhookQueuable || $forceWebhookManually['webhook']) {
                                // webhooks
                                $webhooksModel = model(WebhooksModel::class);
                                $webhooksModel->doWebhooks($transaction_details->account_number, $parser_result);
                            }
            
                            if (!$webhookQueuable || $forceWebhookManually['sapo']) {
                                // sapo
                                $sapoModel = model(SapoModel::class);
                                $sapoModel->doWebhooks($transaction_details->account_number, $parser_result);
                            }
            
                            if (!$webhookQueuable || $forceWebhookManually['haravan']) {
                                // haravan
                                $haravanModel = model(HaravanModel::class);
                                $haravanModel->doWebhooks($transaction_details->account_number, $parser_result);
                            }

                            if (!$webhookQueuable || $forceWebhookManually['shopify']) {
                                // shopify
                                ExecuteShopifyWebhooksAction::run($parser_result);
                            }

                            if (!$webhookQueuable || $forceWebhookManually['googlesheet']) {
                                // googlesheet
                                ExecuteGoogleSheetsWebhooksAction::run($parser_result);
                            }

                            if (!$webhookQueuable || $forceWebhookManually['webhook']) {
                                // telegram queue
                                $notificationTelegramModel = model(NotificationTelegramModel::class);
                                $notificationTelegramModel->checkAndAddQueue($parser_result);
                                
                                // lark messenger queue
                                $notificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);
                                $notificationLarkMessengerModel->checkAndAddQueue($parser_result);
                            }
                            // output device
                            if (!$webhookQueuable || $forceWebhookManually['output_device']) {
                                // output device queue
                                ExecuteOutputDevice::run($parser_result,0);
                            }
                            
                            if (!$webhookQueuable || $forceWebhookManually['viber']) {
                                // viber
                                (new \App\Features\Viber\ViberFeature)->sendNotificationByTransactionId($parser_result);
                            }

                            //counter
                            $counterModel = model(CounterModel::class);
                            $counterModel->transaction($company_check[0]->id,FALSE,$transaction_details->amount_in, $transaction_details->amount_out);

                            // cashflow
                            $bankAccountCashFlowModel = model(BankAccountCashflowModel::class);
                            $bankAccountCashFlowModel->amount_update($company_check[0]->id, date('Y-m-d', strtotime($transaction_details->transaction_date)), $transaction_details->bank_account_id, $transaction_details->amount_in, $transaction_details->amount_out, $transaction_details->sub_account,$transaction_details->accumulated);

                            $last_trans = $transaction_details;

                            if(is_object($last_trans)) {
                                $bankAccountModel->set(['last_transaction' => $last_trans->transaction_date, 'accumulated' => $last_trans->accumulated,'bank_sms_connected' => 1])->where(['id' => $bankAccountDetails->id])->update();
                                $lastAccumulatedKey = 'sepay_last_accumulated_bank_account_' . $bankAccountDetails->id;
                                $cache = service('cache');
                                $cache->save($lastAccumulatedKey, $last_trans->accumulated, 0);
                            }
                        }
                    }
                }
            } else {
               // $this->fail("Cannot insert data", 400);
                log_message('error', "Dinstar SMS Error: Cannot insert data: " . serialize($data));
            }
        }

        return $this->respondCreated($response);
    }
}
