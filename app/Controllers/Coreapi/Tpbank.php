<?php

namespace App\Controllers\Coreapi;

use App\Actions\BankAccount\FilterTransactionAction;
use App\Actions\Merchant\PushMerchantTransactionNotificationAction;
use App\Actions\Mobile\PushMobileTransactionNotificationQueueAction;
use App\Actions\PayCodeDetector;
use App\Actions\Shopify\ExecuteShopifyWebhooksAction;
use App\Actions\GoogleSheets\ExecuteGoogleSheetsWebhooksAction;
use App\Actions\Transaction\CreateTransactionQueueAction;
use App\Libraries\RabbitMQClient;
use App\Models\TpbankTraceCompanyMapModel;
use CodeIgniter\RESTful\ResourceController;
use App\Libraries\Tpbank as Service;
use App\Models\BankAccountCashflowModel;
use Config\Tpbank as Config;
use App\Models\BankAccountModel;
use App\Models\BankSubAccountModel;
use App\Models\CompanyModel;
use App\Models\CounterModel;
use App\Models\HaravanModel;
use App\Models\NotificationLarkMessengerModel;
use App\Models\NotificationTelegramModel;
use App\Models\SapoModel;
use App\Models\SmsParserModel;
use App\Models\TpbankEnterpriseBankAccountModel;
use App\Models\TpbankNotificationModel;
use App\Models\TpbankNotificationRawModel;
use App\Models\WebhooksModel;
use CodeIgniter\HTTP\Response;
use Config\Merchant;
use DateTime;
use Exception;
use PhpAmqpLib\Message\AMQPMessage;
use App\Actions\OutputDevice\ExecuteOutputDevice;
use App\Models\RejectedTransactionLogModel;

class Tpbank extends ResourceController
{
    protected Service $service;

    protected Config $config;

    public function __construct()
    {
        $this->service = new Service();
        $this->config = config(Config::class);
    }

    public function callback(): Response
    {
        if ($this->request->getMethod(true) !== 'POST') {
            return $this->fail('Invalid request');
        }

        $data = $this->request->getJSON(true);

        if (empty($data)) {
            return $this->fail('Invalid request');
        }

        $rules = [
            'sourceAppId' => 'required',
            'traceNumber' => 'required',
            'merchantId' => 'required',
            'merchantName' => 'required',
            'accountType' => 'required|in_list[CB,RB]',
            'accountList' => 'required',
            'accountList.*.accountNumber' => 'required',
            'accountList.*.accountName' => 'required',
            'requestType' => 'required|in_list[ADD_ACC_NOTI,REMOVE_ACC_NOTI]',
            'signature' => 'required',
        ];

        if (! $this->validateData($data,  $rules)) {
            if ($this->config->debug) {
                log_message('error', 'TPBank Callback Error: ' . json_encode($this->validator->getErrors()));
                log_message('error', json_encode($data));
            }

            return $this->failValidationErrors($this->validator->getErrors());
        }

        if (! $this->service->verifySignature(
            $data['signature'],
            [
                $data['sourceAppId'],
                $data['traceNumber'],
                $data['merchantId'],
                $data['merchantName'],
                $data['accountType'],
                $data['requestType']
            ]
        )) {
            if ($this->config->debug) {
                log_message('error', 'TPBank Callback Error: Invalid signature ' . json_encode($data));
            }

            return $this->respond([
                'status' => 'FAILED',
                'errorCode' => '01',
                'errorDesc' => 'Invalid signature',
            ]);
        }

        if ($this->config->debug) {
            log_message('info', 'TPBank Callback: ' . json_encode($data));
        }

        $bankAccountModel = model(BankAccountModel::class);
        $traceModel = model(TpbankTraceCompanyMapModel::class);
        $enterpriseBankAccountModel = model(TpbankEnterpriseBankAccountModel::class);

        $bankAccounts = $bankAccountModel
            ->whereIn('account_number', array_column($data['accountList'], 'accountNumber'))
            ->where('bank_id', $this->config->bankId)
            ->where('active', true)
            ->where('bank_sms', false)
            ->findAll();

        $accountNumbers = array_column($bankAccounts, 'account_number');

        if ($data['requestType'] === 'REMOVE_ACC_NOTI') {
            $accounts = array_filter(
                $data['accountList'],
                fn($account) => in_array($account['accountNumber'], $accountNumbers)
            );

            if (empty($accounts)) {
                return $this->respond([
                    'status' => 'FAILED',
                    'errorCode' => '01',
                    'errorDesc' => 'Bank account not found',
                ]);
            }

            $bankAccountModel
                ->whereIn('account_number', array_column($accounts, 'accountNumber'))
                ->where('bank_id', $this->config->bankId)
                ->set([
                    'active' => false,
                    'bank_api_connected' => false,
                    'bank_api' => false,
                ])
                ->update();

            return $this->respond([
                'status' => 'SUCCESS',
                'errorCode' => '00',
                'errorDesc' => null,
            ]);
        }

        if (! $this->config->allowedEnterpriseConnection && $data['accountType'] === 'CB') {
            if ($this->config->debug) {
                log_message('error', 'TPBank Callback Error: Enterprise connection is not allowed ' . json_encode($data));
            }

            return $this->respond([
                'status' => 'FAILED',
                'errorCode' => '01',
                'errorDesc' => 'Enterprise connection is not allowed',
            ]);
        }

        $trace = $traceModel
            ->select([
                'tb_autopay_tpbank_trace_company_map.id',
                'tb_autopay_tpbank_trace_company_map.company_id',
                'tb_autopay_tpbank_trace_company_map.trace_number',
                'tb_autopay_tpbank_trace_company_map.ip',
                'tb_autopay_tpbank_trace_company_map.user_agent',
                'tb_autopay_company_user.company_id',
                'tb_autopay_company_user.user_id',
            ])
            ->where('trace_number', $data['traceNumber'])
            ->join(
                'tb_autopay_company_user',
                'tb_autopay_company_user.company_id = tb_autopay_tpbank_trace_company_map.company_id'
            )
            ->first();

        if (! $trace) {
            return $this->respond([
                'status' => 'FAILED',
                'errorCode' => '01',
                'errorDesc' => 'Invalid trace number',
            ]);
        }

        $accounts = array_filter(
            $data['accountList'],
            fn($account) => ! in_array($account['accountNumber'], $accountNumbers)
        );

        if (empty($accounts)) {
            return $this->respond([
                'status' => 'FAILED',
                'errorCode' => '01',
                'errorDesc' => 'All accounts are already existed',
            ]);
        }

        helper('general');

        foreach ($accounts as $account) {
            $existingAccount = $bankAccountModel
                ->where('account_number', $account['accountNumber'])
                ->where('bank_id', $this->config->bankId)
                ->first();

            if ($existingAccount) {
                $bankAccountModel->update($existingAccount->id, [
                    'account_holder_name' => remove_accents($account['accountName'], true),
                    'account_number' => $account['accountNumber'],
                    'active' => true,
                    'bank_api' => true,
                    'bank_api_connected' => true,
                    'bank_sms' => false,
                ]);
                $bankAccountId = $existingAccount->id;
            } else {
                $bankAccountId = $bankAccountModel->insert([
                    'company_id' => $trace->company_id,
                    'bank_id' => $this->config->bankId,
                    'account_holder_name' => remove_accents($account['accountName'], true),
                    'account_number' => $account['accountNumber'],
                    'active' => true,
                    'bank_api' => true,
                    'bank_api_connected' => true,
                ]);
            }

            if ($data['accountType'] === 'CB') {
                $enterpriseBankAccountModel->insert(['bank_account_id' => $bankAccountId]);
            }

            add_user_log([
                'data_id' => $bankAccountId,
                'company_id' => $trace->company_id,
                'data_type' => 'bank_account_add',
                'description' => 'Thêm tài khoản ngân hàng TPBank API',
                'user_id' => $trace->user_id,
                'ip' => $trace->ip,
                'user_agent' => $trace->user_agent,
                'status' => 'Success',
            ]);
        }

        $traceModel->update($trace->id, ['bank_account_id' => $bankAccountId]);

        return $this->respond([
            'status' => 'SUCCESS',
            'errorCode' => '00',
            'errorDesc' => null,
        ]);
    }

    public function notify()
    {
        if ($this->request->getMethod(true) !== 'POST') {
            return $this->fail('Invalid request');
        }

        $data = $this->request->getJSON(true);

        if (empty($data)) {
            return $this->fail('Invalid request');
        }

        $rules = [
            'sourceAppId' => 'required',
            'batchId' => 'required',
            'timestamp' => 'required',
            'signature' => 'required',
            'data' => 'required',
            'data.*.transactionId' => 'required',
            'data.*.tranRefNo' => 'permit_empty',
            'data.*.accountNumber' => 'required',
            'data.*.amount' => 'required',
            'data.*.balanceAvailable' => 'required',
            'data.*.transType' => 'permit_empty|in_list[C,D]',
            'data.*.notiCreatedTime' => 'required',
            'data.*.transTime' => 'required',
            'data.*.tranDesc' => 'permit_empty',
            'data.*.ofsAccountNumber' => 'permit_empty',
            'data.*.ofsAccountName' => 'permit_empty',
            'data.*.ofsBankId' => 'permit_empty',
            'data.*.ofsBankName' => 'permit_empty',
            'data.*.isVirtualTrans' => 'permit_empty',
            'data.*.virtualAcc' => 'permit_empty',
        ];

        if (! $this->validateData($data,  $rules)) {
            if ($this->config->debug) {
                log_message('error', 'TPBank Notification Error: ' . json_encode($this->validator->getErrors()));
            }

            return $this->failValidationErrors($this->validator->getErrors());
        }

        $standardResponse = function (string $message, array $transacitons = []) use ($data) {
            $body = [
                'batchId' => $data['batchId'],
                'code' => '200',
                'message' => $message,
            ];

            if (! empty($transacitons)) {
                $body['data'] = $transacitons;
            }

            return $this->respond($body);
        };

        if (! $this->service->verifySignature(
            $data['signature'],
            [
                $data['sourceAppId'],
                $data['batchId'],
                $data['timestamp'],
            ]
        )) {
            if ($this->config->debug) {
                log_message('error', 'TPBank Notification Error: Invalid signature ' . json_encode($data));
            }

            return $standardResponse('Invalid signature');
        }

        if ($this->config->debug) {
            log_message('info', 'TPBank Notification: ' . json_encode($data));
        }

        $rawId = model(TpbankNotificationRawModel::class)->insert([
            'payload' => json_encode($data),
            'ip_address' => $this->request->getIPAddress(),
        ]);

        $merchantConfig = config(Merchant::class);
        $tpbankNotificationModel = model(TpbankNotificationModel::class);
        $bankAccountModel = model(BankAccountModel::class);
        $smsParserModel = model(SmsParserModel::class);
        $companyModel = model(CompanyModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);

        $rawTransactions = $data['data'];
        $transactions = [];

        $bankAccounts = $bankAccountModel
            ->whereIn('account_number', array_column($rawTransactions, 'accountNumber'))
            ->where('bank_id', $this->config->bankId)
            ->where('active', true)
            ->findAll();

        $accountNumbers = array_column($bankAccounts, 'account_number');

        helper('general');

        foreach ($rawTransactions as $key => $transaction) {
            if ($tpbankNotificationModel->where('transaction_id', $transaction['transactionId'])->first()) {
                $transactions[] = [
                    'transactionId' => $transaction['transactionId'],
                    'errorCode' => '02',
                    'description' => 'Transaction already existed',
                ];

                if ($this->config->debug) {
                    log_message('error', 'TPBank Notifcation Error: Transaction already existed');
                }

                unset($rawTransactions[$key]);

                continue;
            }

            if (! in_array($transaction['accountNumber'], $accountNumbers)) {
                $transactions[] = [
                    'transactionId' => $transaction['transactionId'],
                    'errorCode' => '02',
                    'description' => 'Account number not found',
                ];

                if ($this->config->debug) {
                    log_message('error', 'TPBank Notifcation Error: Account number does not exists in SePay');
                }

                unset($rawTransactions[$key]);

                continue;
            }

            $bankAccount = $bankAccounts[array_search($transaction['accountNumber'], $accountNumbers)];

            if ($bankAccount->bank_id == $this->config->bankId && $bankAccount->bank_sms) {
                if ($this->config->debug) {
                    log_message('error', 'Skip Bank API notification when TPBank account switch to SMS connection');
                    log_message('error', json_encode($transaction));
                }

                $transactions[] = [
                    'transactionId' => $transaction['transactionId'],
                    'errorCode' => '02',
                    'description' => 'Bank account switch to SMS connection',
                ];

                continue;
            }

            $transactionDate = DateTime::createFromFormat("d/m/Y H:i:s", $transaction['notiCreatedTime'])->format('Y-m-d H:i:s');
            $transDesc = $transaction['tranDesc'] ?? null;

            if (in_array($transDesc, ['CHUYEN CHI NHANH'])) {
                model(RejectedTransactionLogModel::class)->insert([
                    'bank_account_id' => $bankAccount->id,
                    'reason' => 'internal_bank_transaction',
                    'message' => 'Giao dịch nội bộ ngân hàng',
                    'payload' => json_encode($transaction),
                ]);

                $transactions[] = [
                    'transactionId' => $transaction['transactionId'],
                    'errorCode' => '02',
                    'description' => 'Giao dịch nội bộ ngân hàng',
                ];

                continue;
            }

            $notificationId = $tpbankNotificationModel->insert([
                'raw_id' => $rawId,
                'batch_id' => $data['batchId'],
                'transaction_id' => $transaction['transactionId'],
                'account_number' => $transaction['accountNumber'],
                'amount' => $transaction['amount'],
                'balance' => $transaction['balanceAvailable'],
                'trans_type' => $transaction['transType'] ?? null,
                'trans_desc' => $transDesc,
                'trans_time' => DateTime::createFromFormat('d/m/Y', $transaction['transTime'])->format('Y-m-d'),
                'noti_created_time' => $transactionDate,
            ]);

            $canSyncAccumulated = true;

            if (($this->config->enableFilterTransaction ?? false) && ! $bankAccount->merchant_id) {
                $filterTransaction = FilterTransactionAction::forBankAccount($bankAccount->id)
                    ->amountIn($transaction['transType'] === 'C')
                    ->description($transaction['tranDesc'] ?? null)
                    ->payload($transaction)
                    ->filter();

                if ($filterTransaction['isRejected']) {
                    $transactions[] = [
                        'transactionId' => $transaction['transactionId'],
                        'errorCode' => '00',
                        'description' => $filterTransaction['message'],
                    ];

                    continue;
                }

                $canSyncAccumulated = $filterTransaction['canSyncAccumulated'];
            }

            $bankSubAccountNumber = null;
            $bankSubAccount = null;
            $code = null;

            preg_match('/TKP[A-Z0-9]{2,16}/', $transDesc, $matches, PREG_OFFSET_CAPTURE);

            if (is_array($matches) && count($matches) > 0 && isset($matches[0][0])) {
                $bankSubAccountNumber = str_replace('TKP', '', $matches[0][0]);
            }

            if ($transDesc && $bankAccount) {
                $code = PayCodeDetector::getCode($transDesc, $bankAccount->company_id);
            }

            if ($bankSubAccountNumber) {
                $bankSubAccount = $bankSubAccountModel
                    ->where([
                        'sub_account' => $bankSubAccountNumber,
                        'bank_account_id' => $bankAccount->id,
                        'va_active' => true,
                        'active' => true,
                    ])
                    ->first();
            }

            $transactionData = [
                'sms_id' => $notificationId,
                'gateway' => 'TPBank',
                'transaction_date' => $transactionDate,
                'account_number' => $transaction['accountNumber'],
                'sub_account' => $bankSubAccount->sub_account ?? null,
                'amount_in' => $transaction['transType'] === 'C' ? $transaction['amount'] : 0,
                'amount_out' => $transaction['transType'] === 'D' ? $transaction['amount'] : 0,
                'accumulated' => $canSyncAccumulated ? $transaction['balanceAvailable'] : 0,
                'code' => $code,
                'transaction_content' => $transDesc,
                'reference_number' => $transaction['tranRefNo'] ?? null,
                'source' => 'BankAPINotify',
                'body' => "BankAPINotify {$transDesc}",
                'parser_status' => 'Success',
                'bank_account_id' => $bankAccount->id,
                'merchant_id' => $bankAccount->merchant_id,
                'transaction_id' => uuid(),
            ];

            $queueTransactionId = null;
            $transactionDetails = null;

            if ($bankAccount->merchant_id && $merchantConfig->enabledTransactionInsertionOffload) {
                $queueTransactionId = CreateTransactionQueueAction::run((object) $transactionData, $bankAccount);
            }

            if (! $queueTransactionId) {
                $smsParserId = $smsParserModel->insert($transactionData);
                $transactionDetails = $smsParserModel->find($smsParserId);
            }

            if ($transactionDetails && $transactionDetails->account_number && $bankAccount->merchant_id && ! $queueTransactionId) {
                try {
                    PushMerchantTransactionNotificationAction::run(
                        $bankAccount,
                        $transactionDetails,
                        $merchantConfig->enabledPushTransactionNotificationOffload
                    );
                } catch (Exception $e) {
                    log_message('error', '[PushableMerchantTransactionNotificationAction] ' . $e->getMessage() . ' - ' . $e->getTraceAsString());
                }
            }

            if (! $transactionDetails || ! $transactionDetails->account_number || $bankAccount->merchant_id) {
                $transactions[] = [
                    'transactionId' => $transaction['transactionId'],
                    'errorCode' => '02',
                    'description' => 'Transaction failed',
                ];

                continue;
            }

            $company = $companyModel->select('tb_autopay_company.id')
                ->join('tb_autopay_company_subscription', 'tb_autopay_company.id=tb_autopay_company_subscription.company_id')
                ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.company_id=tb_autopay_company.id')
                ->where([
                    'tb_autopay_bank_account.id' => $bankAccount->id,
                    'tb_autopay_company_subscription.status' => 'Active',
                    'tb_autopay_company.status' => 'Active',
                    'tb_autopay_company.active' => true,
                ])
                ->first();

            if (! $company) {
                $transactions[] = [
                    'transactionId' => $transaction['transactionId'],
                    'errorCode' => '02',
                    'description' => 'Company not found',
                ];

                continue;
            }

            $transactions[] = [
                'transactionId' => $transaction['transactionId'],
                'errorCode' => '00',
                'description' => 'Success',
            ];

            try {
                PushMobileTransactionNotificationQueueAction::run(
                    $transactionDetails,
                    $bankAccount,
                    $bankSubAccount
                );
            } catch (Exception $e) {
                log_message('error', 'TPBank PushMobileTransactionNotificationQueueAction failed: ' . $e->getMessage() . ' - ' . $e->getTraceAsString() . ' - ' . json_encode($transactionDetails));
            }

            $webhookQueuable = $this->config->enableWebhookOffload;
            $forceWebhookManually = ['webhook' => false, 'sapo' => false, 'haravan' => false, 'shopify' => false, 'googlesheet' => false,'output_device' => false, 'viber' => false];
            $rabbitmq = null;

            if ($webhookQueuable) {
                $rabbitmq = new RabbitMQClient();
                $webhookQueuable = $rabbitmq->connect();
            }

            if ($rabbitmq && $webhookQueuable) {
                $msg = new AMQPMessage(
                    json_encode(['account_number' => $transactionDetails->account_number, 'parser_id' => $smsParserId]),
                    array('delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT)
                );

                try {
                    $rabbitmq->queueDeclare('webhook');
                    $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('webhook', 'name'));
                } catch (Exception $e) {
                    log_message('error', 'TPBank API webhook queue failed: ' . $e->getMessage());
                    $forceWebhookManually['webhook'] = true;
                }

                try {
                    $rabbitmq->queueDeclare('sapo');
                    $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('sapo', 'name'));
                } catch (Exception $e) {
                    log_message('error', 'TPBank API Sapo queue failed: ' . $e->getMessage());
                    $forceWebhookManually['sapo'] = true;
                }

                try {
                    $rabbitmq->queueDeclare('haravan');
                    $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('haravan', 'name'));
                } catch (Exception $e) {
                    log_message('error', 'TPBank API Haravan queue failed: ' . $e->getMessage());
                    $forceWebhookManually['haravan'] = true;
                }

                try {
                    $rabbitmq->queueDeclare('shopify');
                    $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('shopify', 'name'));
                } catch (Exception $e) {
                    log_message('error', 'TPBank API Shopify queue failed: ' . $e->getMessage());
                    $forceWebhookManually['shopify'] = true;
                }

                try {
                    $rabbitmq->queueDeclare('googlesheet');
                    $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('googlesheet', 'name'));
                } catch (\Exception $e) {
                    log_message('error', 'TPBank API GoogleSheet queue failed: ' . $e->getMessage());
                    $forceWebhookManually['googlesheet'] = true;
                }

                 // output device
                 try {
                    $rabbitmq->queueDeclare('output_device');
                    $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('output_device', 'name'));
                } catch (\Exception $e) {
                    log_message('error', 'TPBank API output device queue failed: ' . $e->getMessage());
                    $forceWebhookManually['output_device'] = true;
                } 
                
                // viber
                try {
                    $rabbitmq->queueDeclare('viber');
                    $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('viber', 'name'));
                } catch (\Exception $e) {
                    log_message('error', 'TPBank API Viber queue failed: ' . $e->getMessage());
                    $forceWebhookManually['viber'] = true;
                }


                $rabbitmq->close();
            }

            if (! $webhookQueuable || $forceWebhookManually['webhook']) {
                $webhooksModel = model(WebhooksModel::class);
                $webhooksModel->doWebhooks($transactionDetails->account_number, $smsParserId);
            }

            if (! $webhookQueuable || $forceWebhookManually['sapo']) {
                $sapoModel = model(SapoModel::class);
                $sapoModel->doWebhooks($transactionDetails->account_number, $smsParserId);
            }

            if (! $webhookQueuable || $forceWebhookManually['haravan']) {
                $haravanModel = model(HaravanModel::class);
                $haravanModel->doWebhooks($transactionDetails->account_number, $smsParserId);
            }

            if (! $webhookQueuable || $forceWebhookManually['shopify']) {
                ExecuteShopifyWebhooksAction::run($smsParserId);
            }

            if (! $webhookQueuable || $forceWebhookManually['googlesheet']) {
                ExecuteGoogleSheetsWebhooksAction::run($smsParserId);
            }

            if (! $webhookQueuable || $forceWebhookManually['webhook']) {
                $notificationTelegramModel = model(NotificationTelegramModel::class);
                $notificationTelegramModel->checkAndAddQueue($smsParserId);

                $notificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);
                $notificationLarkMessengerModel->checkAndAddQueue($smsParserId);
            }


             // output device
             if (!$webhookQueuable || $forceWebhookManually['output_device']) {
                // output device queue
                ExecuteOutputDevice::run($smsParserId,0);
            }
            
            if (!$webhookQueuable || $forceWebhookManually['viber']) {
                // viber
                (new \App\Features\Viber\ViberFeature)->sendNotificationByTransactionId($smsParserId);
            }

            $counterModel = model(CounterModel::class);
            $counterModel->transaction(
                $company->id,
                false,
                $transactionDetails->amount_in,
                $transactionDetails->amount_out
            );

            $bankAccountCashFlowModel = model(BankAccountCashflowModel::class);
            $bankAccountCashFlowModel->amount_update(
                $company->id,
                date('Y-m-d', strtotime($transactionDetails->transaction_date)),
                $transactionDetails->bank_account_id,
                $transactionDetails->amount_in,
                $transactionDetails->amount_out,
                $transactionDetails->sub_account,
                $transactionDetails->accumulated
            );

            $lastTransaction = $transactionDetails;

            if ($lastTransaction) {
                $bankAccountModel
                    ->set([
                        'last_transaction' => $lastTransaction->transaction_date,
                        'accumulated' => $lastTransaction->accumulated,
                    ])
                    ->where('id', $bankAccount->id)
                    ->update();
            }
        }

        return $standardResponse('Success', $transactions);
    }
}
