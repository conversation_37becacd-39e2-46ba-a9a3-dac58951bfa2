<?php

namespace App\Controllers\Coreapi;

use App\Actions\GoogleSheets\ExecuteGoogleSheetsWebhooksAction;
use App\Actions\Merchant\PushMerchantTransactionNotificationAction;
use App\Actions\Mobile\PushMobileTransactionNotificationQueueAction;
use App\Actions\OutputDevice\ExecuteOutputDevice;
use App\Actions\PayCodeDetector;
use App\Actions\Shopify\ExecuteShopifyWebhooksAction;
use App\Actions\Transaction\CreateTransactionQueueAction;
use App\Features\PaymentGateway\PaymentGatewayFeature;
use App\Models\BankAccountCashflowModel;
use App\Models\BankAccountModel;
use App\Models\CompanyModel;
use App\Models\CounterModel;
use App\Models\CybersourceNotificationModel;
use App\Models\CybersourceProfileModel;
use App\Models\HaravanModel;
use App\Models\NotificationLarkMessengerModel;
use App\Models\NotificationTelegramModel;
use App\Models\SapoModel;
use App\Models\SmsParserModel;
use App\Models\UserModel;
use App\Models\WebhooksModel;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\RESTful\ResourceController;
use PhpAmqpLib\Message\AMQPMessage;

class Cybersource extends ResourceController
{
    use ResponseTrait;
    
    public function respondTransaction(string $merchant = '')
    {
        try {
            $paymentGatewayFeature = new PaymentGatewayFeature(\App\Features\PaymentGateway\Cybersource::class);
        
            /** @var \App\Features\PaymentGateway\Cybersource $paymentGateway */
            $paymentGateway = $paymentGatewayFeature->gateway;
            
            $profileId = $this->request->getVar('profile_id');

            $paymentGateway->loadFromModel($profileId);
            
            $valid = $paymentGateway->validateTransactionResponse($this->request);
            
            if (!$valid) {
                return $this->respond(['message' => 'Đã có lỗi xảy ra']);
            }

            $params = [];
            
            if ($merchant === 'SEPAY') {
                try {
                    $params = $this->handleCallbackForSePay($paymentGateway);
                    
                    if ($this->request->getVar('decision') === 'ERROR') {
                        set_alert('error', 'Thanh toán không thành công');
                    }
                } catch (\Exception $e) {
                    return redirect()->to(base_url('login'));
                }
            }
            
            $transactionResponseUrl = $paymentGateway->getTransactionResponseUrl($params);
            
            echo '<script>window.location.href = "' . $transactionResponseUrl .  '";</script>';
        } catch (\Exception $e) {
            return $this->respond(['message' => 'Đã có lỗi xảy ra']);
        }
    }
    
    public function cancelTransaction(string $merchant = '')
    {
        try {
            $paymentGatewayFeature = new PaymentGatewayFeature();
            
            /** @var \App\Features\PaymentGateway\Cybersource $paymentGateway */
            $paymentGateway = $paymentGatewayFeature->gateway;
            
            $profileId = $this->request->getVar('profile_id');
    
            $paymentGateway->loadFromModel($profileId);
            
            $valid = $paymentGateway->validateTransactionResponse($this->request);
            
            if (!$valid) {
                return $this->respond(['message' => 'Đã có lỗi xảy ra']);
            }
            
            $params = [];
            
            if ($merchant === 'SEPAY') {
                try {
                    $params = $this->handleCallbackForSePay($paymentGateway);
                } catch (\Exception $e) {
                    return redirect()->to(base_url('login'));
                }
            }
            
            $cancelResponseUrl = $paymentGateway->getCancelResponseUrl($params);
            
            echo '<script>window.location.href = "' . $cancelResponseUrl .  '";</script>';
        } catch (\Exception $e) {
            return $this->respond(['message' => 'Đã có lỗi xảy ra' . $e->getMessage()]);
        }
    }
    
    public function notifyTransaction()
    {
        $data = $this->request->getVar();
        
        $data['full_payload'] = json_encode($data);
        
        if (!isset($data['signed_field_names']) || empty($data['signed_field_names'])) {
            return $this->fail(['status' => 'error', 'message' => 'Missing signed_field_names parameter']);
        }
        
        $signedFieldNames = explode(',', str_replace('%2C', ',', $data['signed_field_names']));
        
        $rules = [];
        
        foreach ($signedFieldNames as $field) {
            $rules[$field] = ['required'];
        }
        
        if (! $this->validateData($data, $rules)) {
            log_message('error', '[Cybersource IPN] Invalid data ' . $this->validator->getErrors());
            return $this->fail($this->validator->getErrors());
        }
        
        $paymentGatewayFeature = new PaymentGatewayFeature();
        
        /** @var PaymentGatewayCybersource **/
        $paymentGateway = $paymentGatewayFeature->gateway;
        
        if (!isset($data['signature'])) {
            log_message('error', '[Cybersource IPN] Missing signature parameter - ' . $data['full_payload']);
            return $this->fail(['status' => 'error', 'message' => 'Missing signature parameter']);
        }
        
        $cybersourceNotificationModel = model(CybersourceNotificationModel::class);
        
        if ($cybersourceNotificationModel->where('transaction_id', $data['transaction_id'])->countAllResults() > 0) {
            return $this->respond(['status' => 'error', 'message' => 'Notification duplicated']);
        }
        
        try {
            $cybersourceNotificationId = $cybersourceNotificationModel->insert($data);
        } catch (\Exception $e) {
            log_message('error', '[Cybersource IPN] Failed to insert notification: ' . $e->getMessage() . ' - ' . $data['full_payload']);
            return $this->fail(['status' => 'error', 'message' => 'Failed to insert notification']);
        }
        
        $cybersourceProfileModel = model(CybersourceProfileModel::class);
        $profile = $cybersourceProfileModel->where('profile_id', $data['req_profile_id'])->first();
        
        if (!$profile) {
            log_message('error', '[Cybersource IPN] Profile not found: ' . $data['full_payload']);
            return $this->fail(['status' => 'error', 'message' => 'Profile not found']);
        }
        
        if ($profile->active == 0) {
            log_message('error', '[Cybersource IPN] Profile is inactive: ' . $data['full_payload']);
            return $this->fail(['status' => 'error', 'message' => 'Profile is inactive']);
        }
        
        $paymentGateway->loadFromModel($profile->id);
        $validSignature = $paymentGateway->signFields($data);
        
        if ($data['signature'] !== $validSignature) {
            log_message('error', '[Cybersource IPN] Invalid signature - ' . $data['full_payload']);
            return $this->fail(['status' => 'error', 'message' => 'Invalid signature']);
        }
        
        $bankAccountDetails = model(BankAccountModel::class)->where('id', $profile->bank_account_id)->first();
        
        if (!$bankAccountDetails) {
            log_message('error', '[Cybersource IPN] Bank account not found - ' . $data['full_payload']);
            return $this->fail(['status' => 'error', 'message' => 'Bank account not found']);
        }
        
        $code = null;
        
        if (is_object($bankAccountDetails)) {
            $code = PayCodeDetector::getCode($data['req_reference_number'], $bankAccountDetails->company_id);
        }
        
        $safeTransactionData = [
            'sms_id' => $cybersourceNotificationId,
            'gateway' => 'VPBank_Cybersource',
            'transaction_date' => date("Y-m-d H:i:s", strtotime($data['auth_time'])),
            'account_number' => $bankAccountDetails->account_number,
            'sub_account' => null,
            'amount_out' => 0,
            'amount_in' => 	$data['auth_amount'],
            'accumulated' => 0,
            'code' => $code,
            'body' => $code,
            'transaction_content' => $code,
            'reference_number' => $data['req_reference_number'],
            'source' => 'Cybersource',
            'parser_status' => 'Success',
            'from_bin' => $data['req_card_type'],
            'from_account_number' => $data['req_card_number'],
            'currency' => $data['req_currency'],
            'bank_account_id' => $bankAccountDetails->id,
            'merchant_id' => null,
            'transaction_id' => uuid()
        ];
        
        $smsParserModel = model(SmsParserModel::class);
        $companyModel = model(CompanyModel::class);
        $bankAccountModel = model(BankAccountModel::class);
        
        $queueTransactionId = null;
        $transactionId = null; // primary id of tb_autopay_sms_parsed table
        $transactionDetails = null;

        $cybersourceConfig = config(\Config\CybersourceConfig::class);
        $merchantConfig = config(\Config\Merchant::class);
        
        if ($bankAccountDetails->merchant_id && $merchantConfig->enabledTransactionInsertionOffload) {
            $queueTransactionId = CreateTransactionQueueAction::run((object) $safeTransactionData, $bankAccountDetails);
        }

        if (!$queueTransactionId) {
            $transactionId = $smsParserModel->insert($safeTransactionData);
            $transactionDetails = $smsParserModel->find($transactionId);
        }

        if (!$transactionId && !$queueTransactionId) {
            $cybersourceNotificationModel->where('id', $cybersourceNotificationId)->delete();
            
            log_message('error', '[Cybersource IPN] Failed to create transaction - ' . $data['full_payload']);
            return $this->fail(['status' => 'error', 'message' => 'Failed to create transaction']);
        }

        if (is_object($transactionDetails) && is_numeric($transactionDetails->account_number) && $bankAccountDetails->merchant_id && !$queueTransactionId) {
            try {
                PushMerchantTransactionNotificationAction::run(
                    $bankAccountDetails, 
                    $transactionDetails,
                    $merchantConfig->enabledPushTransactionNotificationOffload
                );
            } catch (\Exception $e) {
                log_message('error', '[Cybersource IPN] PushMerchantTransactionNotificationAction failed: ' . $e->getMessage() . ' - ' . $e->getTraceAsString());
            }
        }

        if (is_object($transactionDetails) && is_numeric($transactionDetails->account_number) && !$bankAccountDetails->merchant_id) {
            try {
                PushMobileTransactionNotificationQueueAction::run($transactionDetails, $bankAccountDetails, null);
            } catch (\Exception $e) {
                log_message('error', '[Cybersource IPN] PushMobileTransactionNotificationQueueAction failed: ' . $e->getMessage() . ' - ' . $e->getTraceAsString() . ' - ' . json_encode($transactionDetails));
            }
        }
        
        $companyCheck = $companyModel->select("tb_autopay_company.id")
            ->join("tb_autopay_company_subscription", "tb_autopay_company.id=tb_autopay_company_subscription.company_id")
            ->join("tb_autopay_bank_account", "tb_autopay_bank_account.company_id=tb_autopay_company.id")
            ->where([
                'tb_autopay_bank_account.id' => $bankAccountDetails->id, 
                'tb_autopay_company_subscription.status' => 'Active',
                'tb_autopay_company.status' => 'Active', 'tb_autopay_company.active' => 1
            ])->get()->getResult();
        
        if (count($companyCheck) == 1) {
            $webhookQueuable = $cybersourceConfig->notifyEnabledWebhookOffload ?? false;
            $forceWebhookManually = ['webhook' => false, 'sapo' => false, 'haravan' => false, 'shopify' => false, 'googlesheet' => false,'output_device' => false];
            $rabbitmq = null;

            if ($webhookQueuable) {
                $rabbitmq = new \App\Libraries\RabbitMQClient;
                $webhookQueuable = $rabbitmq->connect();
            }

            if ($rabbitmq && $webhookQueuable) {
                $msg = new AMQPMessage(
                    json_encode(['account_number' => $transactionDetails->account_number, 'parser_id' => $transactionId]),
                    array('delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT)
                );

                // webhooks
                try {
                    $rabbitmq->queueDeclare('webhook');
                    $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('webhook', 'name'));
                } catch (\Exception $e) {
                    log_message('error', '[Cybersource IPN] Webhook queue failed: ' . $e->getMessage());
                    $forceWebhookManually['webhook'] = true;
                }
                
                // sapo
                try {
                    $rabbitmq->queueDeclare('sapo');
                    $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('sapo', 'name'));
                } catch (\Exception $e) {
                    log_message('error', '[Cybersource IPN] Sapo queue failed: ' . $e->getMessage());
                    $forceWebhookManually['sapo'] = true;
                }

                // haravan
                try {
                    $rabbitmq->queueDeclare('haravan');
                    $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('haravan', 'name'));
                } catch (\Exception $e) {
                    log_message('error', '[Cybersource IPN] Haravan queue failed: ' . $e->getMessage());
                    $forceWebhookManually['haravan'] = true;
                } 

                // shopify
                try {
                    $rabbitmq->queueDeclare('shopify');
                    $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('shopify', 'name'));
                } catch (\Exception $e) {
                    log_message('error', '[Cybersource IPN] Shopify queue failed: ' . $e->getMessage());
                    $forceWebhookManually['shopify'] = true;
                }

                // googlesheet
                try {
                    $rabbitmq->queueDeclare('googlesheet');
                    $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('googlesheet', 'name'));
                } catch (\Exception $e) {
                    log_message('error', '[Cybersource IPN] GoogleSheet queue failed: ' . $e->getMessage());
                    $forceWebhookManually['googlesheet'] = true;
                } 

                // output device
                try {
                    $rabbitmq->queueDeclare('output_device');
                    $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('output_device', 'name'));
                } catch (\Exception $e) {
                    log_message('error', '[Cybersource IPN] output device queue failed: ' . $e->getMessage());
                    $forceWebhookManually['output_device'] = true;
                } 

                $rabbitmq->close();
            } 
            
            if (!$webhookQueuable || $forceWebhookManually['webhook']) {
                // webhooks
                $webhooksModel = model(WebhooksModel::class);
                $webhooksModel->doWebhooks($transactionDetails->account_number, $transactionId);
            }

            if (!$webhookQueuable || $forceWebhookManually['sapo']) {
                // sapo
                $sapoModel = model(SapoModel::class);
                $sapoModel->doWebhooks($transactionDetails->account_number, $transactionId);
            }

            if (!$webhookQueuable || $forceWebhookManually['haravan']) {
                // haravan
                $haravanModel = model(HaravanModel::class);
                $haravanModel->doWebhooks($transactionDetails->account_number, $transactionId);
            }

            if (!$webhookQueuable || $forceWebhookManually['shopify']) {
                // shopify
                ExecuteShopifyWebhooksAction::run($transactionId);
            }

            if (!$webhookQueuable || $forceWebhookManually['googlesheet']) {
                // googlesheet
                ExecuteGoogleSheetsWebhooksAction::run($transactionId);
            }
            
            if (!$webhookQueuable || $forceWebhookManually['webhook']) {
                // telegram queue
                $notificationTelegramModel = model(NotificationTelegramModel::class);
                $notificationTelegramModel->checkAndAddQueue($transactionId);
                
                // lark messenger queue
                $notificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);
                $notificationLarkMessengerModel->checkAndAddQueue($transactionId);
            }

             // output device
             if (!$webhookQueuable || $forceWebhookManually['output_device']) {
                // output device queue
                ExecuteOutputDevice::run($transactionId,0);
            }

            //counter
            $counterModel = model(CounterModel::class);
            $counterModel->transaction($companyCheck[0]->id, false, $transactionDetails->amount_in, $transactionDetails->amount_out);

            // cashflow
            $bankAccountCashFlowModel = model(BankAccountCashflowModel::class);
            $bankAccountCashFlowModel->amount_update($companyCheck[0]->id, date('Y-m-d', strtotime($transactionDetails->transaction_date)), $transactionDetails->bank_account_id, $transactionDetails->amount_in, $transactionDetails->amount_out, $transactionDetails->sub_account, $transactionDetails->accumulated);

            $lastTransaction = $transactionDetails;

            if (is_object($lastTransaction))
                $bankAccountModel
                    ->set(['last_transaction' => $lastTransaction->transaction_date, 'accumulated' => $lastTransaction->accumulated])
                    ->where(['id' => $bankAccountDetails->id])
                    ->update();
        }
    }
    
    protected function handleCallbackForSePay(\App\Features\PaymentGateway\Cybersource $paymentGateway): array
    {
        helper(['general']);
        
        $referenceNumber = trim($this->request->getVar('req_reference_number'));
        
        if (!preg_match('/^([0-9]+)\_SEP([0-9]+)\_([0-9]+)$/', $referenceNumber)) {
            throw new \Exception('Invalid reference number');
        }
        
        if (!$referenceNumber) {
            throw new \Exception('Reference number is required');
        }
        
        $parts = explode('_', $referenceNumber);
        $id = $parts[0];
        $invoiceId = $parts[1];
        
        $userModel = model(UserModel::class);
        $user = $userModel->find($id);
        
        if (! $user) {
            throw new \Exception('User not found');
        }
        
        auth()->login($user);
        
        return ['invoice_id' => str_replace('SEP', '', $invoiceId)];
    }
}
