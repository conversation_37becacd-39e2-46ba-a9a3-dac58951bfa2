<?php

namespace App\Controllers\Coreapi;

use App\Actions\BankAccount\FilterTransactionAction;
use Exception;
use App\Models\SapoModel;
use CodeIgniter\Controller;
use App\Models\CompanyModel;
use App\Models\CounterModel;
use App\Models\HaravanModel;
use App\Models\WebhooksModel;
use App\Models\SmsParserModel;
use App\Models\BankAccountModel;
use App\Models\TransactionsModel;
use App\Models\ConfigurationModel;
use CodeIgniter\API\ResponseTrait;
use App\Models\BankSubAccountModel;
use PhpAmqpLib\Message\AMQPMessage;
use App\Models\BankAccountCashflowModel;
use App\Models\NotificationTelegramModel;
use CodeIgniter\RESTful\ResourceController;
use App\Models\NotificationLarkMessengerModel;
use App\Models\NotificationTelegramQueueModel;
use App\Actions\Shopify\ExecuteShopifyWebhooksAction;
use App\Actions\GoogleSheets\ExecuteGoogleSheetsWebhooksAction;
use App\Actions\Transaction\CreateTransactionQueueAction;
use App\Actions\Merchant\PushMerchantTransactionNotificationAction;
use App\Actions\Mobile\PushMobileTransactionNotificationQueueAction;
use App\Actions\PayCodeDetector;

use App\Actions\OutputDevice\ExecuteOutputDevice;

class Ocb extends ResourceController
{
    use ResponseTrait;
 
    public function notifyTransaction() {

        if ($this->request->getMethod(true) != 'POST')
            return $this->failNotFound();

        helper(['general']);

        $config = config(\Config\Ocb::class);
        $merchantConfig = config(\Config\Merchant::class);
        $merchantConfig->enabledTransactionInsertionOffload = $merchantConfig->enabledTransactionInsertionOffload ?? false; 
        $merchantConfig->enabledPushTransactionNotificationOffload = $merchantConfig->enabledPushTransactionNotificationOffload ?? false; 

        $accessCode = $config->OcbAccessCode;

        $data = [
            'tranSeq' => $this->request->getVar('tranSeq'),
            'vaNumber' => $this->request->getVar('vaNumber'),
            'toAccountNumber' => $this->request->getVar('toAccountNumber'),
            'tranAmount' => $this->request->getVar('tranAmount'),
            'tranRemark' => $this->request->getVar('tranRemark'),
            'tranDate' => $this->request->getVar('tranDate'),
            'signature' => $this->request->getVar('signature')
        ];

        $rules = [
            'tranSeq' => ['required', 'string'],
            'vaNumber' => ['required', 'string'],
            'toAccountNumber' => ['required', 'string'],
            'tranAmount' => ['required', 'integer'],
            'tranRemark' => ['required', 'string'],
            'tranDate' => ['required', 'regex_match[/^[0-9]{12}$/]'],
            'signature' => ['required', 'string']
        ];

	    if (! $this->validateData($data, $rules)) {
	        log_message('error', "OCB Notification Error: Validate: " . serialize($this->validator->getErrors()));
            return $this->respond([
                'code' => 400,
                'message' => 'Bad Request',
                'errors' => $this->validator->getErrors()
            ], 400);
        }

        $ocbNotificationRawModel = model(\App\Models\OcbNotificationRawModel::class);
        $raw_id = $ocbNotificationRawModel->insert(['payload' => json_encode($data), 'ip' =>$this->request->getIPAddress()]);

        $signaturePlainData = "{$accessCode}|{$data['tranSeq']}|{$data['tranDate']}|{$data['vaNumber']}|{$data['tranAmount']}|{$data['toAccountNumber']}|{$data['tranRemark']}";

        $publicKey = openssl_pkey_get_public(file_get_contents($config->OcbCertPath));

        try {
            $signatureIsValid = openssl_verify($signaturePlainData, hex2bin($data['signature']), $publicKey, 'sha256WithRSAEncryption');
        } catch (\Exception $e) {
            $signatureIsValid = false;
        }

        if (!$signatureIsValid) {
	        log_message('error', "OCB Notification Error: Signature Invalid");
            return $this->respond([
                'code' => 401,
                'message' => 'Signature Invalid'
            ], 401);
        }

        $ocbNotificationModel = model(\App\Models\OcbNotificationModel::class);

        $duplicatedTransaction = $ocbNotificationModel->where('tranSeq', $data['tranSeq'])->countAllResults();

        if ($duplicatedTransaction) {
	        log_message('error', "OCB Notification Error: Transaction Duplicated");
            return $this->respond([
                'code' => 409,
                'message' => 'Transaction Duplicated'
            ], 409);
        }

        $db = \Config\Database::connect();

        $bankAccountModel = model(BankAccountModel::class);
        $bank_account_details = $bankAccountModel->where([
            'account_number' => $data['toAccountNumber'],
            'bank_id' => 18,
            'active' => true,
        ])->get()->getRow();

        if(!is_object($bank_account_details)) {
	        log_message('error', "OCB Notification Error: Account number does not exists in SePay");
            return $this->respond([
                'code' => 404,
                'message' => 'Account number does not exists in SePay'
            ], 404);
        }

        $bankSubAccountModel = model(BankSubAccountModel::class);
        $bankSubAccountDetails = $bankSubAccountModel->where([
            'sub_account' => $data['vaNumber'],
            'bank_account_id' => $bank_account_details->id,
        ])->get()->getRow();

        if(!is_object($bankSubAccountDetails)) {
	        log_message('error', "OCB Notification Error: VA number does not exists in SePay");
            return $this->respond([
                'code' => 404,
                'message' => 'VA number does not exists in SePay'
            ], 404);
        }

        $db->transBegin();

        $notificationId = $ocbNotificationModel->insert([
            'raw_id' => $raw_id,
            'tranSeq' => $data['tranSeq'],
            'vaNumber' => $data['vaNumber'],
            'toAccountNumber' => $data['toAccountNumber'],
            'tranAmount' => $data['tranAmount'],
            'tranRemark' => $data['tranRemark'],
            'tranDate' => $data['tranDate'],
        ]);

        $canSyncAccumulated = true;

        if (($config->enableFilterTransaction ?? false) && ! $bank_account_details->merchant_id) {
            $filterTransaction = FilterTransactionAction::forBankAccount($bank_account_details->id)
                ->amountIn(true)
                ->description($data['tranRemark'])
                ->payload($data)
                ->filter();

            if ($filterTransaction['isRejected']) {
                return $this->respond([
                    'code' => 200,
                    'message' => 'OK',
                ]);
            }

            $canSyncAccumulated = $filterTransaction['canSyncAccumulated'];
        }

        $smsParserModel = model(SmsParserModel::class);
        $companyModel = model(CompanyModel::class);
        $transactionsModel = model(TransactionsModel::class);

        $transaction_content = $data['tranRemark'];
        // payment code
        $code = NULL;

        if(is_object($bank_account_details)) {
            $code = PayCodeDetector::getCode($transaction_content, $bank_account_details->company_id);
        }

        $transactionsModel = model(TransactionsModel::class);
        $accumulated = 0;

        // Only calculate accumulated when OCB API bank account was assigned a SIM
        $lastAccumulatedKey = 'sepay_last_accumulated_bank_account_' . $bank_account_details->id;
        $cache = service('cache');

        if ($bank_account_details->bank_sms_connected) {
            $lastAccumulated = $cache->get($lastAccumulatedKey);
            
            if ($lastAccumulated === 0) {
                $accumulated = 0;
            } else if (!is_null($lastAccumulated)) {
                $accumulated = $lastAccumulated + $data['tranAmount'];
            } else {
                $last_trans = $transactionsModel->where([
                    'account_number' => $data['toAccountNumber'], 
                    'bank_account_id' => $bank_account_details->id,
                ])->orderBy('transaction_date', 'desc')->first();
        
                if (is_object($last_trans)) {
                    $accumulated = $last_trans->accumulated + $data['tranAmount'];
                }
            }
        }

        $transaction_data = [
            "sms_id" => $notificationId,
            "gateway" => "OCB",
            "transaction_date" => \DateTime::createFromFormat("ymdHis", $data['tranDate'])->format('Y-m-d H:i:s'),
            "account_number" => $data['toAccountNumber'],
            "sub_account" => $data['vaNumber'],
            "amount_in" => $data['tranAmount'],
            "amount_out" => 0,
            "accumulated" => $canSyncAccumulated ? $accumulated : 0,
            "code" => $code,
            "transaction_content" => $transaction_content,
            "reference_number" => $data['tranSeq'],
            "source" => "BankAPINotify",
            "body" => "BankAPINotify " . $data['tranRemark'],
            "parser_status" => "Success",
            "from_account_number" => $data['toAccountNumber'],
            'bank_account_id' => $bank_account_details->id,
            'merchant_id' => $bank_account_details->merchant_id,
            'transaction_id' => uuid(),
        ];

        $queueTransactionId = null;
        $parserId = null; // primary id of tb_autopay_sms_parsed table
        $transaction_details = null;

        if ($bank_account_details->merchant_id && $merchantConfig->enabledTransactionInsertionOffload) {
            $queueTransactionId = CreateTransactionQueueAction::run((object) $transaction_data, $bank_account_details);
        }

        if (!$queueTransactionId) {
            $parser_result = $smsParserModel->insert($transaction_data);
            $transaction_details = $smsParserModel->find($parser_result);
        }

        $cache->save($lastAccumulatedKey, $accumulated, 0);

        if ($db->transStatus() === false) {
            $db->transRollback();
	        log_message('error', "OCB Notification Error: Hook failed, please send again");
            return $this->respond([
                'code' => 503,
                'message' => 'Hook failed, please send again'
            ], 503);
        } else {
            $db->transCommit();
        }

        if (is_object($transaction_details) && is_numeric($transaction_details->account_number) && $bank_account_details->merchant_id && !$queueTransactionId) {
            try {
                PushMerchantTransactionNotificationAction::run(
                    $bank_account_details, 
                    $transaction_details,
                    $merchantConfig->enabledPushTransactionNotificationOffload
                );
            } catch (Exception $e) {
                log_message('error', '[PushableMerchantTransactionNotificationAction] ' . $e->getMessage() . ' - ' . $e->getTraceAsString());
            }
        }

        if(is_object($transaction_details) && is_numeric($transaction_details->account_number) && !$bank_account_details->merchant_id) {
            // check company and subscription status
            $company_check = $companyModel->select("tb_autopay_company.id")
                ->join("tb_autopay_company_subscription","tb_autopay_company.id=tb_autopay_company_subscription.company_id")
                ->join("tb_autopay_bank_account","tb_autopay_bank_account.company_id=tb_autopay_company.id")
                ->where(['tb_autopay_bank_account.id' => $bank_account_details->id, 'tb_autopay_company_subscription.status'=>'Active','tb_autopay_company.status' => 'Active', 'tb_autopay_company.active' => 1])->get()->getResult();
            
            if(count($company_check) == 1) {
                try {
                    PushMobileTransactionNotificationQueueAction::run($transaction_details, $bank_account_details, $bankSubAccountDetails);
                } catch (Exception $e) {
                    log_message('error', 'OCB PushMobileTransactionNotificationQueueAction failed: ' . $e->getMessage() . ' - ' . $e->getTraceAsString() . ' - ' . json_encode($transaction_details));
                }

                $webhookQueuable = $config->enableOCBWebhookOffload;
                $forceWebhookManually = ['webhook' => false, 'sapo' => false, 'haravan' => false, 'shopify' => false, 'googlesheet' => false,'output_device' => false, 'viber' => false];
                $rabbitmq = null;

                if ($webhookQueuable) {
                    $rabbitmq = new \App\Libraries\RabbitMQClient;
                    $webhookQueuable = $rabbitmq->connect();
                }

                if ($rabbitmq && $webhookQueuable) {
                    $msg = new AMQPMessage(
                        json_encode(['account_number' => $transaction_details->account_number, 'parser_id' => $parser_result]),
                        array('delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT)
                    );

                    // webhooks
                    try {
                        $rabbitmq->queueDeclare('webhook');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('webhook', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'OCB API webhook queue failed: ' . $e->getMessage());
                        $forceWebhookManually['webhook'] = true;
                    }
                    
                    // sapo
                    try {
                        $rabbitmq->queueDeclare('sapo');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('sapo', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'OCB API Sapo queue failed: ' . $e->getMessage());
                        $forceWebhookManually['sapo'] = true;
                    }

                    // haravan
                    try {
                        $rabbitmq->queueDeclare('haravan');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('haravan', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'OCB API Haravan queue failed: ' . $e->getMessage());
                        $forceWebhookManually['haravan'] = true;
                    } 

                    // shopify
                    try {
                        $rabbitmq->queueDeclare('shopify');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('shopify', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'OCB API Shopify queue failed: ' . $e->getMessage());
                        $forceWebhookManually['shopify'] = true;
                    } 

                    //googlesheet
                    try {
                        $rabbitmq->queueDeclare('googlesheet');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('googlesheet', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'OCB API GoogleSheet queue failed: ' . $e->getMessage());
                        $forceWebhookManually['googlesheet'] = true;
                    }
                    
                     // output device
                    try {
                        $rabbitmq->queueDeclare('output_device');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('output_device', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'Ocb API output device queue failed: ' . $e->getMessage());
                        $forceWebhookManually['output_device'] = true;
                    } 
                    
                    // viber
                    try {
                        $rabbitmq->queueDeclare('viber');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('viber', 'name'));
                    } catch (\Exception $e) {
                        log_message('error', 'OCB API Viber queue failed: ' . $e->getMessage());
                        $forceWebhookManually['viber'] = true;
                    }

                    $rabbitmq->close();
                } 
                
                if (!$webhookQueuable || $forceWebhookManually['webhook']) {
                    // webhooks
                    $webhooksModel = model(WebhooksModel::class);
                    $webhooksModel->doWebhooks($transaction_details->account_number, $parser_result);
                }

                if (!$webhookQueuable || $forceWebhookManually['sapo']) {
                    // sapo
                    $sapoModel = model(SapoModel::class);
                    $sapoModel->doWebhooks($transaction_details->account_number, $parser_result);
                }

                if (!$webhookQueuable || $forceWebhookManually['haravan']) {
                    // haravan
                    $haravanModel = model(HaravanModel::class);
                    $haravanModel->doWebhooks($transaction_details->account_number, $parser_result);
                }

                if (!$webhookQueuable || $forceWebhookManually['shopify']) {
                    // shopify
                    ExecuteShopifyWebhooksAction::run($parser_result);
                }

                if (!$webhookQueuable || $forceWebhookManually['googlesheet']) {
                    // googlesheet
                    ExecuteGoogleSheetsWebhooksAction::run($parser_result);
                }
                
                if (!$webhookQueuable || $forceWebhookManually['webhook']) {
                    // telegram queue
                    $notificationTelegramModel = model(NotificationTelegramModel::class);
                    $notificationTelegramModel->checkAndAddQueue($parser_result);
                    
                    // lark messenger queue
                    $notificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);
                    $notificationLarkMessengerModel->checkAndAddQueue($parser_result);
                }

                // output device
                if (!$webhookQueuable || $forceWebhookManually['output_device']) {
                    // output device queue
                    ExecuteOutputDevice::run($parser_result,0);
                }
                
                if (!$webhookQueuable || $forceWebhookManually['viber']) {
                    // viber
                    (new \App\Features\Viber\ViberFeature)->sendNotificationByTransactionId($parser_result);
                }

               

                //counter
                $counterModel = model(CounterModel::class);
                $counterModel->transaction($company_check[0]->id,FALSE,$transaction_details->amount_in, $transaction_details->amount_out);

                // cashflow
                $bankAccountCashFlowModel = model(BankAccountCashflowModel::class);
                $bankAccountCashFlowModel->amount_update($company_check[0]->id, date('Y-m-d', strtotime($transaction_details->transaction_date)), $transaction_details->bank_account_id, $transaction_details->amount_in, $transaction_details->amount_out, $transaction_details->sub_account,$transaction_details->accumulated);

                $last_trans = $transaction_details;

                if(is_object($last_trans))
                    $bankAccountModel->set(['last_transaction' => $last_trans->transaction_date, 'accumulated' => $last_trans->accumulated])->where(['id' => $bank_account_details->id])->update();
            }
        }
 
        return $this->respond([
            'code' => 200,
            'message' => 'OK',
        ]);
    }
}
