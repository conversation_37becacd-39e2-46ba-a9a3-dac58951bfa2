<?php

namespace App\Controllers;
use App\Models\BankAccountModel;
use App\Models\BankSubAccountModel;
use App\Models\TransactionsModel;
use App\Models\SimModel;
use App\Models\UserModel;

use CodeIgniter\Controller;
use App\Models\VietinbankEnterpriseAccountModel;
use App\Models\OutputDeviceDecalModel;

class Banksubaccount extends BaseController
{
    public function index()
    { 
        $data = [
            'page_title' => 'Ngân hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        if(!has_permission('BankAccount', 'can_view_all'))
            show_404();

        $bankSubAccountModel = model(BankSubAccountModel::class);
        $bankAccountModel = model(BankAccountModel::class);
        $data['bank_accounts'] = $bankAccountModel->get_bank_account_company($this->user_session['company_id']);

        $data['is_enable'] = get_configuration('BankSubAccount');
        $data['parrent_id'] = $this->request->getGet('parrent_id');

        if(!is_numeric($data['parrent_id']))
            $data['parrent_id'] = FALSE;

        echo view('templates/autopay/header',$data);
        echo view('banksubaccount/index',$data);
        echo view('templates/autopay/footer',$data);

    }

     
    public function ajax_va_list() {

        if(!has_permission('BankAccount', 'can_view_all'))
            show_404();
     
        if ($this->request->getMethod(true) != 'POST')
            return '';

        $parrent_account_id = $this->request->getGet('parrent_id');

        if(!is_numeric($parrent_account_id))
            $parrent_account_id = FALSE;
 
        $bankSubAccountModel = model(BankSubAccountModel::class);

        //$result = $bankSubAccountModel->where(['id' => ])

        $bank_accounts = $bankSubAccountModel->getDatatables($this->user_session['company_id'], $parrent_account_id);
         
        $data = array();

        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');

        $can_edit = has_permission('BankAccount', 'can_edit');
        $can_delete = has_permission('BankAccount', 'can_delete');
 
        foreach ($bank_accounts as $account) {

            $no++;
            $row = array();

            $actions_btn = '';

            if($can_edit)
                $actions_btn = "<a href='javascript:;' onclick='edit_va(" . $account->id. ")' class='btn btn-sm btn-outline-warning ms-2 me-2 mt-2'><i class='bi bi-pencil'></i> Sửa</a>";
            
            if($can_delete && $account->acc_type == "Virtual")               
                $actions_btn = $actions_btn .  "<a href='javascript:;' onclick='delete_va(" . $account->id. ")' class='btn btn-sm btn-outline-danger ms-2 mt-2'><i class='bi bi-trash3'></i> Xóa</a>";

 
            
            $row[] = $no;
            $row[] = $account->id;
            $row[] = "<a href='javascript:;' onclick='view_va(" . $account->id . ")'>" . esc($account->sub_account) . "</a>";

            $row[] = esc($account->label);
            

            $row[] = esc($account->created_at);
            $row[] = $actions_btn;
       
            $data[] = $row;
        }
 
        $output = array(
            "draw" => $draw,
            "recordsTotal" => $bankSubAccountModel->countAll($this->user_session['company_id'], $parrent_account_id),
            "recordsFiltered" => $bankSubAccountModel->countFiltered($this->user_session['company_id'], $parrent_account_id),
            "data" => $data,
        );
        return $this->response->setJSON($output);
    
    
    }

    public function ajax_va_add() {

        if ($this->request->getMethod(true) != 'POST')
            return '';


        if(!has_permission('BankAccount', 'can_add'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền thêm tài khoản ngân hàng"));
 
        $validation =  \Config\Services::validation();

        helper('text');
        
        $rules = [
            'bank_account_id' => ['label' => 'Tài khoản chính', 'rules' => 'integer|is_natural'],
            'sub_account' => ['label' => 'Số tài khoản ảo', 'rules' => "required|min_length[2]|max_length[22]"],
            'label' => ['label' => 'Tên gợi nhớ', 'rules' => "max_length[100]"],
        ];
        
        if (is_speaker_billing_subscription()) {
            $rules['sub_account'] = ['label' => 'Số tài khoản ảo', 'rules' => "required|regex_match[/^[A-Z0-9]{4}$/i]"];
        }

        if(! $this->validate($rules,
        [
            'bank_account_id' => [
                'integer'=>'Bạn chưa chọn tài khoản chính',
            ],
            'acc_type' => 'Loại tài khoản ảo phải chọn là Thật hoặc Ảo',
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }  

        $bankAccountModel = model(BankAccountModel::class);

        $results = $bankAccountModel->get_bank_account_company($this->user_session['company_id']);
        $bank_accounts = [];
        foreach($results as $result) {
            array_push($bank_accounts,$result->id);
        }

        $bank_account_id = trim(xss_clean($this->request->getVar('bank_account_id')));
        $sub_account = trim(xss_clean($this->request->getVar('sub_account')));

        if(!in_array($bank_account_id, $bank_accounts))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Tài khoản chính không tồn tại"));

        if (is_speaker_billing_subscription()) {
            $sub_account = strtoupper('L' . $sub_account);
        }
        
        $sub_account = strtoupper($sub_account);
            
        $bankSubAccountModel = model(BankSubAccountModel::class);

        $result = $bankSubAccountModel->join("tb_autopay_bank_account","tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id")->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id'], 'tb_autopay_bank_sub_account.sub_account'=>$sub_account])->get()->getRow();

        if(is_object($result))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Tài khoản ảo này đã tồn tại. Bạn đã tạo tài khoản ảo này rồi."));
            
        $result2 = $bankSubAccountModel->where(['tb_autopay_bank_sub_account.sub_account'=>$sub_account])->get()->getRow();

        if(is_object($result2))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Tài khoản ảo này đã tồn tại trên hệ thống. Vui lòng chọn tên khác."));
            
        if (!is_speaker_billing_subscription() && preg_match('/^L[a-zA-Z0-9]+$/i', $sub_account)) {
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Tài khoản ảo này đã tồn tại trên hệ thống. Vui lòng chọn tên khác."));
        }
        
        $outputDeviceDecalModel = model(OutputDeviceDecalModel::class);
        
        if ($outputDeviceDecalModel->where('virtual_account_number', $sub_account)->countAllResults() > 0) {
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Tài khoản ảo này đã tồn tại trên hệ thống. Vui lòng chọn tên khác."));
        }

        $bank_account_details = $bankAccountModel->where(['id' => $bank_account_id, 'company_id' => $this->user_session['company_id']])->get()->getRow();
        
        if(!is_object($bank_account_details))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Tài khoản chính không tồn tại."));

        $data = array(
            'sub_account' =>  $sub_account,
            'bank_account_id' => $bank_account_id,
            'label' => trim(xss_clean($this->request->getVar('label'))),
            'acc_type' => 'Virtual',
        );

        $result = $bankSubAccountModel->insert($data);
        
        if($result) {
            return $this->response->setJSON(array("status"=>true, 'id' => $result));
        } else {
            return $this->response->setJSON(array("status"=>false,"message"=>"Không thể thêm tài khoản ảo này. Vui lòng liên hệ SePay để được hỗ trợ."));
        }
            

    
    }

     


    public function ajax_get_va($id='') {
        
        if(!is_numeric($id)) 
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"ID tài khoản ngân hàng không hợp lệ"));

        if(!has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền sửa tài khoản ngân hàng"));
 
        
        $bankSubAccountModel = model(BankSubAccountmodel::class);
        
        $result = $bankSubAccountModel->select("tb_autopay_bank_sub_account.id,tb_autopay_bank_sub_account.sub_account,tb_autopay_bank_sub_account.acc_type,tb_autopay_bank_sub_account.bank_account_id,tb_autopay_bank_sub_account.label,tb_autopay_bank_sub_account.sub_holder_name")->join("tb_autopay_bank_account","tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id")->where(['tb_autopay_bank_sub_account.id'=>$id, 'tb_autopay_bank_account.company_id' => $this->user_session['company_id']])->get()->getRow();
          
        if($result)
            return $this->response->setJSON(["status"=>TRUE, "data"=>$result]);
        else
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy tài khoản ảo này"));
    }

    public function ajax_view_va($id='') {
        
      
        if(!has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền sửa tài khoản ngân hàng"));
        
        if(!is_numeric($id)) 
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"ID tài khoản ngân ảo hợp lệ"));

        $bankSubAccountModel = model(BankSubAccountmodel::class);
        $transactionsModel = slavable_model(TransactionsModel::class, 'Banksubaccount');

        $data['va_details'] = $bankSubAccountModel->select("tb_autopay_bank_sub_account.id,tb_autopay_bank_sub_account.sub_account,tb_autopay_bank_sub_account.acc_type,tb_autopay_bank_sub_account.bank_account_id,tb_autopay_bank_sub_account.label,tb_autopay_bank_account.account_number,tb_autopay_bank_account.label,tb_autopay_bank_account.active,tb_autopay_bank_account.created_at,tb_autopay_bank.brand_name, tb_autopay_bank_account.account_number,tb_autopay_bank_account.account_holder_name,tb_autopay_bank_account.bank_api,tb_autopay_bank_account.bank_api_connected,tb_autopay_bank.icon_path,tb_autopay_bank.logo_path,tb_autopay_bank.full_name,tb_autopay_bank_sub_account.va_active, tb_autopay_bank_sub_account.sub_holder_name,tb_autopay_bank_account.bank_id,tb_autopay_bank.code as bank_code")->join("tb_autopay_bank_account","tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id")->join("tb_autopay_bank","tb_autopay_bank.id=tb_autopay_bank_account.bank_id")->where(['tb_autopay_bank_sub_account.id'=>$id, 'tb_autopay_bank_account.company_id' => $this->user_session['company_id']])->get()->getRow();

        $data['count_transactions'] = $transactionsModel->where(['sub_account' => $data['va_details']->sub_account])->countAllResults();

        $data['last_transaction'] = $transactionsModel->where(['sub_account' => $data['va_details']->sub_account, 'accumulated!=' => ''])->orderBy('transaction_date','DESC')->get()->getRow();

        if($data['va_details']) {
            if($data['va_details']->acc_type == 'Virtual' && $data['va_details']->bank_api != 1) 
                $html = view('banksubaccount/va_view', $data);
            else
                $html = view('banksubaccount/va_view_real', $data);
            return $this->response->setJSON(["status"=>TRUE, "html"=>$html]);
        }
        else
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy tài khoản ảo này"));
    }


    public function ajax_va_update() {
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        if(!has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền sửa tài khoản ảo này"));
 
        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'id' => 'required|integer|is_natural',
            'label' => ['label' => 'Tên gợi nhớ', 'rules' => "max_length[100]"],
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }   
        $bankSubAccountModel = model(BankSubAccountmodel::class);
        $sub_account_id = xss_clean($this->request->getPost('id'));

        $bankSubAccountModel = model(BankSubAccountmodel::class);
        
        $result = $bankSubAccountModel->select("tb_autopay_bank_sub_account.id,tb_autopay_bank_sub_account.sub_account,tb_autopay_bank_sub_account.acc_type,tb_autopay_bank_sub_account.bank_account_id,tb_autopay_bank_sub_account.label,tb_autopay_bank_account.bank_id")->join("tb_autopay_bank_account","tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id")->where(['tb_autopay_bank_sub_account.id'=>$sub_account_id, 'tb_autopay_bank_account.company_id' => $this->user_session['company_id']])->get()->getRow();
          
        if(!is_object($result))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy tài khoản ảo này"));

        $data = array(
            'label' => xss_clean($this->request->getVar('label')),
        );

        if($result->acc_type == 'Real' && $result->bank_id != 9) {
            $data['sub_holder_name'] = xss_clean($this->request->getVar('sub_holder_name'));
        }

            
        $result = $bankSubAccountModel->set($data)->where("id",$sub_account_id)->update();
        
        if($result) { 
            return $this->response->setJSON(array("status"=>true));
        } else {
            return $this->response->setJSON(array("status"=>false,"message"=>"Không thể cập nhật!"));
        }
            

        
    }
 

    public function ajax_va_delete() {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!has_permission('BankAccount', 'can_delete'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền xóa tài khoản ngân hàng"));
 
 
        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'id' => 'required|integer|is_natural',
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }
        $sub_account_id = $this->request->getPost('id');

        $bankSubAccountModel = model(BankSubAccountmodel::class);
        $sub_account_details = $bankSubAccountModel->select("tb_autopay_bank_sub_account.id, tb_autopay_bank_account.bank_id")->join("tb_autopay_bank_account","tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id")->where(['tb_autopay_bank_sub_account.id'=>$sub_account_id,'tb_autopay_bank_account.company_id'=>$this->user_session['company_id'],'tb_autopay_bank_sub_account.acc_type' => 'Virtual'])->get()->getRow();

        if(!is_object($sub_account_details))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Không tìm thấy tài khoản ngân hàng này"));

        if($sub_account_details->bank_id == 17 || $sub_account_details->bank_id == 18)
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Không thể xoá VA của tài khoản ngân hàng đã kết nối API. Bạn chỉ có thể vô hiệu hoá nó."));


        $bankSubAccountModel->delete($sub_account_id);
    
        return $this->response->setJSON(array("status"=>true));
         
            

    
    }
 

    public function details($id='')
    { 
        $data = [
            'page_title' => 'Ngân hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        if(!has_permission('BankAccount', 'can_add'))
            show_404();

        if(!is_numeric($id))
            show_404();

        
        $bankSubAccountModel = model(BankSubAccountmodel::class);
        $transactionsModel = slavable_model(TransactionsModel::class, 'Banksubaccount');

        $data['sub_account_details'] = $bankSubAccountModel->select("tb_autopay_bank_sub_account.id,tb_autopay_bank_sub_account.sub_account,tb_autopay_bank_sub_account.acc_type,tb_autopay_bank_sub_account.bank_account_id,tb_autopay_bank_sub_account.label,tb_autopay_bank_account.account_number,tb_autopay_bank_account.label,tb_autopay_bank_account.active,tb_autopay_bank_account.created_at,tb_autopay_bank.brand_name, tb_autopay_bank_account.account_number,tb_autopay_bank_account.account_holder_name,tb_autopay_bank.icon_path,tb_autopay_bank.logo_path,tb_autopay_bank.full_name")->join("tb_autopay_bank_account","tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id")->join("tb_autopay_bank","tb_autopay_bank.id=tb_autopay_bank_account.bank_id")->where(['tb_autopay_bank_sub_account.id'=>$id, 'tb_autopay_bank_account.company_id' => $this->user_session['company_id']])->get()->getRow();

        if(!is_object($data['sub_account_details']))
            show_404();
        
      
        $data['count_transactions'] = $transactionsModel->where(['sub_account' => $data['sub_account_details']->sub_account])->countAllResults();

        $data['last_transaction'] = $transactionsModel->where(['sub_account' => $data['sub_account_details']->sub_account, 'accumulated!=' => ''])->orderBy('transaction_date','DESC')->get()->getRow();
    
      
        echo view('templates/autopay/header',$data);
        echo view('banksubaccount/details',$data);
        echo view('templates/autopay/footer',$data);

    }



    public function qrcode($sub_account_id = '') {
        if(!is_numeric($sub_account_id))
            show_404();

        $data = [
            'page_title' => 'Ngân hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];


        if(!has_permission('BankAccount', 'can_view_all'))
            show_404();


        $bankSubAccountModel = model(BankSubAccountModel::class);

    
        $data['sub_account_details'] = $bankSubAccountModel->select("tb_autopay_bank_sub_account.id,
        tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.bank_sms_connected,
        tb_autopay_bank_sub_account.sub_account,tb_autopay_bank_sub_account.bank_account_id,tb_autopay_bank_sub_account.acc_type,tb_autopay_bank_sub_account.label as `sub_label`,tb_autopay_bank_account.account_number,tb_autopay_bank_account.label,tb_autopay_bank_account.active,tb_autopay_bank_account.created_at,tb_autopay_bank.brand_name, tb_autopay_bank_account.account_number,tb_autopay_bank_account.account_holder_name,tb_autopay_bank.icon_path,tb_autopay_bank.logo_path,tb_autopay_bank.full_name,tb_autopay_bank.id as bank_id,tb_autopay_bank.short_name,tb_autopay_bank_sub_account.sub_holder_name,tb_autopay_bank_account.bank_api as bank_api")->join("tb_autopay_bank_account","tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id")->join("tb_autopay_bank","tb_autopay_bank.id=tb_autopay_bank_account.bank_id")->where(['tb_autopay_bank_sub_account.id'=>$sub_account_id, 'tb_autopay_bank_account.company_id' => $this->user_session['company_id']])->get()->getRow();

        if(!is_object($data['sub_account_details']))
            show_404();

        $qr_url = "https://qr.sepay.vn/img?template=";
        $bank = $data['sub_account_details']->brand_name;
        $des = "";
        $acc = $data['sub_account_details']->account_number;
        if (!empty($data['sub_account_details']->id)) {
                // VA Real
            if ($data['sub_account_details']->acc_type == 'Real') {
                $acc = $data['sub_account_details']->sub_account;
            }
            // swap brand name
            if ($data['sub_account_details']->brand_name == "LPBank") {
                $bank = "LienVietPostBank";
            }
            if ($data['sub_account_details']->brand_name == "BVBank") {
                $bank = "VietCapitalBank";
            }

            // VA Virtual API
            if ($data['sub_account_details']->acc_type == 'Virtual' && $data['sub_account_details']->bank_api_connected == 1) {
                $acc = $data['sub_account_details']->account_number;
                $des = "TKP" . $data['sub_account_details']->sub_account;
            }

            // VA Virtual SMS
            if ($data['sub_account_details']->acc_type == 'Virtual' && $data['sub_account_details']->bank_sms_connected == 1) {
                $acc = $data['sub_account_details']->account_number;
                $des = "TKP" . $data['sub_account_details']->sub_account;
            }
            
            // VietinBank
            if (in_array($data['sub_account_details']->brand_name, ['VietinBank', 'ABBANK'])) {

                $acc = $data['sub_account_details']->account_number;
                $des = ($data['sub_account_details']->bank_api_connected == 1) ? "SEVQR TKP".$data['sub_account_details']->sub_account : "TKP" .$data['sub_account_details']->sub_account;

                if ($data['sub_account_details']->brand_name == 'VietinBank' && $data['sub_account_details']->bank_api == 1) {
                    $vietinbankEnterpriseAccountModel = model(VietinbankEnterpriseAccountModel::class);

                    if ($vietinbankEnterpriseAccountModel->where(['bank_account_id' => $data['sub_account_details']->id])->countAllResults()) {
                        $des = "";
                    }
                }
            }

            $data['qr_url'] = $qr_url . "&bank=" . $bank . "&acc=" . $acc . "&des=" . $des;
        } else {
            $acc = $data['sub_account_details']->account_number;
            if (in_array($data['sub_account_details']->brand_name, ['VietinBank', 'ABBANK'])) {
                $des = ($data['sub_account_details']->bank_api_connected == 1) ? "SEVQR " : "";
            }
        }

        $data['des'] = $des;

        $data['qrcode'] = $qr_url . "&bank=" . $bank . "&acc=" . $acc . "&des=" . $des;
            
        $print = trim(xss_clean($this->request->getGet('print')));
        $embed = trim(xss_clean($this->request->getGet('embed')));

        if($print == 'yes') {
            echo view('banksubaccount/qrcode_print',$data);
        } else  if($embed == 'yes') {
            echo view('templates/autopay/header',$data);
            echo view('banksubaccount/embed',$data);
            echo view('templates/autopay/footer',$data);

        } else {
            echo view('templates/autopay/header',$data);
            echo view('banksubaccount/qrcode',$data);
            echo view('templates/autopay/footer',$data);
        }

    }
 

    public function list_va($bank_account_id)
    {
        $data = [];
        if (!has_permission('BankAccount', 'can_view_all')) {
            return $this->response->setJSON(array("status" => true, "message" => "Danh sách VA", "data" => $data));
        }
    
        if (empty($bank_account_id)) {
            return $this->response->setJSON(array("status" => true, "message" => "Danh sách VA", "data" => []));
        }
    
        $query = model(BankSubAccountModel::class)
            ->select("tb_autopay_bank_sub_account.id, tb_autopay_bank_sub_account.acc_type, tb_autopay_bank.brand_name, tb_autopay_bank.icon_path, tb_autopay_bank_account.account_holder_name, tb_autopay_bank_account.account_number, tb_autopay_bank_sub_account.sub_account, tb_autopay_bank_sub_account.label")
            ->join("tb_autopay_bank_account", "tb_autopay_bank_account.id = tb_autopay_bank_sub_account.bank_account_id")
            ->join("tb_autopay_bank", "tb_autopay_bank.id = tb_autopay_bank_account.bank_id", "left")
            ->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id']])
            ->where('tb_autopay_bank_account.id', $bank_account_id);
    
        if (in_array($this->company_details->role, ['Admin', 'SuperAdmin'])) {
            $data = $query->get()->getResultArray();
        } else {
            $data = $query
                ->join("tb_autopay_user_permission_bank_sub", "tb_autopay_user_permission_bank_sub.sub_account_id = tb_autopay_bank_sub_account.id", "left")
                ->where("tb_autopay_user_permission_bank_sub.user_id", $this->user_session['user_id'])
                ->get()
                ->getResultArray();
        }
    
        return $this->response->setJSON(array("status" => true, "message" => "Danh sách VA", "data" => $data));
    }
    
}