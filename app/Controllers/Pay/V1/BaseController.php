<?php

namespace App\Controllers\Pay\V1;

use App\Features\PaymentGateway\Contexts\CheckoutContext;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\Controller;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use Psr\Log\LoggerInterface;

/**
 * @property IncomingRequest $request
 */
abstract class BaseController extends Controller
{
    use ResponseTrait;
    
    protected $helpers = ['general'];

    public function initController(RequestInterface $request, ResponseInterface $response, LoggerInterface $logger): void
    {
        parent::initController($request, $response, $logger);
    }
    
    public function logError(\Throwable $e, ?CheckoutContext $context = null): void
    {
        if ($context) {
            log_message('error', sprintf('[PAY#%s] %s | Trace ID: %s | URL: %s | Fields: %s | %s', $context->merchant->merchant_id, $e->getMessage(), $context->traceId, $this->request->getUri(), json_encode($context->getPaymentFields()), $e->getTraceAsString()));
        } else {
            log_message('error', sprintf('[PAY] %s | URI: %s | Raw body: %s | %s', $e->getMessage(), $this->request->getUri(), $this->request->getBody(), $e->getTraceAsString()));
        }
    }
}
