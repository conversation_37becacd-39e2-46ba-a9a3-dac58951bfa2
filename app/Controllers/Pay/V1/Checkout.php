<?php

namespace App\Controllers\Pay\V1;

use App\Features\PaymentGateway\Contexts\BankTransferCheckoutContext;
use App\Features\PaymentGateway\Contexts\CheckoutContext;
use App\Features\PaymentGateway\Contexts\MpgsCheckoutContext;
use App\Features\PaymentGateway\Interfaces\RealtimeOrderFulfillmentWithQueryDR;
use App\Features\PaymentGateway\Interfaces\StoreablePaymentCard;
use App\Features\PaymentGateway\PaymentGatewayFeature;
use CodeIgniter\HTTP\Response;
use Exception;

class Checkout extends BaseController
{
    public function index()
    {
        $method = $this->request->getMethod(true);
        
        if ($method !== 'GET') {
            show_404();
        }
       
        $merchant = trim($this->request->getGet('merchant'));
        $fields = $this->request->getGet();
        
        /** @var CheckoutContext $checkoutContext */
        $checkoutContext = null;
        
        try {
            $paymentGatewayFeature = new PaymentGatewayFeature;
            $paymentGatewayFeature->withCheckoutContext($merchant, $this->request->getGet('payment_method'));
            $checkoutContext = $paymentGatewayFeature->checkoutContext();
            $checkoutContext->loadPaymentFields($fields);
            
            $checkoutContext->loadPgCustomer();
            $checkoutContext->loadPgOrder();
            
            return $checkoutContext->renderCheckoutView();
        } catch (\Throwable $e) {
            $this->logError($e, $checkoutContext);
            
            if ($e->getCode() === 400) {
                return view('pay/v1/error/bad-request', ['fields' => $fields]);
            }
            
            return view('pay/v1/error/internal-error', ['fields' => $checkoutContext ? $checkoutContext->getPaymentFields() : $fields, 'context' => $checkoutContext]);
        }
    }
    
    public function cancel(): Response
    {
        $merchant = trim($this->request->getJsonVar('merchant'));
        $paymentMethod = trim($this->request->getJsonVar('payment_method'));
        $fields = $this->request->getJSON(true);
        
        try {
            $paymentGatewayFeature = new PaymentGatewayFeature;
            $paymentGatewayFeature->withCheckoutContext($merchant, $paymentMethod);
            $checkoutContext = $paymentGatewayFeature->checkoutContext();
            $checkoutContext->loadPaymentFields($fields);
            $checkoutContext->loadPgSession();
            
            $checkoutContext->markSessionAsCancelled();
        } catch (\Throwable $e) {
            $this->logError($e);
            $this->failServerError('Đã có lỗi xảy ra, vui lòng liên hệ để được hỗ trợ.');
        }
        
        return $this->respondNoContent();
    }
    
    public function requestFulfill(): string
    {
        $merchant = trim($this->request->getGet('merchant'));
        $paymentMethod = trim($this->request->getGet('payment_method'));
        $nonce = trim($this->request->getGet('nonce'));
        $fields = $this->request->getGet();
        
        if (!$nonce) {
            return view('pay/v1/error/bad-request', ['fields' => $fields]);
        }
        
        /** @var CheckoutContext $checkoutContext */
        $checkoutContext = null;
        
        try {
            $paymentGatewayFeature = new PaymentGatewayFeature;
            $paymentGatewayFeature->withCheckoutContext($merchant, $paymentMethod);
            $checkoutContext = $paymentGatewayFeature->checkoutContext();
            $checkoutContext->loadPaymentFields($fields);
            $checkoutContext->loadPgSession();
            
            if (!$checkoutContext->pgSession) {
                return view('pay/v1/error/bad-request', ['fields' => $checkoutContext->getPaymentFields()]);
            }
            
            if ($checkoutContext instanceof RealtimeOrderFulfillmentWithQueryDR) {
                $checkoutContext->requestOrderFulfillment();
            }
            
            echo '<script>window.parent.document.getElementById("iframe").remove();</script>';
            return '';
        } catch (\Throwable $e) {
            $this->logError($e);
            
            return view('pay/v1/error/internal-error', ['fields' => $checkoutContext->getPaymentFields(), 'context' => $checkoutContext]);
        }
    }
    
    public function sync()
    {
        $merchant = trim($this->request->getGet('merchant'));
        $queryParams = $this->request->getGet();
        
        $paymentGatewayFeature = new PaymentGatewayFeature;
        $paymentGatewayFeature->withCheckoutContext($merchant, $this->request->getGet('payment_method'));
        $checkoutContext = $paymentGatewayFeature->checkoutContext();
        $checkoutContext->loadPaymentFields($queryParams);
        $checkoutContext->loadPgSession();
        $checkoutContext->loadPgCustomer();
        $checkoutContext->loadPgOrder();
        $additionalResponseFields = [];
        
        if ($checkoutContext->isSessionInitiated() && $checkoutContext instanceof RealtimeOrderFulfillmentWithQueryDR) {
            $checkoutContext->requestOrderFulfillment();
        }
        
        return $this->respond(array_merge([
            'session_status' => $checkoutContext->pgSession->status,
            'order_status' => $checkoutContext->pgOrder ? $checkoutContext->pgOrder->order_status : null,
            'session_meta' => $checkoutContext->getSessionMeta(),
            'fulfilled' => $checkoutContext->isFulfilledCheckout(),
        ], $additionalResponseFields));
    }
    
    public function init()
    {
        $rawBody = $this->request->getRawInput(true);
        
        if (! is_array($rawBody)) {
            show_404();
        }
        
        $rules = [
            'merchant' => ['required', 'string'],
            'payment_method' => ['required', 'string', 'in_list[BANK_TRANSFER,CARD]'],
            'currency' => ['required', 'string', 'in_list[VND]'],
            'order_invoice_number' => ['required', 'string', 'regex_match[/^[a-zA-Z0-9_-]+$/]', 'max_length[100]'],
            'order_amount' => ['required', 'numeric'],
            'operation' => ['required', 'in_list[PURCHASE,VERIFY]'],
            'order_description' => ['required', 'string'],
            'success_url' => ['permit_empty', 'string', 'valid_url'],
            'error_url' => ['permit_empty', 'string', 'valid_url'],
            'cancel_url' => ['permit_empty', 'string', 'valid_url'],
            'customer_id' => ['permit_empty', 'string'],
            'agreement_id' => ['permit_empty', 'string'],
            'agreement_name' => ['permit_empty', 'string'],
            'agreement_type' => ['permit_empty', 'in_list[RECURRING]'],
            'agreement_payment_frequency' => ['permit_empty', 'in_list[DAILY,MONTHLY,YEARLY]'],
            'signature' => ['required', 'string'],
        ];
        
        $merchant = trim($rawBody['merchant']);
        
        if (!$merchant) {
            show_404();
        }
        
        /** @var MpgsCheckoutContext */
        $checkoutContext = null;
        
        try {
            if (! $this->validateData($rawBody, $rules)) {
                throw new \Exception(sprintf('Bad request: %s', json_encode($this->validator->getErrors())), 400);
            }
            
            if ($rawBody['operation'] === 'PURCHASE' && $rawBody['order_amount'] <= 0) {
                throw new \Exception('Bad request: Order amount must be greater than 0', 400);
            }
            
            if ($rawBody['operation'] === 'VERIFY' && $rawBody['order_amount'] > 0) {
                throw new \Exception('Bad request: Order amount must be equals to 0', 400);
            }
            
            if ($rawBody['payment_method'] == 'CARD' && isset($rawBody['agreement_type']) && $rawBody['agreement_type'] === 'RECURRING') {
                if (!isset($rawBody['agreement_name']) || !$rawBody['agreement_name']) {
                    throw new \Exception('Bad request: Agreement name is required', 400);
                }
                
                if (!isset($rawBody['agreement_payment_frequency']) || !$rawBody['agreement_payment_frequency']) {
                    throw new \Exception('Bad request: Agreement payment frequency is required', 400);
                }
            }
            
            $paymentGatewayFeature = new PaymentGatewayFeature;
            $paymentGatewayFeature->withCheckoutContext($merchant, $rawBody['payment_method']);
            $checkoutContext = $paymentGatewayFeature->checkoutContext();
            
            $checkoutContext->initPaymentFields($rawBody);
            
            if ($checkoutContext instanceof RealtimeOrderFulfillmentWithQueryDR && $checkoutContext->isOnetimePayment()) {
                $checkoutContext->initPgSession();
            }

            if ($checkoutContext instanceof BankTransferCheckoutContext) {
                $checkoutContext->initPgSession();
            }
            
            $fields = $checkoutContext->getPaymentFields();
        
            return redirect()->to(base_url(sprintf('pay/v1/checkout?%s', http_build_query($fields))));
        } catch (\Throwable $e) {
            $this->logError($e, $checkoutContext);
            
            if ($e->getCode() === 400) {
                return view('pay/v1/error/bad-request', ['fields' => $rawBody]);
            }
            
            return view('pay/v1/error/internal-error', ['fields' => $rawBody, 'context' => $checkoutContext]);
        }
    }
    
    public function revokePaymentCard(): Response
    {
        if ($this->request->getMethod(true) !== 'POST') {
            return $this->failNotFound();
        }
        
        $merchant = trim($this->request->getPost('merchant'));
        $paymentMethod = trim($this->request->getPost('payment_method'));
        $fields = $this->request->getPost();
        
        /** @var CheckoutContext $checkoutContext */
        $checkoutContext = null;
        
        try {
            $paymentGatewayFeature = new PaymentGatewayFeature;
            $paymentGatewayFeature->withCheckoutContext($merchant, $paymentMethod);
            $checkoutContext = $paymentGatewayFeature->checkoutContext();
            $checkoutContext->loadPaymentFields($fields);

            if (!$checkoutContext->isAgreementPayment()) {
                return $this->failNotFound();
            }
            
            if (!$checkoutContext instanceof StoreablePaymentCard) {
                return $this->failNotFound();
            }
            
            $checkoutContext->loadPgCustomer();
            $checkoutContext->loadPgCard();
            
            if (!$checkoutContext->pgCard) {
                throw new Exception('PG card not found');
            }
            
            if (!$checkoutContext->revokePgCard()) {
                throw new Exception('PG card revoke faield');
            }
            
            return $this->respondNoContent();
        } catch (\Throwable $e) {
            $this->logError($e, $checkoutContext);
            
            return $this->failServerError('Đã có lỗi xảy ra, vui lòng liên hệ để được hỗ trợ.');
        }
    }

    public function test()
    {
        $paymentGatewayFeature = new PaymentGatewayFeature;

        $formData = [
            'merchant' => 'SEPAY',
            'payment_method' => 'BANK_TRANSFER',
            'currency' => 'VND',
            'order_id' => 'DH00001',
            'order_amount' => '10000',
            'operation' => 'PURCHASE',
            'order_description' => 'Test Order',
            'success_url' => 'https://example.com/success',
            'error_url' => 'https://example.com/error',
            'cancel_url' => 'https://example.com/cancel',
            'order_invoice_number' => '**********',
        ];

        $formData['signature'] = $paymentGatewayFeature->signFields($formData, 'acb');

        return view('pay/test', [
            'formData' => $formData,
        ]);
    }
}
