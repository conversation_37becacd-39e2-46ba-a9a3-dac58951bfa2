<?php

namespace App\Controllers;

use CodeIgniter\API\ResponseTrait;
use App\Controllers\BaseController;
use App\Models\FcmDeviceTokenModel;
use App\Models\NotificationUserModel;

class Notification extends BaseController
{
    use ResponseTrait;

    public function index()
    {
        $data = [
            'page_title' => 'Thông báo',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        echo theme_view('templates/autopay/header', $data);
        echo theme_view('notification/index', $data);
        echo theme_view('templates/autopay/footer', $data);
    }

    public function ajax_list()
    {
        if (! $this->validate([
            'notificable_type' => 'permit_empty|string|in_list[*,promotion]',
            'limit' => 'permit_empty|is_natural_no_zero',
        ])) {
            return $this->failValidationErrors($this->validator->getErrors());
        }

        $perPage = 20;
        $notificableType = trim(htmlspecialchars(addslashes($this->request->getGet('notificable_type') ?? '*')));
        $limit = trim(htmlspecialchars(addslashes($this->request->getGet('limit'))));

        $notificationUserModel = model(NotificationUserModel::class);
        $builder = $notificationUserModel
            ->select(['tb_autopay_notification.id', 'tb_autopay_notification.title', 'tb_autopay_notification.description', 'tb_autopay_notification.notificable_type', 'tb_autopay_popup_notification.cta_url', 'tb_autopay_notification_user.seen_at', 'tb_autopay_notification_user.created_at'])
            ->join('tb_autopay_notification', 'tb_autopay_notification.id = tb_autopay_notification_user.notification_id')
            ->join('tb_autopay_popup_notification', 'tb_autopay_notification.id = tb_autopay_popup_notification.notification_id', 'left')
            ->where(['tb_autopay_notification_user.user_id' => $this->user_session['user_id']])
            ->where(['tb_autopay_notification.hidden' => 0])
            ->orderBy('tb_autopay_notification_user.created_at', 'desc');

        if ($notificableType != '*' && $notificableType) {
            $builder->where(['tb_autopay_notification.notificable_type' => $notificableType]);
        }

        if (!$notificableType) {
            $builder->groupStart()
                ->where(['tb_autopay_notification.notificable_type' => ''])
                ->orWhere(['tb_autopay_notification.notificable_type' => null])
            ->groupEnd();
        }

        if ($limit) {
            $notifications = $builder->limit($limit)->get()->getResult();
        } else {
            $notifications = $builder->paginate($perPage);
        }

        $notifications = array_map(function($noti) {
            $noti->created_at = timespan($noti->created_at, true);
            $noti->cta_external = false;
            
            if ($noti->cta_url) {
                $ctaHost = parse_url($noti->cta_url)['host'] ?? '';
                $noti->cta_external = $ctaHost != parse_url(base_url())['host'];
            }

            return $noti;
        }, $notifications);

        return $this->respond([
            'data' => $notifications,
            'has_more' => $notificationUserModel->pager ? $notificationUserModel->pager->hasMore() : null, 
        ]);
    }

    public function details($id = '')
    {
        $id = trim($id);

        if (! is_numeric($id)) {
            return show_404();
        }

        $notificationUserModel = model(NotificationUserModel::class);
        $notificationDetails = $notificationUserModel
            ->select(['tb_autopay_notification.id', 'tb_autopay_notification.title', 'tb_autopay_notification.description', 'tb_autopay_notification.body', 'tb_autopay_notification.notificable_type', 'tb_autopay_popup_notification.cta_url', 'tb_autopay_notification_user.seen_at', 'tb_autopay_notification_user.created_at'])
            ->join('tb_autopay_notification', 'tb_autopay_notification.id = tb_autopay_notification_user.notification_id')
            ->join('tb_autopay_popup_notification', 'tb_autopay_notification.id = tb_autopay_popup_notification.notification_id', 'left')
            ->where([
                'tb_autopay_notification.hidden' => 0,
                'tb_autopay_notification_user.user_id' => $this->user_session['user_id'],
                'tb_autopay_notification_user.notification_id' => $id
            ])->first();

        if (!$notificationDetails) {
            return show_404();
        }

        if (!$notificationDetails->seen_at && !check_logged_admin()) {
            $notificationUserModel->where([
                    'user_id' => $this->user_session['user_id'], 
                    'notification_id' => $notificationDetails->id
                ])
                ->set(['seen_at' => date('Y-m-d H:i:s')])
                ->update();

            $this->user_details->unread_noti_count--;
        }

        if ($notificationDetails->cta_url) {
            return redirect()->to($notificationDetails->cta_url);
        }

        $data = [
            'page_title' => 'Thông báo',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'notification_details' => $notificationDetails
        ];

        echo theme_view('templates/autopay/header', $data);
        echo theme_view('notification/details', $data);
        echo theme_view('templates/autopay/footer', $data);
    }

    public function ajax_mark_read_all_notifications()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (check_logged_admin()) {
            return $this->respond(['status' => false]);
        }

        $notificationUserModel = model(NotificationUserModel::class);
        $notificationUserModel->where(['user_id' => $this->user_session['user_id']])->set(['seen_at' => date('Y-m-d H:i:s')])->update();

        return $this->respond(['status' => true]); 
    }
    
    public function ajax_force_hide_popup_notification()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (check_logged_admin()) {
            return $this->respond(['status' => false]);
        }

        $lastForceHidePopupNotification = trim(htmlspecialchars(addslashes($this->request->getVar('last_force_hide_popup_notification'))));
        $checked = $this->request->getVar('checked') == 'true' ? true : false;

        if (! $this->validateData(
            ['date' => $lastForceHidePopupNotification], 
            [
                'date' => [
                    'rules' => ['permit_empty', 'valid_date']
                ]
            ])) {
            return $this->respond(['status' => false]);
        }

        $userModel = model(UserModel::class);
        $userModel->where(['id' => $this->user_session['user_id']])->set(['last_force_hide_popup_notification' => $checked ? date('Y-m-d H:i:s') : ($lastForceHidePopupNotification ? $lastForceHidePopupNotification : null)])->update();

        return $this->respond(['status' => true]); 
    }

    public function ajax_add_device_token()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (check_logged_admin()) {
            return $this->respond(['status' => false]);
        }

        if (! $this->validate([
            'device_token' => 'required|string',
        ])) {
            return $this->failValidationErrors($this->validator->getErrors());
        }

        $deviceToken = trim($this->request->getVar('device_token'));

        if (! $deviceToken) {
            return $this->respond(['status' => false]);
        }


        $fcmDeviceTokenModel = model(FcmDeviceTokenModel::class);

        if ($fcmDeviceTokenModel->where(['user_id' => $this->user_details->id, 'token' => $deviceToken])->countAllResults()) {
            return $this->respond(['status' => false]);
        }

        $agent = $this->request->getUserAgent();
        $inserted = $fcmDeviceTokenModel->insert([
            'token' => $deviceToken,
            'platform' => $agent->getPlatform(),
            'device_name' => $agent->getBrowser(),
            'user_id' => $this->user_details->id,
            'is_browser' => 1,
        ]);

        if (! $inserted) {
            return $this->respond(['status' => false]);
        }

        return $this->respond(['status' => true]);
    }
}
