<?php

namespace App\Controllers;

use App\Models\SimModel;
use App\Models\BankModel;
use App\Models\SapoModel;
use App\Models\UserModel;
use CodeIgniter\Controller;
use App\Models\HaravanModel;
use App\Models\WebhooksModel;
use App\Models\BankAccountModel;
use App\Models\CompanyUserModel;
use App\Models\WebhooksLogModel;
use CodeIgniter\Database\RawSql;
use App\Models\TransactionsModel;
use App\Models\ConfigurationModel;
use CodeIgniter\API\ResponseTrait;
use App\Models\BankSubAccountModel;
use App\Models\UserPermissionBankModel;
use App\Models\NotificationTelegramModel;

use App\Models\UserPermissionBankSubModel;
use App\Models\UserPermissionFeatureModel;
use App\Models\NotificationLarkMessengerModel;
use App\Models\NotificationTelegramQueueModel;
use App\Models\NotificationLarkMessengerQueueModel;
use App\Models\VietinbankEnterpriseAccountModel;
use App\Models\SmsModel;
use App\Models\SmsParserModel;
use App\Models\ProductPromotionModel;
use App\Models\SimCompanyModel;

class Bankaccount extends BaseController
{
    use ResponseTrait;
    
    public function index()
    { 
        $data = [
            'page_title' => 'Ngân hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];


        if(!has_permission('BankAccount', 'can_view_all'))
            show_404();

        $data['add'] = $this->request->getGet('add');

        $bankModel = model(BankModel::class);
        $simCompanyModel = model(SimCompanyModel::class);
        $simModel = model(SimModel::class);

        $data['banks'] = $bankModel->where("active",1)->orderBy('id','ASC')->get()->getResult();

        // check dedicated sim
        $data['sims'] = $simCompanyModel->select("tb_autopay_sim.id, tb_autopay_sim.sim_phonenumber")->join("tb_autopay_sim","tb_autopay_sim.id=tb_autopay_sim_company.sim_id")->where(['tb_autopay_sim_company.company_id' => $this->user_session['company_id'], "tb_autopay_sim.active" => 1])->orderBy('tb_autopay_sim_company.created_at','DESC')->get()->getResult();

              
        echo theme_view('templates/autopay/header',$data);
        echo theme_view('bankaccount/index',$data);
        echo theme_view('templates/autopay/footer',$data);

    }

    public function connect()
    { 
        $bidvConfig = config(\Config\Bidv::class);

        $data = [
            'page_title' => 'Ngân hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'bidv' => [
                'api_connection_visibility' => $bidvConfig->apiConnectionVisibility ?? false,
                'allowed_enterprise_connection' => $bidvConfig->allowedEnterpriseConnection ?? false,
                'enterprise_prefix_id_maxlen' => $bidvConfig->enterprisePrefixIdMaxlen ?? 3,
                'enterprise_prefix_char_type' => $bidvConfig->enterprisePrefixCharType ?? 'NumberAndLetter'
            ]
        ];


        if(!has_permission('BankAccount', 'can_add'))
            show_404();

        $data['add'] = $this->request->getGet('add');

        $bankModel = model(BankModel::class);
        $simCompanyModel = model(SimCompanyModel::class);

        $productPromotion = model(ProductPromotionModel::class)
            ->select('tb_autopay_product_promotions.*')
            ->join('tb_autopay_company_promotion_history', 'tb_autopay_company_promotion_history.promotion_id = tb_autopay_product_promotions.id')
            ->where('tb_autopay_company_promotion_history.company_id', $this->user_session['company_id'])
            ->where('tb_autopay_product_promotions.product_id', $this->company_details->plan_id)
            ->first();

        $data['banks'] = $bankModel->where('active', 1);

        if ($productPromotion) {
            $data['banks']->where('id', $productPromotion->bank_id);
        }

        $data['banks'] = $data['banks']->orderBy('id', 'ASC')->findAll();

        $data['msb_request'] = $this->request->getGet('msb_request');

        // check dedicated sim
        $data['sims'] = $simCompanyModel->select("tb_autopay_sim.id, tb_autopay_sim.sim_phonenumber")->join("tb_autopay_sim","tb_autopay_sim.id=tb_autopay_sim_company.sim_id")->where(['tb_autopay_sim_company.company_id' => $this->user_session['company_id'], "tb_autopay_sim.active" => 1])->orderBy('tb_autopay_sim_company.created_at','DESC')->get()->getResult();

        $data['banks_sms_info'] = get_bank_sms_info(FALSE);

        $invididualBanks = [];
        $enterpriseBanks = [];

        foreach ($data['banks'] as $bank) {
            if ($bank->brand_name === 'ABBANK') {
                continue;
            }

            if ($bank->invididual_api_connection) {
                if ($bank->invididual_promotion_description) {
                    $bank->invididual_promotion_description = $this->parse_checklist($bank->invididual_promotion_description);
                }
                $invididualBanks[] = $bank;
            }

            if ($bank->enterprise_api_connection) {
                if ($bank->enterprise_promotion_description) {
                    $bank->enterprise_promotion_description = $this->parse_checklist($bank->enterprise_promotion_description);
                }
                $enterpriseBanks[] = $bank;
            }
        }

        if ($productPromotion) {
            $invididualBanks = array_filter($invididualBanks, function($bank) use ($productPromotion) {
                return ($bank->id == $productPromotion->bank_id) && ($productPromotion->supported_account_types == 'individual' || $productPromotion->supported_account_types == 'both');
            });
            $enterpriseBanks = array_filter($enterpriseBanks, function($bank) use ($productPromotion) {
                return ($bank->id == $productPromotion->bank_id) && ($productPromotion->supported_account_types == 'enterprise' || $productPromotion->supported_account_types == 'both');
            });
        }

        usort($invididualBanks, fn($a, $b) => $a->invididual_order - $b->invididual_order);
        usort($enterpriseBanks, fn($a, $b) => $a->enterprise_order - $b->enterprise_order);

        $data['invididualBanks'] = $invididualBanks;
        $data['enterpriseBanks'] = $enterpriseBanks;
        $data['totalBanks'] = count($data['banks']);
        $data['totalApiBanks'] = max(count($invididualBanks), count($enterpriseBanks));
        $data['productPromotion'] = $productPromotion;

        echo theme_view('templates/autopay/header',$data);
        echo theme_view('bankaccount/connect',$data);
        echo theme_view('templates/autopay/footer',$data);
    }

    public function link_account($id='') {
        if(!has_permission('BankAccount', 'can_add'))
            show_404();

        if(!is_numeric($id))
            show_404();

        $data = [
            'page_title' => 'Ngân hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];
    


        $bankAccountModel = model(BankAccountModel::class);
        $transactionsModel = slavable_model(TransactionsModel::class, 'Bankaccount');


        $data['bank_account_details'] = $bankAccountModel->select("tb_autopay_bank_account.id,tb_autopay_bank_account.account_number,tb_autopay_bank_account.label,tb_autopay_bank_account.active,tb_autopay_bank_account.created_at,tb_autopay_bank.brand_name, tb_autopay_bank_account.account_number,tb_autopay_bank_account.account_holder_name,tb_autopay_bank.icon_path,tb_autopay_bank.logo_path,tb_autopay_sim.sim_phonenumber, tb_autopay_bank_account.last_transaction, tb_autopay_bank_account.accumulated,tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.bank_id")->join("tb_autopay_bank","tb_autopay_bank.id=tb_autopay_bank_account.bank_id")->join("tb_autopay_sim","tb_autopay_sim.id=tb_autopay_bank_account.sim_id")->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id'], 'tb_autopay_bank_account.id' => $id])->get()->getRow();

        if(!is_object($data['bank_account_details']))
            show_404();
        
        $data['count_transactions'] = $transactionsModel->where(['bank_account_id' => $data['bank_account_details']->id, 'deleted_at' => NULL])->countAllResults();

      
        echo view('templates/autopay/header',$data);
        echo view('bankaccount/otp_verify',$data);
        echo view('templates/autopay/footer',$data);

        
    }



    public function details($id='')
    { 
        $data = [
            'page_title' => 'Ngân hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        if(!has_permission('BankAccount', 'can_view_all'))
            show_404();

        if(!is_numeric($id))
            show_404();

        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $transactionsModel = slavable_model(TransactionsModel::class, 'Bankaccount');
        $simCompanyModel = model(SimCompanyModel::class);


        $data['bank_account_details'] = $bankAccountModel->select("tb_autopay_bank_account.id,tb_autopay_bank_account.account_number,tb_autopay_bank_account.label,tb_autopay_bank_account.active,tb_autopay_bank_account.created_at,tb_autopay_bank.full_name,tb_autopay_bank.brand_name, tb_autopay_bank_account.account_number,tb_autopay_bank_account.account_holder_name,tb_autopay_bank.icon_path,tb_autopay_bank.logo_path,tb_autopay_sim.sim_phonenumber, tb_autopay_bank_account.last_transaction, tb_autopay_bank_account.accumulated,tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.bank_id,tb_autopay_bank_account.bank_sms, tb_autopay_bank_account.bank_sms_connected, tb_autopay_bank_account.bank_api, tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.deleted_at")->join("tb_autopay_bank","tb_autopay_bank.id=tb_autopay_bank_account.bank_id")->join("tb_autopay_sim","tb_autopay_sim.id=tb_autopay_bank_account.sim_id","left")->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id'], 'tb_autopay_bank_account.id' => $id])->get()->getRow();

        if(!is_object($data['bank_account_details']))
            show_404();


        if($data['bank_account_details']->bank_id == 17)
            return redirect()->to('klb/details/' . $id);

        if($data['bank_account_details']->bank_id == 18)
            return redirect()->to('ocb/details/' . $id);

        if($data['bank_account_details']->bank_id == 8)
            return redirect()->to('mbb/details/' . $id);

        if($data['bank_account_details']->bank_id == 9)
            return redirect()->to('bidv/details/' . $id);

        if ($data['bank_account_details']->bank_id == 6)
            return redirect()->to('vietinbank/details/' . $id);

        if ($data['bank_account_details']->bank_id == 12)
            return redirect()->to('tpbank/details/' . $id);
        
        if ($data['bank_account_details']->bank_id == 3)
            return redirect()->to('acb/details/' . $id);
        
        $data['count_transactions'] = $transactionsModel->where(['bank_account_id' => $data['bank_account_details']->id, 'deleted_at' => NULL])->countAllResults();


        if($data['bank_account_details']->bank_id == 17 && $data['bank_account_details']->bank_api_connected == 0) {
            return redirect()->to(base_url('klb/step2/' . $data['bank_account_details']->id));
        }

        if($data['bank_account_details']->bank_id == 18 && $data['bank_account_details']->bank_api_connected == 0) {
            return redirect()->to(base_url('ocb/step2/' . $data['bank_account_details']->id));
        }

        if($data['bank_account_details']->bank_id == 8 && $data['bank_account_details']->bank_api == 1 && $data['bank_account_details']->bank_sms == 0) {
            return redirect()->to(base_url('mbb/step2/' . $data['bank_account_details']->id));
        }

        if ($data['bank_account_details']->bank_id == 2) {
            return redirect()->to(base_url('vpbank/details/' . $data['bank_account_details']->id));
        }

        $data['count_subs'] = $bankSubAccountModel->where(['bank_account_id' => $id, 'deleted_at' => NULL])->countAllResults();

        $data['bank_sms_info'] = get_bank_sms_info($data['bank_account_details']->brand_name);

         // check dedicated sim
         $data['sims'] = $simCompanyModel->select("tb_autopay_sim.id, tb_autopay_sim.sim_phonenumber")->join("tb_autopay_sim","tb_autopay_sim.id=tb_autopay_sim_company.sim_id")->where(['tb_autopay_sim_company.company_id' => $this->user_session['company_id'], "tb_autopay_sim.active" => 1])->orderBy('tb_autopay_sim_company.created_at','DESC')->get()->getResult();
       
         // data QR
         $data['bank_sub_accounts_custom']=[];
         if(!empty($data['bank_account_details'])){
 
             $data['bank_sub_accounts_custom'] = $bankSubAccountModel->select("tb_autopay_bank_sub_account.id,tb_autopay_bank_sub_account.acc_type,tb_autopay_bank.brand_name,tb_autopay_bank.icon_path, tb_autopay_bank_account.account_holder_name, tb_autopay_bank_account.account_number, tb_autopay_bank_sub_account.sub_account, tb_autopay_bank_sub_account.label")
                 ->join("tb_autopay_bank_account", "tb_autopay_bank_account.id = tb_autopay_bank_sub_account.bank_account_id")
                 ->join("tb_autopay_bank", "tb_autopay_bank.id = tb_autopay_bank_account.bank_id", "left")
                 ->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id']])
                 ->where('tb_autopay_bank_account.id', $data['bank_account_details']->id) 
                 ->get()
                 ->getResultArray();
         }

        echo view('templates/autopay/header',$data);
        //echo view('bankaccount/details',$data);
        echo view('bankaccount/details_v2',$data);
        echo view('templates/autopay/footer',$data);

    }

    public function ajax_bank_account_list() {

        if(!has_permission('BankAccount', 'can_view_all'))
            show_404();
     
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        $bankAccountModel = model(BankAccountModel::class);
        $transactionsModel = slavable_model(TransactionsModel::class, 'Bankaccount');
        $bankSubAccountModel = model(BankSubAccountModel::class);

        $bank_accounts = $bankAccountModel->getDatatables($this->user_session['company_id'], $this->company_details->role, $this->user_details->id);
        $productPromotion = model(ProductPromotionModel::class)
            ->select('tb_autopay_product_promotions.*')
            ->join('tb_autopay_company_promotion_history', 'tb_autopay_company_promotion_history.promotion_id = tb_autopay_product_promotions.id')
            ->where('tb_autopay_product_promotions.product_id', $this->company_details->plan_id)
            ->where('tb_autopay_company_promotion_history.company_id', $this->user_session['company_id'])
            ->where('tb_autopay_company_promotion_history.product_id', $this->company_details->plan_id)
            ->first();

        $data = array();

        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');

        $can_edit = has_permission('BankAccount', 'can_edit');
        $can_delete = has_permission('BankAccount', 'can_delete');

        foreach ($bank_accounts as $account) {

            $no++;
            $row = array();

            $actions_btn = '';

            if($can_edit)
                $actions_btn = "<a href='javascript:;' onclick='edit_bank_account(" . $account->id. ")' class='btn btn-sm btn-outline-warning ms-2 me-2 mt-2'><i class='bi bi-pencil'></i> Sửa</a>";
            
            if($can_delete)
                $actions_btn = $actions_btn .  "<a href='javascript:;' onclick='delete_bank_account(" . $account->id. ")' class='btn btn-sm btn-outline-danger ms-2 mt-2'><i class='bi bi-trash3'></i> Xóa</a>";

            if($account->bank_id == 17)
                $details_url = base_url('klb/details/' . $account->id);
            elseif($account->bank_id == 18)
                $details_url = base_url('ocb/details/' . $account->id);
            if($account->bank_id == 12)
                $details_url = base_url('tpbank/details/' . $account->id);
            elseif($account->bank_id == 8)
                $details_url = base_url('mbb/details/' . $account->id);
            else if ($account->bank_id == 6)
                $details_url = base_url('vietinbank/details/' . $account->id);
            else if ($account->bank_id == 3)
                $details_url = base_url('acb/details/' . $account->id);
            else if ($account->bank_id == 9)
                $details_url = base_url('bidv/details/' . $account->id);
            else if ($account->bank_id == 2)
                $details_url = base_url('vpbank/details/' . $account->id);
            else
                $details_url = base_url('bankaccount/details/' . $account->id);
                        
            $row[] = $no;

            $row[] = "<img class='ms-3' src='" . esc(base_url('assets/images/banklogo/' . $account->icon_path)) . "' class='avatar img-fluid m-auto' style='height:20px; width:20px'> ".  esc($account->brand_name) . "<br> " . 
                ((! $productPromotion || $productPromotion->bank_id == $account->bank_id) 
                    ? "<a class='ms-3' href='" . $details_url . "'>" . esc($account->account_number) . "</a>"
                    : "<span class='ms-3'>" . esc($account->account_number) . "</span>");

            $row[] = (! $productPromotion || $productPromotion->bank_id == $account->bank_id)
                ? "<a href='" . $details_url . "'>" . esc($account->account_holder_name) . "</a> <br>" . esc($account->label)
                : '<strong>' . esc($account->account_holder_name) . "</strong><br>" . esc($account->label);

            $connect_text = "<div class='d-flex flex-column' style='gap: 0.5rem'>";
            if($account->bank_sms == 1) {
                $connect_text .= "<div>";
                $connect_text = $connect_text . "<p class='text-info mb-0'><i class='bi bi-chat-left-text-fill'></i> SMS Banking</p>";
            
                if($account->bank_sms_connected == 1)
                    $connect_text = $connect_text . "<div><span class='badge bg-success'>Đã kết nối</span></div>";
                else
                    $connect_text = $connect_text ."<div><span class='badge bg-secondary'>Chưa kết nối</span></div>";

                $connect_text .= "</div>";
            }
            
            if($account->bank_api == 1) {
                $connect_text .= "<div>";
                $connect_text = $connect_text . "<p class='text-warning mb-0'><i class='bi bi-lightning-charge'></i> API Banking</p>";
            
                if($account->bank_api_connected  == 1)
                    $connect_text = $connect_text ."<div><span class='badge bg-success'>Đã kết nối</span></div>";
                else
                    $connect_text = $connect_text ."<div><span class='badge bg-secondary'>Chưa kết nối</span></div>";
                
                $connect_text .= "</div>";
            }
            
            $connect_text .= '</div>';

            $row[] = $connect_text;

            

            if(is_numeric($account->accumulated))
                $row[] = number_format($account->accumulated) . " đ";
            else
                $row[] = ""; 
            
            //$row[] = $actions_btn;
       
            $data[] = $row;
        }
 
        $output = array(
            "draw" => $draw,
            "recordsTotal" => $bankAccountModel->countAll($this->user_session['company_id'], $this->company_details->role, $this->user_details->id),
            "recordsFiltered" => $bankAccountModel->countFiltered($this->user_session['company_id'], $this->company_details->role, $this->user_details->id),
            "data" => $data,
        );
        return $this->response->setJSON($output);
    
    
    }

    public function ajax_bank_account_add() {

        if ($this->request->getMethod(true) != 'POST')
            return '';


        if(!has_permission('BankAccount', 'can_add'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền thêm tài khoản ngân hàng"));
 
        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'account_number' => ['label' => 'Số tài khoản', 'rules' => 'integer|greater_than[1]|min_length[3]|max_length[20]|is_natural'],
            'account_holder_name' => ['label' => 'Tên chủ tài khoản', 'rules' => "required|min_length[4]|max_length[200]|valid_account_holder_name"],
            'active' => ['label' => 'Trại thái', 'rules' => "required|in_list[0,1]"],
            'label' => ['label' => 'Tên gợi nhớ', 'rules' => "max_length[100]"],
            'bank_id' => ['label' => 'Chọn Ngân hàng', 'rules' => "required|integer|is_not_unique[tb_autopay_bank.id]"],
        ],
        [
            'account_holder_name' => [
                'valid_account_holder_name'=>'Trường chủ tài khoản bạn điền không đúng định dạng',
            ],
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        } else {
            $account_holder_name = remove_accents(trim($this->request->getVar('account_holder_name')), TRUE);

            $data = array(
                'company_id' => $this->user_session['company_id'],
                'account_holder_name' => $account_holder_name,
                'account_number' => $this->request->getVar('account_number'),
                'bank_id' => $this->request->getVar('bank_id'),
                'label' => trim(xss_clean($this->request->getVar('label'))),
                'active' => $this->request->getVar('active'),
                'bank_sms' => 1,
            );

            $bankAccountModel = model(BankAccountModel::class);
            $simModel = model(SimModel::class);
            $simCompanyModel = model(SimCompanyModel::class);
            $productModel = model(ProductModel::class);
            $companySubscriptionModel = model(CompanySubscriptionModel::class);

            if ($bankAccountModel->where(['account_number' => $data['account_number'], 'bank_id' => $data['bank_id']])->get()->getRow()) {
                return $this->response->setJSON(array('status'=>FALSE,'message'=>'Số tài khoản này đã tồn tại trên hệ thống'));
            }

            $subscription_details = $companySubscriptionModel->where(['company_id' => $this->user_session['company_id']])->get()->getRow();
             
            if(!is_object($subscription_details)) {
                return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn chưa đăng ký gói dịch vụ. Vui lòng chọn gói dịch vụ trước"));
            }

            $product_details = $productModel->where(['id' => $subscription_details->plan_id])->get()->getRow();

            if(is_object($product_details) && $product_details->sms_allow == 0) {
                return $this->response->setJSON(array("status"=>FALSE,"message"=>"Gói dịch vụ của bạn không cho phép kết nối Ngân hàng qua SMS báo số dư. Vui lòng liên hệ SePay để nâng cấp gói."));

            }

            $account_holder_name = remove_accents(xss_clean($this->request->getVar('account_holder_name')), TRUE);

            $data = array(
                'company_id' => $this->user_session['company_id'],
                'account_holder_name' => $account_holder_name,
                'account_number' => $this->request->getVar('account_number'),
                'bank_id' => $this->request->getVar('bank_id'),
                'label' => xss_clean($this->request->getVar('label')),
                'active' => $this->request->getVar('active'),
                'bank_sms' => 1,
            );

            $sim_id = $this->request->getVar('sim_id');

            $sims = $simCompanyModel->select("tb_autopay_sim.id, tb_autopay_sim.sim_phonenumber")->join("tb_autopay_sim","tb_autopay_sim.id=tb_autopay_sim_company.sim_id")->where(['tb_autopay_sim_company.company_id' => $this->user_session['company_id'], "tb_autopay_sim.active" => 1])->orderBy('tb_autopay_sim_company.created_at','ASC')->get()->getResult();

            if(count($sims) == 1) {
                $data['sim_id'] = $sims[0]->id;
            } else if(count($sims) > 1 && is_numeric($sim_id) && $sim_id > 0) {
                // check dedicated sim
                // Nếu có dedicated sim thì phải chọn trong danh sách sim dedicated

                $sim_details = $simCompanyModel->select("tb_autopay_sim.id")->join("tb_autopay_sim","tb_autopay_sim.id=tb_autopay_sim_company.sim_id")->where(['tb_autopay_sim_company.company_id' => $this->user_session['company_id'], "tb_autopay_sim.active" => 1,"tb_autopay_sim.id" => $sim_id])->get()->getRow();

                if(!is_object($sim_details))
                    return $this->response->setJSON(array("status"=>FALSE,"message"=>"SIM nhận SMS mà bạn chọn không khả dụng"));
                else
                    $data['sim_id'] = $sim_id;
            } else {

                
                return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn chưa được cấp SIM. Vui lòng liên hệ SePay để được hỗ trợ."));


            }
 
            $result = $bankAccountModel->insert($data);
            
            if($result) {
                add_user_log(array('data_id'=>$result,'company_id' => $this->user_session['company_id'], 'data_type'=>'bank_account_add','description'=>'Thêm tài khoản ngân hàng','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
 
 
                return $this->response->setJSON(array("status"=>true,"id" => $result));
            } else {
                add_user_log(array('data_id'=>0,'company_id' => $this->user_session['company_id'], 'data_type'=>'bank_account_add','description'=>'Thêm tài khoản ngân hàng','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Failed'));
                return $this->response->setJSON(array("status"=>false,"message"=>"Không thể thêm tài khoản ngân hàng. Vui lòng liên hệ SePay để được hỗ trợ."));
            }
                

        }
    }


    public function ajax_msb_account_add() {
        if ($this->request->getMethod(true) != 'POST')
            return '';


        if(!has_permission('BankAccount', 'can_add'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền thêm tài khoản ngân hàng"));
 
        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'msb_account_number' => ['label' => 'Số tài khoản', 'rules' => 'integer|greater_than[1]|min_length[3]|max_length[20]|is_natural'],
            'msb_account_holder_name' => ['label' => 'Tên chủ tài khoản', 'rules' => "required|min_length[4]|max_length[200]|valid_account_holder_name"],
            'msb_access_code' => ['label' => 'Access Code', 'rules' => "required"],
            'msb_label' => ['label' => 'Tên gợi nhớ', 'rules' => "max_length[100]"],
            'msb_tid' => ['permit_empty'],
            'msb_mid' => ['permit_empty'],
        ],
        [
            'account_holder_name' => [
                'valid_account_holder_name'=>'Trường chủ tài khoản bạn điền không đúng định dạng',
            ],
            'account_number' => [
                'is_unique'=>'Số tài khoản này đã tồn tại trên hệ thống',
            ],
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        } else {
            $bankAccountModel = model(BankAccountModel::class);
            $simModel = model(SimModel::class);
            $simCompanyModel = model(SimCompanyModel::class);

            if ($bankAccountModel->where(['account_number' => trim($this->request->getVar('msb_account_number')), 'bank_id' => 10])->get()->getRow()) {
                return $this->response->setJSON(array('status'=>FALSE,'message'=>'Số tài khoản này đã tồn tại trên hệ thống'));
            }
            
            $account_holder_name = remove_accents(xss_clean($this->request->getVar('msb_account_holder_name')), TRUE);

            $data = array(
                'company_id' => $this->user_session['company_id'],
                'account_holder_name' => $account_holder_name,
                'account_number' => $this->request->getVar('msb_account_number'),
                'bank_id' => 10,
                'label' => xss_clean($this->request->getVar('msb_label')),
                'active' => 1,
                'bank_api' => 1,
                'bank_api_connected' => 1,
                'access_code' => trim($this->request->getVar('msb_access_code')),
                'mid' => trim($this->request->getVar('msb_mid')),
                'tid' => trim($this->request->getVar('msb_tid'))
            );

            $bankAccountModel->skipApplyReferral();

            $result = $bankAccountModel->insert($data);
            
            if($result) {
                add_user_log(array('data_id'=>$result,'company_id' => $this->user_session['company_id'], 'data_type'=>'bank_account_add','description'=>'Thêm tài khoản ngân hàng MSB API','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
 
                $bankSubAccountConfig = get_configuration('BankSubAccount');

                if($bankSubAccountConfig == "off") {
                    $configurationModel = model(ConfigurationModel::class);
                    $configurationModel->set([
                        'value' => "on",
                    ])->where(['company_id' => $this->user_session['company_id'],'setting' => 'BankSubAccount'])->update();
            
                }
 
                return $this->response->setJSON(array("status"=>true,"id" => $result));
            } else {
                add_user_log(array('data_id'=>0,'company_id' => $this->user_session['company_id'], 'data_type'=>'bank_account_add','description'=>'Thêm tài khoản ngân hàng MSB API','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Failed'));
                return $this->response->setJSON(array("status"=>false,"message"=>"Không thể thêm tài khoản ngân hàng. Vui lòng liên hệ SePay để được hỗ trợ."));
            }
        }
    }


    public function ajax_get_bank_account($id='') {
        
        if(!is_numeric($id)) 
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"ID tài khoản ngân hàng không hợp lệ"));

        if(!has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền sửa tài khoản ngân hàng"));
 
        
        $bankAccountModel = model(BankAccountmodel::class);
        
        $result = $bankAccountModel->select("id,company_id,bank_id,sim_id,account_holder_name,account_number,label,active")->where(['id'=>$id, 'company_id' => $this->user_session['company_id']])->get()->getRow();
        
        if($result)
            return $this->response->setJSON(["status"=>TRUE, "data"=>$result]);
        else
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy tài khoản ngân hàng này"));
    }

    public function ajax_bank_account_update() {
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        if(!has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền sửa tài khoản ngân hàng"));
 
        $validation =  \Config\Services::validation();

        helper('text');

        $bankAccountModel = model(BankAccountmodel::class);
        $bank_account_id = $this->request->getPost('id');

        $result = $bankAccountModel->where(['id'=>$bank_account_id, 'company_id' => $this->user_session['company_id']])->get()->getRow();
        
        if(!is_object($result))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy tài khoản ngân hàng này"));

        $rules = [
            'id' => 'required|integer|is_natural',
            'label' => ['label' => 'Tên gợi nhớ', 'rules' => "max_length[100]"],
        ];

        if ($result->bank_sms) {
            $rules['account_holder_name'] = ['label' => 'Tên chủ tài khoản', 'rules' => "required|min_length[3]|max_length[200]|valid_account_holder_name"];
        }

        if(! $this->validate($rules,
        [
            'account_holder_name' => [
                'valid_account_holder_name'=>'Trường chủ tài khoản bạn điền không đúng định dạng',
            ],
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }  

        $data = array(
            'label' => xss_clean($this->request->getVar('label')),
        );

        if ($result->bank_sms) {
            $data['account_holder_name'] = remove_accents(xss_clean($this->request->getPost('account_holder_name')), TRUE);
        }

        // check dedicated sim
        $sim_id = $this->request->getPost('sim_id');

        if(is_numeric($sim_id)) {
            $simCompanyModel = model(SimCompanyModel::class);
            $sim_details = $simCompanyModel->select("tb_autopay_sim.id")->join("tb_autopay_sim","tb_autopay_sim.id=tb_autopay_sim_company.sim_id")->where(['tb_autopay_sim_company.company_id' => $this->user_session['company_id'], "tb_autopay_sim.active" => 1,"tb_autopay_sim.id" => $sim_id])->get()->getRow();

            if(is_object($sim_details))
                $data['sim_id'] = $sim_id;
            else
                return $this->response->setJSON(array("status"=>FALSE,"message"=>"SIM nhận SMS mà bạn chọn không khả dụng"));

            
        }
           
        $result = $bankAccountModel->set($data)->where("id",$bank_account_id)->update();
        
        if($result) { 
            add_user_log(array('data_id'=>$bank_account_id,'company_id' => $this->user_session['company_id'], 'data_type'=>'bank_account_update','description'=>'Sửa tài khoản ngân hàng','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
            return $this->response->setJSON(array("status"=>true));
        } else {
            return $this->response->setJSON(array("status"=>false,"message"=>"Không thể cập nhật tài khoản ngân hàng!"));
        }
            

    
    }

    public function ajax_edit_label()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        if (! has_permission('BankAccount', 'can_edit'))
            return $this->respond(['status' => false, 'message' => 'Bạn không có quyền sửa tài khoản ngân hàng']);

        $bankAccountModel = model(BankAccountmodel::class);
        $bankAccountId = $this->request->getPost('id');
        $label = trim(xss_clean($this->request->getVar('label')));

        $bankAccountDetails = $bankAccountModel->where(['id'=> $bankAccountId, 'company_id' => $this->user_session['company_id']])->first();
        
        if (! $bankAccountDetails)
            return $this->respond(['status' => fasle, 'message' => 'Không tìm thấy tài khoản ngân hàng này']);

        if (strlen($label) > 100) {
            return $this->respond(['status' => fasle, 'message' => 'Tên gợi nhớ không được vượt quá 100 ký tự']);
        }

        $updated = $bankAccountModel->set(['label' => $label])->where('id', $bankAccountId)->update();
        
        if ($updated) { 
            add_user_log(['data_id'=> $bankAccountId, 'company_id' => $this->user_session['company_id'], 'data_type' => 'bank_account_update', 'description' => 'Sửa tài khoản ngân hàng', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);

            return $this->respond(['status' => true]);
        }

        return $this->respond(['status' => fasle, 'message' => 'Đã có lỗi xảy ra, vui lòng thử lại']);
    }

    public function ajax_bank_account_delete() {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!has_permission('BankAccount', 'can_delete'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền xóa tài khoản ngân hàng"));
 
 
        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'id' => 'required|integer|is_natural',
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }
        $bank_account_id = $this->request->getPost('id');

        $bankAccountModel = model(BankAccountmodel::class);
        $bank_account_details = $bankAccountModel->where(['id'=>$bank_account_id,'company_id'=>$this->user_session['company_id']])->get()->getRow();

        if(!is_object($bank_account_details))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Không tìm thấy tài khoản ngân hàng này"));

        if(in_array($bank_account_details->bank_id,array(10,17,18)) && $bank_account_details->bank_api_connected == 1)
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Không thể xoá các ngân hàng kết nối qua API. Bạn chỉ có thể xoá giao dịch của nó."));

        // Prevent deletion of TPBank bank account if it is not already marked as deleted
        if ($bank_account_details->bank_id == 12 && $bank_account_details->bank_api == 1 && $bank_account_details->active == 1) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Không thể xóa tài khoản ngân hàng TPBank này',
            ]);
        }


        $transactionsModel = slavable_model(TransactionsModel::class, 'Bankaccount');
      
        $webhooksModel = model(WebhooksModel::class);
        $webhooksLogModel = model(WebhooksLogModel::class);
        $notificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);
        $notificationLarkMessengerQueueModel = model(NotificationLarkMessengerQueueModel::class);

        $notificationTelegramModel = model(NotificationTelegramModel::class);
        $notificationTelegramQueueModel = model(NotificationTelegramQueueModel::class);
        
        $smsModel = model(SmsModel::class);

        $sapoModel = model(SapoModel::class);
        $haravanModel = model(HaravanModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $userPermissionBankModel = model(UserPermissionBankModel::class);
        $userPermissionBankSubModel = model(UserPermissionBankSubModel::class);

        $bankAccountCashflowModel = model(BankAccountCashflowModel::class);
        $bankSubAccountCashflowModel = model(BankSubAccountCashflowModel::class);

        $webhooks = $webhooksModel->where(['bank_account_id' => $bank_account_id, 'company_id' => $this->user_session['company_id'], 'deleted_at' => NULL])->get()->getResult();
        $sapos = $sapoModel->where(['bank_account_id' => $bank_account_id, 'company_id' => $this->user_session['company_id'], 'deleted_at' => NULL])->get()->getResult();
        $haravans = $haravanModel->where(['bank_account_id' => $bank_account_id, 'company_id' => $this->user_session['company_id'], 'deleted_at' => NULL])->get()->getResult();
        $bank_sub_accounts = $bankSubAccountModel->where(['bank_account_id' => $bank_account_id, 'deleted_at' => NULL])->get()->getResult();
        
        //$user_permission_banks = $userPermissionBankModel->where(['bank_account_id' => $bank_account_id, 'deleted_at' => NULL])->get()->getResult();
        
        $telegrams = $notificationTelegramModel->where(['bank_account_id' => $bank_account_id, 'company_id' => $this->user_session['company_id'], 'deleted_at' => NULL])->get()->getResult();
        
        $lark_messengers = $notificationLarkMessengerModel->where(['bank_account_id' => $bank_account_id, 'company_id' => $this->user_session['company_id'], 'deleted_at' => NULL])->get()->getResult();

        

        $transactions_count = $transactionsModel->where(['bank_account_id' => $bank_account_details->id,'deleted_at' => NULL])->countAllResults();

       if( $this->request->getIPAddress() !="**************" &&  $this->user_session['company_id'] == 5 && $transactions_count > 0) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Tài khoản demo không thể xóa ngân hàng đã có giao dịch."));
        } 
        

        if($transactions_count > 0 && !in_array($this->company_details->role,['Admin', 'SuperAdmin']))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Tài khoản ngân hàng này đã có giao dịch trên hệ thống. Để xóa tài khoản ngân hàng này, người dùng phải có quyền Admin hoặc Super Admin."));
        

        // Xóa webhook log liên quan
        foreach($webhooks as $webhook) {
            $webhooksLogModel->where(['webhook_id' => $webhook->id, 'company_id' => $this->user_session['company_id'],'webhook_type' => 'Webhooks'])->delete();
            add_system_log(['company_id' => $this->user_session['company_id'], 'data_type' => 'hard_delete_webhook_by_user', 'description' => "Hard delete webhook ID " . $webhook->id . ". By user delete bank account " . $bank_account_details->account_number,'level' => 'Info', 'by' => 'Bankaccount Controller']);

        }
        
        // chay vong lap lay webhook, sapo, haravan sau do xoa webhook log theo webhook_id va webhook_type
         // Xóa webhook log liên quan
        foreach($sapos as $sapo) {
            $webhooksLogModel->where(['webhook_id' => $sapo->id, 'company_id' => $this->user_session['company_id'],'webhook_type' => 'Sapo'])->delete();
            add_system_log(['company_id' => $this->user_session['company_id'], 'data_type' => 'hard_delete_sapo_by_user', 'description' => "Hard delete Sapo ID " . $sapo->id . ". By user delete bank account " . $bank_account_details->account_number,'level' => 'Info', 'by' => 'Bankaccount Controller']);

        }

        foreach($haravans as $haravan) {
            $webhooksLogModel->where(['webhook_id' => $haravan->id, 'company_id' => $this->user_session['company_id'],'webhook_type' => 'Haravan'])->delete();
            add_system_log(['company_id' => $this->user_session['company_id'], 'data_type' => 'hard_delete_sapo_by_user', 'description' => "Hard delete Haravan ID " . $haravan->id . ". By user delete bank account " . $bank_account_details->account_number,'level' => 'Info', 'by' => 'Bankaccount Controller']);

        }

        // Xóa sapo, haravan liên quan
        $sapos = $sapoModel->where(['bank_account_id' => $bank_account_id, 'company_id' => $this->user_session['company_id']])->delete();

        $haravans = $haravanModel->where(['bank_account_id' => $bank_account_id, 'company_id' => $this->user_session['company_id']])->delete();
        
        // Xoá log telegram, lark liên quan (trong queue)
        foreach($telegrams as $telegram) {
            $notificationTelegramQueueModel->where(['notify_id' => $telegram->id])->delete();
            add_system_log(['company_id' => $this->user_session['company_id'], 'data_type' => 'hard_delete_telegram_by_user', 'description' => "Hard delete Telegram ID " . $telegram->id . ". By user delete bank account " . $bank_account_details->account_number,'level' => 'Info', 'by' => 'Bankaccount Controller']);

        }
        foreach($lark_messengers as $lark_messenger) {
            $notificationLarkMessengerQueueModel->where(['notify_id' => $lark_messenger->id])->delete();
            add_system_log(['company_id' => $this->user_session['company_id'], 'data_type' => 'hard_delete_lark_messenger_by_user', 'description' => "Hard delete Lark Messenger ID " . $lark_messenger->id . ". By user delete bank account " . $bank_account_details->account_number,'level' => 'Info', 'by' => 'Bankaccount Controller']);

        }

        // Xóa tích hợp telegram, lark liên quan
        $notificationTelegramModel->where(['bank_account_id' => $bank_account_id, 'company_id' => $this->user_session['company_id']])->delete();
        $notificationLarkMessengerModel->where(['bank_account_id' => $bank_account_id, 'company_id' => $this->user_session['company_id']])->delete();

        // Xóa phân quyền liên quan
        foreach($bank_sub_accounts as $bank_sub_account) {
            $userPermissionBankSubModel->where(['sub_account_id' => $bank_sub_account->id])->delete();
        }
        $userPermissionBankModel->where(['bank_account_id' => $bank_account_id])->delete();

        // Xóa tài khoản phụ liên quan
        $bankSubAccountModel->where(['bank_account_id' => $bank_account_id])->delete();
        
        // Xóa giao dịch liên quan
        $transactions = $transactionsModel->select("tb_autopay_sms_parsed.id, tb_autopay_sms_parsed.sms_id, tb_autopay_sms_parsed.source")
            ->join("tb_autopay_bank_account","tb_autopay_bank_account.id=tb_autopay_sms_parsed.bank_account_id")
            ->where(["tb_autopay_bank_account.company_id" => $this->user_session['company_id'],'tb_autopay_sms_parsed.deleted_at' => NULL, 'tb_autopay_bank_account.id' => $bank_account_id])->get()->getResult();
        
        if(count($transactions) > 0) {
            $sms_count = 0;
            foreach($transactions as $trans) {
                if($trans->source == "SMS" && $trans->sms_id>0) {
                    $smsModel->delete($trans->sms_id);
                    $sms_count = $sms_count + 1;
                }
                $transactionsModel->delete($trans->id);

            }

            add_system_log(['company_id' => $this->user_session['company_id'], 'data_type' => 'soft_delete_sms_by_user', 'description' => "Soft delete " . $sms_count . " sms. By user delete bank account " . $bank_account_details->account_number,'level' => 'Info', 'by' => 'Bankaccount Controller']);

            add_system_log(['company_id' => $this->user_session['company_id'], 'data_type' => 'soft_delete_transactions_by_user', 'description' => "Soft delete " . count($transactions) . " transactions. By user delete bank account " . $bank_account_details->account_number,'level' => 'Info', 'by' => 'Bankaccount Controller']);    
        }

        // Xoá dữ liệu cashflow liên quan
        $bankAccountCashflowModel->where(['bank_account_id' => $bank_account_id])->delete();
        $bankSubAccountCashflowModel->where(['bank_account_id' => $bank_account_id])->delete();
        
        // Xóa tài khoản ngân hàng

        $bankAccountModel->delete($bank_account_id);

        add_user_log(array('data_id'=>$bank_account_id,'company_id' => $this->user_session['company_id'], 'data_type'=>'bank_account_delete','description'=>'Xóa tài khoản ngân hàng ' . $bank_account_details->account_number,'user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
    
        return $this->response->setJSON(array("status"=>true));
        
    
    }

    public function qrcode($bank_account_id = '') {
        if(!is_numeric($bank_account_id))
            show_404();

        $data = [
            'page_title' => 'Ngân hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];


        if(!has_permission('BankAccount', 'can_view_all'))
            show_404();


            $bankAccountModel = model(BankAccountModel::class);
            $transactionsModel = slavable_model(TransactionsModel::class, 'Bankaccount');
    
    
        $data['bank_account_details'] = $bankAccountModel->select("tb_autopay_bank_account.id,tb_autopay_bank_account.account_number,tb_autopay_bank_account.label,tb_autopay_bank_account.active,tb_autopay_bank_account.created_at,tb_autopay_bank.brand_name, tb_autopay_bank_account.account_number,tb_autopay_bank_account.account_holder_name,tb_autopay_bank.full_name,tb_autopay_bank.short_name,tb_autopay_bank.icon_path,tb_autopay_bank.logo_path,tb_autopay_bank.id as bank_id,tb_autopay_bank_account.bank_api as bank_api")->join("tb_autopay_bank","tb_autopay_bank.id=tb_autopay_bank_account.bank_id")->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id'], 'tb_autopay_bank_account.id' => $bank_account_id])->get()->getRow();

        $data['notify_detection_code'] = null;

        if ($data['bank_account_details']->bank_id == 6 && $data['bank_account_details']->bank_api == 1) {
            $vietinbankEnterpriseAccountModel = model(VietinbankEnterpriseAccountModel::class);

            if (!$vietinbankEnterpriseAccountModel->where(['bank_account_id' => $data['bank_account_details']->id])->countAllResults()) {
                $data['notify_detection_code'] = (config(\App\Config\Vietinbank::class)->notifyDetectionCode ?? null);
            }
        }

        if(!is_object($data['bank_account_details']))
            show_404();
    
        $data['qrcode'] = "https://qr.sepay.vn/img?bank=" . urlencode($data['bank_account_details']->brand_name) . 
                          "&acc=" . $data['bank_account_details']->account_number . 
                          "&template=" . 
                          "&des=" . ($data['notify_detection_code'] ? $data['notify_detection_code'] . "+Chuyen+tien" : '');

        $print = $this->request->getGet('print');
        $embed = $this->request->getGet('embed');

        if($print == 'yes') {
            echo view('bankaccount/qrcode_print',$data);
        } else  if($embed == 'yes') {
            echo view('templates/autopay/header',$data);
            echo view('bankaccount/embed',$data);
            echo view('templates/autopay/footer',$data);

        } else  {
            echo view('templates/autopay/header',$data);
            echo view('bankaccount/qrcode',$data);
            echo view('templates/autopay/footer',$data);
        }


    }

    public function sub($bank_account_id='')
    { 
        $data = [
            'page_title' => 'Ngân hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];


        if(!has_permission('BankAccount', 'can_view_all'))
            show_404();

        $bankAccountModel = model(BankAccountmodel::class);
        $data['bank_account_details'] = $bankAccountModel->select("tb_autopay_bank_account.id,tb_autopay_bank_account.account_number,tb_autopay_bank_account.label,tb_autopay_bank_account.active,tb_autopay_bank_account.created_at,tb_autopay_bank.brand_name, tb_autopay_bank_account.account_number,tb_autopay_bank_account.account_holder_name,tb_autopay_bank.full_name,tb_autopay_bank.short_name,tb_autopay_bank.icon_path,tb_autopay_bank.logo_path,tb_autopay_sim.sim_phonenumber,tb_autopay_bank_account.bank_id, tb_autopay_bank_account.bank_api_connected")->join("tb_autopay_bank","tb_autopay_bank.id=tb_autopay_bank_account.bank_id")->join("tb_autopay_sim","tb_autopay_sim.id=tb_autopay_bank_account.sim_id", "left")->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id'], 'tb_autopay_bank_account.id' => $bank_account_id])->get()->getRow();
    
        if(!is_object($data['bank_account_details']))
            show_404();

        $va_notice = $this->request->getGet('va_notice');
        $data['va_add'] = $this->request->getGet('va_add');

        if($va_notice == "yes") {
            set_alert("success", "Liên kết thành công. Vui lòng thêm ít nhất một Tài khoản phụ (VA) và dùng VA để nhận thanh toán.");
            return redirect()->to('bankaccount/sub/' . $bank_account_id . '?va_add=yes');
        }

        echo view('templates/autopay/header',$data);
        if($data['bank_account_details']->bank_id == 17 || $data['bank_account_details']->bank_id == 18)
            echo view('bankaccount/sub_va',$data);
        else
            echo view('bankaccount/sub',$data);
        echo view('templates/autopay/footer',$data);

    }

    public function ajax_get_bank_sms_info($bank_id='') {
        if(!is_numeric($bank_id))
            return $this->response->setJSON(array("status"=>false));

        $bankModel = model(BankModel::class);

        $bank_details = $bankModel->where(['id' => $bank_id])->get()->getRow();
        if(!is_object($bank_details))
            return $this->response->setJSON(array("status"=>false));

        $results = get_bank_sms_info($bank_details->brand_name);

        return $this->response->setJSON(array("status"=>true,'brand_name' => $bank_details->brand_name, 'data' => $results));

    }

    // for haravan, sapo
    public function ajax_get_vas($bank_account_id='') {
        if(!is_numeric($bank_account_id)) 
            return $this->response->setJSON(array("status"=>FALSE));

        if(!has_permission('BankAccount', 'can_view_all'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền xem tài khoản ngân hàng"));
 
        
        $bankSubAccountModel = model(BankSubAccountmodel::class);
        
        $result = $bankSubAccountModel->select("tb_autopay_bank_sub_account.id,tb_autopay_bank_sub_account.bank_account_id,tb_autopay_bank_sub_account.sub_account,tb_autopay_bank_sub_account.sub_holder_name,tb_autopay_bank_sub_account.va_active, tb_autopay_bank_sub_account.label")->join("tb_autopay_bank_account", "tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id")->where(['tb_autopay_bank_sub_account.bank_account_id'=>$bank_account_id, 'tb_autopay_bank_account.company_id' => $this->user_session['company_id'],'va_active' => 1, 'acc_type' => 'Real'])->get()->getResult();
        
        if($result)
            return $this->response->setJSON(["status"=>TRUE, "data"=>$result]);
        else
            return $this->response->setJSON(array("status"=>FALSE));

    }


    // for telegram, lark
    public function ajax_get_va_by_bank_account($bank_account_id='') {
        if(!is_numeric($bank_account_id)) 
            return $this->response->setJSON(array("status"=>FALSE));

        if(!has_permission('BankAccount', 'can_view_all'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền xem tài khoản ngân hàng"));
 
        
        $bankSubAccountModel = model(BankSubAccountmodel::class);
        
        $result = $bankSubAccountModel->select("tb_autopay_bank_sub_account.id,tb_autopay_bank_sub_account.bank_account_id,tb_autopay_bank_sub_account.sub_account,tb_autopay_bank_sub_account.sub_holder_name,tb_autopay_bank_sub_account.va_active, tb_autopay_bank_sub_account.label")->join("tb_autopay_bank_account", "tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id")->where(['tb_autopay_bank_sub_account.bank_account_id'=>$bank_account_id, 'tb_autopay_bank_account.company_id' => $this->user_session['company_id'],'va_active' => 1])->get()->getResult();
        
        if($result)
            return $this->response->setJSON(["status"=>TRUE, "data"=>$result]);
        else
            return $this->response->setJSON(array("status"=>FALSE));

    }
 
    public function ajax_assign_sim()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        if(!has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền cấm SIM cho tài khoản ngân hàng"));
        
        $bankAccountModel = model(BankAccountmodel::class);
        $bank_account_id = $this->request->getPost('id');

        $result = $bankAccountModel->where(['id'=>$bank_account_id, 'company_id' => $this->user_session['company_id']])->get()->getRow();
        
        if(!is_object($result))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy tài khoản ngân hàng này"));

        $data = [];
        
        $sim_id = $this->request->getPost('sim_id');

        if(is_numeric($sim_id)) {
            $simCompanyModel = model(SimCompanyModel::class);
            $sim_details = $simCompanyModel->select("tb_autopay_sim.id")->join("tb_autopay_sim","tb_autopay_sim.id=tb_autopay_sim_company.sim_id")->where(['tb_autopay_sim_company.company_id' => $this->user_session['company_id'], "tb_autopay_sim.active" => 1,"tb_autopay_sim.id" => $sim_id])->get()->getRow();

            if(is_object($sim_details))
                $data['sim_id'] = $sim_id;
            else
                return $this->response->setJSON(array("status"=>FALSE,"message"=>"SIM nhận SMS mà bạn chọn không khả dụng"));
        }

        $result = $bankAccountModel->set($data)->where("id",$bank_account_id)->update();

        if($result) { 
            add_user_log(array('data_id'=>$bank_account_id,'company_id' => $this->user_session['company_id'], 'data_type'=>'bank_account_update','description'=>'Sửa tài khoản ngân hàng','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
            return $this->response->setJSON(array("status"=>true));
        } else {
            return $this->response->setJSON(array("status"=>false,"message"=>"Không thể cập nhật tài khoản ngân hàng!"));
        }
    }

    public function permissionByPos($bankAccountId = '')
    {
        if (!is_numeric($bankAccountId)) show_404();

        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin']))
            return redirect()->to(base_url());

        if (! $this->validateData([
            'va_id' => $this->request->getGet('va_id'),
        ], [
            'va_id' => 'permit_empty|string|is_natural',
        ])) {
            return redirect()->to(base_url('bankaccount/permissionByPos/' . $bankAccountId));
        }

        $data = [
            'page_title' => 'Mobile app',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
        ];

        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $userModel = model(UserModel::class);      
        $companyUserModel = model(CompanyUserModel::class);

        $bankAccountDetails = $bankAccountModel
            ->select(['tb_autopay_bank_account.id', 'tb_autopay_bank.icon_path', 'tb_autopay_bank_account.account_number', 'tb_autopay_bank.full_name', 'tb_autopay_bank.brand_name'])
            ->join('tb_autopay_bank', 'tb_autopay_bank.id = tb_autopay_bank_account.bank_id')
            ->where([
                'tb_autopay_bank_account.id' => $bankAccountId,
                'tb_autopay_bank_account.company_id' => $this->user_session['company_id'],
            ])->first();

        if (!$bankAccountDetails) show_404();

        $vaId = trim(xss_clean($this->request->getGet('va_id')));

        if (is_numeric($vaId)) {
            $bankSubAccountDetails = $bankSubAccountModel->where([
                'bank_account_id' => $bankAccountDetails->id,
                'id' => $vaId,
            ])->first();
        } else {
            $bankSubAccountDetails = null;
        }

        $data['bank_account_details'] = $bankAccountDetails;
        $data['bank_sub_account_details'] = $bankSubAccountDetails;

        echo view('templates/autopay/header', $data);

        if ($data['bank_sub_account_details']) {
            echo view('bankaccount/permission_by_pos/details', $data);
        } else {
            echo view('bankaccount/permission_by_pos/index', $data);
        }
        echo view('templates/autopay/footer', $data);
    }

    public function ajax_get_va_list_by_bank_account($bankAccountId = '')
    {
        if (!is_numeric($bankAccountId)) show_404();

        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin']))
            return redirect()->to(base_url());
     
        if ($this->request->getMethod(true) != 'POST')
            return '';

        $bankAccountModel = model(BankAccountModel::class);
        $bankAccountDetails = $bankAccountModel
            ->select(['tb_autopay_bank_account.id', 'tb_autopay_bank.icon_path', 'tb_autopay_bank_account.account_number', 'tb_autopay_bank_account.bank_api'])
            ->join('tb_autopay_bank', 'tb_autopay_bank.id = tb_autopay_bank_account.bank_id')
            ->where([
                'tb_autopay_bank_account.id' => $bankAccountId,
                'tb_autopay_bank_account.company_id' => $this->user_session['company_id'],
            ])->first();

        if (!$bankAccountDetails) show_404();

        $bankSubAccountModel = model(BankSubAccountModel::class);
        $bankSubAccounts = $bankSubAccountModel->getDatatables($this->user_session['company_id'], $bankAccountId);
        $data = [];
        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');
 
        foreach ($bankSubAccounts as $bankSubAccount) {
            $no++;
            $row = array();
            $actionsBtnHtml = '';
          
            $row[] = $no;
            $row[] = $bankSubAccount->id;
            $row[] = esc($bankSubAccount->sub_account);
          
            if ($bankSubAccount->acc_type == 'Real') {
                $row[] = esc($bankSubAccount->sub_holder_name);
            } else {
                $row[] = esc($bankSubAccount->label);
            }

            if ($bankSubAccount->acc_type == 'Real') {
                if ($bankSubAccount->va_active == 1)
                    $row[] = "<span class='text-success'>Hoạt động</span>";
                else
                    $row[] = "<span class='text-danger'>Tạm ngưng</span>";
            } else {
                $row[] = "<span class='text-success'>Hoạt động</span>";
            }

            $row[] = "<a href='" . base_url('bankaccount/permissionByPos/' . $bankAccountDetails->id . '?va_id=' . $bankSubAccount->id) . "' class='d-inline-flex align-items-center btn btn-sm btn-outline-primary' style='gap: 0.25rem'><i class='bi bi-shield'></i> Chia sẻ biến động số dư</button>";
            $data[] = $row;
        }
 
        return $this->response->setJSON([
            'draw' => $draw,
            'recordsTotal' => $bankSubAccountModel->countAll($this->user_session['company_id'], $bankAccountId),
            'recordsFiltered' => $bankSubAccountModel->countFiltered($this->user_session['company_id'], $bankAccountId),
            'data' => $data,
        ]);
    }

    public function ajax_get_user_by_va_permission($bankAccountId = '')
    {
        if (!is_numeric($bankAccountId)) show_404();

        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin']))
            return $this->respond(['status' => false, 'message' => 'Bạn không có quyền truy cập.']);

        $bankAccountModel = model(BankAccountModel::class);      
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $userModel = model(UserModel::class);

        $bankAccountDetails = $bankAccountModel
            ->select(['tb_autopay_bank_account.id', 'tb_autopay_bank.icon_path', 'tb_autopay_bank_account.account_number'])
            ->join('tb_autopay_bank', 'tb_autopay_bank.id = tb_autopay_bank_account.bank_id')
            ->where([
                'tb_autopay_bank_account.id' => $bankAccountId,
                'tb_autopay_bank_account.company_id' => $this->user_session['company_id'],
            ])->first();

        if (!$bankAccountDetails)
            return $this->respond(['status' => false, 'message' => 'ID tài khoản ngân hàng không tồn tại.']);

        $vaId = $this->request->getGet('va_id');

        if (!is_numeric($vaId)) {
            return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng tải lại trang.']);
        }

        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');
        $length = $this->request->getVar('length');
        $query = $this->request->getVar('search')['value'] ?? '';

        $db = db_connect();

        $builder = function() use ($vaId, $bankAccountDetails, $query, $db) {
            $builder = $db->table('tb_autopay_user')
            ->select(['tb_autopay_user.id', 'tb_autopay_user.email', 'tb_autopay_user.firstname', 'tb_autopay_user.lastname', 'tb_autopay_user.active'])
            ->join('tb_autopay_company_user', 'tb_autopay_company_user.user_id = tb_autopay_user.id')
            ->join('tb_autopay_user_permission_bank', 'tb_autopay_user_permission_bank.user_id = tb_autopay_user.id')
            ->join('tb_autopay_user_permission_bank_sub', 'tb_autopay_user_permission_bank_sub.user_id = tb_autopay_user.id')
            ->where('tb_autopay_company_user.company_id', $this->user_session['company_id'])
            ->where('tb_autopay_user_permission_bank.bank_account_id', $bankAccountDetails->id)
            ->where('tb_autopay_company_user.role', 'User')
            ->where('tb_autopay_user_permission_bank_sub.sub_account_id', $vaId)
            ->where(new RawSql("EXISTS(SELECT 1 FROM tb_autopay_user_permission_feature WHERE feature_slug = 'Transactions' AND can_view_all = 1 AND user_id = tb_autopay_user.id)"))
            ->where(new RawSql("EXISTS(SELECT 1 FROM tb_autopay_user_permission_feature WHERE feature_slug = 'PushMobileTransactionNotification' AND can_view_all = 1 AND user_id = tb_autopay_user.id)"));
        
            if ($query) {
                $builder->groupStart()
                    ->like('email', $query)
                    ->orLike('firstname', $query)
                    ->orLike('lastname', $query)
                ->groupEnd();
            }

            return $builder;
        };

        $total = $builder()->countAllResults();
        $users = $builder()->limit($length, $no)->get()->getResult();
        $data = [];

        foreach ($users as $user) {
            $no++;
            $row = [];
            
            $row[] = $no;
            $row[] = $user->id;
            $row[] = esc($user->email);
            
            $row[] = esc($user->lastname . ' ' . $user->firstname);

            if ($user->active)
                $row[] = "<span class='badge bg-success'>Hoạt động</span>";
            else
                $row[] = "<span class='badge bg-danger'>Tạm đóng</span>";

            $row[] = '
                <button type="button" class="btn btn-outline-primary btn-sm" onclick="editUser(' . $user->id . ')"><i class="bi bi-pencil"></i> Sửa</button>
                <button type="button" class="btn btn-outline-warning btn-sm" onclick="revokeUserPermission(' . $user->id . ')"><i class="bi bi-shield-x"></i> Gỡ quyền</button>
            ';
       
            $data[] = $row;
        }

        return $this->response->setJSON([
            'draw' => $draw,
            'recordsTotal' => $total,
            'recordsFiltered' => $total,
            'data' => $data,
        ]);
    }

    public function ajax_create_user_by_pos()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin']))
            return $this->respond(['status' => false, 'message' => 'Bạn không có quyền truy cập.']);

        $bankAccountId = trim(xss_clean($this->request->getVar('bank_account_id')));
        $bankSubAccountId = trim(xss_clean($this->request->getVar('bank_sub_account_id')));

        if (!is_numeric($bankAccountId) || !is_numeric($bankSubAccountId)) show_404();

        $bankAccountModel = model(BankAccountModel::class);      
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $userModel = model(UserModel::class);
        $companyUserModel = model(CompanyUserModel::class);
        $userPermissionFeatureModel = model(UserPermissionFeatureModel::class);

        $bankAccountDetails = $bankAccountModel
            ->select(['tb_autopay_bank_account.id', 'tb_autopay_bank.icon_path', 'tb_autopay_bank_account.account_number'])
            ->join('tb_autopay_bank', 'tb_autopay_bank.id = tb_autopay_bank_account.bank_id')
            ->where([
                'tb_autopay_bank_account.id' => $bankAccountId,
                'tb_autopay_bank_account.company_id' => $this->user_session['company_id'],
            ])->first();

        if (!$bankAccountDetails)
            return $this->respond(['status' => false, 'message' => 'ID tài khoản ngân hàng không tồn tại.']);

        $bankSubAccountDetails = $bankSubAccountModel->where([
            'id' => $bankSubAccountId,
            'bank_account_id' => $bankAccountDetails->id
        ])->first();

        if (!$bankSubAccountDetails)
            return $this->respond(['status' => false, 'message' => 'ID tài khoản phụ không tồn tại.']);

        $userData = [
            'lastname' => mb_convert_case(xss_clean($this->request->getVar('lastname')), MB_CASE_TITLE, 'UTF-8'),
            'firstname' => mb_convert_case(xss_clean($this->request->getVar('firstname')), MB_CASE_TITLE, 'UTF-8'),
            'email' => trim($this->request->getVar('email')),
            'password' => $this->request->getVar('password'),
            'permissions' => [
                'hide_amount_out' => $this->request->getVar('hide_amount_out') ? 1 : 0,
                'hide_accumulated' => $this->request->getVar('hide_accumulated') ? 1 : 0,
                'hide_reference_number' => $this->request->getVar('hide_reference_number') ? 1 : 0,
                'hide_transaction_content' => $this->request->getVar('hide_transaction_content') ? 1 : 0,
            ]
        ];

        $rules = [
            'lastname' => ['required', 'min_length[2]', 'max_length[50]'],
            'firstname' => ['required', 'min_length[2]', 'max_length[50]'],
            'email' => [
                'rules' => ['required', 'valid_email', 'is_unique[tb_autopay_user.email]'],
                'label' => 'email',
                'errors' => [
                    'is_unique' => 'Email đã được sử dụng, vui lòng sử dụng email khác'
                ]
            ],
            'password' => ['required', 'min_length[8]', 'max_length[250]'],
        ];

        if (! $this->validateData($userData, $rules)) {
            return $this->fail($this->validator->getErrors());
        }

        $safeUserData = [
            'firstname' => $userData['firstname'],
            'lastname' => $userData['lastname'],
            'email' => $userData['email'],
            'password' => password_hash($userData['password'], PASSWORD_DEFAULT),
            'active' => 1,
        ];

        $userId = $userModel->insert($safeUserData);

        if (!$userId) {
            return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng thử lại']);
        }

        $companyUserModel->insert([
            'company_id' => $this->user_session['company_id'],
            'user_id' => $userId,
            'role' => 'User'
        ]);

        $userPermissionFeatureModel->initialize_permission($userId, $this->user_session['company_id']);
        $this->assignPosPermissionToUser(
            $userId, 
            $this->user_session['company_id'], 
            $bankAccountDetails->id, 
            $bankSubAccountDetails->id, 
            $userData['permissions'], 
        );

        add_user_log(['data_id' => $userId, 'company_id' => $this->user_session['company_id'], 'data_type' => 'admin_create_user', 'description' => 'Tạo người dùng', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);

        return $this->respond(['status' => true, 'message' => 'Tạo nhân viên thành công']);
    }

    public function ajax_get_user_by_pos()
    {
        $userId = trim(xss_clean($this->request->getGet('user_id')));
        $bankAccountId = trim(xss_clean($this->request->getGet('bank_account_id')));
        $bankSubAccountId = trim(xss_clean($this->request->getGet('bank_sub_account_id')));

        if (!is_numeric($userId) || !is_numeric($bankAccountId) || !is_numeric($bankSubAccountId)) show_404();

        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin']))
            return $this->respond(['status' => false, 'message' => 'Bạn không có quyền truy cập.']);

        $userModel = model(UserModel::class);
        $userPermissionBankModel = model(UserPermissionBankModel::class);
        $userPermissionFeatureModel = model(UserPermissionFeatureModel::class);

        $user = $userModel
            ->select(['tb_autopay_user.id', 'tb_autopay_user.email', 'tb_autopay_user.firstname', 'tb_autopay_user.lastname', 'tb_autopay_user.active'])
            ->join('tb_autopay_company_user', 'tb_autopay_company_user.user_id = tb_autopay_user.id')
            ->join('tb_autopay_user_permission_bank', 'tb_autopay_user_permission_bank.user_id = tb_autopay_user.id')
            ->join('tb_autopay_user_permission_bank_sub', 'tb_autopay_user_permission_bank_sub.user_id = tb_autopay_user.id')
            ->where('tb_autopay_company_user.company_id', $this->user_session['company_id'])
            ->where('tb_autopay_user_permission_bank.bank_account_id', $bankAccountId)
            ->where('tb_autopay_user_permission_bank_sub.sub_account_id', $bankSubAccountId)
            ->where('tb_autopay_company_user.role', 'User')
            ->where('tb_autopay_user.id', $userId)
            ->first();

        if (!$user) {
            return $this->respond(['status' => false, 'message' => 'ID người dùng không tồn tại.']);
        }

        $bankPermission = $userPermissionBankModel
            ->select(['hide_amount_out', 'hide_accumulated', 'hide_reference_number', 'hide_transaction_content'])
            ->where(['user_id' => $user->id, 'bank_account_id' => $bankAccountId])->first();

        $pushMobileTransactionNotificationPermission = $userPermissionFeatureModel
            ->select(['can_view_all'])
            ->where(['user_id' => $user->id, 'feature_slug' => 'PushMobileTransactionNotification'])->first();

        if (!$bankPermission) {
            return $this->respond(['status' => false, 'message' => 'Người dùng không có quyền với tài khoản ngân hàng này.']);
        }

        return $this->respond([
            'data' => array_merge((array) $user, [
                'permissions' => (array) $bankPermission
            ])
        ]);
    }

    public function ajax_edit_user_by_pos()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin']))
            return $this->respond(['status' => false, 'message' => 'Bạn không có quyền truy cập.']);

        $userId = trim(xss_clean($this->request->getVar('user_id')));
        $bankAccountId = trim(xss_clean($this->request->getVar('bank_account_id')));
        $bankSubAccountId = trim(xss_clean($this->request->getVar('bank_sub_account_id')));

        if (!is_numeric($userId) || !is_numeric($bankAccountId) || !is_numeric($bankSubAccountId)) show_404();

        $userModel = model(UserModel::class);
        $userPermissionFeatureModel = model(UserPermissionFeatureModel::class);

        $user = $userModel
            ->select(['tb_autopay_user.id', 'tb_autopay_user.email', 'tb_autopay_user.firstname', 'tb_autopay_user.lastname', 'tb_autopay_user.active'])
            ->join('tb_autopay_company_user', 'tb_autopay_company_user.user_id = tb_autopay_user.id')
            ->join('tb_autopay_user_permission_bank', 'tb_autopay_user_permission_bank.user_id = tb_autopay_user.id')
            ->join('tb_autopay_user_permission_bank_sub', 'tb_autopay_user_permission_bank_sub.user_id = tb_autopay_user.id')
            ->where('tb_autopay_company_user.company_id', $this->user_session['company_id'])
            ->where('tb_autopay_user_permission_bank.bank_account_id', $bankAccountId)
            ->where('tb_autopay_user_permission_bank_sub.sub_account_id', $bankSubAccountId)
            ->where('tb_autopay_company_user.role', 'User')
            ->where('tb_autopay_user.id', $userId)
            ->first();

        if (!$user) {
            return $this->respond(['status' => false, 'message' => 'ID người dùng không tồn tại.']);
        }

        $userData = [
            'lastname' => mb_convert_case(xss_clean($this->request->getVar('lastname')), MB_CASE_TITLE, 'UTF-8'),
            'firstname' => mb_convert_case(xss_clean($this->request->getVar('firstname')), MB_CASE_TITLE, 'UTF-8'),
            'password' => $this->request->getVar('password'),
            'active' => $this->request->getVar('active'),
            'permissions' => [
                'hide_amount_out' => $this->request->getVar('hide_amount_out') ? 1 : 0,
                'hide_accumulated' => $this->request->getVar('hide_accumulated') ? 1 : 0,
                'hide_reference_number' => $this->request->getVar('hide_reference_number') ? 1 : 0,
                'hide_transaction_content' => $this->request->getVar('hide_transaction_content') ? 1 : 0,
            ]
        ];

        $rules = [
            'lastname' => ['required', 'min_length[2]', 'max_length[50]'],
            'firstname' => ['required', 'min_length[2]', 'max_length[50]'],
            'password' => ['permit_empty', 'min_length[8]', 'max_length[250]'],
        ];

        if (! $this->validateData($userData, $rules)) {
            return $this->fail($this->validator->getErrors());
        }

        $safeUserData = [
            'firstname' => $userData['firstname'],
            'lastname' => $userData['lastname'],
            'active' => $userData['active'] ? 1 : 0,
        ];

        if ($userData['password']) {
            $safeUserData['password'] = password_hash($userData['password'], PASSWORD_DEFAULT);
        }

        $userModel->where(['id' => $user->id])->set($safeUserData)->update();
        
        $this->assignPosPermissionToUser(
            $userId, 
            $this->user_session['company_id'], 
            $bankAccountId, 
            $bankSubAccountId, 
            $userData['permissions'],
        );

        return $this->respond(['status' => true, 'message' => 'Cập nhật nhân viên thành công']);
    }

    public function ajax_revoke_pos_permission_to_user()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin']))
            return $this->respond(['status' => false, 'message' => 'Bạn không có quyền truy cập.']);

        $userId = trim(xss_clean($this->request->getVar('user_id')));
        $bankAccountId = trim(xss_clean($this->request->getVar('bank_account_id')));
        $bankSubAccountId = trim(xss_clean($this->request->getVar('bank_sub_account_id')));

        if (!is_numeric($userId) || !is_numeric($bankAccountId) || !is_numeric($bankSubAccountId)) show_404();

        $userModel = model(UserModel::class);
        $userPermissionFeatureModel = model(UserPermissionFeatureModel::class);

        $user = $userModel
            ->select(['tb_autopay_user.id', 'tb_autopay_user.email', 'tb_autopay_user.firstname', 'tb_autopay_user.lastname', 'tb_autopay_user.active'])
            ->join('tb_autopay_company_user', 'tb_autopay_company_user.user_id = tb_autopay_user.id')
            ->join('tb_autopay_user_permission_bank', 'tb_autopay_user_permission_bank.user_id = tb_autopay_user.id')
            ->join('tb_autopay_user_permission_bank_sub', 'tb_autopay_user_permission_bank_sub.user_id = tb_autopay_user.id')
            ->where('tb_autopay_company_user.company_id', $this->user_session['company_id'])
            ->where('tb_autopay_user_permission_bank.bank_account_id', $bankAccountId)
            ->where('tb_autopay_user_permission_bank_sub.sub_account_id', $bankSubAccountId)
            ->where('tb_autopay_company_user.role', 'User')
            ->where('tb_autopay_user.id', $userId)
            ->first();

        if (!$user) {
            return $this->respond(['status' => false, 'message' => 'ID người dùng không tồn tại.']);
        }

        $userPermissionBankSubModel = model(UserPermissionBankSubModel::class);
        $userPermissionBankSubModel->where(['user_id' => $user->id, 'sub_account_id' => $bankSubAccountId])->delete();

        return $this->respond(['status' => true, 'message' => 'Đã gỡ quyền nhân viên thành công.']);
    }

    public function ajax_get_able_assign_pos_permission_user()
    {
        $bankAccountId = trim(xss_clean($this->request->getGet('bank_account_id')));
        $bankSubAccountId = trim(xss_clean($this->request->getGet('bank_sub_account_id')));
        $query = trim(xss_clean($this->request->getGet('query')));

        if (!is_numeric($bankAccountId) || !is_numeric($bankSubAccountId)) show_404();

        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin']))
            return $this->respond(['status' => false, 'message' => 'Bạn không có quyền truy cập.']);

        $bankAccountModel = model(BankAccountModel::class);      
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $userModel = model(UserModel::class);
        
        $db = db_connect();
        $builder = $db->table('tb_autopay_user')
            ->select(['tb_autopay_user.id', 'tb_autopay_user.email', 'tb_autopay_user.firstname', 'tb_autopay_user.lastname', 'tb_autopay_user.active'])
            ->join('tb_autopay_company_user', 'tb_autopay_company_user.user_id = tb_autopay_user.id')
            ->where('tb_autopay_company_user.company_id', $this->user_session['company_id'])
            ->where('tb_autopay_company_user.role', 'User')
            ->groupStart()
                ->where(new RawSql("NOT EXISTS(SELECT 1 FROM tb_autopay_user_permission_bank_sub WHERE sub_account_id = '" . $db->getConnection()->real_escape_string($bankSubAccountId) . "' AND user_id = tb_autopay_user.id)"))
                ->orGroupStart()
                    ->where(new RawSql("EXISTS(SELECT 1 FROM tb_autopay_user_permission_bank_sub WHERE sub_account_id = '" . $db->getConnection()->real_escape_string($bankSubAccountId) . "' AND user_id = tb_autopay_user.id)"))
                    ->groupStart()
                        ->where(new RawSql("NOT EXISTS(SELECT 1 FROM tb_autopay_user_permission_feature WHERE feature_slug = 'Transactions' AND can_view_all = 1 AND user_id = tb_autopay_user.id)"))
                        ->orWhere(new RawSql("NOT EXISTS(SELECT 1 FROM tb_autopay_user_permission_feature WHERE feature_slug = 'PushMobileTransactionNotification' AND can_view_all = 1 AND user_id = tb_autopay_user.id)"))
                    ->groupEnd()
                ->groupEnd()
            ->groupEnd();
        
            if ($query) {
                $builder->groupStart()
                    ->like('email', $query)
                    ->orLike('firstname', $query)
                    ->orLike('lastname', $query)
                ->groupEnd();
            }

        return $this->respond([
            'data' => $builder->limit(5)->get()->getResult()
        ]);
    }

    public function ajax_assign_pos_permission_to_user()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        $bankAccountId = trim(xss_clean($this->request->getVar('bank_account_id')));
        $bankSubAccountId = trim(xss_clean($this->request->getVar('bank_sub_account_id')));

        if (!is_numeric($bankAccountId) || !is_numeric($bankSubAccountId)) show_404();

        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin']))
            return $this->respond(['status' => false, 'message' => 'Bạn không có quyền truy cập.']);

        $bankAccountModel = model(BankAccountModel::class);      
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $userModel = model(UserModel::class);

        $bankAccountDetails = $bankAccountModel
            ->select(['tb_autopay_bank_account.id', 'tb_autopay_bank.icon_path', 'tb_autopay_bank_account.account_number'])
            ->join('tb_autopay_bank', 'tb_autopay_bank.id = tb_autopay_bank_account.bank_id')
            ->where([
                'tb_autopay_bank_account.id' => $bankAccountId,
                'tb_autopay_bank_account.company_id' => $this->user_session['company_id'],
            ])->first();

        if (!$bankAccountDetails)
            return $this->respond(['status' => false, 'message' => 'ID tài khoản ngân hàng không tồn tại.']);

        $bankSubAccountDetails = $bankSubAccountModel->where([
            'id' => $bankSubAccountId,
            'bank_account_id' => $bankAccountDetails->id
        ])->first();

        if (!$bankSubAccountDetails)
            return $this->respond(['status' => false, 'message' => 'ID tài khoản phụ không tồn tại.']);

        $userIdList = $this->request->getVar('user_id_list');
        $assignData = [
            'permissions' => [
                'hide_amount_out' => $this->request->getVar('hide_amount_out') ? 1 : 0,
                'hide_accumulated' => $this->request->getVar('hide_accumulated') ? 1 : 0,
                'hide_reference_number' => $this->request->getVar('hide_reference_number') ? 1 : 0,
                'hide_transaction_content' => $this->request->getVar('hide_transaction_content') ? 1 : 0,
            ]
        ];

        if (!is_array($userIdList))
            return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng tải lại trang.']);

        $db = db_connect();

        foreach ($userIdList as $userId) {
            $user = $db->table('tb_autopay_user')
                ->select(['tb_autopay_user.id', 'tb_autopay_user.email', 'tb_autopay_user.firstname', 'tb_autopay_user.lastname', 'tb_autopay_user.active'])
                ->join('tb_autopay_company_user', 'tb_autopay_company_user.user_id = tb_autopay_user.id')
                ->where('tb_autopay_company_user.company_id', $this->user_session['company_id'])
                ->where('tb_autopay_company_user.role', 'User')
                ->where(new RawSql("NOT EXISTS(SELECT 1 FROM tb_autopay_user_permission_bank_sub WHERE sub_account_id = '" . $db->getConnection()->real_escape_string($bankSubAccountId) . "' AND user_id = tb_autopay_user.id)"))
                ->get()->getRow();

            if (!$user) continue;

            $this->assignPosPermissionToUser(
                $userId, 
                $this->user_session['company_id'], 
                $bankAccountDetails->id, 
                $bankSubAccountDetails->id, 
                $assignData['permissions'], 
            );
        }

        return $this->respond(['status' => true, 'message' => 'Phân quyền thành công.']);
    }

    public function settings($id)
    {
        if (! has_permission('BankAccount', 'can_edit')) {
            show_404();
        }

        if (! is_numeric($id)) {
            show_404();
        }

        $bankAccount = model(BankAccountModel::class)
            ->select([
                'tb_autopay_bank_account.id',
                'tb_autopay_bank_account.bank_id',
                'tb_autopay_bank_account.account_number',
                'tb_autopay_bank_account.bank_sms',
                'tb_autopay_bank_account.bank_sms_connected',
                'tb_autopay_bank.icon_path',
                'tb_autopay_bank.brand_name',
                'tb_autopay_bank.short_name',
            ])
            ->join('tb_autopay_bank', 'tb_autopay_bank.id = tb_autopay_bank_account.bank_id')
            ->where('company_id', $this->user_session['company_id'])
            ->find($id);

        if (! $bankAccount) {
            show_404();
        }

        if (! can_show_bank_account_settings($bankAccount->bank_id, $bankAccount->bank_sms)) {
            show_404();
        }

        $data = [
            'page_title' => 'Cấu hình chung',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
        ];

        $data['bankAccount'] = $bankAccount;
        $data['settings'] = bank_account_settings($bankAccount->id);
        $data['transactionsCount'] = slavable_model(TransactionsModel::class, 'Bankaccount')
            ->getQueryByBankAccount($this->company_details->id, $bankAccount->id)
            ->countAllResults();

        echo theme_view('templates/autopay/header', $data);
        echo theme_view('bankaccount/settings', $data);
        echo theme_view('templates/autopay/footer', $data);
    }

    public function ajax_update_settings($id)
    {
        if ($this->request->getMethod(true) !== 'POST') {
            show_404();
        }

        if (! is_numeric($id)) {
            show_404();
        }

        if (! has_permission('BankAccount', 'can_edit')) {
            return $this->respond([
                'status' => false,
                'message' => 'Bạn không có quyền cập nhật cấu hình tài khoản ngân hàng.',
            ]);
        }

        $bankAccount = model(BankAccountModel::class)
            ->where('company_id', $this->user_session['company_id'])
            ->find($id);

        if (! $bankAccount) {
            return $this->respond([
                'status' => false,
                'message' => 'Tài khoản ngân hàng không tồn tại.',
            ]);
        }

        $rules = [
            'sync_transactions' => 'required|in_list[0,1]',
        ];

        $data = [
            'sync_transactions' => $this->request->getVar('sync_transactions'),
        ];

        if ($data['sync_transactions'] == 1) {
            $rules['sync_amount_in'] = 'required|in_list[0,1]';
            $rules['whitelist_keywords'] = 'permit_empty';

            $data['sync_amount_in'] = $this->request->getVar('sync_amount_in');
            $data['whitelist_keywords'] = (array) $this->request->getVar('whitelist_keywords');

            if (bank_account_supported_amount_out($bankAccount->id)) {
                $rules['sync_amount_out'] = 'required|in_list[0,1]';
                $data['sync_amount_out'] = $this->request->getVar('sync_amount_out');
            }

            if (bank_account_supported_balance($bankAccount->id)) {
                $rules['sync_accumulated'] = 'required|in_list[0,1]';
                $data['sync_accumulated'] = $this->request->getVar('sync_accumulated');
            }
        }

        if (! $this->validateData($data, $rules)) {
            return $this->failValidationErrors($this->validator->getErrors());
        }

        $settings = bank_account_settings($bankAccount->id);
        $settings->set($data);
        $settings->clearCache();
        
        add_user_log([
            'data_id' => $bankAccount->id,
            'company_id' => $this->user_session['company_id'],
            'data_type' => 'bank_account_settings',
            'description' => 'Cập nhật cấu hình tài khoản ngân hàng',
            'user_id' => $this->user_details->id,
            'ip' => $this->request->getIPAddress(),
            'user_agent' => $this->request->getUserAgent()->getAgentString(),
            'status' => 'Success',
        ]);

        return $this->respond([
            'status' => true,
            'message' => 'Cập nhật thành công',
        ]);
    }

    public function ajax_delete_all_transactions($id)
    {
        if ($this->request->getMethod(true) !== 'POST') {
            show_404();
        }

        if (! is_numeric($id)) {
            show_404();
        }

        if (! has_permission('Transactions', 'can_delete')) {
            return $this->respond([
                'status' => false,
                'message' => 'Bạn không có quyền xóa giao dịch.',
            ]);
        }

        $bankAccount = model(BankAccountModel::class)
            ->where('company_id', $this->user_session['company_id'])
            ->find($id);

        if (! $bankAccount) {
            return $this->respond([
                'status' => false,
                'message' => 'Tài khoản ngân hàng không tồn tại.',
            ]);
        }

        $transactions = slavable_model(TransactionsModel::class, 'Bankaccount')
            ->getQueryByBankAccount($this->company_details->id, $bankAccount->id)
            ->findAll();

        if (empty($transactions)) {
            return $this->respond([
                'status' => false,
                'message' => 'Không có giao dịch nào để xóa.',
            ]);
        }

        model(TransactionsModel::class)->delete(array_column($transactions, 'id'));

        add_system_log([
            'company_id' => $this->user_session['company_id'],
            'data_type' => 'soft_delete_all_transactions',
            'description' => 'Soft delete all transactions',
            'level' => 'Info',
            'by' => 'Bankaccount Controller', 
        ]);

        $smsIds = array_filter(array_column($transactions, 'sms_id'));

        $sms = slavable_model(SmsModel::class, 'Bankaccount')
            ->select('id')
            ->where('deleted_at', null)
            ->whereIn('id', $smsIds)
            ->findAll();

        if (! empty($sms)) {
            model(SmsModel::class)->delete(array_column($sms, 'id'));
        }

        add_system_log([
            'company_id' => $this->user_session['company_id'],
            'data_type' => 'soft_delete_all_sms',
            'description' => 'Soft delete all sms',
            'level' => 'Info',
            'by' => 'Bankaccount Controller', 
        ]);

        set_alert('success', 'Đã xóa tất cả giao dịch thành công.');

        return $this->respond(['status' => true]); 
    }

    protected function assignPosPermissionToUser($userId, $companyId, $bankAccountId, $bankSubAccountId, $bankPermissions = [], $pushMobileTransactionNotificationFeature = 1)
    {
        $bankPermissions['hide_amount_out'] = isset($bankPermissions['hide_amount_out']) ? $bankPermissions['hide_amount_out'] : 0;
        $bankPermissions['hide_accumulated'] = isset($bankPermissions['hide_accumulated']) ? $bankPermissions['hide_accumulated'] : 0;
        $bankPermissions['hide_reference_number'] = isset($bankPermissions['hide_reference_number']) ? $bankPermissions['hide_reference_number'] : 0;
        $bankPermissions['hide_transaction_content'] = isset($bankPermissions['hide_transaction_content']) ? $bankPermissions['hide_transaction_content'] : 0;
        
        $userPermissionFeatureModel = model(UserPermissionFeatureModel::class);
        $userPermissionBankModel = model(UserPermissionBankModel::class);
        $userPermissionBankSubModel = model(UserPermissionBankSubModel::class);

        $transactionFeaturePermission = $userPermissionFeatureModel->where(['feature_slug' => 'Transactions', 'user_id' => $userId])->first();
        $pushMobileTransactionNotificationFeaturePermission = $userPermissionFeatureModel->where(['feature_slug' => 'PushMobileTransactionNotification', 'user_id' => $userId])->first();

        if (! $transactionFeaturePermission) {
            $userPermissionFeatureModel->insert([
                'user_id' => $userId,
                'company_id' => $companyId,
                'feature_slug' => 'Transactions',
                'can_view_all' => 1,
            ]);
        } else if ($transactionFeaturePermission && $transactionFeaturePermission->can_view_all == 0) {
            $userPermissionFeatureModel
                ->where(['id' => $transactionFeaturePermission->id])
                ->set(['can_view_all' => 1])
                ->update();
        }

        if ($pushMobileTransactionNotificationFeature && ! $pushMobileTransactionNotificationFeaturePermission) {
            $userPermissionFeatureModel->insert([
                'user_id' => $userId,
                'feature_slug' => 'PushMobileTransactionNotification',
                'can_view_all' => 1,
            ]);
        } else if ($pushMobileTransactionNotificationFeature && $pushMobileTransactionNotificationFeaturePermission && $pushMobileTransactionNotificationFeaturePermission->can_view_all == 0) {
            $userPermissionFeatureModel
                ->where(['id' => $pushMobileTransactionNotificationFeaturePermission->id])
                ->set(['can_view_all' => 1])
                ->update();
        } else if (!$pushMobileTransactionNotificationFeature && $pushMobileTransactionNotificationFeaturePermission) {
            $userPermissionFeatureModel
                ->where(['id' => $pushMobileTransactionNotificationFeaturePermission->id])
                ->set(['can_view_all' => 0])
                ->update();
        }

        $bankAccountPermission = $userPermissionBankModel->where(['user_id' => $userId, 'bank_account_id' => $bankAccountId])->first();
        $bankSubAccountPermission = $userPermissionBankSubModel->where(['user_id' => $userId, 'sub_account_id' => $bankSubAccountId])->first();

        if (! $bankAccountPermission) {
            $userPermissionBankModel->insert(array_merge($bankPermissions, [
                'user_id' => $userId,
                'bank_account_id' => $bankAccountId
            ]));
        } else if ($bankAccountPermission) {
            $userPermissionBankModel->where(['id' => $bankAccountPermission->id])
                ->set($bankPermissions)->update();
        }

        if (! $bankSubAccountPermission) {
            $userPermissionBankSubModel->insert([
                'user_id' => $userId,
                'sub_account_id' => $bankSubAccountId
            ]);
        }

        model(UserPermissionFeatureModel::class)->set(['can_view_all' => 1])
            ->where(['user_id' => $userId, 'company_id' => $companyId])
            ->whereIn('feature_slug', ['Transactions', 'BankAccount'])
            ->update();

        return true;
    }

    protected function parse_checklist($html) {
        $html = preg_replace('/<li>\[ \] (.*?)<\/li>/', '<li><label class="form-check"><input type="checkbox" class="form-check-input"><span class="form-check-label">$1</span></label></li>', $html);
    
        return $html;
    }
}
