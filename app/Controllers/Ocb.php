<?php

namespace App\Controllers;
use App\Models\SimModel;
use App\Models\ShopModel;
use CodeIgniter\Controller;
use App\Libraries\OcbClient;
use App\Models\BankAccountModel;
use App\Models\BankShopLinkModel;
use App\Models\TransactionsModel;
use App\Models\ConfigurationModel;
use App\Models\UserPermissionBankSubModel;
use App\Models\UserPermissionFeatureModel;
use App\Models\UserPermissionBankModel;
use App\Models\NotificationTelegramModel;
use App\Models\NotificationLarkMessengerModel;
use CodeIgniter\API\ResponseTrait;
use App\Models\BankSubAccountModel;
use App\Models\OcbBankSubAccountMetaData;
use App\Actions\Company\GetCompanyConfigurationAction;
use App\Actions\Company\SetCompanyConfigurationAction;
use App\Models\OutputDeviceDecalModel;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use Psr\Log\LoggerInterface;

class Ocb extends BaseController
{
    use ResponseTrait;

    public function initController(RequestInterface $request, ResponseInterface $response, LoggerInterface $logger)
    {
        parent::initController($request, $response, $logger);

        is_bank_supported_by_promotion(18, $this->company_details->company_id);
    }

    protected function handleOcbClientException($e) {
        if (strpos($e->getMessage(), 'Operation timed out') !== false) {
            return $this->response->setJSON([
                "status" => false,
                "message" => 'Hệ thống OCB đang bận, vui lòng thử lại sau'
            ]);
        }

        log_message('error', $e->getMessage());

        return $this->response->setJSON([
            "status" => false,
            "message" => 'Đã có lỗi xảy ra, vui lòng thử lại sau hoặc liên hệ SePay để được hỗ trợ.'
        ]);
    }

    public function step1()
    { 
        $data = [
            'page_title' => 'Ngân hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
        ];

        if(!has_permission('BankAccount', 'can_add'))
            show_404();
        
        echo theme_view('templates/autopay/header',$data);
        echo theme_view('ocb/individual/step1',$data);
        echo theme_view('templates/autopay/footer',$data);
    }

    public function step2($id='')
    { 
        $ocbConfig = config(\Config\Ocb::class);

        $data = [
            'page_title' => 'Ngân hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'ocb_va_only_number' => $ocbConfig->OcbVAOnlyNumber,
            'prefix_va' => $ocbConfig->OcbPrefixVA,
            'ocb_default_va_number_min_length' => $ocbConfig->OcbDefaultVANumberMinLength,
            'sepay_vip_user_id_list' => $ocbConfig->SePayVIPUserIdList,
            'skip_verify_merchant_info' => $ocbConfig->skipVerifyMerchantInformation ?? false,
        ];

        // Check VIP user
        if (in_array($this->user_details->id, $data['sepay_vip_user_id_list'])) {
            $data['ocb_default_va_number_min_length'] = 1;
        }

        if(!has_permission('BankAccount', 'can_add'))
            show_404();

        if(!is_numeric($id))
            show_404();
 
        $bankAccountModel = model(BankAccountModel::class);
        $transactionsModel = slavable_model(TransactionsModel::class, 'Ocb');

        $data['bank_account_details'] = $bankAccountModel->select("tb_autopay_bank_account.id,tb_autopay_bank_account.account_number,tb_autopay_bank_account.label,tb_autopay_bank_account.active,tb_autopay_bank_account.created_at,tb_autopay_bank.brand_name, tb_autopay_bank_account.account_number,tb_autopay_bank_account.account_holder_name,tb_autopay_bank.icon_path,tb_autopay_bank.logo_path, tb_autopay_bank_account.last_transaction, tb_autopay_bank_account.accumulated,tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.bank_id, tb_autopay_bank_account.identification_number, tb_autopay_bank_account.phone_number")->join("tb_autopay_bank","tb_autopay_bank.id=tb_autopay_bank_account.bank_id")->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id'], 'tb_autopay_bank_account.id' => $id])->get()->getRow();

        if(!is_object($data['bank_account_details']))
            show_404();
        
        $data['count_transactions'] = $transactionsModel->where(['id' => $data['bank_account_details']->id, 'deleted_at' => NULL])->countAllResults();

        if($data['bank_account_details']->bank_api_connected == 1) {
            set_alert('error','Tài khoản này đã mở API rồi. Bạn không cần phải liên kết lại.');
            return redirect()->to('ocb/settings/' . $id);
        }
        
        echo theme_view('templates/autopay/header',$data);
        echo theme_view('ocb/individual/step2',$data);
        echo theme_view('templates/autopay/footer',$data);
    }

    public function step3($id='')
    {
        $data = [
            'page_title' => 'Ngân hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        if(!has_permission('BankAccount', 'can_add') || !is_numeric($id))
            show_404();

        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $transactionsModel = model(TransactionsModel::class);

        $data['bank_account_details'] = $bankAccountModel->select("tb_autopay_bank_account.id,tb_autopay_bank_account.account_number,tb_autopay_bank_account.label,tb_autopay_bank_account.active,tb_autopay_bank_account.created_at,tb_autopay_bank.brand_name, tb_autopay_bank_account.account_number,tb_autopay_bank_account.account_holder_name,tb_autopay_bank.icon_path,tb_autopay_bank.logo_path, tb_autopay_bank_account.last_transaction, tb_autopay_bank_account.accumulated,tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.bank_id")->join("tb_autopay_bank","tb_autopay_bank.id=tb_autopay_bank_account.bank_id")->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id'], 'tb_autopay_bank_account.id' => $id])->get()->getRow();

        if(!is_object($data['bank_account_details']))
            show_404();
        
        $data['va_details'] =  $bankSubAccountModel->where(['bank_account_id' => $id, 'va_order>=' => 1,'acc_type' => 'Real'])->orderBy('id','DESC')->get()->getRow();

        if (!$data['va_details']) {
            return redirect()->to(base_url('ocb/step2/' . $id));
        }

        echo theme_view('templates/autopay/header',$data);
        echo theme_view('ocb/individual/step3',$data);
        echo theme_view('templates/autopay/footer',$data);
    }

    public function settings($id)
    {
        $data = [
            'page_title' => 'Cấu hình',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
        ];

        if(! has_permission('BankAccount', 'can_add') || ! is_numeric($id)) {
            show_404();
        }

        $bankAccount = model(BankAccountModel::class)
            ->select("tb_autopay_bank_account.id,tb_autopay_bank_account.account_number,tb_autopay_bank_account.label,tb_autopay_bank_account.active,tb_autopay_bank_account.created_at,tb_autopay_bank.brand_name, tb_autopay_bank_account.account_number,tb_autopay_bank_account.account_holder_name,tb_autopay_bank.icon_path,tb_autopay_bank.logo_path, tb_autopay_bank_account.last_transaction, tb_autopay_bank_account.accumulated,tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.bank_id")
            ->join("tb_autopay_bank","tb_autopay_bank.id=tb_autopay_bank_account.bank_id")
            ->where('tb_autopay_bank_account.company_id', $this->user_session['company_id'])
            ->where('tb_autopay_bank_account.id', $id)
            ->first();

        if(! $bankAccount) {
            show_404();
        }

        $va = model(BankSubAccountModel::class)
            ->where('bank_account_id', $id)
            ->where('va_order >=', 1)
            ->where('acc_type', 'Real')
            ->orderBy('id', 'DESC')
            ->first();

        if (! $va) {
            return redirect()->to(base_url('ocb/step2/' . $id));
        }

        $data['bankAccount'] = $bankAccount;
        $data['settings'] = bank_account_settings($bankAccount->id);
        $data['va_success'] = $this->request->getGet('va_success');

        echo theme_view('templates/autopay/header', $data);
        echo theme_view('ocb/individual/settings', $data);
        echo theme_view('templates/autopay/footer', $data);
    }

    public function ajax_get_account_name()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!has_permission('BankAccount', 'can_add'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền thêm tài khoản ngân hàng"));
    
        if (! $this->validate([
            'account_number' => [
                'label' => 'số tài khoản',
                'rules' => 'required|min_length[1]|max_length[20]'
            ],
        ])) return $this->fail($this->validator->getErrors());

        try {
            $client = new OcbClient;
        } catch (\Exception $e) {
            return $this->handleOcbClientException($e);
        }

        try {
            $response = $client->retrieveOCBRecipientName(
                trim($this->request->getVar('account_number'))
            );
        } catch (\Exception $e) {
            return $this->handleOcbClientException($e);
        }

        $responseData = json_decode($response->getBody(), true);

        if (isset($responseData['trace']['bankRefNo']) && !isset($responseData['error'])) {
            return $this->response->setJSON([
                'account_holder_name' => $responseData['data']['queryResult']['accountName']
            ]);
        } else {
            $errorMessage = 'Không thể kết nối với OCB, vui lòng liên hệ với SePay để được hỗ trợ';
            log_message('error', $responseData['error']['details']);

            if ($responseData['error']['code'] === '41715') {
                return $this->fail(['account_number' => 'Số tài khoản không tồn tại trên hệ thống OCB.']);
            }

            if ($responseData['error']['code'] == '504') {
                $errorMessage = 'Hệ thống OCB đang bận, vui lòng thử lại sau';
            }

            return $this->response->setJSON([
                "status" => false,
                "message" => $errorMessage
            ]);
        }
    }

    public function ajax_step_1()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!has_permission('BankAccount', 'can_add'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền thêm tài khoản ngân hàng"));

        // FIX_DUPLICATE_ACCOUNT_NUMBER
        if (! $this->validate([
            'account_number' => [
                'label' => 'số tài khoản',
                'rules' => 'required',
            ],
            'identification_number' => 'required|max_length[100]',
            'phone_number' => 'required|min_length[10]|max_length[20]',
            'label' => 'permit_empty|max_length[100]'
        ])) return $this->fail($this->validator->getErrors());

        if (model(BankAccountModel::class)->where([
            'account_number' => trim($this->request->getVar('account_number')),
            'bank_id' => 18
        ])->get()->getRow()) {
            return $this->fail(['account_number' => 'Số tài khoản này đã tồn tại trên hệ thống']);
        }
        // FIX_DUPLICATE_ACCOUNT_NUMBER

        try {
            $client = new OcbClient;
        } catch (\Exception $e) {
            return $this->handleOcbClientException($e);
        }

        try {
            $response = $client->verifyMerchantInformation(
                trim($this->request->getVar('identification_number')),
                trim($this->request->getVar('phone_number')),
                trim($this->request->getVar('account_number'))
            );
        } catch (\Exception $e) {
            return $this->handleOcbClientException($e);
        }

        $responseData = json_decode($response->getBody(), true);

        $ocbConfig = config(\Config\Ocb::class);
        if ($ocbConfig->skipVerifyMerchantInformation) {
            $responseData = [
                'trace' => 'SKIP_VERIFY_MERCHANT_INFO_' . uniqid(),
                'data' => [
                    'merchantVerifyResult' => [
                        'identificationNumberValidation' => 1,
                        'mobilePhoneValidation' => 1
                    ]
                ]
            ];
        }

        if (isset($responseData['trace']) && !isset($responseData['error'])) {
            $failedVerificationMessages = [];

            if ($responseData['data']['merchantVerifyResult']['identificationNumberValidation'] != '1') {
                $failedVerificationMessages['identification_number'] = 'Số CCCD/CMND không khớp số tài khoản trên';
            }

            if ($responseData['data']['merchantVerifyResult']['mobilePhoneValidation'] != '1') {
                $failedVerificationMessages['phone_number'] = 'Số điện thoại không khớp số tài khoản trên';
            }

            if (count($failedVerificationMessages)) {
                return $this->fail($failedVerificationMessages);
            }
        
            try {
                $response = $client->retrieveOCBRecipientName(
                    $this->request->getVar('account_number')
                );
            } catch (\Exception $e) {
                return $this->handleOcbClientException($e);
            }

            $responseData = json_decode($response->getBody(), true);

            if (isset($responseData['error'])) {
                return $this->response->setJSON([
                    "status" => false,
                    "message" => 'Đã có lỗi xảy ra, vui lòng thử lại'
                ]);
            }

            $bankAccountModel = model(BankAccountModel::class);
            $simModel = model(SimModel::class);
            $simCompanyModel = model(SimCompanyModel::class);

            $data = array(
                'company_id' => $this->user_session['company_id'],
                'account_holder_name' => $responseData['data']['queryResult']['accountName'],
                'account_number' => trim($this->request->getVar('account_number')),
                'bank_id' => 18,
                'label' => xss_clean($this->request->getVar('label')),
                'active' => 1,
                'bank_api' => 1,
                'bank_api_connected' => 0,
                'identification_number' => trim($this->request->getVar('identification_number')),
                'phone_number' => trim($this->request->getVar('phone_number'))
            );

            $sim_id = $this->request->getVar('sim_id');

            $sims = $simCompanyModel->select("tb_autopay_sim.id, tb_autopay_sim.sim_phonenumber")->join("tb_autopay_sim","tb_autopay_sim.id=tb_autopay_sim_company.sim_id")->where(['tb_autopay_sim_company.company_id' => $this->user_session['company_id'], "tb_autopay_sim.active" => 1])->orderBy('tb_autopay_sim_company.created_at','ASC')->get()->getResult();

            if(count($sims) == 1) {
                $data['sim_id'] = $sims[0]->id;
            } else if(count($sims) > 1 && is_numeric($sim_id) && $sim_id > 0) {
                $sim_details = $simCompanyModel->select("tb_autopay_sim.id")->join("tb_autopay_sim","tb_autopay_sim.id=tb_autopay_sim_company.sim_id")->where(['tb_autopay_sim_company.company_id' => $this->user_session['company_id'], "tb_autopay_sim.active" => 1,"tb_autopay_sim.id" => $sim_id])->get()->getRow();

                if(!is_object($sim_details))
                    return $this->response->setJSON(array("status"=>FALSE,"message"=>"SIM nhận SMS mà bạn chọn không khả dụng"));
                else
                    $data['sim_id'] = $sim_id;
            } else if(count($sims) > 1) {
                $data['sim_id'] = $sims[0]->id;
            }

            $result = $bankAccountModel->insert($data);
            
            if ($result) {
                add_user_log(array('data_id'=>$result,'company_id' => $this->user_session['company_id'], 'data_type'=>'bank_account_add','description'=>'Thêm tài khoản ngân hàng OCB API','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
                return $this->response->setJSON(array("status"=>true,"id" => $result));
            } else {
                add_user_log(array('data_id'=>0,'company_id' => $this->user_session['company_id'], 'data_type'=>'bank_account_add','description'=>'Thêm tài khoản ngân hàng OCB API','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Failed'));
                return $this->response->setJSON(array("status"=>false,"message"=>"Không thể thêm tài khoản ngân hàng. Vui lòng liên hệ SePay để được hỗ trợ."));
            }
        } else {
            $errorMessage = 'Không thể kết nối với OCB, vui lòng liên hệ với SePay để được hỗ trợ';
            log_message('error', $responseData['error']['details']);

            if ($responseData['error']['code'] == '504') {
                $errorMessage = 'Hệ thống OCB đang bận, vui lòng thử lại sau';
            }

            return $this->response->setJSON([
                "status" => false,
                "message" => $errorMessage
            ]);
        }
    }

    public function ajax_register_merchant_step_1() 
    {
        $ocbConfig = config(\Config\Ocb::class);

        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        if(!has_permission('BankAccount', 'can_add'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn chưa được cấp quyền để thực hiện thao tác này"));

        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $bankAccountId = $this->request->getPost('id');

        $bankAccountDetails = $bankAccountModel->where(['id'=>$bankAccountId, 'company_id' => $this->user_session['company_id']])->get()->getRow();
        
        if(!is_object($bankAccountDetails))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy tài khoản ngân hàng này"));

        $defaultVANumberMinLength = $ocbConfig->OcbDefaultVANumberMinLength;

        if (in_array($this->user_details->id, $ocbConfig->SePayVIPUserIdList)) {
            $defaultVANumberMinLength = 1;
        }

        $rules = [
            'merchant_address' => 'required|min_length[1]|max_length[1000]',
            'sub_id' => $ocbConfig->OcbVAOnlyNumber 
                ? 'required|min_length[' . $defaultVANumberMinLength .']|max_length[15]|regex_match[/^[0-9]+$/]' 
                : 'required|min_length[' . $defaultVANumberMinLength .']|max_length[15]|regex_match[/^[a-zA-Z0-9]+$/]',
            'label' => 'permit_empty|max_length[100]'
        ];

        if ($ocbConfig->skipVerifyMerchantInformation) {
            $rules['identification_number'] = ['required', 'max_length[100]'];
            $rules['phone_number'] = ['required', 'min_length[10]', 'max_length[20]'];
        }

        if (! $this->validate($rules)) return $this->fail($this->validator->getErrors());

        if ($ocbConfig->skipVerifyMerchantInformation) {
            $safeBankAccountUpdateData = [
                'identification_number' => trim(xss_clean($this->request->getVar('identification_number'))),
                'phone_number' => trim(xss_clean($this->request->getVar('phone_number'))),
            ];

            $bankAccountModel->where(['id' => $bankAccountDetails->id, 'company_id' => $this->user_session['company_id']])->set($safeBankAccountUpdateData)->update();
            $bankAccountDetails = $bankAccountModel->where(['id' => $bankAccountDetails->id, 'company_id' => $this->user_session['company_id']])->get()->getRow();
        }

        try {
            $client = new OcbClient;
        } catch (\Exception $e) {
            return $this->handleOcbClientException($e);
        }

        if ($bankSubAccountModel->where(['sub_account' => $client->getPartnerCode() . $this->request->getVar('sub_id')])->first()) {
            return $this->fail(['sub_id' => 'Số VA đã tồn tại']);
        }

        try {
            $session = service('session');
            $bypassSpeakerVirtualAccountCheckBuilder = model(OutputDeviceDecalModel::class)
                ->where('tb_autopay_output_device_decal.bank_id', $bankAccountDetails->bank_id)
                ->where('tb_autopay_output_device_decal.virtual_account_number', $client->getPartnerCode() . $this->request->getVar('sub_id'))
                ->where('tb_autopay_output_device_decal.account_number', $bankAccountDetails->account_number)
                ->where('tb_autopay_output_device_decal.output_device_id !=', null);
                
            if (isset($session->get('output_device_integration')['output_device_id'])) {
                $bypassSpeakerVirtualAccountCheckBuilder->where('tb_autopay_output_device_decal.output_device_id', $session->get('output_device_integration')['output_device_id']);
            } else {
                $bypassSpeakerVirtualAccountCheckBuilder->join('tb_autopay_output_device', 'tb_autopay_output_device.id = tb_autopay_output_device_decal.output_device_id');
                $bypassSpeakerVirtualAccountCheckBuilder->where('tb_autopay_output_device.company_id', $this->user_session['company_id']);
            }
                
            $bypassSpeakerVirtualAccountCheck = $bypassSpeakerVirtualAccountCheckBuilder->countAllResults();
        } catch (\Exception $e) {
            $bypassSpeakerVirtualAccountCheck = false;
        }
        
        if (!is_speaker_billing_subscription() && !$bypassSpeakerVirtualAccountCheck && preg_match('/^' . $client->getPartnerCode() . '9[a-zA-Z0-9]+$/i', $client->getPartnerCode() . $this->request->getVar('sub_id'))) {
            return $this->fail(['sub_id' => 'Số VA đã tồn tại']);
        }

        $merchantAddress = remove_accents($this->request->getVar('merchant_address'), true);

        // $mccWords = explode(' ', remove_accents($this->company_details->short_name, true));

        // if (count($mccWords) > 1) {
        //     $mcc = implode('', array_map(function($word) {
        //         return substr($word, 0, 1);
        //     }, $mccWords));
        // } else {
        //     $mcc = $mccWords[0];
        // }

        // set default mcc
        $mcc = '0004';

        try {
            $response = $client->registerMerchantStep1(
                $bankAccountDetails->identification_number,
                $bankAccountDetails->phone_number,
                $bankAccountDetails->account_number,
                $client->getPartnerCode(),
                $this->user_session['email'],
                remove_accents($this->user_session['lastname'] . ' ' . $this->user_session['firstname'], true),
                remove_accents($this->company_details->full_name, true),
                $mcc,
                $merchantAddress,
                trim(strtoupper($this->request->getVar('sub_id'))),
            );
        } catch (\Exception $e) {
            return $this->handleOcbClientException($e);
        }

        $responseData = json_decode($response->getBody(), true);

        if (isset($responseData['trace']['bankRefNo']) && !isset($responseData['error'])) {
            $session = session();
            $session->setFlashdata('register-merchant-step-1-data', [
                'email' => $this->user_session['email'],
                'customer_name' => $bankAccountDetails->account_holder_name,
                'merchant_name' => remove_accents($this->company_details->full_name, true),
                'merchant_address' => $merchantAddress,
                'mcc' => $mcc,
                'label' => trim(xss_clean($this->request->getVar('label') ?? '')),
                'sub_id' => trim($this->request->getVar('sub_id')),
            ]);

            return $this->response->setJSON([
                "status" => true,
                "bank_ref_no" => $responseData['trace']['bankRefNo'],
                "message" => "Đã gửi OTP đến số điện thoại",
            ]);
        } else {
            $errorMessage = 'Không thể kết nối với OCB, vui lòng liên hệ với SePay để được hỗ trợ';
            log_message('error', $responseData['error']['details']);
            
            if ($responseData['error']['code'] == '41776') {
                return $this->fail(['sub_id' => 'Số VA đã tồn tại']);
            }

            if ($responseData['error']['code'] == '41749') {
                return $this->response->setJSON([
                    'status' => false,
                    'message' => 'Thông tin số điện thoại đăng ký không khớp với số tài khoản, vui lòng liên hệ SePay để được hỗ trợ'
                ]);
            }

            if ($responseData['error']['code'] == '41744') {
                return $this->response->setJSON([
                    'status' => false,
                    'message' => 'Số giấy tờ tùy thân không khớp hoặc đã bị thay đổi ở phía ngân hàng, vui lòng liên hệ SePay để được hỗ trợ'
                ]);
            }

            if ($responseData['error']['code'] == '504') {
                $errorMessage = 'Hệ thống OCB đang bận, vui lòng thử lại sau';
            }

            return $this->response->setJSON([
                "status" => false,
                "message" => $errorMessage
            ]);
        }
    }

    public function ajax_register_merchant_step_2() 
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        if(!has_permission('BankAccount', 'can_add'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn chưa được cấp quyền để thực hiện thao tác này"));

        $bankAccountModel = model(BankAccountmodel::class);
        $bankAccountId = $this->request->getPost('id');

        $bankAccountDetails = $bankAccountModel->where(['id'=>$bankAccountId, 'company_id' => $this->user_session['company_id']])->get()->getRow();
        
        if(!is_object($bankAccountDetails))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy tài khoản ngân hàng này"));

        if (!$bankRefNo = $this->request->getPost('bank_ref_no')) {
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Đã có lỗi xảy ra, vui lòng tải lại trang"));
        }

        if (! $this->validate([
            'otp' => 'required',
        ])) return $this->fail($this->validator->getErrors());

        try {
            $client = new OcbClient;
        } catch (\Exception $e) {
            return $this->handleOcbClientException($e);
        }

        $session = session();

        $simulator = '';

        if ($simulator) {
            $responseData = [
                'data' => [
                    'merchantInfo' => [
                        'vaAccountNumber' => 'SEP' . $session->getFlashdata('register-merchant-step-1-data')['sub_id'],
                        'merchantName' => $session->getFlashdata('register-merchant-step-1-data')['merchant_name'],
                        'merchantAddress' => $session->getFlashdata('register-merchant-step-1-data')['merchant_address']
                    ]
                ],
                'error' => [
                    'code' => '41724',
                    'details' => 'Simulator error'
                ]
            ];
        } else {
            try {
                $response = $client->registerMerchantStep2(
                    $client->getPartnerCode(),
                    trim($this->request->getVar('otp')),
                    $bankRefNo
                );
            } catch (\Exception $e) {
                return $this->handleOcbClientException($e);
            }

            $responseData = json_decode($response->getBody(), true);
        }

        $session->keepFlashdata('register-merchant-step-1-data');

        if (($simulator && $simulator === $this->request->getVar('otp') && (ENVIRONMENT !== 'production')) 
        || (isset($responseData['trace']['bankRefNo']) && !isset($responseData['error']))) {
            $db = \Config\Database::connect();
            $db->transStart();

            $bankAccountModel->set(['bank_api_connected' => 1])->update($bankAccountId);

            if (!$this->request->getVar('success_message')) {
                add_user_log(array('data_id'=>$bankAccountId,'company_id' => $this->user_session['company_id'], 'data_type'=>'bank_account_linked','description'=>'Liên kết tài khoản ngân hàng','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
            }

            $bankSubAccountConfig = get_configuration('BankSubAccount');
            if($bankSubAccountConfig == "off") {
                $configurationModel = model(ConfigurationModel::class);
                $configurationModel->set([
                    'value' => "on",
                ])->where(['company_id' => $this->user_session['company_id'],'setting' => 'BankSubAccount'])->update();
            }

            $configurationModel = model(ConfigurationModel::class);
            $config_result = $configurationModel->where(['setting' => 'OCBVaOrderLastNumber'])->get()->getRow();
            $order = is_object($config_result) ? $config_result->value + 1 : 1;

            $bankSubAccountModel = model(BankSubAccountModel::class);
            $result = $bankSubAccountModel->insert([
                'bank_account_id' => $bankAccountId,
                'sub_account' => $responseData['data']['merchantInfo']['vaAccountNumber'], 
                'va_order' => $order, 
                'acc_type' => 'Real', 
                'sub_holder_name' => $bankAccountDetails->account_holder_name,
                'label' => $session->getFlashdata('register-merchant-step-1-data') 
                    ? $session->getFlashdata('register-merchant-step-1-data')['label'] 
                    : null
            ]);

            $ocbBankSubAccountMetaData = model(OcbBankSubAccountMetaData::class);
            $ocbBankSubAccountMetaData->insert([
                'bank_account_id' => $bankAccountId,
                'sub_account_id' => $result,
                'email' => $session->getFlashdata('register-merchant-step-1-data')['email'],
                'merchant_name' => $responseData['data']['merchantInfo']['merchantName'],
                'mcc' => $session->getFlashdata('register-merchant-step-1-data')['mcc'],
                'merchant_address' => $responseData['data']['merchantInfo']['merchantAddress'],
            ]);

            $configurationModel->set(['value' => $order])->where(['setting' => 'OCBVaOrderLastNumber', 'company_id' => 0])->update();

            set_alert('success', $this->request->getVar('success_message') ?? "Liên kết ngân hàng và tạo VA thành công!");
            $db->transComplete();

            if ($db->transStatus() === false) {
                return $this->response->setJSON([
                    "status" => false,
                    "message" => 'Đã có lỗi xảy ra, vui lòng tải lại trang'
                ]);
            }

            return $this->response->setJSON([
                "status" => true,
                'id' => $result,
                "message" => $this->request->getVar('success_message') ?? "Liên kết ngân hàng và tạo VA thành công!"
            ]);
        } else {
            $errorMessage = 'Không thể kết nối với OCB, vui lòng liên hệ với SePay để được hỗ trợ';
            log_message('error', $responseData['error']['details']);

            if ($responseData['error']['code'] == '41731') {
                $errorMessage = 'Đã có lỗi xảy ra, vui lòng tải lại trang';
            }

            if ($responseData['error']['code'] == '504') {
                $errorMessage = 'Hệ thống OCB đang bận, vui lòng thử lại sau';
            }

            if ($responseData['error']['code'] == '41724') {
                return $this->fail(['otp' => 'OTP không chính xác']);
            }

            if ($responseData['error']['code'] == '41726') {
                return $this->fail(['otp' => 'OTP đã hết hạn']);
            }

            return $this->response->setJSON([
                "status" => false,
                "message" => $errorMessage
            ]);
        }
    }

    public function ajax_check_va_trans() {

        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!has_permission('BankAccount', 'can_add'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền thực hiện thao tác này"));
 
        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'va_id' => ['label' => 'Số tài khoản', 'rules' => 'integer|required'],
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        } else {

            $va_id = $this->request->getVar('va_id');

            if(!is_numeric($va_id))
                return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy VA ID"));

            $bankSubAccountModel = model(BankSubAccountModel::class);
            $transactionsModel = slavable_model(TransactionsModel::class, 'Ocb');

            $result =  $bankSubAccountModel->select("tb_autopay_bank_account.account_number, tb_autopay_bank_account.id as `bank_account_id`, tb_autopay_bank_sub_account.sub_account")->join("tb_autopay_bank_account","tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id")->where(['tb_autopay_bank_sub_account.id' => $va_id, "tb_autopay_bank_account.company_id" => $this->user_session['company_id']])->get()->getRow();
            if(!is_object($result))
                return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy VA này"));

            $last_trans = $transactionsModel->select("id, amount_in,account_number,sub_account,transaction_content,transaction_date")->where(['deleted_at' => NULL, 'parser_status' => 'Success', 'bank_account_id' => $result->bank_account_id, 'sub_account' => $result->sub_account])->orderBy('id','DESC')->get()->getRow();

            if(is_object($last_trans))
                return $this->response->setJSON(array("status"=>TRUE, 'last_transaction' => $last_trans));
            else
                return $this->response->setJSON(array("status"=>FALSE));
        }
    }

    public function details($id='')
    { 
        $ocbConfig = config(\Config\Ocb::class);

        $data = [
            'page_title' => 'Ngân hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'ocb_va_only_number' => $ocbConfig->OcbVAOnlyNumber,
            'prefix_va' => $ocbConfig->OcbPrefixVA,
            'ocb_default_va_number_min_length' => $ocbConfig->OcbDefaultVANumberMinLength,
            'sepay_vip_user_id_list' => $ocbConfig->SePayVIPUserIdList,
        ];

        // Check VIP user
        if (in_array($this->user_details->id, $data['sepay_vip_user_id_list'])) {
            $data['ocb_default_va_number_min_length'] = 1;
        }

        if(!has_permission('BankAccount', 'can_view_all'))
            show_404();

        if(!is_numeric($id))
            show_404();

        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $transactionsModel = slavable_model(TransactionsModel::class, 'Ocb');
        $simCompanyModel = model(SimCompanyModel::class);
        $ocbEnterpriseAccountModel = model(\App\Models\OcbEnterpriseAccountModel::class);

        $data['bank_account_details'] = $bankAccountModel->select("tb_autopay_bank_account.id,tb_autopay_bank_account.sim_id,tb_autopay_bank_account.account_number,tb_autopay_bank_account.label,tb_autopay_bank_account.active,tb_autopay_bank_account.created_at,tb_autopay_bank.full_name,tb_autopay_bank.brand_name, tb_autopay_bank_account.account_number,tb_autopay_bank_account.account_holder_name,tb_autopay_bank.icon_path,tb_autopay_bank.logo_path,tb_autopay_sim.sim_phonenumber, tb_autopay_bank_account.last_transaction, tb_autopay_bank_account.accumulated,tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.bank_id,tb_autopay_bank_account.bank_sms, tb_autopay_bank_account.bank_sms_connected, tb_autopay_bank_account.bank_api, tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.identification_number, tb_autopay_bank_account.phone_number")->join("tb_autopay_bank","tb_autopay_bank.id=tb_autopay_bank_account.bank_id")->join("tb_autopay_sim","tb_autopay_sim.id=tb_autopay_bank_account.sim_id","left")->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id'], 'tb_autopay_bank_account.id' => $id])->get()->getRow();

        if(!is_object($data['bank_account_details']))
            show_404();
        
        $data['count_transactions'] = $transactionsModel->where(['bank_account_id' => $data['bank_account_details']->id, 'deleted_at' => NULL])->countAllResults();

        $isOcbEnterpriseBankAccount = $ocbEnterpriseAccountModel->where(['bank_account_id' => $data['bank_account_details']->id])->countAllResults();

        if($data['bank_account_details']->bank_id == 18 && $data['bank_account_details']->bank_api_connected == 0 && $isOcbEnterpriseBankAccount == 0) {
            return redirect()->to(base_url('ocb/step2/' . $data['bank_account_details']->id));
        }

        if ($isOcbEnterpriseBankAccount) {
            $data['ocb_enterprise_account_details'] = $ocbEnterpriseAccountModel->where(['bank_account_id' => $data['bank_account_details']->id])->get()->getRow();
        }

        $data['count_subs'] = $bankSubAccountModel->where(['bank_account_id' => $id, 'deleted_at' => NULL])->countAllResults();
        
        $data['bank_sms_info'] = get_bank_sms_info($data['bank_account_details']->brand_name);

        // check dedicated sim
        $data['sims'] = $simCompanyModel->select("tb_autopay_sim.id, tb_autopay_sim.sim_phonenumber")->join("tb_autopay_sim","tb_autopay_sim.id=tb_autopay_sim_company.sim_id")->where(['tb_autopay_sim_company.company_id' => $this->user_session['company_id'], "tb_autopay_sim.active" => 1])->orderBy('tb_autopay_sim_company.created_at','DESC')->get()->getResult();

        if ($data['bank_account_details']->sim_id) {
            $data['sim_connected'] = (boolean) $transactionsModel->join('tb_autopay_sms','tb_autopay_sms.id=tb_autopay_sms_parsed.sms_id')->where(['tb_autopay_sms.to' => $data['bank_account_details']->sim_phonenumber, 'source' => 'SMS', 'bank_account_id' => $data['bank_account_details']->id])->countAllResults();
        } else {
            $data['sim_connected'] = false;
        }
        // data QR
        $data['bank_sub_accounts_custom']=[];
        if(!empty($data['bank_account_details'])){
            $data['bank_sub_accounts_custom'] = $bankSubAccountModel->select("tb_autopay_bank_sub_account.id,tb_autopay_bank_sub_account.acc_type, tb_autopay_bank.brand_name,tb_autopay_bank.icon_path, tb_autopay_bank_account.account_holder_name, tb_autopay_bank_account.account_number, tb_autopay_bank_sub_account.sub_account, tb_autopay_bank_sub_account.label")
                ->join("tb_autopay_bank_account", "tb_autopay_bank_account.id = tb_autopay_bank_sub_account.bank_account_id")
                ->join("tb_autopay_bank", "tb_autopay_bank.id = tb_autopay_bank_account.bank_id", "left")
                ->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id']])
                ->where('tb_autopay_bank_account.id',$data['bank_account_details']->id) 
                ->get()
                ->getResultArray();
        }

        echo theme_view('templates/autopay/header',$data);
        echo theme_view($isOcbEnterpriseBankAccount ? 'ocb/enterprise/details' : 'ocb/individual/details',$data);
        echo theme_view('templates/autopay/footer',$data);
    }

    public function ajax_view_va($id='') {
        if(!has_permission('BankAccount', 'can_view_all'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền xem tài khoản ngân hàng"));
        
        if(!is_numeric($id)) 
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"ID tài khoản ngân ảo hợp lệ"));

        $bankSubAccountModel = model(BankSubAccountModel::class);
        $transactionsModel = slavable_model(TransactionsModel::class, 'Ocb');
        $OcbBankSubAccountMetaDataModel = model(OcbBankSubAccountMetaData::class);

        $data['va_details'] = $bankSubAccountModel->select("tb_autopay_bank_sub_account.id,tb_autopay_bank_sub_account.sub_account,tb_autopay_bank_sub_account.acc_type,tb_autopay_bank_sub_account.bank_account_id,tb_autopay_bank_sub_account.label,tb_autopay_bank_account.account_number,tb_autopay_bank_account.active,tb_autopay_bank_account.created_at,tb_autopay_bank.brand_name, tb_autopay_bank_account.account_number,tb_autopay_bank_account.account_holder_name,tb_autopay_bank.icon_path,tb_autopay_bank.logo_path,tb_autopay_bank.full_name, tb_autopay_bank_sub_account.sub_holder_name,tb_autopay_bank_sub_account.va_active")->join("tb_autopay_bank_account","tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id")->join("tb_autopay_bank","tb_autopay_bank.id=tb_autopay_bank_account.bank_id")->where(['tb_autopay_bank_sub_account.id'=>$id, 'tb_autopay_bank_account.company_id' => $this->user_session['company_id'],'tb_autopay_bank_account.bank_id' => 18])->get()->getRow();
        $data['count_transactions'] = $transactionsModel->where(['sub_account' => $data['va_details']->sub_account])->countAllResults();
        $data['last_transaction'] = $transactionsModel->where(['sub_account' => $data['va_details']->sub_account, 'accumulated!=' => ''])->orderBy('transaction_date','DESC')->get()->getRow();
        
        if($data['va_details']) {
            $data['va_metadata'] = $OcbBankSubAccountMetaDataModel->where(['sub_account_id' => $id])->get()->getRow();

            if ($data['va_metadata']->ocb_enterprise_app_id) {
                $data['ocb_enteprise_app_details'] = model(\App\Models\OcbEnterpriseAppModel::class)->where(['id' => $data['va_metadata']->ocb_enterprise_app_id])->get()->getRow();
            }

            $html = view($data['va_metadata']->ocb_enterprise_app_id ? 'ocb/enterprise/va_view' : 'ocb/individual/va_view', $data);
            return $this->response->setJSON(["status"=>TRUE, "html"=>$html]);
        }
        else
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy tài khoản ảo này"));
    }

    public function ajax_generate_va_number()
    {
        $ocbConfig = config(\Config\Ocb::class);

        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        if(!has_permission('BankAccount', 'can_add'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn chưa được cấp quyền để thực hiện thao tác này"));

        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $bankAccountId = $this->request->getPost('bank_account_id');
    
        $bankAccountDetails = $bankAccountModel->where(['id'=>$bankAccountId, 'company_id' => $this->user_session['company_id']])->get()->getRow();
            
        if(!is_object($bankAccountDetails))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy tài khoản ngân hàng này"));

        $number = '';

        for ($i = 0; $i < $ocbConfig->OcbDefaultVANumberMinLength; $i++) {
            $number .= rand(0, 9);
        }

        $vaNumber = ($ocbConfig->OcbVAOnlyNumber ? '' : preg_replace('/\s+/', '', remove_accents($this->company_details->short_name, true))) . $number;

        return $this->respond([
            'va_number' => substr($vaNumber, 0, 15)
        ]);
    }

    public function send_ocb_enterprise_connect_request()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!has_permission('BankAccount', 'can_add'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn chưa được cấp quyền để thực hiện thao tác này"));
    
        if (! $this->validate([
            'company_name' => 'required|min_length[5]|max_length[100]',
            'has_ocb_account' => 'required',
            'phone_number' => 'required|min_length[10]|max_length[20]',
            'notes' => 'permit_empty'
        ])) return $this->fail($this->validator->getErrors());

        $ocbConfig = config(\Config\Ocb::class);

        $data = [
            'company_name' => trim($this->request->getVar('company_name')),
            'phone_number' => trim($this->request->getVar('phone_number')),
            'has_ocb_account' => $this->request->getVar('has_ocb_account'),
            'notes' => trim($this->request->getVar('notes'))
        ];

        $message = '
------------------------------

Có yêu cầu kết nối API OCB doanh nghiệp mới:

#️⃣ Tên cá nhân/tổ chức: ' . $data['company_name'] . '

ℹ️ Đã có tài khoản OCB: ' . ($data['has_ocb_account'] ? 'Đã có' : 'Chưa') . '

📞 Số điện thoại liên hệ: ' . $data['phone_number'] . '

⏰ Thực hiện lúc: ' . date("Y-m-d H:i:s") . '

✍🏻 Ghi chú: ' . $data['notes'] . '

------------------------------
        ';

        $telegramQueueModel = model(\App\Models\NotificationTelegramQueueModel::class);
        $telegramQueueModel->insert([
            'chat_id' => $ocbConfig->OcbTelegramChatId,
            'status' => 'Pending',
            'message' => $message
        ]);

        // $telegramQueueModel->sendTelegramMessage($message, $ocbConfig->OcbTelegramChatId);

        return $this->response->setJSON([
            "status" => true,
            "message" => "SePay đã nhận được yêu cầu của bạn và sẽ liên hệ lại trong thời gian sớm nhất",
        ]);
    }

    public function connect_ocb_enterprise()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!has_permission('BankAccount', 'can_add'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn chưa được cấp quyền để thực hiện thao tác này"));
    
        if (! $this->validate([
            'account_number' => [
                'label' => 'số tài khoản',
                'rules' => 'required|max_length[250]',
            ],
            'account_holder_name' => 'required|max_length[250]',
            'username' => 'required|max_length[250]',
            'password' => 'required|max_length[250]',
            'partner_code' => 'required|max_length[250]',
            'prefix_va' => 'required|max_length[250]',
            'label' => 'permit_empty|max_length[100]'
        ])) return $this->fail($this->validator->getErrors());

        $ocbConfig = config(\Config\Ocb::class);

        $bankAccountModel = model(BankAccountModel::class);
        
        if ($bankAccountModel->where([
            'bank_id' => 18, 
            'account_number' => $this->request->getVar('account_number')
        ])->countAllResults()) {
            return $this->fail(['account_number' => 'Số tài khoản này đã tồn tại trên hệ thống.']);
        }

        $data = array(
            'company_id' => $this->user_session['company_id'],
            'account_holder_name' => remove_accents(trim($this->request->getVar('account_holder_name')), true),
            'account_number' => trim($this->request->getVar('account_number')),
            'bank_id' => 18,
            'label' => xss_clean($this->request->getVar('label')),
            'active' => 1,
            'bank_api' => 1,
            'bank_api_connected' => 0,
        );

        $bankAccountModel->skipApplyReferral();

        $result = $bankAccountModel->insert($data);
            
        if ($result) {
            $ocbEnterpriseAccountModel = model(\App\Models\OcbEnterpriseAccountModel::class);
            $ocbEnterpriseAccountModel->insert([
                'bank_account_id' => $result,
                'username' => trim($this->request->getVar('username')),
                'password' => $this->request->getVar('password'),
                'partner_code' => trim($this->request->getVar('partner_code')),
                'prefix_va' => trim($this->request->getVar('prefix_va'))
            ]);

            add_user_log(array('data_id'=>$result,'company_id' => $this->user_session['company_id'], 'data_type'=>'bank_account_add','description'=>'Thêm tài khoản ngân hàng OCB API','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
            return $this->response->setJSON(array("status"=>true,"id" => $result));
        } else {
            add_user_log(array('data_id'=>0,'company_id' => $this->user_session['company_id'], 'data_type'=>'bank_account_add','description'=>'Thêm tài khoản ngân hàng OCB API','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Failed'));
            return $this->response->setJSON(array("status"=>false,"message"=>"Không thể thêm tài khoản ngân hàng. Vui lòng liên hệ SePay để được hỗ trợ."));
        }
    }

    public function apps($id='')
    { 
        $data = [
            'page_title' => 'Ngân hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
        ];


        if(!has_permission('BankAccount', 'can_edit'))
            show_404();

        if(!is_numeric($id))
            show_404();

        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $transactionsModel = slavable_model(TransactionsModel::class, 'Ocb');
        $simCompanyModel = model(SimCompanyModel::class);
        $ocbEnterpriseAccountModel = model(\App\Models\OcbEnterpriseAccountModel::class);
        $ocbEnterpriseAppModel = model(\App\Models\OcbEnterpriseAppModel::class);
        
        $data['app_count_all'] = $ocbEnterpriseAppModel->countAll($id);
        
        $data['bank_account_details'] = $bankAccountModel->select("tb_autopay_bank_account.id,tb_autopay_bank_account.account_number,tb_autopay_bank_account.label,tb_autopay_bank_account.active,tb_autopay_bank_account.created_at,tb_autopay_bank.full_name,tb_autopay_bank.brand_name, tb_autopay_bank_account.account_number,tb_autopay_bank_account.account_holder_name,tb_autopay_bank.icon_path,tb_autopay_bank.logo_path,tb_autopay_sim.sim_phonenumber, tb_autopay_bank_account.last_transaction, tb_autopay_bank_account.accumulated,tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.bank_id,tb_autopay_bank_account.bank_sms, tb_autopay_bank_account.bank_sms_connected, tb_autopay_bank_account.bank_api, tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.identification_number, tb_autopay_bank_account.phone_number")->join("tb_autopay_bank","tb_autopay_bank.id=tb_autopay_bank_account.bank_id")->join("tb_autopay_sim","tb_autopay_sim.id=tb_autopay_bank_account.sim_id","left")->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id'], 'tb_autopay_bank_account.id' => $id])->get()->getRow();

        if(!is_object($data['bank_account_details']))
            show_404();
        
        $isOcbEnterpriseBankAccount = $ocbEnterpriseAccountModel->where(['bank_account_id' => $data['bank_account_details']->id])->countAllResults();

        if($isOcbEnterpriseBankAccount == 0)
            show_404();

        if($data['bank_account_details']->bank_id == 18 && $data['bank_account_details']->bank_api_connected == 0 && $isOcbEnterpriseBankAccount == 0) {
            return redirect()->to(base_url('ocb/step2/' . $data['bank_account_details']->id));
        }

        $data['count_subs'] = $bankSubAccountModel->where(['bank_account_id' => $id, 'deleted_at' => NULL])->countAllResults();

        echo theme_view('templates/autopay/header',$data);
        echo theme_view('ocb/enterprise/apps', $data);
        echo theme_view('templates/autopay/footer',$data);
    }

    public function ajax_add_app()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn chưa được cấp quyền để thực hiện thao tác này"));

        $bankAccountModel = model(BankAccountModel::class);
        $ocbEnterpriseAccountModel = model(\App\Models\OcbEnterpriseAccountModel::class);

        $bankAccountId = $this->request->getPost('bank_account_id');
        $bankAccountDetails = $bankAccountModel->where(['id'=>$bankAccountId, 'company_id' => $this->user_session['company_id']])->get()->getRow();
        $ocbEnterpriseAccountDetails = $ocbEnterpriseAccountModel->where(['bank_account_id' => $bankAccountId])->get()->getRow();

        if(!is_object($bankAccountDetails) || !is_object($ocbEnterpriseAccountDetails))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy tài khoản ngân hàng này"));

        if (! $this->validate([
            'client_id' => [
                'label' => 'client ID',
                'rules' => 'required|is_unique[tb_autopay_ocb_enterprise_app.client_id]',
                'errors' => [
                    'is_unique' => 'Client ID đã tồn tại trên hệ thống',
                ]
            ],
            'client_secret' => [
                'label' => 'client secret',
                'rules' => 'required',
            ],
            'username' => [
                'label' => 'tên đăng nhập',
                'rules' => 'required',
            ],
            'password' => [
                'label' => 'mật khẩu',
                'rules' => 'required',
            ],
            'client_cert' => [
                'label' => 'chứng chỉ máy khách',
                'rules' => 'required',
            ],
            'private_key' => [
                'label' => 'khóa riêng tư',
                'rules' => 'required',
            ],
            'label' => 'permit_empty|max_length[100]'
        ])) return $this->fail($this->validator->getErrors());

        $data = [
            'client_id' => trim($this->request->getVar('client_id')),
            'client_secret' => $this->request->getVar('client_secret'),
            'username' => trim($this->request->getVar('username')),
            'password' => $this->request->getVar('password'),
            'client_cert' => $this->request->getVar('client_cert'),
            'private_key' => $this->request->getVar('private_key'),
            'bank_account_id' => $bankAccountId,
            'ocb_enterprise_account_id' => $ocbEnterpriseAccountDetails->id,
            'label' => trim(xss_clean($this->request->getVar('label'))),
        ];

        try {
            $client = new OcbClient(
                $data['client_id'],
                $data['client_secret'],
                $data['client_cert'],
                $data['private_key'],
                $data['username'],
                $data['password'],
                $ocbEnterpriseAccountDetails->partner_code,
                $ocbEnterpriseAccountDetails->prefix_va,
                $this->company_details->id,
                null,
                true
            );
        } catch (\Exception $e) {
            return $this->handleOcbClientException($e);
        }

        try {
            $connected = $client->testConnection();

            if ($this->request->getGet('test') && $connected) {
                return $this->response->setJSON([
                    "status" => true,
                    "message" => 'Ứng dụng kết nối thành công'
                ]);
            }

            $ocbEnterpriseAppModel = model(\App\Models\OcbEnterpriseAppModel::class);
            $app = $ocbEnterpriseAppModel->insert($data);

            if (!$app) {
                return $this->response->setJSON([
                    "status" => false,
                    "message" => 'Đã có lỗi xảy ra, vui lòng thử lại sau hoặc liên hệ SePay để được hỗ trợ.'
                ]);
            }

            $bankAccountModel->update($bankAccountId, [
                'bank_api_connected' => 1
            ]);

            return $this->response->setJSON([
                "status" => true,
                "message" => 'Thêm ứng dụng thành công.'
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                "status" => false,
                "message" => $e->getMessage()
            ]);
        }
    }

    public function ajax_app_list()
    {
        if(!has_permission('BankAccount', 'can_edit'))
            show_404();
     
        if ($this->request->getMethod(true) != 'POST')
            return '';

        $bankAccountModel = model(BankAccountModel::class);
        $ocbEnterpriseAccountModel = model(\App\Models\OcbEnterpriseAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $ocbBankSubAccountMetadataModel = model(OcbBankSubAccountMetaData::class);

        $bankAccountId = $this->request->getGet('bank_account_id');
        $bankAccountDetails = $bankAccountModel->where(['id'=>$bankAccountId, 'company_id' => $this->user_session['company_id']])->get()->getRow();
        $ocbEnterpriseAccountDetails = $ocbEnterpriseAccountModel->where(['bank_account_id' => $bankAccountId])->get()->getRow();

        if(!is_object($bankAccountDetails) || !is_object($ocbEnterpriseAccountDetails))
            show_404();

        $ocbEnterpriseAppModel = model(\App\Models\OcbEnterpriseAppModel::class);
        $apps = $ocbEnterpriseAppModel->getDatatables($bankAccountId);
         
        $data = array();

        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');

        $can_edit = has_permission('BankAccount', 'can_edit');
        $can_delete = has_permission('BankAccount', 'can_delete');
 
        foreach ($apps as $app) {

            $no++;
            $row = array();

            $actions_btn = '';

            if($can_edit)
                $actions_btn = "<a href='javascript:;' onclick='edit_app(" . $app->id. ")' class='btn btn-sm btn-outline-warning ms-2 me-2 mt-2'><i class='bi bi-pencil'></i> Sửa</a>";
            
            $row[] = $no;
            $row[] = $app->id;
            $row[] = esc($app->client_id);

            $row[] = esc($app->label);

            $row[] = '<a href="' . base_url('ocb/vas/' . $bankAccountDetails->id) . '?app_id=' . $app->id . '">' . $ocbBankSubAccountMetadataModel->selectCount('id')
            ->where('ocb_enterprise_app_id', $app->id)
            ->countAllResults() . '</a>';

            $row[] = esc($app->created_at);
            $row[] = $actions_btn;
       
            $data[] = $row;
        }
 
        $output = array(
            "draw" => $draw,
            "recordsTotal" => $ocbEnterpriseAppModel->countAll($bankAccountId),
            "recordsFiltered" => $ocbEnterpriseAppModel->countFiltered($bankAccountId),
            "data" => $data,
        );

        return $this->response->setJSON($output);
    }

    public function ajax_get_app($id = '')
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!is_numeric($id)) 
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"ID ứng dụng không hợp lệ"));

        if(!has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn chưa được cấp quyền để thực hiện thao tác này"));
        
        $bankAccountModel = model(BankAccountModel::class);
        $ocbEnterpriseAppModel = model(\App\Models\OcbEnterpriseAppModel::class);

        $bankAccountId = $this->request->getGet('bank_account_id');
        $bankAccountDetails = $bankAccountModel->where(['id'=> $bankAccountId, 'company_id' => $this->user_session['company_id']])->get()->getRow();
        $ocbEnterpriseAppDetails = $ocbEnterpriseAppModel->where(['id' => $id])->get()->getRow();

        if(!is_object($bankAccountDetails) || !is_object($ocbEnterpriseAppDetails))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy ứng dụng này"));

        return $this->response->setJSON(["status"=>TRUE, "data"=> $ocbEnterpriseAppDetails]);
    }

    public function ajax_edit_app()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        $id = trim($this->request->getPost('id'));

        if(!is_numeric($id)) 
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"ID ứng dụng không hợp lệ"));

        if(!has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn chưa được cấp quyền để thực hiện thao tác này"));
        
        $bankAccountModel = model(BankAccountModel::class);
        $ocbEnterpriseAppModel = model(\App\Models\OcbEnterpriseAppModel::class);
        $ocbEnterpriseAccountModel = model(\App\Models\OcbEnterpriseAccountModel::class);

        $bankAccountId = $this->request->getPost('bank_account_id');
        $bankAccountDetails = $bankAccountModel->where(['id'=> $bankAccountId, 'company_id' => $this->user_session['company_id']])->get()->getRow();
        $ocbEnterpriseAppDetails = $ocbEnterpriseAppModel->where(['id' => $id])->get()->getRow();
        $ocbEnterpriseAccountDetails = $ocbEnterpriseAccountModel->where(['bank_account_id' => $bankAccountId])->get()->getRow();

        if(!is_object($bankAccountDetails) || !is_object($ocbEnterpriseAppDetails) || !is_object($ocbEnterpriseAccountDetails))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy ứng dụng này"));

        if (! $this->validate([
            'client_id' => [
                'label' => 'client ID',
                'rules' => 'required|is_unique[tb_autopay_ocb_enterprise_app.client_id,id,{id}]',
                'errors' => [
                    'is_unique' => 'Client ID đã tồn tại trên hệ thống',
                ]
            ],
            'client_secret' => [
                'label' => 'client secret',
                'rules' => 'required',
            ],
            'username' => [
                'label' => 'tên đăng nhập',
                'rules' => 'required',
            ],
            'password' => [
                'label' => 'mật khẩu',
                'rules' => 'required',
            ],
            'client_cert' => [
                'label' => 'chứng chỉ máy khách',
                'rules' => 'required',
            ],
            'private_key' => [
                'label' => 'khóa riêng tư',
                'rules' => 'required',
            ],
            'label' => 'permit_empty|max_length[100]'
        ])) return $this->fail($this->validator->getErrors());

        $data = [
            'client_id' => trim($this->request->getVar('client_id')),
            'client_secret' => $this->request->getVar('client_secret'),
            'username' => trim($this->request->getVar('username')),
            'password' => $this->request->getVar('password'),
            'client_cert' => $this->request->getVar('client_cert'),
            'private_key' => $this->request->getVar('private_key'),
            'label' => xss_clean($this->request->getVar('label')),
        ];

        try {
            $client = new OcbClient(
                $data['client_id'],
                $data['client_secret'],
                $data['client_cert'],
                $data['private_key'],
                $data['username'],
                $data['password'],
                $ocbEnterpriseAccountDetails->partner_code,
                $ocbEnterpriseAccountDetails->prefix_va,
                $this->company_details->id,
                null,
                true
            );
        } catch (\Exception $e) {
            return $this->handleOcbClientException($e);
        }

        try {
            $client->testConnection();

            if ($this->request->getGet('test')) {
                return $this->response->setJSON([
                    "status" => true,
                    "message" => 'Ứng dụng kết nối thành công'
                ]);
            }

            $ocbEnterpriseAppModel = model(\App\Models\OcbEnterpriseAppModel::class);
            $app = $ocbEnterpriseAppModel->update($id, $data);

            if (!$app) {
                return $this->response->setJSON([
                    "status" => false,
                    "message" => 'Đã có lỗi xảy ra, vui lòng thử lại sau hoặc liên hệ SePay để được hỗ trợ.'
                ]);
            }

            return $this->response->setJSON([
                "status" => true,
                "message" => 'Sửa ứng dụng thành công.'
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                "status" => false,
                "message" => $e->getMessage()
            ]);
        }
    }

    public function ajax_va_list() {

        if(!has_permission('BankAccount', 'can_view_all'))
            show_404();
     
        if ($this->request->getMethod(true) != 'POST')
            return '';

        $parrent_account_id = $this->request->getGet('parrent_id');

        if(!is_numeric($parrent_account_id))
            $parrent_account_id = FALSE;
 
        $bankSubAccountModel = model(BankSubAccountModel::class);

        $bank_accounts = $bankSubAccountModel->getDatatables($this->user_session['company_id'], $parrent_account_id);
         
        $data = array();

        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');

        $can_edit = has_permission('BankAccount', 'can_edit');
        $can_delete = has_permission('BankAccount', 'can_delete');
 
        foreach ($bank_accounts as $account) {

            $no++;
            $row = array();

            $actions_btn = '';

            if($can_edit) {
                $actions_btn = "<a href='javascript:;' onclick='edit_va(" . $account->id. ")' class='btn btn-sm btn-outline-warning ms-2 me-2 mt-2'><i class='bi bi-pencil'></i> Sửa</a>";
            }
                 
            $row[] = $no;
            $row[] = $account->id;
            $row[] = "<a href='javascript:;' onclick='view_va("  . $account->id . ")'>" . esc($account->sub_account) . "</a>";
          
            if($account->va_active == 1)
                $row[] = "<span class='text-success'>Hoạt động</span>";
            else
                $row[] = "<span class='text-danger'>Tạm ngưng</span>";
            $row[] = esc($account->label);
           

            $row[] = esc($account->created_at);
            $row[] = $actions_btn;
       
            $data[] = $row;
        }
 
        $output = array(
            "draw" => $draw,
            "recordsTotal" => $bankSubAccountModel->countAll($this->user_session['company_id'], $parrent_account_id),
            "recordsFiltered" => $bankSubAccountModel->countFiltered($this->user_session['company_id'], $parrent_account_id),
            "data" => $data,
        );

        return $this->response->setJSON($output);
    }

    public function vas($id='')
    {
        $ocbConfig = config(\Config\Ocb::class);

        $data = [
            'page_title' => 'Ngân hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'ocb_va_only_number' => $ocbConfig->OcbVAOnlyNumber,
            'ocb_default_va_number_min_length' => $ocbConfig->OcbDefaultVANumberMinLength,
            'sepay_vip_user_id_list' => $ocbConfig->SePayVIPUserIdList,
        ];

        if(!has_permission('BankAccount', 'can_view_all'))
            show_404();

        if(!is_numeric($id))
            show_404();

        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $transactionsModel = slavable_model(TransactionsModel::class, 'Ocb');
        $simCompanyModel = model(SimCompanyModel::class);
        $ocbEnterpriseAccountModel = model(\App\Models\OcbEnterpriseAccountModel::class);
        $ocbEnterpriseAppModel = model(\App\Models\OcbEnterpriseAppModel::class);

        $data['bank_account_details'] = $bankAccountModel->select("tb_autopay_bank_account.id,tb_autopay_bank_account.account_number,tb_autopay_bank_account.label,tb_autopay_bank_account.active,tb_autopay_bank_account.created_at,tb_autopay_bank.full_name,tb_autopay_bank.brand_name, tb_autopay_bank_account.account_number,tb_autopay_bank_account.account_holder_name,tb_autopay_bank.icon_path,tb_autopay_bank.logo_path,tb_autopay_sim.sim_phonenumber, tb_autopay_bank_account.last_transaction, tb_autopay_bank_account.accumulated,tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.bank_id,tb_autopay_bank_account.bank_sms, tb_autopay_bank_account.bank_sms_connected, tb_autopay_bank_account.bank_api, tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.identification_number, tb_autopay_bank_account.phone_number")->join("tb_autopay_bank","tb_autopay_bank.id=tb_autopay_bank_account.bank_id")->join("tb_autopay_sim","tb_autopay_sim.id=tb_autopay_bank_account.sim_id","left")->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id'], 'tb_autopay_bank_account.id' => $id])->get()->getRow();

        if(!is_object($data['bank_account_details']))
            show_404();
        
        $isOcbEnterpriseBankAccount = $ocbEnterpriseAccountModel->where(['bank_account_id' => $data['bank_account_details']->id])->countAllResults();

        if($isOcbEnterpriseBankAccount == 0)
            show_404();

        if($data['bank_account_details']->bank_id == 18 && $data['bank_account_details']->bank_api_connected == 0 && $isOcbEnterpriseBankAccount == 0) {
            return redirect()->to(base_url('ocb/step2/' . $data['bank_account_details']->id));
        }

        $data['ocb_enterprise_bank_account'] = $ocbEnterpriseAccountModel->where(['bank_account_id' => $data['bank_account_details']->id])->first();

        $data['ocb_enterprise_apps'] = $ocbEnterpriseAppModel->where(['bank_account_id' => $data['bank_account_details']->id])->get()->getResult();

        $appId = $this->request->getGet('app_id');

        if ((!$appId || !is_numeric($appId) || !$ocbEnterpriseAppModel->where(['id' => $appId])->first()) && count($data['ocb_enterprise_apps']) > 0) {
            return redirect()->to(base_url('ocb/vas/' . $data['bank_account_details']->id) . '?app_id=' . $data['ocb_enterprise_apps'][0]->id);
        }

        $data['app_id'] = $appId;
        $data['app'] = array_values(array_filter($data['ocb_enterprise_apps'], function($app) use ($data) {
            return $app->id == $data['app_id'];
        }))[0] ?? [];

        $data['count_subs'] = $bankSubAccountModel->where(['bank_account_id' => $id, 'deleted_at' => NULL])->countAllResults();

        echo theme_view('templates/autopay/header',$data);
        echo theme_view('ocb/enterprise/vas', $data);
        echo theme_view('templates/autopay/footer',$data);
    }

    public function ajax_app_va_list() {

        if(!has_permission('BankAccount', 'can_view_all'))
            show_404();
     
        if ($this->request->getMethod(true) != 'POST')
            return '';

        $appId = $this->request->getGet('app_id');
        $bankAccountId = $this->request->getGet('bank_account_id');
        $ocbEnterpriseAppId = $this->request->getGet('ocb_enterprise_app_id');

        if(!is_numeric($bankAccountId))
            $bankAccountId = FALSE;

        if(!is_numeric($ocbEnterpriseAppId))
            $ocbEnterpriseAppId = FALSE;
 
        $bankSubAccountModel = model(\App\Models\OcbBankSubAccountModel::class);

        $bank_accounts = $bankSubAccountModel->getDatatables($this->user_session['company_id'], $bankAccountId, $appId);
         
        $data = array();

        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');

        $can_edit = has_permission('BankAccount', 'can_edit');
        $can_delete = has_permission('BankAccount', 'can_delete');
 
        foreach ($bank_accounts as $account) {

            $no++;
            $row = array();

            $actions_btn = '';

            if($can_edit) {
                $actions_btn = "<a href='javascript:;' onclick='edit_va(" . $account->id. ")' class='btn btn-sm btn-outline-warning ms-2 me-2 mt-2'><i class='bi bi-pencil'></i> Sửa</a>";
                
                if($account->va_active == 1)                  
                    $actions_btn = $actions_btn .  "<button type='button' onclick='disable_va(" . $account->id. ")' class='d-inline-flex align-items-center btn btn-sm btn-outline-danger ms-2 mt-2 btn-disable-va-".$account->id."' style='gap: 0.25rem'><div class='spinner-border text-danger loader' style='width: 10px; height: 10px; display: none;' role='status'></div> <i class='bi bi-toggle-off'></i> Vô hiệu hoá</button>";
                else 
                    $actions_btn = $actions_btn .  "<button type='button' onclick='enable_va(" . $account->id. ")' class='d-inline-flex align-items-center btn btn-sm btn-outline-primary ms-2 mt-2 btn-enable-va-".$account->id."' style='gap: 0.25rem'><div class='spinner-border text-primary loader' style='width: 10px; height: 10px; display: none;' role='status'></div> <i class='bi bi-toggle-off'></i> Bật lại</button>";
            }
                 
            $row[] = $no;
            $row[] = $account->id;
            $row[] = "<a href='javascript:;' onclick='view_va("  . $account->id . ")'>" . esc($account->sub_account) . "</a>";
          
            if($account->va_active == 1)
                $row[] = "<span class='text-success'>Hoạt động</span>";
            else
                $row[] = "<span class='text-danger'>Tạm ngưng</span>";
            $row[] = esc($account->label);
           

            $row[] = esc($account->created_at);
            $row[] = $actions_btn;
       
            $data[] = $row;
        }
 
        $output = array(
            "draw" => $draw,
            "recordsTotal" => $bankSubAccountModel->countAll($this->user_session['company_id'], $bankAccountId, $appId),
            "recordsFiltered" => $bankSubAccountModel->countFiltered($this->user_session['company_id'], $bankAccountId, $appId),
            "data" => $data,
        );

        return $this->response->setJSON($output);
    }

    public function ajax_add_app_va()
    {
        $ocbConfig = config(\Config\Ocb::class);

        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        if(!has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn chưa được cấp quyền để thực hiện thao tác này"));

        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $ocbEnterpriseAppModel = model(OcbEnterpriseAppModel::class);
        $ocbEnterpriseAccountModel = model(\App\Models\OcbEnterpriseAccountModel::class);
        $bankAccountId = trim($this->request->getPost('bank_account_id'));

        $bankAccountDetails = $bankAccountModel->where(['id'=>$bankAccountId, 'company_id' => $this->user_session['company_id']])->get()->getRow();
        
        if(!is_object($bankAccountDetails))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy tài khoản ngân hàng này"));

        $ocbEnterpriseAppDetails = $ocbEnterpriseAppModel->where([
            'id'=> trim($this->request->getVar('ocb_enterprise_app_id')),
            'bank_account_id' => $bankAccountDetails->id
        ])->get()->getRow();

        if(!is_object($bankAccountDetails))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy ứng dụng ngân hàng OCB này"));

        $ocbEnterpriseAccountDetails = $ocbEnterpriseAccountModel->where([
            'bank_account_id' => $bankAccountDetails->id
        ])->get()->getRow();

        $defaultVANumberMinLength = $ocbConfig->OcbDefaultVANumberMinLength;

        if (in_array($this->user_details->id, $ocbConfig->SePayVIPUserIdList)) {
            $defaultVANumberMinLength = 1;
        }

        if (! $this->validate([
            'sub_id' => $ocbConfig->OcbVAOnlyNumber 
                ? 'required|min_length[' . $defaultVANumberMinLength .']|max_length[15]|regex_match[/^[0-9]+$/]' 
                : 'required|min_length[' . $defaultVANumberMinLength .']|max_length[15]|regex_match[/^[a-zA-Z0-9]+$/]',
            'label' => 'required|max_length[65]|regex_match[/^[a-zA-Z0-9\s\.\-]+$/]'
        ])) return $this->fail($this->validator->getErrors());

        try {
            $client = new OcbClient(
                $ocbEnterpriseAppDetails->client_id,
                $ocbEnterpriseAppDetails->client_secret,
                $ocbEnterpriseAppDetails->client_cert,
                $ocbEnterpriseAppDetails->private_key,
                $ocbEnterpriseAppDetails->username,
                $ocbEnterpriseAppDetails->password,
                $ocbEnterpriseAccountDetails->partner_code,
                $ocbEnterpriseAccountDetails->prefix_va,
                $this->company_details->id,
                $ocbEnterpriseAppDetails->id,
                false
            );
        } catch (\Exception $e) {
            return $this->handleOcbClientException($e);
        }

        $data = [
            'sub_id' => trim($this->request->getVar('sub_id')),
            'label' => xss_clean($this->request->getVar('label')),
        ];

        $data['sub_account'] = $client->getPartnerCode() . $data['sub_id'];

        if ($bankSubAccountModel->where(['sub_account' => $data['sub_account']])->first()) {
            return $this->fail(['sub_id' => 'Số VA đã tồn tại']);
        }
        
        try {
            $response = $client->createVirtualAccountDirect(
                $client->getPrefixVA(),
                $data['sub_id'],
                $data['label'],
                $bankAccountDetails->account_number,
            );
        } catch (\Exception $e) {
            return $this->handleOcbClientException($e);
        }

        $responseData = json_decode($response->getBody(), true);

        if (isset($responseData['trace']) && !isset($responseData['error'])) {
            $bankSubAccountConfig = get_configuration('BankSubAccount');
            if($bankSubAccountConfig == "off") {
                $configurationModel = model(ConfigurationModel::class);
                $configurationModel->set([
                    'value' => "on",
                ])->where(['company_id' => $this->user_session['company_id'],'setting' => 'BankSubAccount'])->update();
            }

            $configurationModel = model(ConfigurationModel::class);
            $config_result = $configurationModel->where(['setting' => 'OCBVaOrderLastNumber'])->get()->getRow();
            $order = is_object($config_result) ? $config_result->value + 1 : 1;

            $bankSubAccountModel = model(BankSubAccountModel::class);
            $result = $bankSubAccountModel->insert([
                'bank_account_id' => $bankAccountDetails->id,
                'sub_account' => $data['sub_account'], 
                'va_order' => $order, 
                'acc_type' => 'Real', 
                'sub_holder_name' => $bankAccountDetails->account_holder_name,
                'label' => $data['label']
            ]);

            $ocbBankSubAccountMetaData = model(OcbBankSubAccountMetaData::class);
            $ocbBankSubAccountMetaData->insert([
                'bank_account_id' => $bankAccountId,
                'sub_account_id' => $result,
                'ocb_enterprise_app_id' => $ocbEnterpriseAppDetails->id,
            ]);

            $configurationModel->set(['value' => $order])->where(['setting' => 'OCBVaOrderLastNumber', 'company_id' => 0])->update();
        
            return $this->response->setJSON([
                "status" => true,
                "message" => 'Thêm VA thành công'
            ]);
        } else {
            $errorMessage = 'Không thể kết nối với OCB, vui lòng liên hệ với SePay để được hỗ trợ';
            log_message('error', $responseData['error']['details']);

            if ($responseData['error']['code'] == '504') {
                $errorMessage = 'Hệ thống OCB đang bận, vui lòng thử lại sau';
            }

            if ($responseData['error']['code'] == '41776') {
                return $this->fail(['sub_id' => 'Số VA đã tồn tại trên ứng dụng thuộc hệ thống của OCB']);
            }

            return $this->response->setJSON([
                "status" => false,
                "message" => $errorMessage
            ]);
        }
    }

    public function ajax_edit_app_va()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        $id = trim($this->request->getPost('id'));

        if(!is_numeric($id)) 
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"ID tài khoản ảo không hợp lệ"));

        if(!has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn chưa được cấp quyền để thực hiện thao tác này"));
        
        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $ocbEnterpriseAccountModel = model(\App\Models\OcbEnterpriseAccountModel::class);
        $ocbEnterpriseAppModel = model(\App\Models\OcbEnterpriseAppModel::class);

        $bankAccountId = $this->request->getPost('bank_account_id');
        $bankAccountDetails = $bankAccountModel->where(['id'=> $bankAccountId, 'company_id' => $this->user_session['company_id']])->get()->getRow();
        $bankSubAccountDetails = $bankSubAccountModel
            ->select('tb_autopay_ocb_bank_sub_account_metadata.ocb_enterprise_app_id,tb_autopay_bank_sub_account.id,tb_autopay_bank_sub_account.sub_account,tb_autopay_bank_sub_account.label')
            ->join('tb_autopay_ocb_bank_sub_account_metadata', 'tb_autopay_ocb_bank_sub_account_metadata.sub_account_id=tb_autopay_bank_sub_account.id')
            ->where(['tb_autopay_bank_sub_account.id' => $id, 'tb_autopay_bank_sub_account.bank_account_id' => $bankAccountDetails->id])->get()->getRow();
        $ocbEnterpriseAccountDetails = $ocbEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->get()->getRow();
        $ocbEnterpriseAppDetails = $ocbEnterpriseAppModel->where(['id' => $bankSubAccountDetails->ocb_enterprise_app_id])->get()->getRow();

        if(!is_object($bankAccountDetails) 
            || !is_object($bankSubAccountDetails) 
            || !is_object($ocbEnterpriseAccountDetails) 
            || !is_object($ocbEnterpriseAppDetails))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy tài khoản ảo này"));

        if (! $this->validate([
            'label' => 'required|max_length[65]|regex_match[/^[a-zA-Z0-9\s\.\-]+$/]'
        ])) return $this->fail($this->validator->getErrors());

        $data = [
            'label' => trim(xss_clean($this->request->getVar('label'))),
        ];

        try {
            $client = new OcbClient(
                $ocbEnterpriseAppDetails->client_id,
                $ocbEnterpriseAppDetails->client_secret,
                $ocbEnterpriseAppDetails->client_cert,
                $ocbEnterpriseAppDetails->private_key,
                $ocbEnterpriseAppDetails->username,
                $ocbEnterpriseAppDetails->password,
                $ocbEnterpriseAccountDetails->partner_code,
                $ocbEnterpriseAccountDetails->prefix_va,
                $this->company_details->id,
                $ocbEnterpriseAppDetails->id,
                false
            );
        } catch (\Exception $e) {
            return $this->handleOcbClientException($e);
        }

        if ($data['label'] === $bankSubAccountDetails->label) {
            return $this->response->setJSON([
                "status" => true,
                "message" => 'Sửa VA thành công.'
            ]);
        }

        try {
            $response = $client->modifyVirtualAccountDirect(
                $client->getPartnerCode(),
                str_replace($client->getPartnerCode(), '', $bankSubAccountDetails->sub_account),
                $bankSubAccountDetails->label,
                $data['label']
            );
        } catch (\Exception $e) {
            return $this->handleOcbClientException($e);
        }

        $responseData = json_decode($response->getBody(), true);

        if (isset($responseData['trace']) && !isset($responseData['error'])) {
            $updated = $bankSubAccountModel->update($bankSubAccountDetails->id, $data);

            if (!$updated) {
                return $this->response->setJSON([
                    "status" => false,
                    "message" => 'Đã có lỗi xảy ra, vui lòng thử lại sau hoặc liên hệ SePay để được hỗ trợ.'
                ]);
            }

            return $this->response->setJSON([
                "status" => true,
                "message" => 'Sửa VA thành công.'
            ]);
        } else {
            $errorMessage = 'Không thể kết nối với OCB, vui lòng liên hệ với SePay để được hỗ trợ';
            log_message('error', $responseData['error']['details']);

            if ($responseData['error']['code'] == '504') {
                $errorMessage = 'Hệ thống OCB đang bận, vui lòng thử lại sau';
            }

            return $this->response->setJSON([
                "status" => false,
                "message" => $errorMessage
            ]);
        }
    }

    public function ajax_disable_app_va()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        $id = trim($this->request->getPost('id'));

        if(!is_numeric($id)) 
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"ID tài khoản ảo không hợp lệ"));

        if(!has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn chưa được cấp quyền để thực hiện thao tác này"));
        
        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $ocbEnterpriseAccountModel = model(\App\Models\OcbEnterpriseAccountModel::class);
        $ocbEnterpriseAppModel = model(\App\Models\OcbEnterpriseAppModel::class);

        $bankAccountId = $this->request->getPost('bank_account_id');
        $bankAccountDetails = $bankAccountModel->where(['id'=> $bankAccountId, 'company_id' => $this->user_session['company_id']])->get()->getRow();
        $bankSubAccountDetails = $bankSubAccountModel
            ->select('tb_autopay_ocb_bank_sub_account_metadata.ocb_enterprise_app_id,tb_autopay_bank_sub_account.id,tb_autopay_bank_sub_account.sub_account,tb_autopay_bank_sub_account.label')
            ->join('tb_autopay_ocb_bank_sub_account_metadata', 'tb_autopay_ocb_bank_sub_account_metadata.sub_account_id=tb_autopay_bank_sub_account.id')
            ->where(['tb_autopay_bank_sub_account.id' => $id, 'tb_autopay_bank_sub_account.bank_account_id' => $bankAccountDetails->id])->get()->getRow();
        $ocbEnterpriseAccountDetails = $ocbEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->get()->getRow();
        $ocbEnterpriseAppDetails = $ocbEnterpriseAppModel->where(['id' => $bankSubAccountDetails->ocb_enterprise_app_id])->get()->getRow();

        if(!is_object($bankAccountDetails) 
            || !is_object($bankSubAccountDetails) 
            || !is_object($ocbEnterpriseAccountDetails) 
            || !is_object($ocbEnterpriseAppDetails))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy tài khoản ảo này"));

        try {
            $client = new OcbClient(
                $ocbEnterpriseAppDetails->client_id,
                $ocbEnterpriseAppDetails->client_secret,
                $ocbEnterpriseAppDetails->client_cert,
                $ocbEnterpriseAppDetails->private_key,
                $ocbEnterpriseAppDetails->username,
                $ocbEnterpriseAppDetails->password,
                $ocbEnterpriseAccountDetails->partner_code,
                $ocbEnterpriseAccountDetails->prefix_va,
                $this->company_details->id,
                $ocbEnterpriseAppDetails->id,
                false
            );
        } catch (\Exception $e) {
            return $this->handleOcbClientException($e);
        }

        try {
            $response = $client->closeVirtualAccountDirect(
                $client->getPartnerCode(),
                str_replace($client->getPartnerCode(), '', $bankSubAccountDetails->sub_account),
            );
        } catch (\Exception $e) {
            return $this->handleOcbClientException($e);
        }

        $responseData = json_decode($response->getBody(), true);

        if (isset($responseData['trace']) && !isset($responseData['error'])) {
            $updated = $bankSubAccountModel->update($bankSubAccountDetails->id, ['va_active' => 0]);

            if (!$updated) {
                return $this->response->setJSON([
                    "status" => false,
                    "message" => 'Đã có lỗi xảy ra, vui lòng thử lại sau hoặc liên hệ SePay để được hỗ trợ.'
                ]);
            }

            return $this->response->setJSON([
                "status" => true,
                "message" => 'Đã vô hiệu hoá VA'
            ]);
        } else {
            $errorMessage = 'Không thể kết nối với OCB, vui lòng liên hệ với SePay để được hỗ trợ';
            log_message('error', $responseData['error']['details']);

            if ($responseData['error']['code'] == '504') {
                $errorMessage = 'Hệ thống OCB đang bận, vui lòng thử lại sau';
            }

            return $this->response->setJSON([
                "status" => false,
                "message" => $errorMessage
            ]);
        }
    }

    public function ajax_enable_app_va()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        $id = trim($this->request->getPost('id'));

        if(!is_numeric($id)) 
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"ID tài khoản ảo không hợp lệ"));

        if(!has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn chưa được cấp quyền để thực hiện thao tác này"));
        
        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $ocbEnterpriseAccountModel = model(\App\Models\OcbEnterpriseAccountModel::class);
        $ocbEnterpriseAppModel = model(\App\Models\OcbEnterpriseAppModel::class);

        $bankAccountId = $this->request->getPost('bank_account_id');
        $bankAccountDetails = $bankAccountModel->where(['id'=> $bankAccountId, 'company_id' => $this->user_session['company_id']])->get()->getRow();
        $bankSubAccountDetails = $bankSubAccountModel
            ->select('tb_autopay_ocb_bank_sub_account_metadata.ocb_enterprise_app_id,tb_autopay_bank_sub_account.id,tb_autopay_bank_sub_account.sub_account,tb_autopay_bank_sub_account.label')
            ->join('tb_autopay_ocb_bank_sub_account_metadata', 'tb_autopay_ocb_bank_sub_account_metadata.sub_account_id=tb_autopay_bank_sub_account.id')
            ->where(['tb_autopay_bank_sub_account.id' => $id, 'tb_autopay_bank_sub_account.bank_account_id' => $bankAccountDetails->id])->get()->getRow();
        $ocbEnterpriseAccountDetails = $ocbEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->get()->getRow();
        $ocbEnterpriseAppDetails = $ocbEnterpriseAppModel->where(['id' => $bankSubAccountDetails->ocb_enterprise_app_id])->get()->getRow();

        if(!is_object($bankAccountDetails) 
            || !is_object($bankSubAccountDetails) 
            || !is_object($ocbEnterpriseAccountDetails) 
            || !is_object($ocbEnterpriseAppDetails))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy tài khoản ảo này"));

        try {
            $client = new OcbClient(
                $ocbEnterpriseAppDetails->client_id,
                $ocbEnterpriseAppDetails->client_secret,
                $ocbEnterpriseAppDetails->client_cert,
                $ocbEnterpriseAppDetails->private_key,
                $ocbEnterpriseAppDetails->username,
                $ocbEnterpriseAppDetails->password,
                $ocbEnterpriseAccountDetails->partner_code,
                $ocbEnterpriseAccountDetails->prefix_va,
                $this->company_details->id,
                $ocbEnterpriseAppDetails->id,
                false
            );
        } catch (\Exception $e) {
            return $this->handleOcbClientException($e);
        }

        try {
            $response = $client->reActiveVirtualAccountNumber(
                $client->getPartnerCode(),
                str_replace($client->getPartnerCode(), '', $bankSubAccountDetails->sub_account),
            );
        } catch (\Exception $e) {
            return $this->handleOcbClientException($e);
        }

        $responseData = json_decode($response->getBody(), true);

        if (isset($responseData['trace']) && !isset($responseData['error'])) {
            $updated = $bankSubAccountModel->update($bankSubAccountDetails->id, ['va_active' => 1]);

            if (!$updated) {
                return $this->response->setJSON([
                    "status" => false,
                    "message" => 'Đã có lỗi xảy ra, vui lòng thử lại sau hoặc liên hệ SePay để được hỗ trợ.'
                ]);
            }

            return $this->response->setJSON([
                "status" => true,
                "message" => 'Đã kích hoạt lại VA'
            ]);
        } else {
            $errorMessage = 'Không thể kết nối với OCB, vui lòng liên hệ với SePay để được hỗ trợ';
            log_message('error', $responseData['error']['details']);

            if ($responseData['error']['code'] == '504') {
                $errorMessage = 'Hệ thống OCB đang bận, vui lòng thử lại sau';
            }

            return $this->response->setJSON([
                "status" => false,
                "message" => $errorMessage
            ]);
        }
    }

    public function ajax_get_enterprise_bank_account($id)
    {
        if(!is_numeric($id)) 
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"ID tài khoản ngân hàng không hợp lệ"));

        if(!has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền sửa tài khoản ngân hàng"));
    
        
        $bankAccountModel = model(BankAccountmodel::class);
        $ocbEnterpriseAccountModel = model(OcbEnterpriseAccountModel::class);

        $result = $bankAccountModel->select("id,company_id,bank_id,sim_id,account_holder_name,account_number,label,active")->where(['id'=>$id, 'company_id' => $this->user_session['company_id']])->get()->getRow();
        $result->ocb_enterprise_account = $ocbEnterpriseAccountModel->where('bank_account_id', $id)->get()->getRow();

        if($result)
            return $this->response->setJSON(["status"=>TRUE, "data"=>$result]);
        else
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy tài khoản ngân hàng này"));
    }

    public function ajax_enterprise_bank_account_update() {
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        if(!has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền sửa tài khoản ngân hàng"));
 
        $validation =  \Config\Services::validation();

        helper('text');

        $rules = [
            'id' => 'required|integer|is_natural',
            'label' => ['label' => 'Tên gợi nhớ', 'rules' => "max_length[100]"],
        ];

        if (is_admin()) {
            $rules = array_merge($rules, [
                'username' => 'required|max_length[250]',
                'password' => 'required|max_length[250]',
                'partner_code' => 'required|max_length[250]',
                'prefix_va' => 'required|max_length[250]',
            ]);
        }

        if(! $this->validate($rules)) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }

        $bankAccountModel = model(BankAccountmodel::class);
        $ocbEnterpriseAccountModel = model(OcbEnterpriseAccountModel::class);
        $bank_account_id = $this->request->getPost('id');

        $result = $bankAccountModel->where(['id'=>$bank_account_id, 'company_id' => $this->user_session['company_id']])->get()->getRow();
        $ocbEnterpriseAccountDetails = $ocbEnterpriseAccountModel->where(['bank_account_id'=>$bank_account_id])->get()->getRow();
        
        if(!is_object($result) || !is_object($ocbEnterpriseAccountDetails))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy tài khoản ngân hàng này"));

        $data = array(
            'label' => trim(xss_clean($this->request->getVar('label'))),
        );

        $bankAccountUpdateResult = $bankAccountModel->set($data)->where("id",$bank_account_id)->update();

        if (is_admin()) {
            $data = array(
                'username' => trim($this->request->getVar('username')),
                'password' => $this->request->getVar('password'),
                'partner_code' => trim($this->request->getVar('partner_code')),
                'prefix_va' => trim($this->request->getVar('prefix_va')),
            );
    
            $ocbEnterpriseAccountUpdateResult = $ocbEnterpriseAccountModel->set($data)->where("id",$ocbEnterpriseAccountDetails->id)->update();
        } else {
            $ocbEnterpriseAccountUpdateResult = true;
        }
        
        if($bankAccountUpdateResult && $ocbEnterpriseAccountUpdateResult) { 
            add_user_log(array('data_id'=>$bank_account_id,'company_id' => $this->user_session['company_id'], 'data_type'=>'bank_account_update','description'=>'Sửa tài khoản ngân hàng','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
            return $this->response->setJSON(array("status"=>true, 'message' => 'Cập nhật tài khoản ngân hàng thành công!'));
        } else {
            return $this->response->setJSON(array("status"=>false,"message"=>"Không thể cập nhật tài khoản ngân hàng!"));
        }
    }

    public function ajax_bank_account_update() 
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        if (!has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền sửa tài khoản ngân hàng']);
 
        $validation = \Config\Services::validation();

        helper('text');

        if (! $this->validate([
            'id' => 'required|integer|is_natural',
            'label' => ['label' => 'Tên gợi nhớ', 'rules' => "max_length[100]"],
        ])) {
            return $this->response->setJSON(['status' => false, 'message' => implode(". ", $validation->getErrors())]);
        }  

        $bankAccountModel = model(BankAccountmodel::class);
        $bankAccountId = trim(xss_clean($this->request->getPost('id')));

        $bankAccount = $bankAccountModel->where(['id' => $bankAccountId, 'company_id' => $this->user_session['company_id']])->get()->getRow();
        
        if (!is_object($bankAccount))
            return $this->response->setJSON(['status' => false, 'message' => 'Không tìm thấy tài khoản ngân hàng này']);

        $data = [
            'label' => trim(xss_clean($this->request->getVar('label'))),
        ];
           
        $updated = $bankAccountModel->set($data)->where('id', $bankAccountId)->update();
        
        if ($updated) { 
            add_user_log(['data_id' => $bankAccountId, 'company_id' => $this->user_session['company_id'], 'data_type' => 'bank_account_update', 'description' => 'Sửa tài khoản ngân hàng', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);
            return $this->response->setJSON(['status' => true]);
        } else {
            return $this->response->setJSON(['status' => false, 'message' => 'Không thể cập nhật tài khoản ngân hàng!']);
        }
    }

    
    public function ajax_request_create_qr_shop($shopId = null)
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (! has_permission('BankAccount', 'can_create'))
            return $this->respond(['status ' => false, 'message' => 'Bạn không đủ quyền.']);
        
        $shopModel = model(ShopModel::class);
        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $ocbConfig = config(\Config\Ocb::class);

        $shopId = trim(xss_clean($shopId));

        if (!$shopId || !is_numeric($shopId)) return $this->failNotFound();

        $shopDetails = $shopModel->where(['id' => $shopId, 'company_id' => $this->company_details->id])->first();

        if (!$shopDetails) return $this->failNotFound();

        $data = [
            'account_number' => trim(xss_clean($this->request->getVar('account_number'))),
            'identification_number' => trim(xss_clean($this->request->getVar('identification_number'))),
            'phone_number' => trim(xss_clean($this->request->getVar('phone_number'))),
            'bank_account_id' => trim(xss_clean($this->request->getVar('bank_account_id'))),
            'label' => trim(xss_clean($this->request->getVar('label'))),
        ];

        $bankAccountDetails = $data['bank_account_id'] ? $bankAccountModel->where(['id' => $data['bank_account_id']])->first() : null;

        if ($bankAccountDetails) {
            $data['account_number'] = $bankAccountDetails->account_number;
            $data['identification_number'] = $bankAccountDetails->identification_number;
            $data['phone_number'] = $bankAccountDetails->phone_number;
        }

        $rules = is_null($bankAccountDetails) ? [
            'account_number' => ['required', 'max_length[100]'],
            'identification_number' => ['required', 'max_length[100]'],
            'phone_number' => ['required', 'max_length[20]'],
        ] : [];

        $rules['label'] = ['required', 'max_length[100]'];

        if (! $this->validateData($data, $rules)) {
            return $this->fail($this->validator->getErrors());
        }

        if (!$bankAccountDetails && $bankAccountModel->where(['account_number' => $data['account_number'], 'bank_id' => 18])->countAllResults()) {
            return $this->fail(['account_number' => 'Số tài khoản ngân hàng này đã tồn tại trên hệ thống']);
        }

        do {
            $subAccount = '';
        
            for ($i = 1; $i <= 10; $i++) {
                $subAccount .= rand(0, 9);
            }
        } while ($bankSubAccountModel->where(['sub_account' => $subAccount])->countAllResults() > 0);        

        try {
            $client = new OcbClient;
        } catch (\Exception $e) {
            return $this->handleOcbClientException($e);
        }

        $errorMessage = 'Không thể kết nối với OCB, vui lòng liên hệ với SePay để được hỗ trợ';

        if ($bankAccountDetails) {
            $accountHolderName = $bankAccountDetails->account_holder_name;
        } else {
            try {
                $getAccountHolderNameResponse = $client->retrieveOCBRecipientName(
                    $this->request->getVar('account_number')
                );
            } catch (\Exception $e) {
                return $this->handleOcbClientException($e);
            }
    
            $getAccountHolderNameResponseData = json_decode($getAccountHolderNameResponse->getBody(), true);
    
            if (isset($responseData['error'])) {
                return $this->respond([
                    'status' => false,
                    'message' => 'Đã có lỗi xảy ra, vui lòng thử lại'
                ]);
            }
    
            $accountHolderName = $getAccountHolderNameResponseData['data']['queryResult']['accountName'];
        }

        try {
            $registerMerchantStep1Response = $client->registerMerchantStep1(
                $data['identification_number'],
                $data['phone_number'],
                $data['account_number'],
                $client->getPartnerCode(),
                $this->user_session['email'],
                $accountHolderName,
                remove_accents($shopDetails->name, true),
                '0004',
                remove_accents($shopDetails->address, true),
                $subAccount,
            );
        } catch (\Exception $e) {
            return $this->handleOcbClientException($e);
        }

        if (!in_array($registerMerchantStep1Response->getStatusCode(), [200, 201])) {
            return $this->respond([
                'status' => false,
                'message' => $errorMessage
            ]);
        }

        $registerMerchantStep1ResponseData = json_decode($registerMerchantStep1Response->getBody(), true);

        //SIMULATE
        // $registerMerchantStep1ResponseData = [
        //     'trace' => [
        //         'bankRefNo' => uniqid()
        //     ]
        // ];

        if (isset($registerMerchantStep1ResponseData['error'])) {
            // VA number already exists on OCB
            if ($registerMerchantStep1ResponseData['error']['code'] == '41776') {
                return $this->fail(['sub_account' => 'Đã có lỗi xảy ra, vui lòng tải lại trang']);
            }

            if ($registerMerchantStep1ResponseData['error']['code'] == '41749') {
                return $this->respond([
                    'status' => false,
                    'message' => 'Số điện thoại đăng ký không chính xác'
                ]);
            }

            if ($registerMerchantStep1ResponseData['error']['code'] == '41744') {
                return $this->fail(['identification_number' => 'Số giấy tờ tùy thân không chính xác']);
            }

            if ($registerMerchantStep1ResponseData['error']['code'] == '504') {
                $errorMessage = 'Hệ thống OCB đang bận, vui lòng thử lại sau';
            }

            return $this->respond([
                'status' => false,
                'message' => $errorMessage
            ]);
        }

        return $this->respond([
            'status' => true,
            'request_id' => $registerMerchantStep1ResponseData['trace']['bankRefNo'],
            'sub_account' => $subAccount,
            'message' => 'OTP đã được gửi đi',
        ]);
    }

    public function ajax_confirm_create_qr_shop_with_new_bank_account($shopId = null)
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (! has_permission('BankAccount', 'can_create'))
            return $this->respond(['status ' => false, 'message' => 'Bạn không đủ quyền.']);
        
        $shopModel = model(ShopModel::class);
        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $bankShopLinkModel = model(BankShopLinkModel::class);
        $ocbConfig = config(\Config\Ocb::class);

        $shopId = trim(xss_clean($shopId));

        if (!$shopId || !is_numeric($shopId)) return $this->failNotFound();

        $shopDetails = $shopModel->where(['id' => $shopId, 'company_id' => $this->company_details->id])->first();

        if (!$shopDetails) return $this->failNotFound();

        $data = [
            'bank_account_id' => trim(xss_clean($this->request->getVar('bank_account_id'))), 
            'account_number' => trim(xss_clean($this->request->getVar('account_number'))),
            'identification_number' => trim(xss_clean($this->request->getVar('identification_number'))),
            'phone_number' => trim(xss_clean($this->request->getVar('phone_number'))),
            'otp' => trim(xss_clean($this->request->getVar('otp'))),
            'request_id' => $this->request->getVar('request_id'),
            'sub_account' => trim(xss_clean($this->request->getVar('sub_account'))), 
            'label' => trim(xss_clean($this->request->getVar('label')))
        ];

        $bankAccountDetails = $data['bank_account_id'] ? $bankAccountModel->where([
            'id' => $data['bank_account_id'],
            'company_id' => $this->user_session['company_id']
        ])->first() : null;

        $rules = !$bankAccountDetails ? [
            'account_number' => ['required', 'max_length[100]'],
            'identification_number' => ['required', 'max_length[100]'],
            'phone_number' => ['required', 'max_length[20]'],
        ] : [];

        $rules['sub_account'] = [
            'rules' => 'required', "regex_match[/^/d{10}$/]",
            'errors' => [
                'required' => 'Đã có lỗi xảy ra, vui lòng tải lại trang',
                'regex_match' => 'Đã có lỗi xảy ra, vui lòng tải lại trang',
            ]
        ];
        
        $rules['label'] = ['required', 'max_length[100]'];
        $rules['otp'] = ['required', 'max_length[6]'];
        $rules['request_id'] = ['required'];

        if (! $this->validateData($data, $rules)) {
            return $this->fail($this->validator->getErrors());
        }

        if (!$bankAccountDetails && $bankAccountModel->where(['account_number' => $data['account_number'], 'bank_id' => 18])->countAllResults()) {
            return $this->fail(['account_number' => 'Số tài khoản ngân hàng này đã tồn tại trên hệ thống']);
        }

        if (!$bankAccountDetails && $bankSubAccountModel->where(['sub_account' => $ocbConfig->OcbPrefixVA . $data['sub_account']])->countAllResults()) {
            return $this->fail(['sub_account' => 'Đã có lỗi xảy ra, vui lòng tải lại trang']);
        }

        try {
            $client = new OcbClient;
        } catch (\Exception $e) {
            return $this->handleOcbClientException($e);
        }

        $errorMessage = 'Không thể kết nối với OCB, vui lòng liên hệ với SePay để được hỗ trợ';

        if(!$bankAccountDetails){
            try {
                $getAccountHolderNameResponse = $client->retrieveOCBRecipientName($this->request->getVar('account_number'));
            } catch (\Exception $e) {
                return $this->handleOcbClientException($e);
            }

            if (!in_array($getAccountHolderNameResponse->getStatusCode(), [200, 201]))
                return $this->respond(['status' => false, 'message' => $errorMessage]);

            $getAccountHolderNameResponseData = json_decode($getAccountHolderNameResponse->getBody(), true);

            if (isset($responseData['error'])) {
                return $this->respond(['status' => false, 'message' => $errorMessage]);
            }

            $accountHolderName = dot_array_search('data.queryResult.accountName', $getAccountHolderNameResponseData);

            if (!$accountHolderName)
                return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng tải lại trang']);
        }else{
            $accountHolderName = $bankAccountDetails->account_holder_name;
        }
        try {
            $registerMerchantStep2Response = $client->registerMerchantStep2(
                $client->getPartnerCode(),
                $data['otp'],
                $data['request_id']
            );
        } catch (\Exception $e) {
            return $this->handleOcbClientException($e);
        }

        if (!in_array($registerMerchantStep2Response->getStatusCode(), [200, 201]))
            return $this->respond(['status' => false, 'message' => $errorMessage]);

        $registerMerchantStep2ResponseData = json_decode($registerMerchantStep2Response->getBody(), true);

        //SIMULATE
        // $registerMerchantStep2ResponseData = [
        //     'trace' => [
        //         'bankRefNo' => uniqid()
        //     ],
        //     'data' => [
        //         'merchantInfo' => [
        //             'vaAccountNumber' => $data['sub_account'],
        //         ]
        //     ]
        // ];
        
        if (isset($registerMerchantStep2ResponseData['error'])) {
            if ($registerMerchantStep2ResponseData['error']['code'] == '41724') {
                return $this->fail(['otp' => 'OTP không chính xác']);
            }

            if ($registerMerchantStep2ResponseData['error']['code'] == '41726') {
                return $this->fail(['otp' => 'OTP đã hết hạn']);
            }

            if ($registerMerchantStep2ResponseData['error']['code'] == '41731') {
                $errorMessage = 'Đã có lỗi xảy ra, vui lòng tải lại trang';
            }

            if ($registerMerchantStep2ResponseData['error']['code'] == '504') {
                $errorMessage = 'Hệ thống OCB đang bận, vui lòng thử lại sau';
            }

            if ($registerMerchantStep2ResponseData['error']['code'] == '504') {
                $errorMessage = 'Hệ thống OCB đang bận, vui lòng thử lại sau';
            }

            return $this->respond([
                'status' => false,
                'message' => $errorMessage
            ]);
        }

        if (!$bankAccountDetails) {
            $safeCreateBankAccountData = array(
                'company_id' => $this->user_session['company_id'],
                'account_holder_name' => $accountHolderName,
                'account_number' => $data['account_number'],
                'bank_id' => 18,
                'active' => 1,
                'bank_api' => 1,
                'bank_api_connected' => 1,
                'identification_number' => $data['identification_number'],
                'phone_number' => $data['phone_number']
            );
    
            $bankAccountId = $bankAccountModel->insert($safeCreateBankAccountData);

            if (!$bankAccountId) {
                log_message('error', '[OcbController->ajax_confirm_create_qr_shop_with_new_bank_account]: Create bank account failed - ' . json_encode($safeCreateBankAccountData));
                return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng thử lại sau.']);
            }
            add_user_log(['data_id' => $bankAccountId, 'company_id' => $this->company_details->id, 'data_type' => 'bank_account_add', 'description' => 'Thêm tài khoản ngân hàng OCB API', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);
        } else {
            $bankAccountId = $bankAccountDetails->id;
        }

        $bankSubAccountConfig = GetCompanyConfigurationAction::run($this->company_details->id, 'BankSubAccount');
        
        if (is_object($bankSubAccountConfig) && $bankSubAccountConfig->value == 'off') {
            SetCompanyConfigurationAction::run($this->company_details->id, 'BankSubAccount', 'on');
        }

        $vaOrderLastNumber = GetCompanyConfigurationAction::run($this->company_details->id, 'OCBVaOrderLastNumber');
        $vaOrder = is_object($vaOrderLastNumber) ? $vaOrderLastNumber->value + 1 : 1;

        $safeCreateBankSubAccountData = [
            'bank_account_id' => $bankAccountId,
            'sub_account' => $registerMerchantStep2ResponseData['data']['merchantInfo']['vaAccountNumber'], 
            'va_order' => $vaOrder, 
            'acc_type' => 'Real', 
            'sub_holder_name' => $accountHolderName,
            'label' => $data['label']
        ];

        $bankSubAccountId = $bankSubAccountModel->insert($safeCreateBankSubAccountData);

        if (!$bankSubAccountId) {
            log_message('error', '[OcbController->ajax_confirm_create_qr_shop_with_new_bank_account]: Create sub bank account failed - ' . json_encode($safeCreateBankSubAccountData));
            return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng thử lại sau.']);
        }

        SetCompanyConfigurationAction::run($this->company_details->id, 'OCBVaOrderLastNumber', $vaOrder);

        $safeBankShopLinkData = [
            'shop_id' => $shopDetails->id,
            'bank_account_id' => $bankAccountId,
            'bank_sub_account_id' => $bankSubAccountId,
        ];

        $linked = $bankShopLinkModel->insert($safeBankShopLinkData);

        if (!$linked) {
            log_message('error', '[OcbController->ajax_confirm_create_qr_shop_with_new_bank_account]: Link bank shop failed - ' . json_encode($safeBankShopLinkData));
            return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng thử lại sau.']);
        }

        $this->handleVaShop($shopDetails->id, $bankAccountId, $bankSubAccountId);

        return $this->respond([
            'status' => true,
            'va_id' => $bankSubAccountId,
            'message' => 'Tạo QR nhận thanh toán thành công',
        ]);
    }

    protected function handleVaShop($shopId, $bankAccountId, $bankSubAccountId)
    {
        $shopModel = model(ShopModel::class);

        $checkActive = $shopModel
        ->where([
            'id' => $shopId,
            'company_id' => $this->company_details->id,
            'active' => 0
            ])
        ->first();

        if($checkActive)
            $shopModel->update($shopId, ['active' => 1]);

        $noticationTelegramModel = model(NotificationTelegramModel::class);
        $telegrams = $noticationTelegramModel
        ->select([
            'tb_autopay_notification_telegram.*',
        ])
        ->join("tb_autopay_bank_shop_link", "tb_autopay_bank_shop_link.bank_account_id = tb_autopay_notification_telegram.bank_account_id AND tb_autopay_bank_shop_link.bank_sub_account_id = tb_autopay_notification_telegram.sub_account_id")
        ->join("tb_autopay_shop", "tb_autopay_shop.id = tb_autopay_bank_shop_link.shop_id")
        ->where("tb_autopay_bank_shop_link.shop_id", $shopId)
        ->where('tb_autopay_shop.company_id', $this->user_session['company_id'])
        ->groupBy('tb_autopay_notification_telegram.chat_id')->get()->getResult();

        if(!empty($telegrams)){
            foreach($telegrams as $t)
            {
                $data = [
                    'company_id' => $t->company_id ?? $this->user_session['company_id'],
                    'transaction_type' => 'In_only',
                    'bank_account_id' => $bankAccountId,
                    'sub_account_id' => $bankSubAccountId,
                    'contains_content' => $t->contains_content ?? '',
                    'description' => $t->description ?? '',
                    'chat_id' => $t->chat_id,
                    'amount_in_less_than_equal_to' => $t->amount_in_less_than_equal_to ?? 0,
                    'amount_in_great_than_equal_to' => $t->amount_in_great_than_equal_to ?? 0,
                    'ignore_phrases' => $t->ignore_phrases ?? '',
                    'active' => $t->active ?? 1,
                    'message_thread_id' => $t->message_thread_id ?? 0,
                    'verify_payment' => $t->verify_payment ?? 'No',
                    'is_template_custom' => $t->is_template_custom ?? 0,
                    'template_custom' => $t->template_custom ?? '',
                    'template_name' => $t->template_name ?? 'template_1',
                    'hide_accumulated' => $t->hide_accumulated ?? 0,
                    'hide_details_link' => $t->hide_details_link ?? 0
                ];
                $noticationTelegramModel->insert($data);
            }
        }

        $noticationLarkMessengerModel = model(NotificationLarkMessengerModel::class);
        $larkmessenger = $noticationLarkMessengerModel
        ->select([
                'tb_autopay_notification_larkmessenger.*'
        ])
        ->join("tb_autopay_bank_shop_link", "tb_autopay_bank_shop_link.bank_account_id = tb_autopay_notification_larkmessenger.bank_account_id AND tb_autopay_bank_shop_link.bank_sub_account_id = tb_autopay_notification_larkmessenger.sub_account_id")
        ->join("tb_autopay_shop", "tb_autopay_shop.id = tb_autopay_bank_shop_link.shop_id")
        ->where("tb_autopay_bank_shop_link.shop_id", $shopId)
        ->where('tb_autopay_shop.company_id', $this->user_session['company_id'])
        ->groupBy('tb_autopay_notification_larkmessenger.bot_webhook_url')->get()->getResult();

        if(!empty($larkmessenger)){
            foreach($larkmessenger as $l)
            {
                $data = [
                    'company_id' => $l->company_id ?? $this->user_session['company_id'],
                    'transaction_type' => 'In_only',
                    'bank_account_id' => $bankAccountId,
                    'sub_account_id' => $bankSubAccountId,
                    'contains_content' => $l->contains_content ?? '',
                    'description' => $l->description ?? '',
                    'bot_webhook_url' => $l->bot_webhook_url,
                    'amount_in_less_than_equal_to' => $l->amount_in_less_than_equal_to ?? 0,
                    'amount_in_great_than_equal_to' => $l->amount_in_great_than_equal_to ?? 0,
                    'ignore_phrases' => $l->ignore_phrases ?? '',
                    'active' => $l->active ?? 1,
                    'message_thread_id' => $l->message_thread_id ?? 0,
                    'verify_payment' => $l->verify_payment ?? 'No',
                    'is_template_custom' => $l->is_template_custom ?? 0,
                    'template_custom' => $l->template_custom ?? '',
                    'template_name' => $l->template_name ?? 'template_1',
                    'hide_accumulated' => $l->hide_accumulated ?? 0,
                    'hide_details_link' => $l->hide_details_link ?? 0
                ];
                $noticationLarkMessengerModel->insert($data);
            }
        }

        $userPermissionBankSubModel = model(UserPermissionBankSubModel::class);
        $userIdList = $userPermissionBankSubModel
        ->select([
                'tb_autopay_user_permission_bank_sub.user_id',
            ])
        ->join('tb_autopay_user', 'tb_autopay_user_permission_bank_sub.user_id = tb_autopay_user.id')
        ->join('tb_autopay_bank_shop_link', 'tb_autopay_bank_shop_link.bank_sub_account_id = tb_autopay_user_permission_bank_sub.sub_account_id AND tb_autopay_bank_shop_link.shop_id = ' . $shopId)
        ->join('tb_autopay_shop', 'tb_autopay_shop.id = tb_autopay_bank_shop_link.shop_id')
        ->where('tb_autopay_shop.company_id', $this->user_session['company_id'])
        ->where('tb_autopay_bank_shop_link.shop_id', $shopId)
        ->groupBy('tb_autopay_user_permission_bank_sub.user_id')->get()->getResult();

        if(!empty($userIdList)){
            foreach($userIdList as $u)
            {
                $userData = [
                    'permissions' => [
                        'hide_amount_out' => 1,
                        'hide_accumulated' => 1,
                        'hide_reference_number' => 1,
                        'hide_transaction_content' => 1,
                    ]
                ];

                $this->assignPosPermissionToUser(
                    $u->user_id, 
                    $this->user_session['company_id'], 
                    $bankAccountId, 
                    $bankSubAccountId, 
                    $userData['permissions'], 
                );
            }
        }
    }
    protected function assignPosPermissionToUser($userId, $companyId, $bankAccountId, $bankSubAccountId, $bankPermissions = [], $pushMobileTransactionNotificationFeature = 1)
    {
        $bankPermissions['hide_amount_out'] = isset($bankPermissions['hide_amount_out']) ? $bankPermissions['hide_amount_out'] : 0;
        $bankPermissions['hide_accumulated'] = isset($bankPermissions['hide_accumulated']) ? $bankPermissions['hide_accumulated'] : 0;
        $bankPermissions['hide_reference_number'] = isset($bankPermissions['hide_reference_number']) ? $bankPermissions['hide_reference_number'] : 0;
        $bankPermissions['hide_transaction_content'] = isset($bankPermissions['hide_transaction_content']) ? $bankPermissions['hide_transaction_content'] : 0;
        
        $userPermissionFeatureModel = model(UserPermissionFeatureModel::class);
        $userPermissionBankModel = model(UserPermissionBankModel::class);
        $userPermissionBankSubModel = model(UserPermissionBankSubModel::class);

        $transactionFeaturePermission = $userPermissionFeatureModel->where(['feature_slug' => 'Transactions', 'user_id' => $userId])->first();
        $pushMobileTransactionNotificationFeaturePermission = $userPermissionFeatureModel->where(['feature_slug' => 'PushMobileTransactionNotification', 'user_id' => $userId])->first();

        if (! $transactionFeaturePermission) {
            $userPermissionFeatureModel->insert([
                'user_id' => $userId,
                'company_id' => $companyId,
                'feature_slug' => 'Transactions',
                'can_view_all' => 1,
            ]);
        } else if ($transactionFeaturePermission && $transactionFeaturePermission->can_view_all == 0) {
            $userPermissionFeatureModel
                ->where(['id' => $transactionFeaturePermission->id])
                ->set(['can_view_all' => 1])
                ->update();
        }

        if ($pushMobileTransactionNotificationFeature && ! $pushMobileTransactionNotificationFeaturePermission) {
            $userPermissionFeatureModel->insert([
                'user_id' => $userId,
                'feature_slug' => 'PushMobileTransactionNotification',
                'can_view_all' => 1,
            ]);
        } else if ($pushMobileTransactionNotificationFeature && $pushMobileTransactionNotificationFeaturePermission && $pushMobileTransactionNotificationFeaturePermission->can_view_all == 0) {
            $userPermissionFeatureModel
                ->where(['id' => $pushMobileTransactionNotificationFeaturePermission->id])
                ->set(['can_view_all' => 1])
                ->update();
        } else if (!$pushMobileTransactionNotificationFeature && $pushMobileTransactionNotificationFeaturePermission) {
            $userPermissionFeatureModel
                ->where(['id' => $pushMobileTransactionNotificationFeaturePermission->id])
                ->set(['can_view_all' => 0])
                ->update();
        }

        $bankAccountPermission = $userPermissionBankModel->where(['user_id' => $userId, 'bank_account_id' => $bankAccountId])->first();
        $bankSubAccountPermission = $userPermissionBankSubModel->where(['user_id' => $userId, 'sub_account_id' => $bankSubAccountId])->first();

        if (! $bankAccountPermission) {
            $userPermissionBankModel->insert(array_merge($bankPermissions, [
                'user_id' => $userId,
                'bank_account_id' => $bankAccountId
            ]));
        } else if ($bankAccountPermission) {
            $userPermissionBankModel->where(['id' => $bankAccountPermission->id])
                ->set($bankPermissions)->update();
        }

        if (! $bankSubAccountPermission) {
            $userPermissionBankSubModel->insert([
                'user_id' => $userId,
                'sub_account_id' => $bankSubAccountId
            ]);
        }

        model(UserPermissionFeatureModel::class)->set(['can_view_all' => 1])
            ->where(['user_id' => $userId, 'company_id' => $companyId])
            ->whereIn('feature_slug', ['Transactions', 'BankAccount'])
            ->update();

        return true;
    }
}

