<?php

namespace App\Controllers;

use App\Models\NotificationTelegramQueueModel;
use CodeIgniter\HTTP\Response;
use Exception;
use CodeIgniter\API\ResponseTrait;
use App\Controllers\BaseController;
use App\Features\BankAccount\Contexts\Interfaces\HasDetermineBankAccountExists;

class BankBox extends BaseController
{
    use ResponseTrait;

    public function ajax_authorize_bank_account_connect(string $brandName = ''): Response
    {
        if ($this->request->getMethod(true) !== 'POST') {
            return $this->failNotFound();
        }

        if (! has_permission('BankAccount', 'can_add')) {
            return $this->respond([
                'status' => false,
                'message' => 'Bạn không có quyền liên kết tài khoản ngân hàng mới.',
            ]);
        }

        $brandName = trim(xss_clean($brandName));
        $type = trim(xss_clean($this->request->getVar('type')));
        $type = $type === 'enterprise' ? 'enterprise' : 'individual';
        $outputDeviceId = $this->request->getVar('output_device_id') ? (int) $this->request->getVar('output_device_id') : null;

        $bankFeature = service('bankFeature');
        $bankAccountFeature = service('bankAccountFeature');

        $bankDetails = $bankFeature->bankFilter()->onlyApiSupport()->findByBrandName($brandName);

        if (!$bankDetails) {
            return $this->failNotFound('Không tìm thấy ngân hàng.');
        }

        $bankAccountFeature->withBankAccountContext($bankDetails->id);
        $bankAccountFeature->withCompanyContext($this->user_session['company_id']);

        try {
            $bankAccountFeature->withBankAccountConnectContext($bankDetails->brand_name, $type);
        } catch (\Exception $e) {
            return $this->respond(['status' => false, 'message' => 'Ngân hàng chưa hỗ trợ liên kết loại tài khoản này.']);
        }
        
        $bankAccountConnectContext = $bankAccountFeature->bankAccountConnectContext();

        try {
            $authorized = $bankAccountConnectContext->authorize();
            $bankAccountConnectContext->initSession($outputDeviceId);
        } catch (\Exception $e) {
            log_message('error', '[BankBox] ajax_authorize_bank_account_connect: ' . $e->getMessage() . ' - ' . $e->getTraceAsString());
            $message = $bankAccountConnectContext->handleBankClientException($e);
            return $this->respond(['status' => false, 'message' => $message]);
        }

        $outputDevice = $bankAccountConnectContext->getOutputDevice();

        if ($outputDevice && $outputDevice->bank_id == 19 && $brandName !== 'ABBANK') {
            return $this->respond([
                'status' => false,
                'message' => 'Loa thanh toán chỉ hỗ trợ liên kết tài khoản ngân hàng ABBANK.'
            ]);
        }

        return $this->respond([
            'status' => $authorized, 
            'webview' => $bankAccountConnectContext->webviewUrl,
            'schema' => $bankAccountConnectContext->schema()->toArray()
        ]);
    }

    public function ajax_lookup_account_holder_name(string $brandName = ''): Response
    {
        if ($this->request->getMethod(true) !== 'POST') {
            return $this->failNotFound();
        }

        if (! has_permission('BankAccount', 'can_add')) {
            return $this->respond([
                'status' => false,
                'message' => 'Bạn không có quyền liên kết tài khoản ngân hàng mới.',
            ]);
        }

        $brandName = trim(xss_clean($brandName));
        $type = trim(xss_clean($this->request->getVar('type')));
        $type = $type === 'enterprise' ? 'enterprise' : 'individual';

        $bankFeature = service('bankFeature');
        $bankAccountFeature = service('bankAccountFeature');

        $bankDetails = $bankFeature->bankFilter()->onlyApiSupport()->findByBrandName($brandName);

        if (!$bankDetails) {
            return $this->failNotFound('Không tìm thấy ngân hàng.');
        }

        $inputData = [
            'account_number' => trim(xss_clean($this->request->getVar('account_number'))),
        ];

        $rules = [
            'account_number' => ['required', 'max_length[20]'],
        ];

        if (! $this->validateData($inputData, $rules)) {
            return $this->fail($this->validator->getErrors());
        }

        try {
            $bankAccountFeature->withBankAccountConnectContext($bankDetails->brand_name, $type);
        } catch (\Exception $e) {
            return $this->respond(['status' => false, 'message' => 'Ngân hàng chưa hỗ trợ liên kết loại tài khoản này.']);
        }

        if (! $bankAccountFeature->bankAccountConnectContext()->hasLookupAccountHolderNameSupport()) {
            return $this->respond(['status' => false, 'message' => 'Ngân hàng chưa hỗ trợ tra cứu tên người thụ hưởng']);
        }

        try {
            $accountHolderName = $bankAccountFeature->bankAccountConnectContext()->lookupAccountHolderName($inputData['account_number']);
        } catch (\Exception $e) {
            log_message('error', '[BankBox] ajax_lookup_account_holder_name: ' . $e->getMessage() . ' - ' . $e->getTraceAsString());
            $message = $bankAccountFeature->bankAccountConnectContext()->handleBankClientException($e);
            return $this->respond(['status' => false, 'message' => $message]);
        }

        if (!$accountHolderName) {
            return $this->fail(['account_number' => 'Số tài khoản không tồn tại trên hệ thống ngân hàng ' . $bankDetails->brand_name]);
        }

        return $this->respond([
            'status' => true,
            'account_holder_name' => $accountHolderName,
        ]);
    }

    public function ajax_request_bank_account_connect(string $brandName = ''): Response
    {
        if ($this->request->getMethod(true) !== 'POST') {
            return $this->failNotFound();
        }

        if (! has_permission('BankAccount', 'can_add')) {
            return $this->respond([
                'status' => false,
                'message' => 'Bạn không có quyền liên kết tài khoản ngân hàng mới.',
            ]);
        }

        $brandName = trim(xss_clean($brandName));
        $type = trim(xss_clean($this->request->getVar('type')));
        $type = $type === 'enterprise' ? 'enterprise' : 'individual';

        $bankFeature = service('bankFeature');

        /** @var \App\Feature\BankAccount\BankAccountFeature $bankAccountFeature */
        $bankAccountFeature = service('bankAccountFeature');
        $bankAccountFeature->withCompanyContext($this->user_session['company_id']);

        $bankDetails = $bankFeature->bankFilter()->onlyApiSupport()->findByBrandName($brandName);

        if (!$bankDetails) {
            return $this->failNotFound('Không tìm thấy ngân hàng.');
        }

        $bankAccountFeature->withBankAccountContext($bankDetails->id);

        try {
            $bankAccountFeature->withBankAccountConnectContext($bankDetails->brand_name, $type);
        } catch (\Exception $e) {
            return $this->respond(['status' => false, 'message' => 'Ngân hàng chưa hỗ trợ liên kết loại tài khoản này.']);
        }

        $outputDevice = $bankAccountFeature->bankAccountConnectContext()->getOutputDevice();

        if ($outputDevice->bank_id == 19 && $brandName !== 'ABBANK') {
            return $this->respond([
                'status' => false,
                'message' => 'Loa thanh toán chỉ hỗ trợ liên kết tài khoản ngân hàng ABBANK.'
            ]);
        }

        $bankAccountConnectContext = $bankAccountFeature->bankAccountConnectContext();
        $bankAccountConnectSchema = $bankAccountConnectContext->schema();

        if (count($bankAccountConnectSchema->fieldRules()) > 0) {
            $inputData = [];

            foreach (array_merge($bankAccountConnectSchema->fields, $bankAccountConnectSchema->additionalFields) as $name => $fieldOption) {
                if (!isset($fieldOption['enabled']) || !$fieldOption['enabled']) continue;

                $inputData[$name] = trim(xss_clean($this->request->getVar($name)));
                $bankAccountConnectContext->setField($name, $inputData[$name]);
            }

            $rules = $bankAccountConnectSchema->fieldRules();

            if (! $this->validateData($inputData, $rules)) {
                return $this->fail($this->validator->getErrors());
            }

            if ($bankAccountConnectContext instanceof HasDetermineBankAccountExists) {
                if ($bankAccountConnectContext->determineIfBankAccountExistsByAccountNumber()) {
                    return $this->fail(['account_number' => 'Số tài khoản này đã tồn tại trên hệ thống SePay']);
                }
            } else {
                if ($bankAccountFeature->bankAccountContext()->determineIfBankAccountExistsByAccountNumber($inputData['account_number'])) {
                    return $this->fail(['account_number' => 'Số tài khoản này đã tồn tại trên hệ thống SePay']);
                }
            }
        }

        $bankAccountConnectContext->loadOutputDeviceDecalIfPresent();

        try {
            $result = $bankAccountConnectContext->requestConnect();

            if (
                $result === $bankAccountConnectContext::SUCCESS_CODE
                && !$bankAccountConnectContext->verifiedOtpRequestConnect
            ) {
                return $this->respond([
                    'status' => true,
                    'otp' => (bool) $bankAccountConnectContext->otpRequestId,
                    'webview' => $bankAccountConnectContext->webviewUrl,
                    'context' => $bankAccountConnectContext->getSession(),
                    'schema' => $bankAccountConnectContext->schema()->toArray()
                ]);
            }

            if (
                $result === $bankAccountConnectContext::SUCCESS_CODE
                && $bankAccountConnectContext->verifiedOtpRequestConnect
            ) {
                $result = $bankAccountConnectContext->confirmConnect();

                if ($result && $bankAccountConnectContext->isOkResponse()) {
                    return $this->respond([
                        'status' => true,
                        'id' => $result,
                        'context' => $bankAccountConnectContext->getSession()
                    ]);
                }
            }

            if ($bankAccountConnectContext->responseCode == $bankAccountConnectContext::UNEXPECTED_ERROR_CODE) {
                return $this->respond($bankAccountConnectContext->responseData);
            }

            return $this->fail($bankAccountConnectContext->responseData);
        } catch (Exception $e) {
            log_message('error', '[BankBox] ajax_request_bank_account_connect: ' . $e->getMessage() . ' - ' . $e->getTraceAsString());

            $message = $bankAccountFeature->bankAccountConnectContext()->handleBankClientException($e);
            return $this->respond(['status' => false, 'message' => $message]);
        }
    }

    public function ajax_retry_request_bank_account_connect(string $brandName = ''): Response
    {
        if ($this->request->getMethod(true) !== 'POST') {
            return $this->failNotFound();
        }

        if (! has_permission('BankAccount', 'can_add')) {
            return $this->respond([
                'status' => false,
                'message' => 'Bạn không có quyền liên kết tài khoản ngân hàng mới.',
            ]);
        }

        $brandName = trim(xss_clean($brandName));
        $bankFeature = service('bankFeature');

        /** @var \App\Feature\BankAccount\BankAccountFeature $bankAccountFeature */
        $bankAccountFeature = service('bankAccountFeature');
        $bankAccountFeature->withCompanyContext($this->user_session['company_id']);

        $bankDetails = $bankFeature->bankFilter()->onlyApiSupport()->findByBrandName($brandName);

        if (!$bankDetails) {
            return $this->failNotFound('Không tìm thấy ngân hàng.');
        }

        $bankAccountFeature->withBankAccountContext($bankDetails->id);

        try {
            $bankAccountFeature->withBankAccountConnectContext($bankDetails->brand_name);
        } catch (\Exception $e) {
            return $this->respond(['status' => false, 'message' => 'Ngân hàng chưa hỗ trợ liên kết loại tài khoản này.']);
        }

        $bankAccountConnectContext = $bankAccountFeature->bankAccountConnectContext();
        $bankAccountConnectContext->loadFromSession();

        if (! $bankAccountConnectContext->canRetryRequestConnect()) {
            return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng tại lại trang.']);
        }

        try {
            $result = $bankAccountConnectContext->requestConnect();

            if (
                $result === $bankAccountConnectContext::SUCCESS_CODE
                && !$bankAccountConnectContext->verifiedOtpRequestConnect
            ) {
                return $this->respond([
                    'status' => true,
                    'otp' => (bool) $bankAccountConnectContext->otpRequestId,
                    'webview' => $bankAccountConnectContext->webviewUrl,
                    'context' => $bankAccountConnectContext->getSession()
                ]);
            }

            if (
                $result === $bankAccountConnectContext::SUCCESS_CODE
                && $bankAccountConnectContext->verifiedOtpRequestConnect
            ) {
                $result = $bankAccountConnectContext->confirmConnect();

                if ($result && $bankAccountConnectContext->isOkResponse()) {
                    return $this->respond([
                        'status' => true,
                        'id' => $result,
                        'context' => $bankAccountConnectContext->getSession()
                    ]);
                }
            }

            return $this->fail(
                $bankAccountConnectContext->responseData,
                $bankAccountConnectContext->responseCode
            );
        } catch (\Exception $e) {
            log_message('error', '[BankBox] ajax_retry_request_bank_account_connect: ' . $e->getMessage() . ' - ' . $e->getTraceAsString());

            $message = $bankAccountFeature->bankAccountConnectContext()->handleBankClientException($e);
            return $this->respond(['status' => false, 'message' => $message]);
        }
    }

    public function ajax_confirm_bank_account_connect(string $brandName = ''): Response
    {
        if ($this->request->getMethod(true) !== 'POST') {
            return $this->failNotFound();
        }

        if (! has_permission('BankAccount', 'can_add')) {
            return $this->respond([
                'status' => false,
                'message' => 'Bạn không có quyền liên kết tài khoản ngân hàng mới.',
            ]);
        }

        $brandName = trim(xss_clean($brandName));

        $bankFeature = service('bankFeature');

        /** @var \App\Feature\BankAccount\BankAccountFeature $bankAccountFeature */
        $bankAccountFeature = service('bankAccountFeature');
        $bankAccountFeature->withCompanyContext($this->user_session['company_id']);

        $bankDetails = $bankFeature->bankFilter()->onlyApiSupport()->findByBrandName($brandName);

        if (!$bankDetails) {
            return $this->failNotFound('Không tìm thấy ngân hàng.');
        }

        $bankAccountFeature->withBankAccountContext($bankDetails->id);

        try {
            $bankAccountFeature->withBankAccountConnectContext($bankDetails->brand_name);
        } catch (\Exception $e) {
            return $this->respond(['status' => false, 'message' => 'Ngân hàng chưa hỗ trợ liên kết loại tài khoản này.']);
        }

        $bankAccountConnectContext = $bankAccountFeature->bankAccountConnectContext();
        $bankAccountConnectContext->loadFromSession();

        if (!$bankAccountConnectContext->canConfirmRequest()) {
            return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng tải lại trang.']);
        }

        $bankAccountConnectSchema = $bankAccountConnectContext->schema();

        if ($bankAccountConnectSchema->otp['support']) {
            $otp = trim(xss_clean($this->request->getVar('otp')));

            if (! $otp) {
                return $this->fail(['otp' => 'Mã OTP không được để trống']);
            }

            $bankAccountConnectContext->otp = $otp;
        }

        try {
            $result = $bankAccountConnectContext->confirmConnect();

            if ($bankAccountConnectSchema->confirmConnectType === 'callback') {
                return $this->respond(array_filter([
                    'id' => $bankAccountConnectContext->isOkResponse() ? $result : null,
                    'status' => $bankAccountConnectContext->isOkResponse(),
                    'context' => $bankAccountConnectContext->getSession(),
                ], fn($value) => $value !== null));
            }

            if ($result && $bankAccountConnectContext->isOkResponse()) {
                return $this->respond([
                    'status' => true,
                    'id' => $result,
                    'context' => $bankAccountConnectContext->getSession(),
                ]);
            }

            if ($bankAccountConnectContext->responseCode == $bankAccountConnectContext::UNEXPECTED_ERROR_CODE) {
                return $this->respond($bankAccountConnectContext->responseData);
            }

            return $this->fail($bankAccountConnectContext->responseData);
        } catch (\Exception $e) {
            log_message('error', '[BankBox] ajax_confirm_bank_account_connect: ' . $e->getMessage() . ' - ' . $e->getTraceAsString());

            $message = $bankAccountConnectContext->handleBankClientException($e);
            return $this->respond(['status' => false, 'message' => $message]);
        }
    }
    
    public function ajax_check_confirm_bank_account_connect(string $brandName = ''): Response
    {
        if ($this->request->getMethod(true) !== 'POST') {
            return $this->failNotFound();
        }

        if (! has_permission('BankAccount', 'can_add')) {
            return $this->respond([
                'status' => false,
                'message' => 'Bạn không có quyền liên kết tài khoản ngân hàng mới.',
            ]);
        }

        $brandName = trim(xss_clean($brandName));

        $bankFeature = service('bankFeature');

        /** @var \App\Feature\BankAccount\BankAccountFeature $bankAccountFeature */
        $bankAccountFeature = service('bankAccountFeature');
        $bankAccountFeature->withCompanyContext($this->user_session['company_id']);

        $bankDetails = $bankFeature->bankFilter()->onlyApiSupport()->findByBrandName($brandName);

        if (!$bankDetails) {
            return $this->failNotFound('Không tìm thấy ngân hàng.');
        }

        $bankAccountFeature->withBankAccountContext($bankDetails->id);

        try {
            $bankAccountFeature->withBankAccountConnectContext($bankDetails->brand_name);
        } catch (\Exception $e) {
            return $this->respond(['status' => false, 'message' => 'Ngân hàng chưa hỗ trợ liên kết loại tài khoản này.']);
        }

        $bankAccountConnectContext = $bankAccountFeature->bankAccountConnectContext();
        $bankAccountConnectContext->loadFromSession();

        if (!$bankAccountConnectContext->canConfirmRequest()) {
            return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng tải lại trang.']);
        }

        try {
            return $this->respond(array_filter([
                'id' => $bankAccountConnectContext->bankAccountId,
                'va_id' => $bankAccountConnectContext->firstVaId,
                'status' => $bankAccountConnectContext->isConfirmedConnect(),
                'context' => $bankAccountConnectContext->getSession(),
            ], fn($value) => $value !== null));
        } catch (\Exception $e) {
            return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng tải lại trang.']);
        }
    }

    public function ajax_send_request_enterprise_bank_account_connect(string $brandName = ''): Response
    {
        if ($this->request->getMethod(true) !== 'POST') {
            return $this->failNotFound();
        }

        if (! has_permission('BankAccount', 'can_add')) {
            return $this->respond([
                'status' => false,
                'message' => 'Bạn không có quyền liên kết tài khoản ngân hàng mới.',
            ]);
        }

        $brandName = trim(xss_clean($brandName));
        $type = trim(xss_clean($this->request->getVar('type')));

        $bankFeature = service('bankFeature');

        /** @var \App\Feature\BankAccount\BankAccountFeature $bankAccountFeature */
        $bankAccountFeature = service('bankAccountFeature');
        $bankAccountFeature->withCompanyContext($this->user_session['company_id']);

        $bankDetails = $bankFeature->bankFilter()->onlyApiSupport()->findByBrandName($brandName);

        if (!$bankDetails) {
            return $this->failNotFound('Không tìm thấy ngân hàng.');
        }

        $bankAccountFeature->withBankAccountContext($bankDetails->id);

        try {
            $bankAccountFeature->withBankAccountConnectContext($bankDetails->brand_name);
        } catch (\Exception $e) {
            return $this->respond(['status' => false, 'message' => 'Ngân hàng chưa hỗ trợ liên kết loại tài khoản này.']);
        }

        if (! $this->validate([
            'company_name' => 'required|min_length[5]|max_length[100]',
            'has_account' => 'required',
            'phone_number' => 'required|min_length[10]|max_length[20]',
            'notes' => 'permit_empty',
            'account_number' => [
                'rules' => $this->request->getVar('has_account') == 1 ? 'required|max_length[50]' : 'permit_empty|max_length[50]',
            ],
            'branch_name' => [
                'rules' => $this->request->getVar('has_account') == 1 ? 'required|max_length[100]' : 'permit_empty|max_length[100]',
            ],
        ])) return $this->fail($this->validator->getErrors());

        $data = [
            'company_name' => trim($this->request->getVar('company_name')),
            'phone_number' => trim($this->request->getVar('phone_number')),
            'has_account' => $this->request->getVar('has_account'),
            'account_number' => trim($this->request->getVar('account_number')),
            'branch_name' => trim($this->request->getVar('branch_name')),
            'notes' => trim($this->request->getVar('notes'))
        ];

        $message = '
------------------------------

Có yêu cầu kết nối API ' . $brandName . ' doanh nghiệp mới:

#️⃣ Tên cá nhân/tổ chức: ' . $data['company_name'] . '

ℹ️ Đã có tài khoản ' . $brandName . ': ' . ($data['has_account'] ? 'Đã có' : 'Chưa') . '

📞 Số điện thoại liên hệ: ' . $data['phone_number'] . '

' . ($data['has_account'] ? '🏦 Số tài khoản ' . $brandName . ': ' . $data['account_number'] . '

📍 Chi nhánh: ' . $data['branch_name'] . '

' : '') . '⏰ Thực hiện lúc: ' . date("Y-m-d H:i:s") . '

✍🏻 Ghi chú: ' . $data['notes'] . '

------------------------------
        ';

        $telegramQueueModel = model(NotificationTelegramQueueModel::class);
        $telegramQueueModel->insert([
            'chat_id' => config(\Config\BankAccount::class)->telegramChatId,
            'status' => 'Pending',
            'message' => $message
        ]);

        return $this->respond([
            'status' => true,
            'message' => 'SePay đã nhận được yêu cầu của bạn và sẽ liên hệ lại trong thời gian sớm nhất.',
        ]);
    }
    
    public function ajax_request_create_official_va(string $brandName = ''): Response
    {
        if ($this->request->getMethod(true) !== 'POST') {
            return $this->failNotFound();
        }

        if (! has_permission('BankAccount', 'can_add')) {
            return $this->respond([
                'status' => false,
                'message' => 'Bạn không có quyền liên kết tài khoản ngân hàng mới.',
            ]);
        }

        $brandName = trim(xss_clean($brandName));
        $type = trim(xss_clean($this->request->getVar('type')));
        $type = $type === 'enterprise' ? 'enterprise' : 'individual';

        $bankFeature = service('bankFeature');

        /** @var \App\Feature\BankAccount\BankAccountFeature $bankAccountFeature */
        $bankAccountFeature = service('bankAccountFeature');
        $bankAccountFeature->withCompanyContext($this->user_session['company_id']);

        $bankDetails = $bankFeature->bankFilter()->onlyApiSupport()->findByBrandName($brandName);

        if (!$bankDetails) {
            return $this->failNotFound('Không tìm thấy ngân hàng.');
        }

        $bankAccountFeature->withBankAccountContext($bankDetails->id);

        try {
            $bankAccountFeature->withBankAccountConnectContext($bankDetails->brand_name, $type);
        } catch (\Exception $e) {
            return $this->respond(['status' => false, 'message' => 'Ngân hàng chưa hỗ trợ liên kết loại tài khoản này.']);
        }

        $bankAccountConnectContext = $bankAccountFeature->bankAccountConnectContext();
        $bankAccountConnectContext->loadFromSession();
        
        if (!$bankAccountConnectContext->bankAccountId) {
            return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng tải lại trang.']);
        }

        $bankAccountConnectSchema = $bankAccountConnectContext->schema();

        $rules = $bankAccountConnectSchema->createVaOptionFieldRules();
        
        if (count($rules) > 0) {
            $inputData = [];

            foreach (array_merge($bankAccountConnectSchema->createVaOptions['fields']) as $name => $fieldOption) {
                if (!isset($fieldOption['enabled']) || !$fieldOption['enabled']) continue;

                $inputData[$name] = trim(xss_clean($this->request->getVar($name)));
                $bankAccountConnectContext->setField($name, $inputData[$name]);
            }

            if (! $this->validateData($inputData, $rules)) {
                return $this->fail($this->validator->getErrors());
            }
        }

        try {
            $result = $bankAccountConnectContext->requestCreateOfficialVa();

            if (
                $result === $bankAccountConnectContext::SUCCESS_CODE
                && !$bankAccountConnectContext->verifiedOtpRequestConnect
            ) {
                return $this->respond([
                    'status' => true,
                    'otp' => (bool) $bankAccountConnectContext->otpRequestId,
                    'webview' => $bankAccountConnectContext->webviewUrl,
                    'context' => $bankAccountConnectContext->getSession(),
                    'schema' => $bankAccountConnectContext->schema()->toArray()
                ]);
            }

            if (
                $result === $bankAccountConnectContext::SUCCESS_CODE
                && $bankAccountConnectContext->verifiedOtpRequestConnect
            ) {
                $result = $bankAccountConnectContext->confirmCreateOfficialVa();

                if ($result && $bankAccountConnectContext->isOkResponse()) {
                    return $this->respond([
                        'status' => true,
                        'id' => $bankAccountConnectContext->bankAccountId,
                        'context' => $bankAccountConnectContext->getSession(),
                        'va_id' => $result,
                    ]);
                }
            }

            if ($bankAccountConnectContext->responseCode == $bankAccountConnectContext::UNEXPECTED_ERROR_CODE) {
                return $this->respond($bankAccountConnectContext->responseData);
            }

            return $this->fail($bankAccountConnectContext->responseData);
        } catch (Exception $e) {
            log_message('error', '[BankBox] ajax_request_create_official_va: ' . $e->getMessage() . ' - ' . $e->getTraceAsString());

            $message = $bankAccountFeature->bankAccountConnectContext()->handleBankClientException($e);
            return $this->respond(['status' => false, 'message' => $message]);
        }
    }
    
    public function ajax_confirm_create_official_va(string $brandName = ''): Response
    {
        if ($this->request->getMethod(true) !== 'POST') {
            return $this->failNotFound();
        }

        if (! has_permission('BankAccount', 'can_add')) {
            return $this->respond([
                'status' => false,
                'message' => 'Bạn không có quyền liên kết tài khoản ngân hàng mới.',
            ]);
        }

        $brandName = trim(xss_clean($brandName));

        $bankFeature = service('bankFeature');

        /** @var \App\Feature\BankAccount\BankAccountFeature $bankAccountFeature */
        $bankAccountFeature = service('bankAccountFeature');
        $bankAccountFeature->withCompanyContext($this->user_session['company_id']);

        $bankDetails = $bankFeature->bankFilter()->onlyApiSupport()->findByBrandName($brandName);

        if (!$bankDetails) {
            return $this->failNotFound('Không tìm thấy ngân hàng.');
        }

        $bankAccountFeature->withBankAccountContext($bankDetails->id);

        try {
            $bankAccountFeature->withBankAccountConnectContext($bankDetails->brand_name);
        } catch (\Exception $e) {
            return $this->respond(['status' => false, 'message' => 'Ngân hàng chưa hỗ trợ liên kết loại tài khoản này.']);
        }

        $bankAccountConnectContext = $bankAccountFeature->bankAccountConnectContext();
        $bankAccountConnectContext->loadFromSession();

        if (!$bankAccountConnectContext->canConfirmRequest()) {
            return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng tải lại trang.']);
        }

        $bankAccountConnectSchema = $bankAccountConnectContext->schema();

        if ($bankAccountConnectSchema->createVaOption['support']) {
            $otp = trim(xss_clean($this->request->getVar('otp')));

            if (! $otp) {
                return $this->fail(['otp' => 'Mã OTP không được để trống']);
            }

            $bankAccountConnectContext->otp = $otp;
        }

        try {
            $result = $bankAccountConnectContext->confirmCreateOfficialVa();

            if ($result && $bankAccountConnectContext->isOkResponse()) {
                return $this->respond([
                    'status' => true,
                    'id' => $bankAccountConnectContext->bankAccountId,
                    'va_id' => $result,
                    'context' => $bankAccountConnectContext->getSession(),
                ]);
            }

            if ($bankAccountConnectContext->responseCode == $bankAccountConnectContext::UNEXPECTED_ERROR_CODE) {
                return $this->respond($bankAccountConnectContext->responseData);
            }

            return $this->fail($bankAccountConnectContext->responseData);
        } catch (\Exception $e) {
            log_message('error', '[BankBox] ajax_confirm_create_official_va: ' . $e->getMessage() . ' - ' . $e->getTraceAsString());

            $message = $bankAccountConnectContext->handleBankClientException($e);
            return $this->respond(['status' => false, 'message' => $message]);
        }
    }
    
    public function ajax_retry_request_create_official_va(string $brandName = ''): Response
    {
        if ($this->request->getMethod(true) !== 'POST') {
            return $this->failNotFound();
        }

        if (! has_permission('BankAccount', 'can_add')) {
            return $this->respond([
                'status' => false,
                'message' => 'Bạn không có quyền liên kết tài khoản ngân hàng mới.',
            ]);
        }

        $brandName = trim(xss_clean($brandName));
        $bankFeature = service('bankFeature');

        /** @var \App\Feature\BankAccount\BankAccountFeature $bankAccountFeature */
        $bankAccountFeature = service('bankAccountFeature');
        $bankAccountFeature->withCompanyContext($this->user_session['company_id']);

        $bankDetails = $bankFeature->bankFilter()->onlyApiSupport()->findByBrandName($brandName);

        if (!$bankDetails) {
            return $this->failNotFound('Không tìm thấy ngân hàng.');
        }

        $bankAccountFeature->withBankAccountContext($bankDetails->id);

        try {
            $bankAccountFeature->withBankAccountConnectContext($bankDetails->brand_name);
        } catch (\Exception $e) {
            return $this->respond(['status' => false, 'message' => 'Ngân hàng chưa hỗ trợ liên kết loại tài khoản này.']);
        }

        $bankAccountConnectContext = $bankAccountFeature->bankAccountConnectContext();
        $bankAccountConnectContext->loadFromSession();

        if (! $bankAccountConnectContext->canRetryRequestCreateOfficialVa()) {
            return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng tại lại trang.']);
        }

        try {
            $result = $bankAccountConnectContext->requestCreateOfficialVa();

            if (
                $result === $bankAccountConnectContext::SUCCESS_CODE
                && !$bankAccountConnectContext->verifiedOtpRequestConnect
            ) {
                return $this->respond([
                    'status' => true,
                    'otp' => (bool) $bankAccountConnectContext->otpRequestId,
                    'webview' => $bankAccountConnectContext->webviewUrl,
                    'context' => $bankAccountConnectContext->getSession()
                ]);
            }

            if (
                $result === $bankAccountConnectContext::SUCCESS_CODE
                && $bankAccountConnectContext->verifiedOtpRequestConnect
            ) {
                $result = $bankAccountConnectContext->confirmCreateOfficialVa();

                if ($result && $bankAccountConnectContext->isOkResponse()) {
                    return $this->respond([
                        'status' => true,
                        'id' => $result,
                        'context' => $bankAccountConnectContext->getSession()
                    ]);
                }
            }

            return $this->fail(
                $bankAccountConnectContext->responseData,
                $bankAccountConnectContext->responseCode
            );
        } catch (\Exception $e) {
            log_message('error', '[BankBox] ajax_retry_request_create_official_va: ' . $e->getMessage() . ' - ' . $e->getTraceAsString());

            $message = $bankAccountFeature->bankAccountConnectContext()->handleBankClientException($e);
            return $this->respond(['status' => false, 'message' => $message]);
        }
    }
    
    public function ajax_create_inhouse_va(string $brandName = ''): Response
    {
        if ($this->request->getMethod(true) !== 'POST') {
            return $this->failNotFound();
        }

        if (! has_permission('BankAccount', 'can_add')) {
            return $this->respond([
                'status' => false,
                'message' => 'Bạn không có quyền liên kết tài khoản ngân hàng mới.',
            ]);
        }

        $brandName = trim(xss_clean($brandName));
        $bankFeature = service('bankFeature');

        /** @var \App\Feature\BankAccount\BankAccountFeature $bankAccountFeature */
        $bankAccountFeature = service('bankAccountFeature');
        $bankAccountFeature->withCompanyContext($this->user_session['company_id']);

        $bankDetails = $bankFeature->bankFilter()->onlyApiSupport()->findByBrandName($brandName);

        if (!$bankDetails) {
            return $this->failNotFound('Không tìm thấy ngân hàng.');
        }

        $bankAccountFeature->withBankAccountContext($bankDetails->id);

        try {
            $bankAccountFeature->withBankAccountConnectContext($bankDetails->brand_name);
        } catch (\Exception $e) {
            return $this->respond(['status' => false, 'message' => 'Ngân hàng chưa hỗ trợ liên kết loại tài khoản này.']);
        }

        $bankAccountConnectContext = $bankAccountFeature->bankAccountConnectContext();
        $bankAccountConnectContext->loadFromSession();
     
        try {
            $result = $bankAccountConnectContext->createInhouseVa();

            if ($result === $bankAccountConnectContext::SUCCESS_CODE) {
                return $this->respond([
                    'status' => true,
                    'id' => $bankAccountConnectContext->bankAccountId,
                    'va_id' => $bankAccountConnectContext->firstVaId,
                    'context' => $bankAccountConnectContext->getSession(),
                ]);
            }

            return $this->fail(
                $bankAccountConnectContext->responseData,
                $bankAccountConnectContext->responseCode
            );
        } catch (\Exception $e) {
            log_message('error', '[BankBox] ajax_create_inhouse_va: ' . $e->getMessage() . ' - ' . $e->getTraceAsString());
            return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ.']);
        }
    }
}