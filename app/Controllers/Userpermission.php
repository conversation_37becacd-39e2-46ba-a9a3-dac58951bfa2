<?php

namespace App\Controllers;
use App\Models\BankAccountModel;
use App\Models\UserModel;
use App\Models\CompanyModel;
use App\Models\CompanyUserModel;
use App\Models\UserPermissionBankModel;
use App\Models\UserPermissionFeatureModel;
use App\Models\UserPermissionBankSubModel;
use App\Models\BankSubAccountModel;

use CodeIgniter\Controller;

class Userpermission extends BaseController
{
    public function permission($user_id = '')
    { 
        if(!is_numeric($user_id))
            show_404();

        // logged user is not an admin
        if(!in_array($this->company_details->role,['Admin','SuperAdmin']))
            return redirect()->to(base_url());

        $data = [
            'page_title' => 'Phân quyền',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'user_id' => $user_id
        ];


        $companyUserModel = model(CompanyUserModel::class);
        
        $result = $companyUserModel->where(['user_id' => $user_id,'company_id' => $this->user_session['company_id']])->get()->getRow();

        if(!is_object($result))
            show_404();

        if(in_array($result->role,['Admin','SuperAdmin']))
            return redirect()->to(base_url());

        $userModel = model(UserModel::class);      

        $data['user'] = $userModel->select('firstname, lastname, email')->where(['id' => $user_id])->get()->getRow();
    
        // permissions for bank accounts
        $bankAccountModel = model(BankAccountModel::class);      
        $data['bank_accounts'] = $bankAccountModel->get_bank_account_company($this->user_session['company_id']);

        $userPermissionBankModel = model(UserPermissionBankModel::class);
        $bank_permissions = $userPermissionBankModel->where(['user_id' => $user_id])->orderBy('bank_account_id', 'desc')->get()->getResult();

        $data['bank_permissions'] = [];
        foreach($bank_permissions as $bank_permission) {
            $data['bank_permissions'][$bank_permission->bank_account_id]['hide_amount_out'] = $bank_permission->hide_amount_out;
            $data['bank_permissions'][$bank_permission->bank_account_id]['hide_accumulated'] = $bank_permission->hide_accumulated;
            $data['bank_permissions'][$bank_permission->bank_account_id]['hide_reference_number'] = $bank_permission->hide_reference_number;
            $data['bank_permissions'][$bank_permission->bank_account_id]['hide_transaction_content'] = $bank_permission->hide_transaction_content;
        }

        // permissions for features
        $userPermissionFeatureModel = model(UserPermissionFeatureModel::class);
        $results = $userPermissionFeatureModel->where(['user_id' => $user_id, 'company_id' => $this->user_session['company_id']])->get()->getResult();
        $data['feature_permissions'] = [];
        foreach ($results as $result) {
            $data['feature_permissions'][$result->feature_slug]['can_view_all'] = $result->can_view_all;
            $data['feature_permissions'][$result->feature_slug]['can_add'] = $result->can_add;
            $data['feature_permissions'][$result->feature_slug]['can_edit'] = $result->can_edit;
            $data['feature_permissions'][$result->feature_slug]['can_delete'] = $result->can_delete;
        }

        // bank sub accounts
        $bankSubAccountModel = model(BankSubAccountModel::class);      
        $data['bank_sub_accounts'] = $bankSubAccountModel->get_bank_sub_account_company($this->user_session['company_id']);

        // permission for bank sub accounts
        $userPermissionBankSubModel = model(UserPermissionBankSubModel::class);

        $results = $userPermissionBankSubModel->get_permissions($user_id, $this->user_session['company_id']);
        $data['permission_sub_accounts'] = [];
        foreach($results as $result) {
            array_push($data['permission_sub_accounts'], $result->sub_account_id);
        }
 

        echo view('templates/autopay/header',$data);
        echo view('userpermission/user_v2',$data);
        echo view('templates/autopay/footer',$data);

    }

    public function ajax_account_permission_update() {
        if ($this->request->getMethod(true) != 'POST')
            return '';


        if(!in_array($this->company_details->role,['Admin','SuperAdmin']))
            return redirect()->to(base_url());
 
        $validation =  \Config\Services::validation();

        helper('text');
 
        if(! $this->validate([
            'assign_permission' => "required|in_list[1]",
            'user_id' => "required|integer|is_natural",
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        } 

        $user_id = $this->request->getPost('user_id');

        $companyUserModel = model(CompanyUserModel::class);

        $result = $companyUserModel->where(['user_id' => $user_id,'company_id' => $this->user_session['company_id']])->get()->getRow();

        if(!is_object($result))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>'Người dùng này không tồn tại'));

        if(in_array($result->role,['Admin','SuperAdmin']))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>'Bạn không cần phân quyền cho quản trị viên, bởi quản trị viên được toàn quyền'));

        $bankAccountModel = model(BankAccountModel::class);      
        $results = $bankAccountModel->get_bank_account_company($this->user_session['company_id']);
        $company_bank_accounts = [];
        foreach($results as $result) {
            array_push($company_bank_accounts, $result->id);
        }
     
       

        $userPermissionBankModel = model(UserPermissionBankModel::class);

        $permissions_subcheck = [];

        $bank_accounts_checked = $this->request->getPost('pacc');

        if(!is_array($bank_accounts_checked)) {
            $userPermissionBankModel->where(['user_id' => $user_id])->delete();
            return $this->response->setJSON(array('status'=>TRUE,'message'=>'Đã cập nhật'));
        }
             
        foreach($bank_accounts_checked as $bank_account_id) {
            if(in_array($bank_account_id, $company_bank_accounts)) {
                $permissions_subcheck[$bank_account_id]['hide_amount_out'] = 0;
                $permissions_subcheck[$bank_account_id]['hide_accumulated'] = 0;
                $permissions_subcheck[$bank_account_id]['hide_reference_number'] = 0;
                $permissions_subcheck[$bank_account_id]['hide_transaction_content'] = 0;
            }
          
        }

        
        foreach($this->request->getPost() as $key => $column) {

            if($key == 'user_id' || $key == 'assign_permission')
                continue;
 
            if(is_array($column) && $key != 'pacc') {
                foreach($column as $bank_account_id) {
                    if(in_array($bank_account_id, $company_bank_accounts)) { 
                        if(in_array($bank_account_id, $bank_accounts_checked))
                            $permissions_subcheck[$bank_account_id][$key] = 1;
                    }
                   
                } 
            }
            
        } 

        $userPermissionBankModel->where(['user_id' => $user_id])->delete();

        foreach($permissions_subcheck as $bank_account_id => $data_update) {
            $data_update['user_id'] = $user_id;
            $data_update['bank_account_id'] = $bank_account_id;

            $userPermissionBankModel->insert($data_update);
        }

        if (is_array($permissions_subcheck) && count($permissions_subcheck)) {
            model(UserPermissionFeatureModel::class)->set(['can_view_all' => 1])
                ->where(['user_id' => $user_id, 'company_id' => $this->user_session['company_id']])
                ->whereIn('feature_slug', ['Transactions', 'BankAccount', 'PushMobileTransactionNotification'])
                ->update();
        }

        add_user_log(array('data_id'=>$user_id,'company_id' => $this->user_session['company_id'], 'data_type'=>'user_bank_account_permission_update','description'=>'Cập nhật phân quyền truy cập tài khoản ngân hàng','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));

        return $this->response->setJSON(array("status"=>true));
        
    }

    public function ajax_feature_permission_update() {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!in_array($this->company_details->role,['Admin','SuperAdmin']))
            return redirect()->to(base_url());
 
        $validation =  \Config\Services::validation();

        helper('text');
 
        if(! $this->validate([
            'assign_permission_feature' => "required|in_list[1]",
            'user_id' => "required|integer|is_natural",
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        } 

        $user_id = $this->request->getPost('user_id');

        $companyUserModel = model(CompanyUserModel::class);

        $result = $companyUserModel->where(['user_id' => $user_id,'company_id' => $this->user_session['company_id']])->get()->getRow();

        if(!is_object($result))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>'Người dùng này không tồn tại'));

        if(in_array($result->role,['Admin','SuperAdmin']))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>'Bạn không cần phân quyền cho quản trị viên, bởi quản trị viên được toàn quyền'));

        $userPermissionFeatureModel = model(UserPermissionFeatureModel::class);      
      
        // reset permission
        $userPermissionFeatureModel->set(['can_view_all'=>0,'can_add'=>0,'can_edit'=>0,'can_delete'=>0])->where(['user_id' => $user_id, 'company_id' => $this->user_session['company_id']])->update();
 
        $permissions_subcheck = [];
        $features = $userPermissionFeatureModel->get_features();

        foreach($this->request->getPost() as $key => $column) {

            if($key == 'user_id' || $key == 'assign_permission_feature')
                continue;
 
                
            if(is_array($column) && in_array($key, ['can_view_all','can_add','can_edit','can_delete'])) {
                foreach($column as $feature_slug) {


                    $result = $userPermissionFeatureModel->where(['user_id' => $user_id, 'company_id' => $this->user_session['company_id'], 'feature_slug' => $feature_slug])->get()->getRow();
                    if(!is_object($result)) {
                        $userPermissionFeatureModel->insert(['user_id' => $user_id, 'company_id' => $this->user_session['company_id'], 'feature_slug' => $feature_slug, 'can_view_all' => 0,'can_add' => 0,'can_edit' => 0, 'can_delete'=>0]);
                    }

                    if(in_array($feature_slug, $features)) {
                        $userPermissionFeatureModel->set([$key => 1])->where(['user_id'=>$user_id, 'company_id' => $this->user_session['company_id'],'feature_slug' => $feature_slug])->update();
                    }

                    //$permissions_subcheck[$feature_slug][$key] = 1;
                 

                } 
            } 
            
        } 

        add_user_log(array('data_id'=>$user_id,'company_id' => $this->user_session['company_id'], 'data_type'=>'user_feature_permission_update','description'=>'Cập nhật phân quyền truy cập tính năng','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
      
        return $this->response->setJSON(array("status"=>true));
        
    }

    public function ajax_subaccount_permission_update() {
        if ($this->request->getMethod(true) != 'POST')
            return '';


        if(!in_array($this->company_details->role,['Admin','SuperAdmin']))
            return redirect()->to(base_url());
 
        $validation =  \Config\Services::validation();

        helper('text');
 
        if(! $this->validate([
            'assign_sub_account_permission' => "required|in_list[1]",
            'user_id' => "required|integer|is_natural",
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        } 

        $user_id = $this->request->getPost('user_id');

        $companyUserModel = model(CompanyUserModel::class);

        $result = $companyUserModel->where(['user_id' => $user_id,'company_id' => $this->user_session['company_id']])->get()->getRow();

        if(!is_object($result))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>'Người dùng này không tồn tại'));

        if(in_array($result->role,['Admin','SuperAdmin']))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>'Bạn không cần phân quyền cho quản trị viên, bởi quản trị viên được toàn quyền'));

        $bankSubAccountModel = model(BankSubAccountModel::class);      
        $results = $bankSubAccountModel->get_bank_sub_account_company($this->user_session['company_id']);

        $company_bank_sub_accounts = [];
        foreach($results as $result) {
            array_push($company_bank_sub_accounts, $result->id);
        }
      
        $userPermissionBankSubModel = model(UserPermissionBankSubModel::class);

        // reset permission first 
        //$result = $userPermissionBankSubModel->join("tb_autopay_bank_sub_account","tb_autopay_bank_sub_account.id=tb_autopay_user_permission_bank_sub.sub_account_id")->join("tb_autopay_bank_account","tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id")->where(['tb_autopay_user_permission_bank_sub.user_id' => $user_id,"tb_autopay_bank_account.company_id" => $this->user_session['company_id']])->delete();
        $userPermissionBankSubModel->reset_permission($user_id, $this->user_session['company_id']);
         
        $sub_acc_ids = $this->request->getPost('sub_acc_id');

        if(is_array($sub_acc_ids)) {
            foreach($sub_acc_ids as $sub_acc_id) {
                if(in_array($sub_acc_id, $company_bank_sub_accounts)) {
                    $userPermissionBankSubModel->insert(['user_id' => $user_id, 'sub_account_id' => $sub_acc_id]);
                }
            }
        }

        if (is_array($sub_acc_ids) && count($sub_acc_ids)) {
            model(UserPermissionFeatureModel::class)->set(['can_view_all' => 1])
                ->where(['user_id' => $user_id, 'company_id' => $this->user_session['company_id']])
                ->whereIn('feature_slug', ['Transactions', 'BankAccount'])
                ->update();
        }

        add_user_log(array('data_id'=>$user_id,'company_id' => $this->user_session['company_id'], 'data_type'=>'user_bank_sub_account_permission_update','description'=>'Cập nhật phân quyền truy cập tài khoản phụ ngân hàng','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));

 

        return $this->response->setJSON(array("status"=>true));
        
    }

  
    
}