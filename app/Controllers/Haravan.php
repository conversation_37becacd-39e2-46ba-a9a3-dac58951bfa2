<?php

namespace App\Controllers;

use App\Actions\PayCodeDetector;
use App\Models\BankAccountModel;
use App\Models\BankSubAccountModel;
use App\Models\ConfigurationModel;
use App\Models\HaravanModel;
use App\Models\WebhooksLogModel;

class Haravan extends BaseController
{
    public function index()
    { 
        $data = [
            'page_title' => 'Haravan',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        if(!has_permission('Webhooks', 'can_view_all'))
            show_404();

        $bankAccountModel = model(BankAccountModel::class);
        $data['paycodes'] = PayCodeDetector::getPayCodeList($this->user_session['company_id']);

        $data['bankAccounts'] = $bankAccountModel->select("tb_autopay_bank_account.id, tb_autopay_bank.brand_name, tb_autopay_bank_account.account_holder_name, tb_autopay_bank_account.account_number")->join("tb_autopay_bank","tb_autopay_bank.id=tb_autopay_bank_account.bank_id","left")->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id']])->get()->getResult();
        
      
        echo view('templates/autopay/header',$data);
        echo view('haravan/index',$data);
        echo view('templates/autopay/footer',$data);

    }

    public function ajax_haravan_list() {
     
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!has_permission('Webhooks', 'can_view_all'))
            show_404();
 
        $paycodePrefix = model(ConfigurationModel::class)->getFirstPaycodePrefix($this->user_session['company_id']);
        $haravanModel = model(HaravanModel::class);
        $webhooksLogModel = slavable_model(WebhooksLogModel::class, 'Haravan');
        $list = $haravanModel->getDatatables($this->user_session['company_id']);
         
        $data = array();

        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');

        foreach ($list as $haravan) {
            $total_success = $webhooksLogModel->where(['webhook_id' => $haravan->id,'webhook_type'=>'Haravan', 'response_status_code<=' => 201, 'response_status_code>=' => 200, 'company_id' =>$this->user_session['company_id']])->countAllResults();
            $total_failed = $webhooksLogModel->where(['webhook_id' => $haravan->id, 'response_status_code>' => 201,'webhook_type'=>'Haravan'])->countAllResults();

            $today_success = $webhooksLogModel->where(['webhook_id' => $haravan->id, 'response_status_code<=' => 201, 'response_status_code>=' => 200,'created_at>=' => date('Y-m-d 00:00:00'), 'webhook_type' => 'Haravan', 'company_id' =>$this->user_session['company_id']])->countAllResults();
            $today_failed = $webhooksLogModel->where(['webhook_id' => $haravan->id, 'response_status_code>' => 201, 'created_at >=' => date('Y-m-d 00:00:00'), 'webhook_type' => 'Haravan', 'company_id' =>$this->user_session['company_id']])->countAllResults();


            $no++;
            $row = array();
            
            $row[] = $no;
            $row[] = esc($haravan->id);
            $row[] = "<a href='".base_url('haravan/details/' . $haravan->id)."'>" . esc($haravan->name) . "</a>";

            $row[] = esc($haravan->brand_name) . ' <br> ' . esc($haravan->account_number) . ' <br>' . esc($haravan->account_holder_name);
            
            $row[] = esc($haravan->paycode_prefix ?: $paycodePrefix);

            $row[] = "Hôm nay: <a href='" . base_url('haravan/logs/' . $haravan->id). "' class='text-success'>". number_format($today_success) . "</a> / <a href='" . base_url('haravan/logs/' . $haravan->id). "' class='text-danger'>" . $today_failed ."</a><br>Tổng: <a href='" . base_url('haravan/logs/' . $haravan->id). "' class='text-success'>". number_format($total_success) . "</a> / <a href='" . base_url('haravan/logs/' . $haravan->id). "' class='text-danger'>" . $total_failed ."</a>";
            
            if($haravan->active == 1)
                $row[] = "<span class='text-success'>Kích hoạt</span>";
            else
                $row[] = "<span class='text-secondary'>Tạm ngưng</span>";
            $row[] = esc($haravan->created_at);
            $row[] = "<a href='javascript:;' onclick='edit_haravan(" . $haravan->id. ")' class='btn btn-sm btn-outline-warning ms-2 me-2 mt-2'><i class='bi bi-pencil'></i> Sửa</a> <a href='javascript:;' onclick='delete_haravan(" . $haravan->id. ")' class='btn btn-sm btn-outline-danger ms-2 mt-2'><i class='bi bi-trash3'></i> Xóa</a>";
       
            $data[] = $row;
        }
 
        $output = array(
            "draw" => $draw,
            "recordsTotal" => $haravanModel->countAll($this->user_session['company_id']),
            "recordsFiltered" => $haravanModel->countFiltered($this->user_session['company_id']),
            "data" => $data,
        );
        return $this->response->setJSON($output);
    
    
    }

    public function ajax_haravan_add() {

        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!has_permission('Webhooks', 'can_add'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền thêm tích hợp"));

 
        $validation =  \Config\Services::validation();

        helper('text');

        $rules = [
            'bank_account_id' =>['label' => 'Tài khoản ngân hàng', 'rules' => 'required|integer|is_natural'],
            'name' => ['label' => 'Đặt tên', 'rules' => "required|max_length[250]"],
            'api_token' => ['label' => 'API Token', 'rules' => "required|min_length[10]|max_length[200]"],
            'active' => "required|in_list[0,1]",
            'paycode_prefix' => 'required',
        ];  
        
        if(! $this->validate($rules)) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        } 

        $paycodes = PayCodeDetector::getPayCodeList($this->user_session['company_id']);

        if (! in_array($this->request->getVar('paycode_prefix'), $paycodes)) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Mã thanh toán không hợp lệ',
            ]);
        }
        
        $bank_account_id = $this->request->getPost('bank_account_id');
        $va_id = $this->request->getPost('va_id');

        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);

        $bank_account_details = $bankAccountModel->where(['company_id' => $this->user_session['company_id'], 'id'=>$bank_account_id])->get()->getRow();
        if(!is_object($bank_account_details))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>'Không tìm thấy tài khoản ngân hàng mà bạn chọn'));
        

        if(is_numeric($va_id) && $va_id > 0) {
            $va_details = $bankSubAccountModel->join("tb_autopay_bank_account", "tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id")->where(['tb_autopay_bank_sub_account.bank_account_id'=>$bank_account_id, 'tb_autopay_bank_sub_account.id' => $va_id ,'tb_autopay_bank_account.company_id' => $this->user_session['company_id'], 'acc_type' => 'Real'])->get()->getRow();

            if(!is_object($va_details))
                return $this->response->setJSON(array('status'=>FALSE,'message'=>'Không tìm thấy VA mà bạn chọn'));

        } else {

            if($bank_account_details->bank_api == 1 && in_array($bank_account_details->bank_id, [9,10,17,18]))
                return $this->response->setJSON(array('status'=>FALSE,'message'=>'Với các ngân hàng kết nối qua API, bạn cần chọn VA để nhận thanh toán. Vui lòng chọn 1 VA'));
            else
               $va_id = 0;
        }

       
        
        $data = array(
            'company_id' => $this->user_session['company_id'],
            'bank_account_id' => $this->request->getVar('bank_account_id'),
            'api_token' => trim($this->request->getVar('api_token')),
            'name' => xss_clean($this->request->getVar('name')),
            'active' => $this->request->getVar('active'),
            'va_id' => $va_id,
            'paycode_prefix' => $this->request->getVar('paycode_prefix'),
        );

                    
        $haravanModel = model(HaravanModel::class);
        $result = $haravanModel->insert($data);
        
        if($result) {
            set_alert('success','Tạo tích hợp Haravan thành công');
            add_user_log(array('data_id'=>$result,'company_id' => $this->user_session['company_id'], 'data_type'=>'haravan_add','description'=>'Thêm tích hợp Haravan','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
            return $this->response->setJSON(array("status"=>true,"id"=>$result));
        } else {
            add_user_log(array('data_id'=>0,'company_id' => $this->user_session['company_id'], 'data_type'=>'haravan_add','description'=>'Thêm tích hợp Haravan','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Failed'));
            return $this->response->setJSON(array("status"=>false,"message"=>"Không thể thêm webhook. Vui lòng liên hệ SePay để được hỗ trợ."));
        }
    
    }


    public function ajax_get_haravan($id='') {
        
        if(!has_permission('Webhooks', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền sửa tích hợp"));

        if(!is_numeric($id)) 
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"ID tích hợp không hợp lệ"));
        
        $haravanModel = model(HaravanModel::class);
        
        $result = $haravanModel->where(['id'=>$id, 'company_id' => $this->user_session['company_id']])->get()->getRow();
        $result->paycode_prefix = $result->paycode_prefix ?: model(ConfigurationModel::class)->getFirstPaycodePrefix($this->user_session['company_id']);
        
        if($result)
            return $this->response->setJSON(["status"=>TRUE, "data"=>$result]);
        else
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy tích hợp này"));
    }

    public function ajax_haravan_update() {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!has_permission('Webhooks', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền sửa tích hợp"));

 
        $validation =  \Config\Services::validation();

        helper('text');

       
        $rules = [
            'id' => ['label' => 'ID', 'rules' => 'required|integer|is_natural'],
            'bank_account_id' =>['label' => 'Tài khoản ngân hàng', 'rules' => 'required|integer|is_natural'],
            'name' => ['label' => 'Đặt tên', 'rules' => "required|max_length[250]"],
            'api_token' => ['label' => 'API Key', 'rules' => "required|min_length[10]|max_length[200]"],
            'active' => "required|in_list[0,1]",
            'paycode_prefix' => 'required',
        ]; 

         
        if(! $this->validate($rules)) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }

        $paycodes = PayCodeDetector::getPayCodeList($this->user_session['company_id']);

        if (! in_array($this->request->getVar('paycode_prefix'), $paycodes)) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Mã thanh toán không hợp lệ',
            ]);
        }

        $bank_account_id = $this->request->getPost('bank_account_id');
        $va_id = $this->request->getPost('va_id');

        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);

        $bank_account_details = $bankAccountModel->where(['company_id' => $this->user_session['company_id'], 'id'=>$bank_account_id])->get()->getRow();
        if(!is_object($bank_account_details))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>'Không tìm thấy tài khoản ngân hàng mà bạn chọn'));
        
        $haravanModel = model(HaravanModel::class);
        $haravan_id = $this->request->getVar('id');

        $haravan_details = $haravanModel->where(["id" =>$haravan_id,"company_id"=>$this->user_session['company_id']])->get()->getRow();

        if(!is_object($haravan_details))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>'Không tìm thấy tích hợp này'));


        if(is_numeric($va_id) && $va_id > 0) {
            $va_details = $bankSubAccountModel->join("tb_autopay_bank_account", "tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id")->where(['tb_autopay_bank_sub_account.bank_account_id'=>$bank_account_id, 'tb_autopay_bank_sub_account.id' => $va_id ,'tb_autopay_bank_account.company_id' => $this->user_session['company_id'], 'acc_type' => 'Real'])->get()->getRow();
            if(!is_object($va_details))
                return $this->response->setJSON(array('status'=>FALSE,'message'=>'Không tìm thấy VA mà bạn chọn'));

        } else {

            if($bank_account_details->bank_api == 1 && in_array($bank_account_details->bank_id, [9,10,17,18]))
                return $this->response->setJSON(array('status'=>FALSE,'message'=>'Với các ngân hàng kết nối qua API, bạn cần chọn VA để nhận thanh toán. Vui lòng chọn 1 VA'));
            else
               $va_id = 0;
        }
       

        $data = array(
            'bank_account_id' => $this->request->getVar('bank_account_id'),
            'api_token' => trim($this->request->getVar('api_token')),
            'name' => xss_clean($this->request->getVar('name')),
            'active' => $this->request->getVar('active'),
            'va_id' => $va_id,
            'paycode_prefix' => $this->request->getVar('paycode_prefix'),
        );

        
            
        $result = $haravanModel->set($data)->where(["id" =>$haravan_id,"company_id"=>$this->user_session['company_id']])->update();
        
        if($result) { 
            add_user_log(array('data_id'=>$haravan_id,'company_id' => $this->user_session['company_id'], 'data_type'=>'haravan_update','description'=>'Sửa tích hợp Haravan','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
            return $this->response->setJSON(array("status"=>true, 'id' => $haravan_id));
        } else {
            add_user_log(array('data_id'=>$haravan_id,'company_id' => $this->user_session['company_id'], 'data_type'=>'haravan_update','description'=>'Sửa tích hợp Haravan','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Failed'));
            return $this->response->setJSON(array("status"=>false,"message"=>"Không thể cập nhật tích hợp!"));
        }
            

    
    }

    public function ajax_haravan_delete() {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!has_permission('Webhooks', 'can_delete'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền xóa tích hợp"));

 
        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'id' => 'required|integer|is_natural',
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }
        $haravan_id = $this->request->getPost('id');

        $haravanModel = model(HaravanModel::class);
        $haravan_details = $haravanModel->where(['id'=>$haravan_id,'company_id'=>$this->user_session['company_id']])->get()->getRow();

        if(!is_object($haravan_details))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Không tìm thấy tích hợp này"));

        $haravanModel->delete($haravan_id);

        add_user_log(array('data_id'=>$haravan_id,'company_id' => $this->user_session['company_id'], 'data_type'=>'haravan_delete','description'=>'Xóa tích hợp Haravan','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
    
        return $this->response->setJSON(array("status"=>true));
         
    
    }
 

    public function details($id = '')
    { 
        $data = [
            'page_title' => 'Haravan',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        if(!has_permission('Webhooks', 'can_view_all'))
            show_404();

        if(!is_numeric($id))
            show_404();

        $haravanModel = model(HaravanModel::class);
        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);

        $data['bankAccounts'] = $bankAccountModel->select("tb_autopay_bank_account.id, tb_autopay_bank.brand_name, tb_autopay_bank_account.account_holder_name, tb_autopay_bank_account.account_number")->join("tb_autopay_bank","tb_autopay_bank.id=tb_autopay_bank_account.bank_id","left")->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id']])->get()->getResult();

        $data['haravan_details'] = $haravanModel->where(['id' => $id, 'company_id' => $this->user_session['company_id']])->get()->getRow();
        $data['haravan_details']->paycode_prefix = $data['haravan_details']->paycode_prefix ?: model(ConfigurationModel::class)->getFirstPaycodePrefix($this->user_session['company_id']);

        if(!is_object($data['haravan_details']))
            show_404();
        
        $data['bank_account_details'] = $bankAccountModel->select("tb_autopay_bank_account.account_holder_name,tb_autopay_bank_account.account_number,tb_autopay_bank.brand_name, tb_autopay_bank.full_name, tb_autopay_bank.logo_path, tb_autopay_bank.icon_path")->join("tb_autopay_bank","tb_autopay_bank_account.bank_id=tb_autopay_bank.id")->where(['tb_autopay_bank_account.id' => $data['haravan_details']->bank_account_id,'company_id' => $this->user_session['company_id']])->get()->getRow();

        if(!is_object($data['bank_account_details']))
            show_404();

        if(is_numeric($data['haravan_details']->va_id) && $data['haravan_details']->va_id > 0) {
            $data['va_details'] = $bankSubAccountModel->select("tb_autopay_bank_sub_account.sub_account, tb_autopay_bank_sub_account.sub_holder_name, tb_autopay_bank_sub_account.label")->join("tb_autopay_bank_account","tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id")->where(["tb_autopay_bank_account.company_id" => $this->user_session['company_id'],'tb_autopay_bank_sub_account.id' => $data['haravan_details']->va_id])->get()->getRow();
        } else
            $data['va_details'] = FALSE;
        

      
        echo view('templates/autopay/header',$data);
        echo view('haravan/details',$data);
        echo view('templates/autopay/footer',$data);

    }


    public function embed($id = '')
    { 
        $data = [
            'page_title' => 'Haravan',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        if(!has_permission('Webhooks', 'can_view_all'))
            show_404();

        if(!is_numeric($id))
            show_404();

        $haravanModel = model(HaravanModel::class);
        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);

        $data['bankAccounts'] = $bankAccountModel->select("tb_autopay_bank_account.id, tb_autopay_bank.brand_name, tb_autopay_bank_account.account_holder_name, tb_autopay_bank_account.account_number")->join("tb_autopay_bank","tb_autopay_bank.id=tb_autopay_bank_account.bank_id","left")->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id']])->get()->getResult();

        $data['haravan_details'] = $haravanModel->where(['id' => $id, 'company_id' => $this->user_session['company_id']])->get()->getRow();


        if(!is_object($data['haravan_details']))
            show_404();
        
        $data['bank_account_details'] = $bankAccountModel->select("tb_autopay_bank_account.account_holder_name,tb_autopay_bank_account.account_number,tb_autopay_bank.brand_name, tb_autopay_bank.full_name,tb_autopay_bank.bin,tb_autopay_bank. code, tb_autopay_bank.logo_path, tb_autopay_bank.icon_path")->join("tb_autopay_bank","tb_autopay_bank_account.bank_id=tb_autopay_bank.id")->where(['tb_autopay_bank_account.id' => $data['haravan_details']->bank_account_id,'company_id' => $this->user_session['company_id']])->get()->getRow();

        if(!is_object($data['bank_account_details']))
            show_404();

        $payCodePrefix = $data['haravan_details']->paycode_prefix ?: model(ConfigurationModel::class)->getFirstPaycodePrefix($this->user_session['company_id']);

        $account_number = $data['bank_account_details']->account_number;
        $account_holder_name = $data['bank_account_details']->account_holder_name;

        if(is_numeric($data['haravan_details']->va_id) && $data['haravan_details']->va_id>0) {

            $va_details = $bankSubAccountModel->join("tb_autopay_bank_account", "tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id","left")->where(['tb_autopay_bank_sub_account.id' => $data['haravan_details']->va_id, 'tb_autopay_bank_account.company_id' => $this->user_session['company_id']])->get()->getRow();

            if(is_object($va_details)) {
                $account_number = $va_details->sub_account;
                $account_holder_name = $va_details->sub_holder_name;
        
            }

        }  

        $data['api_json_data'] = '{"bank_bin":'.$data['bank_account_details']->bin.',"bank_code":"'.$data['bank_account_details']->code.'","account_number":"'.$account_number.'","prefix":"'.$payCodePrefix.'","bank_brand_name":"'.$data['bank_account_details']->brand_name.'","account_name":"'.$account_holder_name.'"}';

        $data['api_data_encode'] = base64_encode($data['api_json_data']);

        echo view('templates/autopay/header',$data);
        echo view('haravan/embed',$data);
        echo view('templates/autopay/footer',$data);

    }

    public function logs($id = '')
    { 
        $data = [
            'page_title' => 'Haravan',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        if(!has_permission('Webhooks', 'can_view_all'))
            show_404();

        if(!is_numeric($id))
            show_404();

        $haravanModel = model(HaravanModel::class);
        $bankAccountModel = model(BankAccountModel::class);

        $data['haravan_details'] = $haravanModel->where(['id' => $id, 'company_id' => $this->user_session['company_id']])->get()->getRow();


        if(!is_object($data['haravan_details']))
            show_404();
         

        echo view('templates/autopay/header',$data);
        echo view('haravan/logs',$data);
        echo view('templates/autopay/footer',$data);

    }

 
    
}