<?php

namespace App\Controllers;
use App\Models\BankAccountModel;
use App\Models\WebhooksModel;
use App\Models\WebhooksLogModel;
use App\Models\TransactionsModel;
use App\Models\UserModel;

use CodeIgniter\Controller;

class Webhookslog extends BaseController
{
    public function index()
    { 
        $data = [
            'page_title' => 'Webhooks Logs',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        if(!has_permission('WebhooksLog', 'can_view_all'))
            show_404();

        $webhooksModel = model(WebhooksModel::class);

        $data['webhooks'] = $webhooksModel->select("tb_autopay_webhooks.id, tb_autopay_bank.brand_name, tb_autopay_bank_account.account_holder_name, tb_autopay_bank_account.account_number")->join("tb_autopay_bank_account","tb_autopay_bank_account.id=tb_autopay_webhooks.bank_account_id","left")->join("tb_autopay_bank","tb_autopay_bank.id=tb_autopay_bank_account.bank_id","left")->where(['tb_autopay_webhooks.company_id' => $this->user_session['company_id']])->get()->getResult();
        
      
        echo view('templates/autopay/header',$data);
        echo view('webhookslog/index',$data);
        echo view('templates/autopay/footer',$data);

    }

    public function ajax_logs_list() {
     
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!has_permission('WebhooksLog', 'can_view_all'))
            show_404();
 
        $webhooksLogModel = slavable_model(WebhooksLogModel::class, 'Webhookslog');

        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');
        
        $webhook_id = $this->request->getGet('webhook_id');
        $webhook_type = $this->request->getGet('webhook_type');

        if(!is_numeric($webhook_id) || $webhook_id <= 0)
            $webhook_id = FALSE;

        if(!in_array($webhook_type, ['Webhooks','Sapo','Haravan','Woo','KiotViet','Nhanh.vn','Gohighlevel']))
            $webhook_type = FALSE;

        $logs = $webhooksLogModel->getDatatables($this->user_session['company_id'], $webhook_id, $webhook_type);
         
        $data = array();
 
      
        foreach ($logs as $log) {

            $no++;
            $row = array();
            
            $row[] = $no;
            $row[] = esc($log->id);
            //$row[] = '#' . esc($log->webhook_id) . ' ' . esc($log->name);
            $row[] = '#' . esc($log->webhook_id) . ' ' . esc($log->webhook_type);
            $row[] = "<a target='_blank' class='text-decoration-underline' href='". base_url('transactions/details/' . $log->sms_parsed_id) ."'>" . esc($log->sms_parsed_id) . "</a>";
            $row[] = esc($log->request_method);
            $row[] = esc($log->request_url);
            if($log->connect_success == 1)
                $row[] = "<span class='text-success'>Thành công</span>";
            else
                $row[] = "<span class='text-danger'>Thất bại</span>";
                
            if(in_array($log->response_status_code,[200,201]))
                $row[] = "<span class='text-success'>". esc($log->response_status_code) . "</span>";
            else
            $row[] = "<span class='text-danger'>" . esc($log->response_status_code) . "</span>";

            
            //$row[] = esc($log->request_header);
            //$row[] = esc($log->request_body);
            $row[] = "<a class='btn btn-info btn-sm' onclick='show_request_body_log(".$log->id.")'><i style='font-size:1.2em' class='bi bi-info-circle text-white'></i></a>";
            //$row[] = esc($log->response_body);
            $row[] = "<a class='btn btn-info btn-sm' onclick='show_response_body_log(".$log->id.")'><i style='font-size:1.2em' class='bi bi-info-circle text-white'></i></a>";

            $row[] = "<a class='btn btn-outline-primary btn-sm' onclick='manual_send_webhook(".$log->id.")'><i class='bi bi-repeat'></i> Gọi lại</a>";


            $row[] = esc($log->created_at);
           
       
            $data[] = $row;
        }
 
        $output = array(
            "draw" => $draw,
            "recordsTotal" => $webhooksLogModel->countAll($this->user_session['company_id'], $webhook_id, $webhook_type),
            "recordsFiltered" => $webhooksLogModel->countFiltered($this->user_session['company_id'], $webhook_id, $webhook_type),
            "data" => $data,
        );
        return $this->response->setJSON($output);
    
    
    }
 
    function ajax_get_log($id='') {

        if(!has_permission('WebhooksLog', 'can_view_all'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền xem webhooks logs"));

        if(!is_numeric($id)) 
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Log id không hợp lệ"));
    
        $webhooksLogModel = model(WebhooksLogModel::class);
        
        //$result = $webhooksLogModel->select("tb_autopay_webhooks_log.id,tb_autopay_webhooks_log.request_body,tb_autopay_webhooks_log.response_body")->join("tb_autopay_webhooks","tb_autopay_webhooks.id=tb_autopay_webhooks_log.webhook_id")->where(['tb_autopay_webhooks_log.id'=>$id, 'tb_autopay_webhooks.company_id' => $this->user_session['company_id']])->get()->getRow();
        $result = $webhooksLogModel->select("tb_autopay_webhooks_log.id,tb_autopay_webhooks_log.request_body,tb_autopay_webhooks_log.response_body")->where(['tb_autopay_webhooks_log.id'=>$id, 'tb_autopay_webhooks_log.company_id' => $this->user_session['company_id']])->get()->getRow();
        
        if($result)
            return $this->response->setJSON(["status"=>TRUE, "data"=>$result]);
        else
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy dữ liệu"));
    }
 

 
    
}