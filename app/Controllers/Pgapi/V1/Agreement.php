<?php

namespace App\Controllers\Pgapi\V1;

use App\Controllers\Pgapi\V1\BaseController;
use App\Features\PaymentGateway\Contexts\AgreementContext;
use App\Features\PaymentGateway\PaymentGatewayFeature;
use CodeIgniter\HTTP\Response;
use Throwable;

class Agreement extends BaseController
{
    public function cancel(): Response
    {
        $method = $this->request->getMethod(true);
        
        if ($method !== 'POST') {
            return $this->failNotFound();
        }
        
        if (!$this->authorize()) {
            return $this->failForbidden();
        }
        
        $fields = $this->request->getJSON(true);

        $rules = [
            'agreement_id' => ['required', 'string'],
            'customer_id' => ['required', 'string'],
        ];
        
        if (!$this->validateData($fields, $rules)) {
            return $this->fail($this->validator->getErrors());
        }
        
        $agreementId = trim($this->request->getJsonVar('agreement_id'));
       
        /** @var AgreementContext $agreementCheckout **/
        $agreementCheckout = null;

        try {
            $paymentGatewayFeature = new PaymentGatewayFeature;
            
            $paymentGatewayFeature->withAgreementContext($this->pgMerchant->merchant_id, $agreementId);
            $paymentGatewayFeature->agreementContext()->loadFields($fields);
            $agreementCheckout = $paymentGatewayFeature->agreementContext();
            
            $agreementCheckout->cancelAgreement();
            
            return $this->respondNoContent();
        } catch (Throwable $e) {
            if ($e->getCode() === 400) {
                return $this->fail($e->getMessage());
            }
            
            if ($e->getCode() === 404) {
                return $this->failNotFound($e->getMessage());
            }
            
            $this->logError($e);
            
            return $this->failServerError('Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ.');
        }
    }
}