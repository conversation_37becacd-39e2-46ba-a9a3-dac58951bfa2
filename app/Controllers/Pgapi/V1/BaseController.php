<?php

namespace App\Controllers\Pgapi\V1;

use App\Models\PgMerchantModel;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\Controller;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use Psr\Log\LoggerInterface;

/**
 * @property IncomingRequest $request
 */
abstract class BaseController extends Controller
{
    use ResponseTrait;
    
    public ?object $pgMerchant = null;
    
    protected $helpers = ['general'];

    public function initController(RequestInterface $request, ResponseInterface $response, LoggerInterface $logger): void
    {
        parent::initController($request, $response, $logger);
    }
    
    public function logError(\Throwable $e): void
    {
        log_message('error', sprintf('[PGAPI] %s | URI: %s | Raw body: %s | Trace: %s', $e->getMessage(), $this->request->getUri(), $this->request->getBody(), $e->getTraceAsString()));
    }
    
    public function authorize()
    {
        $authorization = $this->request->getHeaderLine('Authorization');
        
        if (!$authorization) return false;
        
        $encodedToken = str_replace('Basic ', '', $authorization);

        if (!$encodedToken) {
            return false;
        }
        
        $token = base64_decode($encodedToken);
        
        $credentials = explode(':', $token);
        
        if (count($credentials) !== 2) return false;
        
        $this->pgMerchant = model(PgMerchantModel::class)->where('merchant_id', $credentials[0])->first();
        
        if (!$this->pgMerchant || !$this->pgMerchant->active) {
            return false;
        }
        
        return $credentials[1] === $this->pgMerchant->secret_key;
    }
}
