<?php

namespace App\Controllers\Pgapi\V1;

use App\Controllers\Pgapi\V1\BaseController;
use App\Features\PaymentGateway\PaymentGatewayFeature;
use Throwable;

class Order extends BaseController
{
    public function void()
    {
        $method = $this->request->getMethod(true);
        
        if ($method !== 'POST') {
            return $this->failNotFound();
        }
        
        if (!$this->authorize()) {
            return $this->failUnauthorized();
        }
        
        $fields = $this->request->getJSON(true);

        $rules = [
            'order_id' => ['required', 'string'],
        ];
        
        if (!$this->validateData($fields, $rules)) {
            return $this->fail($this->validator->getErrors());
        }
        
        $orderId = trim($this->request->getJsonVar('order_id'));
       
        try {
            $paymentGatewayFeature = new PaymentGatewayFeature;
            $paymentGatewayFeature->withOrderContext($this->pgMerchant->merchant_id, $orderId);
            $orderContext = $paymentGatewayFeature->orderContext();
            $orderContext->voidOrder();
            
            return $this->respondNoContent();
        } catch (Throwable $e) {
            if ($e->getCode() === 400) {
                return $this->fail($e->getMessage());
            }
            
            if ($e->getCode() === 404) {
                return $this->failNotFound($e->getMessage());
            }
            
            $this->logError($e);
            
            return $this->failServerError('Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ.');
        }
    }
}