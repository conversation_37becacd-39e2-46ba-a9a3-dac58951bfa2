<?php

namespace App\Controllers;

use Exception;
use App\Models\ProductModel;
use Psr\Log\LoggerInterface;
use CodeIgniter\HTTP\Response;
use App\Libraries\VpbankClient;
use App\Models\SimCompanyModel;
use App\Models\BankAccountModel;
use App\Models\TransactionsModel;
use App\Libraries\VpbankOrgClient;
use CodeIgniter\API\ResponseTrait;
use App\Controllers\BaseController;
use App\Models\BankSubAccountModel;
use App\Models\OutputDeviceDecalModel;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use App\Models\CompanySubscriptionModel;
use App\Models\VpbankEnterpriseAccountModel;
use App\Exceptions\DisableBankClientException;
use App\Models\VpbankBankAccountMetadataModel;
use App\Actions\BankAccount\DeleteCompanyBankAccountAction;
use App\Features\BankAccount\Contexts\VPBankBankAccountConnectContext;

class Vpbank extends BaseController
{
    use ResponseTrait;

    const BANK_ID = 2;

    /** 
     * @var \Config\Vpbank
     */
    protected $vpbankConfig;

    public function __construct()
    {
        $this->vpbankConfig = config(\Config\Vpbank::class);
    }

    public function initController(RequestInterface $request, ResponseInterface $response, LoggerInterface $logger)
    {
        parent::initController($request, $response, $logger);

        is_bank_supported_by_promotion(self::BANK_ID, $this->company_details->company_id);
    }

    protected function handleVpbankClientException($e, $client = null)
    {
        log_message('error', 'VPBank Controller Error: ' . $e->getMessage() . ' - ' . $e->getTraceAsString());

        if ($client) $client->forceDebug();

        if ($e->getCode() == 504) {
            return $this->response->setJSON(['status' => false, 'message' => 'Hệ thống ngân hàng VPBank đang bận, vui lòng thử lại sau.']);
        }

        if ($e instanceof DisableBankClientException) {
            return $this->respond(['status' => false, 'message' => 'Hệ thống ngân hàng VPBank đang bảo trì, vui lòng liên hệ SePay để biết thêm thông tin chi tiết.']);
        }

        return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ.']);
    }

    public function step1($bankAccountId = '')
    {
        if ($this->isEnterpriseConnectRequest()) {
            return $this->handleEnterpriseStep1($bankAccountId);
        }
        
        if (!has_permission('BankAccount', 'can_add')) {
            return show_404();
        }

        $data = [
            'page_title' => 'Ngân hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'allowed_switch_api_connection' => $this->determineIfPersonalAllowedSwitchApiConnection()
        ];

        if (!$this->determineIfPersonalAllowedApiConnection()) {
            show_404();
        }

        $bankAccountId = trim(xss_clean($bankAccountId));

        $bankAccountModel = model(BankAccountModel::class);
        $data['bank_account_details'] = $bankAccountId ? $this->getConnectableCompanyBankAccount($bankAccountId) : null;

        if ($bankAccountId && !$data['bank_account_details']) {
            show_404();
        }

        if ($data['bank_account_details']) {
            $vpbankBankAccountMetadataModel = model(VpbankBankAccountMetadataModel::class);
            $vpbankBankAccountMetadataDetails = $vpbankBankAccountMetadataModel->where(['bank_account_id' => $data['bank_account_details']->id])->first();

            if ($vpbankBankAccountMetadataDetails) {
                try {
                    $client = new VpbankClient;

                    try {
                        $response = $client->status($vpbankBankAccountMetadataDetails->register_id);
            
                        $responseData = json_decode($response->getBody());

                        if (is_object($responseData) && property_exists($responseData, 'status') && $responseData->status) {
                            $bankAccountModel->set([
                                'bank_sms' => 0,
                                'bank_sms_connected' => 0,
                                'bank_api' => 1,
                                'bank_api_connected' => 1,
                            ])->update($data['bank_account_details']->id);

                            set_alert('success', 'Đã chuyển phương thức kết nối sang API Banking thành công');
                            add_user_log(['data_id' => $bankAccountId, 'company_id' => $this->user_session['company_id'], 'data_type' => 'bank_account_update', 'description' => 'Chuyển đổi phương thức kết nối sang API Banking', 'user_id' => $this->user_details->id, 'ip'=>$this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);

                            return redirect()->to(base_url('vpbank/details/' . $bankAccountId));
                        }
                    } catch (Exception $e) {
                        $this->handleVpbankClientException($e, $client);
                    }
                } catch (Exception $e) {
                    $this->handleVpbankClientException($e);
                }
            }
        }

        if (!$this->determineIfVpbankClientAccessTokenExpired()) {
            return redirect()->to(base_url('vpbank/step2/' . $bankAccountId));
        }

        echo view('templates/autopay/header', $data);
        echo view('vpbank/step1', $data);
        echo view('templates/autopay/footer', $data);
    }

    public function ajax_step1()
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }

        if (!has_permission('BankAccount', 'can_add')) {
            return $this->repsond(['status' => false, 'message' => 'Bạn không có quyền thêm tài khoản ngân hàng.']);
        }

        $bankAccountFeature = service('bankAccountFeature');
        $bankAccountFeature->withBankAccountConnectContext('VPBank');
        $bankAccountFeature->bankAccountConnectContext()->clearSession();

        try {
            $client = new VpbankClient;
        } catch (exception $e) {
            return $this->handleVpbankClientException($e);        
        }

        $loginPageUrl = null;

        try {
            $response = $client->getLoginPageUrl();
            $loginPageUrl = $response->getHeaderLine('Location');

            if (strpos($loginPageUrl, 'login.do') < 0) {
                throw new Exception('Login page URL generated failed');
            }
        } catch (exception $e) {
            return $this->handleVpbankClientException($e, $client);        
        }

        return $this->respond(['status' => true, 'login_page_url' => $loginPageUrl]);
    }

    public function step2($bankAccountId = '')
    {
        if ($this->isEnterpriseConnectRequest()) {
            return $this->handleEnterpriseStep2($bankAccountId);
        }
        
        if (!has_permission('BankAccount', 'can_add')) {
            return show_404();
        }

        $data = [
            'page_title' => 'Ngân hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'allowed_switch_api_connection' => $this->determineIfPersonalAllowedSwitchApiConnection(),
        ];

        if (!$data['allowed_switch_api_connection']) {
            show_404();
        }

        $bankAccountId = trim(xss_clean($bankAccountId));

        $bankAccountModel = model(BankAccountModel::class);
        $data['bank_account_details'] = $bankAccountId ? $this->getConnectableCompanyBankAccount($bankAccountId) : null;

        if ($this->determineIfVpbankClientAccessTokenExpired()) {
            set_alert('error', 'Quý khách vui lòng đăng nhập VPBank NEO trước.');
            
            return redirect()->to(base_url('vpbank/step1/' . $bankAccountId));
        }

        if ($bankAccountId && !$data['bank_account_details']) {
            show_404();
        }

        echo view('templates/autopay/header', $data);
        echo view('vpbank/step2', $data);
        echo view('templates/autopay/footer', $data);
    }

    public function ajax_step2()
    {
        if ($this->isEnterpriseConnectRequest()) {
            return $this->handleEnterpriseAjaxStep2();
        }
        
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }

        if (!has_permission('BankAccount', 'can_add')) {
            return $this->repsond(['status' => false, 'message' => 'Bạn không có quyền thêm tài khoản ngân hàng.']);
        }

        if ($this->determineIfVpbankClientAccessTokenExpired()) {
            set_alert('error', 'Quý khách vui lòng đăng nhập VPBank NEO trước.');

            return $this->respond(['status' => false, 'redirect_url' => base_url('vpbank/step1')]);
        }

        $data = [
            'bank_account_id' => trim(xss_clean($this->getVarString('bank_account_id'))),
            'account_number' => trim(xss_clean($this->getVarString('account_number'))),
            'account_holder_name' => trim(xss_clean($this->getVarString('account_holder_name'))),
            'identification_number' => trim(xss_clean($this->getVarString('identification_number'))),
            'label' => trim(xss_clean($this->getVarString('label'))),
        ];

        $rules = [
            'bank_account_id' => [
                'label' => 'ID tài khoản ngân hàng',
                'rules' => ['permit_empty'],
            ],
            'account_number' => [
                'label' => 'số tài khoản',
                'rules' => ['required', 'max_length[100]'],
            ],
            'account_holder_name' => [
                'label' => 'tên chủ tài khoản',
                'rules' => ['required', 'max_length[100]'], 
            ],
            'identification_number' => [
                'label' => 'CCCD/CMND',
                'rules' => ['required', 'min_length[10]', 'max_length[20]']
            ],
            'label' => [
                'label' => 'tên gợi nhớ',
                'rules' => ['permit_empty', 'max_length[100]']
            ], 
        ];

        if (! $this->validateData($data, $rules)) {
            return $this->fail($this->validator->getErrors());
        }

        $bankAccountModel = model(BankAccountModel::class);
        
        $vpbankBankAccountMetadataModel = model(VpbankBankAccountMetadataModel::class);

        $bankAccountDetails = $data['bank_account_id'] ? $this->getConnectableCompanyBankAccount($data['bank_account_id']) : null;

        if ($data['bank_account_id'] && !$bankAccountDetails) {
            return $this->failNotFound();
        }

        if (!$bankAccountDetails && $bankAccountModel->where(['account_number' => $data['account_number'], 'bank_id' => Vpbank::BANK_ID])->countAllResults()) {
            return $this->fail(['account_number' => 'Số tài khoản này đã tồn tại trên hệ thống']);
        }

        try {
            $client = new VpbankClient($this->company_details->id);
        } catch (Exception $e) {
            return $this->handleVpbankClientException($e);
        }

        $requestId = null;
        $registerId = md5(uniqid() . $data['account_number']);

        try {
            $response = $client->initRegister(
                $data['identification_number'],
                $data['account_number'],
                $data['account_holder_name'],
                $registerId,
            );

            $responseData = json_decode($response->getBody());

            if (!$responseData) {
                throw new Exception('Response data parsed failed');
            }
         
            if (property_exists($responseData, 'error')) {
                if ($responseData->error == 'ISS-010') {
                    throw new Exception('Integration type not found');
                }
    
                if (in_array($responseData->error, ['ISS-002', 'ISS-006', 'ISS-007'])) {
                    return $this->fail(['account_number' => 'Số tài khoản không tồn tại hoặc đã bị khóa ở hệ thống ngân hàng VPBank.']);
                }
    
                if ($responseData->error == 'ISS-003') {
                    return $this->fail(['identification_number' => 'Số CCCD/CMND không được đăng ký cho số tài khoản trên.']);
                }
    
                if ($responseData->error == 'ISS-004') {
                    return $this->fail(['account_holder_name' => 'Tên người thụ hưởng không khớp với thông tin số tài khoản trên.']);
                }
    
                if ($responseData->error == 'ISS-005') {
                    return $this->fail(['account_number' => 'Số tài khoản phải là khách hàng cá nhân.']);
                }
    
                if ($responseData->error == 'ISS-008') {
                    return $this->fail(['account_number' => 'Số tài khoản đang được liên kết tại VPBank, liên hệ SePay để được hỗ trợ.']);
                }
    
                if ($responseData->error == 'ISS-013') {
                    return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng thử lại sau.']);
                }
    
                if ($responseData->error == 'ISS-020') {
                    return $this->fail(['account_number' => 'Tài khoản không hợp lệ, vui lòng liên hệ SePay để được hỗ trợ.']);
                }
            }

            if (!property_exists($responseData, 'requestId')) {
                throw new Exception('requestId not found');
            }

            $requestId = $responseData->requestId;
            $sendableOtp = false;

            if ($bankAccountDetails) {
                $bankAccountId = $bankAccountDetails->id;
                $sendableOtp = $bankAccountModel->where(['id' => $bankAccountDetails->id])->set([
                    'account_number' => $data['account_number'],
                    'account_holder_name' => $data['account_holder_name'],
                    'identification_number' => $data['identification_number'],
                    'label' => $data['label'],
                ])->update();
            } else {
                $sendableOtp = $bankAccountId = $bankAccountModel->insert([
                    'company_id' => $this->company_details->id,
                    'bank_id' => Vpbank::BANK_ID,
                    'account_number' => $data['account_number'],
                    'account_holder_name' => $data['account_holder_name'],
                    'identification_number' => $data['identification_number'],
                    'label' => $data['label'],
                    'bank_api' => 1,
                    'pending_api_connection' => 1,
                ]);

                add_user_log(['data_id' => $bankAccountId, 'company_id' => $this->user_session['company_id'], 'data_type' => 'bank_account_add', 'description' => 'Thêm tài khoản ngân hàng VPBank API', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);
            }

            if (!$sendableOtp) {
                return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng thử lại.']);
            }

            return $this->respond(['status' => true, 'id' => $bankAccountId]);
        } catch (Exception $e) {
            return $this->handleVpbankClientException($e, $client);
        }
    }

    public function ajax_logout()
    {
        if ($this->isEnterpriseConnectRequest()) {
            return $this->handleEnterpriseAjaxLogout();
        }
        
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }

        if (!has_permission('BankAccount', 'can_add')) {
            return $this->repsond(['status' => false, 'message' => 'Bạn không có quyền thêm tài khoản ngân hàng.']);
        }

        $vpbankClientAccessTokenModel = model(VpbankClientAccessTokenModel::class);
        $vpbankClientAccessTokenModel->where('company_id', $this->company_details->id)->delete();

        return $this->respond(['status' => true]);
    }

    public function step3($id = '')
    {
        if ($this->isEnterpriseConnectRequest()) {
            return $this->handleEnterpriseStep3($id);
        }
        
        if (!has_permission('BankAccount', 'can_add')) {
            return show_404();
        }

        $data = [
            'page_title' => 'Ngân hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
        ];

        $bankAccountId = trim(xss_clean($id));

        if (! is_numeric($bankAccountId)) {
            show_404();
        }

        $bankAccountModel = model(BankAccountModel::class);
        $data['bank_account_details'] = $this->getConnectableCompanyBankAccount($bankAccountId);

        if (!$data['bank_account_details']) {
            show_404();
        }

        $vpbankClientAccessTokenModel = model(VpbankClientAccessTokenModel::class);

        if ($vpbankClientAccessTokenModel->where(['expires_at >' => date('Y-m-d H:i:s'), 'company_id' => $this->company_details->id, 'authorize_code !=' => ''])->countAllResults() == 0) {
            set_alert('error', 'Quý khách vui lòng đăng nhập VPBank NEO trước.');
            
            return redirect()->to(base_url('vpbank/step1/' . $bankAccountId));
        }

        if ($data['bank_account_details']->bank_api_connected) {
            return redirect()->to(base_url('vpbank/step4/' . $bankAccountId));
        }

        echo view('templates/autopay/header', $data);
        echo view('vpbank/step3', $data);
        echo view('templates/autopay/footer', $data);
    }

    public function ajax_step3()
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }

        if (!has_permission('BankAccount', 'can_add')) {
            return $this->repsond(['status' => false, 'message' => 'Bạn không có quyền thêm tài khoản ngân hàng.']);
        }

        if ($this->determineIfVpbankClientAccessTokenExpired()) {
            set_alert('error', 'Quý khách vui lòng đăng nhập VPBank NEO trước.');

            return $this->respond(['status' => false, 'redirect_url' => base_url('vpbank/step1')]);
        }

        $data = [
            'bank_account_id' => trim(xss_clean($this->getVarString('bank_account_id'))),
        ];

        if (!$data['bank_account_id']) {
            return $this->failNotFound();
        }

        $bankAccountModel = model(BankAccountModel::class);
        $vpbankBankAccountMetadataModel = model(VpbankBankAccountMetadataModel::class);

        $bankAccountDetails = $this->getConnectableCompanyBankAccount($data['bank_account_id']);

        if (!$bankAccountDetails) {
            return $this->failNotFound();
        }

        $bankAccountFeature = service('bankAccountFeature');
        $bankAccountFeature->withBankAccountConnectContext('VPBank');
        $bankAccountFeature->bankAccountConnectContext()->clearSession();

        try {
            $client = new VpbankClient($this->company_details->id);
        } catch (Exception $e) {
            return $this->handleVpbankClientException($e);
        }

        $requestId = null;
        $registerId = md5(uniqid() . $bankAccountDetails->account_number);

        try {
            $response = $client->initRegister(
                $bankAccountDetails->identification_number,
                $bankAccountDetails->account_number,
                $bankAccountDetails->account_holder_name,
                $registerId,
            );

            $responseData = json_decode($response->getBody());

            if (!$responseData) {
                throw new Exception('Response data parsed failed');
            }
         
            if (property_exists($responseData, 'error')) {
                if ($responseData->error == 'ISS-010') {
                    throw new Exception('Integration type not found');
                }
    
                if (in_array($responseData->error, ['ISS-002', 'ISS-006', 'ISS-007'])) {
                    set_alert('Số tài khoản không tồn tại hoặc đã bị khóa ở hệ thống ngân hàng VPBank.');

                    return $this->respond(['status' => false, 'redirect_url' => base_url('vpbank/step2/' . $bankAccountDetails->id)]);
                }
    
                if ($responseData->error == 'ISS-003') {
                    set_alert('Số CCCD/CMND không được đăng ký cho số tài khoản trên.');

                    return $this->respond(['status' => false, 'redirect_url' => base_url('vpbank/step2/' . $bankAccountDetails->id)]);
                }
    
                if ($responseData->error == 'ISS-004') {
                    set_alert('Tên người thụ hưởng không khớp với thông tin số tài khoản trên.');

                    return $this->respond(['status' => false, 'redirect_url' => base_url('vpbank/step2/' . $bankAccountDetails->id)]);
                }
    
                if ($responseData->error == 'ISS-005') {
                    return $this->fail(['account_number' => 'Số tài khoản phải là khách hàng cá nhân.']);

                    return $this->respond(['status' => false, 'redirect_url' => base_url('vpbank/step2/' . $bankAccountDetails->id)]);
                }
    
                if ($responseData->error == 'ISS-008') {
                    throw new Exception('The customer already has registered for this provider');

                    return $this->respond(['status' => false, 'redirect_url' => base_url('vpbank/step2/' . $bankAccountDetails->id)]);
                }
    
                if ($responseData->error == 'ISS-013') {
                    return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng thử lại sau.']);
                }
    
                if ($responseData->error == 'ISS-020') {
                    return $this->respond(['status' => false, 'message' => 'Tài khoản không hợp lệ, vui lòng liên hệ SePay để được hỗ trợ.']);
                }
            }

            if (!property_exists($responseData, 'requestId')) {
                throw new Exception('requestId not found');
            }

            $requestId = $responseData->requestId;
            
        } catch (Exception $e) {
            return $this->handleVpbankClientException($e, $client);
        }

        try {
            $response = $client->sendOtp($requestId);

            $otpPageUrl = $response->getHeaderLine('location');

            if (!$otpPageUrl) {
                throw new Exception('OTP page URL generated failed');
            }

            $session = session();
            $session->set('vpbank_register', [
                'bank_account_id' => $bankAccountDetails->id,
                'request_id' => $requestId,
                'register_id' => $registerId
            ]);

            return $this->respond(['status' => true, 'otp_page_url' => $otpPageUrl]);
        } catch (Exception $e) {
            return $this->handleVpbankClientException($client);
        }
    }

    public function step4($id = '')
    {
        if ($this->isEnterpriseConnectRequest()) {
            return $this->handleEnterpriseStep4($id);
        }
        
        if (!has_permission('BankAccount', 'can_add')) {
            return show_404();
        }

        $data = [
            'page_title' => 'Cấu hình',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
        ];

        $bankAccountId = trim(xss_clean($id));

        if (! is_numeric($bankAccountId)) {
            show_404();
        }

        $data['bankAccount'] = $this->getConnectedCompanyBankAccount($bankAccountId);

        if (!$data['bankAccount']) {
            show_404();
        }

        if (!$data['bankAccount']->bank_api_connected) {
            set_alert('error', 'Quý khách vui lòng xác thực OTP để hoàn tất liên kết.');
            
            return redirect()->to(base_url('vpbank/step3/' . $bankAccountId));
        }

        $data['settings'] = bank_account_settings($data['bankAccount']->id);

        echo view('templates/autopay/header', $data);
        echo view('vpbank/step4', $data);
        echo view('templates/autopay/footer', $data);   
    }

    public function step5($id = '')
    {
        if (!has_permission('BankAccount', 'can_add')) {
            return show_404();
        }

        $data = [
            'page_title' => 'Ngân hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
        ];

        $bankAccountId = trim(xss_clean($id));

        if (! is_numeric($bankAccountId)) {
            show_404();
        }

        $bankAccountModel = model(BankAccountModel::class);
        $data['bank_account_details'] = $this->getConnectedCompanyBankAccount($bankAccountId);

        if (!$data['bank_account_details']) {
            show_404();
        }

        if (!$data['bank_account_details']->bank_api_connected) {
            set_alert('Quý khách vui lòng xác thực OTP để hoàn tất liên kết.');
            
            return redirect()->to(base_url('vpbank/step3/' . $bankAccountId));
        }

        echo view('templates/autopay/header', $data);
        echo view('vpbank/step5', $data);
        echo view('templates/autopay/footer', $data);
    }

    public function ajax_check_trans() 
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }

        if (!has_permission('BankAccount', 'can_add'))
            return $this->respond(['status' => false, 'message' => 'Bạn không có quyền thực hiện thao tác này']);

        $bankAccountId = trim(xss_clean($this->request->getVar('bank_account_id')));

        if (!is_numeric($bankAccountId))
            show_404();

        $bankAccountDetails = $this->getConnectedCompanyBankAccount($bankAccountId);

        if (!is_object($bankAccountDetails))
            show_404();

        $session = session();

        if (!$session->get('checking_transaction_date'))
            $session->set('checking_transaction_date', date('Y-m-d H:i:s'));

        $checkingTransactionDate = $session->get('checking_transaction_date');
        $transactionsModel = slavable_model(TransactionsModel::class, 'Vpbank');
        $lastTransaction = $transactionsModel
            ->select('id, amount_in, account_number, sub_account, transaction_content, transaction_date')
            ->where([
                'deleted_at' => null, 
                'parser_status' => 'Success', 
                'bank_account_id' => $bankAccountId, 
                'source' => 'BankAPINotify', 
                'transaction_date >= ' => $checkingTransactionDate
            ])
            ->orderBy('id', 'DESC')
            ->get()->getRow();

        if (!is_object($lastTransaction))
            return $this->response->setJSON(['status' => false]);

        $session->remove('checking_transaction_date');

        return $this->response->setJSON(['status' => true, 'last_transaction' => $lastTransaction]);
    }

    public function details($id = '')
    {
        $data = [
            'page_title' => 'Ngân hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'allowed_delete_bank_account' => $this->determineIfPersonalAllowedDeleteBankAccount(),
            'allowed_switch_sms_connection' => $this->determineIfPersonalAllowedSwitchSmsConnection(),
            'allowed_switch_api_connection' => $this->determineIfPersonalAllowedSwitchApiConnection(),
        ];

        if (!has_permission('BankAccount', 'can_view_all'))
            show_404();

        if (!is_numeric($id))
            show_404();

        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $transactionsModel = slavable_model(TransactionsModel::class, 'Vpbank');
        $simCompanyModel = model(SimCompanyModel::class);

        $bankAccountDetails = $bankAccountModel
            ->select('tb_autopay_bank_account.id, tb_autopay_bank_account.account_number, tb_autopay_bank_account.label, tb_autopay_bank_account.active, tb_autopay_bank_account.created_at, tb_autopay_bank.full_name, tb_autopay_bank.brand_name, tb_autopay_bank_account.account_number, tb_autopay_bank_account.account_holder_name, tb_autopay_bank.icon_path, tb_autopay_bank.logo_path, tb_autopay_sim.sim_phonenumber, tb_autopay_bank_account.last_transaction, tb_autopay_bank_account.accumulated,tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.bank_id,tb_autopay_bank_account.bank_sms, tb_autopay_bank_account.bank_sms_connected, tb_autopay_bank_account.bank_api, tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.identification_number, tb_autopay_bank_account.phone_number, tb_autopay_bank_account.sim_id')
            ->join('tb_autopay_bank', 'tb_autopay_bank.id=tb_autopay_bank_account.bank_id')
            ->join('tb_autopay_sim', 'tb_autopay_sim.id=tb_autopay_bank_account.sim_id', 'left')
            ->where([
                'tb_autopay_bank_account.company_id' => $this->user_session['company_id'], 
                'tb_autopay_bank_account.id' => $id,
                'tb_autopay_bank_account.bank_id' => Vpbank::BANK_ID
            ])->get()->getRow();
        
        if (!is_object($bankAccountDetails))
            show_404();
            
        $vpbankEnterpriseAccountModel = model(VpbankEnterpriseAccountModel::class);
        $vpbankEnterpriseAccount = $vpbankEnterpriseAccountModel->where('bank_account_id', $bankAccountDetails->id)->first();

        if ($vpbankEnterpriseAccount && $bankAccountDetails->bank_api_connected == 0 && $bankAccountDetails->bank_api == 1) {
            return redirect()->to(base_url('vpbank/step2/' . $bankAccountDetails->id . '?is_enterprise=1'));
        }
       
        if ($bankAccountDetails->bank_api_connected == 0 && $bankAccountDetails->bank_api == 1) {
            return redirect()->to(base_url('vpbank/step2/' . $bankAccountDetails->id));
        }

        $data['bank_account_details'] = $bankAccountDetails;

        $data['count_subs'] = $bankSubAccountModel->where(['bank_account_id' => $id, 'deleted_at' => null])->countAllResults();
       
        $data['bank_sms_info'] = get_bank_sms_info($data['bank_account_details']->brand_name);
       
        $data['count_transactions'] = $transactionsModel->where(['bank_account_id' => $data['bank_account_details']->id, 'deleted_at' => NULL])->countAllResults();
       
        $data['count_sms_transactions'] = $transactionsModel->where(['bank_account_id' => $data['bank_account_details']->id, 'deleted_at' => NULL, 'source' => 'SMS'])->countAllResults();
       
        $data['sims'] = $simCompanyModel
            ->select('tb_autopay_sim.id, tb_autopay_sim.sim_phonenumber')
            ->join('tb_autopay_sim', 'tb_autopay_sim.id=tb_autopay_sim_company.sim_id')
            ->where(['tb_autopay_sim_company.company_id' => $this->user_session['company_id'], 'tb_autopay_sim.active' => 1])
            ->orderBy('tb_autopay_sim_company.created_at', 'DESC')
            ->get()->getResult();

        $vpbankEnterpriseAccountModel = model(VpbankEnterpriseAccountModel::class);
        $data['enterprise_account'] = $vpbankEnterpriseAccountModel->where(['bank_account_id' => $data['bank_account_details']->id])->first();

        if ($data['enterprise_account']) {
            $data['allowed_switch_api_connection'] = $this->determineIfEnterpriseAllowedSwitchApiConnection();
            $data['allowed_switch_sms_connection'] = $this->determineIfEnterpriseAllowedSwitchSmsConnection();
            $data['allowed_delete_bank_account'] = $this->determineIfEnterpriseAllowedDeleteBankAccount();
        }

        echo theme_view('templates/autopay/header', $data);
        echo theme_view($data['bank_account_details']->bank_sms ? 'vpbank/sms/details' : ($data['enterprise_account'] ? 'vpbank/org/details' : 'vpbank/details'), $data);
        echo theme_view('templates/autopay/footer', $data);
    }

    public function ajax_va_list()
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }

        if (! has_permission('BankAccount', 'can_view_all')) {
            return $this->failNotFound();
        }

        $bankAccountId = $this->getVarString('bank_account_id');
        
        if (! is_numeric($bankAccountId) || ! $this->getConnectedCompanyBankAccount($bankAccountId)) {
            return $this->failNotFound();
        }
 
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $bankSubAccounts = $bankSubAccountModel->getDatatables($this->user_session['company_id'], $bankAccountId);
        $data = [];
        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');
        $canEdit = has_permission('BankAccount', 'can_edit');
        $canDelete = has_permission('BankAccount', 'can_delete');
 
        foreach ($bankSubAccounts as $bankSubAccount) {
            $no++;
            $row = array();
            $actionsBtnHtml = '';

            if ($canEdit)
                $actionsBtnHtml .= "<a href='javascript:;' onclick='edit_va(" . esc($bankSubAccount->id) . ")' class='btn btn-sm btn-outline-warning ms-2 me-2'><i class='bi bi-pencil'></i> Sửa</a>";

            if ($canDelete)
                $actionsBtnHtml .= "<a href='javascript:;' onclick='delete_va(" . esc($bankSubAccount->id) . ")' class='btn btn-sm btn-outline-danger ms-2'><i class='bi bi-trash3'></i> Xóa</a>";
                 
            $row[] = $no;
            $row[] = $bankSubAccount->id;
            $row[] = "<a href='javascript:;' onclick='view_va("  . esc($bankSubAccount->id) . ")'>" . esc($bankSubAccount->sub_account) . "</a>";
          
            if ($bankSubAccount->va_active == 1)
                $row[] = "<span class='text-success'>Hoạt động</span>";
            else
                $row[] = "<span class='text-danger'>Tạm ngưng</span>";

            $row[] = esc($bankSubAccount->label);
            $row[] = esc($bankSubAccount->created_at);
            $row[] = $actionsBtnHtml;
            $data[] = $row;
        }
 
        return $this->respond([
            'draw' => $draw,
            'recordsTotal' => $bankSubAccountModel->countAll($this->user_session['company_id'], $bankAccountId),
            'recordsFiltered' => $bankSubAccountModel->countFiltered($this->user_session['company_id'], $bankAccountId),
            'data' => $data,
        ]);
    }

    public function ajax_request_switch_api_connection()
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }

        if (! $this->determineIfPersonalAllowedApiConnection()) {
            return $this->failNotFound();
        }
 
        if (! has_permission('BankAccount', 'can_edit')) {
            return $this->respond(['status' => false, 'message' => 'Bạn chưa được cấp quyền để thực hiện thao tác này']);
        }

        $isEnteprise = $this->request->getVar('is_enterprise') == '1';
        $bankAccountId = trim(xss_clean($this->getVarString('id')));
        $bankAccountDetails = $this->getSwitchableApiConnectionCompanyBankAccount($bankAccountId);

        $vpbankEntepriseAccountModel = model(VpbankEnterpriseAccountModel::class);
        $vpbankEnterpriseAccount = $vpbankEntepriseAccountModel->where('bank_account_id', $bankAccountDetails->id)->first();
        
        if (!is_object($bankAccountDetails)) {
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng không đủ điều kiện để thực hiện thao tác này']);
        }

        if ($vpbankEnterpriseAccount) {
            $bankAccountModel = model(BankAccountModel::class);
            $bankAccountModel->where('id', $bankAccountDetails->id)->set(['bank_api' => 1, 'bank_sms' => 0, 'sms_id' => 0])->update();
            $redirectTo = base_url('vpbank/details/' . $bankAccountDetails->id);
        } else {
            $redirectTo = base_url($isEnteprise ? sprintf('vpbank/step1/%s?is_enterprise=1', $bankAccountDetails->id) : 'vpbank/step1/' . $bankAccountDetails->id);
        }
        
        return $this->respond(['status' => true, 'redirect_to' => $redirectTo]);
    }

    public function ajax_delete_bank_account()
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }
        
        if (!has_permission('BankAccount', 'can_delete')) {
            return $this->respond(['status' => false, 'message' => 'Bạn không có quyền hủy liên kết tài khoản ngân hàng']);
        }

        $bankAccountId = trim(xss_clean($this->getVarString('bank_account_id')));

        if (!$bankAccountId || !is_numeric($bankAccountId)) {
            return $this->failNotFound();
        }

        $bankAccountDetails = $this->getConnectedCompanyBankAccount($bankAccountId);

        if (!is_object($bankAccountDetails)) {
            return $this->failNotFound();
        }

        /** @var \Config\Vpbank $config */
        $config = config(\Config\Vpbank::class);

        $vpbankEnterpriseAccountModel = model(VpbankEnterpriseAccountModel::class);
        $vpbankEnterpriseAccount = $vpbankEnterpriseAccountModel->where('bank_account_id', $bankAccountDetails->id)->first();

        if ($vpbankEnterpriseAccount && !$config->orgFeatureAllowedDeleteApiBankAccount) {
            return $this->respond(['status' => false, 'message' => 'Không thể thực hiện thao tác này.']);
        }

        $vpbankBankAccountMetadataModel = model(VpbankBankAccountMetadataModel::class);
        $vpbankBankAccountMetadataDetails = $vpbankBankAccountMetadataModel->where(['bank_account_id' => $bankAccountDetails->id])->first();

        if (!$vpbankBankAccountMetadataDetails) {
            return $this->failNotFound();
        }

        try {
            $client = new VpbankClient;
        } catch (Exception $e) {
            return $this->handleVpbankClientException($e);
        }

        try {
            $response = $client->deregister($vpbankBankAccountMetadataDetails->register_id);

            $responseData = json_decode($response->getBody());

            if (is_object($responseData) && property_exists($responseData, 'error')) {
                if ($responseData->error == 'ISS-011') {
                    throw new Exception('Register ID not found');
                }

                if ($responseData->error == 'ISS-012') {
                    throw new Exception('Register ID is not active');
                }
            }

            $vpbankBankAccountMetadataModel->where(['id' => $vpbankBankAccountMetadataDetails->id])->delete();

            DeleteCompanyBankAccountAction::run($bankAccountDetails, $this->company_details, $this->request->getIPAddress(), self::class);

            add_user_log(['data_id' => $bankAccountId, 'company_id' => $this->user_session['company_id'], 'data_type' => 'bank_account_delete', 'description' => 'Xóa tài khoản ngân hàng ' . $bankAccountDetails->account_number, 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);
            set_alert('success', 'Đã hủy liên kết tài khoản ngân hàng thành công.');

            return $this->respond(['status' => true]);
        } catch (Exception $e) {
            return $this->handleVpbankClientException($e, $client);
        }
    }

    public function ajax_add_va()
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }

        if (!has_permission('BankAccount', 'can_edit')) {
            return $this->respond(['status' => false, 'message' => 'Bạn không có quyền thực hiện thao tác này.']);
        }
 
        $data = [
            'sub_account' =>  trim(xss_clean($this->getVarString('sub_account'))),
            'bank_account_id' => trim(xss_clean($this->getVarString('bank_account_id'))),
            'label' => trim(xss_clean($this->getVarString('label'))),
        ];

        $rules = [
            'bank_account_id' => ['label' => 'Tài khoản chính', 'rules' => 'integer|is_natural'],
            'sub_account' => ['label' => 'Số tài khoản ảo', 'rules' => 'required|min_length[2]|max_length[3]|regex_match[/^[A-Z0-9]{2,3}$/]'],
            'label' => ['label' => 'Tên gợi nhớ', 'rules' => 'max_length[100]'],
        ];

        if (!$this->validateData($data, $rules)) {
            return $this->respond(['status' => false, 'message' => implode('. ', $this->validator->getErrors())]);
        }  

        $bankAccountModel = model(BankAccountModel::class);
        $bankAccountDetails = $this->getConnectedCompanyBankAccount($data['bank_account_id']);
        
        if (!is_object($bankAccountDetails)) {
            return $this->failNotFound();
        }

        $bankSubAccountModel = model(BankSubAccountModel::class);
        $existOwnBankSubAccount = $bankSubAccountModel
            ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id')
            ->where([
                'tb_autopay_bank_account.company_id' => $this->user_session['company_id'], 
                'tb_autopay_bank_sub_account.sub_account' => $data['sub_account']
            ])
            ->first();

        if (is_object($existOwnBankSubAccount)) {
            return $this->fail(['sub_account' => 'Tài khoản ảo này đã tồn tại. Bạn đã tạo tài khoản ảo này rồi.']);
        }
          
        $existBankSubAccount = $bankSubAccountModel->where(['tb_autopay_bank_sub_account.sub_account' => $data['sub_account']])->get()->getRow();

        if (is_object($existBankSubAccount)) {
            return $this->fail(['sub_account' => 'Tài khoản ảo này đã tồn tại trên hệ thống. Vui lòng chọn tên khác.']);
        }
        
        if (!is_speaker_billing_subscription() && preg_match('/^L[a-zA-Z0-9]+$/i', $data['sub_account'])) {
            return $this->fail(['sub_account' => 'Tài khoản ảo này đã tồn tại trên hệ thống. Vui lòng chọn tên khác.']);
        }
        
        $outputDeviceDecalModel = model(OutputDeviceDecalModel::class);
        
        if ($outputDeviceDecalModel->where('virtual_account_number', $data['sub_account'])->countAllResults() > 0) {
            return $this->fail(['sub_account' => 'Tài khoản ảo này đã tồn tại trên hệ thống. Vui lòng chọn tên khác.']);
        }
     
        $bankSubAccountId = $bankSubAccountModel->insert([
            'sub_account' =>  $data['sub_account'],
            'bank_account_id' => $data['bank_account_id'],
            'label' => $data['label'],
            'acc_type' => 'Virtual',
        ]);
        
        if (!$bankSubAccountId) {
            return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng thử lại']);
        }

        return $this->respond(['status' => true, 'id' => $bankSubAccountId]);
    }
    
    public function ajax_bank_account_update() 
    {
        if ($this->request->getMethod(true) != 'POST')
            return $this->failNotFound();
 
        if (!has_permission('BankAccount', 'can_edit'))
            return $this->respond(['status' => false, 'message' => 'Bạn không có quyền sửa tài khoản ngân hàng']);
 
        $data = [
            'id' => trim(xss_clean($this->request->getVar('id'))),
            'label' => trim(xss_clean($this->request->getVar('label'))),
        ];

        $rules = [
            'id' => ['required', 'integer', 'is_natural'],
            'label' => [
                'label' => 'Tên gợi nhớ',
                'rules' => ['permit_empty', 'max_length[100]']
            ]
        ];

        if (! $this->validateData($data, $rules))
            return $this->response->setJSON(['status' => false, 'message' => implode(". ", $this->validator->getErrors())]);

        $bankAccountModel = model(BankAccountmodel::class);

        $bankAccountDetails = $bankAccountModel->where(['id' => $data['id'], 'company_id' => $this->user_session['company_id']])->get()->getRow();
        
        if (!is_object($bankAccountDetails))
            return $this->response->setJSON(['status' => false, 'message' => 'Không tìm thấy tài khoản ngân hàng này']);

        $updated = $bankAccountModel->set(['label' => $data['label']])->where('id', $bankAccountDetails->id)->update();
        
        if ($updated) { 
            add_user_log(['data_id' => $data['id'], 'company_id' => $this->user_session['company_id'], 'data_type' => 'bank_account_update', 'description' => 'Sửa tài khoản ngân hàng', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);
            return $this->response->setJSON(['status' => true]);
        }
         
        return $this->response->setJSON(['status' => false, 'message' => 'Không thể cập nhật tài khoản ngân hàng!']);
    }

    public function ajax_view_va($id = '')
    {
        if (!has_permission('BankAccount', 'can_view_all'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền xem tài khoản ngân hàng']);
        
        if (!is_numeric($id)) 
            return $this->response->setJSON(['status' => false, 'message' => 'ID tài khoản ngân ảo không hợp lệ']);

        $bankSubAccountModel = model(BankSubAccountModel::class);
        $transactionsModel = slavable_model(TransactionsModel::class, 'Vpbank');

        $data['va_details'] = $bankSubAccountModel
            ->select('tb_autopay_bank_sub_account.id, tb_autopay_bank_sub_account.sub_account, tb_autopay_bank_sub_account.acc_type, tb_autopay_bank_sub_account.bank_account_id, tb_autopay_bank_sub_account.label, tb_autopay_bank_account.account_number, tb_autopay_bank_account.active, tb_autopay_bank_account.created_at, tb_autopay_bank.brand_name, tb_autopay_bank_account.account_number, tb_autopay_bank_account.account_holder_name, tb_autopay_bank.icon_path, tb_autopay_bank.logo_path, tb_autopay_bank.full_name, tb_autopay_bank_sub_account.sub_holder_name, tb_autopay_bank_sub_account.va_active')
            ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id')
            ->join('tb_autopay_bank', 'tb_autopay_bank.id=tb_autopay_bank_account.bank_id')
            ->where([
                'tb_autopay_bank_sub_account.id' => $id, 
                'tb_autopay_bank_account.company_id' => $this->user_session['company_id'],
                'tb_autopay_bank_account.bank_id' => Vpbank::BANK_ID
            ])->get()->getRow();

        $data['count_transactions'] = $transactionsModel->where([
            'sub_account' => $data['va_details']->sub_account
        ])->countAllResults();
        
        $data['last_transaction'] = $transactionsModel->where([
            'sub_account' => $data['va_details']->sub_account, 
            'accumulated != ' => ''
        ])->orderBy('transaction_date', 'DESC')->get()->getRow();
        
        if ($data['va_details']) {
            $html = view('vpbank/va_view', $data);
            return $this->response->setJSON(['status' => true, 'html' => $html]);
        }

        return $this->response->setJSON(['status' => false, 'message' => 'Không tìm thấy tài khoản ảo này']);
    }

    public function ajax_request_switch_sms_connection()
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }

        if (! $this->determineIfPersonalAllowedSwitchSmsConnection()) {
            return $this->failNotFound();
        }
 
        if (! has_permission('BankAccount', 'can_edit')) {
            return $this->respond(['status' => false, 'message' => 'Bạn chưa được cấp quyền để thực hiện thao tác này']);
        }

        $bankAccountId = trim(xss_clean($this->getVarString('id')));
        $bankAccountDetails = $this->getSwitchableSmsConnectionCompanyBankAccount($bankAccountId);
        
        if (!is_object($bankAccountDetails)) {
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng không đủ điều kiện để thực hiện thao tác này']);
        }

        if (!$this->determineIfCompanySwitchableSmsConnection()) {
            return $this->respond(['status' => false, 'message' => 'Gói dịch vụ của bạn không cho phép kết nối ngân hàng qua SMS báo số dư. Vui lòng nâng cấp gói hoặc liên hệ SePay để được hỗ trợ.']);
        }

        return $this->respond(['status' => true]);
    }

    public function ajax_confirm_switch_sms_connection()
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }
 
        if (!$this->determineIfCompanySwitchableSmsConnection()) {
            return $this->failNotFound();
        }

        if (!has_permission('BankAccount', 'can_edit')) {
            return $this->respond(['status' => false, 'message' => 'Bạn chưa được cấp quyền để thực hiện thao tác này']);
        }

        $bankAccountId = trim(xss_clean($this->getVarString('id')));
        $bankAccountDetails = $this->getSwitchableSmsConnectionCompanyBankAccount($bankAccountId);
        
        if (!is_object($bankAccountDetails)) {
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng không đủ điều kiện để thực hiện thao tác này']);
        }

        if (!$this->determineIfCompanySwitchableSmsConnection()) {
            return $this->respond(['status' => false, 'message' => 'Gói dịch vụ của bạn không cho phép kết nối Ngân hàng qua SMS báo số dư. Vui lòng nâng cấp gói hoặc liên hệ SePay để được hỗ trợ.']);
        }

        $bankAccountModel = model(BankAccountModel::class);
        $simCompanyModel = model(SimCompanyModel::class);

        $simId = trim(xss_clean($this->getVarString('sim_id')));
        $bankAccountUpdateData = [
            'bank_sms' => 1,
            'bank_api' => 0,
        ];

        if (is_numeric($simId)) {
            $simDetails = $simCompanyModel->select('tb_autopay_sim.id')
                ->join('tb_autopay_sim', 'tb_autopay_sim.id=tb_autopay_sim_company.sim_id')
                ->where([
                    'tb_autopay_sim_company.company_id' => $this->user_session['company_id'], 
                    'tb_autopay_sim.active' => 1, 
                    'tb_autopay_sim.id' => $simId
                ])
                ->first();

            if (! is_object($simDetails)) {
                return $this->respond(['status' => false, 'message' => 'SIM nhận SMS mà bạn chọn không khả dụng']);
            }

            $bankAccountUpdateData['sim_id'] = $simId;
        }

        $bankAccountUpdated = $bankAccountModel->set($bankAccountUpdateData)->update($bankAccountId);

        if (!$bankAccountUpdated) {
            return $this->respond(['status' => false, 'message' => 'Không thể chuyển đổi phương thức kết nối sang SMS Banking, vui lòng liên hệ SePay để được hỗ trợ.']);
        }

        set_alert('success', 'Đã chuyển phương thức kết nối sang SMS Banking thành công');
        add_user_log(['data_id' => $bankAccountId, 'company_id' => $this->user_session['company_id'], 'data_type' => 'bank_account_update', 'description' => 'Chuyển đổi phương thức kết nối sang SMS Banking', 'user_id' => $this->user_details->id, 'ip'=>$this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);

        return $this->respond(['status' => true]);
    }
    
    public function ajax_unlink($bankAccountId = '')
    {
        if ($this->isEnterpriseConnectRequest()) {
            return $this->handleEnterpriseAjaxUnlink($bankAccountId);
        }
        
        return $this->failNotFound();
    }

    public function authorizeCallback()
    {
        $code = trim($this->request->getGet('code'));

        if (!$code) {
            return $this->failNotFound();
        }

        try {
            $client = new VpbankClient($this->company_details->id);
            $client->authorizeLoginCode($code);
        } catch (Exception $e) {
            echo view('vpbank/failed-authorization', [
                'user_details' => $this->user_details
            ]);
            return;
        }

        $session = session();
        
        if ($session->get('bank_account_connect_context') && is_bank_box_support()) {
            echo view('vpbank/authorization', [
                'user_details' => $this->user_details
            ]);
            echo "<script>
                window.close();
            </script>";
            return;
        }

        set_alert('success', 'Đã đăng nhập VPBank NEO');

        echo "<script>
            window.opener.location = window.opener.location.href.replace('step1', 'step2');
            window.close();
        </script>";
    }

    public function loginErrorCallback()
    {
        set_alert('error', 'Đăng nhập VPBank NEO thất bại, vui lòng thử lại');
    
        echo "<script>
            window.close();
        </script>";
    }

    public function loginCancelCallback()
    {
        echo "<script>
            window.close();
        </script>";
    }

    public function otpConfirmCallback()
    {
        $sessionDataKey = trim($this->getVarString('sessionDataKey'));

        if (!$sessionDataKey) {
            return $this->failNotFound();
        }

        try {
            $client = new VpbankClient($this->company_details->id);
        } catch (Exception $e) {
            echo view('vpbank/failed-authorization', [
                'user_details' => $this->user_details
            ]);
            return;
        }

        $session = session();
        
        if ($session->get('bank_account_connect_context') && is_bank_box_support()) {
            return $this->handleOtpConfirmCallbackWithBankBox($sessionDataKey);
        }
        
        $registration = $session->get('vpbank_register');

        if (!isset($registration['request_id']) || !isset($registration['register_id']) || !isset($registration['bank_account_id'])) {
            echo view('vpbank/failed-authorization', ['user_details' => $this->user_details]);
            return;
        }

        if ($registration['request_id'] !== $sessionDataKey) {
            echo view('vpbank/failed-otp', ['user_details' => $this->user_details]);
            return;
        }

        $bankAccountModel = model(BankAccountModel::class);
        $vpbankBankAccountMetadataModel = model(VpbankBankAccountMetadataModel::class);
        $bankAccountDetails = $this->getConnectableCompanyBankAccount($registration['bank_account_id']);

        if (!$bankAccountDetails) {
            echo view('vpbank/failed-otp', ['user_details' => $this->user_details]);
            return;
        }

        try {
            $response = $client->submitRegister($registration['request_id']);
            $responseData = json_decode($response->getBody());

            if (!$responseData) {
                throw new Exception('Response data parsed failed');
            }
         
            if (property_exists($responseData, 'error')) {
                if ($responseData->error == 'ISS-010') {
                    throw new Exception('Integration type not found');
                }

                if ($responseData->error == 'ISS-008') {
                    throw new Exception('The customer already has registered for this provider');
                }

                if ($responseData->error == 'ISS-009') {
                    throw new Exception('The operation has not been approved yet');
                }
            }

            if ($vpbankBankAccountMetadataModel->where(['bank_account_id' => $bankAccountDetails->id])->countAllResults()) {
                $success = $vpbankBankAccountMetadataModel->where(['bank_account_id' => $bankAccountDetails->id])->set(['register_id' => $registration['register_id']])->update();
            } else {
                $success = $vpbankBankAccountMetadataModel->insert([
                    'bank_account_id' => $bankAccountDetails->id,
                    'register_id' => $registration['register_id']
                ]);
            }
    
            if (!$success) {
                try {
                    $client->deregister($registration['register']);
                } catch (Exception $e) {}

                echo view('vpbank/failed-otp', ['user_details' => $this->user_details]);
                return;
            }

            $bankAccountModel->set([
                'bank_api_connected' => 1, 
                'bank_api' => 1,
                'bank_sms' => 0,
                'bank_sms_connected' => 0,
            ])->update($bankAccountDetails->id);

            $this->cleanVpbankRegistrationSession();

            add_user_log(['data_id' => $bankAccountDetails->id, 'company_id' => $this->user_session['company_id'], 'data_type'=> 'bank_account_linked', 'description' => 'Liên kết tài khoản ngân hàng', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);

            set_alert('success', 'Đã xác thực OTP thành công');

            echo "<script>
                window.opener.location = window.opener.location.href.replace('step3', 'step4');
                window.close();
            </script>";
        } catch (Exception $e) {
            echo view('vpbank/failed-otp', ['user_details' => $this->user_details]);
            return;
        }
    }
    
    protected function handleOtpConfirmCallbackWithBankBox(string $sessionDataKey)
    {
        if (!is_bank_box_support()) {
            return show_404();
        }

        $bankAccountFeature = service('bankAccountFeature');
        $bankAccountFeature->withCompanyContext($this->user_session['company_id']);
        $bankAccountFeature->withBankAccountConnectContext('VPBank');
        
        /** @var VPBankBankAccountConnectContext $bankAccountConnectContext */
        $bankAccountConnectContext = $bankAccountFeature->bankAccountConnectContext();
        $bankAccountConnectContext->loadFromSession();

        if (!$bankAccountConnectContext->otpRequestId || !$bankAccountConnectContext->registerId) {
            echo view('vpbank/failed-authorization', ['user_details' => $this->user_details]);
            return;
        }

        if ($bankAccountConnectContext->otpRequestId !== $sessionDataKey) {
            echo view('vpbank/failed-otp', ['user_details' => $this->user_details]);
            return;
        }
        
        try {
            $result = $bankAccountConnectContext->confirmConnect();

            if (!$bankAccountConnectContext->isOkResponse()) {
                echo view('vpbank/failed-otp', ['user_details' => $this->user_details]);
                return;
            }

            echo "<script>
                window.close();
            </script>";
        } catch (Exception $e) {
            log_message('error', 'VPBank Controller Error: handleOtpConfirmCallbackWithBankBox: ' . $e->getMessage() . ' - ' . $e->getTraceAsString());
            
            echo view('vpbank/failed-otp', ['user_details' => $this->user_details]);
            return;
        }
    }

    public function otpErrorCallback()
    {
        set_alert('error', 'Xác thực OTP thất bại, vui lòng thử lại');
    
        echo "<script>
            window.close();
        </script>";
    }

    public function otpCloseCallback()
    {
        echo "<script>
            window.close();
        </script>";
    }
    
    public function neobizAuthorizeCallback($sessionToken = '')
    {
        $sessionToken = trim($sessionToken);
        
        if (!$sessionToken) {
            show_404();
        }
        
        if (!$this->determineIfEnterpriseAllowedApiConnection()) {
            show_404();
        }
        
        $session = session();
        $bankAccount = $session->get('vpbank_neobiz_bank_account');
        $session->remove('vpbank_neobiz_bank_account');
        
        try {
            $client = new VpbankOrgClient($sessionToken);
            
            $response = $client->getCurrentAccount();
            $responseJson = json_decode($response->getBody());
            
            if (!$responseJson) {
                throw new Exception('VPBank neobizAuthorizeCallback error: Unable to parse response data');
            }
            
            if (!property_exists($responseJson, 'ResultCode') || !property_exists($responseJson, 'ResultDesc') || !property_exists($responseJson, 'Products')) {
                throw new Exception(sprintf('VPBank neobizAuthorizeCallback error: Invalid response structure | Raw body: %s', $response->getBody()));
            }
            
            if ($responseJson->ResultCode != 'A000') {
                throw new Exception('VPBank neobizAuthorizeCallback error: ' . $responseJson->ResultCode . ' ' . $responseJson->ResultDesc);
            }
            
            $accounts = $responseJson->Products;
            
            $session->set('vpbank_neobiz_accounts', $accounts);
            $session->set('vpbank_neobiz_session_token', $sessionToken);
            
            if ($bankAccount) {
                if ($bankAccount->bank_sms) {
                    $this->request->setMethod('POST');
                    $this->request->setHeader('Content-Type', 'application/json');
                    $this->request->appendBody(json_encode([
                        'bank_account_id' => $bankAccount->id,
                        'account_number' => $bankAccount->account_number,
                    ]));

                    $response = $this->handleEnterpriseAjaxStep2();
                    
                    if ($response->getStatusCode() == 404) {
                        set_alert('error', 'Đã có lỗi xảy ra, vui lòng thử lại');
                        return redirect()->to(base_url(sprintf('vpbank/step1/%s?is_enterprise=1', $bankAccount->id)));
                    }

                    $responseJson = json_decode($response->getBody());

                    if (property_exists($responseJson, 'redirect_url') && $responseJson->redirect_url) {
                        return redirect()->to($responseJson->redirect_url);
                    }

                    if ($responseJson->status === false) {
                        set_alert('error', $responseJson->message);
                        return redirect()->to(base_url(sprintf('vpbank/step1/%s?is_enterprise=1', $bankAccount->id)));
                    }
                }
                
                return redirect()->to(base_url(sprintf('vpbank/step2/%s?is_enterprise=1', $bankAccount->id)));
            }
            
            return redirect()->to(base_url('vpbank/step2?is_enterprise=1'));
        } catch (\Throwable $e) {
            log_message('error', 'VPBank neobizAuthorizeCallback error: ' . $e->getMessage());
            set_alert('error', 'Đăng nhập VPBank NEOBiz thất bại, vui lòng thử lại');
            
            if ($bankAccount) {
                return redirect()->to(base_url(sprintf('vpbank/step1/%s?is_enterprise=1', $bankAccount->id)));
            }
            
            return redirect()->to(base_url('vpbank/step1?is_enterprise=1'));
        }
    }
    
    public function ajax_check_connected($bankAccountId = '')
    {
        if ($this->isEnterpriseConnectRequest()) {
            return $this->handleEntepriseCheckConnected($bankAccountId);
        }
        
        return $this->failNotFound();
    }

    protected function determineIfVpbankClientAccessTokenExpired()
    {
        return model(VpbankClientAccessTokenModel::class)
            ->where([
                'expires_at >' => date('Y-m-d H:i:s'), 
                'company_id' => $this->company_details->id, 
                'authorize_code !=' => ''
            ])->countAllResults() == 0;
    }

    protected function getConnectableCompanyBankAccount($bankAccountId) 
    {
        return model(BankAccountModel::class)->where([
            'id' => $bankAccountId, 
            'bank_id' => Vpbank::BANK_ID, 
            'company_id' => $this->company_details->id,
            'bank_api_connected' => 0
        ])->first();
    }

    protected function getConnectedCompanyBankAccount($bankAccountId) 
    {
        return model(BankAccountModel::class)->where([
            'id' => $bankAccountId, 
            'bank_id' => Vpbank::BANK_ID, 
            'company_id' => $this->company_details->id,
            'bank_api_connected' => 1
        ])->first();
    }

    protected function getCompanyBankAccount($bankAccountId) 
    {
        return model(BankAccountModel::class)->where([
            'id' => $bankAccountId, 
            'bank_id' => Vpbank::BANK_ID, 
            'company_id' => $this->company_details->id,
        ])->first();
    }

    protected function determineIfPersonalAllowedApiConnection()
    {
        return ($this->vpbankConfig->personalAllowedApiConnection ?? false) || is_admin();
    }

    protected function determineIfPersonalAllowedSwitchApiConnection()
    {
        return $this->vpbankConfig->personalAllowedSwitchApiConnection ?? false;
    }

    protected function determineIfPersonalAllowedSwitchSmsConnection()
    {
        return $this->vpbankConfig->personalAllowedSwitchSmsConnection ?? false;
    }

    protected function determineIfPersonalAllowedDeleteBankAccount()
    {
        return $this->vpbankConfig->personalAllowedDeleteApiBankAccount ?? false;
    }

    protected function determineIfEnterpriseAllowedApiConnection(): bool
    {
        return ($this->vpbankConfig->orgFeatureAllowedApiConnection ?? false) || is_admin();
    }

    protected function determineIfEnterpriseAllowedSwitchApiConnection(): bool
    {
        return $this->vpbankConfig->orgFeatureAllowedSwitchApiConnection ?? false;
    }

    protected function determineIfEnterpriseAllowedSwitchSmsConnection(): bool
    {
        return $this->vpbankConfig->orgFeatureAllowedSwitchSmsConnection ?? false;
    }

    protected function determineIfEnterpriseAllowedDeleteBankAccount(): bool
    {
        return $this->vpbankConfig->orgFeatureAllowedDeleteApiBankAccount ?? false;
    }

    protected function getSwitchableApiConnectionCompanyBankAccount($bankAccountId)
    {
        return model(BankAccountModel::class)->where([
            'id' => $bankAccountId, 
            'company_id' => $this->user_session['company_id'],
            'bank_id' => Vpbank::BANK_ID,
            'bank_api' => 0,
            'bank_sms' => 1,
            'active' => 1,
        ])->first();
    }

    protected function getSwitchableSmsConnectionCompanyBankAccount($bankAccountId)
    {
        return model(BankAccountModel::class)->where([
            'id' => $bankAccountId, 
            'company_id' => $this->user_session['company_id'],
            'bank_id' => Vpbank::BANK_ID,
            'bank_sms' => 0,
            'bank_api' => 1,
            'bank_api_connected' => 1,
            'active' => 1,
        ])->first();
    }

    protected function determineIfCompanySwitchableSmsConnection()
    {
        $subscriptionDetails = model(CompanySubscriptionModel::class)->where(['company_id' => $this->user_session['company_id']])->first();
        $productDetails = model(ProductModel::class)->where(['id' => $subscriptionDetails->plan_id])->first();

        return is_object($subscriptionDetails) && $productDetails->sms_allow == 1;        
    }

    protected function getVarString($name)
    {
        $value = $this->request->getVar($name) ?? $this->request->getGet($name);

        if (is_array($value) && count($value)) {
            return $value[0];
        }
        
        return is_string($value) ? $value : '';
    }

    protected function handleVpbankClientExpcetion($e, $client = null)
    {
        log_message('error', 'VPBank Controller Error:' . $e->getMessage() . ' - ' . $e->getTraceAsString());

        if ($client) $client->forceDebug();

        if (strpos($e->getMessage(), 'timed out') !== false)
            return $this->respond(['status' => false, 'message' => 'Hệ thống ngân hàng VPBank đang bận, vui lòng thử lại sau.']);

        if ($e instanceof DisableBankClientException)
            return $this->respond(['status' => false, 'message' => 'Hệ thống ngân hàng VPBank đang bảo trì, vui lòng liên hệ SePay để biết thêm thông tin chi tiết.']);    

        return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng thử lại sau hoặc liên hệ SePay để được hỗ trợ.']);
    }

    protected function cleanVpbankRegistrationSession()
    {
        $session = session();
        $session->remove('vpbank_register');
    }
    
    protected function isEnterpriseConnectRequest(): bool
    {
        return $this->request->getGet('is_enterprise') && $this->request->getGet('is_enterprise') == 1;
    }
    
    protected function handleEnterpriseStep1($bankAccountId = '')
    {
        if (!has_permission('BankAccount', 'can_add')) {
            return show_404();
        }

        $data = [
            'page_title' => 'Ngân hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'allowed_switch_api_connection' => $this->determineIfEnterpriseAllowedSwitchApiConnection()
        ];

        if (!$this->determineIfEnterpriseAllowedApiConnection()) {
            show_404();
        }

        $bankAccountId = trim(xss_clean($bankAccountId));

        $data['bank_account_details'] = $bankAccountId ? $this->getConnectableCompanyBankAccount($bankAccountId) : null;

        if ($bankAccountId && !$data['bank_account_details']) {
            show_404();
        }
        
        $bankAccountFeature = service('bankAccountFeature');

        try {
            $bankAccountFeature->withBankAccountConnectContext('VPBank', 'enterprise');
        } catch (\Throwable $e) {
            show_404();
        }
        
        /** @var VPBankBankAccountConnectContext $bankAccountConnectContext */
        $bankAccountConnectContext = $bankAccountFeature->bankAccountConnectContext();
        $authorized = $bankAccountConnectContext->authorize();
        $bankAccountConnectContext->initSession();
        
        $data['webview_url'] = $bankAccountConnectContext->webviewUrl;

        $session = session();
        $session->remove('vpbank_neobiz_session_token');
        $session->remove('vpbank_neobiz_accounts');
        
        if ($data['bank_account_details']) {
            $session->set('vpbank_neobiz_bank_account', $data['bank_account_details']);
        } else {
            $session->remove('vpbank_neobiz_bank_account');
        }

        echo view('templates/autopay/header', $data);
        echo view('vpbank/org/connect/step1', $data);
        echo view('templates/autopay/footer', $data);
    }
    
    protected function handleEnterpriseStep2($bankAccountId = '')
    {
        if (!has_permission('BankAccount', 'can_add')) {
            return show_404();
        }

        $data = [
            'page_title' => 'Ngân hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'allowed_switch_api_connection' => $this->determineIfEnterpriseAllowedSwitchApiConnection()
        ];

        if (!$this->determineIfEnterpriseAllowedApiConnection()) {
            show_404();
        }

        $bankAccountId = trim(xss_clean($bankAccountId));
        
        $data['bank_account_details'] = $bankAccountId ? $this->getCompanyBankAccount($bankAccountId) : null;

        $session = session();
        $data['accounts'] = $session->get('vpbank_neobiz_accounts');
        
        if ((!$data['bank_account_details'] || !$data['bank_account_details']->bank_api_connected) && (!$session->has('vpbank_neobiz_session_token') || (is_null($data['accounts']) && !$data['bank_account_details']))) {
            $session->remove('vpbank_neobiz_session_token');
            $session->remove('vpbank_neobiz_accounts');
            
            set_alert('error', 'Vui lòng đăng nhập VPBank NEOBiz trước.');
            return redirect()->to(base_url('vpbank/step1?is_enterprise=1'));
        }
        
        $bankAccountList = is_array($data['accounts']) && count($data['accounts']) ? model(BankAccountModel::class)
            ->where('bank_id', Vpbank::BANK_ID)
            ->whereIn('account_number', array_map(fn ($account) => $account->AccountNumber, $data['accounts']))
            ->get()->getResult() : [];
            
        $data['accounts'] = array_map(function ($account) use ($bankAccountList) {
            $bankAccount = array_values(array_filter($bankAccountList, fn ($bankAccount) => $bankAccount->account_number == $account->AccountNumber))[0] ?? null;
            
            $account->owns = $bankAccount && $bankAccount->company_id == $this->user_session['company_id'];
            $account->exists = is_object($bankAccount);
            $account->connected = $bankAccount && $bankAccount->bank_api_connected;
            
            return $account;
        }, $data['accounts'] ?? []);
        
        echo view('templates/autopay/header', $data);
        echo view('vpbank/org/connect/step2', $data);
        echo view('templates/autopay/footer', $data);
    }
    
    protected function handleEnterpriseAjaxLogout()
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }

        if (!has_permission('BankAccount', 'can_add')) {
            return $this->respond(['status' => false, 'message' => 'Bạn không có quyền thêm tài khoản ngân hàng.']);
        }

        $session = session();
        $session->remove('vpbank_neobiz_session_token');

        return $this->respond(['status' => true]);
    }
    
    protected function handleEnterpriseAjaxStep2(): Response
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }

        if (!$this->determineIfEnterpriseAllowedApiConnection()) {
            return $this->failNotFound();
        }

        if (!has_permission('BankAccount', 'can_add')) {
            return $this->respond(['status' => false, 'message' => 'Bạn không có quyền thêm tài khoản ngân hàng.']);
        }

        $data = [
            'bank_account_id' => trim(xss_clean($this->getVarString('bank_account_id'))),
            'account_number' => trim(xss_clean($this->getVarString('account_number'))),
        ];

        if (! $data['account_number']) {
            return $this->respond(['status' => false, 'message' => 'Vui lòng chọn một tài khoản ngân hàng khả dụng.']);
        }

        $bankAccountModel = model(BankAccountModel::class);
        
        $bankAccountDetails = $data['bank_account_id'] ? $this->getConnectableCompanyBankAccount($data['bank_account_id']) : null;

        if ($data['bank_account_id'] && !$bankAccountDetails) {
            return $this->failNotFound();
        }

        // if (!$bankAccountDetails && $bankAccountModel->where(['account_number' => $data['account_number'], 'bank_id' => Vpbank::BANK_ID])->countAllResults()) {
        //     return $this->respond(['status' => false, 'message' => 'Số tài khoản này đã tồn tại trên hệ thống.']);
        // }
        
        $session = session();
        $sessionToken = $session->get('vpbank_neobiz_session_token');
        $accounts = $session->get('vpbank_neobiz_accounts');
        
        if (is_null($sessionToken) || is_null($accounts)) {
            set_alert('error', 'Phiên đăng nhập VPBank NEOBiz đã hết hạn, vui lòng đăng nhập lại.');
            
            return $this->respond(['redirect_url' => base_url($bankAccountDetails ? sprintf('vpbank/step1/%s?is_enterprise=1', $bankAccountDetails->id) : 'vpbank/step1?is_enterprise=1')]);
        }
        
        try {
            $client = new VpbankOrgClient($sessionToken);
        } catch (Exception $e) {
            return $this->handleVpbankClientException($e);
        }
        
        $account = array_values(array_filter($accounts, function ($account) use ($data) {
            return $account->AccountNumber == $data['account_number'];
        }))[0] ?? null;
        
        if (!$account) {
            return $this->respond(['status' => false, 'message' => 'Số tài khoản không được chọn từ danh sách tài khoản từ VPBank NEOBiz.']);
        }
        
        $registerId = md5(uniqid() . $data['account_number']);
        $registrationType = 'both';
        
        try {
            $response = $client->registerNotify($registerId, $registrationType, [$data['account_number']]);
            
            if ($response->getStatusCode() === 401) {
                set_alert('error', 'Phiên đăng nhập VPBank NEOBiz đã hết hạn, vui lòng đăng nhập lại.');
                
                return $this->respond(['redirect_url' => base_url($bankAccountDetails ? sprintf('vpbank/step1/%s?is_enterprise=1', $bankAccountDetails->id) : 'vpbank/step1?is_enterprise=1')]);
            }
            
            $responseJson = json_decode($response->getBody());
            
            if (!$responseJson) {
                throw new Exception('Response parsed failed');
            }
         
            // {"code":"900901","message":"Invalid Credentials","description":"Invalid Credentials. Make sure you have provided the correct security credentials"}
            if (property_exists($responseJson, 'code') && $responseJson->code == '900901') {
                return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng tải lại trang và thử lại.']);
            }
            
            if (property_exists($responseJson, 'ResultCode') 
            && property_exists($responseJson, 'ResultDesc') 
            && $responseJson->ResultCode == '500'
            && $responseJson->ResultDesc == 'Session login expired') {
                $session->remove('vpbank_neobiz_session_token');
                $session->remove('vpbank_neobiz_accounts');
                
                set_alert('error', 'Phiên đăng nhập VPBank NEOBiz đã hết hạn, vui lòng đăng nhập lại.');
                
                return $this->respond(['redirect_url' => base_url($bankAccountDetails ? sprintf('vpbank/step1?%sis_enterprise=1', $bankAccountDetails->id) : 'vpbank/step1?is_enterprise=1')]);
            }

            // {"ResultCode":"A0081","ResultDesc":"List Account invalid [\"...\"]","ReferID":null}
            if (property_exists($responseJson, 'ResultCode') 
            && property_exists($responseJson, 'ResultDesc') 
            && $responseJson->ResultCode == 'A0081') {
                return $this->respond(['status' => false, 'message' => 'Tài khoản không hợp lệ để đăng ký, vui lòng liên hệ SePay để được hỗ trợ']);
            }
            
            // {"ResultCode":"A0082","ResultDesc":"List Account exist registered [\"...\"]","ReferID":null}
            if (property_exists($responseJson, 'ResultCode') 
            && property_exists($responseJson, 'ResultDesc') 
            && $responseJson->ResultCode == 'A0082'
            && strpos($responseJson->ResultDesc, 'exist registered') !== false) {
                return $this->respond(['status' => false, 'message' => 'Tài khoản đã được đăng ký trước đó, vui lòng liên hệ SePay để được hỗ trợ']);
            }
            
            // {"ResultCode":"A0082","ResultDesc":"List Account exist request [\"...\"]","ReferID":null}
            if (property_exists($responseJson, 'ResultCode') 
            && property_exists($responseJson, 'ResultDesc') 
            && $responseJson->ResultCode == 'A0082'
            && strpos($responseJson->ResultDesc, 'exist request') !== false) {
                if ($bankAccountDetails) {
                    return $this->respond(['status' => false, 'message' => 'Yêu cầu đăng ký vẫn còn hiệu lực, vui lòng liên hệ người duyệt lệnh để kiểm tra']);
                } else {
                    return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ']);
                }
            }
            
            // {"ResultCode":"A099","ResultDesc":"Cac loi khac","ReferID":null}
            if (property_exists($responseJson, 'ResultCode') 
            && property_exists($responseJson, 'ResultDesc') 
            && $responseJson->ResultCode == 'A099') {
                return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ']);
            }

            // Exception error
            if (property_exists($responseJson, 'ResultCode') 
            && property_exists($responseJson, 'ResultDesc') 
            && $responseJson->ResultCode != 'A000') {
                return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ']);
            }
            
            $bankAccountId = null;

            if ($bankAccountDetails) {
                $bankAccountId = $bankAccountDetails->id;

                $bankAccountModel->set([
                    'sim_id' => 0,
                    'bank_api' => 1,
                    'bank_sms' => 0,
                    'pending_api_connection' => 1,
                ])->update($bankAccountId);
            } else {
                $bankAccountId = $bankAccountModel->insert([
                    'company_id' => $this->company_details->id,
                    'bank_id' => Vpbank::BANK_ID,
                    'account_number' => $data['account_number'],
                    'account_holder_name' => $account->AccountName,
                    'bank_api' => 1,
                    'pending_api_connection' => 1,
                ]);

                add_user_log(['data_id' => $bankAccountId, 'company_id' => $this->user_session['company_id'], 'data_type' => 'bank_account_add', 'description' => 'Thêm tài khoản ngân hàng VPBank API', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);
            }

            if (!$bankAccountId) {
                return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng thử lại.']);
            }
            
            $vpbankEntepriseAccountModel = model(VpbankEnterpriseAccountModel::class);
            $vpbankEntepriseAccount = $vpbankEntepriseAccountModel->where(['bank_account_id' => $bankAccountId])->first();
            
            if ($vpbankEntepriseAccount) {
                $vpbankEntepriseAccountModel->set(['partner_reference_number' => $registerId])->update($vpbankEntepriseAccount->id);
            } else {
                $vpbankEntepriseAccountModel->insert([
                    'bank_account_id' => $bankAccountId,
                    'registration_type' => $registrationType,
                    'partner_reference_number' => $registerId
                ]);
            }
            
            if (!$bankAccountDetails) {
                return $this->respond(['redirect_url' => base_url('vpbank/step2/' . $bankAccountId . '?is_enterprise=1')]);
            }
            
            return $this->respond(['status' => true, 'message' => 'Đã gửi lại yêu cầu kết nối']);
        } catch (Exception $e) {
            return $this->handleVpbankClientException($e, $client);
        }
    }
    
    protected function handleEnterpriseStep3($bankAccountId = '')
    {
        if (!has_permission('BankAccount', 'can_add')) {
            return show_404();
        }

        if (!$this->determineIfEnterpriseAllowedApiConnection()) {
            show_404();
        }

        $data = [
            'page_title' => 'Ngân hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
        ];

        $bankAccountId = trim(xss_clean($bankAccountId));

        if (! is_numeric($bankAccountId)) {
            show_404();
        }

        $data['bank_account_details'] = $this->getConnectedCompanyBankAccount($bankAccountId);

        if (!$data['bank_account_details']) {
            show_404();
        }
        
        $data['settings'] = bank_account_settings($bankAccountId);

        echo view('templates/autopay/header', $data);
        echo view('vpbank/org/connect/step3', $data);
        echo view('templates/autopay/footer', $data);
    }
    
    protected function handleEnterpriseStep4($bankAccountId = '')
    {
        if (!has_permission('BankAccount', 'can_add')) {
            return show_404();
        }

        if (!$this->determineIfEnterpriseAllowedApiConnection()) {
            show_404();
        }

        $data = [
            'page_title' => 'Ngân hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
        ];

        $bankAccountId = trim(xss_clean($bankAccountId));

        if (! is_numeric($bankAccountId)) {
            show_404();
        }

        $data['bank_account_details'] = $this->getConnectedCompanyBankAccount($bankAccountId);

        if (!$data['bank_account_details']) {
            show_404();
        }
        
        echo view('templates/autopay/header', $data);
        echo view('vpbank/org/connect/step4', $data);
        echo view('templates/autopay/footer', $data);
    }
    
    protected function handleEnterpriseAjaxUnlink($bankAccountId = '')
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }

        if (!$this->determineIfEnterpriseAllowedApiConnection()) {
            return $this->failNotFound();
        }

        if (! has_permission('BankAccount', 'can_delete')) {
            return $this->respond(['status' => false, 'message' => 'Bạn không có quyền xóa tài khoản ngân hàng.']);
        }

        $bankAccountDetails = $bankAccountId ? $this->getConnectableCompanyBankAccount($bankAccountId) : null;
        
        if (!$bankAccountDetails) {
            return $this->respond(['status' => false, 'message' => 'Không tìm thấy tài khoản ngân hàng']);
        }

        DeleteCompanyBankAccountAction::run($bankAccountDetails, $this->company_details, $this->request->getIPAddress(), self::class);

        model(VpbankBankAccountMetadataModel::class)->where(['bank_account_id' => $bankAccountDetails->id])->delete();
        model(VpbankEnterpriseAccountModel::class)->where(['bank_account_id' => $bankAccountDetails->id])->delete();

        set_alert('success', 'Đã xóa tài khoản ngân hàng.');

        return $this->response->setJSON([
            'status' => true,
        ]);
    }
    
    protected function handleEntepriseCheckConnected($bankAccountId = '')
    {
        if (! has_permission('BankAccount', 'can_add')) {
            return $this->respond(['status' => false, 'message' => 'Bạn không có quyền thêm tài khoản ngân hàng.']);
        }
        
        $bankAccountDetails = $bankAccountId ? $this->getCompanyBankAccount($bankAccountId) : null;
        
        if (!$bankAccountDetails) {
            return $this->respond(['status' => false, 'message' => 'Không tìm thấy tài khoản ngân hàng']);
        }

        return $this->respond(['connected' => $bankAccountDetails->bank_api_connected ? true : false]);
    }
}
