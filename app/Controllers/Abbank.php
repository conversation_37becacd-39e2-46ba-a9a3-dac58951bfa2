<?php

namespace App\Controllers;

use App\Actions\BankAccount\DeleteCompanyBankAccountAction;
use App\Controllers\BaseController;
use App\Libraries\AbbankClient;
use App\Models\AbbankLinkedAccountModel;
use App\Models\BankAccountModel;
use App\Models\BankIntegrationOutputDeviceModel;
use App\Models\BankSubAccountModel;
use CodeIgniter\API\ResponseTrait;
use Exception;

class Abbank extends BaseController
{
    use ResponseTrait;

    public function ajax_delete_bank_account($deviceId = null, $bankAccountId = null)
    {
        if ($this->request->getMethod(true) !== 'POST') {
            return $this->failNotFound();
        }

        if (! has_permission('BankAccount', 'can_delete')) {
            return $this->respond(['status' => false, 'message' => 'Bạn không có quyền hủy liên kết tài khoản ngân hàng']);
        }

        $linkedAccount = $this->getLinkedAccount($deviceId, $bankAccountId);

        if (! $linkedAccount) {
            return $this->failNotFound();
        }

        $bankAccount = model(BankAccountModel::class)
            ->select('tb_autopay_bank_account.*, tb_autopay_bank.brand_name as bank_name')
            ->join('tb_autopay_bank', 'tb_autopay_bank.id = tb_autopay_bank_account.bank_id')
            ->where('tb_autopay_bank_account.id', $bankAccountId)
            ->where('tb_autopay_bank_account.company_id', $this->company_details->id)
            ->first();

        try {
            $client = new AbbankClient();
            $response = $client->unlinkAccount($linkedAccount->serial_number, $linkedAccount->account_number);

            $data = json_decode($response->getBody()->getContents(), true);

            if ($data['code'] == '00') {
                model(AbbankLinkedAccountModel::class)
                    ->where('output_device_id', $deviceId)
                    ->where('bank_account_id', $bankAccountId)
                    ->delete();

                $integrationModel = model(BankIntegrationOutputDeviceModel::class);

                $integrations = $integrationModel
                    ->where('output_device_id', $deviceId)
                    ->where('bank_account_id', $bankAccountId)
                    ->where('company_id', $this->company_details->id)
                    ->findAll();

                if (! empty($integrations)) {
                    $bankSubAccountIds = array_filter(array_column($integrations, 'sub_account_id'));

                    if (! empty($bankSubAccountIds)) {
                        model(BankSubAccountModel::class)->whereIn('id', $bankSubAccountIds)->delete();
                    }

                    $integrationModel->whereIn('id', array_column($integrations, 'id'))->delete();
                }

                $remainingLinkedAccounts = model(AbbankLinkedAccountModel::class)->where('bank_account_id', $bankAccountId)->countAllResults();

                if ($remainingLinkedAccounts == 0) {
                    DeleteCompanyBankAccountAction::run($bankAccount, $this->company_details, $this->request->getIPAddress(), self::class);

                    add_user_log([
                        'data_id' => $bankAccount->id,
                        'company_id' => $this->user_session['company_id'],
                        'data_type' => 'bank_account_delete',
                        'description' => "Xóa tài khoản ngân hàng {$bankAccount->bank_name} {$bankAccount->account_number}",
                        'user_id' => $this->user_details->id,
                        'ip' => $this->request->getIPAddress(),
                        'user_agent' => $this->request->getUserAgent()->getAgentString(),
                        'status' => 'Success',
                    ]);

                    set_alert('success', 'Đã hủy liên kết và xóa tài khoản ngân hàng thành công');
                }

                return $this->respond(['status' => true]);
            }

            return $this->respond([
                'status' => false,
                'message' => 'Lỗi hệ thống, vui lòng thử lại sau',
            ]);
        } catch (Exception $e) {
            return $this->handleResponseException($e);
        }
    }

    protected function getLinkedAccount($deviceId, $bankAccountId)
    {
        return model(AbbankLinkedAccountModel::class)
            ->select([
                'tb_autopay_bank_account.account_number',
                'tb_autopay_output_device.serial_number',
            ])
            ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id = tb_autopay_abbank_linked_accounts.bank_account_id')
            ->join('tb_autopay_output_device', 'tb_autopay_output_device.id = tb_autopay_abbank_linked_accounts.output_device_id')
            ->where('tb_autopay_abbank_linked_accounts.output_device_id', $deviceId)
            ->where('tb_autopay_bank_account.id', $bankAccountId)
            ->where('tb_autopay_bank_account.company_id', $this->company_details->id)
            ->where('tb_autopay_bank_account.bank_id', 19)
            ->where('tb_autopay_bank_account.bank_api_connected', true)
            ->where('tb_autopay_bank_account.active', true)
            ->first();
    }

    protected function handleResponseException(Exception $e)
    {
        switch ($e->getCode()) {
            case 400:
                $response = json_decode($e->getResponse()->getBody(), true);

                $messages = [
                    '00' => 'Thao tác của bạn đã được thực hiện thành công',
                    '01' => 'Tài khoản ngân hàng này đã được liên kết với hệ thống. Bạn có thể sử dụng ngay',
                    '02' => 'Thông tin bạn nhập chưa chính xác. Vui lòng kiểm tra lại số tài khoản, số điện thoại và thông tin cá nhân',
                    '03' => 'Tài khoản ngân hàng chưa được liên kết. Bạn cần thực hiện liên kết tài khoản trước khi sử dụng',
                    '04' => 'Hiện tại không thể tạo mã QR. Vui lòng thử lại sau ít phút',
                    '05' => 'Mã xác thực OTP không đúng. Vui lòng kiểm tra lại mã đã nhận qua tin nhắn',
                    '06' => 'Mã xác thực OTP đã hết hạn. Vui lòng yêu cầu gửi lại mã mới',
                    '07' => 'Bạn đã nhập sai mã OTP nhiều lần. Vui lòng đợi 5 phút và thử lại',
                    '08' => 'Không tìm thấy thông tin giao dịch. Vui lòng kiểm tra lại thông tin đã nhập',
                    '10' => 'Thông tin bạn nhập không hợp lệ. Vui lòng kiểm tra lại các trường thông tin',
                    '99' => 'Hệ thống đang tạm thời bận. Vui lòng thử lại sau ít phút',
                    '900001' => 'Thông tin đầu vào không hợp lệ. Vui lòng kiểm tra lại các trường thông tin',
                    '900002' => 'Không thể xác thực thông tin. Vui lòng thử lại sau ít phút',
                    '900003' => 'Chữ ký xác thực không hợp lệ. Vui lòng thử lại sau ít phút',
                    '900004' => 'Không thể tạo chữ ký xác thực. Vui lòng thử lại sau ít phút',
                    '900408' => 'Hệ thống đang tạm thời bận. Vui lòng thử lại sau ít phút',
                    '900999' => 'Đã xảy ra lỗi hệ thống. Vui lòng thử lại sau ít phút'
                ];

                $message = $messages[$response['code']] ?? 'Lỗi hệ thống, vui lòng thử lại sau';

                return $this->respond(['status' => false, 'message' => $message]);
            default:
                log_message('error', "ABBANK error: " . $e->getMessage() . ' - ' . $e->getTraceAsString());

                return $this->respond(['status' => false, 'message' => 'Lỗi hệ thống, vui lòng thử lại sau']);
        }
    }
}
