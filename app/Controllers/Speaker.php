<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Features\Store\StoreFeature;
use CodeIgniter\API\ResponseTrait;

class Speaker extends BaseController
{
    use ResponseTrait;
    
    public function ajax_index()
    {
        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])) {
            return $this->failNotFound();
        }
        
        $storeId = $this->request->getVar('store_id');
        $type = $this->request->getVar('type');

        $storeFeature = new StoreFeature();
        $storeFeature->withCompanyContext($this->user_session['company_id']);
        $speakers = $storeFeature->getStoreSpeakers($storeId ? [$storeId] : [], $type);
                
        return $this->respond([
            'data' => $speakers
        ]);
    }
}
