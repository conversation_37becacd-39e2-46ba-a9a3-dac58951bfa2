<?php

namespace App\Controllers;

use App\Models\Branch;
use App\Models\PgBranchModel;

class Branches extends BaseController
{
    public function index()
    {
        if ($this->request->isAJAX()) {
            $branchModel = model(PgBranchModel::class)->setCompanyId($this->company_details->id);
            $branches = $branchModel->getDatatables($this->request);
            $data = [];

            foreach ($branches as $branch) {
                $data[] = [
                    $branch->id,
                    $branch->name,
                    $branch->code,
                    sprintf('<span class="badge bg-%s">%s</span>', $branch->active ? 'success' : 'danger', $branch->active ? 'Hoạt động' : 'Không hoạt động'),
                    $branch->created_at,
                    $branch->updated_at,
                ];
            }

            return $this->response->setJSON([
                'draw' => $this->request->getPost('draw'),
                'recordsTotal' => $branchModel->countAll(),
                'recordsFiltered' => $branchModel->countFiltered($this->request),
                'data' => $data,
            ]);
        }

        return view('branches/index', [
            'page_title' => 'Chi nhánh',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
        ]);
    }
}
