<?php

namespace App\Controllers;

use Exception;
use App\Models\ProductModel;
use App\Libraries\BidvClient;
use App\Models\SimCompanyModel;
use App\Models\BankAccountModel;
use App\Models\TransactionsModel;
use CodeIgniter\API\ResponseTrait;
use App\Controllers\BaseController;
use App\Models\BankSubAccountModel;
use App\Models\CompanySubscriptionModel;
use App\Models\BidvEnterpriseAccountModel;
use App\Exceptions\DisableBankClientException;
use App\Models\NotificationTelegramQueueModel;
use CodeIgniter\Exceptions\PageNotFoundException;
use App\Actions\BankAccount\DeleteCompanyBankAccountAction;
use App\Models\OutputDeviceDecalModel;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use Psr\Log\LoggerInterface;

class Bidv extends BaseController
{
    use ResponseTrait;

    protected $bidvConfig;

    const BANK_ID = 9;

    public function __construct()
    {
        $this->bidvConfig = config(\Config\Bidv::class);
        $this->bidvConfig->enabled = $this->bidvConfig->enabled ?? false;
        $this->bidvConfig->allowedEnterpriseConnection = $this->bidvConfig->allowedEnterpriseConnection ?? false;
        $this->bidvConfig->enterprisePrefixIdMaxlen = $this->bidvConfig->enterprisePrefixIdMaxlen ?? 3;
        $this->bidvConfig->enterprisePrefixCharType = $this->bidvConfig->enterprisePrefixCharType ?? 'NumberAndLetter';
        $this->bidvConfig->vaPrefix = $this->bidvConfig->vaPrefix ?? 'V';
        $this->bidvConfig->vaMaxlen = $this->bidvConfig->vaMaxlen ?? 19;
        $this->bidvConfig->vaNameMinlen = $this->bidvConfig->vaNameMinlen ?? 1;
        $this->bidvConfig->vaNameMaxlen = $this->bidvConfig->vaNameMaxlen ?? 70;
        $this->bidvConfig->allowedSwitchApiConnection = $this->bidvConfig->allowedSwitchApiConnection ?? false;

        if (!$this->bidvConfig->enabled) {
            throw PageNotFoundException::forPageNotFound();
        }
    }

    public function initController(RequestInterface $request, ResponseInterface $response, LoggerInterface $logger)
    {
        parent::initController($request, $response, $logger);

        is_bank_supported_by_promotion(self::BANK_ID, $this->company_details->company_id);
    }

    protected function handleBidvClientException($e, $client = null)
    {
        log_message('error', 'BIDV Controller: ' . $e->getMessage() . ' - ' . $e->getTraceAsString());

        if ($client) $client->forceDebug();

        if ($e instanceof DisableBankClientException)
            return $this->response->setJSON(['status' => false, 'message' => 'Hệ thống ngân hàng BIDV đang bảo trì, vui lòng liên hệ SePay để biết thêm thông tin chi tiết.']);    

        return $this->response->setJSON(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng thử lại sau hoặc liên hệ SePay để được hỗ trợ.']);
    }

    public function step1()
    {
        $data = [
            'page_title' => 'Ngân hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'allowed_switch_api_connection' => $this->bidvConfig->allowedSwitchApiConnection,
        ];

        if (!has_permission('BankAccount', 'can_add')) 
            return show_404();

        $session = service('session');
        $bankAccountId = $session->get('bidv_request_switch_api_connection');
        $data['bank_account_details'] = null;

        if ($data['allowed_switch_api_connection'] && $bankAccountId) {
            $data['bank_account_details'] = $this->getSwitchableApiConnectionBankAccount($bankAccountId);
        }

        $session->remove('bidv_request_switch_api_connection');

        echo view('templates/autopay/header', $data);
        echo view('bidv/personal/step1', $data);
        echo view('templates/autopay/footer', $data);
    }

    public function ajax_step1()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (! has_permission('BankAccount', 'can_add'))
            return $this->respond(['status' => false, 'message' => 'Bạn không có quyền thêm tài khoản ngân hàng']);

        $session = service('session');

        $data = [
            'id' => trim(xss_clean($this->request->getVar('id'))),
            'account_number' => trim(xss_clean($this->request->getVar('account_number'))),
            'account_holder_name' => trim(xss_clean($this->request->getVar('account_holder_name'))),
            'phone_number' => trim(xss_clean($this->request->getVar('phone_number'))),
            'identification_number' => trim(xss_clean($this->request->getVar('identification_number'))),
            'label' => trim(xss_clean($this->request->getVar('label'))),
            'user_agreement' => (bool) $this->request->getVar('user_agreement'),
        ];

        if (!$data['user_agreement'])
            return $this->fail(['user_agreement' => 'Bạn cần đồng ý chính sách và điều khoản trước để tiếp tục.']);

        $rules = [
            'account_number' => [
                'label' => 'số tài khoản',
                'rules' => ['required', 'max_length[100]'],
            ],
            'account_holder_name' => [
                'label' => 'tên chủ tài khoản',
                'rules' => ['required', 'max_length[100]'], 
            ],
            'phone_number' => [
                'label' => 'số điện thoại',
                'rules' => ['required', 'min_length[10]', 'max_length[20]']
            ],
            'label' => [
                'label' => 'tên gợi nhớ',
                'rules' => ['permit_empty', 'max_length[100]']
            ],
        ];

        if (! $this->validateData($data, $rules)) return $this->fail($this->validator->getErrors());

        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);

        $bankAccountDetails = $data['id'] ? $this->getSwitchableApiConnectionBankAccount($data['id']) : null;

        // Skip validation account number exist when switch bank account to api conenction
        if ($this->bidvConfig->allowedSwitchApiConnection && $bankAccountDetails) {
            $data['account_number'] = $bankAccountDetails->account_number;
        } else {
            if ($bankAccountDetails = $bankAccountModel->where(['account_number' => $data['account_number'], 'bank_id' => Bidv::BANK_ID, 'company_id' => $this->user_session['company_id'], 'bank_sms' => 1])->get()->getRow()) {
                return $this->fail(['account_number' => 'Tài khoản này của bạn đang sử dụng phương thức kết nối SMS Banking', 'bank_account_id' => $bankAccountDetails->id]);
            }

            if ($bankAccountModel->where(['account_number' => $data['account_number'], 'bank_id' => Bidv::BANK_ID])->countAllResults()) {
                return $this->fail(['account_number' => 'Số tài khoản này đã tồn tại trên hệ thống', 'owner' => false]);
            }
        }

        try {
            $client = new BidvClient;
        } catch (Exception $e) {
            return $this->handleBidvClientException($e);
        }

        try {
            $client->allowErrorCodes([184]);

            $response = $client->createVAQLBH(
                '000000000000000000000', // fixed invalid VA to validate fields bellow
                $data['account_holder_name'],
                $data['account_number'],
                $data['account_holder_name'],
                $data['identification_number'],
                $data['phone_number'],
            );

            $responseData = json_decode($response->getBody());
            $errorCode = $responseData->errorCode;

            $passedBankAccountValidation = !in_array($errorCode, ['033', '040', '021']);
          
            if ($passedBankAccountValidation && !$bankAccountDetails) {
                $simModel = model(SimModel::class);
                $simCompanyModel = model(SimCompanyModel::class);

                $safeBankAccountData = array(
                    'company_id' => $this->user_session['company_id'],
                    'account_holder_name' => $data['account_holder_name'],
                    'account_number' => $data['account_number'],
                    'bank_id' => Bidv::BANK_ID,
                    'label' => $data['label'],
                    'active' => 1,
                    'bank_api' => 1,
                    'bank_api_connected' => 0,
                    'phone_number' => $data['phone_number'],
                    'identification_number' => $data['identification_number']
                );

                $sims = $simCompanyModel
                    ->select('tb_autopay_sim.id, tb_autopay_sim.sim_phonenumber')
                    ->join('tb_autopay_sim', 'tb_autopay_sim.id=tb_autopay_sim_company.sim_id')
                    ->where([
                        'tb_autopay_sim_company.company_id' => $this->user_session['company_id'], 
                        'tb_autopay_sim.active' => 1
                    ])
                    ->orderBy('tb_autopay_sim_company.created_at', 'ASC')
                    ->get()->getResult();

                if (count($sims) > 0) {
                    $safeBankAccountData['sim_id'] = $sims[0]->id;
                }

                $bankAccountId = $bankAccountModel->insert($safeBankAccountData);

                if ($bankAccountId) {
                    add_user_log(['data_id' => $bankAccountId, 'company_id' => $this->user_session['company_id'], 'data_type' => 'bank_account_add', 'description' => 'Thêm tài khoản ngân hàng BIDV API', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);

                    return $this->response->setJSON(['status' => true, 'id' => $bankAccountId]);
                }

                add_user_log(['data_id' => 0, 'company_id' => $this->user_session['company_id'], 'data_type' => 'bank_account_add', 'description' => 'Thêm tài khoản ngân hàng BIDV API', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Failed']);
                return $this->response->setJSON(['status' => false, 'message' => 'Không thể thêm tài khoản ngân hàng. Vui lòng liên hệ SePay để được hỗ trợ.']);
            }

            // Switch BIDV bank account to API connection
            if ($passedBankAccountValidation && $bankAccountDetails) {
                $apiConnected = $bankSubAccountModel->where(['bank_account_id' => $bankAccountDetails->id, 'acc_type' => 'Real'])->countAllResults() > 0;

                $bankAccountUpdated = $bankAccountModel->set([
                    'account_holder_name' => $data['account_holder_name'],
                    'phone_number' => $data['phone_number'],
                    'identification_number' => $data['identification_number'],
                    'bank_api' => 1,
                    'bank_api_connected' => $apiConnected,
                    'bank_sms' => 0,
                ])->where(['id' => $bankAccountDetails->id])->update();

                if ($bankAccountUpdated) {
                    add_user_log(['data_id' => $bankAccountDetails->id, 'company_id' => $this->user_session['company_id'], 'data_type' => 'bank_account_add', 'description' => 'Chuyển phương thức kết nối sang API', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);

                    return $this->response->setJSON(['status' => true, 'id' => $bankAccountDetails->id]);
                }

                add_user_log(['data_id' => 0, 'company_id' => $this->user_session['company_id'], 'data_type' => 'bank_account_add', 'description' => 'Chuyển phương thức kết nối sang API', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Failed']);
                return $this->response->setJSON(['status' => false, 'message' => 'Không thể chuyển sang phương thức kết nối API, vui lòng liên hệ SePay để được hỗ trợ.']);
            }
        } catch (Exception $e) {
            $errorCode = $e->getCode();

            if ($errorCode == '033')
                return $this->fail(['account_number' => 'Số tài khoản không tồn tại hoặc đã bị khóa trên hệ thống ngân hàng BIDV.']);

            if ($errorCode == '040')
                return $this->fail(['account_holder_name' => 'Tên chủ tài khoản không khớp với số tài khoản trên.']);
    
            if ($errorCode == '021')
                return $this->fail(['identification_number' => 'Số CCCD/CMND không được đăng ký với số tài khoản trên.']);

            if ($errorCode == '022')
                return $this->fail(['phone_number' => 'Số điện thoại không được đăng ký với số tài khoản trên.']);
    
            if ($errorCode == '172')
                return $this->fail(['account_number' => 'Số tài khoản không thuộc nhóm khách hàng cá nhân của ngân hàng BIDV.']);

            return $this->handleBidvClientException($e);
        }
    }

    public function step2($id = '')
    {
        $data = [
            'page_title' => 'Ngân hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'personal_va_prefix' => $this->bidvConfig->personalVaPrefix,
            'personal_va_char_type' => $this->bidvConfig->personalVaCharType,
            'personal_va_maxlen' => $this->bidvConfig->personalVaMaxlen,
            'sub_id_regex' => $this->getSubIdRegex()
        ];

        if (!has_permission('BankAccount', 'can_add'))
            show_404();

        if (!is_numeric($id))
            show_404();

        $bankAccountModel = model(BankAccountModel::class);
        $bankAccountDetails = $this->getBankAccountDetails($id);

        if (!is_object($bankAccountDetails))
            show_404();

        $data['bank_account_details'] = $bankAccountDetails;

        $transactionsModel = slavable_model(TransactionsModel::class, 'Bidv');
        $data['count_transactions'] = $transactionsModel->where([
            'id' => $bankAccountDetails->id, 
            'deleted_at' => null
        ])->countAllResults();

        if ($bankAccountDetails->bank_api_connected == 1) {
            set_alert('success', 'Tài khoản này đã mở API rồi. Bạn không cần phải liên kết lại.');
            return redirect()->to('bidv/settings/' . $id);
        }

        if ($bankAccountDetails->bank_sms == 1) {
            set_alert('error', 'Tài khoản này đang kết nối bằng phương thức SMS Banking.');
            return redirect()->to('bidv/details/' . $id);
        }
        
        echo view('templates/autopay/header', $data);
        echo view('bidv/personal/step2', $data);
        echo view('templates/autopay/footer', $data);
    }

    public function step3($id = '')
    {
        $data = [
            'page_title' => 'Ngân hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
        ];

        if (!has_permission('BankAccount', 'can_add'))
            show_404();

        if (!is_numeric($id))
            show_404();

        $bankSubAccountModel = model(BankSubAccountModel::class);
        $bankAccountDetails = $this->getBankAccountDetails($id);

        if (!$bankAccountDetails)
            show_404();

        $data['bank_account_details'] = $bankAccountDetails;
        $data['va_details'] = $bankSubAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->orderBy('id', 'desc')->first();
        $data['va_success'] = $this->request->getGet('va_success');
        
        echo view('templates/autopay/header', $data);
        echo view('bidv/personal/step3', $data);
        echo view('templates/autopay/footer', $data);
    }

    public function settings($id)
    {
        $data = [
            'page_title' => 'Cấu hình',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
        ];

        if (! has_permission('BankAccount', 'can_add')) {
            show_404();
        }

        if (! is_numeric($id)) {
            show_404();
        }

        $bankAccount = $this->getBankAccountDetails($id);

        if (! $bankAccount) {
            show_404();
        }

        $data['bankAccount'] = $bankAccount;
        $data['settings'] = bank_account_settings($bankAccount->id);
        $data['va_success'] = $this->request->getGet('va_success');

        echo view('templates/autopay/header', $data);
        echo view('bidv/personal/settings', $data);
        echo view('templates/autopay/footer', $data);
    }

    public function ajax_request_create_personal_va()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (! has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền sửa tài khoản ngân hàng']);

        $data = [
            'bank_account_id' => trim(xss_clean($this->request->getVar('bank_account_id'))),
            'sub_id' => strtoupper(trim(xss_clean($this->request->getVar('sub_id')))),
            'label' => trim(xss_clean($this->request->getVar('label'))),
        ];

        $rules = [
            'bank_account_id' => ['required'],
            'sub_id' => ['required', 'regex_match[' . $this->getSubIdRegex() . ']'],
            'label' => ['permit_empty', 'max_length[100]'],
        ];

        if (! $this->validateData($data, $rules)) return $this->fail($this->validator->getErrors());

        $bankAccountModel = model(BankAccountmodel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $bidvEnterpriseAccountModel = model(BidvEnterpriseAccountModel::class);

        $bankAccountDetails = $this->getBankAccountDetails($data['bank_account_id']);

        if (! $bankAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng không tồn tại']);

        if ($bankAccountDetails->bank_sms)
            return $this->respond(['status' => false, 'message' => 'Phương thức kết nối SMS không thể sử dụng tính năng này']);

        $bidvEnterpriseAccountDetails = $bidvEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->first();

        if ($bidvEnterpriseAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng BIDV doanh nghiệp không được sử dụng tính năng này']);
           
        if ($bankSubAccountModel->where(['sub_account' => $this->personalVa($data['sub_id'])])->first()) {
            return $this->fail(['sub_id' => 'Số VA đã được đăng ký, vui lòng sử dụng số khác.']);
        }   
     
        try {
            $session = service('session');
            $bypassSpeakerVirtualAccountCheckBuilder = model(OutputDeviceDecalModel::class)
                ->where('tb_autopay_output_device_decal.bank_id', $bankAccountDetails->bank_id)
                ->where('tb_autopay_output_device_decal.virtual_account_number', $this->personalVa($data['sub_id']))
                ->where('tb_autopay_output_device_decal.account_number', $bankAccountDetails->account_number)
                ->where('tb_autopay_output_device_decal.output_device_id !=', null);
                
            if (isset($session->get('output_device_integration')['output_device_id'])) {
                $bypassSpeakerVirtualAccountCheckBuilder->where('tb_autopay_output_device_decal.output_device_id', $session->get('output_device_integration')['output_device_id']);
            } else {
                $bypassSpeakerVirtualAccountCheckBuilder->join('tb_autopay_output_device', 'tb_autopay_output_device.id = tb_autopay_output_device_decal.output_device_id');
                $bypassSpeakerVirtualAccountCheckBuilder->where('tb_autopay_output_device.company_id', $this->user_session['company_id']);
            }
                
            $bypassSpeakerVirtualAccountCheck = $bypassSpeakerVirtualAccountCheckBuilder->countAllResults();
        } catch (\Exception $e) {
            $bypassSpeakerVirtualAccountCheck = false;
        }
            
        if (!is_speaker_billing_subscription() && !$bypassSpeakerVirtualAccountCheck && preg_match('/^' . $this->bidvConfig->personalVaPrefix . '9[a-zA-Z0-9]+$/i', $this->personalVa($data['sub_id']))) {
            return $this->fail(['sub_id' => 'Số VA đã được đăng ký, vui lòng sử dụng số khác.']);
        }

        try {
            $client = new BidvClient;
        } catch (Exception $e) {
            return $this->handleBidvClientException($e);
        }

        try {
            $response = $client->createVAQLBH(
                $data['sub_id'],
                $bankAccountDetails->account_holder_name,
                $bankAccountDetails->account_number,
                $bankAccountDetails->account_holder_name,
                $bankAccountDetails->identification_number,
                $bankAccountDetails->phone_number,
            );

            return $this->response->setJSON([
                'status' => true, 
                'message' => $this->getSentOtpAlert(),
                'otp_request' => [
                    'request_id' => trim(xss_clean($response->getHeaderLine('X-API-Interaction-ID'))),
                    'sub_id' => $data['sub_id']
                ]
            ]);
        } catch (Exception $e) {
            $errorCode = $e->getCode();

            if ($errorCode == '182')
                return $this->fail(['sub_id' => 'Số VA đã được đăng ký, vui lòng sử dụng số khác.']);

            if ($errorCode == '184')
                return $this->fail(['sub_id' => 'Số VA không được chấp nhận.']);

            if ($errorCode == '033')
                return $this->fail(['account_number' => 'Số tài khoản không tồn tại hoặc đã bị khóa trên hệ thống ngân hàng BIDV.']);

            if ($errorCode == '040')
                return $this->fail(['account_holder_name' => 'Tên chủ tài khoản không khớp với số tài khoản trên.']);
    
            if ($errorCode == '021')
                return $this->fail(['identification_number' => 'Số CCCD/CMND không được đăng ký với số tài khoản trên.']);

            if ($errorCode == '022')
                return $this->fail(['phone_number' => 'Số điện thoại không được đăng ký với số tài khoản trên.']);
    
            if ($errorCode == '060')
                return $this->respond(['status' => false, 'message' => "Bạn đã yêu cầu OTP quá nhiều lần, vui lòng thử lại sau"]);

            if ($errorCode == '172')
                return $this->fail(['account_number' => 'Số tài khoản không thuộc nhóm khách hàng cá nhân của ngân hàng BIDV.']);

            return $this->handleBidvClientException($e, $client);
        }
    }

    public function ajax_confirm_create_personal_va()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (! has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền sửa tài khoản ngân hàng']);

        $data = [
            'bank_account_id' => trim(xss_clean($this->request->getVar('bank_account_id'))),
            'sub_id' => strtoupper(trim(xss_clean($this->request->getVar('sub_id')))),
            'request_id' => trim(xss_clean($this->request->getVar('request_id'))),
            'otp' => trim(xss_clean($this->request->getVar('otp'))),
            'label' => trim(xss_clean($this->request->getVar('label'))),
        ];

        $rules = [
            'bank_account_id' => ['required'],
            'sub_id' => ['required', 'regex_match[' . $this->getSubIdRegex() . ']'],
            'request_id' => ['required'],
            'otp' => ['required'],
            'label' => ['permit_empty', 'max_length[100]'],
        ];

        if (! $this->validateData($data, $rules)) return $this->fail($this->validator->getErrors());

        $bankAccountModel = model(BankAccountmodel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $bidvEnterpriseAccountModel = model(BidvEnterpriseAccountModel::class);

        $bankAccountDetails = $this->getBankAccountDetails($data['bank_account_id']);

        if (! $bankAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng không tồn tại']);

        if ($bankAccountDetails->bank_sms)
            return $this->respond(['status' => false, 'message' => 'Phương thức kết nối SMS không thể sử dụng tính năng này']);

        $bidvEnterpriseAccountDetails = $bidvEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->first();

        if ($bidvEnterpriseAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng BIDV doanh nghiệp không được sử dụng tính năng này']);

        try {
            $client = new BidvClient;
        } catch (Exception $e) {
            return $this->handleBidvClientException($e);
        }

        try {
            $response = $client->confirmVAQLBH(
                $data['sub_id'],
                $data['request_id'],
                $data['otp']
            );

            $responseData = json_decode($response->getBody());

            $bankSubAccountId = $bankSubAccountModel->insert([
                'sub_account' => $this->personalVa($data['sub_id']),
                'sub_holder_name' => $bankAccountDetails->account_holder_name,
                'acc_type' => 'Real',
                'bank_account_id' => $bankAccountDetails->id,
                'label' => $data['label'],
                'va_active' => 1,
            ]);

            if (!$bankSubAccountId)
                return $this->response->setJSON(['status' => false, 'message' => 'Không thể thêm tài khoản ảo này. Vui lòng liên hệ SePay để được hỗ trợ.']);

            if (!$bankAccountDetails->bank_api_connected) {
                $bankAccountModel->set(['bank_api_connected' => 1])->update($bankAccountDetails->id);
            }

            return $this->response->setJSON(['status' => true, 'id' => $bankSubAccountId]);
        } catch (Exception $e) {
            $errorCode = $e->getCode();

            if (in_array($errorCode, ['018']))
                return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng tải lại trang']);

            if (in_array($errorCode, ['004']))
                return $this->fail(['otp' => 'OTP đã hết hiệu lực, vui lòng lấy OTP mới.']);

            if (in_array($errorCode, ['005']))
                return $this->fail(['otp' => 'OTP không chính xác.']);

            if (in_array($errorCode, ['061']))
                return $this->fail(['otp' => 'Bạn nhập sai quá nhiều lần, vui lòng lấy lại OTP mới.']);

            return $this->handleBidvClientException($e);
        }
    }

    public function ajax_check_trans() 
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!has_permission('BankAccount', 'can_add'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền thực hiện thao tác này']);

        $bankAccountId = trim(xss_clean($this->request->getVar('bank_account_id')));

        if (!is_numeric($bankAccountId))
            show_404();

        $bankAccountDetails = $this->getBankAccountDetails($bankAccountId);

        if (!is_object($bankAccountDetails))
            show_404();

        $session = session();

        if (!$session->get('checking_transaction_date'))
            $session->set('checking_transaction_date', date('Y-m-d H:i:s'));

        $checkingTransactionDate = $session->get('checking_transaction_date');
        $transactionsModel = slavable_model(TransactionsModel::class, 'Bidv');
        $lastTransaction = $transactionsModel
            ->select('id, amount_in, account_number, sub_account, transaction_content, transaction_date')
            ->where([
                'deleted_at' => null, 
                'parser_status' => 'Success', 
                'bank_account_id' => $bankAccountId, 
                'source' => 'BankAPINotify', 
                'transaction_date >= ' => $checkingTransactionDate
            ])
            ->orderBy('id', 'DESC')
            ->get()->getRow();

        if (!is_object($lastTransaction))
            return $this->response->setJSON(['status' => false]);

        $session->remove('checking_transaction_date');

        return $this->response->setJSON(['status' => true, 'last_transaction' => $lastTransaction]);
    }

    public function details($id = '')
    { 
        $data = [
            'page_title' => 'Ngân hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'allowed_delete_bank_account' => $this->bidvConfig->allowedDeleteBankAccount ?? false,
            'allowed_switch_sms_connection' => $this->bidvConfig->allowedSwitchSmsConnection ?? false,
            'allowed_switch_api_connection' => $this->bidvConfig->allowedSwitchApiConnection ?? false,
            'allowed_enterprise_connection' => $this->bidvConfig->allowedEnterpriseConnection ?? false,
            'api_connection_visibility' => $this->bidvConfig->apiConnectionVisibility ?? false,
            'enterprise_prefix_id_maxlen' => $this->bidvConfig->enterprisePrefixIdMaxlen ?? 3,
            'enterprise_prefix_char_type' => $this->bidvConfig->enterprisePrefixCharType ?? 'NumberAndLetter'
        ];

        if (!has_permission('BankAccount', 'can_view_all'))
            show_404();

        if (!is_numeric($id))
            show_404();

        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $transactionsModel = slavable_model(TransactionsModel::class, 'Bidv');
        $simCompanyModel = model(SimCompanyModel::class);
        $bidvEnterpriseAccountModel = model(BidvEnterpriseAccountModel::class);

        $bankAccountDetails = $bankAccountModel
            ->select('tb_autopay_bank_account.id, tb_autopay_bank_account.account_number, tb_autopay_bank_account.label, tb_autopay_bank_account.active, tb_autopay_bank_account.created_at, tb_autopay_bank.full_name, tb_autopay_bank.brand_name, tb_autopay_bank_account.account_number, tb_autopay_bank_account.account_holder_name, tb_autopay_bank.icon_path, tb_autopay_bank.logo_path, tb_autopay_sim.sim_phonenumber, tb_autopay_bank_account.last_transaction, tb_autopay_bank_account.accumulated,tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.bank_id,tb_autopay_bank_account.bank_sms, tb_autopay_bank_account.bank_sms_connected, tb_autopay_bank_account.bank_api, tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.identification_number, tb_autopay_bank_account.phone_number, tb_autopay_bank_account.sim_id')
            ->join('tb_autopay_bank', 'tb_autopay_bank.id=tb_autopay_bank_account.bank_id')
            ->join('tb_autopay_sim', 'tb_autopay_sim.id=tb_autopay_bank_account.sim_id', 'left')
            ->where([
                'tb_autopay_bank_account.company_id' => $this->user_session['company_id'], 
                'tb_autopay_bank_account.id' => $id,
                'tb_autopay_bank_account.bank_id' => 9
            ])->get()->getRow();
        
        if (!is_object($bankAccountDetails))
            show_404();
       
        $bidvEnterpriseAccountDetails = $bidvEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->get()->getRow();

        if (!$bidvEnterpriseAccountDetails && $bankAccountDetails->bank_api_connected == 0 && $bankAccountDetails->bank_api == 1)
            return redirect()->to(base_url('bidv/step2/' . $bankAccountDetails->id));

        $data['bank_account_details'] = $bankAccountDetails;

        $data['is_enterprise_account'] = !!$bidvEnterpriseAccountDetails;

        $data['count_subs'] = $bankSubAccountModel->where(['bank_account_id' => $id, 'deleted_at' => null])->countAllResults();
       
        $data['bank_sms_info'] = get_bank_sms_info($data['bank_account_details']->brand_name);
       
        $data['count_transactions'] = $transactionsModel->where(['bank_account_id' => $data['bank_account_details']->id, 'deleted_at' => NULL])->countAllResults();
       
        $data['count_sms_transactions'] = $transactionsModel->where(['bank_account_id' => $data['bank_account_details']->id, 'deleted_at' => NULL, 'source' => 'SMS'])->countAllResults();
       
        $data['sims'] = $simCompanyModel
            ->select('tb_autopay_sim.id, tb_autopay_sim.sim_phonenumber')
            ->join('tb_autopay_sim', 'tb_autopay_sim.id=tb_autopay_sim_company.sim_id')
            ->where(['tb_autopay_sim_company.company_id' => $this->user_session['company_id'], 'tb_autopay_sim.active' => 1])
            ->orderBy('tb_autopay_sim_company.created_at', 'DESC')
            ->get()->getResult();

        //data QR
        $data['bank_sub_accounts_custom']=[];
        if(!empty($bankAccountDetails)){    
            $data['bank_sub_accounts_custom'] = $bankSubAccountModel->select("tb_autopay_bank_sub_account.id,tb_autopay_bank_sub_account.acc_type, tb_autopay_bank.brand_name,tb_autopay_bank.icon_path, tb_autopay_bank_account.account_holder_name, tb_autopay_bank_account.account_number, tb_autopay_bank_sub_account.sub_account, tb_autopay_bank_sub_account.label")
                ->join("tb_autopay_bank_account", "tb_autopay_bank_account.id = tb_autopay_bank_sub_account.bank_account_id")
                ->join("tb_autopay_bank", "tb_autopay_bank.id = tb_autopay_bank_account.bank_id", "left")
                ->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id']])
                ->where('tb_autopay_bank_account.id', $bankAccountDetails->id) 
                ->get()
                ->getResultArray();
        }    

        echo view('templates/autopay/header', $data);
        if ($data['bank_account_details']->bank_api && $data['is_enterprise_account']) {
            $data['prefix_id'] = $this->prefixId($bidvEnterpriseAccountDetails);
            $data['va_suffix_maxlen'] = $this->bidvConfig->vaMaxlen - strlen($data['prefix_id']);
            $data['va_name_minlen'] = $this->bidvConfig->vaNameMinlen;
            $data['va_name_maxlen'] = $this->bidvConfig->vaNameMaxlen;
            $data['custom_va_name'] = $bidvEnterpriseAccountDetails->custom_va_name ?? false;

            $data['allowed_delete_bank_account'] = $data['allowed_delete_bank_account'] && $data['custom_va_name'];

            echo view('bidv/enterprise/details', $data);
        } else if ($data['bank_account_details']->bank_api && !$data['is_enterprise_account']) {
            $data['personal_va_prefix'] = $this->bidvConfig->personalVaPrefix;
            $data['va_suffix_maxlen'] = $this->bidvConfig->personalVaMaxlen - strlen($data['personal_va_prefix']);
            $data['personal_va_char_type'] = $this->bidvConfig->personalVaCharType;
            $data['personal_va_maxlen'] = $this->bidvConfig->personalVaMaxlen;
            $data['sub_id_regex'] = $this->getSubIdRegex();

            echo view('bidv/personal/details', $data);
        } else {
            echo view('bidv/sms/details', $data);
        }
        echo view('templates/autopay/footer', $data);
    }

    public function ajax_va_list()
    {
        if (!has_permission('BankAccount', 'can_view_all'))
            show_404();
     
        if ($this->request->getMethod(true) != 'POST')
            return '';

        $bankAccountId = $this->request->getGet('bank_account_id');

        if (!is_numeric($bankAccountId))
            $bankAccountId = false;
 
        if ($bankAccountId) {
            $bankAccountModel = model(BankAccountModel::class);
            $bankAccountDetails = $bankAccountModel->where('id', $bankAccountId)->get()->getRow();
            $bidvEnterpriseAccountDetails = model(BidvEnterpriseAccountModel::class)->where('bank_account_id', $bankAccountId)->first();
            $accType = $bankAccountDetails ? ($bankAccountDetails->bank_api ? 'Real' : 'Virtual') : null;
        } else {
            $bankAccountDetails = null;
            $accType = null;
            $bidvEnterpriseAccountDetails = null;
        }

        $bankSubAccountModel = model(BankSubAccountModel::class);
        $bankSubAccounts = $bankSubAccountModel->getDatatables($this->user_session['company_id'], $bankAccountId, $accType);
        $data = [];
        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');
        $canEdit = has_permission('BankAccount', 'can_edit');
        $canDelete = has_permission('BankAccount', 'can_delete');
 
        foreach ($bankSubAccounts as $bankSubAccount) {
            $no++;
            $row = array();
            $actionsBtnHtml = '<div class="d-flex align-items-center" style="gap: 0.5rem;">';

            if ($canEdit ) {
                if ($accType == 'Real' && !$bidvEnterpriseAccountDetails) {
                    $actionsBtnHtml .=  '<a href="' . base_url('banksubaccount/qrcode/' . $bankSubAccount->id . '?print=yes') . '" target="_blank" class="btn btn-sm btn-outline-primary">
                        <i class="bi bi-printer mt-2"></i> In Standee QR
                    </a>';
                }

                if ($bankSubAccount->acc_type == 'Virtual' || ($bankSubAccount->acc_type == 'Real' && $bankAccountDetails->bank_api == 1 && !$bidvEnterpriseAccountDetails) || ($bankSubAccount->acc_type == 'Real' && $bankAccountDetails->bank_api == 1 && $bidvEnterpriseAccountDetails && $bidvEnterpriseAccountDetails->custom_va_name)) {
                    $actionsBtnHtml .= "<a href='javascript:;' onclick='edit_va(" . esc($bankSubAccount->id) . ")' class='btn btn-sm btn-outline-warning'><i class='bi bi-pencil'></i> Sửa</a>";
                }

                if ($bankSubAccount->acc_type == 'Real') {
                    if ($bankSubAccount->va_active) {
                        $actionsBtnHtml .=  "<button type='button' onclick='disable_va(" . esc($bankSubAccount->id) . ")' class='d-inline-flex align-items-center btn btn-sm btn-outline-dark btn-disable-va-".$bankSubAccount->id."' style='gap: 0.25rem'><div class='spinner-border text-danger loader' style='width: 10px; height: 10px; display: none;' role='status'></div> <i class='bi bi-toggle-off'></i> Vô hiệu hoá</button>";
                    } else {
                        $actionsBtnHtml .=  "<button type='button' onclick='enable_va(" . esc($bankSubAccount->id) . ")' class='d-inline-flex align-items-center btn btn-sm btn-outline-primary btn-enable-va-".$bankSubAccount->id."' style='gap: 0.25rem'><div class='spinner-border text-primary loader' style='width: 10px; height: 10px; display: none;' role='status'></div> <i class='bi bi-toggle-off'></i> Kích hoạt</button>";
                    }
                }
            }

            if ($canDelete && ($bankSubAccount->acc_type == 'Virtual' || ($bankSubAccount->acc_type == 'Real' && $bankAccountDetails->bank_api == 1)))
                $actionsBtnHtml .= "<a href='javascript:;' onclick='delete_va(" . esc($bankSubAccount->id) . ")' class='btn btn-sm btn-outline-danger'><i class='bi bi-trash3'></i> Xóa</a>";
                 
            $actionsBtnHtml .= '</div>';
                
            $row[] = $no;
            $row[] = $bankSubAccount->id;
            $row[] = "<a href='javascript:;' onclick='view_va("  . esc($bankSubAccount->id) . ")'>" . esc($bankSubAccount->sub_account) . ($bankSubAccount->acc_type == 'Real' ? " <i class='bi bi-patch-check-fill'></i>" : '') . " </a>";
          
            if ($accType == 'Real') {
                if ($bankSubAccount->va_active == 1)
                    $row[] = "<span class='text-success'>Hoạt động</span>";
                else
                    $row[] = "<span class='text-danger'>Tạm ngưng</span>";
            }

            if ($accType == 'Real' && $bidvEnterpriseAccountDetails) {
                $row[] = esc($bankSubAccount->sub_holder_name);
            } else {
                $row[] = esc($bankSubAccount->label);
            }

            $row[] = esc($bankSubAccount->created_at);
            $row[] = $actionsBtnHtml;
            $data[] = $row;
        }
 
        return $this->response->setJSON([
            'draw' => $draw,
            'recordsTotal' => $bankSubAccountModel->countAll($this->user_session['company_id'], $bankAccountId, $accType),
            'recordsFiltered' => $bankSubAccountModel->countFiltered($this->user_session['company_id'], $bankAccountId, $accType),
            'data' => $data,
        ]);
    }

    public function ajax_request_switch_personal_api_connection()
    {
        if (!$this->bidvConfig->allowedSwitchApiConnection)
            return $this->failNotFound();
        
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        if (!has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn chưa được cấp quyền để thực hiện thao tác này']);

        $bankAccountId = trim(xss_clean($this->request->getPost('bank_account_id')));
        $bankAccountDetails = $this->getSwitchableApiConnectionBankAccount($bankAccountId);
        
        if (!is_object($bankAccountDetails))
            return $this->response->setJSON(['status' => false, 'message' => 'Tài khoản ngân hàng không đủ điều kiện']);

        $bankAccountModel = model(BankAccountModel::class);

        $session = session();
        $session->set('bidv_request_switch_api_connection', $bankAccountDetails->id);
        $redirectTo = base_url('bidv/step1');
        
        return $this->response->setJSON(['status' => true, 'redirect_to' => $redirectTo]);
    }

    public function ajax_send_enterprise_connection_request()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!$this->bidvConfig->allowedEnterpriseConnection || !$this->bidvConfig->enabled)
            return show_404();

        if (!has_permission('BankAccount', 'can_add'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn chưa được cấp quyền để thực hiện thao tác này']);
    
        if (! $this->validate([
            'company_name' => 'required|min_length[5]|max_length[100]',
            'has_bidv_account' => 'required',
            'phone_number' => 'required|min_length[10]|max_length[20]',
            'account_number' => [
                'rules' => $this->request->getVar('has_bidv_account') == 1 ? 'required|max_length[50]' : 'permit_empty|max_length[50]',
            ],
            'branch_name' => [
                'rules' => $this->request->getVar('has_bidv_account') == 1 ? 'required|max_length[100]' : 'permit_empty|max_length[100]',
            ],
            'notes' => 'permit_empty'
        ])) return $this->fail($this->validator->getErrors());

        $data = [
            'company_name' => trim($this->request->getVar('company_name')),
            'phone_number' => trim($this->request->getVar('phone_number')),
            'has_bidv_account' => $this->request->getVar('has_bidv_account'),
            'account_number' => trim($this->request->getVar('account_number')),
            'branch_name' => trim($this->request->getVar('branch_name')),
            'notes' => trim($this->request->getVar('notes'))
        ];

        $message = '
------------------------------

Có yêu cầu kết nối API BIDV doanh nghiệp mới:

#️⃣ Tên cá nhân/tổ chức: ' . $data['company_name'] . '

ℹ️ Đã có tài khoản BIDV: ' . ($data['has_bidv_account'] ? 'Đã có' : 'Chưa') . '

📞 Số điện thoại liên hệ: ' . $data['phone_number'] . '

' . ($data['has_bidv_account'] ? '🏦 Số tài khoản BIDV: ' . $data['account_number'] . '

📍 Chi nhánh: ' . $data['branch_name'] . '

' : '') . '⏰ Thực hiện lúc: ' . date("Y-m-d H:i:s") . '

✍🏻 Ghi chú: ' . $data['notes'] . '

------------------------------
        ';

        $telegramQueueModel = model(NotificationTelegramQueueModel::class);
        $telegramQueueModel->insert([
            'chat_id' => $this->bidvConfig->telegramChatId,
            'status' => 'Pending',
            'message' => $message
        ]);

        return $this->response->setJSON([
            'status' => true,
            'message' => 'SePay đã nhận được yêu cầu của bạn và sẽ liên hệ lại trong thời gian sớm nhất',
        ]);
    }

    public function ajax_switch_enterprise_api_connection()
    {
        if (!$this->bidvConfig->allowedSwitchApiConnection)
            return show_404();
        
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        if (!has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn chưa được cấp quyền để thực hiện thao tác này']);

        $bankAccountId = trim(xss_clean($this->request->getPost('bank_account_id')));
        $bankAccountDetails = $this->getSwitchableApiConnectionBankAccount($bankAccountId);
        
        if (!is_object($bankAccountDetails))
            return $this->response->setJSON(['status' => false, 'message' => 'Tài khoản ngân hàng không đủ điều kiện']);

        $bankAccountModel = model(BankAccountModel::class);
        $bidvEnterpriseAccountModel = model(BidvEnterpriseAccountModel::class);
        $isEnterpriseAccountDetails = $bidvEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->first();

        if (!$isEnterpriseAccountDetails) {
            if ($this->bidvConfig->enterprisePrefixCharType === 'OnlyNumber') {
                $prefixIdRule = 'regex_match[/^[0-9]{1,' . $this->bidvConfig->enterprisePrefixIdMaxlen . '}$/]';
            } else if ($this->bidvConfig->enterprisePrefixCharType === 'OnlyLetter') {
                $prefixIdRule = 'regex_match[/^[A-Z]{1,' . $this->bidvConfig->enterprisePrefixIdMaxlen . '}$/]';
            } else {
                $prefixIdRule = 'regex_match[/^[A-Z0-9]{1,' . $this->bidvConfig->enterprisePrefixIdMaxlen . '}$/]';
            }

            if (! $this->validate([
                'va_prefix' => ['required', 'in_list[V,96]'],
                'prefix_id' => ['required', $prefixIdRule],
            ])) return $this->fail($this->validator->getErrors());
    
            $prefixId = trim(xss_clean(strtoupper($this->request->getVar('prefix_id'))));
            $vaPrefix = trim(xss_clean($this->request->getVar('va_prefix')));
    
            if ($bidvEnterpriseAccountModel->where('prefix_id', $prefixId)->countAllResults()) {
                return $this->fail(['prefix_id' => 'Mã định danh đã tồn tại.']);
            }

            $bidvEnterpriseAccountModel->insert(['bank_account_id' => $bankAccountDetails->id, 'prefix_id' => $prefixId, 'va_prefix' => $vaPrefix]);
        }

        $updated = $bankAccountModel
            ->set([
                'bank_api_connected' => 1, 
                'bank_api' => 1, 
                'bank_sms' => 0,
                'accumulated' => 0,
            ])->update($bankAccountDetails->id);

        if ($updated) {
            add_user_log(['data_id' => $bankAccountDetails->id, 'company_id' => $this->user_session['company_id'], 'data_type' => 'bank_account_add', 'description' => 'Chuyển phương thức kết nối sang API', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);
            $redirectTo = base_url('bidv/details/' . $bankAccountDetails->id);

            return $this->respond(['status' => true, 'redirect_to' => $redirectTo]);
        }

        return $this->respond(['status' => false]);
    }

    public function ajax_connect_enterprise()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!is_admin()) {
            return show_404();
        }

        if (!$this->bidvConfig->allowedEnterpriseConnection || !$this->bidvConfig->enabled)
            return show_404();

        if (!has_permission('BankAccount', 'can_add'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn chưa được cấp quyền để thực hiện thao tác này']);
    
        if ($this->bidvConfig->enterprisePrefixCharType === 'OnlyNumber') {
            $prefixIdRule = 'regex_match[/^[0-9]{1,' . $this->bidvConfig->enterprisePrefixIdMaxlen . '}$/]';
        } else if ($this->bidvConfig->enterprisePrefixCharType === 'OnlyLetter') {
            $prefixIdRule = 'regex_match[/^[A-Z]{1,' . $this->bidvConfig->enterprisePrefixIdMaxlen . '}$/]';
        } else {
            $prefixIdRule = 'regex_match[/^[A-Z0-9]{1,' . $this->bidvConfig->enterprisePrefixIdMaxlen . '}$/]';
        }

        if (! $this->validate([
            'account_number' => [
                'label' => 'số tài khoản',
                'rules' => 'required|max_length[250]',
            ],
            'account_holder_name' => 'required|max_length[250]',
            'va_prefix' => ['required', 'in_list[V,96]'],
            'prefix_id' => ['required', $prefixIdRule],
            'label' => 'permit_empty|max_length[100]'
        ])) return $this->fail($this->validator->getErrors());

        $bankAccountModel = model(BankAccountModel::class);
        $bidvEnterpriseAccountModel = model(BidvEnterpriseAccountModel::class);
        $prefixId = trim(xss_clean(strtoupper($this->request->getVar('prefix_id'))));
        $accountNumber = trim(xss_clean($this->request->getVar('account_number')));

        if ($bankAccountModel->where(['bank_id' => 9, 'account_number' => $accountNumber])->countAllResults()) {
            return $this->fail(['account_number' => 'Số tài khoản đã tồn tại.']);
        }
        
        if ($bidvEnterpriseAccountModel->where('prefix_id', $prefixId)->countAllResults()) {
            return $this->fail(['prefix_id' => 'Mã định danh đã tồn tại.']);
        }

        $bankAccountModel->skipApplyReferral();
        $bankAccountId = $bankAccountModel->insert([
            'company_id' => $this->user_session['company_id'],
            'account_holder_name' => remove_accents(trim(xss_clean($this->request->getVar('account_holder_name'))), true),
            'account_number' => $accountNumber,
            'bank_id' => 9,
            'label' => trim(xss_clean($this->request->getVar('label'))),
            'active' => 1,
            'bank_api' => 1,
            'bank_api_connected' => 1,
        ]);

        if ($bankAccountId) {
            $bidvEnterpriseAccountModel->insert(['bank_account_id' => $bankAccountId, 'prefix_id' => $prefixId, 'va_prefix' => trim(xss_clean(strtoupper($this->request->getVar('va_prefix'))))]);
            
            add_user_log(['data_id' => $bankAccountId, 'company_id' => $this->user_session['company_id'], 'data_type' => 'bank_account_add', 'description' => 'Thêm tài khoản ngân hàng BIDV API', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);
            return $this->response->setJSON(['status' => true, 'id' => $bankAccountId]);
        }

        add_user_log(['data_id' => 0, 'company_id' => $this->user_session['company_id'], 'data_type' => 'bank_account_add', 'description' => 'Thêm tài khoản ngân hàng BIDV API', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Failed']);
        return $this->response->setJSON(['status' => false, 'message' => 'Không thể thêm tài khoản ngân hàng. Vui lòng liên hệ SePay để được hỗ trợ.']);
    }

    public function ajax_view_va($id = null)
    {
        if (!has_permission('BankAccount', 'can_view_all'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền xem tài khoản ngân hàng']);
        
        if (!is_numeric($id)) 
            return $this->response->setJSON(['status' => false, 'message' => 'ID tài khoản ngân ảo không hợp lệ']);

        $bankSubAccountModel = model(BankSubAccountModel::class);
        $transactionsModel = slavable_model(TransactionsModel::class, 'Bidv');
        $bidvEnterpriseAccountModel = model(BidvEnterpriseAccountModel::class);

        $data['va_details'] = $bankSubAccountModel
            ->select('tb_autopay_bank_sub_account.id, tb_autopay_bank_sub_account.sub_account, tb_autopay_bank_sub_account.acc_type, tb_autopay_bank_sub_account.bank_account_id, tb_autopay_bank_sub_account.label, tb_autopay_bank_account.account_number, tb_autopay_bank_account.active, tb_autopay_bank_account.created_at, tb_autopay_bank.brand_name, tb_autopay_bank_account.account_number, tb_autopay_bank_account.account_holder_name, tb_autopay_bank.icon_path, tb_autopay_bank.logo_path, tb_autopay_bank.full_name, tb_autopay_bank_sub_account.sub_holder_name, tb_autopay_bank_sub_account.va_active')
            ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id')
            ->join('tb_autopay_bank', 'tb_autopay_bank.id=tb_autopay_bank_account.bank_id')
            ->where([
                'tb_autopay_bank_sub_account.id' => $id, 
                'tb_autopay_bank_account.company_id' => $this->user_session['company_id'],
                'tb_autopay_bank_account.bank_id' => 9
            ])->get()->getRow();

        $data['count_transactions'] = $transactionsModel->where([
            'sub_account' => $data['va_details']->sub_account
        ])->countAllResults();
        
        $data['last_transaction'] = $transactionsModel->where([
            'sub_account' => $data['va_details']->sub_account, 
            'accumulated != ' => ''
        ])->orderBy('transaction_date', 'DESC')->get()->getRow();

        $bidvEnterpriseAccountDetails = $bidvEnterpriseAccountModel->where(['bank_account_id' => $data['va_details']->bank_account_id])->get()->getRow();
        
        if ($data['va_details']) {
            $html = view($bidvEnterpriseAccountDetails ? 'bidv/enterprise/va_view' : 'bidv/personal/va_view', $data);
            return $this->response->setJSON(['status' => true, 'html' => $html]);
        }

        return $this->response->setJSON(['status' => false, 'message' => 'Không tìm thấy tài khoản ảo này']);
    }

    public function ajax_add_real_va()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!has_permission('BankAccount', 'can_add'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền thêm tài khoản ngân hàng']);
 
        $data = [
            'bank_account_id' => trim(xss_clean($this->request->getVar('bank_account_id'))),
            'sub_id' => trim(strtoupper(xss_clean($this->request->getVar('sub_id')))),
        ];
        
        $bankAccountModel = model(BankAccountModel::class);
        $bankAccountDetails = $bankAccountModel
            ->select('id, active, company_id, bank_id, account_holder_name')
            ->where([
                'active' => 1,
                'bank_id' => 9,
                'id' => $data['bank_account_id'],
                'bank_api' => 1,
                'bank_api_connected' => 1,
                'company_id' => $this->user_session['company_id'],
            ])->get()->getRow();
        
        if (!is_object($bankAccountDetails))
            show_404();

        $bidvEnterpriseAccountModel = model(BidvEnterpriseAccountModel::class);
        $bidvEnterpriseAccountDetails = $bidvEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->get()->getRow();

        if (!$bidvEnterpriseAccountDetails) {
            return $this->response->setJSON(['status' => false, 'message' => 'Chưa hỗ trợ thêm VA cho tài khoản ngân hàng này. Vui lòng liên hệ SePay để được hỗ trợ.']);
        }

        $validation = \Config\Services::validation();

        $prefixId = $this->prefixId($bidvEnterpriseAccountDetails);
        $subIdRule = 'regex_match[/^[A-Z0-9]{1,' . ($this->bidvConfig->vaMaxlen - strlen($prefixId)) . '}$/]';

        $rules = [
            'bank_account_id' => ['label' => 'Tài khoản chính', 'rules' => 'integer|is_natural'],
            'sub_id' => ['label' => 'Số tài khoản ảo', 'rules' => ['required', $subIdRule]],
        ];

        if ($bidvEnterpriseAccountDetails->custom_va_name) {
            $data['sub_holder_name'] = trim(xss_clean(remove_accents($this->request->getVar('sub_holder_name'), true)));
            $subHolderNameRule = 'regex_match[/^[A-Z0-9\s]{' . $this->bidvConfig->vaNameMinlen . ',' . $this->bidvConfig->vaNameMaxlen . '}$/]';
            $rules['sub_holder_name'] = ['label' => 'Tên hiển thị', 'rules' => ['required', $subHolderNameRule]];
        } else {
            $data['sub_holder_name'] = substr(trim($bankAccountDetails->account_holder_name), 0, 70);
        }

        if (!$this->validateData($data, $rules, ['bank_account_id' => ['integer' =>'Bạn chưa chọn tài khoản chính']])) {
            return $this->response->setJSON(['status' => false, 'message' => implode('. ', $validation->getErrors())]);
        }  

        $data['sub_account'] = $prefixId . $data['sub_id'];

        $bankSubAccountModel = model(BankSubAccountModel::class);

        $existOwnBankSubAccount = $bankSubAccountModel
            ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id')
            ->where([
                'tb_autopay_bank_account.company_id' => $this->user_session['company_id'], 
                'tb_autopay_bank_sub_account.sub_account' => $data['sub_account']
            ])
            ->get()->getRow();

        if (is_object($existOwnBankSubAccount))
            return $this->fail(['sub_id' => 'Tài khoản ảo này đã tồn tại. Bạn đã tạo tài khoản ảo này rồi.']);
          
        $existBankSubAccount = $bankSubAccountModel->where(['tb_autopay_bank_sub_account.sub_account' => $data['sub_account']])->get()->getRow();

        if (is_object($existBankSubAccount))
            return $this->fail(['sub_id' => 'Tài khoản ảo này đã tồn tại trên hệ thống. Vui lòng chọn tên khác.']);
     
        $bankSubAccountId = $bankSubAccountModel->insert([
            'sub_account' =>  $data['sub_account'],
            'sub_holder_name' => $data['sub_holder_name'],
            'bank_account_id' => $data['bank_account_id'],
            'acc_type' => 'Real',
        ]);
        
        if (!$bankSubAccountId)
            return $this->response->setJSON(['status' => false, 'message' => 'Không thể thêm tài khoản ảo này. Vui lòng liên hệ SePay để được hỗ trợ.']);

        return $this->response->setJSON(['status' => true, 'id' => $bankSubAccountId]);
    }

    public function ajax_edit_real_personal_va()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        $id = trim($this->request->getPost('id'));
        $bankAccountId = trim($this->request->getPost('bank_account_id'));

        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $bidvEnterpriseAccountModel = model(BidvEnterpriseAccountModel::class);

        if (! is_numeric($id)) 
            return $this->response->setJSON(['status' => false, 'message' => 'ID tài khoản ảo không hợp lệ']);

        if (!has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền thực hiện thao tác này']);

        $bankAccountDetails = $this->getBankAccountDetails($bankAccountId);
        $bankSubAccountDetails = $bankSubAccountModel->where(['id' => $id])->get()->getRow();
        $bidvEnterpriseAccountDetails = $bidvEnterpriseAccountModel->where('bank_account_id', $bankAccountDetails->id)->first();

        if (!$bankAccountDetails || !$bankSubAccountDetails) {
            return $this->respond(['status' => false, 'message' => 'Không tìm thấy tài khoản ảo này']);
        }

        if ($bidvEnterpriseAccountDetails) {
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng BIDV doanh nghiệp không được sử dụng tính năng này']);
        }

        $rawData = [
            'label' => trim(xss_clean($this->request->getVar('label')))
        ];

        if (! $this->validateData($rawData, [
            'label' => ['permit_empty', 'max_length[100]']
        ])) {
            return $this->fail($this->validator->getErrors());
        }

        $updated = $bankSubAccountModel->update($bankSubAccountDetails->id, $rawData);

        if (!$updated) {
            return $this->respond([
                'status' => false,
                'message' => 'Đã có lỗi xảy ra, vui lòng thử lại sau hoặc liên hệ SePay để được hỗ trợ.'
            ]);
        }

        return $this->respond([
            'status' => true,
            'message' => 'Đã cập nhật VA'
        ]);
    }

    public function ajax_edit_real_va()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        $id = trim($this->request->getPost('id'));
        $bankAccountId = trim($this->request->getPost('bank_account_id'));

        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);

        if (! is_numeric($id)) 
            return $this->response->setJSON(['status' => false, 'message' => 'ID tài khoản ảo không hợp lệ']);

        if (!has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền thực hiện thao tác này']);

        $bankAccountDetails = $bankAccountModel->where(['id' => $bankAccountId, 'company_id' => $this->user_session['company_id']])->get()->getRow();
        $bankSubAccountDetails = $bankSubAccountModel->where(['id' => $id])->get()->getRow();

        if (!$bankAccountDetails || !$bankSubAccountDetails) {
            return $this->respond(['status' => false, 'message' => 'Không tìm thấy tài khoản ảo này']);
        }

        $bidvEnterpriseAccountModel = model(BidvEnterpriseAccountModel::class);
        $bidvEnterpriseAccountDetails = $bidvEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->get()->getRow();

        if (!$bidvEnterpriseAccountDetails || ($bidvEnterpriseAccountDetails && !$bidvEnterpriseAccountDetails->custom_va_name)) {
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền thực hiện thao tác này']);
        }

        $rawData = [
            'sub_holder_name' => trim($this->request->getVar('sub_holder_name'))
        ];

        if (! $this->validateData($rawData, [
            'sub_holder_name' => ['required', 'regex_match[/^[A-Z0-9\s]{' . $this->bidvConfig->vaNameMinlen . ',' . $this->bidvConfig->vaNameMaxlen . '}$/]']
        ])) {
            return $this->fail($this->validator->getErrors());
        }

        $updated = $bankSubAccountModel->update($bankSubAccountDetails->id, $rawData);

        if (!$updated) {
            return $this->respond([
                'status' => false,
                'message' => 'Đã có lỗi xảy ra, vui lòng thử lại sau hoặc liên hệ SePay để được hỗ trợ.'
            ]);
        }

        return $this->respond([
            'status' => true,
            'message' => 'Đã cập nhật VA'
        ]);
    }

    public function ajax_close_real_va()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        $id = trim($this->request->getPost('id'));
        $bankAccountId = trim($this->request->getPost('bank_account_id'));

        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);

        if (! is_numeric($id)) 
            return $this->response->setJSON(['status' => false, 'message' => 'ID tài khoản ảo không hợp lệ']);

        if (!has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền thực hiện thao tác này']);

        $bankAccountDetails = $bankAccountModel->where(['id' => $bankAccountId, 'company_id' => $this->user_session['company_id']])->get()->getRow();
        $bankSubAccountDetails = $bankSubAccountModel->where(['id' => $id])->get()->getRow();

        if (!$bankAccountDetails || !$bankSubAccountDetails) {
            return $this->respond(['status' => false, 'message' => 'Không tìm thấy tài khoản ảo này']);
        }

        $updated = $bankSubAccountModel->update($bankSubAccountDetails->id, ['va_active' => 0]);

        if (!$updated) {
            return $this->respond([
                'status' => false,
                'message' => 'Đã có lỗi xảy ra, vui lòng thử lại sau hoặc liên hệ SePay để được hỗ trợ.'
            ]);
        }

        return $this->respond([
            'status' => true,
            'message' => 'Đã vô hiệu hoá VA'
        ]);
    }

    public function ajax_active_real_va()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        $id = trim($this->request->getPost('id'));
        $bankAccountId = trim($this->request->getPost('bank_account_id'));

        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);

        if (! is_numeric($id)) 
            return $this->response->setJSON(['status' => false, 'message' => 'ID tài khoản ảo không hợp lệ']);

        if (!has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền thực hiện thao tác này']);

        $bankAccountDetails = $bankAccountModel->where(['id' => $bankAccountId, 'company_id' => $this->user_session['company_id']])->get()->getRow();
        $bankSubAccountDetails = $bankSubAccountModel->where(['id' => $id])->get()->getRow();

        if (!$bankAccountDetails || !$bankSubAccountDetails) {
            return $this->respond(['status' => false, 'message' => 'Không tìm thấy tài khoản ảo này']);
        }

        $updated = $bankSubAccountModel->update($bankSubAccountDetails->id, ['va_active' => 1]);

        if (!$updated) {
            return $this->respond([
                'status' => false,
                'message' => 'Đã có lỗi xảy ra, vui lòng thử lại sau hoặc liên hệ SePay để được hỗ trợ.'
            ]);
        }

        return $this->respond([
            'status' => true,
            'message' => 'Đã kích hoạt VA'
        ]);
    }

    public function ajax_delete_real_va()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        $id = trim($this->request->getPost('id'));
        $bankAccountId = trim($this->request->getPost('bank_account_id'));

        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);

        if (! is_numeric($id)) 
            return $this->response->setJSON(['status' => false, 'message' => 'ID tài khoản ảo không hợp lệ']);

        if (!has_permission('BankAccount', 'can_delete'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền thực hiện thao tác này']);

        $bankAccountDetails = $bankAccountModel->where(['id' => $bankAccountId, 'company_id' => $this->user_session['company_id']])->get()->getRow();
        $bankSubAccountDetails = $bankSubAccountModel->where(['id' => $id])->get()->getRow();

        if (!$bankAccountDetails || !$bankSubAccountDetails) {
            return $this->respond(['status' => false, 'message' => 'Không tìm thấy tài khoản ảo này']);
        }

        $bankSubAccountModel->delete($id);
    
        return $this->respond(['status' => true]);
    }

    public function ajax_delete_bank_account()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        if (!has_permission('BankAccount', 'can_delete'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn chưa được cấp quyền để thực hiện thao tác này']);

        $bankAccountModel = model(BankAccountmodel::class);
        $bankAccountId = trim(xss_clean($this->request->getPost('id')));

        $bankAccountDetails = $bankAccountModel->where([
            'id' => $bankAccountId, 
            'company_id' => $this->user_session['company_id'],
            'bank_id' => 9
        ])->get()->getRow();

        if (!$this->bidvConfig->allowedDeleteBankAccount && $bankAccountDetails->bank_api) return show_404();
        
        if (!is_object($bankAccountDetails))
            return $this->response->setJSON(['status' => false, 'message' => 'Không tìm thấy tài khoản ngân hàng này']);

        $bidvEnterpriseAccountModel = model(BidvEnterpriseAccountModel::class);
        $bidvEnterpriseAccountDetails = $bidvEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->first();

        if (!$bidvEnterpriseAccountDetails) {
            $bankSubAccountModel = model(BankSubAccountModel::class);
            $deletable = $bankSubAccountModel->where(['acc_type' => 'Real', 'bank_account_id' => $bankAccountId])->countAllResults() === 0;

            if (!$deletable) {
                return $this->respond(['status' => false, 'message' => 'Quý khách vui lòng xóa toàn bộ tài khoản ảo (VA) chính thức từ ngân hàng trước khi thực hiện xóa tài khoản chính']);
            }
        }

        if ($bidvEnterpriseAccountDetails && !$bidvEnterpriseAccountDetails->custom_va_name) {
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn chưa được cấp quyền để thực hiện thao tác này']);
        }

        DeleteCompanyBankAccountAction::run($bankAccountDetails, $this->company_details, $this->request->getIPAddress(), self::class);
        $bidvEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->delete();
                    
        add_user_log(['data_id' => $bankAccountId, 'company_id' => $this->user_session['company_id'], 'data_type' => 'bank_account_delete', 'description' => 'Xóa tài khoản ngân hàng ' . $bankAccountDetails->account_number, 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);
        set_alert('success', 'Đã xóa tài khoản ngân hàng thành công.');

        return $this->response->setJSON(['status' => true]);
    }

    public function ajax_request_switch_sms_connection()
    {
        if (!$this->bidvConfig->allowedSwitchSmsConnection)
            return show_404();
        
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        if (!has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn chưa được cấp quyền để thực hiện thao tác này']);

        $bankAccountId = trim(xss_clean($this->request->getPost('id')));
        $bankAccountDetails = $this->getSwitchableSmsConnectionBankAccount($bankAccountId);
        
        if (!is_object($bankAccountDetails))
            return $this->response->setJSON(['status' => false, 'message' => 'Tài khoản ngân hàng không đủ điều kiện']);

        if (!$this->determineIfSwitchToSmsConnection())
            return $this->response->setJSON(['status' => false, 'message' => 'Gói dịch vụ của bạn không cho phép kết nối Ngân hàng qua SMS báo số dư. Vui lòng nâng cấp gói hoặc liên hệ SePay để được hỗ trợ.']);

        return $this->response->setJSON(['status' => true]);
    }

    public function ajax_confirm_switch_sms_connection()
    {
        if (!$this->bidvConfig->allowedSwitchSmsConnection) 
            return show_404();
        
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        if (!has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn chưa được cấp quyền để thực hiện thao tác này']);

        $bankAccountId = trim(xss_clean($this->request->getPost('id')));
        $bankAccountDetails = $this->getSwitchableSmsConnectionBankAccount($bankAccountId);
        
        if (!is_object($bankAccountDetails))
            return $this->response->setJSON(['status' => false, 'message' => 'Tài khoản ngân hàng không đủ điều kiện']);

        if (!$this->determineIfSwitchToSmsConnection())
            return $this->response->setJSON(['status' => false, 'message' => 'Gói dịch vụ của bạn không cho phép kết nối Ngân hàng qua SMS báo số dư. Vui lòng nâng cấp gói hoặc liên hệ SePay để được hỗ trợ.']);

        $bankAccountModel = model(BankAccountModel::class);
        $simCompanyModel = model(SimCompanyModel::class);

        $simId = trim(xss_clean($this->request->getPost('sim_id')));
        $bankAccountUpdateData = [
            'bank_sms' => 1,
            'bank_sms_connected' => 0,
            'bank_api' => 0,
            'bank_api_connected' => 0
        ];

        if (is_numeric($simId)) {
            $simDetails = $simCompanyModel->select('tb_autopay_sim.id')
                ->join('tb_autopay_sim', 'tb_autopay_sim.id=tb_autopay_sim_company.sim_id')
                ->where([
                    'tb_autopay_sim_company.company_id' => $this->user_session['company_id'], 
                    'tb_autopay_sim.active' => 1, 
                    'tb_autopay_sim.id' => $simId
                ])
                ->get()->getRow();

            if (!is_object($simDetails))
                return $this->response->setJSON(['status' => false, 'message' => 'SIM nhận SMS mà bạn chọn không khả dụng']);

            $bankAccountUpdateData['sim_id'] = $simId;
        }

        $bankAccountUpdated = $bankAccountModel->set($bankAccountUpdateData)->update($bankAccountId);

        if (!$bankAccountUpdated)
            return $this->response->setJSON(['status' => false, 'message' => 'Không thể chuyển đổi phương thức kết nối sang SMS Banking, vui lòng liên hệ SePay để được hỗ trợ.']);
        
        add_user_log(['data_id' => $bankAccountId, 'company_id' => $this->user_session['company_id'], 'data_type' => 'bank_account_update', 'description' => 'Chuyển đổi phương thức kết nối sang SMS Banking', 'user_id' => $this->user_details->id, 'ip'=>$this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);
        return $this->response->setJSON(['status' => true]);
    }

    public function ajax_switch_api_connection()
    {
        if (!$this->bidvConfig->allowedSwitchApiConnection)
            return show_404();
        
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        if (!has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn chưa được cấp quyền để thực hiện thao tác này']);

        $bankAccountId = trim(xss_clean($this->request->getPost('id')));
        $bankAccountDetails = $this->getSwitchableApiConnectionBankAccount($bankAccountId);
        
        if (!is_object($bankAccountDetails))
            return $this->response->setJSON(['status' => false, 'message' => 'Tài khoản ngân hàng không đủ điều kiện']);

        $bankAccountModel = model(BankAccountModel::class);
        $bidvEnterpriseAccountModel = model(BidvEnterpriseAccountModel::class);
        $isEnterpriseAccount = $bidvEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->countAllResults();

        if ($isEnterpriseAccount) {
            $bankAccountModel
                ->set([
                    'bank_api_connected' => 1, 
                    'bank_api' => 1, 
                    'bank_sms' => 0,
                    'accumulated' => 0,
                ])
                ->where(['id' => $bankAccountDetails->id])->update();

            $redirectTo = base_url('bidv/details/' . $bankAccountDetails->id);
        } else {
            $redirectTo = base_url('bidv/step1/' . $bankAccountDetails->id);
        }
        
        return $this->response->setJSON(['status' => true, 'redirect_to' => $redirectTo]);
    }

    protected function getSwitchableApiConnectionBankAccount($id)
    {
        return model(BankAccountmodel::class)->where([
            'id' => $id, 
            'company_id' => $this->user_session['company_id'],
            'bank_id' => Bidv::BANK_ID,
            'bank_sms' => 1,
            'bank_api' => 0,
            'bank_api_connected' => 0,
            'active' => 1,
        ])->get()->getRow();
    }

    protected function getSwitchableSmsConnectionBankAccount($id)
    {
        return model(BankAccountmodel::class)->where([
            'id' => $id, 
            'company_id' => $this->user_session['company_id'],
            'bank_id' => Bidv::BANK_ID,
            'bank_sms' => 0,
            'bank_api' => 1,
            'active' => 1,
        ])->get()->getRow();
    }

    protected function determineIfSwitchToSmsConnection()
    {
        $subscriptionDetails = model(CompanySubscriptionModel::class)->where(['company_id' => $this->user_session['company_id']])->get()->getRow();
        $productDetails = model(ProductModel::class)->where(['id' => $subscriptionDetails->plan_id])->get()->getRow();

        return is_object($subscriptionDetails) && $productDetails->sms_allow == 1;        
    }

    public function ajax_bank_account_update() 
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        if (!has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền sửa tài khoản ngân hàng']);
 
        $validation = \Config\Services::validation();

        helper('text');

        if (! $this->validate([
            'id' => 'required|integer|is_natural',
            'label' => ['label' => 'Tên gợi nhớ', 'rules' => "max_length[100]"],
        ])) {
            return $this->response->setJSON(['status' => false, 'message' => implode(". ", $validation->getErrors())]);
        }  

        $bankAccountModel = model(BankAccountmodel::class);
        $bankAccountId = trim(xss_clean($this->request->getPost('id')));

        $bankAccount = $bankAccountModel->where(['id' => $bankAccountId, 'company_id' => $this->user_session['company_id']])->get()->getRow();
        
        if (!is_object($bankAccount))
            return $this->response->setJSON(['status' => false, 'message' => 'Không tìm thấy tài khoản ngân hàng này']);

        $data = [
            'label' => trim(xss_clean($this->request->getVar('label'))),
        ];
           
        $updated = $bankAccountModel->set($data)->where('id', $bankAccountId)->update();
        
        if ($updated) { 
            add_user_log(['data_id' => $bankAccountId, 'company_id' => $this->user_session['company_id'], 'data_type' => 'bank_account_update', 'description' => 'Sửa tài khoản ngân hàng', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);
            return $this->response->setJSON(['status' => true]);
        } else {
            return $this->response->setJSON(['status' => false, 'message' => 'Không thể cập nhật tài khoản ngân hàng!']);
        }
    }

    public function ajax_delete_personal_real_va()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (! has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền xóa tài khoản VA']);

        $data = [
            'bank_account_id' => trim(xss_clean($this->request->getVar('bank_account_id'))),
            'sub_account_id' => trim(xss_clean($this->request->getVar('sub_account_id'))),
        ];

        $bankAccountModel = model(BankAccountmodel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $bidvEnterpriseAccountModel = model(BidvEnterpriseAccountModel::class);

        $bankAccountDetails = $this->getBankAccountDetails($data['bank_account_id']);

        if (! $bankAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng không tồn tại']);

        if ($bankAccountDetails->bank_sms)
            return $this->respond(['status' => false, 'message' => 'Phương thức kết nối SMS không thể sử dụng tính năng này']);

        $bidvEnterpriseAccountDetails = $bidvEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->first();

        if ($bidvEnterpriseAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng BIDV doanh nghiệp không được sử dụng tính năng này']);

        $bankSubAccountDetails = $this->getBankSubAccountDetails($data['sub_account_id'], $bankAccountDetails->id);

        if (! $bankSubAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản VA không tồn tại']);

        try {
            $client = new BidvClient;
        } catch (Exception $e) {
            return $this->handleBidvClientException($e);
        }

        try {
            $response = $client->deleteVAQLBH(strtoupper(str_replace($this->bidvConfig->personalVaPrefix, '', $bankSubAccountDetails->sub_account)));

            $bankSubAccountModel->where(['id' => $bankSubAccountDetails->id])->delete();

            return $this->response->setJSON(['status' => true]);
        } catch (Exception $e) {
            $errorCode = $e->getCode();

            if (in_array($errorCode, ['183'])) {
                $bankSubAccountModel->where(['id' => $bankSubAccountDetails->id])->delete();

                return $this->response->setJSON(['status' => true]);
            }

            return $this->handleBidvClientException($e);
        }
    }

    public function ajax_unlink($id = '')
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        if (!has_permission('BankAccount', 'can_add'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn chưa được cấp quyền để thực hiện thao tác này']);

        $bankAccountModel = model(BankAccountmodel::class);
        $bankAccountId = trim(xss_clean($this->request->getPost('id')));

        $bankAccountDetails = $bankAccountModel->where([
            'id' => $bankAccountId, 
            'company_id' => $this->user_session['company_id'],
            'bank_id' => Bidv::BANK_ID
        ])->get()->getRow();
        
        if (!is_object($bankAccountDetails))
            return $this->response->setJSON(['status' => false, 'message' => 'Không tìm thấy tài khoản ngân hàng này']);

        DeleteCompanyBankAccountAction::run($bankAccountDetails, $this->company_details, $this->request->getIPAddress(), self::class);

        $bidvEnterpriseAccountModel = model(BidvEnterpriseAccountModel::class);
        $bidvEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->delete();

        set_alert('success', 'Đã xóa tài khoản ngân hàng.');

        return $this->response->setJSON([
            'status' => true, 
        ]);
    }

    protected function getBankAccountDetails($id)
    {
        $bankAccountModel = model(BankAccountModel::class);

        return $bankAccountModel
            ->select('tb_autopay_bank_account.id, tb_autopay_bank_account.account_number, tb_autopay_bank_account.label, tb_autopay_bank_account.active, tb_autopay_bank_account.created_at, tb_autopay_bank.full_name, tb_autopay_bank.brand_name, tb_autopay_bank_account.account_number, tb_autopay_bank_account.account_holder_name, tb_autopay_bank.icon_path, tb_autopay_bank.logo_path, tb_autopay_sim.sim_phonenumber, tb_autopay_bank_account.last_transaction, tb_autopay_bank_account.accumulated,tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.bank_id,tb_autopay_bank_account.bank_sms, tb_autopay_bank_account.bank_sms_connected, tb_autopay_bank_account.bank_api, tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.identification_number, tb_autopay_bank_account.phone_number, tb_autopay_bank_account.sim_id, tb_autopay_bank_account.merchant_id')
            ->join('tb_autopay_bank', 'tb_autopay_bank.id=tb_autopay_bank_account.bank_id')
            ->join('tb_autopay_sim', 'tb_autopay_sim.id=tb_autopay_bank_account.sim_id', 'left')
            ->where([
                'tb_autopay_bank_account.company_id' => $this->user_session['company_id'], 
                'tb_autopay_bank_account.id' => $id,
                'tb_autopay_bank_account.bank_id' => BIDV::BANK_ID
            ])->first();
    }

    protected function getBankSubAccountDetails($id, $bankAccountId = null)
    {
        $bankSubAccountModel = model(BankSubAccountModel::class);

        $builder = $bankSubAccountModel
            ->select('tb_autopay_bank_sub_account.id, tb_autopay_bank_sub_account.sub_account, tb_autopay_bank_sub_account.label, tb_autopay_bank_sub_account.active, tb_autopay_bank_sub_account.va_active, tb_autopay_bank_sub_account.sub_holder_name, tb_autopay_bank_sub_account.created_at')
            ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id')
            ->where([
                'tb_autopay_bank_account.company_id' => $this->user_session['company_id'], 
                'tb_autopay_bank_sub_account.id' => $id,
                'tb_autopay_bank_account.bank_id' => Bidv::BANK_ID
            ]);

        if ($bankAccountId) {
            $builder->where(['tb_autopay_bank_account.id' => $bankAccountId]);
        }

        return $builder->first();
    }

    protected function personalVa($subId)
    {
        return $this->bidvConfig->personalVaPrefix . $subId;
    }

    protected function getSentOtpAlert()
    {
        return 'Đã gửi OTP về số điện thoại.';
    }

    protected function getSubIdRegex()
    {
        $charType = $this->bidvConfig->personalVaCharType;
        $vaSuffixMaxlen = $this->bidvConfig->personalVaMaxlen - strlen($this->bidvConfig->personalVaPrefix);

        if ($charType == 'OnlyNumber') {
            $sets = '0-9';
        } else if ($charType == 'OnlyLetter') {
            $sets = 'A-Z';
        } else {
            $sets = '0-9A-Z';
        }

        return '/^[' . $sets . ']{1,' . $vaSuffixMaxlen . '}$/';
    }

    protected function prefixId($bidvEnterpriseAccountDetails)
    {
        $vaPrefix = $bidvEnterpriseAccountDetails->va_prefix ?? $this->bidvConfig->vaPrefix;

        return $vaPrefix . strlen($bidvEnterpriseAccountDetails->prefix_id) . $bidvEnterpriseAccountDetails->prefix_id . ($vaPrefix == '96' ? '88' : 'VA');
    }
}