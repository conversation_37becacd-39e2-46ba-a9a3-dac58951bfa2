<?php

namespace App\Controllers;

use App\Models\ReferralCodeModel;
use App\Models\ReferralContentModel;
use App\Models\ReferralUseModel;
use Config\Referral as Config;

class Referral extends BaseController
{
    public function index()
    {
        if (! in_array($this->company_details->role, ['Admin', 'SuperAdmin'])) {
            show_404();
        }

        $data = [
            'page_title' => 'Giới thiệu',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
        ];

        $config = config(Config::class);
        $referralCodeModel = model(ReferralCodeModel::class);
        $referralUseModel = model(ReferralUseModel::class);

        $referralCode = $referralCodeModel
            ->where('company_id', $this->user_session['company_id'])
            ->first();

        if (! $referralCode) {
            $referralCodeModel->createReferralCode($this->user_session['company_id']);
        }

        if ($config->showTopReferrals) {
            $topReferrals = $referralCodeModel->getTopReferrals(5, date('Y-m-01 00:00:00'));
            $myRanking = $referralCodeModel->getMyRanking($this->user_session['company_id']);
        } else {
            $topReferrals = null;
            $myRanking = null;
        }

        $referralCode = $referralCodeModel
            ->select('tb_autopay_referral_codes.id, tb_autopay_referral_codes.code, tb_autopay_referral_codes.total_bonus_received')
            ->where('tb_autopay_referral_codes.company_id', $this->user_session['company_id'])
            ->first();

        $data = array_merge($data, [
            'config' => $config,
            'referralCode' => $referralCode,
            'referralUses' => $referralUseModel
                ->select('COUNT(id) as total_referrals')
                ->where('referral_code_id', $referralCode->id)
                ->where('action', 'first_connect_bank')
                ->where('is_valid', true)
                ->first(),
            'usagePercentage' => round($referralCode->total_bonus_received / $config->maxReferralBonus * 100, 2),
            'topReferrals' => $topReferrals,
            'myRanking' => $myRanking,
            'manuals' => model(ReferralContentModel::class)
                ->where('type', 'manual')
                ->where('active', true)
                ->orderBy('order', 'asc')
                ->findAll(),
            'faqs' => model(ReferralContentModel::class)
                ->where('type', 'faq')
                ->where('active', true)
                ->orderBy('order', 'asc')
                ->findAll(),
        ]);

        echo view('templates/autopay/header', $data);
        echo view('referral/index', $data);
        echo view('templates/autopay/footer', $data);
    }

    public function ajax_referral_list()
    {
        if (! in_array($this->company_details->role, ['Admin', 'SuperAdmin'])) {
            show_404();
        }

        if ($this->request->getMethod(true) !== 'POST') {
            show_404();
        }

        $referralUseModel = model(ReferralUseModel::class);

        $data = [];
        $start = $this->request->getPost('start');
        $draw = $this->request->getPost('draw');

        $referralUses = $referralUseModel->getDataTables($this->user_session['company_id']);

        foreach ($referralUses as $referralUse) {
            $start++;

            switch ($referralUse->action) {
                case 'first_connect_bank':
                    $action = 'Đăng ký mới';
                    break;
                case 'connect_bank':
                    $action = 'Kết nối ngân hàng';
                    break;
                case 'paid':
                    $action = 'Trả phí';
                    break;
            }

            $data[] = [
                $start,
                sprintf(
                    '<img src="%s" class="rounded-pill me-1" style="max-width: 24px; max-height: 24px;">%s',
                    get_gravatar($referralUse->email),
                    str_mask($referralUse->fullname, '***', 3, 4)
                ),
                $action,
                sprintf(
                    '<span class="%s">+%s</span>%s',
                    $referralUse->is_valid ? 'text-success' : 'text-warning',
                    number_format($referralUse->value),
                    $referralUse->is_valid ? '' : '<i class="fas fa-exclamation-circle ms-1" data-bs-toggle="tooltip" title="Số giao dịch này sẽ được cộng vào gói dịch vụ của bạn sau người được giới thiệu kết nối một tài khoản ngân hàng thành công"></i>'
                ),
                date('d/m/Y H:i:s', strtotime($referralUse->created_at)),
            ];
        }

        return $this->response->setJSON([
            'draw' => $draw,
            'recordsTotal' => $referralUseModel->countTotal($this->user_session['company_id']),
            'recordsFiltered' => $referralUseModel->countFiltered($this->user_session['company_id']),
            'data' => $data,
        ]);
    }
}
