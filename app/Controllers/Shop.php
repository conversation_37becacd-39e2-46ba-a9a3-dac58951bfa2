<?php

namespace App\Controllers;

use App\Models\ShopModel;
use App\Models\BankAccountModel;
use CodeIgniter\Database\RawSql;
use App\Models\BankShopLinkModel;
use App\Models\TransactionsModel;
use CodeIgniter\API\ResponseTrait;
use App\Controllers\BaseController;
use App\Models\BankSubAccountModel;
use App\Models\NotificationTelegramModel;
use App\Models\NotificationLarkMessengerModel;
use App\Models\UserPermissionBankSubModel;
use App\Models\UserModel;
use App\Models\CompanyUserModel;
use App\Models\UserPermissionFeatureModel;
use App\Models\UserPermissionBankModel;
use App\Models\CompanySubscriptionModel;
use App\Models\ProductModel;
use CodeIgniter\Model;
use CodeIgniter\Exceptions\PageNotFoundException;

class Shop extends BaseController
{
    use ResponseTrait;

    public function __construct() {
        $channel_partner = session()->get('channel_partner') ?? null;
        
        if (!$channel_partner) {
            throw new PageNotFoundException();
        }
    }

    public function index()
    {
        $data = [
            'page_title' => 'Cửa hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        $data['shop_used_limit'] = $this->checkShopLimit();

        if ($this->shop_billing) {
            $data['shop_count'] = $this->countCompanyShop();
        }

        echo theme_view('templates/autopay/header',$data);
        echo theme_view('shop/index', $data);
        echo theme_view('templates/autopay/footer',$data);
    }

    public function ajax_index_shop()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        $shopModel = model(ShopModel::class);

        $no = xss_clean($this->request->getVar('start') ?? 0);
        $length = xss_clean($this->request->getVar('length') ?? 25);
        $draw = xss_clean($this->request->getVar('draw'));
        $columns = $this->request->getVar('columns') ?? [];
        $order = $this->request->getVar('order') ?? [];
        $search = $this->request->getVar('search') ?? [];

        $column_order = array('tb_autopay_shop.name', 'tb_autopay_shop.address', 'tb_autopay_shop.active');

        $builder = $shopModel->select('tb_autopay_shop.*')->where(['tb_autopay_shop.company_id' => $this->user_session['company_id']]);

        if (count($search) && isset($search['value'])) {
            $builder->groupStart()
                    ->like('tb_autopay_shop.name', $search['value'])
                    ->orLike('tb_autopay_shop.address', $search['value'])
                    ->groupEnd();
        }

        if (!empty($order)) {
            foreach ($order as $ord) {
                $colIdx = $ord['column'];
                $dir = $ord['dir'] === 'asc' ? 'ASC' : 'DESC';
                if (isset($column_order[$colIdx])) {
                    $builder->orderBy($column_order[$colIdx], $dir);
                }
            }
        } else {
            $builder->orderBy('tb_autopay_shop.id', 'DESC');
        }

        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])) {
            $builder->join('tb_autopay_bank_shop_link', 'tb_autopay_bank_shop_link.shop_id = tb_autopay_shop.id');
            $builder->join(
                'tb_autopay_user_permission_bank_sub',
                "tb_autopay_user_permission_bank_sub.user_id = {$this->user_session['user_id']} 
                AND tb_autopay_user_permission_bank_sub.sub_account_id = tb_autopay_bank_shop_link.bank_sub_account_id"
            );
        }

        $shops = $builder->limit($length, $no)->get()->getResult();

        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])) {
            $uniqueShops = [];
            foreach ($shops as $shop) {
                if (!isset($uniqueShops[$shop->id])) {
                    $uniqueShops[$shop->id] = $shop;
                }
            }
            $shops = array_values($uniqueShops);
        }

        $data = [];
        foreach ($shops as $shop) {
            $row = [];
            $row[] = ++$no;
            $row[] = '<a href="' . base_url('shop/details') . '/' . $shop->id . '">' . esc($shop->name) . '</a>';
            $row[] = esc($shop->address);
            $row[] = $shop->active == 1 ? '<span class="text-success">Đang hoạt động</span>' : '<span class="text-danger">Chưa hoạt động</span>';
            $row[] = "<a href='javascript:;' onclick='edit_shop(" . $shop->id. ")' class='btn btn-sm btn-outline-warning ms-2 me-2 mt-2'><i class='bi bi-pencil'></i> Sửa</a>";

            $data[] = $row;
        }

        return $this->respond([
            'draw' => $draw,
            'recordsTotal' => $shopModel->where(['company_id' => $this->user_session['company_id']])->countAllResults(),
            'recordsFiltered' => $shopModel->where(['company_id' => $this->user_session['company_id']])->countAllResults(),
            'data' => $data
        ]);
    }



    public function ajax_get_shop($shopId = null)
    {
        if (!$shopId || !is_numeric($shopId)) return $this->failNotFound();

        $shop = $this->getCompanyUserShop($shopId);

        if (!$shop) return $this->failNotFound();

        return $this->respond([
            'status' => TRUE,
            'data' => $shop,
        ]);
    }

    public function ajax_shop_update()
    {
        if ($this->request->getMethod(true) != 'POST') {
            return '';
        }
        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin']))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền sửa cửa hàng này"));

        $validation = \Config\Services::validation();

        helper('text');

        if (!$this->validate([
            'id' => 'required|integer|is_natural',
            'name' => 'required|max_length[255]',
            'address' => 'required|max_length[255]',
        ])) {
            return $this->response->setJSON([
                'status' => FALSE,
                'message' => implode(". ", $validation->getErrors())
            ]);
        }

        $shopId = trim(xss_clean($this->request->getPost('id')));
        $data = [
            'name' => trim(xss_clean($this->request->getPost('name'))),
            'address' => trim(xss_clean($this->request->getPost('address'))),
        ];

        $shop = $this->getCompanyUserShop($shopId);

        if (!$shop)
            return $this->response->setJSON(['status' => FALSE, 'message' => 'Không tìm thấy cửa hàng']);

        $shopModel = model(ShopModel::class);
        if ($shopModel->update($shopId, $data)) {
            add_user_log(['data_id' => $shopId, 'company_id' => $this->company_details->id, 'data_type' => 'admin_update_shop', 'description' => 'Cập nhật cửa hàng thành công', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);
            return $this->response->setJSON(['status' => TRUE, 'message' => 'Cập nhật thành công']);
        } else {
            add_user_log(['data_id' => $shopId, 'company_id' => $this->company_details->id, 'data_type' => 'admin_update_shop', 'description' => 'Cập nhật cửa hàng thất bại', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);
            return $this->response->setJSON(['status' => FALSE, 'message' => 'Không thể cập nhật dữ liệu']);
        }
    }


    public function details($shopId = null)
    {
        helper('general');

        if (!$shopId || !is_numeric($shopId)) return redirect()->to('shop');

        $data = [
            'page_title' => 'Cửa hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'period' => xss_clean($this->request->getGet('period') ?? 'today')
        ];

        $startDate = null;
        $endDate = null;

        if ($data['period'] == 'today') {
            $startDate = date('Y-m-d 00:00:00');
            $endDate = date('Y-m-d 23:59:59');
        } else if($data['period'] == 'week') {
            $startDate = date('Y-m-d 00:00:00', strtotime('monday this week'));
            $endDate = date('Y-m-d 23:59:59');
        }else if($data['period'] == 'month') {
            $startDate = date("Y-m-01 00:00:00");
            $endDate = date('Y-m-d 23:59:59');
        } else if($data['period'] == 'year') {
            $startDate = date("Y-01-01 00:00:00");
            $endDate = date('Y-m-d 23:59:59');
        }

        $data['shop'] = $this->getCompanyUserShop($shopId);

        if (!$data['shop']) show_404();

        $shopModel = slavable_model(ShopModel::class, 'Shop');

        $checkActive = $shopModel
        ->where([
            'id' => $shopId,
            'company_id' => $this->company_details->id,
            'active' => 1
            ])
        ->orderBy('id', 'ASC')
        ->get()
        ->getResult();

        if(!$checkActive)
            return redirect()->to('shop/step2/'.$shopId);
        
        $data['statistics'] = $this->getCompanyUserShopStatistics($shopId, null, null, $startDate, $endDate);

        echo theme_view('templates/autopay/header',$data);
        echo theme_view('shop/details', $data);
        echo theme_view('templates/autopay/footer',$data);
    }

    public function step1()
    {
        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])) show_404();

        $data = [
            'page_title' => 'Cửa hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        if($this->checkShopLimit()){
            return redirect()->to('company/change_plan');
        }

        echo theme_view('templates/autopay/header',$data);
        echo theme_view('shop/step1', $data);
        echo theme_view('templates/autopay/footer',$data);
    }

    public function ajax_step_1()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin']))
            return $this->respond(['status' => false, 'message' => 'Bạn không có quyền truy cập.']);

        // TODO: determine company able create shop with subscription and addons...

        $shopModel = model(ShopModel::class);

        $shopData = [
            'name' => trim(xss_clean($this->request->getVar('name'))),
            'address' => trim(xss_clean($this->request->getVar('address'))),
        ];

        $rules = [
            'name' => ['required', 'string', 'max_length[250]'],
            'address' => ['required', 'string', 'max_length[250]'],
        ];

        if (! $this->validateData($shopData, $rules)) {
            return $this->fail($this->validator->getErrors());
        }

        $isNameExistsInCompany = $shopModel->where(['name' => $shopData['name'], 'company_id' => $this->company_details->id])->countAllResults();

        if ($isNameExistsInCompany) {
            return $this->fail(['name' => 'Tên cửa hàng đã được sử dụng.']);
        }

        $isCheckShopLimit = $this->checkShopLimit();

        if($isCheckShopLimit){
            return $this->fail(['message' => 'Cửa hàng đã đến giới hạn. Vui lòng nâng cấp gói để tạo cửa hàng']);
        }

        $safeShopData = array_merge($shopData, [
            'company_id' => $this->company_details->id
        ]);

        $shopId = $shopModel->insert($safeShopData);

        if (!$shopId)
            return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng thử lại.']);

        add_user_log(['data_id' => $shopId, 'company_id' => $this->company_details->id, 'data_type' => 'admin_create_shop', 'description' => 'Tạo cửa hàng thành công', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);
    
        return $this->respond(['status' => true, 'message' => 'Đã tạo cửa hàng thành công.', 'id' => $shopId]);
    }

    public function step2($shopId = null)
    {
        if (!$shopId || !is_numeric($shopId)) return redirect()->to('shop/step1');

        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])) show_404();

        $data = [
            'page_title' => 'Cửa hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        $shopModel = model(ShopModel::class);

        $data['shop'] = $shopModel->where(['id' => $shopId, 'company_id' => $this->company_details->id])->first();

        if (!$data['shop']) return redirect()->to('shop/step1');

        $data['bank_accounts'] = $this->getCompanyUserBankAccountList();

        echo theme_view('templates/autopay/header',$data);
        echo theme_view('shop/step2', $data);
        echo theme_view('templates/autopay/footer',$data);
    }

    public function step3($shopId = null)
    {
        if (!$shopId || !is_numeric($shopId)) return redirect()->to('shop/step1');

        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])) show_404();

        $data = [
            'page_title' => 'Cửa hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $shopModel = model(ShopModel::class);

        $data['shop'] = $shopModel->where(['id' => $shopId, 'company_id' => $this->company_details->id])->first();

        if (!$data['shop']) return redirect()->to('shop/step1');

        $data['va'] = $this->getCompanyUserBankSubAccountList($shopId)[0] ?? null;

        if (!$data['va']) return redirect()->to('shop/step2/' . $data['shop']->id);

        echo theme_view('templates/autopay/header',$data);
        echo theme_view('shop/step3', $data);
        echo theme_view('templates/autopay/footer',$data);
    }

    public function qrcodes($shopId = null)
    {
        if (!$shopId || !is_numeric($shopId)) return redirect()->to('shop');

        $data = [
            'page_title' => 'Cửa hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        $data['shop'] = $this->getCompanyUserShop($shopId);

        if (!$data['shop']) show_404();

        $data['sub_accounts'] = $this->getCompanyUserBankSubAccountList($shopId); 

        echo theme_view('templates/autopay/header',$data);
        echo theme_view('shop/qrcode/index', $data);
        echo theme_view('templates/autopay/footer',$data);
    }

    public function createQrCode($shopId = null)
    {
        if (!$shopId || !is_numeric($shopId)) return redirect()->to('shop');

        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])) return redirect()->to('shop');

        $data = [
            'page_title' => 'Cửa hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        $bankAccountModel = model(BankAccountModel::class);

        $data['shop'] = $this->getCompanyUserShop($shopId);

        if (!$data['shop']) return redirect()->to('shop');

        $data['bank_accounts'] = $bankAccountModel->where(['company_id' => $this->company_details->id])->get()->getResult();

        $data['create_qr_code_template'] = true;

        echo theme_view('templates/autopay/header',$data);
        echo theme_view('shop/step2', $data);
        echo theme_view('templates/autopay/footer',$data);
    }

    public function printQrCode($shopId = null)
    {
        if (!$shopId || !is_numeric($shopId)) return redirect()->to('shop');

        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])) return redirect()->to('shop');

        $data = [
            'page_title' => 'Cửa hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        $bankSubAccountId = trim(xss_clean($this->request->getGet('va_id')));

        $bankSubAccountModel = model(BankSubAccountModel::class);

        $data['shop'] = $this->getCompanyUserShop($shopId);

        if (!$data['shop']) return redirect()->to('shop');

        $data['va'] = $this->getCompanyUserBankSubAccount($shopId, $bankSubAccountId);

        if (!$data['va']) return redirect()->to('shop');

        $data['create_qr_code_template'] = true;

        echo theme_view('templates/autopay/header',$data);
        echo theme_view('shop/step3', $data);
        echo theme_view('templates/autopay/footer',$data);
    }

    public function transactions($shopId = null)
    {
        if (!$shopId || !is_numeric($shopId)) return redirect()->to('shop');

        $vaId = xss_clean($this->request->getGet('va_id'));

        if($vaId && !is_numeric($vaId))
            return show_404();

        $data = [
            'page_title' => 'Cửa hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'page' => $this->request->getGet('page') ?? 1,
        ];

        $data['shop'] = $this->getCompanyUserShop($shopId);

        if (!$data['shop']) return redirect()->to('shop');

        $data['sub_accounts'] = $this->getCompanyUserBankSubAccountList($shopId); 

        $data['va'] = $this->getCompanyUserBankSubAccount($shopId, $vaId);

        $bankAccountId = $data['va']->bank_account_id ?? null;
        $vaId = $data['va']->id ?? null;

        echo theme_view('templates/autopay/header',$data);
        echo theme_view('shop/transaction/index', $data);
        echo theme_view('templates/autopay/footer',$data);
    }

    public function ajax_index_transactions($shopId = null)
    {
        if (!$shopId || !is_numeric($shopId)) return $this->failNotFound();

        $vaId = trim(xss_clean($this->request->getGet('va_id')));
        $perPage = trim(xss_clean($this->request->getGet('per_page')));
        $query = trim(xss_clean($this->request->getGet('query')));

        $shop = $this->getCompanyUserShop($shopId);

        if (!$shop) return $this->failNotFound();

        $va = $this->getCompanyUserBankSubAccount($shopId, $vaId);

        $bankAccountId = $va->bank_account_id ?? null;
        $vaId = $va->id ?? null;

        $transactionsModel = slavable_model(TransactionsModel::class, 'Transactions');
        $transactions = $this->getCompanyUserTransactionList($transactionsModel, $shopId, $bankAccountId, $vaId, $query, $perPage);
        $pager = $transactionsModel->pager;

        return $this->respond([
            'data' => $transactions,
            'paper' => [
                'per_page' => $pager->getPerPage(),
                'total' => $pager->getTotal(),
                'has_more' => $pager->hasMore(),
                'current_page' => $pager->getCurrentPage(),
                'page_count' => $pager->getPageCount(),
            ]
        ]);
    }

    public function shares($shopId = null)
    {
        if (!$shopId || !is_numeric($shopId)) return $this->failNotFound();

        $type = trim(xss_clean($this->request->getGet('type')));

        if (!in_array($type, ['telegram', 'lark-messenger', 'employee'])) {
            return show_404();
        }

        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])) return redirect()->to('shop');

        $data = [
            'page_title' => 'Cửa hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];
        $data['shop'] = $this->getCompanyUserShop($shopId);

        if (!$data['shop']) show_404();

        $data['statistics'] = $this->getCompanyUserShopShareStatistics($shopId);

        echo theme_view('templates/autopay/header',$data);
        echo theme_view('shop/share/index', $data);
        echo theme_view('templates/autopay/footer',$data);
    }
    
    public function ajax_index_shares($shopId = null)
    {
        if (!$shopId || !is_numeric($shopId)) return $this->failNotFound();

        $perPage = trim(xss_clean($this->request->getGet('per_page')));
        $query = trim(xss_clean($this->request->getGet('query')));
        $type = trim(xss_clean($this->request->getGet('type')));

        if (!in_array($type, ['telegram', 'lark-messenger', 'employee'])) return $this->failNotFound();

        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])) return redirect()->to('shop');

        $shop = $this->getCompanyUserShop($shopId);

        if (!$shop) return $this->failNotFound();

        if ($type === 'telegram') {
            $model = model(NotificationTelegramModel::class);
        } elseif ($type === 'lark-messenger') {
            $model = model(NotificationLarkMessengerModel::class);
        } else {
            $model = model(UserPermissionBankSubModel::class);
        }

        $shares = $this->getCompanyUserShareList($model, $shopId, $query, $perPage);
        $pager = $model->pager;

        foreach ($shares as &$s) {
            if(isset($s->description)){
                $s->description = 'Tên nhóm: <span class="text-primary">'. $s->description. '</span>';
            }
            if (isset($s->transaction_type)) {
                $s->transaction_type = 'Sự kiện: <span class="text-info">' . 
                    ($s->transaction_type == 'In_only' ? 'Khi có tiền vào' : 
                    ($s->transaction_type == 'Out_only' ? 'Khi có tiền ra' : 
                    ($s->transaction_type == 'All' ? 'Khi có tiền vào hoặc ra' : ''))) . 
                    '</span>';
            }
            
            if (isset($s->active)) {
                $s->active = 'Trạng thái: ' . ($s->active == 1 ? 
                    '<span class="text-success">Kích hoạt</span>' : 
                    '<span class="text-danger">Tạm ngưng</span>');
            }
        
            if (isset($s->firstname) && isset($s->lastname)) {
                $s->description = 'Họ và tên: <span class="text-primary">' . $s->lastname . ' ' . $s->firstname . '</span>';
            }
        
            if (isset($s->email)) {
                $s->transaction_type = 'Email: <span class="text-info">' . $s->email . '</span>';
                $s->action = '<div class="d-flex align-items-center justify-content-between">
                               <button type="button" class="btn btn-outline-primary btn-sm" onclick="editUser(' . $s->user_id . ')"><i class="bi bi-pencil"></i> Sửa</button>
                                <button type="button" class="btn btn-outline-warning btn-sm" onclick="revokeUserPermission(' . $s->user_id . ')"><i class="bi bi-shield-x"></i> Gỡ quyền</button>
                            </div>';
            }
            if (isset($s->chat_id)) {
                $s->action = '<div class="d-flex align-items-center justify-content-between">
                               <a href="'.base_url('notificationtelegram/edit').'/'.$s->chat_id.'" class="btn btn-sm btn-outline-warning ms-2 me-2 mt-2"><i class="bi bi-pencil"></i> Sửa</a>
                                <button type="button" class="btn btn-sm btn-outline-danger ms-2 mt-2" onclick="delete_telegram(' . $s->shop_id . ','.$s->chat_id.')" ><i class="bi bi-trash3"></i> Xóa</button>
                            </div>';
            }
            if (isset($s->bot_webhook_url)) {
                $encoded_url = base64_encode($s->bot_webhook_url);
                $s->action = '<div class="d-flex align-items-center justify-content-between">
                               <a href="'.base_url('notificationlarkmessenger/edit').'/'.$encoded_url.'" class="btn btn-sm btn-outline-warning ms-2 me-2 mt-2"><i class="bi bi-pencil"></i> Sửa</a>
                                <button type="button" class="btn btn-sm btn-outline-danger ms-2 mt-2" onclick="delete_larkmessenger(' . $s->shop_id . ',\'' . $encoded_url . '\')" ><i class="bi bi-trash3"></i> Xóa</button>
                            </div>';
            }
        }        

        return $this->respond([
            'data' => $shares,
            'paper' => [
                'per_page' => $pager->getPerPage(),
                'total' => $pager->getTotal(),
                'has_more' => $pager->hasMore(),
                'current_page' => $pager->getCurrentPage(),
                'page_count' => $pager->getPageCount(),
            ]
        ]);
    }

    protected function getCompanyUserShop($shopId)
    {
        if (!$shopId) return null;

        $builder = model(ShopModel::class)
            ->select('tb_autopay_shop.*')
            ->where(['tb_autopay_shop.id' => $shopId, 'tb_autopay_shop.company_id' => $this->user_session['company_id']]);

        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])) {
            $builder->join('tb_autopay_bank_shop_link', 'tb_autopay_bank_shop_link.shop_id = tb_autopay_shop.id');
            $builder->join('tb_autopay_user_permission_bank', 'tb_autopay_user_permission_bank.bank_account_id = tb_autopay_bank_shop_link.bank_account_id');
            $builder->where('tb_autopay_user_permission_bank.user_id', $this->user_session['user_id']);
        }
        return $builder->first();
    }

    protected function getCompanyUserBankSubAccountList($shopId)
    {
        if (!$shopId) return [];

        $builder = model(BankSubAccountModel::class)
            ->select(['tb_autopay_bank_sub_account.id', 'tb_autopay_bank_sub_account.label', 'tb_autopay_bank_sub_account.sub_holder_name', 'tb_autopay_bank_sub_account.sub_account', 'tb_autopay_bank_account.account_number'])
            ->join('tb_autopay_bank_shop_link', 'tb_autopay_bank_shop_link.bank_sub_account_id = tb_autopay_bank_sub_account.id')
            ->join('tb_autopay_bank_account', 'tb_autopay_bank_shop_link.bank_account_id = tb_autopay_bank_account.id')
            ->join('tb_autopay_shop', 'tb_autopay_shop.id = tb_autopay_bank_shop_link.shop_id')
            ->where('tb_autopay_shop.id', $shopId)
            ->where('tb_autopay_shop.company_id', $this->user_session['company_id']);

        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])) {
            $builder->join('tb_autopay_user_permission_bank', 'tb_autopay_user_permission_bank.bank_account_id = tb_autopay_bank_shop_link.bank_account_id')
                ->join('tb_autopay_user_permission_bank_sub', 'tb_autopay_user_permission_bank_sub.sub_account_id = tb_autopay_bank_shop_link.bank_sub_account_id')
                ->where('tb_autopay_user_permission_bank.user_id', $this->user_session['user_id'])
                ->where('tb_autopay_user_permission_bank_sub.user_id', $this->user_session['user_id']);
        }

        return $builder->get()->getResult();
    }

    protected function getCompanyUserBankSubAccount($shopId, $bankSubAccountId)
    {
        if (!$shopId || !$bankSubAccountId) return null;

        $builder = model(BankSubAccountModel::class)
            ->select(['tb_autopay_bank_sub_account.id', 'tb_autopay_bank_sub_account.label', 'tb_autopay_bank_sub_account.sub_holder_name', 'tb_autopay_bank_sub_account.sub_account', 'tb_autopay_bank_shop_link.bank_account_id', 'tb_autopay_bank_account.account_number'])
            ->join('tb_autopay_bank_shop_link', 'tb_autopay_bank_shop_link.bank_sub_account_id = tb_autopay_bank_sub_account.id')
            ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id = tb_autopay_bank_shop_link.bank_account_id')
            ->join('tb_autopay_shop', 'tb_autopay_shop.id = tb_autopay_bank_shop_link.shop_id')
            ->where('tb_autopay_bank_shop_link.bank_sub_account_id', $bankSubAccountId)
            ->where('tb_autopay_shop.id', $shopId)
            ->where('tb_autopay_shop.company_id', $this->user_session['company_id']);

        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])) {
            $builder->join('tb_autopay_user_permission_bank', 'tb_autopay_user_permission_bank.id = tb_autopay_bank_shop_link.bank_account_id')
                ->join('tb_autopay_user_permission_bank_sub', 'tb_autopay_user_permission_bank_sub.id = tb_autopay_bank_shop_link.bank_sub_account_id')
                ->where('tb_autopay_user_permission_bank.user_id', $this->user_session['user_id'])
                ->where('tb_autopay_user_permission_bank_sub.user_id', $this->user_session['user_id']);
        }

        return $builder->first();
    }

    protected function getCompanyUserTransactionList(&$model, $shopId, $bankAccountId = null, $bankSubAccountId = null, $query = null, $perPage = null)
    {
        if (!$shopId) return [];

        $db = db_connect();

        $builder = $model
            ->select(['tb_autopay_sms_parsed.id', 'tb_autopay_sms_parsed.sub_account', 'tb_autopay_bank_sub_account.label as bank_sub_account_label', 'tb_autopay_sms_parsed.amount_in', 'tb_autopay_sms_parsed.amount_out', 'tb_autopay_sms_parsed.currency', 'tb_autopay_sms_parsed.transaction_date', 'tb_autopay_bank_shop_link.shop_id', 'tb_autopay_sms_parsed.bank_account_id', 'tb_autopay_bank_shop_link.bank_sub_account_id', 'tb_autopay_sms_parsed.transaction_content', 'tb_autopay_bank_account.account_number', 'tb_autopay_sms_parsed.chat_push_message'])
            ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id = tb_autopay_sms_parsed.bank_account_id')
            ->join('tb_autopay_bank_sub_account', 'tb_autopay_bank_sub_account.sub_account = tb_autopay_sms_parsed.sub_account')
            ->join('tb_autopay_bank_shop_link', "tb_autopay_bank_shop_link.bank_account_id = tb_autopay_sms_parsed.bank_account_id AND tb_autopay_bank_shop_link.shop_id = {$db->escapeString($shopId)} AND tb_autopay_bank_shop_link.bank_sub_account_id = tb_autopay_bank_sub_account.id")
            ->where('tb_autopay_bank_account.company_id', $this->user_session['company_id'])
            ->where('tb_autopay_sms_parsed.parser_status', 'Success');

        if ($bankAccountId && !$bankSubAccountId) {
            $builder->where(['tb_autopay_bank_shop_link.bank_account_id' => $bankAccountId]);
        } else if ($bankAccountId && $bankSubAccountId) {
            $builder->where([
                'tb_autopay_bank_shop_link.bank_account_id' => $bankAccountId,
                'tb_autopay_bank_shop_link.bank_sub_account_id' => $bankSubAccountId,
            ]);
        }

        if ($query) {
            $builder->groupStart()
                ->like('tb_autopay_sms_parsed.transaction_content', $query)
                ->orLike('tb_autopay_sms_parsed.amount_in', $query)
                ->orLike('tb_autopay_sms_parsed.amount_out', $query)
                ->orLike('tb_autopay_sms_parsed.transaction_date', $query)
            ->groupEnd();
        }

        $builder->orderBy('transaction_date', 'desc');

        $perPage = is_numeric($perPage) && $perPage > 0 ? $perPage : 10;
        
        return $builder->distinct()->paginate($perPage);
    }

    protected function getCompanyUserShareList($model, $shopId, $query = null, $perPage = null)
    {
        if (!$shopId) return [];

        $db = db_connect();
        $builder = $model->builder();

        switch (get_class($model)) {
            case NotificationTelegramModel::class:
                $builder->select([
                    'tb_autopay_notification_telegram.id',
                    'tb_autopay_notification_telegram.sub_account_id',
                    'tb_autopay_notification_telegram.company_id',
                    'tb_autopay_notification_telegram.transaction_type',
                    'tb_autopay_notification_telegram.active',
                    'tb_autopay_notification_telegram.description',
                    'tb_autopay_bank_shop_link.shop_id',
                    'tb_autopay_notification_telegram.chat_id'
                ])
                ->join("tb_autopay_bank_shop_link", "tb_autopay_bank_shop_link.bank_account_id = tb_autopay_notification_telegram.bank_account_id AND tb_autopay_bank_shop_link.bank_sub_account_id = tb_autopay_notification_telegram.sub_account_id")
                ->join("tb_autopay_shop", "tb_autopay_shop.id = tb_autopay_bank_shop_link.shop_id")
                ->where("tb_autopay_bank_shop_link.shop_id", $db->escapeString($shopId))
                ->where('tb_autopay_shop.company_id', $this->user_session['company_id'])
                ->groupBy('tb_autopay_notification_telegram.chat_id');
                break;

            case NotificationLarkMessengerModel::class:
                $builder->select([
                    'tb_autopay_notification_larkmessenger.id',
                    'tb_autopay_notification_larkmessenger.sub_account_id',
                    'tb_autopay_notification_larkmessenger.company_id',
                    'tb_autopay_notification_larkmessenger.transaction_type',
                    'tb_autopay_notification_larkmessenger.active',
                    'tb_autopay_notification_larkmessenger.description',
                    'tb_autopay_bank_shop_link.shop_id',
                    'tb_autopay_notification_larkmessenger.bot_webhook_url'
                ])
                ->join("tb_autopay_bank_shop_link", "tb_autopay_bank_shop_link.bank_account_id = tb_autopay_notification_larkmessenger.bank_account_id AND tb_autopay_bank_shop_link.bank_sub_account_id = tb_autopay_notification_larkmessenger.sub_account_id")
                ->join("tb_autopay_shop", "tb_autopay_shop.id = tb_autopay_bank_shop_link.shop_id")
                ->where("tb_autopay_bank_shop_link.shop_id", $db->escapeString($shopId))
                ->where('tb_autopay_shop.company_id', $this->user_session['company_id'])
                ->groupBy('tb_autopay_notification_larkmessenger.bot_webhook_url');
                break;

            case UserPermissionBankSubModel::class:
                $builder->select([
                    'tb_autopay_user_permission_bank_sub.id',
                    'tb_autopay_user_permission_bank_sub.sub_account_id',
                    'tb_autopay_user_permission_bank_sub.user_id',
                    'tb_autopay_bank_shop_link.bank_account_id',
                    'tb_autopay_user.email',
                    'tb_autopay_user.firstname',
                    'tb_autopay_user.lastname',
                    'tb_autopay_user.active'
                ])
                ->join('tb_autopay_user', 'tb_autopay_user_permission_bank_sub.user_id = tb_autopay_user.id')
                ->join('tb_autopay_bank_shop_link', 'tb_autopay_bank_shop_link.bank_sub_account_id = tb_autopay_user_permission_bank_sub.sub_account_id AND tb_autopay_bank_shop_link.shop_id = ' . $db->escapeString($shopId))
                ->join('tb_autopay_shop', 'tb_autopay_shop.id = tb_autopay_bank_shop_link.shop_id')
                ->where('tb_autopay_shop.company_id', $this->user_session['company_id'])
                ->where('tb_autopay_bank_shop_link.shop_id', $shopId)
                ->groupBy('tb_autopay_user_permission_bank_sub.user_id');
                break;

            default:
                return [];
        }

        if ($query) {
            $builder->groupStart()
                ->like('description', $query)
                ->orLike('transaction_type', $query);
            
            switch (get_class($model)) {
                case NotificationTelegramModel::class:
                    $builder->orLike('tb_autopay_notification_telegram.active', $query);
                    break;
                case NotificationLarkMessengerModel::class:
                    $builder->orLike('tb_autopay_notification_larkmessenger.active', $query);
                    break;
                case UserPermissionBankSubModel::class:
                    $builder->orLike('tb_autopay_user.active', $query);
                    break;
            }
        
            $builder->groupEnd();
        }

        $builder->orderBy('id', 'desc');

        $perPage = is_numeric($perPage) && $perPage > 0 ? $perPage : 10;

        return $model->distinct()->paginate($perPage);
    }
    protected function getCompanyUserBankAccountList()
    {
        $builder = model(BankAccountModel::class)->where(['company_id' => $this->company_details->id]);

        return $builder->get()->getResult();
    }

    protected function getCompanyUserShopStatistics($shopId, $bankAccountId = null, $bankSubAccountId = null, $startDate = null, $endDate = null)
    {
        $db = db_connect();

        $builder = slavable_model(TransactionsModel::class, 'Shop')
            ->select(['SUM(tb_autopay_sms_parsed.amount_in) as revenue', 'COUNT(tb_autopay_sms_parsed.id) as transaction_count', 'SUM(tb_autopay_sms_parsed.chat_push_message) as chat_push_message_count'])
            ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id = tb_autopay_sms_parsed.bank_account_id')
            ->join('tb_autopay_bank_sub_account', 'tb_autopay_bank_sub_account.sub_account = tb_autopay_sms_parsed.sub_account')
            ->join('tb_autopay_bank_shop_link', "tb_autopay_bank_shop_link.bank_account_id = tb_autopay_sms_parsed.bank_account_id AND tb_autopay_bank_shop_link.shop_id = {$db->escapeString($shopId)} AND tb_autopay_bank_shop_link.bank_sub_account_id = tb_autopay_bank_sub_account.id")
            ->where('tb_autopay_bank_account.company_id', $this->user_session['company_id'])
            ->where('tb_autopay_sms_parsed.parser_status', 'Success');

        if ($bankAccountId && !$bankSubAccountId) {
            $builder->where(['tb_autopay_bank_shop_link.bank_account_id' => $bankAccountId]);
        } else if ($bankAccountId && $bankSubAccountId) {
            $builder->where([
                'tb_autopay_bank_shop_link.bank_account_id' => $bankAccountId,
                'tb_autopay_bank_shop_link.bank_sub_account_id' => $bankSubAccountId,
            ]);
        }

        if ($startDate && $endDate) {
            $builder->where(['tb_autopay_sms_parsed.transaction_date >=' => $startDate, 'tb_autopay_sms_parsed.transaction_date <=' => $endDate]);
        }

        return $builder->first();
    }

    protected function getCompanyUserShopShareStatistics($shopId)
    {
        $notificationTelegramModel = model(NotificationTelegramModel::class);
        $notificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);
        $userPermissionBankSubModel = model(UserPermissionBankSubModel::class);

        $telegram = $this->getCompanyUserShareList($notificationTelegramModel, $shopId);
        $larkmessenger = $this->getCompanyUserShareList($notificationLarkMessengerModel, $shopId);
        $employee = $this->getCompanyUserShareList($userPermissionBankSubModel, $shopId);
        
        

        $data = [
            'telegram_group_count' => count($telegram),
            'larkmessenger_group_count' => count($larkmessenger),
            'employee_count' => count($employee),
        ];

        return $data;
    }

    public function ajax_get_able_assign_pos_permission_user()
    {
        $shopId = $this->request->getGet('shop_id');
        $query = trim(xss_clean($this->request->getGet('query')));

        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])) {
            return $this->respond(['status' => false, 'message' => 'Bạn không có quyền truy cập.']);
        }

        $shopModel = model(ShopModel::class);

        $shops = $shopModel
            ->select([
                'tb_autopay_bank_shop_link.bank_account_id',
                'tb_autopay_bank_shop_link.bank_sub_account_id'
            ])
            ->join('tb_autopay_bank_shop_link', 'tb_autopay_bank_shop_link.shop_id = tb_autopay_shop.id')
            ->where('tb_autopay_shop.company_id', $this->user_session['company_id'])
            ->whereIn('tb_autopay_bank_shop_link.shop_id', $shopId)
            ->whereIn('tb_autopay_shop.id', $shopId)
            ->get()
            ->getResult();

        if (!$shops) {
            return $this->failNotFound();
        }
        
        foreach ($shops as $s) {
            $bankSubAccountId = $s->bank_sub_account_id;

            $db = db_connect();
            $builder = $db->table('tb_autopay_user')
            ->select(['tb_autopay_user.id', 'tb_autopay_user.email', 'tb_autopay_user.firstname', 'tb_autopay_user.lastname', 'tb_autopay_user.active'])
            ->join('tb_autopay_company_user', 'tb_autopay_company_user.user_id = tb_autopay_user.id')
            ->where('tb_autopay_company_user.company_id', $this->user_session['company_id'])
            ->where('tb_autopay_company_user.role', 'User')
            ->groupStart()
                ->where(new RawSql("NOT EXISTS(SELECT 1 FROM tb_autopay_user_permission_bank_sub WHERE sub_account_id = '" . $db->getConnection()->real_escape_string($bankSubAccountId) . "' AND user_id = tb_autopay_user.id)"))
                ->orGroupStart()
                    ->where(new RawSql("EXISTS(SELECT 1 FROM tb_autopay_user_permission_bank_sub WHERE sub_account_id = '" . $db->getConnection()->real_escape_string($bankSubAccountId) . "' AND user_id = tb_autopay_user.id)"))
                    ->groupStart()
                        ->where(new RawSql("NOT EXISTS(SELECT 1 FROM tb_autopay_user_permission_feature WHERE feature_slug = 'Transactions' AND can_view_all = 1 AND user_id = tb_autopay_user.id)"))
                        ->orWhere(new RawSql("NOT EXISTS(SELECT 1 FROM tb_autopay_user_permission_feature WHERE feature_slug = 'PushMobileTransactionNotification' AND can_view_all = 1 AND user_id = tb_autopay_user.id)"))
                    ->groupEnd()
                ->groupEnd()
            ->groupEnd();
        
            if ($query) {
                $builder->groupStart()
                    ->like('email', $query)
                    ->orLike('firstname', $query)
                    ->orLike('lastname', $query)
                ->groupEnd();
            }
        }

        return $this->respond([
            'data' => $builder->limit(5)->get()->getResult()
        ]);
    }


    public function ajax_assign_pos_permission_to_user()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin']))
            return $this->respond(['status' => false, 'message' => 'Bạn không có quyền truy cập.']);

        $session = service('session');

        $bank_account_mobile = $session->get('mobileapp'); 

        if (!is_array($bank_account_mobile)) show_404();

        $bankAccountModel = model(BankAccountModel::class);      
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $userModel = model(UserModel::class);

        foreach ($bank_account_mobile as $r)
        {
            $bankAccountId = $r->bank_account_id;
            $bankSubAccountId = $r->bank_sub_account_id;
            
            $bankAccountDetails = $bankAccountModel
            ->select(['tb_autopay_bank_account.id', 'tb_autopay_bank.icon_path', 'tb_autopay_bank_account.account_number'])
            ->join('tb_autopay_bank', 'tb_autopay_bank.id = tb_autopay_bank_account.bank_id')
            ->where([
                'tb_autopay_bank_account.id' => $bankAccountId,
                'tb_autopay_bank_account.company_id' => $this->user_session['company_id'],
            ])->first();

            if (!$bankAccountDetails)
                return $this->respond(['status' => false, 'message' => 'ID tài khoản ngân hàng không tồn tại.']);

            $bankSubAccountDetails = $bankSubAccountModel->where([
                'id' => $bankSubAccountId,
                'bank_account_id' => $bankAccountDetails->id
            ])->first();

            if (!$bankSubAccountDetails)
                return $this->respond(['status' => false, 'message' => 'ID tài khoản phụ không tồn tại.']);

            $userIdList = $this->request->getVar('user_id_list');
            $assignData = [
                'permissions' => [
                    'hide_amount_out' => $this->request->getVar('hide_amount_out') ? 1 : 0,
                    'hide_accumulated' => $this->request->getVar('hide_accumulated') ? 1 : 0,
                    'hide_reference_number' => $this->request->getVar('hide_reference_number') ? 1 : 0,
                    'hide_transaction_content' => $this->request->getVar('hide_transaction_content') ? 1 : 0,
                ]
            ];

            if (!is_array($userIdList))
                return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng tải lại trang.']);

            $db = db_connect();

            foreach ($userIdList as $userId) {
                $user = $db->table('tb_autopay_user')
                    ->select(['tb_autopay_user.id', 'tb_autopay_user.email', 'tb_autopay_user.firstname', 'tb_autopay_user.lastname', 'tb_autopay_user.active'])
                    ->join('tb_autopay_company_user', 'tb_autopay_company_user.user_id = tb_autopay_user.id')
                    ->where('tb_autopay_company_user.company_id', $this->user_session['company_id'])
                    ->where('tb_autopay_company_user.role', 'User')
                    ->where(new RawSql("NOT EXISTS(SELECT 1 FROM tb_autopay_user_permission_bank_sub WHERE sub_account_id = '" . $db->getConnection()->real_escape_string($bankSubAccountId) . "' AND user_id = tb_autopay_user.id)"))
                    ->get()->getRow();

                if(!$user) continue;

                $this->assignPosPermissionToUser(
                    $userId, 
                    $this->user_session['company_id'], 
                    $bankAccountDetails->id, 
                    $bankSubAccountDetails->id, 
                    $assignData['permissions'], 
                );
            }
        }
        return $this->respond(['status' => true, 'message' => 'Phân quyền thành công.']);
    }

    public function ajax_create_user_by_pos()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin']))
            return $this->respond(['status' => false, 'message' => 'Bạn không có quyền truy cập.']);

        $session = service('session');

        $bank_account_mobile = $session->get('mobileapp'); 

        if (!is_array($bank_account_mobile)) show_404();

        $bankAccountModel = model(BankAccountModel::class);      
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $userModel = model(UserModel::class);
        $companyUserModel = model(CompanyUserModel::class);
        $userPermissionFeatureModel = model(UserPermissionFeatureModel::class);

        $userData = [
            'lastname' => mb_convert_case(xss_clean($this->request->getVar('lastname')), MB_CASE_TITLE, 'UTF-8'),
            'firstname' => mb_convert_case(xss_clean($this->request->getVar('firstname')), MB_CASE_TITLE, 'UTF-8'),
            'email' => trim($this->request->getVar('email')),
            'password' => $this->request->getVar('password'),
            'permissions' => [
                'hide_amount_out' => $this->request->getVar('hide_amount_out') ? 1 : 0,
                'hide_accumulated' => $this->request->getVar('hide_accumulated') ? 1 : 0,
                'hide_reference_number' => $this->request->getVar('hide_reference_number') ? 1 : 0,
                'hide_transaction_content' => $this->request->getVar('hide_transaction_content') ? 1 : 0,
            ]
        ];

        $rules = [
            'lastname' => ['required', 'min_length[2]', 'max_length[50]'],
            'firstname' => ['required', 'min_length[2]', 'max_length[50]'],
            'email' => [
                'rules' => ['required', 'valid_email', 'is_unique[tb_autopay_user.email]'],
                'label' => 'email',
                'errors' => [
                    'is_unique' => 'Email đã được sử dụng, vui lòng sử dụng email khác'
                ]
            ],
            'password' => ['required', 'min_length[8]', 'max_length[250]'],
        ];

        if (! $this->validateData($userData, $rules)) {
            return $this->fail($this->validator->getErrors());
        }

        $safeUserData = [
            'firstname' => $userData['firstname'],
            'lastname' => $userData['lastname'],
            'email' => $userData['email'],
            'password' => password_hash($userData['password'], PASSWORD_DEFAULT),
            'active' => 1,
        ];

        $userId = $userModel->insert($safeUserData);

        if (!$userId) {
            return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng thử lại']);
        }

        $companyUserModel->insert([
            'company_id' => $this->user_session['company_id'],
            'user_id' => $userId,
            'role' => 'User'
        ]);

        foreach ($bank_account_mobile as $r)
        {
            $bankAccountId = $r->bank_account_id;
            $bankSubAccountId = $r->bank_sub_account_id;

            $bankAccountDetails = $bankAccountModel
            ->select(['tb_autopay_bank_account.id', 'tb_autopay_bank.icon_path', 'tb_autopay_bank_account.account_number'])
            ->join('tb_autopay_bank', 'tb_autopay_bank.id = tb_autopay_bank_account.bank_id')
            ->where([
                'tb_autopay_bank_account.id' => $bankAccountId,
                'tb_autopay_bank_account.company_id' => $this->user_session['company_id'],
            ])->first();

            if (!$bankAccountDetails)
                return $this->respond(['status' => false, 'message' => 'ID tài khoản ngân hàng không tồn tại.']);

            $bankSubAccountDetails = $bankSubAccountModel->where([
                'id' => $bankSubAccountId,
                'bank_account_id' => $bankAccountDetails->id
            ])->first();

            if (!$bankSubAccountDetails)
                return $this->respond(['status' => false, 'message' => 'ID tài khoản phụ không tồn tại.']);

            $userPermissionFeatureModel->initialize_permission($userId, $this->user_session['company_id']);
            $this->assignPosPermissionToUser(
                $userId, 
                $this->user_session['company_id'], 
                $bankAccountDetails->id, 
                $bankSubAccountDetails->id, 
                $userData['permissions'], 
            );

            add_user_log(['data_id' => $userId, 'company_id' => $this->user_session['company_id'], 'data_type' => 'admin_create_user', 'description' => 'Tạo người dùng', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);
        }
        return $this->respond(['status' => true, 'message' => 'Tạo nhân viên thành công']);
    }

    protected function assignPosPermissionToUser($userId, $companyId, $bankAccountId, $bankSubAccountId, $bankPermissions = [], $pushMobileTransactionNotificationFeature = 1)
    {
        $bankPermissions['hide_amount_out'] = isset($bankPermissions['hide_amount_out']) ? $bankPermissions['hide_amount_out'] : 0;
        $bankPermissions['hide_accumulated'] = isset($bankPermissions['hide_accumulated']) ? $bankPermissions['hide_accumulated'] : 0;
        $bankPermissions['hide_reference_number'] = isset($bankPermissions['hide_reference_number']) ? $bankPermissions['hide_reference_number'] : 0;
        $bankPermissions['hide_transaction_content'] = isset($bankPermissions['hide_transaction_content']) ? $bankPermissions['hide_transaction_content'] : 0;
        
        $userPermissionFeatureModel = model(UserPermissionFeatureModel::class);
        $userPermissionBankModel = model(UserPermissionBankModel::class);
        $userPermissionBankSubModel = model(UserPermissionBankSubModel::class);

        $transactionFeaturePermission = $userPermissionFeatureModel->where(['feature_slug' => 'Transactions', 'user_id' => $userId])->first();
        $pushMobileTransactionNotificationFeaturePermission = $userPermissionFeatureModel->where(['feature_slug' => 'PushMobileTransactionNotification', 'user_id' => $userId])->first();

        if (! $transactionFeaturePermission) {
            $userPermissionFeatureModel->insert([
                'user_id' => $userId,
                'company_id' => $companyId,
                'feature_slug' => 'Transactions',
                'can_view_all' => 1,
            ]);
        } else if ($transactionFeaturePermission && $transactionFeaturePermission->can_view_all == 0) {
            $userPermissionFeatureModel
                ->where(['id' => $transactionFeaturePermission->id])
                ->set(['can_view_all' => 1])
                ->update();
        }

        if ($pushMobileTransactionNotificationFeature && ! $pushMobileTransactionNotificationFeaturePermission) {
            $userPermissionFeatureModel->insert([
                'user_id' => $userId,
                'feature_slug' => 'PushMobileTransactionNotification',
                'can_view_all' => 1,
            ]);
        } else if ($pushMobileTransactionNotificationFeature && $pushMobileTransactionNotificationFeaturePermission && $pushMobileTransactionNotificationFeaturePermission->can_view_all == 0) {
            $userPermissionFeatureModel
                ->where(['id' => $pushMobileTransactionNotificationFeaturePermission->id])
                ->set(['can_view_all' => 1])
                ->update();
        } else if (!$pushMobileTransactionNotificationFeature && $pushMobileTransactionNotificationFeaturePermission) {
            $userPermissionFeatureModel
                ->where(['id' => $pushMobileTransactionNotificationFeaturePermission->id])
                ->set(['can_view_all' => 0])
                ->update();
        }

        $bankAccountPermission = $userPermissionBankModel->where(['user_id' => $userId, 'bank_account_id' => $bankAccountId])->first();
        $bankSubAccountPermission = $userPermissionBankSubModel->where(['user_id' => $userId, 'sub_account_id' => $bankSubAccountId])->first();

        if (! $bankAccountPermission) {
            $userPermissionBankModel->insert(array_merge($bankPermissions, [
                'user_id' => $userId,
                'bank_account_id' => $bankAccountId
            ]));
        } else if ($bankAccountPermission) {
            $userPermissionBankModel->where(['id' => $bankAccountPermission->id])
                ->set($bankPermissions)->update();
        }

        if (! $bankSubAccountPermission) {
            $userPermissionBankSubModel->insert([
                'user_id' => $userId,
                'sub_account_id' => $bankSubAccountId
            ]);
        }

        model(UserPermissionFeatureModel::class)->set(['can_view_all' => 1])
            ->where(['user_id' => $userId, 'company_id' => $companyId])
            ->whereIn('feature_slug', ['Transactions', 'BankAccount'])
            ->update();

        return true;
    }

    public function ajax_edit_user_by_pos()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin']))
            return $this->respond(['status' => false, 'message' => 'Bạn không có quyền truy cập.']);

        $userId = trim(xss_clean($this->request->getVar('user_id')));

        if (!is_numeric($userId)) show_404();

        $userModel = model(UserModel::class);
        $userPermissionBankModel = model(UserPermissionBankModel::class);
        $userPermissionBankSubModel = model(UserPermissionBankSubModel::class);

        $user = $userModel
            ->select(['tb_autopay_user.id', 'tb_autopay_user.email', 'tb_autopay_user.firstname', 'tb_autopay_user.lastname', 'tb_autopay_user.active'])
            ->join('tb_autopay_company_user', 'tb_autopay_company_user.user_id = tb_autopay_user.id')
            ->join('tb_autopay_user_permission_bank', 'tb_autopay_user_permission_bank.user_id = tb_autopay_user.id')
            ->join('tb_autopay_user_permission_bank_sub', 'tb_autopay_user_permission_bank_sub.user_id = tb_autopay_user.id')
            ->where('tb_autopay_company_user.company_id', $this->user_session['company_id'])
            ->where('tb_autopay_company_user.role', 'User')
            ->where('tb_autopay_user.id', $userId)
            ->first();

        if (!$user) {
            return $this->respond(['status' => false, 'message' => 'ID người dùng không tồn tại.']);
        }

        $userData = [
            'lastname' => mb_convert_case(xss_clean($this->request->getVar('lastname')), MB_CASE_TITLE, 'UTF-8'),
            'firstname' => mb_convert_case(xss_clean($this->request->getVar('firstname')), MB_CASE_TITLE, 'UTF-8'),
            'password' => $this->request->getVar('password'),
            'active' => $this->request->getVar('active'),
            'permissions' => [
                'hide_amount_out' => $this->request->getVar('hide_amount_out') ? 1 : 0,
                'hide_accumulated' => $this->request->getVar('hide_accumulated') ? 1 : 0,
                'hide_reference_number' => $this->request->getVar('hide_reference_number') ? 1 : 0,
                'hide_transaction_content' => $this->request->getVar('hide_transaction_content') ? 1 : 0,
            ]
        ];

        $rules = [
            'lastname' => ['required', 'min_length[2]', 'max_length[50]'],
            'firstname' => ['required', 'min_length[2]', 'max_length[50]'],
            'password' => ['permit_empty', 'min_length[8]', 'max_length[250]'],
        ];

        if (! $this->validateData($userData, $rules)) {
            return $this->fail($this->validator->getErrors());
        }

        $safeUserData = [
            'firstname' => $userData['firstname'],
            'lastname' => $userData['lastname'],
            'active' => $userData['active'] ? 1 : 0,
        ];

        if ($userData['password']) {
            $safeUserData['password'] = password_hash($userData['password'], PASSWORD_DEFAULT);
        }

        $userModel->where(['id' => $user->id])->set($safeUserData)->update();

        $users = $userPermissionBankModel->select(['tb_autopay_user_permission_bank.bank_account_id', 'tb_autopay_user_permission_bank_sub.sub_account_id'])
        ->join('tb_autopay_user_permission_bank_sub', 'tb_autopay_user_permission_bank_sub.user_id = tb_autopay_user_permission_bank.user_id')
        ->join('tb_autopay_bank_shop_link', 'tb_autopay_bank_shop_link.bank_sub_account_id = tb_autopay_user_permission_bank_sub.sub_account_id')
        ->join('tb_autopay_shop', 'tb_autopay_shop.id = tb_autopay_bank_shop_link.shop_id')
        ->where('tb_autopay_shop.company_id', $this->user_session['company_id'])
        ->where('tb_autopay_user_permission_bank_sub.user_id', $userId)
        ->where('tb_autopay_user_permission_bank.user_id', $userId)
        ->get()->getResult();

        foreach($users as $r){
            $this->assignPosPermissionToUser(
                $userId, 
                $this->user_session['company_id'], 
                $r->bank_account_id, 
                $r->sub_account_id, 
                $userData['permissions'],
            );
        }

        return $this->respond(['status' => true, 'message' => 'Cập nhật nhân viên thành công']);
    }

    public function ajax_revoke_pos_permission_to_user()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin']))
            return $this->respond(['status' => false, 'message' => 'Bạn không có quyền truy cập.']);

        $userId = trim(xss_clean($this->request->getVar('user_id')));
        $shopId = trim(xss_clean($this->request->getVar('shop_id')));

        if (!is_numeric($userId) || !is_numeric($shopId)) show_404();

        $userModel = model(UserModel::class);

        $user = $userModel
            ->select(['tb_autopay_user.id', 'tb_autopay_user.email', 'tb_autopay_user.firstname', 'tb_autopay_user.lastname', 'tb_autopay_user.active'])
            ->join('tb_autopay_company_user', 'tb_autopay_company_user.user_id = tb_autopay_user.id')
            ->join('tb_autopay_user_permission_bank', 'tb_autopay_user_permission_bank.user_id = tb_autopay_user.id')
            ->join('tb_autopay_user_permission_bank_sub', 'tb_autopay_user_permission_bank_sub.user_id = tb_autopay_user.id')
            ->where('tb_autopay_company_user.company_id', $this->user_session['company_id'])
            ->where('tb_autopay_company_user.role', 'User')
            ->where('tb_autopay_user.id', $userId)
            ->first();

        if (!$user) {
            return $this->respond(['status' => false, 'message' => 'ID người dùng không tồn tại.']);
        }

        $userPermissionBankSubModel = model(UserPermissionBankSubModel::class);
        
        $users = $userPermissionBankSubModel
            ->select('tb_autopay_user_permission_bank_sub.sub_account_id')
            ->join('tb_autopay_bank_shop_link bsl', 'bsl.bank_sub_account_id = tb_autopay_user_permission_bank_sub.sub_account_id')
            ->join('tb_autopay_shop s', 's.id = bsl.shop_id')
            ->where('s.company_id', $this->user_session['company_id'])
            ->where('s.id', $shopId)
            ->where('tb_autopay_user_permission_bank_sub.user_id', $user->id)
            ->get()
            ->getResult();

        foreach($users as $r)
        {
            $userPermissionBankSubModel->where(['user_id' => $user->id, 'sub_account_id' => $r->sub_account_id])->delete();
        }

        return $this->respond(['status' => true, 'message' => 'Đã gỡ quyền nhân viên thành công.']);
    }
    public function ajax_get_user_by_pos()
    {
        $userId = trim(xss_clean($this->request->getGet('user_id')));

        if (!is_numeric($userId)) show_404();

        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin']))
            return $this->respond(['status' => false, 'message' => 'Bạn không có quyền truy cập.']);

        $userModel = model(UserModel::class);
        $userPermissionBankModel = model(UserPermissionBankModel::class);
        $userPermissionFeatureModel = model(UserPermissionFeatureModel::class);

        $user = $userModel
            ->select(['tb_autopay_user.id', 'tb_autopay_user.email', 'tb_autopay_user.firstname', 'tb_autopay_user.lastname', 'tb_autopay_user.active'])
            ->join('tb_autopay_company_user', 'tb_autopay_company_user.user_id = tb_autopay_user.id')
            ->join('tb_autopay_user_permission_bank', 'tb_autopay_user_permission_bank.user_id = tb_autopay_user.id')
            ->join('tb_autopay_user_permission_bank_sub', 'tb_autopay_user_permission_bank_sub.user_id = tb_autopay_user.id')
            ->where('tb_autopay_company_user.company_id', $this->user_session['company_id'])
            ->where('tb_autopay_company_user.role', 'User')
            ->where('tb_autopay_user.id', $userId)
            ->groupBy('tb_autopay_user.id')
            ->first();

        if (!$user) {
            return $this->respond(['status' => false, 'message' => 'ID người dùng không tồn tại.']);
        }

        $bankPermission = $userPermissionBankModel
            ->select(['hide_amount_out', 'hide_accumulated', 'hide_reference_number', 'hide_transaction_content'])
            ->where(['user_id' => $user->id])->first();

        $pushMobileTransactionNotificationPermission = $userPermissionFeatureModel
            ->select(['can_view_all'])
            ->where(['user_id' => $user->id, 'feature_slug' => 'PushMobileTransactionNotification'])->first();

        if (!$bankPermission) {
            return $this->respond(['status' => false, 'message' => 'Người dùng không có quyền với tài khoản ngân hàng này.']);
        }

        return $this->respond([
            'data' => array_merge((array) $user, [
                'permissions' => (array) $bankPermission
            ])
        ]);
    }

    public function ajax_shop_telegram_delete()
    {
        if ($this->request->getMethod(true) != 'POST')
        return '';

        if(!has_permission('NotificationTelegram', 'can_delete'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền xóa dữ liệu này"));

        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'id' => 'required|integer|is_natural',
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }

        $shop_id = trim($this->request->getPost('id'));

        $chat_id = trim($this->request->getPost('chat_id'));

        if(substr($chat_id,0,1) != "-")
            return $this->response->setJSON(array('status'=>FALSE,'message'=>'Chat ID phải bắt đầu bằng dấu trừ (-). Ví dụ -********'));

        $shopModel = model(ShopModel::class);

        $shops = $shopModel->
        select(['tb_autopay_bank_shop_link.bank_account_id', 'tb_autopay_bank_shop_link.bank_sub_account_id'])
        ->join('tb_autopay_bank_shop_link', 'tb_autopay_bank_shop_link.shop_id = tb_autopay_shop.id')
        ->where('tb_autopay_bank_shop_link.shop_id', $shop_id)
        ->where('tb_autopay_shop.company_id', $this->user_session['company_id'])
        ->get()
        ->getResult();

        foreach($shops as $shop)
        {
            $notificationTelegramModel = model(NotificationTelegramModel::class);
            $telegram_details = $notificationTelegramModel
            ->where([
                'bank_account_id' => $shop->bank_account_id,
                'sub_account_id' => $shop->bank_sub_account_id,
                'chat_id'=>$chat_id,
                'company_id'=>$this->user_session['company_id']
                ])->get()->getRow();

            if((!is_object($telegram_details)))
                return $this->response->setJSON(array('status'=>FALSE,'message'=>"Không tìm thấy dữ liệu này này"));

                $notificationTelegramModel->delete($telegram_details->id);
        }

        add_user_log(array('data_id'=> $chat_id,'company_id' => $this->user_session['company_id'], 'data_type'=>'notification_telegram_delete','description'=>'Xóa tích hợp thông báo Telegram','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));

        return $this->response->setJSON(array("status"=>true));
    }

    public function ajax_shop_larkmessenger_delete()
    {
        if ($this->request->getMethod(true) != 'POST')
        return '';

        if(!has_permission('NotificationLarkMessenger', 'can_delete'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền xóa dữ liệu này"));

        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'id' => 'required|integer|is_natural',
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }

        $shop_id = trim($this->request->getPost('id'));

        $bot_webhook_url = trim($this->request->getPost('bot_webhook_url'));

        $shopModel = model(ShopModel::class);

        $shops = $shopModel->
        select(['tb_autopay_bank_shop_link.bank_account_id', 'tb_autopay_bank_shop_link.bank_sub_account_id'])
        ->join('tb_autopay_bank_shop_link', 'tb_autopay_bank_shop_link.shop_id = tb_autopay_shop.id')
        ->where('tb_autopay_bank_shop_link.shop_id', $shop_id)
        ->where('tb_autopay_shop.company_id', $this->user_session['company_id'])
        ->get()
        ->getResult();

        foreach($shops as $shop)
        {
            $notificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);
            $larkmessenger_details = $notificationLarkMessengerModel
            ->where([
                'bank_account_id' => $shop->bank_account_id,
                'sub_account_id' => $shop->bank_sub_account_id,
                'bot_webhook_url'=>$bot_webhook_url,
                'company_id'=>$this->user_session['company_id']
                ])->get()->getRow();

            if((!is_object($larkmessenger_details)))
                return $this->response->setJSON(array('status'=>FALSE,'message'=>"Không tìm thấy dữ liệu này này"));

                $notificationLarkMessengerModel->delete($larkmessenger_details->id);
        }


        add_user_log(array('data_id'=> $bot_webhook_url,'company_id' => $this->user_session['company_id'], 'data_type'=>'notification_lark_messenger_delete','description'=>'Xóa tích hợp thông báo Lark Messenger','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));

        return $this->response->setJSON(array("status"=>true));
    }

    protected function getChannelPartnerShop()
    {
        return model(ShopModel::class)->where('company_id', $this->user_session['company_id'])
            ->countAllResults();
    }

    protected function getChannelPartnerShopLimit()
    {
        $companySubscriptionModel = model(CompanySubscriptionModel::class);
        $data['subscription_details'] = $companySubscriptionModel->where(['company_id' => $this->user_session['company_id']])->get()->getRow();

        $productModel = model(ProductModel::class);

        if(is_object($data['subscription_details'])) {
            return $productModel->select(['shop_limit'])->where(['id' => $data['subscription_details']->plan_id,
            'channel_partner_id' => $this->channel_partner['id']])->get()->getRow();
        }
    }

    protected function checkShopLimit()
    {
        if($this->channel_partner){
            $shop_used = $this->getChannelPartnerShop();
            $shop_limit = $this->getChannelPartnerShopLimit();
            if(number_format($shop_used) >= number_format($shop_limit->shop_limit)){
                return true;
            }else{
                return null;
            }
        }else{
            return null;
        }
    }

    protected function countCompanyShop()
    {
        return model(ShopModel::class)->where(['company_id' => $this->company_details->id])->countAllResults();
    }
}
