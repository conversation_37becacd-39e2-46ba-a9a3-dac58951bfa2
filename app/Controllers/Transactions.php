<?php

namespace App\Controllers;

use App\Features\Store\StoreFeature;
use App\Models\UserModel;
use App\Models\FcmLogModel;
use CodeIgniter\Controller;
use App\Models\UserLogModel;
use App\Models\BankAccountModel;
use App\Models\TransactionsModel;
use DateTime;
use App\Models\UserPermissionBankModel;
use App\Models\NotificationLarkMessengerQueueModel;
use App\Models\ShopModel;
use App\Models\BankModel;
use App\Models\BankSubAccountModel;
use App\Models\NotificationTelegramQueueModel;
use App\Models\NotificationViberQueueModel;
use App\Models\WebhooksLogModel;
use App\Models\OutputDeviceReplayMessageQueueModel;

class Transactions extends BaseController
{
     
    public function ajax_transactions_list() {
     
        if ($this->request->getMethod(true) != 'POST')
            return '';
            
        $isSpeakerBillingSubscription = is_speaker_billing_subscription();

        $bank_account_id = trim(xss_clean($this->request->getPost('bank_id')));
        $bank_sub_account = xss_clean($this->request->getPost('sub_banks'));
        $date_range = trim(xss_clean($this->request->getPost('date_range')));
        $transaction_type = trim(xss_clean($this->request->getPost('type')));
        $amount_range = trim(xss_clean($this->request->getPost('amount')));

        if(!is_numeric($bank_account_id))
            $bank_account_id = FALSE;
 
        $transactionsModel = slavable_model(TransactionsModel::class, 'Transactions');

        $configBankSubAccount = get_configuration('BankSubAccount');
        $configPayCode = get_configuration('PayCode');

        if($configBankSubAccount == 'off')
            $hide_sub_account = TRUE;
        else
            $hide_sub_account = FALSE;

        if($configPayCode == 'off')
            $hide_code = TRUE;
        else
            $hide_code = FALSE;


        if(in_array($this->company_details->role,['Admin','SuperAdmin']))
            $is_admin = true;
        else
            $is_admin = false;

        if(!$is_admin && !$bank_account_id && $configBankSubAccount != "off" && $configPayCode != "off")
            return $this->response->setJSON(array(
                "recordsTotal" => 0,
                "recordsFiltered" => 0,
                "data" => [],
            ));

        $user_permissions = [];
        $hide_amount_out = FALSE;

        if(!$is_admin) {
            //build permission array
            
            $UserPermissionBankModel = model(UserPermissionBankModel::class);
            $results = $UserPermissionBankModel->select("tb_autopay_user_permission_bank.bank_account_id,tb_autopay_bank_account.account_number,tb_autopay_user_permission_bank.hide_amount_out,tb_autopay_user_permission_bank.hide_accumulated,tb_autopay_user_permission_bank.hide_reference_number,tb_autopay_user_permission_bank.hide_transaction_content")->join("tb_autopay_bank_account","tb_autopay_user_permission_bank.bank_account_id=tb_autopay_bank_account.id")->where(['tb_autopay_user_permission_bank.user_id' => $this->user_details->id])->get()->getResult();
            
            foreach($results as $result) {
                $user_permissions[$result->account_number]['hide_amount_out'] = $result->hide_amount_out;
                $user_permissions[$result->account_number]['hide_accumulated'] = $result->hide_accumulated;
                $user_permissions[$result->account_number]['hide_reference_number'] = $result->hide_reference_number;
                $user_permissions[$result->account_number]['hide_transaction_content'] = $result->hide_transaction_content;

                if($result->bank_account_id == $bank_account_id && $result->hide_amount_out == 1)
                    $hide_amount_out = TRUE;

            }
            
        }
        
        if (is_speaker_billing_subscription()) {
            $transaction_type = 'in';
        }
        
        
        $transactions = $transactionsModel->getDatatables($this->user_session['company_id'], $bank_account_id, $this->company_details->role, $configBankSubAccount, $configPayCode, $hide_amount_out, $this->user_details->id, $date_range, $transaction_type, $bank_sub_account, $amount_range);

        $can_view_telegram_log = has_permission('TelegramLog', 'can_view_all');
        $can_view_webhooks_log = has_permission('WebhooksLog', 'can_view_all');
        $can_view_lark_messenger_log = has_permission('LarkMessengerLog', 'can_view_all');

        $data = array();

        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');

        $mobilePushCounters = null;

        if (count($transactions)) {
            $fcmLogModel = slavable_model(FcmLogModel::class, 'Transactions');
            $mobilePushCounters = $fcmLogModel->select(['transaction_id', 'COUNT(transaction_id) as count'])->whereIn('transaction_id', array_column($transactions, 'id'))->groupBy('transaction_id')->get()->getResult();
        }


        // Check log Loa
        $outputDevicePushCounters = null;
        if (count($transactions)) {
            $OutputDeviceReplayMessageQueueModel = slavable_model(OutputDeviceReplayMessageQueueModel::class, 'Transactions');
            $outputDevicePushCounters = $OutputDeviceReplayMessageQueueModel->select(['transaction_id', 'COUNT(transaction_id) as count', 'status'])->whereIn('transaction_id', array_column($transactions, 'id'))->groupBy('transaction_id')->get()->getResult();
        }

        foreach ($transactions as $transaction) {
            
            $no++;
            $row = array();
            
            $row[] = $no;
            $row[] = esc($transaction->id);

            if($transaction->label)
                $label = " - " . $transaction->label;
            else
                $label = '';

            $row[] = "<span data-bs-toggle='tooltip' data-bs-title='".$transaction->brand_name ." - ". $transaction->account_holder_name . $label ."'><img src='" . esc(base_url('assets/images/banklogo/' . $transaction->icon_path)) . "' class='avatar img-fluid rounded-circle m-auto' style='height:20px; width:20px'> ". esc($transaction->account_number) . "</span>";

            if($hide_sub_account === FALSE)
                $row[] = esc($transaction->sub_account);
 

            if($transaction->amount_in > 0){
                $row[] = "<span class='text-success'>+" . number_format($transaction->amount_in) . "</span>";
            }else {
                if($transaction->amount_out> 0 && !$is_admin && $user_permissions[$transaction->account_number]['hide_amount_out'] == 1)
                    $row[] = '***';
                else {
                    $row[] = "<span class='text-danger'>-" .number_format($transaction->amount_out)  . "</span>";
                } 
            }

        
            if(!$is_admin && isset($user_permissions[$transaction->account_number]) && isset($user_permissions[$transaction->account_number]['hide_accumulated']) && $user_permissions[$transaction->account_number]['hide_accumulated'] == 1)
                $row[] = '***';
            else
                $row[] = number_format($transaction->accumulated);

            if($transaction->amount_in > 0)
                $row[] = "<span class='text-success'>Tiền vào</span>";
            else
                $row[] = "<span class='text-danger'>Tiền ra</span>";
            
                $transaction_date = date('H:i:s d-m-Y', strtotime($transaction->transaction_date));
                $row[] = $transaction_date;
            
            if($hide_code === FALSE){
                $row[] = esc($transaction->code);
            }

            $row[] = $transaction->reference_number;

            if (!$is_admin && $user_permissions[$transaction->account_number]['hide_transaction_content'] == 1) {
                $transaction_content_text = '***';
            } else {
                $transaction_content_text = character_limiter(esc($transaction->transaction_content ?? ''), 60);
            }
            
            $data_content_text = $transaction_content_text !== '***' 
                ? esc($transaction->transaction_content ?? '') 
                : $transaction_content_text;
            
            $row[] = "<span data-content-text='" . $data_content_text . "'>" . $transaction_content_text . "</span>";
            
            if($can_view_webhooks_log)
                $show_webhooks_log_text = "onclick='show_webhooks_log(".$transaction->id.")' style='cursor:pointer'";
            else
                $show_webhooks_log_text = "";

       
            $automation_text = '';
            $total_webhook_sent = $transaction->webhooks_success +  $transaction->webhooks_failed;
            if($transaction->webhooks_verify_payment == 'Success') {
                $checked_label = "<div class='checked-label-primary'></div>";
                $automation_text = "<span class='text-success' ".$show_webhooks_log_text." data-bs-toggle='tooltip1' data-bs-title='Xác thực thanh toán tự động: Thành công'>Pay<i class='bi bi-chevron-right'></i></span>";
            } else if($transaction->webhooks_verify_payment == 'Failed') {
                $checked_label = "<div class='checked-label-warning'></div>";
                $automation_text = "<span class='text-warning' ".$show_webhooks_log_text." data-bs-toggle='tooltip1' data-bs-title='Xác thực thanh toán tự động: Thất bại. Không có dữ liệu trả về {\"success\": true, ....} theo quy ước'>Pay<i class='bi bi-chevron-right'></i></span>";
            } else {
                $checked_label = '';
               // $automation_text = "<span class='text-muted' ".$show_webhooks_log_text." data-bs-toggle='tooltip1' data-bs-title='Xác thực thanh toán tự động: Không thực hiện. Không có WebHooks xác thực thanh toán nào được gửi đi liên quan đến giao dịch này'><i class='bi bi-circle-fill'></i> Pay " .$total_webhook_sent ."<i class='bi bi-chevron-right'></i></span>";
               $automation_text = "";

            }

            if (!$isSpeakerBillingSubscription) {
                if($total_webhook_sent > 0) {
                    $automation_text = $automation_text . "<span class='text-primary' ".$show_webhooks_log_text." data-bs-toggle='tooltip1' data-bs-title='Webhook đã gửi đi: ".$total_webhook_sent."'>Hook <span class=''>" . $total_webhook_sent ."</span><i class='bi bi-chevron-right'></i></span>";
    
                } else {
                    $automation_text = $automation_text . "<span class='text-muted' ".$show_webhooks_log_text." data-bs-toggle='tooltip1' data-bs-title='Webhook đã gửi đi: ".$total_webhook_sent."'>Hook " . $total_webhook_sent ."<i class='bi bi-chevron-right'></i></span>";
    
                }
            }

               
            if (!$isSpeakerBillingSubscription) {
                if ($transaction->chat_push_message > 0) {
                    if ($can_view_telegram_log || $can_view_lark_messenger_log) {
                        $automation_text = $automation_text . "  <a class='ms-2 text-info text-decoration-none' onclick='show_telegram_log(".$transaction->id.")' data-bs-toggle='tooltip1' data-bs-title='Số lượng tin chat đã gửi đi: ".$transaction->chat_push_message."'><i class='bi bi-chat-text'></i> ".$transaction->chat_push_message."<i class='bi bi-chevron-right'></i></a>";
                    } else {
                        $automation_text = $automation_text . "  <span class='ms-2 text-info'  data-bs-toggle='tooltip1' data-bs-title='Số lượng tin chat đã gửi đi: ".$transaction->chat_push_message."'><i class='bi bi-chat-text'></i> ".$transaction->chat_push_message."</span>";
                    }
                } else {
                    $automation_text = $automation_text . "  <span class='ms-2 text-muted' data-bs-toggle='tooltip1' data-bs-title='Số lượng tin chat đã gửi đi: ".$transaction->chat_push_message."'><i class='bi bi-chat-text'></i> ".$transaction->chat_push_message."</span><i class='bi bi-chevron-right'></i>";
                }
            }

            if (in_array($this->company_details->role, ['Admin', 'SuperAdmin']) && !$isSpeakerBillingSubscription) {
                $mobilePushCount = array_values(array_filter($mobilePushCounters, function($counter) use ($transaction) {
                    return $counter->transaction_id == $transaction->id;
                }))[0]->count ?? 0;

                $automation_text .= "<a " . ($mobilePushCount > 0 ? "onclick=\"showFcmLogModal(".$transaction->id.", '" . esc($transaction->datecreated) . "')\"" : "") . " class='ms-2 "  . ($mobilePushCount > 0 ? 'text-info' : 'text-muted') . "' style='text-decoration: none;'><span data-bs-toggle='tooltip1' data-bs-title='Mobile app đã gửi đi: " . $mobilePushCount . "'><i class='bi-phone-vibrate fs-4'></i> " . $mobilePushCount . "<i class='bi bi-chevron-right'></i></span></a>";
            }

            // check tích hợp Loa
            if (in_array($this->company_details->role, ['Admin', 'SuperAdmin'])) {
                $outputDevicePushCount = array_values(array_filter($outputDevicePushCounters, function($counter) use ($transaction) {
                    return $counter->transaction_id == $transaction->id;
                }))[0]->count ?? 0;

                if ($outputDevicePushCount > 0) {
                    $outputDeviceStatus = array_values(array_filter($outputDevicePushCounters, function($counter) use ($transaction) {
                        return $counter->transaction_id == $transaction->id;
                    }))[0]->status ?? '';

                    if ($outputDeviceStatus == 'Pending') {
                        $outputDevicePushColor = 'text-warning';
                    } elseif (in_array($outputDeviceStatus, ['Sending', 'Success'])) {
                        $outputDevicePushColor = 'text-info';
                    } elseif ($outputDeviceStatus == 'Failed') {
                        $outputDevicePushColor = 'text-danger';
                    } else {
                        $outputDevicePushColor = 'text-info';
                    }
                } else {
                    $outputDevicePushColor = 'text-muted';
                }

                $automation_text .= "<a " . ($outputDevicePushCount > 0 ? "onclick=\"showOutputDeviceLogModal(".$transaction->id.", '" . esc($transaction->datecreated) . "')\"" : "") . " class='ms-2 " . $outputDevicePushColor . "' style='text-decoration: none;'><span data-bs-toggle='tooltip1' data-bs-title='Loa đã gửi đi: " . $outputDevicePushCount . "'><i class='bi bi-megaphone fs-4'></i> " . $outputDevicePushCount . "</span></a>";
            }


            $row[] = $automation_text;

            /*
            if(!$is_admin && $user_permissions[$transaction->account_number]['hide_reference_number'] == 1)
                $row[] = '***';
            else
                $row[] = esc($transaction->reference_number);
            */
                
            $row[] = "<a class='' href='" . base_url('transactions/details/' . $transaction->id) .  "'>Xem<i class='bi bi-chevron-right'></i></a>";

            
            if($transaction->amount_in > 0)
                $row[] = "<a class='link-dark text-decoration-none' href='".base_url('transactions/details/' . $transaction->id)."'><div><img src='" . esc(base_url('assets/images/banklogo/' . $transaction->icon_path)) . "' class='avatar img-fluid rounded-circle m-auto' style='height:20px; width:20px'> <span class='text-muted fs-7'>". esc($transaction->account_number). "</span><span class='fw-bold float-end fs-5 text-success'>+ " . number_format($transaction->amount_in) . " đ</span>".$checked_label."</div><div class='mt-1'>" . esc(ellipsize($transaction_content_text ?? '',25,1,'..')).  "<span class='fs-7 float-end text-muted'>".timespan($transaction->transaction_date,1)."</span></div></a>";
            else if($transaction->amount_out> 0 && !$is_admin && $user_permissions[$transaction->account_number]['hide_amount_out'] == 1)
                $row[] = '***';
            else {
                $row[] = "<a class='link-dark text-decoration-none' href='".base_url('transactions/details/' . $transaction->id)."'><div><img src='" . esc(base_url('assets/images/banklogo/' . $transaction->icon_path)) . "' class='avatar img-fluid rounded-circle m-auto' style='height:20px; width:20px'> <span class='text-muted  fs-7'>". esc($transaction->account_number). "</span><span class='fw-bold float-end fs-5 text-danger'>- " . number_format($transaction->amount_out) . " đ</span>".$checked_label."</div><div class='mt-1'>" . esc(ellipsize($transaction_content_text ?? '',25,1,'..')).  "<span class='fs-7 float-end text-muted'>".timespan($transaction->transaction_date,1)."</span></div></a>";
            }

            $data[] = $row;
        }
 
        $output = array(
            "draw" => $draw,
            "recordsTotal" => $transactionsModel->countTransactionsAll($this->user_session['company_id'], $bank_account_id, $this->company_details->role, $configBankSubAccount, $hide_amount_out, $date_range, $transaction_type, $bank_sub_account, $amount_range),
            "recordsFiltered" => $transactionsModel->countTransactionsFiltered($this->user_session['company_id'], $bank_account_id, $this->company_details->role, $configBankSubAccount, $configPayCode, $hide_amount_out, $this->user_details->id, $date_range, $transaction_type, $bank_sub_account, $amount_range),
            "data" => $data,
        );
        return $this->response->setJSON($output);
    
    }

    public function ajax_list_bank_sub_account() {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->response->setJSON(['status' => false, 'message' => 'Method Not Allowed']);
        }
    
        $bank_account_id = trim(xss_clean($this->request->getPost('account_id')));
        if (!is_numeric($bank_account_id)) {
            return $this->response->setJSON(['status' => false, 'message' => 'Invalid bank account ID']);
        }
    
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $userPermissionBankSubModel = model(UserPermissionBankSubModel::class);
        $role = $this->company_details->role;
    
        if (in_array($role, ['Admin', 'SuperAdmin'])) {
            $result = $bankSubAccountModel->where([
                'bank_account_id' => $bank_account_id,
                'active' => 1
            ])->findAll();
        } else {
            $configBankSubAccount = get_configuration('BankSubAccount');
            if ($configBankSubAccount == 'on' && $bank_account_id) {
                $result = $userPermissionBankSubModel->where([
                    'tb_autopay_user_permission_bank_sub.user_id' => $this->user_details->id,
                    'tb_autopay_bank_sub_account.bank_account_id' => $bank_account_id
                ])
                ->join("tb_autopay_bank_sub_account", "tb_autopay_bank_sub_account.id=tb_autopay_user_permission_bank_sub.sub_account_id")
                ->orderBy('tb_autopay_bank_sub_account.id', 'ASC')
                ->get()
                ->getResult();
            } else {
                $result = [];
            }
        }
    
        if (!empty($result)) {
            foreach ($result as &$item) {
                if (isset($item->sub_holder_name)) {
                    $item->sub_holder_name = esc($item->sub_holder_name);
                }
                if (isset($item->label)) {
                    $item->label = esc($item->label);
                }
                if (isset($item->sub_account)) {
                    $item->sub_account = esc($item->sub_account);
                }
            }
            unset($item);
        }
    
        return $this->response->setJSON([
            'status' => true,
            'data' => $result
        ]);
    }

    public function details($id='') {
        if(!is_numeric($id))
            show_404();

        if(!has_permission('Transactions', 'can_view_all'))
            show_404();

    
        $data = [
            'page_title' => 'Giao dịch',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];
            
        $transactionsModel = slavable_model(TransactionsModel::class, 'Transactions');

        $data['transaction_details'] = $transactionsModel->select("tb_autopay_sms_parsed.id ,tb_autopay_bank.brand_name, tb_autopay_bank_account.account_number,tb_autopay_bank_account.account_holder_name, tb_autopay_sms_parsed.transaction_date, tb_autopay_sms_parsed.amount_out,tb_autopay_sms_parsed.amount_in,tb_autopay_sms_parsed.accumulated,tb_autopay_sms_parsed.transaction_content,tb_autopay_sms_parsed.reference_number,tb_autopay_bank_account.company_id, tb_autopay_sms_parsed.code,tb_autopay_sms_parsed.webhooks_success,tb_autopay_sms_parsed.webhooks_failed,tb_autopay_sms_parsed.sub_account,tb_autopay_bank.icon_path, tb_autopay_bank_account.id as `bank_account_id`,tb_autopay_sms_parsed.webhooks_verify_payment,tb_autopay_sms_parsed.datecreated,tb_autopay_sms_parsed.body,tb_autopay_sms_parsed.chat_push_message,tb_autopay_bank.full_name,tb_autopay_sms_parsed.from_account_number, tb_autopay_sms_parsed.from_account_name")
            ->join("tb_autopay_bank","tb_autopay_bank.brand_name=tb_autopay_sms_parsed.gateway")
            ->join("tb_autopay_bank_account","tb_autopay_bank_account.id=tb_autopay_sms_parsed.bank_account_id")->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id'], 'tb_autopay_sms_parsed.id' => $id,'tb_autopay_sms_parsed.parser_status' => 'Success','tb_autopay_sms_parsed.deleted_at'=> NULL])->get()->getRow();

        if(!is_object($data['transaction_details']))
            show_404();

        if(in_array($this->company_details->role,['Admin','SuperAdmin']))
            $data['is_admin'] = true;
        else
            $data['is_admin'] = false;

        $data['user_permissions'] = [];
        if(!$data['is_admin']) {
            //build permission array
            
            $UserPermissionBankModel = model(UserPermissionBankModel::class);

            $configBankSubAccount = get_configuration('BankSubAccount');

            $bankSubAccountModel = model(BankSubAccountModel::class);
            $count_sub = $bankSubAccountModel->where(['bank_account_id' => $data['transaction_details']->bank_account_id])->countAllResults();

            //
            if($configBankSubAccount == "on" && $count_sub > 0 && $data['transaction_details']->sub_account) {
                $UserPermissionBankSubModel = model(UserPermissionBankSubModel::class);
                $result = $UserPermissionBankSubModel->select("tb_autopay_user_permission_bank_sub.id")->join("tb_autopay_bank_sub_account","tb_autopay_bank_sub_account.id=tb_autopay_user_permission_bank_sub.sub_account_id")->where(["tb_autopay_bank_sub_account.sub_account" => $data['transaction_details']->sub_account, "tb_autopay_user_permission_bank_sub.user_id" => $this->user_details->id])->get()->getRow();

                if(!is_object($result))
                    show_404();
                
            }

            $result = $UserPermissionBankModel->select("tb_autopay_user_permission_bank.bank_account_id,tb_autopay_bank_account.account_number,tb_autopay_user_permission_bank.hide_amount_out,tb_autopay_user_permission_bank.hide_accumulated,tb_autopay_user_permission_bank.hide_reference_number,tb_autopay_user_permission_bank.hide_transaction_content")->join("tb_autopay_bank_account","tb_autopay_user_permission_bank.bank_account_id=tb_autopay_bank_account.id")->where(['tb_autopay_user_permission_bank.user_id' => $this->user_details->id, 'bank_account_id' => $data['transaction_details']->bank_account_id])->get()->getRow();
            
            if(is_object($result)) {
                if($result->hide_amount_out == 1 && $data['transaction_details']->amount_out > 0)
                    show_404();
                $data['hide_amount_out'] = $result->hide_amount_out;
                $data['hide_accumulated'] = $result->hide_accumulated;
                $data['hide_reference_number'] = $result->hide_reference_number;
                $data['hide_transaction_content'] = $result->hide_transaction_content;
            } else
                show_404();
            
        }

        $webhooksLogModel = slavable_model(WebhooksLogModel::class, 'Transactions');
        $data['count_webhooks_log'] = $webhooksLogModel->where(['sms_parsed_id' => $id])->countAllResults();

        $fcmLogModel = slavable_model(FcmLogModel::class, 'Transactions');
        $data['count_fcm_log'] = $fcmLogModel->where(['transaction_id' => $id])->countAllResults();
        
        $speakerLogModel = slavable_model(OutputDeviceReplayMessageQueueModel::class, 'Transactions');
        $data['count_speaker_log'] = $speakerLogModel->where(['transaction_id' => $id])->countAllResults();

        $data['user_ip'] = $this->request->getIPAddress();

        echo theme_view('templates/autopay/header',$data);
        echo theme_view('transactions/details',$data);
        echo theme_view('templates/autopay/footer',$data);
    }

    function ajax_get_telegram_log($transaction_id='') {

        $perTelegramLog = has_permission('TelegramLog', 'can_view_all');
        $perLarkMessengerLog = has_permission('LarkMessengerLog', 'can_view_all');
        $perViberLog = has_permission('ViberLog', 'can_view_all');

        if(!$perTelegramLog && !$perLarkMessengerLog && !$perViberLog) {
            echo "Bạn chưa có quyền truy cập tính năng này";
            return FALSE;
        }

        if(!is_numeric($transaction_id)) 
            show_404();
    
        $notificationTelegramQueueModel = model(NotificationTelegramQueueModel::class);
        $notificationLarkMessengerQueueModel = model(NotificationLarkMessengerQueueModel::class);
        $notificationViberQueueModel = model(NotificationViberQueueModel::class);
        $transactionsModel = model(TransactionsModel::class);

        $transaction_details = $transactionsModel->select("tb_autopay_sms_parsed.id, tb_autopay_bank_account.id as `bank_account_id`,tb_autopay_sms_parsed.datecreated")
            ->join("tb_autopay_bank_account","tb_autopay_bank_account.id=tb_autopay_sms_parsed.bank_account_id")
            ->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id'], 'tb_autopay_sms_parsed.id' => $transaction_id,'tb_autopay_sms_parsed.parser_status' => 'Success', 'tb_autopay_sms_parsed.deleted_at' => NULL])->get()->getRow();
        
        if(!is_object($transaction_details))
            show_404();

        if(!has_bank_permission($transaction_details->bank_account_id)) {
            echo "Bạn chưa có quyền truy cập tính năng này";
            return FALSE;
        }
        
        $results = $notificationTelegramQueueModel->select("tb_autopay_notification_telegram_queue.id, tb_autopay_notification_telegram_queue.chat_id,tb_autopay_notification_telegram_queue.message, tb_autopay_notification_telegram_queue.status,tb_autopay_notification_telegram_queue.created_at,tb_autopay_notification_telegram.description")->join("tb_autopay_notification_telegram","tb_autopay_notification_telegram.id=tb_autopay_notification_telegram_queue.notify_id")->join("tb_autopay_sms_parsed","tb_autopay_sms_parsed.id=tb_autopay_notification_telegram_queue.transaction_id")->where(['tb_autopay_notification_telegram_queue.transaction_id' => $transaction_id, 'tb_autopay_notification_telegram_queue.deleted_at' => NULL])->orderBy("tb_autopay_notification_telegram_queue.id",'ASC')->get()->getResult();
        
        $html = "<div><ul><li>ID giao dịch: <a href='".base_url('transactions/details/'. $transaction_id)."'>" .$transaction_id. "</a></li><li>Lúc: ".$transaction_details->datecreated."</li></ul></div>";
        
        if($perTelegramLog) {
            $html = $html . "<h5>Báo qua Telegram</h5><div class='table-responsive'><table class='table'><thead><th>Tên tích hợp</th><th>Trạng thái gửi</th><th>Lúc</th></thead><tbody>";
            if(count($results) == 0) {
                $html = $html . "<tr><td colspan='3' class='text-center'><em>Không có dữ liệu</em></td></tr>";
            }
            foreach($results as $result) {
                if($result->status == "Success")
                    $status_text = "<span class='text-success'>Thành công</span>";
                else if($result->status == "Failed")
                    $status_text = "<span class='text-danger'>Thất bại</span>";
                else
                    $status_text = "<span class='text-warning'>".esc($result->status)."</span>";
    
                $html = $html . "<tr>";
                $html = $html . "<td><span class='text-info'><i class='bi bi-telegram me-2'></i></span> ".esc($result->description)."<br> <a data-bs-toggle='collapse' href='#collaptelegram_".$result->id."' role='button' aria-expanded='false' aria-controls='collaptelegram_".$result->id."'>
                Nội dung <i class='bi bi-chevron-right'></i>
              </a><div class='collapse' id='collaptelegram_".esc($result->id)."'>
              <div class='card card-body'>
               ". nl2br(htmlspecialchars(strip_tags($result->message)))."
              </div>
            </div></td><td>" . $status_text . "</td><td>" . esc($result->created_at) . "</td>";
                $html = $html . "</tr>";
            }
            $html = $html . "</tbody></table></div>";
        }
                
        if($perLarkMessengerLog) {
            $results = $notificationLarkMessengerQueueModel->select("tb_autopay_notification_larkmessenger_queue.id, tb_autopay_notification_larkmessenger_queue.bot_webhook_url,tb_autopay_notification_larkmessenger_queue.message, tb_autopay_notification_larkmessenger_queue.status,tb_autopay_notification_larkmessenger_queue.created_at,tb_autopay_notification_larkmessenger.description")->join("tb_autopay_notification_larkmessenger", "tb_autopay_notification_larkmessenger.id=tb_autopay_notification_larkmessenger_queue.notify_id")->join("tb_autopay_sms_parsed", "tb_autopay_sms_parsed.id=tb_autopay_notification_larkmessenger_queue.transaction_id")->where(['tb_autopay_notification_larkmessenger_queue.transaction_id' => $transaction_id, 'tb_autopay_notification_larkmessenger_queue.deleted_at' => NULL])->orderBy("tb_autopay_notification_larkmessenger_queue.id", 'ASC')->get()->getResult();

            $html = $html . "<h5>Báo qua Lark Messenger</h5><div class='table-responsive'><table class='table'><thead><th>Tên tích hợp</th><th>Trạng thái gửi</th><th>Lúc</th></thead><tbody>";
            if(count($results) == 0) {
                $html = $html . "<tr><td colspan='3' class='text-center'><em>Không có dữ liệu</em></td></tr>";
            }
            foreach($results as $result) {
                if($result->status == "Success") {
                    $status_text = "<span class='text-success'>Thành công</span>";
                } elseif($result->status == "Failed") {
                    $status_text = "<span class='text-danger'>Thất bại</span>";
                } else {
                    $status_text = "<span class='text-warning'>".esc($result->status)."</span>";
                }

                $html = $html . "<tr>";
                $html = $html . "<td><span class='text-info'><img src='". base_url('assets/images/other/lark-icon.png') . "' class='img-fluid me-2' style='max-width: 15px;'></span> ".esc($result->description)."<br> <a data-bs-toggle='collapse' href='#collaplark_".$result->id."' role='button' aria-expanded='false' aria-controls='collaplark_".$result->id."'>
                    Nội dung <i class='bi bi-chevron-right'></i>
                </a><div class='collapse' id='collaplark_".$result->id."'>
                <div class='card card-body'>
                ". nl2br(htmlspecialchars(strip_tags($result->message)))."
                </div>
                </div></td><td>" . $status_text . "</td><td>" . $result->created_at . "</td>";
                $html = $html . "</tr>";
            }
            $html = $html . "</tbody></table></div>";
        }

        if ($perViberLog) {
            $results = $notificationViberQueueModel
                ->select([
                    'tb_autopay_notification_viber_queue.id',
                    'tb_autopay_notification_viber_queue.message',
                    'tb_autopay_notification_viber_queue.status',
                    'tb_autopay_notification_viber_queue.created_at',
                    'tb_autopay_notification_viber.description',
                ])
                ->join('tb_autopay_notification_viber', 'tb_autopay_notification_viber.id=tb_autopay_notification_viber_queue.notify_id')
                ->join('tb_autopay_sms_parsed', 'tb_autopay_sms_parsed.id=tb_autopay_notification_viber_queue.transaction_id')
                ->where(['tb_autopay_notification_viber_queue.transaction_id' => $transaction_id, 'tb_autopay_notification_viber_queue.deleted_at' => NULL])
                ->orderBy("tb_autopay_notification_viber_queue.id",'ASC')
                ->findAll();

            $html .= view('transactions/notifications/viber', ['results' => $results]);
        }
        echo $html;
    }



    function ajax_get_webhooks_log($transaction_id='') {

        if(!has_permission('WebhooksLog', 'can_view_all')) {
            echo "Bạn chưa có quyền truy cập tính năng này";
            return FALSE;
        }

        if(!is_numeric($transaction_id)) 
            show_404();
        $webhooksLog = slavable_model(WebhooksLogModel::class, 'Transactions');
        $transactionsModel = slavable_model(TransactionsModel::class, 'Transactions');

        $transaction_details = $transactionsModel->select("tb_autopay_sms_parsed.id, tb_autopay_bank_account.id as `bank_account_id`,tb_autopay_sms_parsed.datecreated")
            ->join("tb_autopay_bank_account","tb_autopay_bank_account.id=tb_autopay_sms_parsed.bank_account_id")
            ->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id'], 'tb_autopay_sms_parsed.id' => $transaction_id,'tb_autopay_sms_parsed.parser_status' => 'Success', 'tb_autopay_sms_parsed.deleted_at' => NULL])->get()->getRow();
        
        if(!is_object($transaction_details))
            show_404();

        if(!has_bank_permission($transaction_details->bank_account_id)) {
            echo "Bạn chưa có quyền truy cập tính năng này";
            return FALSE;
        }
        
        $results = $webhooksLog->select("tb_autopay_webhooks_log.id, tb_autopay_webhooks_log.type,tb_autopay_webhooks_log.request_method, tb_autopay_webhooks_log.request_url,tb_autopay_webhooks_log.connect_success,tb_autopay_webhooks_log.request_header,tb_autopay_webhooks_log.request_body,tb_autopay_webhooks_log.response_status_code,tb_autopay_webhooks_log.response_header,tb_autopay_webhooks_log.response_body,tb_autopay_webhooks_log.created_at,tb_autopay_webhooks_log.webhook_type, tb_autopay_webhooks_log.webhook_id")->where(['tb_autopay_webhooks_log.sms_parsed_id' => $transaction_id,'tb_autopay_webhooks_log.company_id' => $this->user_session['company_id'],'tb_autopay_webhooks_log.deleted_at' => NULL])->orderBy("tb_autopay_webhooks_log.id",'ASC')->get()->getResult();
        
        $html = "<div><ul><li>ID giao dịch: <a href='".base_url('transactions/details/'. $transaction_id)."'>" .$transaction_id. "</a></li><li>Lúc: ".$transaction_details->datecreated."</li></ul></div><div class='table-responsive'><table class='table'><thead><th>Lần bắn</th><th>Thuộc WebHooks</th><th>Phương thức</th><th>Kết nối mạng</th><th>HTTP Status Code</th><th>Lúc</th></thead><tbody>";
        $i = 1;
        if(count($results) == 0) {
            $html = $html . "<tr><td colspan=6 class='text-center'>Không có dữ liệu</td></tr>";
        }
        foreach($results as $result) {
            if($result->connect_success == 1)
                $net_connect_text = "<span class='text-success'>Thành công</span>";
            else
                $net_connect_text = "<span class='text-danger'>Thất bại</span>";

            if(in_array($result->response_status_code,[200,201]))
                $response_status_text = "<span class='text-success'>". esc($result->response_status_code) . "</span>";
            else
                $response_status_text = "<span class='text-danger'>" . esc($result->response_status_code) . "</span>";

            if($result->webhook_type == "Webhooks" && $result->type == "First")
                $response_status_text = $response_status_text . " <a class='btn btn-sm btn-outline-primary' onclick='manual_send_webhook(". esc($result->id) .",".esc($transaction_id).")'><i class='bi bi-repeat'></i> Gọi lại</a>";
            
            //$stt_text = $i;
            //if($result->type == "Retry")
            //    $stt_text = $i . " <span class='badge bg-primary'>Gọi lại</span>";
             


            $html = $html . "<tr>";
            $html = $html . "<td>".$i. "</td><td>#".esc($result->webhook_id . ' '. $result->webhook_type)."<br> <a data-bs-toggle='collapse' href='#collapwebhooks_".$result->id."' role='button' aria-expanded='false' aria-controls='collapwebhooks_".$result->id."'>
            Chi tiết <i class='bi bi-chevron-right'></i>
          </a><div class='collapse' id='collapwebhooks_".$result->id."' style='max-width:800px'>
          <div class='card card-body'><ul><li>Gọi đến: ".esc($result->request_url)."</li><li>Nội dung gửi: <code>".esc($result->request_body)."</code></li><li>Kết quả trả về: <code>
           ". esc($result->response_body)."</code></li></ul>
          </div>
        </div></td><td>" . $result->request_method . "</td><td>" . $net_connect_text . "</td><td>" . $response_status_text . "</td><td>" . $result->created_at . "<br><em class='text-muted small'>". timespan($result->created_at,2) ."</em></td>";
            $html = $html . "</tr>";
        $i++;
        }
        $html = $html . "</tbody></table></div> <div class='mt-5 text-center'><a href='".base_url('webhookslog')."'>Xem tất cả <i class='bi bi-chevron-right'></i></a></div>";
        echo $html;
    }

    public function manual_send_webhook() {
        if(!has_permission('WebhooksLog', 'can_view_all'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền sử dụng tính năng này"));

        if ($this->request->getMethod(true) != 'POST')
        return '';

        $id = trim(xss_clean($this->request->getPost('id')));


        if(!is_numeric($id))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy dữ liệu."));

        
        $webhooksLogModel = model(WebhooksLogModel::class);
        $transactionsModel = model(TransactionsModel::class);
        $webhooksModel = model(WebhooksModel::class);
        $userLogModel = model(UserLogModel::class);

        $log_details = $webhooksLogModel->select("webhook_id,sms_parsed_id,webhook_type,type")->where(['company_id' => $this->user_session['company_id'], 'id' => $id])->get()->getRow();

        if(!is_object($log_details))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy dữ liệu."));

        //if($log_details->webhook_type != "Webhooks")
         //   return $this->response->setJSON(array("status"=>FALSE,"message"=>"Loại webhook không được hỗ trợ"));
        
        $webhook_details = $webhooksModel->where(['company_id' => $this->user_session['company_id'], 'id' => $log_details->webhook_id])->get()->getRow();

        if(!is_object($webhook_details))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy webhooks này."));

        $transaction_details = $transactionsModel->find($log_details->sms_parsed_id);

        if(!is_object($transaction_details))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy giao dịch này."));

        $fast_check = $userLogModel->where(['company_id' => $this->user_session['company_id'], 'data_type' => 'webhook_manual_send','created_at>=' => date("Y-m-d H:i:s",strtotime("2 seconds ago"))])->orderBy('created_at','DESC')->get()->getRow();

        if(is_object($fast_check))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn thao tác quá nhanh. Vui lòng thử lại sau vài giây"));


           
        $result = $webhooksModel->doWebhook($webhook_details, $transaction_details, TRUE);
        
        add_user_log(array('data_id'=>$id,'company_id' => $this->user_session['company_id'], 'data_type'=>'webhook_manual_send','description'=>'Gọi lại webhooks ID ' . $log_details->webhook_id. '. Transaction ID ' . $log_details->sms_parsed_id . '. Log ID ' . $id,'user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));

        if(isset($result['success'])) {
            $response_text = "Tác vụ hoàn tất.";
            $status = TRUE;
            if(isset($result['response_status_code']) && $result['response_status_code'])
                $response_text = $response_text . " Mã HTTP trả về: " . esc($result['response_status_code']);
        } else {
            $response_text = "Tác vụ không thể thực hiện. Vui lòng liên hệ SePay để được hỗ trợ.";
            $status = FALSE;
        }

        return $this->response->setJSON(array("status"=>$status,"message"=> $response_text));


        
        
    }
 

    public function ajax_add_trans_demo2() {

        // only demo company can access this feature
        if( !in_array( base_url(), ['https://my.dev.sepay.vn','https://my.staging.sepay.vn'])) {
            show_404();
        }

        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!has_permission('Transactions', 'can_view_all'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền sử dụng tính năng này"));
 
        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'amount' => ['label' => 'Số tiền', 'rules' => 'integer|is_natural|required|greater_than[0],less_than[*********]'],
            'bank_account_id' => ['label' => 'Tài khoản ngân hàng', 'rules' => "required|integer"],
            'transaction_content' => ['label' => 'Nội dung', 'rules' => "max_length[100]|alpha_numeric_space|required"],
        ], [
            'transaction_content' => [
                'alpha_numeric_space' => 'Chỉ được điền chữ không dấu, số và khoản trắng'
            ]
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }  
 

        $model = model(TransactionsModel::class);

        $bankAccountModel = model(BankAccountModel::class);

        $bank_account_id = trim(xss_clean($this->request->getPost('bank_account_id')));
        $amount_in = trim(xss_clean($this->request->getPost('amount')));
        $transaction_content = trim(xss_clean($this->request->getPost('transaction_content')));
        
        $bank_account_details = $bankAccountModel->where(['company_id' => $this->company_details->id, 'id' => $bank_account_id])->get()->getRow();

        if(!is_object($bank_account_details))
            return $this->response->setJSON(array("status"=>false, 'message' => 'Không tìm thấy tài khoản ngân hàng mà bạn chọn.'));

        $last_transaction  = $model->where([
            'account_number' => $bank_account_details->account_number,
            'bank_account_id' => $bank_account_details->id,
        ])->orderBy('id','desc')->get()->getRow();

        if(is_object($last_transaction)) {
            $time_diff = strtotime("now") -  strtotime($last_transaction->datecreated);

            if($time_diff < 10)
                return $this->response->setJSON(array("status"=>false, 'message' => 'Bạn thao tác quá nhanh, vui lòng chờ 1 phút trước khi thêm giao dịch mới'));
        }
      

        $result = $model->addTransactionDemo2($bank_account_id, $amount_in, $transaction_content);
        
        if($result) {
            return $this->response->setJSON(array("status"=>true));
        } else {
            return $this->response->setJSON(array("status"=>false,"message"=>"Có lỗi xảy ra khi tạo giao dịch, vui lòng liên hệ SePay để được hỗ trợ."));
        }
            
    }

    public function index()
    { 
        $data = [
            'page_title' => 'Giao dịch',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        if(!has_permission('Transactions', 'can_view_all'))
            show_404();
            
        if ($this->shop_billing) {
            $storeFeature = new StoreFeature();
            $storeFeature->withCompanyContext($this->user_session['company_id']);
            
            if (in_array($this->company_details->role, ['SuperAdmin', 'Admin'])) {
                $data['stores'] = $storeFeature->getStores();
            } else {
                $data['stores'] = $storeFeature->getLinkedStaffStores($this->user_details->id);
            }
            
            echo theme_view('templates/autopay/header',$data);
            echo theme_view('transaction/index', $data);
            echo theme_view('templates/autopay/footer',$data);
            return;
        }

        $accountId = $this->request->getGet('account_id');

        if (! $this->validateData([
            'account_id' => $accountId,
        ], [
            'account_id' => 'permit_empty|string|is_natural',
        ])) {
            return redirect()->to(base_url('transactions'));
        }

        $data['role'] = $this->company_details->role;

        $data['bank_account_id'] = trim(xss_clean($accountId));

        $bankAccountModel = model(BankAccountModel::class);


        if(!is_numeric($data['bank_account_id']))
            $data['bank_account_id'] = FALSE;
        else {
            $bank_account_details = $bankAccountModel->where(['id' => $data['bank_account_id'], 'company_id' => $this->user_session['company_id']])->get()->getRow();
            if(!is_object($bank_account_details))
                show_404();

            if(!has_bank_permission($data['bank_account_id']))
               show_404();
        }

        $transactionsModel = model(Transactionsmodel::class);
        $userPermissionBankModel = model(UserPermissionBankModel::class);
        $userPermissionBankSubModel = model(UserPermissionBankSubModel::class);

        $bankSubAccountModel = model(BankSubAccountModel::class);


        if(in_array($data['role'],['Admin','SuperAdmin']))
            $data['bankAccounts'] = $bankAccountModel->select("
                tb_autopay_bank_account.id,tb_autopay_bank_account.bank_id,tb_autopay_bank_account.account_holder_name,tb_autopay_bank_account.bank_sms_connected, 
                tb_autopay_bank_account.bank_api_connected,tb_autopay_bank_account.account_number,tb_autopay_bank_account.label,tb_autopay_bank.brand_name,tb_autopay_bank.short_name,tb_autopay_bank.brand_name,tb_autopay_bank.logo_path,tb_autopay_bank.icon_path")->join("tb_autopay_bank","tb_autopay_bank.id=tb_autopay_bank_account.bank_id")->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id']])->orderBy('tb_autopay_bank_account.id','asc')->get()->getResult();
        else
            $data['bankAccounts'] = $userPermissionBankModel->get_bank_accounts($this->user_details->id);

        $data['configBankSubAccount'] = get_configuration('BankSubAccount');
        $data['configPayCode'] = get_configuration('PayCode');

        if($data['configBankSubAccount'] == 'on' && $data['bank_account_id']) {
            if(in_array($data['role'],['Admin','SuperAdmin']))
                $data['bank_sub_accounts'] = $bankSubAccountModel->where(['bank_account_id' => $data['bank_account_id']])->orderBy('id','ASC')->get()->getResult();
            else
                $data['bank_sub_accounts'] = $userPermissionBankSubModel->where(['tb_autopay_user_permission_bank_sub.user_id' => $this->user_details->id, 'tb_autopay_bank_sub_account.bank_account_id' => $data['bank_account_id']])->join("tb_autopay_bank_sub_account","tb_autopay_bank_sub_account.id=tb_autopay_user_permission_bank_sub.sub_account_id")->orderBy('tb_autopay_bank_sub_account.id','ASC')->get()->getResult();
        } else {
            $data['bank_sub_accounts'] = [];
        }

         // 
         $bank_accounts = array_map(function($obj) {
            return get_object_vars($obj);
        }, $data['bankAccounts']);

        $list_id_bank_account = array_column($bank_accounts, "id");
        $data['bank_sub_accounts_custom']=[];
        if(!empty($list_id_bank_account)){

            $query = $bankSubAccountModel->select(
                "tb_autopay_bank_sub_account.id, 
                 tb_autopay_bank.brand_name,
                 tb_autopay_bank.logo_path, 
                 tb_autopay_bank.icon_path, 
                 tb_autopay_bank_account.account_holder_name, 
                 tb_autopay_bank_account.account_number, 
                 tb_autopay_bank_account.bank_sms_connected, 
                 tb_autopay_bank_account.bank_api_connected, 
                 tb_autopay_bank_sub_account.sub_account, 
                 tb_autopay_bank_sub_account.acc_type, 
                 tb_autopay_bank_sub_account.label",
                )
                ->join("tb_autopay_bank_account", "tb_autopay_bank_account.id = tb_autopay_bank_sub_account.bank_account_id")
                ->join("tb_autopay_bank", "tb_autopay_bank.id = tb_autopay_bank_account.bank_id", "left")
                ->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id']])
                ->whereIn('tb_autopay_bank_account.id', $list_id_bank_account); 
                
            if (in_array($this->company_details->role, ['Admin', 'SuperAdmin'])) {
                $data['bank_sub_accounts_custom']  = $query->get()->getResultArray();
            } else {
                $data['bank_sub_accounts_custom']  = $query
                ->join("tb_autopay_user_permission_bank_sub", "tb_autopay_user_permission_bank_sub.sub_account_id = tb_autopay_bank_sub_account.id", "left")
                ->where("tb_autopay_user_permission_bank_sub.user_id", $this->user_session['user_id'])
                ->get()
                ->getResultArray();
            }

        }
        
        echo theme_view('templates/autopay/header',$data);
        echo theme_view('transactions/v2',$data);
        echo theme_view('templates/autopay/footer',$data);

    }

    public function ajax_get_account_balance() {
        if (!has_permission('ReportsAccountBalance', 'can_view_all')) {
            echo "Bạn chưa có quyền truy cập tính năng này";
            return FALSE;
        }
    
        $bankAccountModel = model(BankAccountModel::class);

        $data['bankaccounts'] = $bankAccountModel->select("tb_autopay_bank_account.id,tb_autopay_bank_account.account_holder_name,tb_autopay_bank_account.account_number,tb_autopay_bank_account.accumulated,tb_autopay_bank_account.last_transaction,tb_autopay_bank_account.label,tb_autopay_bank.brand_name,tb_autopay_bank.short_name,tb_autopay_bank.full_name,tb_autopay_bank.logo_path,tb_autopay_bank.icon_path")->join("tb_autopay_bank","tb_autopay_bank.id=tb_autopay_bank_account.bank_id")->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id']])->orderBy('accumulated','DESC')->get()->getResult();

        $data['total_balance'] = 0;
        foreach($data['bankaccounts'] as $account) {
            $data['total_balance'] = $data['total_balance'] + $account->accumulated;
        }
        
        echo theme_view('transactions/balance',$data);


    }

    public function recents() {
        $data = [
            'page_title' => 'Giao dịch',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        if(!in_array($this->company_details->id, [54,8,248,297]))
            show_404();

        $transactionsModel = slavable_model(TransactionsModel::class, 'Transactions');
        $bankModel = slavable_model(BankModel::class, 'Transactions');

        $results = $transactionsModel->getRecentTransactions($this->user_session['company_id'],FALSE, date('Y-m-d H:i:s', strtotime("30 minutes ago")),TRUE);
        $data['recent_transactions'] = [];

        foreach($results as $result) {
            $bank_details = $bankModel->where(['id' => $result->bank_id])->get()->getRow();
            if(is_object($bank_details))
                array_push($data['recent_transactions'], ['transaction_details' => $result, 'bank_details' => $bank_details]);
        }

        echo theme_view('templates/autopay/header',$data);
        echo theme_view('transactions/recents',$data);
        echo theme_view('templates/autopay/footer',$data);

    }

    public function ajax_get_fcm_log($transactionId = '')
    {
        $transactionId = trim(xss_clean($transactionId));

        if (!is_numeric($transactionId)) show_404();

        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin']))
            return $this->respond(['status' => false, 'message' => 'Bạn không có quyền truy cập.']);

        $transactionModel = slavable_model(TransactionsModel::class, 'Transactions');
        $fcmLogModel = model(FcmLogModel::class, 'Transactions');

        $transactionDetails = $transactionModel
            ->select(['tb_autopay_sms_parsed.id'])
            ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id = tb_autopay_sms_parsed.bank_account_id')
            ->where('tb_autopay_sms_parsed.id', $transactionId)
            ->where('tb_autopay_bank_account.company_id', $this->user_session['company_id'])
            ->where('tb_autopay_sms_parsed.deleted_at', null)
            ->first();

        if (!$transactionDetails) show_404();

        $fcmLogs = $fcmLogModel
            ->select(['tb_autopay_user.email', 'tb_autopay_user.firstname', 'tb_autopay_user.lastname', 'tb_autopay_fcm_device_token.platform', 'tb_autopay_fcm_log.status', 'tb_autopay_fcm_device_token.device_name', 'tb_autopay_fcm_log.created_at'])
            ->join('tb_autopay_user', 'tb_autopay_user.id = tb_autopay_fcm_log.user_id')
            ->join('tb_autopay_fcm_device_token', 'tb_autopay_fcm_device_token.id = tb_autopay_fcm_log.device_token_id')
            ->where('transaction_id', $transactionDetails->id)->get()->getResult();

        return $this->response->setJSON(['data' => $fcmLogs]);
    }

    // tích hợp Loa
    public function ajax_get_outputdevice_log($transactionId = '')
    {
        $transactionId = trim(xss_clean($transactionId));

        if (!is_numeric($transactionId)) show_404();

        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin']))
            return $this->respond(['status' => false, 'message' => 'Bạn không có quyền truy cập.']);

        $transactionModel = slavable_model(TransactionsModel::class, 'Transactions');
        $OutputDeviceReplayMessageQueueModel = model(OutputDeviceReplayMessageQueueModel::class, 'Transactions');

        $transactionDetails = $transactionModel
            ->select(['tb_autopay_sms_parsed.id'])
            ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id = tb_autopay_sms_parsed.bank_account_id')
            ->where('tb_autopay_sms_parsed.id', $transactionId)
            ->where('tb_autopay_bank_account.company_id', $this->user_session['company_id'])
            ->where('tb_autopay_sms_parsed.deleted_at', null)
            ->first();

        if (!$transactionDetails) show_404();

        $ouputDeviceLogs = $OutputDeviceReplayMessageQueueModel
            ->select(['tb_autopay_output_device.imei', 'tb_autopay_output_device.name', 'tb_autopay_output_device_replay_message_queue.status', 'tb_autopay_output_device_replay_message_queue.created_at'])
            ->join('tb_autopay_output_device', 'tb_autopay_output_device.id = tb_autopay_output_device_replay_message_queue.output_device_id')
            ->where('transaction_id', $transactionDetails->id)->get()->getResult();

        return $this->response->setJSON(['data' => $ouputDeviceLogs]);
    }

    public function index_shop()
    { 
        //Channel Partner
        is_channel_partner();

        $data = [
            'page_title' => 'Giao dịch',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        if(!has_permission('Transactions', 'can_view_all'))
            show_404();

        $data['role'] = $this->company_details->role;

        $shopModel = model(ShopModel::class);

        if (in_array($data['role'], ['Admin', 'SuperAdmin'])) {
            $data['shops'] = $shopModel
                ->where([
                    'company_id' => $this->company_details->id,
                    'active' => 1
                ])
                ->orderBy('id', 'DESC')
                ->get()
                ->getResult();
        } else {
            $userPermissionBankSubModel = model(UserPermissionBankSubModel::class);

            $data['shops'] = $userPermissionBankSubModel
                ->select([
                    'tb_autopay_shop.id', 
                    'tb_autopay_shop.name',
                ])
                ->join('tb_autopay_bank_shop_link', 
                    'tb_autopay_bank_shop_link.bank_sub_account_id = tb_autopay_user_permission_bank_sub.sub_account_id', 
                    'inner'
                )
                ->join('tb_autopay_shop', 
                    'tb_autopay_shop.id = tb_autopay_bank_shop_link.shop_id', 
                    'inner'
                )
                ->where('tb_autopay_shop.company_id', $this->user_session['company_id'])
                ->where('tb_autopay_user_permission_bank_sub.user_id', $this->user_details->id)
                ->groupBy('tb_autopay_shop.id')
                ->get()
                ->getResult();
        }

        echo theme_view('templates/autopay/header',$data);
        echo theme_view('transactions/v2',$data);
        echo theme_view('templates/autopay/footer',$data);

    }

    public function ajax_list_stall($shopId = ''){
        //Channel Partner
        is_channel_partner();
        
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!has_permission('Transactions', 'can_view_all'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền sử dụng tính năng này"));

        $shopId = trim(xss_clean($this->request->getPost('shop_id')));

        if(!is_numeric($shopId))
            return '';

        $shopModel = model(ShopModel::class);

        $shop = $shopModel
        ->where([
            'company_id' => $this->company_details->id,
            'active' => 1,
            'id' => $shopId,
            ])
        ->first();

        if(empty($shop)){
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Cửa hàng không tồn tại"));
        }

        $stall = $shopModel->select([
            'tb_autopay_bank_sub_account.id',
            'tb_autopay_bank_sub_account.sub_account',
            'tb_autopay_bank_sub_account.label',
        ])
        ->join('tb_autopay_bank_shop_link', 'tb_autopay_bank_shop_link.shop_id = tb_autopay_shop.id')
        ->join('tb_autopay_bank_account', 'tb_autopay_bank_shop_link.bank_account_id = tb_autopay_bank_account.id')
        ->join('tb_autopay_bank_sub_account', 'tb_autopay_bank_shop_link.bank_sub_account_id = tb_autopay_bank_sub_account.id')
        ->where('tb_autopay_shop.company_id', $this->user_session['company_id'])
        ->where('tb_autopay_shop.id', $shopId)
        ->get()
        ->getResult();

        return $this->response->setJSON(['status' => true, 'data' => $stall]);
    }

    public function ajax_shop_transactions_list() {
        
        //Channel Partner
        is_channel_partner();

        if ($this->request->getMethod(true) != 'POST')
            return '';

        $shopId = trim(xss_clean($this->request->getPost('shop_id')));
        $stall = xss_clean($this->request->getPost('stall'));
        $date_range = trim(xss_clean($this->request->getPost('date_range')));
        $transaction_type = trim(xss_clean($this->request->getPost('type')));
        $amount_range = trim(xss_clean($this->request->getPost('amount')));

        $transactionsModel = slavable_model(TransactionsModel::class, 'Transactions');


        $transactions = $this->getDatatablesShop($transactionsModel, $shopId,$stall, $amount_range, $date_range, $transaction_type);
        $data = array();

        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');

        foreach ($transactions as $transaction) {
            
            $no++;
            $row = array();
            
            $row[] = $no;
            $row[] = esc($transaction->id);

            $row[] = "<span>". esc($transaction->name) . "</span>";

            
            $row[] = esc($transaction->label. ' - ' .$transaction->sub_account);

            if($transaction->amount_in > 0)
                $row[] = "<span class='text-success'>+" . number_format($transaction->amount_in) . "</span>";
            else {
                $row[] = "<span class='text-danger'>-" .number_format($transaction->amount_out)  . "</span>";
            }

            if($transaction->amount_in > 0)
                $row[] = "<span class='text-success'>Tiền vào</span>";
            else
                $row[] = "<span class='text-danger'>Tiền ra</span>";
            
            $row[] = date('H:i:s d-m-Y', strtotime($transaction->transaction_date));
            
            $transaction_content_text = character_limiter(esc($transaction->transaction_content),60);

            $row[] = $transaction_content_text;
            $row[] = "<a class='' href='" . base_url('transactions/details/' . $transaction->id) .  "'>Xem<i class='bi bi-chevron-right'></i></a>";

            if($transaction->amount_in > 0)
                $row[] = "<div><span class='text-muted fs-7'>". esc($transaction->name). "</span> - <span>".esc($transaction->label)."</span><span class='fw-bold float-end fs-5 text-success'>+ " . number_format($transaction->amount_in) . " đ</span></div><div class='mt-1'>" . esc(ellipsize($transaction_content_text ?? '',25,1,'..')).  "<span class='fs-7 float-end text-muted'>".timespan($transaction->transaction_date,1)."</span></div></a>";
            else {
                $row[] = "<div><span class='text-muted fs-7'>". esc($transaction->name). "</span> - <span>".esc($transaction->label)."</span><span class='fw-bold float-end fs-5 text-danger'>- " . number_format($transaction->amount_out) . " đ</span></div><div class='mt-1'>" . esc(ellipsize($transaction_content_text ?? '',25,1,'..')).  "<span class='fs-7 float-end text-muted'>".timespan($transaction->transaction_date,1)."</span></div></a>";
            }

            $data[] = $row;
        }
 
        $output = array(
            "draw" => $draw,
            "recordsTotal" => $this->countTransactionsFilteredShop($transactionsModel, $shopId, $stall, $amount_range, $date_range, $transaction_type),
            "recordsFiltered" => $this->countTransactionsFilteredShop($transactionsModel, $shopId, $stall, $amount_range, $date_range, $transaction_type),
            "data" => $data,
        );
        return $this->response->setJSON($output);
    
    }

    protected function getDatatablesShop(&$model, $shopId, $stall, $amount, $date_range, $transaction_type)
    {
        $request = \Config\Services::request();

        $column_order = [
            null,
            'tb_autopay_sms_parsed.id', 
            'tb_autopay_shop.name', 
            'tb_autopay_bank_sub_account.label',
            null,
            null, 
            'tb_autopay_sms_parsed.transaction_date', 
            'tb_autopay_sms_parsed.transaction_content',
            null
        ];
        
        $column_search = [
            'tb_autopay_sms_parsed.id',
            'tb_autopay_shop.name',
            'tb_autopay_bank_sub_account.label',
            'tb_autopay_sms_parsed.sub_account',
            'tb_autopay_sms_parsed.transaction_content', 
            'tb_autopay_sms_parsed.amount_in', 
            'tb_autopay_sms_parsed.amount_out', 
            'tb_autopay_sms_parsed.transaction_date'
        ];

        $builder = $model
            ->select([
                'tb_autopay_shop.name',
                'tb_autopay_sms_parsed.id',
                'tb_autopay_sms_parsed.sub_account',
                'tb_autopay_bank_sub_account.label',
                'tb_autopay_sms_parsed.amount_in',
                'tb_autopay_sms_parsed.amount_out',
                'tb_autopay_sms_parsed.currency',
                'tb_autopay_sms_parsed.transaction_date',
                'tb_autopay_bank_shop_link.shop_id',
                'tb_autopay_sms_parsed.bank_account_id',
                'tb_autopay_bank_shop_link.bank_sub_account_id',
                'tb_autopay_sms_parsed.transaction_content',
                'tb_autopay_bank_account.account_number',
                'tb_autopay_sms_parsed.chat_push_message'
            ])
            ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id = tb_autopay_sms_parsed.bank_account_id')
            ->join('tb_autopay_bank_sub_account', 'tb_autopay_bank_sub_account.sub_account = tb_autopay_sms_parsed.sub_account')
            ->join('tb_autopay_bank_shop_link', 'tb_autopay_bank_shop_link.bank_account_id = tb_autopay_sms_parsed.bank_account_id AND tb_autopay_bank_shop_link.bank_sub_account_id = tb_autopay_bank_sub_account.id',)
            ->join('tb_autopay_shop', 'tb_autopay_bank_shop_link.shop_id = tb_autopay_shop.id',)
            ->where('tb_autopay_bank_account.company_id', $this->user_session['company_id'])
            ->where('tb_autopay_sms_parsed.parser_status', 'Success');

        if ($shopId) {
            $builder->where('tb_autopay_bank_shop_link.shop_id', $shopId);
        }

        if(!empty($stall) && is_array($stall))
            $builder->whereIn('tb_autopay_sms_parsed.sub_account', $stall);

        if (!empty($transaction_type)) {
            if ($transaction_type == 'in') {
                $builder->where('tb_autopay_sms_parsed.amount_in >', 0);
            } else if ($transaction_type == 'out') {
                $builder->where('tb_autopay_sms_parsed.amount_out >', 0);
            }
        }

        if (!empty($date_range)) {
            $date_range = trim($date_range);
    
            if (strpos($date_range, ' - ') !== false) {
                $dates = explode(' - ', $date_range);
                
                if (count($dates) == 2) {
                    $start_date = DateTime::createFromFormat('d-m-Y', trim($dates[0]));
                    $end_date = DateTime::createFromFormat('d-m-Y', trim($dates[1]));
                    
                    if ($start_date && $end_date) {
                        $start_date->setTime(0, 0, 0);
                        $end_date->setTime(23, 59, 59);
                        
                        if ($start_date <= $end_date) {
                            $builder->where('tb_autopay_sms_parsed.transaction_date >=', $start_date->format('Y-m-d H:i:s'));
                            $builder->where('tb_autopay_sms_parsed.transaction_date <=', $end_date->format('Y-m-d H:i:s'));
                        }
                    }
                }
            } else {
                $single_date = DateTime::createFromFormat('d-m-Y', trim($date_range));
                
                if ($single_date) {
                    $start_date = clone $single_date;
                    $end_date = clone $single_date;
                    
                    $start_date->setTime(0, 0, 0);
                    $end_date->setTime(23, 59, 59);
                    
                    $builder->where('tb_autopay_sms_parsed.transaction_date >=', $start_date->format('Y-m-d H:i:s'));
                    $builder->where('tb_autopay_sms_parsed.transaction_date <=', $end_date->format('Y-m-d H:i:s'));
                }
            }
        }

        if (!empty($amount)) {
            $ranges = explode('-', $amount);
            $min_amount = (int)$ranges[0];
            $max_amount = isset($ranges[1]) ? (int)$ranges[1] : null;
    
            if ($max_amount) {
                $builder->groupStart()
                    ->where('(CASE WHEN tb_autopay_sms_parsed.amount_in > 0 
                        THEN tb_autopay_sms_parsed.amount_in 
                        ELSE tb_autopay_sms_parsed.amount_out END) >=', $min_amount)
                    ->where('(CASE WHEN tb_autopay_sms_parsed.amount_in > 0 
                        THEN tb_autopay_sms_parsed.amount_in 
                        ELSE tb_autopay_sms_parsed.amount_out END) <=', $max_amount)
                    ->groupEnd();
            } else {
                $builder->where('(CASE WHEN tb_autopay_sms_parsed.amount_in > 0 
                    THEN tb_autopay_sms_parsed.amount_in 
                    ELSE tb_autopay_sms_parsed.amount_out END) >=', $min_amount);
            }
        }

        if ($search_term = $request->getVar('search')['value']) {
            $search_term = str_replace(',', '', trim($search_term));
            $builder->groupStart();
            foreach ($column_search as $i => $item) {
                if ($i === 0) {
                    $builder->like($item, $search_term);
                } else {
                    $builder->orLike($item, $search_term);
                }
            }
            $builder->groupEnd();
        }

        foreach ($column_search as $key => $item) { 
            $column_search_value = $request->getPost('columns')[$key]['search']['value'] ?? null;
            if ($item && $column_search_value) {
                $builder->where($item, $column_search_value);
            }
        }

        $order_column_index = $request->getVar('order')[0]['column'] ?? null;
        if ($order_column_index !== null && isset($column_order[$order_column_index])) {

            $builder->orderBy($column_order[$order_column_index], $request->getVar('order')[0]['dir']);
        } elseif (isset($this->order)) {
            $order = $this->order;
            $builder->orderBy(key($order), $order[key($order)]);
        }

        if (($length = $request->getVar('length')) !== null && $length != -1) {
            $builder->limit($length, $request->getVar('start'));
        }

        $results = $builder->orderBy('tb_autopay_sms_parsed.updated_at', 'DESC')->get()->getResult();
        return $results;
    }

    protected function countTransactionsFilteredShop(&$model, $shopId, $stall, $amount, $date_range, $transaction_type)
    {
        $builder = $model
            ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id = tb_autopay_sms_parsed.bank_account_id')
            ->join('tb_autopay_bank_sub_account', 'tb_autopay_bank_sub_account.sub_account = tb_autopay_sms_parsed.sub_account')
            ->join('tb_autopay_bank_shop_link', 'tb_autopay_bank_shop_link.bank_account_id = tb_autopay_sms_parsed.bank_account_id AND tb_autopay_bank_shop_link.bank_sub_account_id = tb_autopay_bank_sub_account.id',)
            ->join('tb_autopay_shop', 'tb_autopay_bank_shop_link.shop_id = tb_autopay_shop.id',)
            ->where('tb_autopay_bank_account.company_id', $this->user_session['company_id'])
            ->where('tb_autopay_sms_parsed.parser_status', 'Success');

        if ($shopId) {
            $builder->where('tb_autopay_bank_shop_link.shop_id', $shopId);
        }
        if(!empty($stall) && is_array($stall))
            $builder->whereIn('tb_autopay_sms_parsed.sub_account', $stall);

        if (!empty($transaction_type)) {
            if ($transaction_type == 'in') {
                $builder->where('tb_autopay_sms_parsed.amount_in >', 0);
            } else if ($transaction_type == 'out') {
                $builder->where('tb_autopay_sms_parsed.amount_out >', 0);
            }
        }

        if (!empty($date_range)) {
            $date_range = trim($date_range);
    
            if (strpos($date_range, ' - ') !== false) {
                $dates = explode(' - ', $date_range);
                
                if (count($dates) == 2) {
                    $start_date = DateTime::createFromFormat('d-m-Y', trim($dates[0]));
                    $end_date = DateTime::createFromFormat('d-m-Y', trim($dates[1]));
                    
                    if ($start_date && $end_date) {
                        $start_date->setTime(0, 0, 0);
                        $end_date->setTime(23, 59, 59);
                        
                        if ($start_date <= $end_date) {
                            $builder->where('tb_autopay_sms_parsed.transaction_date >=', $start_date->format('Y-m-d H:i:s'));
                            $builder->where('tb_autopay_sms_parsed.transaction_date <=', $end_date->format('Y-m-d H:i:s'));
                        }
                    }
                }
            } else {
                $single_date = DateTime::createFromFormat('d-m-Y', trim($date_range));
                
                if ($single_date) {
                    $start_date = clone $single_date;
                    $end_date = clone $single_date;
                    
                    $start_date->setTime(0, 0, 0);
                    $end_date->setTime(23, 59, 59);
                    
                    $builder->where('tb_autopay_sms_parsed.transaction_date >=', $start_date->format('Y-m-d H:i:s'));
                    $builder->where('tb_autopay_sms_parsed.transaction_date <=', $end_date->format('Y-m-d H:i:s'));
                }
            }
        }

        if (!empty($amount)) {
            $ranges = explode('-', $amount);
            $min_amount = (int)$ranges[0];
            $max_amount = isset($ranges[1]) ? (int)$ranges[1] : null;
    
            if ($max_amount) {
                $builder->groupStart()
                    ->where('(CASE WHEN tb_autopay_sms_parsed.amount_in > 0 
                        THEN tb_autopay_sms_parsed.amount_in 
                        ELSE tb_autopay_sms_parsed.amount_out END) >=', $min_amount)
                    ->where('(CASE WHEN tb_autopay_sms_parsed.amount_in > 0 
                        THEN tb_autopay_sms_parsed.amount_in 
                        ELSE tb_autopay_sms_parsed.amount_out END) <=', $max_amount)
                    ->groupEnd();
            } else {
                $builder->where('(CASE WHEN tb_autopay_sms_parsed.amount_in > 0 
                    THEN tb_autopay_sms_parsed.amount_in 
                    ELSE tb_autopay_sms_parsed.amount_out END) >=', $min_amount);
            }
        }
        
        return $builder->countAllResults();
    }
}