<?php

namespace App\Controllers;

use Exception;
use App\Models\SimModel;
use CodeIgniter\Controller;
use App\Libraries\MbbClient;
use App\Models\ProductModel;
use App\Models\BankAccountModel;
use App\Models\TransactionsModel;
use App\Models\ConfigurationModel;
use CodeIgniter\API\ResponseTrait;
use App\Models\BankSubAccountModel;
use App\Models\CompanySubscriptionModel;
use App\Models\MbbEnterpriseAccountModel;
use App\Exceptions\DisableBankClientException;
use App\Models\NotificationTelegramQueueModel;
use App\Actions\BankAccount\DeleteCompanyBankAccountAction;
use App\Libraries\MbbMmsClient;
use App\Models\BankSubAccountMetadataModel;
use App\Models\MbbMmsMerchantModel;
use App\Models\MbbMmsTerminalModel;
use App\Models\OutputDeviceDecalModel;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use Psr\Log\LoggerInterface;

class Mbb extends BaseController
{
    use ResponseTrait;

    protected $mbbConfig;
    protected $mbbMmsConfig;


    public function __construct()
    {
        $this->mbbConfig = config(\App\Config\Mbb::class);
        $this->mbbMmsConfig = config(\App\Config\MbbMms::class);
    }

    public function initController(RequestInterface $request, ResponseInterface $response, LoggerInterface $logger)
    {
        parent::initController($request, $response, $logger);

        is_bank_supported_by_promotion(8, $this->company_details->company_id);
    }

    protected function handleMbbClientException($e, $client = null)
    {
        log_message('error', 'MBB Client Error (Controller): ' . $e->getMessage() . ' - ' . $e->getTraceAsString());

        if ($client) $client->logRequest();

        if (strpos($e->getMessage(), 'Operation timed out') !== false)
            return $this->response->setJSON(['status' => false, 'message' => 'Hệ thống ngân hàng MB đang bận, vui lòng thử lại sau.']);

        if (strpos($e->getMessage(), '40509 - Fail to subscribe to transaction notification') !== false) {
            return $this->response->setJSON(['status' => false, 'message' => 'Tài khoản của quý khách đang liên kết ở nền tảng khác, vui lòng liên hệ SePay để được hỗ trợ.']);
        }

        if ($e instanceof DisableBankClientException)
            return $this->response->setJSON(['status' => false, 'message' => 'Hệ thống ngân hàng MB đang bảo trì, vui lòng liên hệ SePay để biết thêm thông tin chi tiết.']);    

        return $this->response->setJSON(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng thử lại sau hoặc liên hệ SePay để được hỗ trợ.']);
    }

    protected function getSwitchableApiConnectionBankAccount($id)
    {
        return model(BankAccountmodel::class)->where([
            'id' => $id, 
            'company_id' => $this->user_session['company_id'],
            'bank_id' => 8,
            'bank_sms' => 1,
            'bank_api' => 0,
            'bank_api_connected' => 0,
            'active' => 1,
        ])->get()->getRow();
    }

    protected function getSwitchableSmsConnectionBankAccount($id)
    {
        return model(BankAccountmodel::class)->where([
            'id' => $id, 
            'company_id' => $this->user_session['company_id'],
            'bank_id' => 8,
            'bank_sms' => 0,
            'bank_api' => 1,
            'active' => 1,
        ])->get()->getRow();
    }

    protected function determineIfSwitchToSmsConnection()
    {
        $subscriptionDetails = model(CompanySubscriptionModel::class)->where(['company_id' => $this->user_session['company_id']])->get()->getRow();
        $productDetails = model(ProductModel::class)->where(['id' => $subscriptionDetails->plan_id])->get()->getRow();

        return is_object($subscriptionDetails) && $productDetails->sms_allow == 1;        
    }

    public function step1()
    { 
        $data = [
            'page_title' => 'Ngân hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'allowed_switch_api_connection' => $this->mbbConfig->allowedSwitchApiConnection ?? false,
            'allowed_enterprise_connection_via_otp' => $this->mbbConfig->allowedEnterpriseConnectionViaOtp ?? false,
            'is_enterprise' => $this->request->getGet('group') === 'enterprise',
        ];

        if (!$data['allowed_enterprise_connection_via_otp'] && $data['is_enterprise']) {
            return redirect()->to(base_url('mbb/step1'));
        }

        if (!has_permission('BankAccount', 'can_add')) 
            return show_404();

        $config = config(\App\Config\Mbb::class);
        $session = session();
        $bank_account_id = $session->get('mbb_request_switch_api_connection');
        $data['bank_account_details'] = null;

        if ($config->allowedSwitchApiConnection && $bank_account_id) {
            $bank_account_details = $this->getSwitchableApiConnectionBankAccount($bank_account_id);
            
            if (is_object($bank_account_details)) {
                $data['bank_account_details'] = $bank_account_details;
            }
        }

        $session->remove('mbb_request_switch_api_connection');
        
        echo view('templates/autopay/header', $data);
        echo view('mbb/step1', $data);
        echo view('templates/autopay/footer', $data);
    }

    public function ajax_get_account_name()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!has_permission('BankAccount', 'can_add'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền thêm tài khoản ngân hàng"));
    
        if (! $this->validate([
            'account_number' => [
                'label' => 'số tài khoản',
                'rules' => 'required|min_length[1]|max_length[20]'
            ],
        ])) return $this->fail($this->validator->getErrors());

        try {
            $client = new MbbClient;
        } catch (\Exception $e) {
            return $this->handleMbbClientException($e);
        }

        try {
            $response = $client->getBankAccountInfo(trim($this->request->getVar('account_number')), 'ACCOUNT', 'INHOUSE');
            $responseData = json_decode($response->getBody(), true);

            $errorCode = $responseData['errorCode'] ?? $responseData['soaErrorCode'];
            $errorDesc = $responseData['errorDesc'] ?? $responseData['soaErrorDesc'];

            if ($errorCode == '000') {
                $session = session();
                $accountHolderName = $responseData['data']['accountName'];
                $session->set('mbb_step_1_account_holder_name', $accountHolderName);

                return $this->response->setJSON(['account_holder_name' => $accountHolderName]);
            }

            if (in_array($errorCode, ['3014', '3001', '200'])) {
                return $this->fail(['account_number' => 'Số tài khoản không tồn tại trên hệ thống MBBank.']);
            }

            throw new Exception($errorCode . ' - ' . implode(', ', $errorDesc));
        } catch (\Exception $e) {
            return $this->handleMbbClientException($e, $client);
        }
    }

    public function ajax_step_1()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!has_permission('BankAccount', 'can_add'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền thêm tài khoản ngân hàng']);

        if (! $this->validate([
            'account_number' => [
                'label' => 'số tài khoản',
                'rules' => 'required',
            ],
            'identification_number' => 'required|max_length[100]',
            'phone_number' => 'required|min_length[10]|max_length[20]',
            'label' => 'permit_empty|max_length[100]',
        ])) return $this->fail($this->validator->getErrors());

        $data = [
            'id' => trim(xss_clean($this->request->getVar('id'))),
            'account_number' => trim($this->request->getVar('account_number')),
            'identification_number' => trim($this->request->getVar('identification_number')),
            'phone_number' => trim($this->request->getVar('phone_number')),
            'label' => trim(xss_clean($this->request->getVar('label'))),
            'is_enterprise' => (bool) $this->request->getVar('is_enterprise')
        ];

        $session = session();
        $accountHolderName = $session->get('mbb_step_1_account_holder_name');

        if (!$accountHolderName)
            return $this->response->setJSON(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng tải lại trang.']);

        $bankAccountModel = model(BankAccountModel::class);
        $bankAccountDetails = $data['id'] ? $this->getSwitchableApiConnectionBankAccount($data['id']) : null;

        // Skip validation account number exist when switch bank account to api conenction
        if ($this->mbbConfig->allowedSwitchApiConnection && $bankAccountDetails) {
            $data['account_number'] = $bankAccountDetails->account_number;
        } else {
            if ($bankAccountDetails = $bankAccountModel->where(['account_number' => $data['account_number'], 'bank_id' => 8, 'company_id' => $this->user_session['company_id'], 'bank_sms' => 1])->get()->getRow()) {
                return $this->fail(['account_number' => 'Tài khoản này của bạn đang sử dụng phương thức kết nối SMS Banking', 'bank_account_id' => $bankAccountDetails->id]);
            }

            if ($bankAccountModel->where(['account_number' => $data['account_number'], 'bank_id' => 8])->countAllResults()) {
                return $this->fail(['account_number' => 'Số tài khoản này đã tồn tại trên hệ thống', 'owner' => false]);
            }
        }

        try {
            $client = new MbbClient;
        } catch (Exception $e) {
            return $this->handleMbbClientException($e);
        }

        try {
            $response = $client->requestPushTransactionMessageSubscribe(
                $data['identification_number'],
                $data['account_number'],
                $accountHolderName,
                $data['phone_number'],
                'SMS',
                'DC',
                'WEB_APP'
            );
            $responseData = json_decode($response->getBody(), true);

            $errorCode = $responseData['errorCode'] ?? $responseData['soaErrorCode'];
            $errorDesc = $responseData['errorDesc'] ?? $responseData['soaErrorDesc'];

            // SIMULATE
            // $errorCode = '000';
            // $responseData['data']['requestId'] = uniqid();
            // SIMULATE

            if (in_array($errorCode, ['000', '40504']) && !$bankAccountDetails) {
                $simModel = model(SimModel::class);
                $simCompanyModel = model(SimCompanyModel::class);

                $bankAccountData = array(
                    'company_id' => $this->user_session['company_id'],
                    'account_holder_name' => $accountHolderName,
                    'account_number' => $data['account_number'],
                    'bank_id' => 8,
                    'label' => $data['label'],
                    'active' => 1,
                    'bank_api' => 1,
                    'bank_api_connected' => 0,
                    'identification_number' => $data['identification_number'],
                    'phone_number' => $data['phone_number']
                );

                $sims = $simCompanyModel
                    ->select('tb_autopay_sim.id, tb_autopay_sim.sim_phonenumber')
                    ->join('tb_autopay_sim', 'tb_autopay_sim.id=tb_autopay_sim_company.sim_id')
                    ->where([
                        'tb_autopay_sim_company.company_id' => $this->user_session['company_id'], 
                        'tb_autopay_sim.active' => 1
                    ])
                    ->orderBy('tb_autopay_sim_company.created_at', 'ASC')
                    ->get()->getResult();

                if (count($sims) > 0) {
                    $bankAccountData['sim_id'] = $sims[0]->id;
                }

                $bankAccountId = $bankAccountModel->insert($bankAccountData);

                if ($bankAccountId) {
                    if ($data['is_enterprise'] && $this->mbbConfig->allowedEnterpriseConnectionViaOtp) {
                        $mbbEnterpriseAccountModel = model(MbbEnterpriseAccountModel::class);
                        $mbbEnterpriseAccountModel->insert(['bank_account_id' => $bankAccountId]);
                    }

                    add_user_log(['data_id' => $bankAccountId, 'company_id' => $this->user_session['company_id'], 'data_type' => 'bank_account_add', 'description' => 'Thêm tài khoản ngân hàng MB API', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);

                    // Skip verify OTP when MB bank account was linked API connection yet.
                    if ($errorCode == '40504') {
                        $session->set('mbb_skip_step_2', true);

                        return $this->response->setJSON(['status' => true, 'id' => $bankAccountId]);
                    }

                    $session->remove('mbb_step_1_account_holder_name');
                    $session->set('mbb_step_1_request_id', $responseData['data']['requestId']);
                    
                    set_alert('success', 'Đã gửi OTP đến số điện thoại.');

                    return $this->response->setJSON(['status' => true, 'id' => $bankAccountId]);
                }

                add_user_log(['data_id' => 0, 'company_id' => $this->user_session['company_id'], 'data_type' => 'bank_account_add', 'description' => 'Thêm tài khoản ngân hàng MB API', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Failed']);
                return $this->response->setJSON(['status' => false, 'message' => 'Không thể thêm tài khoản ngân hàng. Vui lòng liên hệ SePay để được hỗ trợ.']);
            }

            // Switch MB bank account to API connection
            if (in_array($errorCode, ['000', '40504']) && $bankAccountDetails) {
                $bankAccountUpdated = $bankAccountModel->set([
                    'account_holder_name' => $accountHolderName,
                    'identification_number' => $data['identification_number'],
                    'phone_number' => $data['phone_number'],
                ])->update($bankAccountDetails->id);

                if ($bankAccountUpdated) {
                    add_user_log(['data_id' => $bankAccountDetails->id, 'company_id' => $this->user_session['company_id'], 'data_type' => 'bank_account_add', 'description' => 'Chuyển phương thức kết nối sang API', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);

                    // Skip verify OTP when MB bank account was linked API connection yet.
                    if ($errorCode == '40504') {
                        $session->set('mbb_skip_step_2', true);

                        return $this->response->setJSON(['status' => true, 'id' => $bankAccountDetails->id]);
                    }

                    $session->remove('mbb_step_1_account_holder_name');
                    $session->set('mbb_step_1_request_id', $responseData['data']['requestId']);
                    
                    set_alert('success', 'Đã gửi OTP đến số điện thoại.');

                    return $this->response->setJSON(['status' => true, 'id' => $bankAccountDetails->id]);
                }

                add_user_log(['data_id' => 0, 'company_id' => $this->user_session['company_id'], 'data_type' => 'bank_account_add', 'description' => 'Chuyển phương thức kết nối sang API', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Failed']);
                return $this->response->setJSON(['status' => false, 'message' => 'Không thể thêm tài khoản ngân hàng. Vui lòng liên hệ SePay để được hỗ trợ.']);
            }

            if ($errorCode == '410')
                return $this->fail([
                    'identification_number' => 'Số CCCD/CMND/Mã số thuế không được đăng ký cho tài khoản trên.',
                    'phone_number' => 'Số điện thoại không được đăng ký cho tài khoản trên, vui lòng liên hệ SePay để được hỗ trợ.'
                ]);

            if ($errorCode == '293')
                return $this->fail(['identification_number' =>  'Số CCCD/CMND/Mã số thuế không được đăng ký cho tài khoản trên.']);

            if ($errorCode == '1020')
                return $this->fail(['account_number' => 'Số tài khoản không tồn tại trên hệ thống ngân hàng MB.']);

            // account holder name is invalid
            if ($errorCode == '40600')
                return $this->response->setJSON(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng tải lại trang.']);

            if ($errorCode == '219')
                return $this->fail(['phone_number' => 'Số điện thoại không được đăng ký cho tài khoản trên, vui lòng liên hệ SePay để được hỗ trợ.']);

            throw new Exception($errorCode . ' - ' . $errorDesc);
        } catch (Exception $e) {
            return $this->handleMbbClientException($e, $client);
        }
    }

    public function step2($id = '')
    { 
        $data = [
            'page_title' => 'Ngân hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'allowed_enterprise_connection_via_otp' => $this->mbbConfig->allowedEnterpriseConnectionViaOtp ?? false,
        ];

        if (!has_permission('BankAccount', 'can_add'))
            show_404();

        if (!is_numeric($id))
            show_404();

        $bankAccountModel = model(BankAccountModel::class);
        $bankAccountDetails = $bankAccountModel
            ->select('tb_autopay_bank_account.id, tb_autopay_bank_account.account_number, tb_autopay_bank_account.label, tb_autopay_bank_account.active, tb_autopay_bank_account.created_at, tb_autopay_bank.full_name, tb_autopay_bank.brand_name, tb_autopay_bank_account.account_number, tb_autopay_bank_account.account_holder_name, tb_autopay_bank.icon_path, tb_autopay_bank.logo_path, tb_autopay_sim.sim_phonenumber, tb_autopay_bank_account.last_transaction, tb_autopay_bank_account.accumulated,tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.bank_id,tb_autopay_bank_account.bank_sms, tb_autopay_bank_account.bank_sms_connected, tb_autopay_bank_account.bank_api, tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.identification_number, tb_autopay_bank_account.phone_number, tb_autopay_bank_account.sim_id')
            ->join('tb_autopay_bank', 'tb_autopay_bank.id=tb_autopay_bank_account.bank_id')
            ->join('tb_autopay_sim', 'tb_autopay_sim.id=tb_autopay_bank_account.sim_id', 'left')
            ->where([
                'tb_autopay_bank_account.company_id' => $this->user_session['company_id'], 
                'tb_autopay_bank_account.id' => $id,
                'tb_autopay_bank_account.bank_id' => 8
            ])->get()->getRow();

        if (!is_object($bankAccountDetails))
            show_404();

        $mbbEnterpriseAccountModel = model(MbbEnterpriseAccountModel::class);
        $isEnterpriseAccount = $mbbEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->countAllResults();

        if ($isEnterpriseAccount && !$this->mbbConfig->allowedEnterpriseConnectionViaOtp) {
            return redirect()->to('/mbb/details/' . $bankAccountDetails->id);
        }

        $data['is_enterprise'] = $isEnterpriseAccount;
        $data['bank_account_details'] = $bankAccountDetails;

        $session = session();
        $data['request_id'] = $session->get('mbb_step_1_request_id');
        
        if ($session->get('mbb_skip_step_2')) {
            $bankAccountModel->set([
                'bank_api_connected' => 1, 
                'bank_api' => 1,
                'bank_sms' => 0,
                'bank_sms_connected' => 0,
            ])->update($bankAccountDetails->id);
            
            set_alert('success', 'Tài khoản đã liên kết ngân hàng trước đó!');
            $session->remove('mbb_skip_step_2');

            return redirect()->to('mbb/settings/' . $id);
        }
        
        $transactionsModel = slavable_model(TransactionsModel::class, 'Mbb');
        $data['count_transactions'] = $transactionsModel->where([
            'id' => $bankAccountDetails->id, 
            'deleted_at' => null
        ])->countAllResults();

        if ($bankAccountDetails->bank_api_connected == 1) {
            set_alert('error', 'Tài khoản này đã mở API rồi. Bạn không cần phải liên kết lại.');
            return redirect()->to('mbb/settings/' . $id);
        }

        if ($bankAccountDetails->bank_sms == 1 && !$data['request_id']) {
            set_alert('error', 'Tài khoản này đang kết nối bằng phương thức SMS Banking.');
            return redirect()->to('bankaccount/details/' . $id);
        }
        
        $session->remove('mbb_step_1_request_id');

        echo view('templates/autopay/header', $data);
        echo view('mbb/step2', $data);
        echo view('templates/autopay/footer', $data);
    }

    public function ajax_step_2()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        if (!has_permission('BankAccount', 'can_add'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn chưa được cấp quyền để thực hiện thao tác này']);

        $bankAccountModel = model(BankAccountmodel::class);
        $bankAccountId = trim(xss_clean($this->request->getPost('id')));
        $session = session();

        $bankAccountDetails = $bankAccountModel->where([
            'id' => $bankAccountId, 
            'company_id' => $this->user_session['company_id'],
            'bank_id' => 8
        ])->get()->getRow();
        
        if (!is_object($bankAccountDetails))
            return $this->response->setJSON(['status' => false, 'message' => 'Không tìm thấy tài khoản ngân hàng này']);

        $mbbEnterpriseAccountModel = model(MbbEnterpriseAccountModel::class);
        $isEnterpriseAccount = $mbbEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->countAllResults();

        if ($isEnterpriseAccount && !$this->mbbConfig->allowedEnterpriseConnectionViaOtp) {
            return $this->response->setJSON(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng tải lại trang']);
        }

        if (! $this->validate([
            'otp' => 'required',
            'request_id' => 'required'
        ])) return $this->fail($this->validator->getErrors());
        
        $data = [
            'otp' => trim($this->request->getVar('otp')),
            'request_id' => trim($this->request->getVar('request_id')),
        ];

        try {
            $client = new MbbClient;
        } catch (Exception $e) {
            return $this->handleMbbClientException($e);
        }

        try {
            $response = $client->confirmPushTransactionMessageSubscribe(
                $data['request_id'],
                $data['otp'],
                'SMS',
                'WEB_APP'
            );
            $responseData = json_decode($response->getBody(), true);
            
            $errorCode = $responseData['errorCode'] ?? $responseData['soaErrorCode'];
            $errorDesc = $responseData['errorDesc'] ?? $responseData['soaErrorDesc'];

            // SIMUATE
            // $errorCode = '000';
            // SIMULATE

            if ($errorCode == '000') {
                $isSwitchToApiConnection = $bankAccountDetails->bank_sms == 0;

                $bankAccountModel->set([
                    'bank_api_connected' => 1, 
                    'bank_api' => 1,
                    'bank_sms' => 0,
                    'bank_sms_connected' => 0,
                ])->update($bankAccountId);

                if ($isSwitchToApiConnection) {
                    add_user_log(['data_id' => $bankAccountId, 'company_id' => $this->user_session['company_id'], 'data_type' => 'bank_account_linked', 'description' => 'Chuyển đổi phương thức kết nối sang API Banking', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent'=> $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);
                } else {
                    add_user_log(['data_id' => $bankAccountId, 'company_id' => $this->user_session['company_id'], 'data_type'=> 'bank_account_linked', 'description' => 'Liên kết tài khoản ngân hàng', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);
                }
                
                $bankSubAccountConfig = get_configuration('BankSubAccount');
                
                if ($bankSubAccountConfig == 'off') {
                    $configurationModel = model(ConfigurationModel::class);
                    $configurationModel->set([
                        'value' => 'on',
                    ])->where([
                        'company_id' => $this->user_session['company_id'],
                        'setting' => 'BankSubAccount'
                    ])->update();
                }

                $this->syncTerminal($bankAccountDetails);

                set_alert('success', 'Liên kết ngân hàng thành công!');

                return $this->response->setJSON(['status' => true]);
            }

            if (in_array($errorCode, ['40507', '237']))
                return $this->fail(['otp' => 'OTP không chính xác hoặc đã hết hiệu lực.']);

            throw new Exception($errorCode . ' - ' . $errorDesc);
        } catch (Exception $e) {
            return $this->handleMbbClientException($e, $client);
        }
    }

    public function ajax_resend_otp()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        if (!has_permission('BankAccount', 'can_add'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn chưa được cấp quyền để thực hiện thao tác này']);

        $bankAccountModel = model(BankAccountmodel::class);
        $bankAccountId = trim(xss_clean($this->request->getPost('id')));

        $bankAccountDetails = $bankAccountModel->where([
            'id' => $bankAccountId, 
            'company_id' => $this->user_session['company_id'],
            'bank_id' => 8
        ])->get()->getRow();
        
        if (!is_object($bankAccountDetails))
            return $this->response->setJSON(['status' => false, 'message' => 'Không tìm thấy tài khoản ngân hàng này']);

        $mbbEnterpriseAccountModel = model(MbbEnterpriseAccountModel::class);
        $isEnterpriseAccount = $mbbEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->countAllResults();

        if ($isEnterpriseAccount && !$this->mbbConfig->allowedEnterpriseConnectionViaOtp) {
            return $this->response->setJSON(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng tải lại trang']);
        }

        try {
            $client = new MbbClient;
        } catch (Exception $e) {
            return $this->handleMbbClientException($e);
        }

        try {
            $response = $client->requestPushTransactionMessageSubscribe(
                $bankAccountDetails->identification_number,
                $bankAccountDetails->account_number,
                $bankAccountDetails->account_holder_name,
                $bankAccountDetails->phone_number,
                'SMS',
                'DC',
                'WEB_APP'
            );
            $responseData = json_decode($response->getBody(), true);

            $errorCode = $responseData['errorCode'] ?? $responseData['soaErrorCode'];
            $errorDesc = $responseData['errorDesc'] ?? $responseData['soaErrorDesc'];

            // SIMULATE
            // $errorCode = '000';
            // $responseData['data']['requestId'] = uniqid();
            // SIMULATE

            if ($errorCode == '000') {
                return $this->response->setJSON([
                    'status' => true, 
                    'message' => "Đã gửi OTP đến số điện thoại.",
                    'request_id' => $responseData['data']['requestId']
                ]);
            }

            if ($errorCode == '40504')
                return $this->response->setJSON(['status' => false, 'message' => 'Tài khoản đã được liên kết trước đó.']);

            throw new Exception($errorCode . ' - ' . $errorDesc);
        } catch (Exception $e) {
            return $this->handleMbbClientException($e, $client);
        }
    }

    public function step3($id = '')
    { 
        $data = [
            'page_title' => 'Ngân hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'allowed_enterprise_connection_via_otp' => $this->mbbConfig->allowedEnterpriseConnectionViaOtp ?? false,
        ];

        if (!has_permission('BankAccount', 'can_add'))
            show_404();

        if (!is_numeric($id))
            show_404();

        $bankAccountModel = model(BankAccountModel::class);
        $bankAccountDetails = $bankAccountModel
            ->select('tb_autopay_bank_account.id, tb_autopay_bank_account.account_number, tb_autopay_bank_account.label, tb_autopay_bank_account.active, tb_autopay_bank_account.created_at, tb_autopay_bank.full_name, tb_autopay_bank.brand_name, tb_autopay_bank_account.account_number, tb_autopay_bank_account.account_holder_name, tb_autopay_bank.icon_path, tb_autopay_bank.logo_path, tb_autopay_sim.sim_phonenumber, tb_autopay_bank_account.last_transaction, tb_autopay_bank_account.accumulated,tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.bank_id,tb_autopay_bank_account.bank_sms, tb_autopay_bank_account.bank_sms_connected, tb_autopay_bank_account.bank_api, tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.identification_number, tb_autopay_bank_account.phone_number, tb_autopay_bank_account.sim_id')
            ->join('tb_autopay_bank', 'tb_autopay_bank.id=tb_autopay_bank_account.bank_id')
            ->join('tb_autopay_sim', 'tb_autopay_sim.id=tb_autopay_bank_account.sim_id', 'left')
            ->where([
                'tb_autopay_bank_account.company_id' => $this->user_session['company_id'], 
                'tb_autopay_bank_account.id' => $id,
                'tb_autopay_bank_account.bank_id' => 8
            ])->get()->getRow();

        if (!$bankAccountDetails)
            show_404();

        $mbbEnterpriseAccountModel = model(MbbEnterpriseAccountModel::class);
        $isEnterpriseAccount = $mbbEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->countAllResults();

        if ($isEnterpriseAccount && !$data['allowed_enterprise_connection_via_otp']) {
            return redirect()->to('/mbb/details/' . $bankAccountDetails->id);
        }

        $data['is_enterprise'] = $isEnterpriseAccount;

        $data['bank_account_details'] = $bankAccountDetails;

        echo view('templates/autopay/header', $data);
        echo view('mbb/step3', $data);
        echo view('templates/autopay/footer', $data);
    }
    
    public function settings($id)
    { 
        $data = [
            'page_title' => 'Cấu hình',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'allowed_enterprise_connection_via_otp' => $this->mbbConfig->allowedEnterpriseConnectionViaOtp ?? false,
        ];

        if (! has_permission('BankAccount', 'can_add')) {
            show_404();
        }

        if (! is_numeric($id)) {
            show_404();
        }

        $bankAccount = model(BankAccountModel::class)
            ->select('tb_autopay_bank_account.id, tb_autopay_bank_account.account_number, tb_autopay_bank_account.label, tb_autopay_bank_account.active, tb_autopay_bank_account.created_at, tb_autopay_bank.full_name, tb_autopay_bank.brand_name, tb_autopay_bank_account.account_number, tb_autopay_bank_account.account_holder_name, tb_autopay_bank.icon_path, tb_autopay_bank.logo_path, tb_autopay_sim.sim_phonenumber, tb_autopay_bank_account.last_transaction, tb_autopay_bank_account.accumulated,tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.bank_id,tb_autopay_bank_account.bank_sms, tb_autopay_bank_account.bank_sms_connected, tb_autopay_bank_account.bank_api, tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.identification_number, tb_autopay_bank_account.phone_number, tb_autopay_bank_account.sim_id')
            ->join('tb_autopay_bank', 'tb_autopay_bank.id=tb_autopay_bank_account.bank_id')
            ->join('tb_autopay_sim', 'tb_autopay_sim.id=tb_autopay_bank_account.sim_id', 'left')
            ->where('tb_autopay_bank_account.company_id', $this->user_session['company_id'])
            ->where('tb_autopay_bank_account.id', $id)
            ->where('tb_autopay_bank_account.bank_id', 8)
            ->first();

        if (! $bankAccount) {
            show_404();
        }

        $isEnterpriseAccount = model(MbbEnterpriseAccountModel::class)
            ->where('bank_account_id', $bankAccount->id)
            ->countAllResults();

        if ($isEnterpriseAccount && ! $data['allowed_enterprise_connection_via_otp']) {
            return redirect()->to('/mbb/details/' . $bankAccount->id);
        }

        $data['is_enterprise'] = $isEnterpriseAccount;
        $data['bankAccount'] = $bankAccount;
        $data['settings'] = bank_account_settings($bankAccount->id);

        echo view('templates/autopay/header', $data);
        echo view('mbb/settings', $data);
        echo view('templates/autopay/footer', $data);
    }

    public function ajax_add_va()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!has_permission('BankAccount', 'can_add'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền thêm tài khoản ngân hàng']);
 
        $validation = \Config\Services::validation();
        helper('text');

        if (!$this->validate([
            'bank_account_id' => ['label' => 'Tài khoản chính', 'rules' => 'integer|is_natural'],
            'sub_account' => ['label' => 'Số tài khoản ảo', 'rules' => 'required|min_length[2]|max_length[3]|regex_match[/^[A-Z0-9]{2,3}$/]'],
            'label' => ['label' => 'Tên gợi nhớ', 'rules' => 'max_length[100]'],
        ], ['bank_account_id' => ['integer' =>'Bạn chưa chọn tài khoản chính']])) {
            return $this->response->setJSON(['status' => false, 'message' => implode('. ', $validation->getErrors())]);
        }  

        $bankAccountId = trim(xss_clean($this->request->getVar('bank_account_id')));
        $subAccount = trim(xss_clean($this->request->getVar('sub_account')));

        $bankAccountModel = model(BankAccountModel::class);
        $bankAccountDetails = $bankAccountModel
            ->select('id, active, company_id, bank_id')
            ->where([
                'active' => 1,
                'bank_id' => 8,
                'id' => $bankAccountId, 
                'company_id' => $this->user_session['company_id'],
            ])->get()->getRow();
        
        if (!is_object($bankAccountDetails))
            show_404();

        $bankSubAccountModel = model(BankSubAccountModel::class);

        $existOwnBankSubAccount = $bankSubAccountModel
            ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id')
            ->where([
                'tb_autopay_bank_account.company_id' => $this->user_session['company_id'], 
                'tb_autopay_bank_sub_account.sub_account' => $subAccount
            ])
            ->get()->getRow();

        if (is_object($existOwnBankSubAccount))
            return $this->fail(['sub_account' => 'Tài khoản ảo này đã tồn tại. Bạn đã tạo tài khoản ảo này rồi.']);
          
        $existBankSubAccount = $bankSubAccountModel->where(['tb_autopay_bank_sub_account.sub_account' => $subAccount])->get()->getRow();

        if (is_object($existBankSubAccount))
            return $this->fail(['sub_account' => 'Tài khoản ảo này đã tồn tại trên hệ thống. Vui lòng chọn tên khác.']);
            
        if (!is_speaker_billing_subscription() && preg_match('/^L[A-Z0-9]+$/i', $subAccount)) {
            return $this->fail(['sub_account' => 'Tài khoản ảo này đã tồn tại trên hệ thống. Vui lòng chọn tên khác.']);
        }
     
        $bankSubAccountId = $bankSubAccountModel->insert([
            'sub_account' =>  $subAccount,
            'bank_account_id' => $bankAccountId,
            'label' => trim(xss_clean($this->request->getVar('label'))),
            'acc_type' => 'Virtual',
        ]);
        
        if (!$bankSubAccountId)
            return $this->response->setJSON(['status' => false, 'message' => 'Không thể thêm tài khoản ảo này. Vui lòng liên hệ SePay để được hỗ trợ.']);

        return $this->response->setJSON(['status' => true, 'id' => $bankSubAccountId]);
    }

    public function ajax_check_trans() 
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!has_permission('BankAccount', 'can_add'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền thực hiện thao tác này']);

        $bankAccountId = trim(xss_clean($this->request->getVar('bank_account_id')));

        if (!is_numeric($bankAccountId))
            show_404();

        $bankAccountModel = slavable_model(BankAccountModel::class, 'Mbb');
        $bankAccountDetails = $bankAccountModel
            ->select('tb_autopay_bank_account.id, tb_autopay_bank_account.account_number, tb_autopay_bank_account.label, tb_autopay_bank_account.active, tb_autopay_bank_account.created_at, tb_autopay_bank.brand_name, tb_autopay_bank_account.account_holder_name, tb_autopay_bank.icon_path, tb_autopay_bank.logo_path, tb_autopay_bank_account.last_transaction, tb_autopay_bank_account.accumulated, tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.bank_id')
            ->join('tb_autopay_bank', 'tb_autopay_bank.id=tb_autopay_bank_account.bank_id')
            ->where([
                'tb_autopay_bank_account.company_id' => $this->user_session['company_id'], 
                'tb_autopay_bank_account.id' => $bankAccountId,
                'tb_autopay_bank_account.bank_id' => 8
            ])
            ->get()->getRow();

        if (!is_object($bankAccountDetails))
            show_404();

        $session = session();

        if (!$session->get('checking_transaction_date'))
            $session->set('checking_transaction_date', date('Y-m-d H:i:s'));

        $checkingTransactionDate = $session->get('checking_transaction_date');
        $transactionsModel = slavable_model(TransactionsModel::class, 'Mbb');
        $lastTransaction = $transactionsModel
            ->select('id, amount_in, account_number, sub_account, transaction_content, transaction_date')
            ->where([
                'deleted_at' => null, 
                'parser_status' => 'Success', 
                'bank_account_id' => $bankAccountId, 
                'source' => 'BankAPINotify', 
                'transaction_date >= ' => $checkingTransactionDate
            ])
            ->orderBy('id', 'DESC')
            ->get()->getRow();

        if (!is_object($lastTransaction))
            return $this->response->setJSON(['status' => false]);

        $session->remove('checking_transaction_date');

        return $this->response->setJSON(['status' => true, 'last_transaction' => $lastTransaction]);
    }

    public function details($id = '')
    { 
        $data = [
            'page_title' => 'Ngân hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'allowed_delete_bank_account' => $this->mbbConfig->allowedDeleteBankAccount ?? false,
            'allowed_switch_sms_connection' => $this->mbbConfig->allowedSwitchSmsConnection ?? false,
            'allowed_switch_api_connection' => $this->mbbConfig->allowedSwitchApiConnection ?? false,
            'allowed_enterprise_connection_via_otp' => $this->mbbConfig->allowedEnterpriseConnectionViaOtp ?? false,
            'api_connection_visibility' => $this->mbbConfig->apiConnectionVisibility ?? false,
            'va_enabled' => $this->mbbMmsConfig->enabled ?? false,
        ];

        if (!has_permission('BankAccount', 'can_view_all'))
            show_404();

        if (!is_numeric($id))
            show_404();

        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $transactionsModel = slavable_model(TransactionsModel::class, 'Mbb');
        $simCompanyModel = model(SimCompanyModel::class);
        $mbbEnterpriseAccountModel = model(MbbEnterpriseAccountModel::class);
        $mbbMmsTerminalModel = model(MbbMmsTerminalModel::class);

        $bankAccountDetails = $bankAccountModel
            ->select('tb_autopay_bank_account.id, tb_autopay_bank_account.account_number, tb_autopay_bank_account.label, tb_autopay_bank_account.active, tb_autopay_bank_account.created_at, tb_autopay_bank.full_name, tb_autopay_bank.brand_name, tb_autopay_bank_account.account_number, tb_autopay_bank_account.account_holder_name, tb_autopay_bank.icon_path, tb_autopay_bank.logo_path, tb_autopay_sim.sim_phonenumber, tb_autopay_bank_account.last_transaction, tb_autopay_bank_account.accumulated,tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.bank_id,tb_autopay_bank_account.bank_sms, tb_autopay_bank_account.bank_sms_connected, tb_autopay_bank_account.bank_api, tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.identification_number, tb_autopay_bank_account.phone_number, tb_autopay_bank_account.sim_id')
            ->join('tb_autopay_bank', 'tb_autopay_bank.id=tb_autopay_bank_account.bank_id')
            ->join('tb_autopay_sim', 'tb_autopay_sim.id=tb_autopay_bank_account.sim_id', 'left')
            ->where([
                'tb_autopay_bank_account.company_id' => $this->user_session['company_id'], 
                'tb_autopay_bank_account.id' => $id,
                'tb_autopay_bank_account.bank_id' => 8
            ])->get()->getRow();
        
        if (!is_object($bankAccountDetails))
            show_404();
       
        $isEnterpriseAccount = $mbbEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->countAllResults();

        if ($bankAccountDetails->bank_api_connected == 0 && $bankAccountDetails->bank_api == 1 && !$isEnterpriseAccount)
            return redirect()->to(base_url('mbb/step2/' . $bankAccountDetails->id));

        if ($bankAccountDetails->bank_api_connected == 0 && $bankAccountDetails->bank_api == 1 && $isEnterpriseAccount && $this->mbbConfig->allowedEnterpriseConnectionViaOtp) 
            return redirect()->to(base_url('mbb/step2/' . $bankAccountDetails->id));

        $data['bank_account_details'] = $bankAccountDetails;

        $data['is_enterprise_account'] = !!$isEnterpriseAccount;
        $data['is_synced_terminal'] = !!$mbbMmsTerminalModel->where(['bank_account_id' => $bankAccountDetails->id])->countAllResults();

        $data['count_subs'] = $bankSubAccountModel->where(['bank_account_id' => $id, 'deleted_at' => null])->countAllResults();
       
        $data['bank_sms_info'] = get_bank_sms_info($data['bank_account_details']->brand_name);
       
        $data['count_transactions'] = $transactionsModel->where(['bank_account_id' => $data['bank_account_details']->id, 'deleted_at' => NULL])->countAllResults();
       
        $data['count_sms_transactions'] = $transactionsModel->where(['bank_account_id' => $data['bank_account_details']->id, 'deleted_at' => NULL, 'source' => 'SMS'])->countAllResults();
       
        $data['sims'] = $simCompanyModel
            ->select('tb_autopay_sim.id, tb_autopay_sim.sim_phonenumber')
            ->join('tb_autopay_sim', 'tb_autopay_sim.id=tb_autopay_sim_company.sim_id')
            ->where(['tb_autopay_sim_company.company_id' => $this->user_session['company_id'], 'tb_autopay_sim.active' => 1])
            ->orderBy('tb_autopay_sim_company.created_at', 'DESC')
            ->get()->getResult();

            // data QR
            $data['bank_sub_accounts_custom']=[];
            if(!empty($data['bank_account_details'])){    
                $data['bank_sub_accounts_custom'] = $bankSubAccountModel->select("tb_autopay_bank_sub_account.id,tb_autopay_bank_sub_account.acc_type, tb_autopay_bank.brand_name,tb_autopay_bank.icon_path, tb_autopay_bank_account.account_holder_name, tb_autopay_bank_account.account_number, tb_autopay_bank_sub_account.sub_account, tb_autopay_bank_sub_account.label")
                    ->join("tb_autopay_bank_account", "tb_autopay_bank_account.id = tb_autopay_bank_sub_account.bank_account_id")
                    ->join("tb_autopay_bank", "tb_autopay_bank.id = tb_autopay_bank_account.bank_id", "left")
                    ->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id']])
                    ->where('tb_autopay_bank_account.id',$data['bank_account_details']->id) 
                    ->get()
                    ->getResultArray();
            }

        echo theme_view('templates/autopay/header', $data);
        echo theme_view($data['bank_account_details']->bank_sms ? 'mbb/sms/details' : 'mbb/details', $data);
        echo theme_view('templates/autopay/footer', $data);
    }

    public function ajax_va_list()
    {
        if (!has_permission('BankAccount', 'can_view_all'))
            show_404();
     
        if ($this->request->getMethod(true) != 'POST')
            return '';

        $bankAccountId = $this->request->getGet('bank_account_id');

        if (!is_numeric($bankAccountId))
            $bankAccountId = false;
 
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $bankSubAccounts = $bankSubAccountModel->getDatatables($this->user_session['company_id'], $bankAccountId);
        $data = [];
        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');
        $canEdit = has_permission('BankAccount', 'can_edit');
        $canDelete = has_permission('BankAccount', 'can_delete');
 
        foreach ($bankSubAccounts as $bankSubAccount) {
            $no++;
            $row = array();
            $actionsBtnHtml = '';

            if ($canEdit && $bankSubAccount->va_active == 1)
                $actionsBtnHtml .= "<a href='javascript:;' onclick='edit_va(" . esc($bankSubAccount->id) . ")' class='btn btn-sm btn-outline-warning ms-2 me-2'><i class='bi bi-pencil'></i> Sửa</a>";

            if ($canDelete && $bankSubAccount->va_active == 1) {
                $actionsBtnHtml .= sprintf(
                    '<a href="javascript:;" onclick="delete_va(this, %d)" class="btn btn-sm btn-outline-danger ms-2"><i class="bi bi-trash3"></i> %s</a>',
                    esc($bankSubAccount->id),
                    $bankSubAccount->va_type === 'Static' ? 'Xóa' : 'Tạm ngưng'
                );
            }
                 
            $row[] = $no;
            $row[] = $bankSubAccount->id;

            if ($bankSubAccount->acc_type == 'Real') {
                $row[] = "<a href='javascript:;' onclick='view_va("  . esc($bankSubAccount->id) . ")'>" . esc($bankSubAccount->sub_account) . " <i class='bi bi-check-circle-fill text-success ms-1' data-bs-toggle='tooltip' data-bs-placement='top' title='Tài khoản VA chính thức được tạo từ API ngân hàng'></i></a>";
            } else {
                $row[] = "<a href='javascript:;' onclick='view_va("  . esc($bankSubAccount->id) . ")'>" . esc($bankSubAccount->sub_account) . "</a>";
            }

            if ($this->request->getGet('type') === 'dynamic') {
                $row[] = esc($bankSubAccount->bill_id);
                $row[] = number_format($bankSubAccount->amount) . 'đ';
                $row[] = esc($bankSubAccount->expires_at ?: 'Không hết hạn');
            }
          
            if ($bankSubAccount->va_active == 1)
                $row[] = "<span class='text-success'>Hoạt động</span>";
            else
                $row[] = "<span class='text-danger'>Tạm ngưng</span>";

            $row[] = esc($bankSubAccount->label);
            $row[] = esc($bankSubAccount->created_at);
            $row[] = $actionsBtnHtml;
            $data[] = $row;
        }
 
        return $this->response->setJSON([
            'draw' => $draw,
            'recordsTotal' => $bankSubAccountModel->countAll($this->user_session['company_id'], $bankAccountId),
            'recordsFiltered' => $bankSubAccountModel->countFiltered($this->user_session['company_id'], $bankAccountId),
            'data' => $data,
        ]);
    }

    public function ajax_view_va($id = '')
    {
        if (!has_permission('BankAccount', 'can_view_all'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền xem tài khoản ngân hàng']);
        
        if (!is_numeric($id)) 
            return $this->response->setJSON(['status' => false, 'message' => 'ID tài khoản ngân ảo không hợp lệ']);

        $bankSubAccountModel = model(BankSubAccountModel::class);
        $transactionsModel = slavable_model(TransactionsModel::class, 'Mbb');

        $data['va_details'] = $bankSubAccountModel
            ->select('tb_autopay_bank_sub_account.id, tb_autopay_bank_sub_account.sub_account, tb_autopay_bank_sub_account.acc_type, tb_autopay_bank_sub_account.bank_account_id, tb_autopay_bank_sub_account.label, tb_autopay_bank_account.account_number, tb_autopay_bank_account.active, tb_autopay_bank_account.created_at, tb_autopay_bank.brand_name, tb_autopay_bank_account.account_number, tb_autopay_bank_account.account_holder_name, tb_autopay_bank.icon_path, tb_autopay_bank.logo_path, tb_autopay_bank.full_name, tb_autopay_bank_sub_account.sub_holder_name, tb_autopay_bank_sub_account.va_active, tb_autopay_bank_sub_account_metadata.qrcode, tb_autopay_bank_sub_account.content, tb_autopay_bank_sub_account_metadata.bill_id, tb_autopay_bank_sub_account_metadata.amount, tb_autopay_bank_sub_account_metadata.expires_at')
            ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id')
            ->join('tb_autopay_bank', 'tb_autopay_bank.id=tb_autopay_bank_account.bank_id')
            ->join('tb_autopay_bank_sub_account_metadata', 'tb_autopay_bank_sub_account_metadata.bank_sub_account_id=tb_autopay_bank_sub_account.id', 'left')
            ->where([
                'tb_autopay_bank_sub_account.id' => $id, 
                'tb_autopay_bank_account.company_id' => $this->user_session['company_id'],
                'tb_autopay_bank_account.bank_id' => 8
            ])->get()->getRow();

        $data['count_transactions'] = $transactionsModel->where([
            'sub_account' => $data['va_details']->sub_account
        ])->countAllResults();
        
        $data['last_transaction'] = $transactionsModel->where([
            'sub_account' => $data['va_details']->sub_account, 
            'accumulated != ' => ''
        ])->orderBy('transaction_date', 'DESC')->get()->getRow();

        if ($data['va_details']) {
            $html = view('mbb/va_view', $data);
            return $this->response->setJSON(['status' => true, 'html' => $html]);
        }

        return $this->response->setJSON(['status' => false, 'message' => 'Không tìm thấy tài khoản ảo này']);
    }

    public function ajax_request_delete_bank_account()
    {
        if (!$this->mbbConfig->allowedDeleteBankAccount)
            return show_404();

        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        if (!has_permission('BankAccount', 'can_delete'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn chưa được cấp quyền để thực hiện thao tác này']);

        $bankAccountModel = model(BankAccountmodel::class);
        $bankAccountId = trim(xss_clean($this->request->getPost('id')));

        $bankAccountDetails = $bankAccountModel->where([
            'id' => $bankAccountId, 
            'company_id' => $this->user_session['company_id'],
            'bank_id' => 8
        ])->get()->getRow();
        
        if (!is_object($bankAccountDetails))
            return $this->response->setJSON(['status' => false, 'message' => 'Không tìm thấy tài khoản ngân hàng này']);

        $mbbEnterpriseAccountModel = model(MbbEnterpriseAccountModel::class);
        $isEnterpriseAccount = $mbbEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->countAllResults();

        if ($isEnterpriseAccount && !$this->mbbConfig->allowedEnterpriseConnectionViaOtp) {
            return $this->response->setJSON(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng tải lại trang']);
        }

        try {
            $client = new MbbClient;
        } catch (Exception $e) {
            return $this->handleMbbClientException($e);
        }

        try {
            $response = $client->requestPushTransactionMessageUnsubscribe(
                $bankAccountDetails->account_number,
                'SMS',
                'WEB_APP'
            );
            $responseData = json_decode($response->getBody(), true);

            $errorCode = $responseData['errorCode'] ?? $responseData['soaErrorCode'];
            $errorDesc = $responseData['errorDesc'] ?? $responseData['soaErrorDesc'];

            // SIMULATE
            // $errorCode = '000';
            // $responseData['data']['requestId'] = uniqid();
            // SIMULATE

            if ($errorCode == '000') {
                return $this->response->setJSON([
                    'status' => true, 
                    'message' => 'Đã gửi OTP đến số điện thoại.',
                    'request_id' => $responseData['data']['requestId']
                ]);
            }

            if (in_array($errorCode, ['432123', '1020', '432124'])) {
                try {
                    $terminal = model(MbbMmsTerminalModel::class)
                        ->where('bank_account_id', $bankAccountDetails->id)
                        ->first();
                    if ($terminal) {
                        $this->syncTerminal($bankAccountDetails, ['terminalId' => $terminal->terminal_id, 'active' => 0]);
                    }

                    DeleteCompanyBankAccountAction::run($bankAccountDetails, $this->company_details, $this->request->getIPAddress(), self::class);
                    
                    add_user_log(['data_id' => $bankAccountId, 'company_id' => $this->user_session['company_id'], 'data_type' => 'bank_account_delete', 'description' => 'Xóa tài khoản ngân hàng ' . $bankAccountDetails->account_number, 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);
                    set_alert('success', 'Đã hủy liên kết và xóa tài khoản ngân hàng thành công.');

                    return $this->respond(['status' => true, 'redirect_to' => base_url('bankaccount')]);
                } catch (Exception $e) {
                    return $this->handleMbbClientException($e);
                }
            }

            throw new Exception($errorCode . ' - ' . $errorDesc);
        } catch (Exception $e) {
            return $this->handleMbbClientException($e, $client);
        }
    }

    public function ajax_confirm_delete_bank_account()
    {
        $config = config(\App\Config\Mbb::class);
        if (!$config->allowedDeleteBankAccount) return show_404();
        
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        if(!has_permission('BankAccount', 'can_delete'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn chưa được cấp quyền để thực hiện thao tác này"));

        $bankAccountModel = model(BankAccountmodel::class);
        $bankAccountId = trim(xss_clean($this->request->getPost('id')));

        $bankAccountDetails = $bankAccountModel->where([
            'id' => $bankAccountId, 
            'company_id' => $this->user_session['company_id'],
            'bank_id' => 8
        ])->get()->getRow();
        
        if (!is_object($bankAccountDetails))
            return $this->response->setJSON(['status' => false, 'message' => 'Không tìm thấy tài khoản ngân hàng này']);

        $mbbEnterpriseAccountModel = model(MbbEnterpriseAccountModel::class);
        $isEnterpriseAccount = $mbbEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->countAllResults();

        if ($isEnterpriseAccount && !$this->mbbConfig->allowedEnterpriseConnectionViaOtp) {
            return $this->response->setJSON(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng tải lại trang']);
        }

        if (! $this->validate([
            'otp' => 'required',
            'request_id' => 'required'
        ])) return $this->fail($this->validator->getErrors());
        
        $data = [
            'otp' => trim($this->request->getVar('otp')),
            'request_id' => trim($this->request->getVar('request_id')),
        ];

        try {
            $client = new MbbClient;
        } catch (Exception $e) {
            return $this->handleMbbClientException($e);
        }

        try {
            $response = $client->confirmPushTransactionMessageUnSubscribe(
                $data['request_id'],
                $data['otp'],
                'SMS',
                'WEB_APP'
            );
            $responseData = json_decode($response->getBody(), true);
            
            $errorCode = $responseData['errorCode'] ?? $responseData['soaErrorCode'];
            $errorDesc = $responseData['errorDesc'] ?? $responseData['soaErrorDesc'];

            // SIMULATE
            // $errorCode = '000';
            // SIMULATE

            if ($errorCode == '000') {
                try {
                    $terminal = model(MbbMmsTerminalModel::class)
                        ->where('bank_account_id', $bankAccountDetails->id)
                        ->first();
                    if ($terminal) {
                        $this->syncTerminal($bankAccountDetails, ['terminalId' => $terminal->terminal_id, 'active' => 0]);
                    }

                    DeleteCompanyBankAccountAction::run($bankAccountDetails, $this->company_details, $this->request->getIPAddress(), self::class);
                    
                    add_user_log(['data_id' => $bankAccountId, 'company_id' => $this->user_session['company_id'], 'data_type' => 'bank_account_delete', 'description' => 'Xóa tài khoản ngân hàng ' . $bankAccountDetails->account_number, 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);
                    set_alert('success', 'Đã hủy liên kết và xóa tài khoản ngân hàng thành công.');

                    return $this->response->setJSON(['status' => true]);
                } catch (Exception $e) {
                    return $this->response->setJSON(['status' => false, 'message' => $e->getMessage()]);
                }
            }

            if (in_array($errorCode, ['40507', '237'])) {
                return $this->fail(['otp' => 'OTP không chính xác hoặc đã hết hiệu lực.']);
            }

            throw new Exception($errorCode . ' - ' . $errorDesc);
        } catch (Exception $e) {
            return $this->handleMbbClientException($e, $client);
        }
    }

    public function ajax_request_switch_api_connection()
    {
        if (!$this->mbbConfig->allowedSwitchApiConnection)
            return show_404();
        
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        if (!has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn chưa được cấp quyền để thực hiện thao tác này']);

        $bankAccountId = trim(xss_clean($this->request->getPost('id')));
        $bankAccountDetails = $this->getSwitchableApiConnectionBankAccount($bankAccountId);
        
        if (!is_object($bankAccountDetails))
            return $this->response->setJSON(['status' => false, 'message' => 'Tài khoản ngân hàng không đủ điều kiện']);

        $bankAccountModel = model(BankAccountModel::class);
        $mbbEnterpriseAccountModel = model(MbbEnterpriseAccountModel::class);
        $isEnterpriseAccount = $mbbEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->countAllResults();

        if ($isEnterpriseAccount) {
            $bankAccountModel
                ->set([
                    'bank_api_connected' => 0, 
                    'bank_api' => 1, 
                    'bank_sms' => 0
                ])->update($bankAccountDetails->id);

            $redirectTo = base_url('mbb/details/' . $bankAccountDetails->id);
        } else {
            $session = session();
            $session->set('mbb_request_switch_api_connection', $bankAccountDetails->id);
            $redirectTo = base_url('mbb/step1');
        }
        
        return $this->response->setJSON(['status' => true, 'redirect_to' => $redirectTo]);
    }

    public function ajax_request_switch_sms_connection()
    {
        if (!$this->mbbConfig->allowedSwitchSmsConnection)
            return show_404();
        
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        if (!has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn chưa được cấp quyền để thực hiện thao tác này']);

        $bankAccountId = trim(xss_clean($this->request->getPost('id')));
        $bankAccountDetails = $this->getSwitchableSmsConnectionBankAccount($bankAccountId);
        
        if (!is_object($bankAccountDetails))
            return $this->response->setJSON(['status' => false, 'message' => 'Tài khoản ngân hàng không đủ điều kiện']);

        if (!$this->determineIfSwitchToSmsConnection())
            return $this->response->setJSON(['status' => false, 'message' => 'Gói dịch vụ của bạn không cho phép kết nối Ngân hàng qua SMS báo số dư. Vui lòng nâng cấp gói hoặc liên hệ SePay để được hỗ trợ.']);

        return $this->response->setJSON(['status' => true]);
    }

    public function ajax_confirm_switch_sms_connection()
    {
        if (!$this->mbbConfig->allowedSwitchSmsConnection) 
            return show_404();
        
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        if (!has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn chưa được cấp quyền để thực hiện thao tác này']);

        $bankAccountId = trim(xss_clean($this->request->getPost('id')));
        $bankAccountDetails = $this->getSwitchableSmsConnectionBankAccount($bankAccountId);
        
        if (!is_object($bankAccountDetails))
            return $this->response->setJSON(['status' => false, 'message' => 'Tài khoản ngân hàng không đủ điều kiện']);

        if (!$this->determineIfSwitchToSmsConnection())
            return $this->response->setJSON(['status' => false, 'message' => 'Gói dịch vụ của bạn không cho phép kết nối Ngân hàng qua SMS báo số dư. Vui lòng nâng cấp gói hoặc liên hệ SePay để được hỗ trợ.']);

        $bankAccountModel = model(BankAccountModel::class);
        $simCompanyModel = model(SimCompanyModel::class);

        $simId = trim(xss_clean($this->request->getPost('sim_id')));
        $bankAccountUpdateData = [
            'bank_sms' => 1,
            'bank_sms_connected' => 0,
            'bank_api' => 0,
            'bank_api_connected' => 0
        ];

        if (is_numeric($simId)) {
            $simDetails = $simCompanyModel->select('tb_autopay_sim.id')
                ->join('tb_autopay_sim', 'tb_autopay_sim.id=tb_autopay_sim_company.sim_id')
                ->where([
                    'tb_autopay_sim_company.company_id' => $this->user_session['company_id'], 
                    'tb_autopay_sim.active' => 1, 
                    'tb_autopay_sim.id' => $simId
                ])
                ->get()->getRow();

            if (!is_object($simDetails))
                return $this->response->setJSON(['status' => false, 'message' => 'SIM nhận SMS mà bạn chọn không khả dụng']);

            $bankAccountUpdateData['sim_id'] = $simId;
        }

        $bankAccountUpdated = $bankAccountModel->set($bankAccountUpdateData)->update($bankAccountId);

        if (!$bankAccountUpdated)
            return $this->response->setJSON(['status' => false, 'message' => 'Không thể chuyển đổi phương thức kết nối sang SMS Banking, vui lòng liên hệ SePay để được hỗ trợ.']);
        
        add_user_log(['data_id' => $bankAccountId, 'company_id' => $this->user_session['company_id'], 'data_type' => 'bank_account_update', 'description' => 'Chuyển đổi phương thức kết nối sang SMS Banking', 'user_id' => $this->user_details->id, 'ip'=>$this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);
        return $this->response->setJSON(['status' => true]);
    }

    public function test()
    {

        $bankAccountDetails = model(BankAccountModel::class)->where([
            'id' => 448, 
            'company_id' => $this->user_session['company_id']
        ])->get()->getRow();
        
        DeleteCompanyBankAccountAction::run($bankAccountDetails, $this->company_details, $this->request->getIPAddress(), self::class);
    }

    public function ajax_send_enterprise_connection_request()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!$this->mbbConfig->allowedEnterpriseConnection)
            return show_404();

        if (!has_permission('BankAccount', 'can_add'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn chưa được cấp quyền để thực hiện thao tác này']);
    
        if (! $this->validate([
            'company_name' => 'required|min_length[5]|max_length[100]',
            'has_mbb_account' => 'required',
            'phone_number' => 'required|min_length[10]|max_length[20]',
            'notes' => 'permit_empty'
        ])) return $this->fail($this->validator->getErrors());

        $data = [
            'company_name' => trim($this->request->getVar('company_name')),
            'phone_number' => trim($this->request->getVar('phone_number')),
            'has_mbb_account' => $this->request->getVar('has_mbb_account'),
            'notes' => trim($this->request->getVar('notes'))
        ];

        $message = '
------------------------------

Có yêu cầu kết nối API MB doanh nghiệp mới:

#️⃣ Tên cá nhân/tổ chức: ' . $data['company_name'] . '

ℹ️ Đã có tài khoản MB: ' . ($data['has_mbb_account'] ? 'Đã có' : 'Chưa') . '

📞 Số điện thoại liên hệ: ' . $data['phone_number'] . '

⏰ Thực hiện lúc: ' . date("Y-m-d H:i:s") . '

✍🏻 Ghi chú: ' . $data['notes'] . '

------------------------------
        ';

        $telegramQueueModel = model(NotificationTelegramQueueModel::class);
        $telegramQueueModel->insert([
            'chat_id' => $config->telegramId,
            'status' => 'Pending',
            'message' => $message
        ]);

        return $this->response->setJSON([
            'status' => true,
            'message' => 'SePay đã nhận được yêu cầu của bạn và sẽ liên hệ lại trong thời gian sớm nhất',
        ]);
    }

    public function ajax_connect_enterprise()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!$this->mbbConfig->allowedEnterpriseConnection)
            return show_404();

        if (!has_permission('BankAccount', 'can_add'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn chưa được cấp quyền để thực hiện thao tác này']);
    
        if (! $this->validate([
            'account_number' => [
                'label' => 'số tài khoản',
                'rules' => 'required|max_length[250]|is_unique[tb_autopay_bank_account.account_number]',
                'errors' => [
                    'is_unique' => 'Số tài khoản này đã tồn tại trên hệ thống',
                ]
            ],
            'account_holder_name' => 'required|max_length[250]',
            'identification_number' => 'required|max_length[100]',
            'phone_number' => 'required|min_length[10]|max_length[20]',
            'label' => 'permit_empty|max_length[100]'
        ])) return $this->fail($this->validator->getErrors());

        $bankAccountModel = model(BankAccountModel::class);
        $bankAccountId = $bankAccountModel->insert([
            'company_id' => $this->user_session['company_id'],
            'account_holder_name' => remove_accents(trim(xss_clean($this->request->getVar('account_holder_name'))), true),
            'account_number' => trim(xss_clean($this->request->getVar('account_number'))),
            'bank_id' => 8,
            'label' => trim(xss_clean($this->request->getVar('label'))),
            'active' => 1,
            'bank_api' => 1,
            'bank_api_connected' => 0,
            'identification_number' => trim(xss_clean($this->request->getVar('identification_number'))),
            'phone_number' => trim(xss_clean($this->request->getVar('phone_number')))
        ]);
            
        if ($bankAccountId) {
            $mbbEnterpriseAccountModel = model(MbbEnterpriseAccountModel::class);
            $mbbEnterpriseAccountModel->insert(['bank_account_id' => $bankAccountId]);
            
            add_user_log(['data_id' => $bankAccountId, 'company_id' => $this->user_session['company_id'], 'data_type' => 'bank_account_add', 'description' => 'Thêm tài khoản ngân hàng MB API', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);
            return $this->response->setJSON(['status' => true, 'id' => $bankAccountId]);
        }

        add_user_log(['data_id' => 0, 'company_id' => $this->user_session['company_id'], 'data_type' => 'bank_account_add', 'description' => 'Thêm tài khoản ngân hàng MB API', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Failed']);
        return $this->response->setJSON(['status' => false, 'message' => 'Không thể thêm tài khoản ngân hàng. Vui lòng liên hệ SePay để được hỗ trợ.']);
    }

    public function ajax_unlink($id = '')
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        if (!has_permission('BankAccount', 'can_add'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn chưa được cấp quyền để thực hiện thao tác này']);

        $bankAccountModel = model(BankAccountmodel::class);
        $bankAccountId = trim(xss_clean($this->request->getPost('id')));

        $bankAccountDetails = $bankAccountModel->where([
            'id' => $bankAccountId, 
            'company_id' => $this->user_session['company_id'],
            'bank_id' => 8
        ])->get()->getRow();
        
        if (!is_object($bankAccountDetails))
            return $this->response->setJSON(['status' => false, 'message' => 'Không tìm thấy tài khoản ngân hàng này']);

        $mbbEnterpriseAccountModel = model(MbbEnterpriseAccountModel::class);
        $isEnterpriseAccount = $mbbEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->countAllResults();

        if ($isEnterpriseAccount && !$this->mbbConfig->allowedEnterpriseConnectionViaOtp) {
            return $this->response->setJSON(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng tải lại trang']);
        }
        
        DeleteCompanyBankAccountAction::run($bankAccountDetails, $this->company_details, $this->request->getIPAddress(), self::class);

        set_alert('success', 'Đã xóa tài khoản ngân hàng.');

        return $this->response->setJSON([
            'status' => true, 
        ]);
    }

    public function ajax_bank_account_update() 
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        if (!has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền sửa tài khoản ngân hàng']);
 
        $validation = \Config\Services::validation();

        helper('text');

        if (! $this->validate([
            'id' => 'required|integer|is_natural',
            'label' => ['label' => 'Tên gợi nhớ', 'rules' => "max_length[100]"],
        ])) {
            return $this->response->setJSON(['status' => false, 'message' => implode(". ", $validation->getErrors())]);
        }  

        $bankAccountModel = model(BankAccountmodel::class);
        $bankAccountId = trim(xss_clean($this->request->getPost('id')));

        $bankAccount = $bankAccountModel->where(['id' => $bankAccountId, 'company_id' => $this->user_session['company_id']])->get()->getRow();
        
        if (!is_object($bankAccount))
            return $this->response->setJSON(['status' => false, 'message' => 'Không tìm thấy tài khoản ngân hàng này']);

        $data = [
            'label' => trim(xss_clean($this->request->getVar('label'))),
        ];
           
        $updated = $bankAccountModel->set($data)->where('id', $bankAccountId)->update();
        
        if ($updated) { 
            add_user_log(['data_id' => $bankAccountId, 'company_id' => $this->user_session['company_id'], 'data_type' => 'bank_account_update', 'description' => 'Sửa tài khoản ngân hàng', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);
            return $this->response->setJSON(['status' => true]);
        } else {
            return $this->response->setJSON(['status' => false, 'message' => 'Không thể cập nhật tài khoản ngân hàng!']);
        }
    }

    public function ajax_assign_terminal()
    {
        if ($this->request->getMethod(true) !== 'POST') {
            show_404();
        }

        if (! has_permission('BankAccount', 'can_edit')) {
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền sửa tài khoản ngân hàng']);
        }

        $bankAccountId = trim(xss_clean($this->request->getPost('id')));

        $mbbMmsTerminalModel = model(MbbMmsTerminalModel::class);
        $bankAccountModel = model(BankAccountModel::class);

        $bankAccount = $bankAccountModel
            ->select('tb_autopay_bank_account.id, tb_autopay_bank_account.account_number, tb_autopay_bank_account.account_holder_name')
            ->join('tb_autopay_bank', 'tb_autopay_bank.id=tb_autopay_bank_account.bank_id')
            ->where('tb_autopay_bank_account.id', $bankAccountId)
            ->where('tb_autopay_bank_account.company_id', $this->user_session['company_id'])
            ->where('tb_autopay_bank_account.bank_id', 8)
            ->first();

        if (! $bankAccount) {
            return $this->response->setJSON(['status' => false, 'message' => 'Không tìm thấy tài khoản ngân hàng này']);
        }

        $isSyncedTerminal = !!$mbbMmsTerminalModel
            ->where('bank_account_id', $bankAccountId)
            ->countAllResults();

        if ($isSyncedTerminal) {
            return $this->response->setJSON(['status' => false, 'message' => 'Tài khoản ngân hàng này đã được gán terminal']);
        }

        $this->syncTerminal($bankAccount);

        return $this->response->setJSON([
            'status' => true,
        ]);
    }

    public function ajax_add_real_va()
    {
        if ($this->request->getMethod(true) !== 'POST') {
            show_404();
        }

        if (! has_permission('BankAccount', 'can_add')) {
            return $this->fail('Bạn không có quyền thêm tài khoản ngân hàng');
        }
 
        $data = [
            'bank_account_id' => trim(xss_clean($this->request->getPost('bank_account_id'))),
            'label' => trim(xss_clean($this->request->getPost('label'))),
        ];

        $rules = [
            'bank_account_id' => ['label' => 'Tài khoản chính', 'rules' => 'integer|is_natural'],
            'label' => ['label' => 'Tên gợi nhớ', 'rules' => 'permit_empty|max_length[100]'],
        ];

        if (! $this->validateData($data, $rules)) {
            return $this->failValidationErrors($this->validator->getErrors());
        }

        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);

        $bankAccount = $bankAccountModel
            ->select('id, active, company_id, bank_id, account_holder_name')
            ->where('id', $data['bank_account_id'])
            ->where('active', true)
            ->where('bank_id', 8)
            ->where('company_id', $this->user_session['company_id'])
            ->first();

        if (! $bankAccount) {
            return $this->fail('Không tìm thấy tài khoản ngân hàng này');
        }

        $mbbMmsTerminal = model(MbbMmsTerminalModel::class)
            ->where('bank_account_id', $data['bank_account_id'])
            ->first();

        $client = new MbbMmsClient();
        $client->setMerchantById($mbbMmsTerminal->merchant_id);

        $response = $client->createQrCode(
            $mbbMmsTerminal->terminal_id,
            1,
            2,
            11,
            null,
            null,
            0,
            0,
            0,
            null,
            $bankAccount->account_holder_name,
            null,
            null
        );

        $resData = json_decode($response->getBody(), true);

        if ($resData['errorCode'] != '000') {
            return $this->fail('Không thể tạo tài khoản ảo');
        }

        $bankSubAccountId = $bankSubAccountModel->insert([
            'bank_account_id' => $data['bank_account_id'],
            'acc_type' => 'Real',
            'sub_account' =>  $resData['data']['accountQR'],
            'sub_holder_name' => $bankAccount->account_holder_name,
            'label' => $data['label'],
        ]);

        model(BankSubAccountMetadataModel::class)->insert([
            'bank_account_id' => $data['bank_account_id'],
            'bank_sub_account_id' => $bankSubAccountId,
            'qrcode' => $resData['data']['qrcode'],
        ]);

        if (! $bankSubAccountId) {
            return $this->fail('Không thể thêm tài khoản ảo');
        }

        return $this->response->setJSON([
            'status' => true,
             'id' => $bankSubAccountId,
        ]);
    }

    public function ajax_delete_real_va()
    {
        if ($this->request->getMethod(true) !== 'POST') {
            show_404();
        }

        $id = trim($this->request->getPost('id'));
        $bankAccountId = trim($this->request->getPost('bank_account_id'));

        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);

        if (! is_numeric($id)) {
            return $this->response->setJSON(['status' => false, 'message' => 'ID tài khoản ảo không hợp lệ']);
        }

        if (! has_permission('BankAccount', 'can_delete')) {
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền thực hiện thao tác này']);
        }

        $bankAccount = $bankAccountModel
            ->select('tb_autopay_bank_account.id, tb_autopay_bank_account.company_id, tb_autopay_mbb_mms_terminal.merchant_id')
            ->where('tb_autopay_bank_account.id', $bankAccountId)
            ->where('tb_autopay_bank_account.company_id', $this->user_session['company_id'])
            ->join('tb_autopay_mbb_mms_terminal', 'tb_autopay_mbb_mms_terminal.bank_account_id=tb_autopay_bank_account.id')
            ->first();

        if (! $bankAccount) {
            return $this->respond(['status' => false, 'message' => 'Không tìm thấy tài khoản ngân hàng này']);
        }

        $bankSubAccount = $bankSubAccountModel
            ->select('tb_autopay_bank_sub_account.id, tb_autopay_bank_sub_account.va_type, tb_autopay_bank_sub_account.acc_type, tb_autopay_bank_sub_account_metadata.qrcode')
            ->where('tb_autopay_bank_sub_account.id', $id)
            ->join('tb_autopay_bank_sub_account_metadata', 'tb_autopay_bank_sub_account_metadata.bank_sub_account_id=tb_autopay_bank_sub_account.id', 'left')
            ->first();

        if (! $bankSubAccount) {
            return $this->respond(['status' => false, 'message' => 'Không tìm thấy tài khoản ảo này']);
        }

        if ($bankSubAccount->acc_type === 'Real') {
            $client = new MbbMmsClient();
            $client->setMerchantById($bankAccount->merchant_id);
            $response = $client->deleteQrCode($bankSubAccount->qrcode);

            $data = json_decode($response->getBody(), true);

            log_message('error', 'Delete QR code response: ' . $response->getBody());

            if ($data['errorCode'] !== '000' || $data['data']['isSuccess'] !== true) {
                return $this->respond(['status' => false, 'message' => 'Không thể xóa tài khoản ảo']);
            }
        }

        if ($bankSubAccount->va_type === 'Static') {
            $bankSubAccountModel->delete($id);
            model(BankSubAccountMetadataModel::class)->where('bank_sub_account_id', $id)->delete();
        } else {
            $bankSubAccountModel->update($id, [
                'active' => false,
                'va_active' => false,
            ]);
        }
    
        return $this->respond(['status' => true]);
    }

    protected function syncTerminal($bankAccount, $extra = []): void
    {
        if (!is_admin() && !$this->mbbMmsConfig->enabled) return;

        $merchant = model(MbbMmsMerchantModel::class)->first();
        $model = model(MbbMmsTerminalModel::class);
        
        $outputDeviceDecalModel = model(OutputDeviceDecalModel::class);
        $outputDeviceDecal = $outputDeviceDecalModel->where([
            'account_number' => $bankAccount->account_number,
            'bank_id' => 8,
            'virtual_account_number !=' => null,
            'output_device_id !=' => null
        ])->first();
        $terminal = $outputDeviceDecal
            ? $model->where(['output_device_id' => $outputDeviceDecal->output_device_id, 'merchant_id' => $merchant->id])->first() 
            : null;
        
        if ($outputDeviceDecal && $terminal) {
            $model->where('id', $terminal->id)->set([
                'bank_account_id' => $bankAccount->id,
            ])->update();
            return;
        }
        
        $client = new MbbMmsClient();
        $client->setMerchantById($merchant->id);

        $response = $client->syncTerminalsWithNonOtp([
            array_merge([
                'terminalName' => $bankAccount->account_holder_name,
                'provinceCode' => '1',
                'districtCode' => '6',
                'wardsCode' => '178',
                'mccCode' => '1024',
                'fee' => 0,
                'bankCode' => '311',
                'bankCodeBranch' => '********',
                'bankAccountNumber' => $client->encryptData($bankAccount->account_number),
                'bankAccountName' => $bankAccount->account_holder_name,
                'bankCurrencyCode' => 1,
            ], $extra),
        ]);

        $data = json_decode($response->getBody(), true);
        
        if ($data['errorCode'] != '000') {
            log_message('error', 'Sync terminal failed: ' . $response->getBody());
        }

        $result = $data['data']['result'][0];

        if ($result['syncStatus'] === 'create_ok') {
            $model->insert([
                'merchant_id' => $merchant->id,
                'bank_account_id' => $bankAccount->id,
                'terminal_id' => $result['terminalId'],
                'output_device_id' => $outputDeviceDecal ? $outputDeviceDecal->output_device_id : null,
            ]);
        }

        if (isset($extra['active']) && $extra['active'] === 0 && $result['syncStatus'] === 'update_ok') {
            $model
                ->where('bank_account_id', $bankAccount->id)
                ->where('terminal_id', $result['terminalId'])
                ->delete();
        }
    }
}
