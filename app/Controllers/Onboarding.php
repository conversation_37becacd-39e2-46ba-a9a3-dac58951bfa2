<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\OnboardingSubmissionModel;
use App\Services\Onboarding\OnboardingFlowFactory;
use Exception;

class Onboarding extends BaseController
{
    public function index()
    {
        $data = $this->request->getGet();

        if (! $this->validateData($data, [
            'feature' => 'required|string|in_list[card,napas_qr]',
            'step' => 'required|string|min_length[1]',
        ])) {
            show_404();
        }

        $feature = $data['feature'];
        $stepParam = $data['step'];

        $flow = OnboardingFlowFactory::make($feature);
        $totalSteps = count($flow->getAllowedSteps()) - 1;

        $onboardingModel = model(OnboardingSubmissionModel::class);
        $record = $onboardingModel->getOrCreate($this->company_details->id, $feature);

        if ($stepParam === 'complete') {
            $currentStepNumber = $totalSteps + 1;
            $isComplete = true;
        } else {
            $currentStepNumber = (int) $stepParam ?: ($record->current_step ?: 1);
            $allowedMaxStep = ($record->current_step ?: 0) + 1;

            if ($currentStepNumber < 1 || $currentStepNumber > $totalSteps) {
                return redirect()->to(base_url("onboarding?feature=$feature&step={$record->current_step}"));
            }

            if ($currentStepNumber > $allowedMaxStep) {
                return redirect()->to(base_url("onboarding?feature=$feature&step=$allowedMaxStep"));
            }

            $isComplete = false;
        }
        $savedData = $onboardingModel->getOnboardData($this->company_details->id, $feature);
        $currentStepData = $savedData[$currentStepNumber - 1] ?? [];

        $backUrl = $this->getPreviousStepUrl($currentStepNumber, $feature);

        $viewData = [
            'page_title' => $flow->getPageTitle($currentStepNumber),
            'user_details' => $this->user_details ?? null,
            'company_details' => $this->company_details ?? null,
            'current_step_number' => $currentStepNumber,
            'saved_data' => $savedData,
            'current_step_data' => $currentStepData,
            'total_steps' => $totalSteps,
            'is_complete' => $isComplete,
            'onboarding_feature' => $feature,
            'back_url' => $backUrl,
            'record_current_step' => ($record->current_step ?: 0) + 1,
        ];

        $view = $flow->getViewForStep($currentStepNumber);
        return view($view, $viewData);
    }

    public function saveStep()
    {
        if (! $this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $feature = $this->request->getPost('feature');
        $step = $this->request->getPost('step');
        $data = $this->request->getPost('data') ?? [];

        if (empty($feature) || empty($step)) {
            return $this->response->setJSON(['success' => false, 'message' => 'Feature and step are required']);
        }

        $flow = OnboardingFlowFactory::make($feature);
        $stepNumber = (int) $step;

        try {
            $onboardingModel = model(OnboardingSubmissionModel::class);
            $onboardingModel->updateOnboardData($this->company_details->id, $feature, [$stepNumber => $data]);
            $onboardingModel->setCurrentStep($this->company_details->id, $feature, $stepNumber);

            $totalSteps = count($flow->getAllowedSteps()) - 1;
            $nextStepNumber = $stepNumber < $totalSteps ? $stepNumber + 1 : $totalSteps;
            $isComplete = $stepNumber >= $totalSteps;

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Step saved successfully',
                'is_complete' => $isComplete,
                'redirect_url' => $isComplete
                    ? base_url('onboarding?feature=' . $feature . '&step=complete')
                    : base_url('onboarding?feature=' . $feature . '&step=' . $nextStepNumber)
            ]);
        } catch (Exception $e) {
            return $this->response->setJSON(['success' => false, 'message' => 'Error saving step: ' . $e->getMessage()]);
        }
    }

    public function completeOnboarding()
    {
        if (! $this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $feature = $this->request->getPost('feature') ?: 'card';

        try {
            $flow = OnboardingFlowFactory::make($feature);
            $totalSteps = count($flow->getAllowedSteps()) - 1;

            $documentsData = [];
            $uploadPath = WRITEPATH . 'uploads/onboarding/' . $this->company_details->id . '/';

            if (! is_dir($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            $fileFields = [
                'business_registration',
                'company_charter',
                'legal_rep_id',
                'director_appointment',
                'cfo_appointment',
                'cfo_id',
                'beneficial_owner_id'
            ];

            foreach ($fileFields as $field) {
                $file = $this->request->getFile($field);
                if ($file && $file->isValid() && ! $file->hasMoved()) {
                    $newName = $field . '_' . time() . '.' . $file->getExtension();
                    if ($file->move($uploadPath, $newName)) {
                        $documentsData[$field] = $newName;
                    }
                }
            }

            $documentsData['confirm_documents'] = $this->request->getPost('confirm_documents') ?: '0';

            $onboardingModel = model(OnboardingSubmissionModel::class);
            $onboardingModel->updateOnboardData($this->company_details->id, $feature, [$totalSteps => $documentsData]);
            $onboardingModel->setCurrentStep($this->company_details->id, $feature, $totalSteps);
            $onboardingModel->submitOnboarding($this->company_details->id, $feature);

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Onboarding completed successfully',
                'redirect_url' => base_url('onboarding?feature=' . $feature . '&step=complete')
            ]);
        } catch (Exception $e) {
            return $this->response->setJSON(['success' => false, 'message' => 'Error completing onboarding: ' . $e->getMessage()]);
        }
    }

    private function getPreviousStepUrl(int $currentStepNumber, string $feature): ?string
    {
        if ($currentStepNumber > 1) {
            $previousStepNumber = $currentStepNumber - 1;
            return base_url('onboarding?feature=' . $feature . '&step=' . $previousStepNumber);
        }

        return null;
    }
}
