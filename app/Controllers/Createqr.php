<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\BankAccountModel;
use App\Models\BankSubAccountModel;
use App\Models\TransactionsModel;
use App\Models\UserPermissionBankModel;
use App\Models\UserPermissionBankSubModel;

class Createqr extends BaseController
{
    public function index()
    { 
        $data = [
            'page_title' => 'Tạo QR',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        if(!has_permission('Transactions', 'can_view_all'))
            show_404();

        $data['role'] = $this->company_details->role;


        $data['bank_account_id'] = $this->request->getGet('account_id');

        $bankAccountModel = model(BankAccountModel::class);


        if(!is_numeric($data['bank_account_id']))
            $data['bank_account_id'] = FALSE;
        else {
            $bank_account_details = $bankAccountModel->where(['id' => $data['bank_account_id'], 'company_id' => $this->user_session['company_id']])->get()->getRow();
            if(!is_object($bank_account_details))
                show_404();

            if(!has_bank_permission($data['bank_account_id']))
               show_404();
        }

        $userPermissionBankModel = model(UserPermissionBankModel::class);
        $userPermissionBankSubModel = model(UserPermissionBankSubModel::class);

        $bankSubAccountModel = model(BankSubAccountModel::class);


        if(in_array($data['role'],['Admin','SuperAdmin']))
            $data['bankAccounts'] = $bankAccountModel->select("
                tb_autopay_bank_account.id,tb_autopay_bank_account.bank_id,tb_autopay_bank_account.account_holder_name,
                tb_autopay_bank_account.bank_sms_connected,tb_autopay_bank_account.bank_api_connected,tb_autopay_bank_account.account_number,tb_autopay_bank_account.label,tb_autopay_bank.brand_name,tb_autopay_bank.short_name,tb_autopay_bank.brand_name,tb_autopay_bank.logo_path,tb_autopay_bank.icon_path")->join("tb_autopay_bank","tb_autopay_bank.id=tb_autopay_bank_account.bank_id")->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id']])->orderBy('tb_autopay_bank_account.id','asc')->get()->getResult();
        else
            $data['bankAccounts'] = $userPermissionBankModel->get_bank_accounts($this->user_details->id);

        $data['configBankSubAccount'] = get_configuration('BankSubAccount');

        if($data['configBankSubAccount'] == 'on' && $data['bank_account_id']) {
            if(in_array($data['role'],['Admin','SuperAdmin']))
                $data['bank_sub_accounts'] = $bankSubAccountModel->where(['bank_account_id' => $data['bank_account_id']])->orderBy('id','ASC')->get()->getResult();
            else
                $data['bank_sub_accounts'] = $userPermissionBankSubModel->where(['tb_autopay_user_permission_bank_sub.user_id' => $this->user_details->id, 'tb_autopay_bank_sub_account.bank_account_id' => $data['bank_account_id']])->join("tb_autopay_bank_sub_account","tb_autopay_bank_sub_account.id=tb_autopay_user_permission_bank_sub.sub_account_id")->orderBy('tb_autopay_bank_sub_account.id','ASC')->get()->getResult();
        } else {
            $data['bank_sub_accounts'] = [];
        }

        // 
        $bank_accounts = array_map(function($obj) {
            return get_object_vars($obj);
        }, $data['bankAccounts']);

        $list_id_bank_account = array_column($bank_accounts, "id");
        $data['bank_sub_accounts_custom']=[];
        if(!empty($list_id_bank_account)){

            $query = $bankSubAccountModel->select(
                "tb_autopay_bank_sub_account.id, 
                 tb_autopay_bank.brand_name,
                 tb_autopay_bank.logo_path, 
                 tb_autopay_bank.icon_path, 
                 tb_autopay_bank_account.account_holder_name, 
                 tb_autopay_bank_account.account_number, 
                 tb_autopay_bank_account.bank_sms_connected, 
                 tb_autopay_bank_account.bank_api_connected, 
                 tb_autopay_bank_sub_account.sub_account, 
                 tb_autopay_bank_sub_account.acc_type, 
                 tb_autopay_bank_sub_account.label",
                )
                ->join("tb_autopay_bank_account", "tb_autopay_bank_account.id = tb_autopay_bank_sub_account.bank_account_id")
                ->join("tb_autopay_bank", "tb_autopay_bank.id = tb_autopay_bank_account.bank_id", "left")
                ->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id']])
                ->whereIn('tb_autopay_bank_account.id', $list_id_bank_account); 
                
            if (in_array($this->company_details->role, ['Admin', 'SuperAdmin'])) {
                $data['bank_sub_accounts_custom']  = $query->get()->getResultArray();
            } else {
                $data['bank_sub_accounts_custom']  = $query
                ->join("tb_autopay_user_permission_bank_sub", "tb_autopay_user_permission_bank_sub.sub_account_id = tb_autopay_bank_sub_account.id", "left")
                ->where("tb_autopay_user_permission_bank_sub.user_id", $this->user_session['user_id'])
                ->get()
                ->getResultArray();
            }

        }
        
        echo view('templates/autopay/header',$data);
        echo view('createqr/index',$data);
        echo view('templates/autopay/footer',$data);

    }
}
