<?php

namespace App\Controllers;

use CodeIgniter\Controller;
use App\Models\NotificationTelegramModel;
use App\Models\NotificationTelegramQueueModel;
use App\Models\NotificationTelegramGroupModel;
use App\Models\TransactionsModel;
use App\Models\WebhooksModel;
use App\Models\WebhooksQueueModel;
use App\Models\WebhooksLogModel;
use App\Models\NotificationLarkMessengerModel;
use App\Models\NotificationLarkMessengerQueueModel;
use App\Models\IbankingModel;
use App\Models\SimModel;
use App\Models\SmsParserModel;
use App\Models\BankAccountModel;
use App\Models\ConfigurationModel;
use App\Models\CounterModel;
use App\Models\SmsModel;
use App\Models\SystemLogModel;
use App\Models\UserLogModel;
use App\Models\CompanySubscriptionModel;
use App\Models\BankAccountCashflowModel;
use App\Models\BankSubAccountCashflowModel;
use App\Models\BankSubAccountModel;
use App\Models\OcbNotificationModel;
use App\Models\OcbNotificationRawModel;
use App\Models\MbbNotificationModel;
use App\Models\MbbNotificationRawModel;
use App\Models\BidvNotificationModel;
use App\Models\BidvNotificationRawModel;
use App\Libraries\ProxyCURLRequest;
use App\Models\AcbNotificationModel;
use App\Models\AcbNotificationRawModel;
use PhpAmqpLib\Message\AMQPMessage;
use App\Models\CompanyModel;
use App\Models\VAModel;
use App\Models\VAOrderModel;
use App\Models\GoogleSheetQueueModel;
use App\Models\BankGoogleSheetModel;
use App\Models\MbbMmsNotificationModel;
use App\Models\NotificationViberQueueModel;
use App\Models\TpbankNotificationModel;
use App\Models\TpbankNotificationRawModel;
use App\Models\VietinbankNotificationModel;
use App\Models\VietinbankNotificationRawModel;
use App\Models\VpbankNotificationModel;
use App\Models\VpbankNotificationRawModel;
use App\Models\ZnsQueueModel;
use Config\Zns;
use App\Models\PgIpnQueueModel;
use App\Libraries\RabbitMQClient;
use App\Models\PgAgreementModel;
use CodeIgniter\CLI\CLI;

class Cron extends Controller
{  

    public function notification_telegram_queues() {
        if(!is_cli())
            return FALSE;
            
        helper('general');

        $slavableModel = slavable_model(NotificationTelegramQueueModel::class, 'Cron');

        while(1) {

            $notificationTelegramQueueModel = model(NotificationTelegramQueueModel::class);
            //$result = $notificationTelegramQueueModel->checkAndAddQueue(751);

            $queues = $slavableModel->where(['status' => 'Pending'])->get()->getResult();

            $cache = service('cache');
            $rabbitmq = new \App\Libraries\RabbitMQClient;
            $queuable = $rabbitmq->connect();

            foreach($queues as $queue) {
                if ($cache->get('telegram_queuing_' . $queue->id)) continue;

                if ($queuable) {

                    $msg = new AMQPMessage(
                        json_encode($queue),
                        array('delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT)
                    );

                    try {
                        $rabbitmq->queueDeclare('telegram');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('telegram', 'name'));
                        $cache->save('telegram_queuing_' . $queue->id, 1, 3600 * 24);

                        continue;
                    } catch (\Exception $e) {
                        log_message('error', 'Telegram queue failed: ' . $e->getMessage());
                    }
                }

                log_message('error', 'fallback telegram queue');

                $result = $notificationTelegramQueueModel->sendTelegramMessage($queue->message,$queue->chat_id, 'html', $queue->message_thread_id);

                $queue_status = 'SoftFailed';
                $queue_last_log = '';

                if(isset($result['response']))
                    $queue_last_log = NotificationTelegramQueueModel::summaryLastLogResponse($result['response']);

                if(isset($result['success']) && $result['success'] == TRUE) {
                    if(isset($result['response'])) {

                        $api_response = json_decode($result['response']);

                        if($api_response) {
                            if(is_object($api_response) && isset($api_response->ok) && $api_response->ok == true) {
                                $queue_status = 'Success';
                            }

                            if(is_object($api_response) && isset($api_response->ok) && $api_response->ok == false) {
                                $queue_status = 'Failed';
                            }
                        }
                    }
                }
  
                $notificationTelegramQueueModel->set(['status' => $queue_status,'last_log' => $queue_last_log])->where(['id' => $queue->id])->update();

                if($queue_status == 'Success') {
                    $transactionsModel = model(TransactionsModel::class);

                    $transactionsModel->set('chat_push_message', 'chat_push_message+1', false)->where(['id' => $queue->transaction_id])->update();

                    $notificationTelegramModel = model(NotificationTelegramModel::class);
                    $counterModel = model(CounterModel::class);
                    $result = $notificationTelegramModel->where(['id' => $queue->notify_id])->get()->getRow();
                    if($result)
                        $counterModel->chat($result->company_id, FALSE, 'telegram');

                }
            }

            sleep(1);
        }
    }

    public function telegram_send_welcome() {
        if(!is_cli())
            return FALSE;

        $updateId = '';
        $telegramBotConfig = config(\App\Config\TelegramBot::class);
        $autopay_bot_id = $telegramBotConfig->id;
        $notificationTelegramQueueModel = model(NotificationTelegramQueueModel::class);
        $notificationTelegramGroupModel = model(NotificationTelegramGroupModel::class);

        while(1) {
            $last_row = $notificationTelegramGroupModel->orderBy('update_id', 'DESC')->limit(1)->get()->getRow();
           
            if(!$updateId && is_object($last_row) && is_numeric($last_row->update_id) && $last_row->update_id > 10) {
                $offset = $last_row->update_id;
                $offset_text = '?offset=' . $offset;
              
            } else if ($updateId) {
                $offset_text = '?offset=' . $updateId;
            } else {
                $offset_text =  '';
            }

            $curl = curl_init();
                curl_setopt_array($curl, array(
                CURLOPT_RETURNTRANSFER => 1,
                CURLOPT_URL => 'https://api.telegram.org/bot' . $telegramBotConfig->id . ':' . $telegramBotConfig->token . '/getUpdates' . $offset_text,
                CURLOPT_USERAGENT => 'AutoBot fetch',
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_PROXY => ProxyCURLRequest::getProxy('telegram')
            ));

            $resp = curl_exec($curl);

            $api_response = json_decode($resp);

            $already_welcome_data = [];

            if(is_object($api_response) && isset($api_response->ok) && $api_response->ok == true && isset($api_response->result) && is_array($api_response->result)) {
                foreach($api_response->result as $result) {
                    $updateId = $result->update_id;

                    if(isset($result->update_id) && isset($result->message) && isset($result->message->chat) &&  isset($result->message->chat->id) && !in_array($result->message->chat->id, $already_welcome_data) ) {

                        $already_welcome = $notificationTelegramGroupModel->where(['chat_id' => $result->message->chat->id])->get()->getRow();
                        
                        if(is_object($already_welcome)) {
                            array_push($already_welcome_data, $result->message->chat->id);
                            continue;
                        }
                            

                        $welcome_message = "Xin chào,
Tôi là SePay Robot, tôi sẽ hỗ trợ thông báo khi phát sinh giao dịch ngân hàng cho bạn. 
Telegram Group ID là: <code>" . $result->message->chat->id . "</code>
Hãy thêm ID trên vào phần cấu hình <a href='" . base_url('notificationtelegram') ."'>Thông báo Telegram</a> của website SePay nhé!
";
                        $group_title = NULL;
                        $last_log = NULL;
                        $send_welcome_status = 'Failed';

                        // SePay Bot added from exist group
                        if(isset($result->message->new_chat_member) && isset($result->message->new_chat_member->id) && $result->message->new_chat_member->id == $autopay_bot_id && !in_array($result->message->chat->id, $already_welcome_data)) {
                            // send welcome message
 
                            $send_result = $notificationTelegramQueueModel->sendTelegramMessage($welcome_message,$result->message->chat->id,'html');
                            echo "sent welcome: chat_id: " . $result->message->chat->id . "<br>";

                            if(isset($send_result['success']) && $send_result['success'] == TRUE) {
                                $send_welcome_status = 'Success';
                                $last_log = $send_result['response'];
                            }
 
                            if(isset($result->message->chat->title))
                                $group_title = $result->message->chat->title;

                            $notificationTelegramGroupModel->insert(['update_id' => $result->update_id, 'chat_id' => $result->message->chat->id, 'type' => 'new_chat_member','send_welcome_status'=>$send_welcome_status, 'send_welcome_message' => $welcome_message,'message' => json_encode($result->message), 'bot_id' => $autopay_bot_id, 'last_log' => $last_log,'group_title' => $group_title]);
                            array_push($already_welcome_data, $result->message->chat->id);
                        }
                        // Create group with autopay bot
                        if(isset($result->message->group_chat_created) && $result->message->group_chat_created == true && !in_array($result->message->chat->id, $already_welcome_data)) {
                        
                            // send welcome message
                            $send_result = $notificationTelegramQueueModel->sendTelegramMessage($welcome_message,$result->message->chat->id,'html');

                            echo "sent welcome: chat_id: " . $result->message->chat->id . "<br>";

                            if(isset($send_result['success']) && $send_result['success'] == TRUE) {
                                $send_welcome_status = 'Success';
                                $last_log = $send_result['response'];
                            }

                            if(isset($result->message->chat->title))
                                $group_title = $result->message->chat->title;

                            $notificationTelegramGroupModel->insert(['update_id' => $result->update_id, 'chat_id' => $result->message->chat->id, 'type' => 'group_chat_created','send_welcome_status'=>$send_welcome_status, 'send_welcome_message' => $welcome_message,'message' => json_encode($result->message), 'bot_id' => $autopay_bot_id, 'last_log' => $last_log,'group_title' => $group_title]);

                            array_push($already_welcome_data, $result->message->chat->id);

                        }
                    }
                }
            }
 
            sleep(2);
       }
    }

    public function add_transaction_in_demo() {
        if(!is_cli())
            return FALSE;

        $accounts_demo = ['*************','*************'];
        $sub_accounts_demo = ['VCB0011ABC001', 'VCB0011ABC002', 'VCB0011ABC003', 'VCB0011ABC004', 'VCB0011ABC005'];

        $sim = "**********";

    
        $account_number = $accounts_demo[array_rand($accounts_demo,1)];

        // random amount_in [from 100k to 50tr]
        $amount_in = rand(500, 32000) * 1000;

        // random time sleep
        $sleep_time = rand(600,3600);

        // random reference_number
        $reference_number = rand(100000,999999) . '.' . date("dmy") . '.' . date("his");

        // random sub account TKP#VCB0011DDV007#
        $sub_account = $sub_accounts_demo[array_rand($sub_accounts_demo,1)];
        $reference_number = "TKP#" . $sub_account . "#" . $reference_number;

        // calculate accumulated
        $transactionsModel = model(TransactionsModel::class);

        $last_transaction  = $transactionsModel->where(['account_number' => $account_number])->orderBy('id','desc')->get()->getRow();

        
        if(!is_object($last_transaction))
            $last_accumulated = 0;
        else
            $last_accumulated = $last_transaction->accumulated;

        $accumulated = $last_accumulated + $amount_in;

        $from_demo = ['NGUYEN VAN A', 'TRAN ANH DUONG', 'TRAN THIEN THAO', 'DUONG THUY ANH', 'LE VAN TAM', 'DINH NHU TOAN', 'HOANG ANH', 'TRUONG QUOC THINH', 'KIEU TRANG', 'LE TRONG HIEU', 'NGUYEN THIEN TUAN', 'TRAN VAN THUAN'];

        $transaction_content = $from_demo[array_rand($from_demo,1)] . ' chuyen tien...' ;

        //SD TK ********** +3,342,000VND luc 12-02-2023 20:36:53. SD 26,223,981,662VND. Ref MBVCB.**********.Anh Hien 198HVT.CT tu ************* TRAN THI THUY LINH toi...
        $body = "SD TK " . $account_number . " +" . number_format($amount_in) . 'VND luc ' . date("d-m-Y H:i:s", strtotime("14 seconds ago")) . ". SD " . number_format($accumulated) . "VND. Ref " . $reference_number . "." . $transaction_content;

        $post_data = ["from" => "Vietcombank", "body" => $body, "to" => $sim];

        
        // Start the request
        $curl = curl_init();

        curl_setopt($curl, CURLOPT_RETURNTRANSFER, TRUE);
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 5); 
        curl_setopt($curl, CURLOPT_TIMEOUT, 8); //timeout in seconds
        curl_setopt($curl, CURLOPT_URL, base_url('coreapi/sms/create'));
        curl_setopt($curl, CURLOPT_USERAGENT, "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.0.0 Safari/537.36");
        curl_setopt($curl, CURLOPT_POST, TRUE);
        curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($post_data));
        curl_setopt($curl, CURLOPT_HTTPHEADER, ['Content-Type:application/json']);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
        
        $response = curl_exec($curl);

    }

    public function add_transaction_out_demo() {
        if(!is_cli())
            return FALSE;
        $accounts_demo = ['*************','*************'];
       
        $sim = "**********";

        foreach($accounts_demo as $account_number) {
            // random reference_number
            $reference_number = rand(100000,999999) . '.' . date("dmy") . '.' . date("his");

          

            // calculate accumulated
            $transactionsModel = model(TransactionsModel::class);

            $last_transaction  = $transactionsModel->where(['account_number' => $account_number])->orderBy('id','desc')->get()->getRow();

            if(!is_object($last_transaction))
                continue;
            else
                $last_accumulated = $last_transaction->accumulated;

            if($last_accumulated)
            $amount_out = round($last_accumulated * 0.05);
                
            $accumulated = $last_accumulated - $amount_out;

            $from_demo = ['Cong ty ABC thanh toan cho doi tac Viettel', 'Thanh toan phi hang hoa ngay ' . date("d"), 'Thanh toan tien quang cao', 'Thanh toan chi phi Marketing','Thanh toan tien nguyen lieu', 'Thanh toan tien thue van phong', 'Thanh toan tien cho doi tac VNPT'];

            $transaction_content = $from_demo[array_rand($from_demo,1)];

            $body = "SD TK " . $account_number . " -" . number_format($amount_out) . 'VND luc ' . date("d-m-Y H:i:s", strtotime("14 seconds ago")) . ". SD " . number_format($accumulated) . "VND. Ref " . $reference_number . "." . $transaction_content;

            $post_data = ["from" => "Vietcombank", "body" => $body, "to" => $sim];
            
            // Start the request
            $curl = curl_init();

            curl_setopt($curl, CURLOPT_RETURNTRANSFER, TRUE);
            curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 5); 
            curl_setopt($curl, CURLOPT_TIMEOUT, 8); //timeout in seconds
            curl_setopt($curl, CURLOPT_URL, base_url('coreapi/sms/create'));
            curl_setopt($curl, CURLOPT_USERAGENT, "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.0.0 Safari/537.36");
            curl_setopt($curl, CURLOPT_POST, TRUE);
            curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($post_data));
            curl_setopt($curl, CURLOPT_HTTPHEADER, ['Content-Type:application/json']);
            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
            
            $response = curl_exec($curl);
        }
  
    }

    public function retry_telegram_queue() {
        if(!is_cli())
            return FALSE;

        $max_retries_count = 7; // begin with 0
        $max_retries_hours = 5; // hours

        helper('general');

        $slavableModel = slavable_model(NotificationTelegramQueueModel::class, 'Cron');
        
        while(1) {

            $notificationTelegramQueueModel = model(NotificationTelegramQueueModel::class);

            $latest_time_retry = date("Y-m-d H:i:s", strtotime("-" . $max_retries_hours . " hours"));
            
            $results = $slavableModel->where(['retries_count<=' => $max_retries_count, 'status' => 'SoftFailed', 'created_at>=' => $latest_time_retry])->orderBy('id','DESC')->limit(10)->get()->getResult();

            foreach($results as $queue) {
                if ($queue->retries_count >= $max_retries_count) {
                    $notificationTelegramQueueModel->set(["status" => "Failed"])->where(['id' => $queue->id])->update();
                } else {
                    helper('general');
                    $can_retry = can_retry_job($queue->retries_count, $queue->last_retry_time);

                    if($can_retry) {

                        $result = $notificationTelegramQueueModel->sendTelegramMessage($queue->message,$queue->chat_id, 'html', $queue->message_thread_id);

                        $queue_status = 'SoftFailed';
                        $queue_last_log = '';
                        $retries_count = $queue->retries_count + 1;

                        if(isset($result['response']))
                            $queue_last_log = NotificationTelegramQueueModel::summaryLastLogResponse($result['response']);

                        if(isset($result['success']) && $result['success'] == TRUE) {
                            if(isset($result['response'])) {

                                $api_response = json_decode($result['response']);

                                if($api_response) {
                                    if(is_object($api_response) && isset($api_response->ok) && $api_response->ok == true) {
                                        $queue_status = 'Success';
                                    }
                                }
                            }
                        }
        
                        $notificationTelegramQueueModel->set(['status' => $queue_status,'last_retry_log' => $queue_last_log,'retries_count' => $retries_count, 'last_retry_time' => date("Y-m-d H:i:s")])->where(['id' => $queue->id])->update();

                        if($queue_status == 'Success') {
                            $transactionsModel = model(TransactionsModel::class);
        
                            $transactionsModel->set('chat_push_message', 'chat_push_message+1', false)->where(['id' => $queue->transaction_id])->update();

                            $notificationTelegramModel = model(NotificationTelegramModel::class);
                            $counterModel = model(CounterModel::class);
                            $result = $notificationTelegramModel->where(['id' => $queue->notify_id])->get()->getRow();
                            if($result)
                                $counterModel->chat($result->company_id, FALSE, 'telegram');
                            }

                        sleep(2);

                    }
                }
           
            }

            sleep(2);
           
        }
        
    }


    public function retry_webhooks_queue() {
        if(!is_cli())
            return FALSE;

        $max_retries_count = 7; // begin with 0
        $max_retries_hours = 5; // hours

        while(1) {

            $webhooksQueueModel = model(WebhooksQueueModel::class);
            $webhooksModel = model(WebhooksModel::class);
            $transactionsModel = model(TransactionsModel::class);

            $latest_time_retry = date("Y-m-d H:i:s", strtotime("-" . $max_retries_hours . " hours"));
            
            $results = $webhooksQueueModel->where(['retries_count<=' => $max_retries_count, 'status' => 'SoftFailed', 'created_at>=' => $latest_time_retry])->orderBy('id','DESC')->limit(20)->get()->getResult();
            $cache = service('cache');
            $rabbitmq = new \App\Libraries\RabbitMQClient;
            $queuable = $rabbitmq->connect();

            foreach($results as $queue) {
                if ($cache->get('retry_webhook_queuing_' . $queue->id)) continue;

                if ($queue->retries_count >= $max_retries_count) {
                    $webhooksQueueModel->set(["status" => "Failed"])->where(['id' => $queue->id])->update();
                } else {
                    helper('general');
                    $can_retry = can_retry_job($queue->retries_count, $queue->last_retry_time);

                    if ($queuable && $can_retry) {
                        $msg = new AMQPMessage(
                            json_encode($queue),
                            array('delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT)
                        );

                        try {
                            $rabbitmq->queueDeclare('retry_webhook');
                            $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('retry_webhook', 'name'));
                            $cache->save('retry_webhook_queuing_' . $queue->id, 1, 3600);

                            continue;
                        } catch (\Exception $e) {
                            log_message('error', 'Retry webhook queue failed: ' . $e->getMessage());
                        }
                    }

                    if($can_retry) {
                        $hooks_details = $webhooksModel->where(['id' => $queue->webhook_id])->get()->getRow();
                        $transaction_details = $transactionsModel->where(['id' => $queue->transaction_id])->get()->getRow();

                        if(is_object($hooks_details) && is_object($transaction_details)) {
                            $result = $webhooksModel->doWebhook($hooks_details, $transaction_details, TRUE);

                            $queue_status = 'SoftFailed';
                            if(isset($result['connect_success']) && $result['connect_success'] == 1 && !is_retry_hook_with_conditions($hooks_details, $result)) {
                                $queue_status = 'Success';
                            }
                            $retries_count = $queue->retries_count + 1;
    
                        } else {
                            $retries_count = $queue->retries_count;
                            $queue_status = "Failed";
                        }
                     
                        $webhooksQueueModel->set(['status' => $queue_status,'retries_count' => $retries_count, 'last_retry_time' => date("Y-m-d H:i:s")])->where(['id' => $queue->id])->update();

                        sleep(2);

                    }
                }
           
            }

            sleep(2);
           
        }
        
    }

    public function alert_sms_parse_failed() {
        if(!is_cli())
            return FALSE;

        $transactionsModel = model(TransactionsModel::class);
        $smsModel = model(SmsModel::class);

        $notificationTelegramQueueModel = model(NotificationTelegramQueueModel::class);

        $results = $transactionsModel->where(['parser_status' => 'Failed', 'chat_push_message' => 0,'datecreated>' => date("Y-m-01 00:00:00"), 'gateway!=' => 'BO CONG AN', 'gateway!=' => 'NAPTHE VT',  'gateway!=' => 'NAPTHEVT',  'gateway!=' => 'VIETTEL KM'])->orderBy('id', 'asc')->get()->getResult();
        $chat_id = "-503646620";

       

        foreach($results as $result) {
            $sms_details = $smsModel->find($result->sms_id);
            if(is_object($sms_details))
                $to = $sms_details->to;
            else
                $to = "";
            //echo $result->body . "\n";
            $message = "Có tin nhắn
- Từ: " . $result->gateway . "

- Đến: " . $to . "

- Nội dung: " . $result->body . " 

- Lúc: " . $result->datecreated;

            preg_match('/(Myclip|THE NAP|vtmoney|Tu choi QC|cuoc goi nho|gia tri the nap|QC|The Visa|MSBPay_VA_DDV|Huy GD|xxx)/', $result->body, $matches, PREG_OFFSET_CAPTURE);
            preg_match('/(NAPTHEVT|BO CONG AN|BaoVietLife|PC ThaiBinh|VNTOPUP|VNPT Money|MTTQ|VIETTEL KM|NAPTHE VT|CSGT|PC|LDTBXH)/', $result->gateway, $matches2, PREG_OFFSET_CAPTURE);

            if(!$matches && !$matches2) {
                $send_result = $notificationTelegramQueueModel->sendTelegramMessage($message,$chat_id, 'html');
                if(isset($send_result['success']) && $send_result['success'] == TRUE)
                    $transactionsModel->set('chat_push_message', 'chat_push_message+1', false)->where(['id' => $result->id])->update();
                else
                    sleep(2);
            } else {
                echo "Skip: " . $result->body . "\n";
            }

            //$send_result = $notificationTelegramQueueModel->sendTelegramMessage($message,$chat_id, 'html');
 
        }

    }
 

    public function alert_new_company_reg() {

        if(!is_cli())
            return FALSE;

        $chat_id = "-*********";

        $companyModel = model(CompanyModel::class);
        $companyUserModel = model(CompanyUserModel::class);
        $userModel = model(UserModel::class);

        $notificationTelegramQueueModel = model(NotificationTelegramQueueModel::class);

        $results = $companyModel->where(['lead_alert' => 0])->orderBy('id','ASC')->get()->getResult();
        
        foreach($results as $company) {
            // find user

            $result = $companyUserModel->where('company_id', $company->id)->orderBy('user_id','ASC')->get()->getRow();

            if(!is_object($result)) {
                $companyModel->set(['lead_alert' => 9])->where('id', $company->id)->update();
                continue;
            }

            $user = $userModel->find($result->user_id);

            if(!is_object($user)) {
                $companyModel->set(['lead_alert' => 8])->where('id', $company->id)->update();
                continue;
            }
            
            echo "\n";
            $message = "Có công ty mới 
- Tên công ty: " . esc($company->full_name ). " (" . esc($company->short_name) . ")
- Người đăng ký:
+ Họ và tên: " . esc($user->lastname) . " " . esc($user->firstname) . "
+ SĐT: " . esc($user->phonenumber) . "
+ Email: " . esc($user->email) . "
- Lúc: " . esc($company->created_at);
            $send_result = $notificationTelegramQueueModel->sendTelegramMessage($message,$chat_id, 'html');
            if(isset($send_result['success']) && $send_result['success'] == TRUE)
                $companyModel->set(['lead_alert' => 1])->where('id', $company->id)->update();
            else
                sleep(2);

            sleep(1);
        }
    }

    public function notification_lark_messenger_queues() {
        if(!is_cli())
            return FALSE;
            
        helper('general');

        $slavableModel = slavable_model(NotificationLarkMessengerQueueModel::class, 'Cron');

        while(1) {

            $notificationLarkMessengerQueueModel = model(NotificationLarkMessengerQueueModel::class);
            $notificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);

            //$result = $notificationLarkMessengerModel->checkAndAddQueue(76731);

            //var_dump($result);
            //die();

            $cache = service('cache');
            $queues = $slavableModel->where(['status' => 'Pending'])->get()->getResult();

            foreach($queues as $queue) {
                if ($cache->get('lark_messenger_queuing_' . $queue->id)) continue;
                
                $result = $notificationLarkMessengerQueueModel->sendMessage($queue->message,$queue->bot_webhook_url, 'text');

                $queue_status = 'SoftFailed';
                $queue_last_log = '';

                if(isset($result['response']))
                    $queue_last_log = $result['response'];

                if(isset($result['success']) && $result['success'] == TRUE) {
                    if(isset($result['response'])) {

                        $api_response = json_decode($result['response']);

                        if($api_response) {
                            if(is_object($api_response) && isset($api_response->StatusMessage) && $api_response->StatusMessage == "success") {
                                $queue_status = 'Success';
                            }
                        }
                    }
                }
  
                $notificationLarkMessengerQueueModel->set(['status' => $queue_status,'last_log' => $queue_last_log])->where(['id' => $queue->id])->update();

                if($queue_status == 'Success') {
                    $transactionsModel = model(TransactionsModel::class);

                    $transactionsModel->set('chat_push_message', 'chat_push_message+1', false)->where(['id' => $queue->transaction_id])->update();

                    // counter
                    $notificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);
                    $counterModel = model(CounterModel::class);
                    $result = $notificationLarkMessengerModel->where(['id' => $queue->notify_id])->get()->getRow();
                    if($result)
                        $counterModel->chat($result->company_id, FALSE, 'lark_messenger');
                }
                
                $cache->save('lark_messenger_queuing_' . $queue->id, 1, 3600 * 24);
            }

            sleep(1);
        }
    }

    public function retry_lark_messenger_queue() {
        if(!is_cli())
            return FALSE;

        $max_retries_count = 7; // begin with 0
        $max_retries_hours = 5; // hours

        helper('general');

        $slavableModel = slavable_model(NotificationLarkMessengerQueueModel::class, 'Cron');
        
        while(1) {

            $notificationLarkMessengerQueueModel = model(NotificationLarkMessengerQueueModel::class);

            $latest_time_retry = date("Y-m-d H:i:s", strtotime("-" . $max_retries_hours . " hours"));
            
            $results = $slavableModel->where(['retries_count<=' => $max_retries_count, 'status' => 'SoftFailed', 'created_at>=' => $latest_time_retry])->orderBy('id','DESC')->limit(10)->get()->getResult();

            foreach($results as $queue) {
                if ($queue->retries_count >= $max_retries_count) {
                    $notificationLarkMessengerQueueModel->set(["status" => "Failed"])->where(['id' => $queue->id])->update();
                } else {
                    helper('general');
                    $can_retry = can_retry_job($queue->retries_count, $queue->last_retry_time);

                    if($can_retry) {

                        $result = $notificationLarkMessengerQueueModel->sendMessage($queue->message,$queue->bot_webhook_url, 'text');

                        $queue_status = 'SoftFailed';
                        $queue_last_log = '';
                        $retries_count = $queue->retries_count + 1;

                        if(isset($result['response']))
                            $queue_last_log = $result['response'];

                        if(isset($result['success']) && $result['success'] == TRUE) {
                            if(isset($result['response'])) {

                                $api_response = json_decode($result['response']);

                                if($api_response) {
                                    if(is_object($api_response) && isset($api_response->StatusMessage) && $api_response->StatusMessage == "success") {
                                        $queue_status = 'Success';
                                    }
                                }
                            }
                        }
        
                        $notificationLarkMessengerQueueModel->set(['status' => $queue_status,'last_retry_log' => $queue_last_log,'retries_count' => $retries_count, 'last_retry_time' => date("Y-m-d H:i:s")])->where(['id' => $queue->id])->update();

                        if($queue_status == 'Success') {
                            $transactionsModel = model(TransactionsModel::class);
        
                            $transactionsModel->set('chat_push_message', 'chat_push_message+1', false)->where(['id' => $queue->transaction_id])->update();

                            // counter
                            $notificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);
                            $counterModel = model(CounterModel::class);
                            $result = $notificationLarkMessengerModel->where(['id' => $queue->notify_id])->get()->getRow();
                            if($result)
                                $counterModel->chat($result->company_id, FALSE, 'lark_messenger');
                            }

                        sleep(2);

                    }
                }
           
            }

            sleep(2);
           
        }
        
    }
    
    public function retry_viber_queue() 
    {
        if (!is_cli()) return false;
        
        $max_retries_count = 7; // begin with 0
        $max_retries_hours = 5; // hours
        
        helper('general');
        
        $slavableModel = slavable_model(NotificationViberQueueModel::class, 'Cron');
        
        while (1) {
            $notificationViberQueueModel = model(NotificationViberQueueModel::class);
    
            $latestTimeRetry = date("Y-m-d H:i:s", strtotime("-" . $max_retries_hours . " hours"));
            
            $results = $slavableModel->where([
                'retries_count<=' => $max_retries_count, 
                'status' => 'SoftFailed', 
                'created_at>=' => $latestTimeRetry
            ])->orderBy('id', 'desc')->limit(10)->get()->getResult();
    
            foreach($results as $queue) {
                if ($queue->retries_count >= $max_retries_count) {
                    $notificationViberQueueModel->set(['status' => 'Failed'])->where(['id' => $queue->id])->update();
                    continue;
                }
                
                if (!can_retry_job($queue->retries_count, $queue->last_retry_time)) continue;
                
                (new \App\Features\Viber\ViberFeature)->sendNotificationByQueue((array) $queue);
            }
            
            sleep(2);
        }
    }
 

    public function re_counter_transaction() {
        die();
        $companyModel = model(CompanyModel::class);
        $counterModel = model(CounterModel::class);
        $transactionsModel = model(TransactionsModel::class);
        $bankAccountModel = model(BankAccountModel::class);

        //$bank_accounts = $bankaccountModel->orderBy('id','DESC')->findAll();
        //2023-01-19

        /*$startTime = strtotime( "2023-01-18" );
        $endTime   = strtotime( "2023-06-17" );*/
        $startTime = strtotime( "2023-06-17" );
        $endTime   = strtotime( "2023-06-19" );
        $i = 1;
        do  {
            $newTime = strtotime('+'.$i++.' days',$startTime); 
            $date = date("Y-m-d",$newTime);

            $bank_accounts = $bankAccountModel->where(['created_at<=' => $date])->orderBy('id','ASC')->findAll();

            foreach($bank_accounts as $bank_account) {
                $count_trans_in = $transactionsModel->where(['account_number' => $bank_account->account_number, 'parser_status' => 'Success', 'transaction_date>=' => $date . " 00:00:00", 'transaction_date<=' => $date . " 23:59:59", "amount_in>" => 0])->countAllResults();
                $count_trans_out = $transactionsModel->where(['account_number' => $bank_account->account_number, 'parser_status' => 'Success', 'transaction_date>=' => $date . " 00:00:00", 'transaction_date<=' => $date . " 23:59:59", "amount_out>" => 0])->countAllResults();
                
                echo $date . " - " . $bank_account->account_number . " => " . $count_trans_in  . "<br>";
                
                if($count_trans_in>0 || $count_trans_out > 0) {
                    $result = $counterModel->where(['date' => $date, 'company_id' => $bank_account->company_id])->get()->getRow();

                    if(is_object($result)) {
                        $total_trans_in = $result->transaction_in + $count_trans_in;
                        $total_trans_out = $result->transaction_out + $count_trans_out;
                        $total_trans = $total_trans_in + $total_trans_out;
                        $counterModel->set(['transaction' => $total_trans, 'transaction_in' => $total_trans_in,'transaction_out' => $total_trans_out ])->where(['date' => $date, 'company_id' => $bank_account->company_id])->update();
                    } else {
                        $counterModel->insert(['company_id' => $bank_account->company_id, 'date' => $date, 'transaction_in' => $count_trans_in, 'transaction_out' => $count_trans_out, 'transaction' => $count_trans_out + $count_trans_in]);
                    }
                }
                
               
            }
            //if($i > 10)
            //    die();

        } while ($newTime < $endTime);

        /*
        foreach($bank_accounts as $bank_account) {
            $count_all_trans = $transactionsModel->where(['account_number' => $bank_account->account_number, 'parser_status' => 'Success'])->countAllResults();
        } */
    }


    public function re_counter_webhook() {
        die();
        $companyModel = model(CompanyModel::class);
        $counterModel = model(CounterModel::class);
        $transactionsModel = model(TransactionsModel::class);
        $bankAccountModel = model(BankAccountModel::class);
        $webhooksQueueModel = model(WebhooksQueueModel::class);
        $webhooksModel = model(WebhooksModel::class);
       
        //$bank_accounts = $bankaccountModel->orderBy('id','DESC')->findAll();
        //2023-01-19

        $startTime = strtotime( "2023-01-18" );
        $endTime   = strtotime( "2023-06-20" );
       
        $i = 1;
        do  {
            $newTime = strtotime('+'.$i++.' days',$startTime); 
            $date = date("Y-m-d",$newTime);

            $bank_accounts = $bankAccountModel->where(['created_at<=' => $date])->orderBy('id','ASC')->findAll();
            
            foreach($bank_accounts as $bank_account) {
               
                $webhooks_counter = $transactionsModel->select("sum(webhooks_success) as `sum_hook_success`, sum(webhooks_failed) as `sum_hook_failed`")->where(['account_number' => $bank_account->account_number, 'parser_status' => 'Success', 'transaction_date>=' => $date . " 00:00:00", 'transaction_date<=' => $date . " 23:59:59"])->get()->getRow();

                //$webhooks_failed = $transactionsModel->select("sum(webhooks_failed) as `sum_hook_failed`")->where(['account_number' => $bank_account->account_number, 'parser_status' => 'Success', 'transaction_date>=' => $date . " 00:00:00", 'transaction_date<=' => $date . " 23:59:59"])->get()->getRow();
 
                if($webhooks_counter->sum_hook_success>0 || $webhooks_counter->sum_hook_failed > 0) {
                    $counter = $counterModel->where(['date' => $date, 'company_id' => $bank_account->company_id])->get()->getRow();

                    if(is_object($counter)) {

                        $sum_webhook = $counter->webhook +  $webhooks_counter->sum_hook_success + $webhooks_counter->sum_hook_failed;

                        $counterModel->set(['webhook' => $sum_webhook, 'webhook_success' => $webhooks_counter->sum_hook_success, 'webhook_failed' => $webhooks_counter->sum_hook_failed])->where(['date' => $date, 'company_id' => $bank_account->company_id])->update();

                      //  echo $date . " - " . $bank_account->account_number . " => " . $sum_webhook . " | " . $sum_hook_success  . "<br>";

                    } else {
                        echo "Alert: Counter not have record but webhook data given! " . $date. " " . $bank_account->company_id . "<br> \n";
                    }
                }
                
               
            }
           // if($i > 20)
            //   die();

        } while ($newTime < $endTime);

       
    }


    public function re_counter_chat() {
       die();
        $companyModel = model(CompanyModel::class);
        $counterModel = model(CounterModel::class);
        $transactionsModel = model(TransactionsModel::class);
        $bankAccountModel = model(BankAccountModel::class);
        $webhooksQueueModel = model(WebhooksQueueModel::class);
        $webhooksModel = model(WebhooksModel::class);
        $notificationTelegramQueueModel = model(NotificationTelegramQueueModel::class);
        $notificationLarkMessengerQueueModel = model(NotificationLarkMessengerQueueModel::class);

        //$bank_accounts = $bankaccountModel->orderBy('id','DESC')->findAll();
        //2023-01-19

        $startTime = strtotime( "2023-01-18" );
        $endTime   = strtotime( "2023-06-21" );
       
        $i = 1;

        do  {
            $newTime = strtotime('+'.$i++.' days',$startTime); 
            $date = date("Y-m-d",$newTime);

            $bank_accounts = $bankAccountModel->where(['created_at<=' => $date])->orderBy('id','ASC')->findAll();
            
            foreach($bank_accounts as $bank_account) {
               
                $result1 = $transactionsModel->select("sum(chat_push_message) as `chat_push`")->where(['account_number' => $bank_account->account_number, 'parser_status' => 'Success', 'transaction_date>=' => $date . " 00:00:00", 'transaction_date<=' => $date . " 23:59:59"])->get()->getRow();
                                
                //echo $date . " - " . $bank_account->account_number . " => " . $result1->chat_push . "<br>";
                
                if($result1->chat_push>0 ) {
                    $counter = $counterModel->where(['date' => $date, 'company_id' => $bank_account->company_id])->get()->getRow();

                    if(is_object($counter)) {

                        $sum_chat_push = $counter->chat +  $result1->chat_push;

                        $counterModel->set(['chat' => $sum_chat_push])->where(['date' => $date, 'company_id' => $bank_account->company_id])->update();

                        echo $date . " - " . $bank_account->account_number . " => " . $sum_chat_push . "<br>";

                    } else {
                        echo "Alert: Counter not have record but webhook data given! " . $date. " " . $bank_account->company_id . "<br> \n";
                    }
                }
                
               
            }
           // if($i > 20)
            //   die();

        } while ($newTime < $endTime);

       
    }

    public function delete_transaction_expired_hourly() {
        if(!is_cli())
            return FALSE;

        helper(['general']);

        $transactionsModel = slavable_model(TransactionsModel::class, 'Cron');
        $companyModel = slavable_model(CompanyModel::class, 'Cron');
        $configurationModel = slavable_model(ConfigurationModel::class, 'Cron');
        $webhooksLogModel = slavable_model(WebhooksLogModel::class, 'Cron');
        $notificationTelegramQueueModel = slavable_model(NotificationTelegramQueueModel::class, 'Cron');
        $notificationLarkMessengerQueueModel = slavable_model(NotificationLarkMessengerQueueModel::class, 'Cron');
        $smsModel = slavable_model(SmsModel::class, 'Cron');
        $bankAccountCashflowModel = slavable_model(BankAccountCashflowModel::class, 'Cron');
        $bankSubAccountCashflowModel = slavable_model(BankSubAccountCashflowModel::class, 'Cron');

        $time_to_string = [
            '1Hour' => "1 hour ago",
            '8Hours' => "8 hours ago",
            '1Day' => "1 day ago",
            '3Days' => "3 days ago",
            '7Days' => "7 days ago",
            '1Month' => "1 month ago",
            '3Months' => "3 months ago",
            '6Months' => "6 months ago",
            '1Years' => "1 year ago",
            '2Years' => "2 years ago",
            '3Years' => "3 years ago",
        ];

        $results = $configurationModel->where(['setting' => 'DataStorageTime'])->like('value','Hour')->orderBy('company_id','ASC')->get()->getResult();

        foreach($results as $config) {
            if(isset($time_to_string[$config->value])) {
                $expired_point = date("Y-m-d H:i:s", strtotime($time_to_string[$config->value]));
                echo $config->company_id . " " . $config->value . " " . $expired_point;
                
                // tb_autopay_webhooks_log (company_id),

                $webhook_logs_expired = $webhooksLogModel->where(['created_at<=' => $expired_point, 'deleted_at' => NULL, 'company_id' => $config->company_id])->countallResults();

                if($webhook_logs_expired > 0 ) {
                    model(WebhooksLogModel::class)->where(['created_at<=' => $expired_point, 'deleted_at' => NULL, 'company_id' => $config->company_id])->delete();

                    add_system_log(['company_id' => $config->company_id, 'data_type' => 'soft_delete_webhook_log_expired_hourly', 'description' => "Soft delete " . $webhook_logs_expired . " webhook logs. Expired point is " . $expired_point,'level' => 'Info', 'by' => 'Cron']);
                }
               

                // tb_autopay_notification_telegram_queue (transaction_id or notify_id)
                $telegram_logs_expired = $notificationTelegramQueueModel->select("tb_autopay_notification_telegram_queue.id")->join("tb_autopay_notification_telegram","tb_autopay_notification_telegram.id=tb_autopay_notification_telegram_queue.notify_id")->where(['tb_autopay_notification_telegram_queue.created_at<=' => $expired_point, 'tb_autopay_notification_telegram_queue.deleted_at' => NULL, 'tb_autopay_notification_telegram.company_id' => $config->company_id, 'tb_autopay_notification_telegram_queue.status!=' =>'Pending', 'tb_autopay_notification_telegram_queue.status!=' =>'Sending', 'tb_autopay_notification_telegram_queue.status!=' =>'SoftFailed'])->get()->getResult();


                if(count($telegram_logs_expired) > 0) {
                    foreach($telegram_logs_expired as $log) {
                        model(NotificationTelegramQueueModel::class)->delete($log->id);
                    }

                    add_system_log(['company_id' => $config->company_id, 'data_type' => 'soft_delete_telegram_log_expired_hourly', 'description' => "Soft delete " . count($telegram_logs_expired) . " telegram logs. Expired point is " . $expired_point,'level' => 'Info', 'by' => 'Cron']);

                }

                // tb_autopay_notification_larkmessenger_queue (transaction_id or notify_id)
                $lark_messenger_logs_expired = $notificationLarkMessengerQueueModel->select("tb_autopay_notification_larkmessenger_queue.id")->join("tb_autopay_notification_larkmessenger","tb_autopay_notification_larkmessenger.id=tb_autopay_notification_larkmessenger_queue.notify_id")->where(['tb_autopay_notification_larkmessenger_queue.created_at<=' => $expired_point, 'tb_autopay_notification_larkmessenger_queue.deleted_at' => NULL, 'tb_autopay_notification_larkmessenger.company_id' => $config->company_id, 'tb_autopay_notification_larkmessenger_queue.status!=' =>'Pending', 'tb_autopay_notification_larkmessenger_queue.status!=' =>'Sending', 'tb_autopay_notification_larkmessenger_queue.status!=' =>'SoftFailed'])->get()->getResult();

                if(count($lark_messenger_logs_expired) > 0) {
                    foreach($lark_messenger_logs_expired as $log) {
                        model(NotificationLarkMessengerQueueModel::class)->delete($log->id);
                    }

                    add_system_log(['company_id' => $config->company_id, 'data_type' => 'soft_delete_lark_messenger_log_expired_hourly', 'description' => "Soft delete " . count($lark_messenger_logs_expired) . " lark messenger logs. Expired point is " . $expired_point,'level' => 'Info', 'by' => 'Cron']);

                }
                // tb_autopay_sms (id)

                // tb_autopay_sms_parsed account_number
                $transactions = $transactionsModel->select("tb_autopay_sms_parsed.id, tb_autopay_sms_parsed.sms_id, tb_autopay_sms_parsed.source")
                    ->join("tb_autopay_bank_account","tb_autopay_bank_account.id=tb_autopay_sms_parsed.bank_account_id")
                    ->where(["tb_autopay_bank_account.company_id" => $config->company_id,'tb_autopay_sms_parsed.deleted_at' => NULL,'tb_autopay_sms_parsed.transaction_date<=' => $expired_point])->get()->getResult();
                
                if(count($transactions) > 0) {
                    $sms_count = 0;
                    foreach($transactions as $trans) {
                        if($trans->source == "SMS" && $trans->sms_id>0) {
                            model(SmsModel::class)->delete($trans->sms_id);
                            $sms_count = $sms_count + 1;
                        }
                        model(TransactionsModel::class)->delete($trans->id);
    
                    }
    
                    add_system_log(['company_id' => $config->company_id, 'data_type' => 'soft_delete_sms_log_expired_hourly', 'description' => "Soft delete " . $sms_count . " sms. Expired point is " . $expired_point,'level' => 'Info', 'by' => 'Cron']);
    
                    add_system_log(['company_id' => $config->company_id, 'data_type' => 'soft_delete_transaction_expired_hourly', 'description' => "Soft delete " . count($transactions) . " transactions. Expired point is " . $expired_point,'level' => 'Info', 'by' => 'Cron']);    
                }

                // Delete cashflow data
                model(BankAccountCashflowModel::class)->where(['company_id' => $config->company_id, 'created_at<=' => $expired_point])->delete();
                model(BankSubAccountCashflowModel::class)->where(['company_id' => $config->company_id, 'created_at<=' => $expired_point])->delete();

                // Delete VA data
                $this->deleteVAOrders($config->company_id, $expired_point);
            }
        }
    }

    public function delete_transaction_expired_daily() {
        if(!is_cli())
            return FALSE;

        helper(['general']);

        $transactionsModel = slavable_model(TransactionsModel::class, 'Cron');
        $companyModel = slavable_model(CompanyModel::class, 'Cron');
        $configurationModel = slavable_model(ConfigurationModel::class, 'Cron');
        $webhooksLogModel = slavable_model(WebhooksLogModel::class, 'Cron');
        $notificationTelegramQueueModel = slavable_model(NotificationTelegramQueueModel::class, 'Cron');
        $notificationLarkMessengerQueueModel = slavable_model(NotificationLarkMessengerQueueModel::class, 'Cron');
        $smsModel = slavable_model(SmsModel::class, 'Cron');
        $bankAccountCashflowModel = slavable_model(BankAccountCashflowModel::class, 'Cron');
        $bankSubAccountCashflowModel = slavable_model(BankSubAccountCashflowModel::class, 'Cron');


        $time_to_string = [
            '1Hour' => "1 hour ago",
            '8Hours' => "8 hours ago",
            '1Day' => "1 day ago",
            '3Days' => "3 days ago",
            '7Days' => "7 days ago",
            '1Month' => "1 month ago",
            '3Months' => "3 months ago",
            '6Months' => "6 months ago",
            '1Years' => "1 year ago",
            '2Years' => "2 years ago",
            '3Years' => "3 years ago",
        ];

        $results = $configurationModel->where(['setting' => 'DataStorageTime'])->like('value','Day')->orLike('value','Month')->orLike('value','Year')->orderBy('company_id','ASC')->get()->getResult();

        foreach($results as $config) {
            if(isset($time_to_string[$config->value])) {
                $expired_point = date("Y-m-d H:i:s", strtotime($time_to_string[$config->value]));
               // echo $config->company_id . " " . $config->value . " " . $expired_point . "<br>";

                
                // tb_autopay_webhooks_log (company_id),
                $webhook_logs_expired = $webhooksLogModel->where(['created_at<=' => $expired_point, 'deleted_at' => NULL, 'company_id' => $config->company_id])->countallResults();

                if($webhook_logs_expired > 0 ) {
                    model(WebhooksLogModel::class)->where(['created_at<=' => $expired_point, 'deleted_at' => NULL, 'company_id' => $config->company_id])->delete();

                    add_system_log(['company_id' => $config->company_id, 'data_type' => 'soft_delete_webhook_log_expired_hourly', 'description' => "Soft delete " . $webhook_logs_expired . " webhook logs. Expired point is " . $expired_point,'level' => 'Info', 'by' => 'Cron']);
                }
               

                // tb_autopay_notification_telegram_queue (transaction_id or notify_id)
                $telegram_logs_expired = $notificationTelegramQueueModel->select("tb_autopay_notification_telegram_queue.id")->join("tb_autopay_notification_telegram","tb_autopay_notification_telegram.id=tb_autopay_notification_telegram_queue.notify_id")->where(['tb_autopay_notification_telegram_queue.created_at<=' => $expired_point, 'tb_autopay_notification_telegram_queue.deleted_at' => NULL, 'tb_autopay_notification_telegram.company_id' => $config->company_id, 'tb_autopay_notification_telegram_queue.status!=' =>'Pending', 'tb_autopay_notification_telegram_queue.status!=' =>'Sending', 'tb_autopay_notification_telegram_queue.status!=' =>'SoftFailed'])->get()->getResult();


                if(count($telegram_logs_expired) > 0) {
                    foreach($telegram_logs_expired as $log) {
                        model(NotificationTelegramQueueModel::class)->delete($log->id);
                    }

                    add_system_log(['company_id' => $config->company_id, 'data_type' => 'soft_delete_telegram_log_expired_hourly', 'description' => "Soft delete " . count($telegram_logs_expired) . " telegram logs. Expired point is " . $expired_point,'level' => 'Info', 'by' => 'Cron']);

                }

                // tb_autopay_notification_larkmessenger_queue (transaction_id or notify_id)
                $lark_messenger_logs_expired = $notificationLarkMessengerQueueModel->select("tb_autopay_notification_larkmessenger_queue.id")->join("tb_autopay_notification_larkmessenger","tb_autopay_notification_larkmessenger.id=tb_autopay_notification_larkmessenger_queue.notify_id")->where(['tb_autopay_notification_larkmessenger_queue.created_at<=' => $expired_point, 'tb_autopay_notification_larkmessenger_queue.deleted_at' => NULL, 'tb_autopay_notification_larkmessenger.company_id' => $config->company_id, 'tb_autopay_notification_larkmessenger_queue.status!=' =>'Pending', 'tb_autopay_notification_larkmessenger_queue.status!=' =>'Sending', 'tb_autopay_notification_larkmessenger_queue.status!=' =>'SoftFailed'])->get()->getResult();

                if(count($lark_messenger_logs_expired) > 0) {
                    foreach($lark_messenger_logs_expired as $log) {
                       model(NotificationLarkMessengerQueueModel::class)->delete($log->id);
                    }

                    add_system_log(['company_id' => $config->company_id, 'data_type' => 'soft_delete_lark_messenger_log_expired_hourly', 'description' => "Soft delete " . count($lark_messenger_logs_expired) . " lark messenger logs. Expired point is " . $expired_point,'level' => 'Info', 'by' => 'Cron']);

                }
                // tb_autopay_sms (id)

                // tb_autopay_sms_parsed account_number
                $transactions = $transactionsModel->select("tb_autopay_sms_parsed.id, tb_autopay_sms_parsed.sms_id, tb_autopay_sms_parsed.source")
                    ->join("tb_autopay_bank_account","tb_autopay_bank_account.id=tb_autopay_sms_parsed.bank_account_id")
                    ->where(["tb_autopay_bank_account.company_id" => $config->company_id,'tb_autopay_sms_parsed.deleted_at' => NULL,'tb_autopay_sms_parsed.transaction_date<=' => $expired_point])->get()->getResult();

                if(count($transactions) > 0) {
                    $sms_count = 0;
                    foreach($transactions as $trans) {
                        if($trans->source == "SMS" && $trans->sms_id>0) {
                            model(SmsModel::class)->delete($trans->sms_id);
                            $sms_count = $sms_count + 1;
                        }
                        model(TransactionsModel::class)->delete($trans->id);
    
                    }
    
                    add_system_log(['company_id' => $config->company_id, 'data_type' => 'soft_delete_sms_log_expired_hourly', 'description' => "Soft delete " . $sms_count . " sms. Expired point is " . $expired_point,'level' => 'Info', 'by' => 'Cron']);
    
                    add_system_log(['company_id' => $config->company_id, 'data_type' => 'soft_delete_transaction_expired_hourly', 'description' => "Soft delete " . count($transactions) . " transactions. Expired point is " . $expired_point,'level' => 'Info', 'by' => 'Cron']);
                    
                }

                // Delete cashflow data
                model(BankAccountCashflowModel::class)->where(['company_id' => $config->company_id, 'date<' => date("Y-m-d", strtotime($time_to_string[$config->value]))])->delete();
                model(BankSubAccountCashflowModel::class)->where(['company_id' => $config->company_id, 'date<' => date("Y-m-d", strtotime($time_to_string[$config->value]))])->delete();

                // Delete VA data
                $this->deleteVAOrders($config->company_id, $expired_point);
            }
                
        }
       
    }

    public function form_alert()
    {
        if(!is_cli()) {
            return false;
        }

        $formModel = model(FormModel::class);
        $notificationTelegramQueueModel = model(NotificationTelegramQueueModel::class);

        $results = $formModel->where(['is_notify' => 0])->orderBy("id","DESC")->get()->getResult();

        $chat_id = "-*********";

        foreach($results as $result) {
           // var_dump($result);
         $message = $result->title . "
" . $result->body . " 
- Lúc: " . $result->created_at; 
            $send_result = $notificationTelegramQueueModel->sendTelegramMessage($message,$chat_id, 'html');
            $formModel->set(['is_notify' => 1])->where(['id' => $result->id])->update();
            
        }
    
    }

    

    public function retry_trans_mb() {
      //  if(!is_cli())
       //     return FALSE;
        die();
        $smsModel = model(SmsModel::class);
        $smsParserModel = model(SmsParserModel::class);
        $simModel = model(SimModel::class);

        $results = $smsParserModel->where(['gateway' => 'MBBANK', 'datecreated>=' => '2023-09-22 11:16:00', 'parser_status' =>'Failed','source' => 'SMS'])->orderBy('id','ASC')->get()->getResult();

        
        foreach($results as $result) {
            //var_dump($result);
            $sim_details = $simModel->select("tb_autopay_sim.sim_phonenumber")->join("tb_autopay_sms","tb_autopay_sms.to=tb_autopay_sim.sim_phonenumber")->where(['tb_autopay_sms.id'=> $result->sms_id])->get()->getRow();

            if(!is_object($sim_details))
                return FALSE;

            $sim = $sim_details->sim_phonenumber;

            
            $post_data = ["from" => $result->gateway, "body" => $result->body, "to" => $sim];
          
            

            // Start the request
            $curl = curl_init();

            curl_setopt($curl, CURLOPT_RETURNTRANSFER, TRUE);
            curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 5); 
            curl_setopt($curl, CURLOPT_TIMEOUT, 8); //timeout in seconds
            curl_setopt($curl, CURLOPT_URL, base_url('coreapi/sms/create'));
            curl_setopt($curl, CURLOPT_USERAGENT, "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.0.0 Safari/537.36");
            curl_setopt($curl, CURLOPT_POST, TRUE);
            curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($post_data));
            curl_setopt($curl, CURLOPT_HTTPHEADER, ['Content-Type:application/json']);
            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
            
            $response = curl_exec($curl); 
            var_dump($response); 

        }
        /*
        $post_data = ["from" => "Vietcombank", "body" => $body, "to" => $sim];

        

        
        // Start the request
        $curl = curl_init();

        curl_setopt($curl, CURLOPT_RETURNTRANSFER, TRUE);
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 5); 
        curl_setopt($curl, CURLOPT_TIMEOUT, 8); //timeout in seconds
        curl_setopt($curl, CURLOPT_URL, base_url('coreapi/sms/create'));
        curl_setopt($curl, CURLOPT_USERAGENT, "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.0.0 Safari/537.36");
        curl_setopt($curl, CURLOPT_POST, TRUE);
        curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($post_data));
        curl_setopt($curl, CURLOPT_HTTPHEADER, ['Content-Type:application/json']);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
        
        $response = curl_exec($curl); */

    }
 
    public function test_msb_api() {
        die();
        $msbNotificationModel = model(MsbNotificationModel::class);
        $results = $msbNotificationModel->where(['vaName!=' => 'DONG HO HAI TRIEU'])->get()->getResult();

        //$access_code = "DhHt@1023";
        $access_code = "DDV@0823";

        foreach($results as $result) {
            $str = $access_code . $result->tranSeq  . $result->tranDate . $result->vaNumber . $result->tranAmount . $result->fromAccountNumber. $result->toAccountNumber;
        
            $input_signature = hash('sha256', $str);
            if($input_signature != $result->signature) {
                echo $result->id . " Invalid singature <br>";
            }  
                

        }
    }

    public function clean_delete_at_records() {
        if(!is_cli()) {
            return false;
        }
        $smsParserModel = model(SmsParserModel::class);
        $smsModel = model(SmsModel::class);
        $webhooksLogModel = model(WebhooksLogModel::class);
        $webhooksQueueModel = model(WebhooksQueueModel::class);
        $notificationTelegramQueueModel = model(NotificationTelegramQueueModel::class);
        $notificationLarkMessengerQueueModel = model(NotificationLarkMessengerQueueModel::class);

        $smsParserModel->purgeDeleted();
        $smsModel->purgeDeleted();
        $webhooksLogModel->purgeDeleted();
        $webhooksQueueModel->purgeDeleted();
        $notificationTelegramQueueModel->purgeDeleted();
        $notificationLarkMessengerQueueModel->purgeDeleted();
        
    }

    public function clean_unessential_records() {
        if(!is_cli()) {
            return false;
        }
        $smsModel = model(SmsModel::class);
        $msbNotificationModel = model(MsbNotificationModel::class);
        $lgTokenModel = model(LgTokenModel::class);
        $klbNotificationModel = model(KlbNotificationModel::class);
        $klbNotificationRawModel = model(KlbNotificationRawModel::class);
        $ocbNotificationModel = model(OcbNotificationModel::class);
        $ocbNotificationRawModel = model(OcbNotificationRawModel::class);
        $mbbNotificationModel = model(MbbNotificationModel::class);
        $mbbNotificationRawModel = model(MbbNotificationRawModel::class);
        $mbbMmsNotificationModel = model(MbbMmsNotificationModel::class);
        $bidvNotificationModel = model(BidvNotificationModel::class);
        $bidvNotificationRawModel = model(BidvNotificationRawModel::class);
        $vietinbankNotificationRawModel = model(VietinbankNotificationRawModel::class);
        $vietinbankNotificationModel = model(VietinbankNotificationModel::class);
        $acbNotificationRawModel = model(AcbNotificationRawModel::class);
        $acbNotificationModel = model(AcbNotificationModel::class);
        $tpbankNotificationRawModel = model(TpbankNotificationRawModel::class);
        $tpbankNotificationModel = model(TpbankNotificationModel::class);
        $vpbankNotificationRawModel = model(VpbankNotificationRawModel::class);
        $vpbankNotificationModel = model(VpbankNotificationModel::class);
        $smsParserModel = model(SmsParserModel::class);

        $sms_delete_point = date("Y-m-d H:i:s", strtotime('-2 days'));
        $msb_delete_point = date("Y-m-d H:i:s", strtotime('-2 days'));
        $klb_delete_point = date("Y-m-d H:i:s", strtotime('-2 days'));
        $ocb_delete_point = date("Y-m-d H:i:s", strtotime('-2 days'));
        $mbb_delete_point = date("Y-m-d H:i:s", strtotime('-2 days'));
        $bidv_delete_point = date("Y-m-d H:i:s", strtotime('-2 days'));
        $vpbank_delete_point = date("Y-m-d H:i:s", strtotime('-2 days'));
        $vietinbank_delete_point = date("Y-m-d H:i:s", strtotime('-2 days'));
        $acb_delete_point = date("Y-m-d H:i:s", strtotime('-2 days'));
        $tpbank_delete_point = date("Y-m-d H:i:s", strtotime('-2 days'));

        $smsModel->where(['created_at<=' => $sms_delete_point])->delete();
        $msbNotificationModel->where(['created_at<=' => $msb_delete_point])->delete();
        $klbNotificationModel->where(['created_at<=' => $klb_delete_point])->delete();
        $klbNotificationRawModel->where(['created_at<=' => $klb_delete_point])->delete();
        $ocbNotificationModel->where(['created_at<=' => $ocb_delete_point])->delete();
        $ocbNotificationRawModel->where(['created_at<=' => $ocb_delete_point])->delete();
        $mbbNotificationModel->where(['created_at<=' => $mbb_delete_point])->delete();
        $mbbNotificationRawModel->where(['created_at<=' => $mbb_delete_point])->delete();
        $mbbMmsNotificationModel->where(['created_at <=' => $mbb_delete_point])->delete();
        $bidvNotificationModel->where(['created_at<=' => $bidv_delete_point])->delete();
        $bidvNotificationRawModel->where(['created_at<=' => $bidv_delete_point])->delete();
        $vpbankNotificationRawModel->where(['created_at <=' => $vpbank_delete_point])->delete();
        $vpbankNotificationModel->where(['created_at <=' => $vpbank_delete_point])->delete();
        $vietinbankNotificationRawModel->where(['created_at <=' => $vietinbank_delete_point])->delete();
        $vietinbankNotificationModel->where(['created_at <=' => $vietinbank_delete_point])->delete();
        $acbNotificationRawModel->where(['created_at <=' => $acb_delete_point])->delete();
        $acbNotificationModel->where(['created_at <=' => $acb_delete_point])->delete();
        $tpbankNotificationRawModel->where(['created_at <=' => $tpbank_delete_point])->delete();
        $tpbankNotificationModel->where(['created_at <=' => $tpbank_delete_point])->delete();
        $lgTokenModel->where(['created_at<=' => $sms_delete_point])->delete();
        $smsParserModel->where(['parser_status' => 'Failed'])->delete();

    }


    public function add_product() {
        die();
        if(!is_cli()) {
            return false;
        }

        $productModel = model(ProductModel::class);

        $productModel->insert(
            [
                'name' =>  'Promo',
                'description' => '<ul class="list-unstyled">
<li class="mb-2 border-top pt-2 fw-bold">
1000 giao dịch/ tháng
</li>
<li class="mb-2 border-top pt-2">
Kết nối<br> <span class="text-warning fw-bold"><i class="bi bi-lightning-charge"></i> API Banking <i class="bi bi-question-circle" data-bs-toggle="tooltip" data-bs-title="API chính thức từ KienLongBank, nhận giao dịch tức thì." data-bs-original-title="" title=""></i></span>
</li>
<li class="mb-4 border-top pt-2">
<div class="mb-1">Ngân Hàng kết nối</div><span class="fw-bold"><img class="ms-3" src="https://my.sepay.vn/assets/images/banklogo/kienlongbank-icon.png" style="height:20px; width:20px"> KienLongBank <i class="bi bi-question-circle" data-bs-toggle="tooltip" data-bs-title="Gói Free chỉ hỗ trợ ngân hàng KienLongBank. Nếu chưa có tài khoản, bạn có thể mở online qua App KienLongBank Plus" data-bs-original-title="" title=""></i></span>
</li>
<li class="mb-2 border-top pt-2">
Tích hợp<br> <span class="fw-bold">Telegram, Lark, Google Sheets, Haravan, Sapo, WordPress Woo</span>
</li>
<li class="mb-2 border-top pt-2">
Hỗ trợ: <b>WebHooks</b>, <b>API</b>
</li>
<li class="mb-2 border-top pt-2">
Tài khoản ngân hàng: <br><b>Không giới hạn</b></b>
</li>
</ul>',
             'sms_allow' => 0,
             'dedicated_sim' => 0,
             'monthly_transaction_limit' => 700,
             'price_monthly' => 250000,
             'price_annually' => 175000,
             'active' => 1,
             'sort_order' => 0
            ]
        );
    }

    public function update_product() {
        die();
	if(!is_cli()) {
            return false;
        }
        $productModel = model(ProductModel::class);
        $productModel->set(
            [
		    'description' => '
<ul class="list-unstyled">
<li class="mb-2 border-top pt-2 fw-bold">
35 giao dịch/ tháng
</li>
<li class="mb-2 border-top pt-2">
Kết nối<br> <span class="text-warning fw-bold"><i class="bi bi-lightning-charge"></i> API Banking <i class="bi bi-question-circle" data-bs-toggle="tooltip" data-bs-title="API chính thức từ KienLongBank, nhận giao dịch tức thì." data-bs-original-title="" title=""></i></span>
</li>
<li class="mb-4 border-top pt-2">
<div class="mb-1">Ngân Hàng kết nối</div><span class="fw-bold"><img class="ms-3" src="https://my.sepay.vn/assets/images/banklogo/kienlongbank-icon.png" style="height:20px; width:20px"> KienLongBank <i class="bi bi-question-circle" data-bs-toggle="tooltip" data-bs-title="Gói Free chỉ hỗ trợ ngân hàng KienLongBank. Nếu chưa có tài khoản, bạn có thể mở online qua App KienLongBank Plus" data-bs-original-title="" title=""></i></span>
</li>
<li class="mb-2 border-top pt-2">
Tích hợp<br> <span class="fw-bold">Telegram, Lark, Google Sheets, Haravan, Sapo, WordPress Woo</span>
</li>
<li class="mb-2 border-top pt-2">
Hỗ trợ: <b>WebHooks</b>, <b>API</b>
</li>
<li class="mb-2 border-top pt-2">
Tài khoản ngân hàng: <br><b>Không giới hạn</b></b>
</li>
</ul>
',
            ]
        )->where(['id'=>7])->update();
    }

    public function recalculator_cashflow() {
        if(!is_cli()) {
            return false;
        }
        die();
        $bankAccountCashflowModel = model(BankAccountCashflowModel::class);
        $bankAccountModel = model(BankAccountModel::class);
        $transactionsModel = model(TransactionsModel::class);

        $bank_accounts = $bankAccountModel->where(['last_transaction!=' => NULL])->orderBy('id', 'ASC')->get()->getResult();

        foreach($bank_accounts as $bank_account) {
            $results = $transactionsModel->select("id, gateway, sum(amount_in) as `sum_amount_in`, sum(amount_out) as `sum_amount_out`, account_number, date(transaction_date) as `transaction_date`")->where(['bank_account_id' => $bank_account->id])->groupBy("date(transaction_date)")->orderBy('transaction_date', 'asc')->get()->getResult();

            foreach($results as $result) {
                $row = $bankAccountCashflowModel->where(['date' => $result->transaction_date, 'bank_account_id' => $bank_account->id])->get()->getRow();

                if(!is_object($row)) {
                    $bankAccountCashflowModel->insert(['company_id' => $bank_account->company_id, 'bank_account_id' => $bank_account->id, 'date' => $result->transaction_date, 'total_amount_in' => $result->sum_amount_in, 'total_amount_out' => $result->sum_amount_out]);
                }

            }

        }
        
    }

    public function recalculator_cashflow2() {
        if(!is_cli()) {
            return false;
        }
        die();
        $bankAccountCashflowModel = model(BankAccountCashflowModel::class);
        $bankAccountModel = model(BankAccountModel::class);
        $transactionsModel = model(TransactionsModel::class);

        $bank_accounts = $bankAccountModel->where(['last_transaction' => NULL])->orderBy('id', 'ASC')->get()->getResult();

        foreach($bank_accounts as $bank_account) {
            $results = $transactionsModel->select("id, gateway, sum(amount_in) as `sum_amount_in`, sum(amount_out) as `sum_amount_out`, account_number, date(transaction_date) as `transaction_date`")->where(['bank_account_id' => $bank_account->id])->groupBy("date(transaction_date)")->orderBy('transaction_date', 'asc')->get()->getResult();

            foreach($results as $result) {
                $row = $bankAccountCashflowModel->where(['date' => $result->transaction_date, 'bank_account_id' => $bank_account->id])->get()->getRow();

                if(!is_object($row)) {
                    $bankAccountCashflowModel->insert(['company_id' => $bank_account->company_id, 'bank_account_id' => $bank_account->id, 'date' => $result->transaction_date, 'total_amount_in' => $result->sum_amount_in, 'total_amount_out' => $result->sum_amount_out]);
                }

            }

        }
        
    }

    public function recalculator_cashflow_accumulated() {
        if(!is_cli()) {
            return false;
        }
         
        $bankAccountCashflowModel = model(BankAccountCashflowModel::class);
        $bankAccountModel = model(BankAccountModel::class);
        $transactionsModel = model(TransactionsModel::class);

        $bank_accounts = $bankAccountModel->orderBy('id', 'ASC')->get()->getResult();

        foreach($bank_accounts as $bank_account) {
            //$results = $transactionsModel->select("bank_account_id, date(transaction_date) as `transaction_date`")->where(['bank_account_id' => $bank_account->id])->groupBy("date(transaction_date)")->orderBy('transaction_date', 'asc')->get()->getResult();

            $results = $bankAccountCashflowModel->where(['bank_account_id' => $bank_account->id])->orderBy('date', 'ASC')->get()->getResult();

            foreach($results as $result) {

                $row_trans = $transactionsModel->select('accumulated')->where(['transaction_date<=' => $result->date . " 23:59:59"])->orderBy('transaction_date', 'DESC')->get()->getRow();

                if(is_object($row_trans)) {

                    $bankAccountCashflowModel->set(['accumulated' => $row_trans->accumulated])->where(['id' => $result->id])->update();
                }

            }

        }
        
    }

    public function recalculator_va_cashflow() {
        if(!is_cli()) {
            return false;
        }
        die();
        $bankSubAccountCashflowModel = model(BankSubAccountCashflowModel::class);
        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $transactionsModel = model(TransactionsModel::class);

        $bank_sub_accounts = $bankSubAccountModel->orderBy('id', 'ASC')->get()->getResult();


        foreach($bank_sub_accounts as $bank_sub_account) {

            $bank_account_details = $bankAccountModel->where(['id' => $bank_sub_account->bank_account_id])->get()->getRow();

            if(!is_object($bank_account_details))
                continue;

            $results = $transactionsModel->select("id, gateway, sub_account, sum(amount_in) as `sum_amount_in`, bank_account_id, date(transaction_date) as `transaction_date`")->where(['sub_account' => $bank_sub_account->sub_account,'bank_account_id' => $bank_sub_account->bank_account_id])->groupBy("date(transaction_date)")->orderBy('transaction_date', 'asc')->get()->getResult();
            
            foreach($results as $result) {
                $row = $bankSubAccountCashflowModel->where(['date' => $result->transaction_date, 'bank_account_id' => $bank_sub_account->bank_account_id, 'sub_account' => $bank_sub_account->sub_account])->get()->getRow();

                if(!is_object($row)) {
                    $bankSubAccountCashflowModel->insert(['company_id' => $bank_account_details->company_id, 'bank_account_id' => $bank_sub_account->bank_account_id,'sub_account' => $bank_sub_account->sub_account, 'date' => $result->transaction_date, 'total_amount_in' => $result->sum_amount_in]);
                }

            }

        }
        
    }

    protected function deleteVAOrders($companyId, $expiredPoint)
    {
        $vaModel = model(VAModel::class);
        $vaOrderModel = model(VAOrderModel::class);

        $vaOrders = $vaOrderModel
            ->select('tb_autopay_va_order.id')
            ->where('tb_autopay_va_order.company_id', $companyId)
            ->where('tb_autopay_va_order.created_at <=', $expiredPoint)
            ->findAll();

        if (! empty($vaOrders)) {
            $vaOrderIds = array_column($vaOrders, 'id');
            $vaModel->whereIn('order_id', $vaOrderIds)->delete();
            $vaOrderModel->whereIn('id', $vaOrderIds)->delete();
        }
    }
  
    public function retry_google_sheet_queue()
    {
        if (!is_cli()) {
            return FALSE;
        }
    
        $max_retries_count = 7;
        $max_retries_hours = 5;
    
        while (1) {
            $googleSheetQueueModelSlave = slavable_model(GoogleSheetQueueModel::class, 'GoogleSheetQueue');
            $googleSheetQueueModelMaster = model(GoogleSheetQueueModel::class);
            
            $latest_time_retry = date("Y-m-d H:i:s", strtotime("-" . $max_retries_hours . " hours"));
    
            $results = $googleSheetQueueModelSlave
                ->select('tb_autopay_google_account.id as google_id, tb_autopay_google_sheet_queue.*')
                ->join("tb_autopay_google_account", "tb_autopay_google_account.id = tb_autopay_google_sheet_queue.google_account_id")
                ->where([
                    'tb_autopay_google_account.active' => 1,
                    'tb_autopay_google_sheet_queue.retries_count<=' => $max_retries_count,
                    'tb_autopay_google_sheet_queue.status' => 'SoftFailed',
                    'tb_autopay_google_sheet_queue.created_at>=' => $latest_time_retry
                ])
                ->orderBy('id', 'DESC')
                ->limit(10)
                ->get()
                ->getResult();

            
    
            foreach ($results as $queue) {
                if ($queue->retries_count >= $max_retries_count) {
                    $googleSheetQueueModelMaster->set(["status" => "Failed"])->where(['id' => $queue->id])->update();
                } else {
                    helper('general');
                    $can_retry = can_retry_job($queue->retries_count, $queue->last_retry_time);
    
                    if ($can_retry) {
                        $result = $googleSheetQueueModelMaster->handleGoogleSheetsTransaction(
                            $queue->company_id,
                            $queue->transaction_id,
                            $queue->bank_google_sheet_id,
                            $queue->google_account_id,
                            $queue->file_id,
                            $queue->sheet_id,
                            $queue->range,
                            $queue->value,
                            $queue->position
                        );
    
                        $queue_status = 'SoftFailed';
                        $queue_last_log = '';
                        $retries_count = $queue->retries_count + 1;
                        if (isset($result['sheet_name_log']) && isset($result['batch_update_sheet_log']) && isset($result['append_sheet_log'])) {
                            $queue_last_log = json_encode([
                                'sheet_name_log' => $result['sheet_name_log'] ?? [],
                                'batch_update_sheet_log' => $result['batch_update_sheet_log'] ?? [],
                                'append_sheet_log' => $result['append_sheet_log'] ?? [],
                            ]);
    
                            if (
                                ($result['sheet_name_log']['success'] &&
                                $result['batch_update_sheet_log']['success']) ||
                                ($result['sheet_name_log']['success'] && $result['append_sheet_log']['success'])
                            ) {
                                $queue_status = 'Success';
                            }
                        }
    
                        $googleSheetQueueModelMaster->set([
                            'status' => $queue_status,
                            'last_retry_log' => $queue_last_log,
                            'retries_count' => $retries_count,
                            'last_retry_time' => date("Y-m-d H:i:s")
                        ])->where(['id' => $queue->id])->update();
                        sleep(2);
                    }
                }
            }
            sleep(2);
        }
    }
    
    public function execute_google_sheet_auto_delete_after()
    {
        if (!is_cli()) {
            return FALSE;
        }
        $bankGoogleSheetModel = model(BankGoogleSheetModel::class);
        $result = $bankGoogleSheetModel
            ->select('tb_autopay_google_account.id as google_id, tb_autopay_bank_google_sheet.*')
            ->join("tb_autopay_google_account", "tb_autopay_google_account.id = tb_autopay_bank_google_sheet.google_account_id")
            ->where([
                'tb_autopay_google_account.active' => 1,
                'tb_autopay_bank_google_sheet.auto_delete_after !=' => 0
            ])->get()->getResult();
        if (empty($result)) {
            return false;
        }
        foreach ($result as $row) {
            $google_account_id = $row->google_account_id;
            $file_id = $row->file_id;
            $sheet_id = $row->sheet_id;
            $auto_delete_after = $row->auto_delete_after;
            $company_id = $row->company_id;
            $bank_google_sheet_id = $row->id;
            
            $transactions = $bankGoogleSheetModel->fetchGoogleSheetTransactions($company_id, $bank_google_sheet_id, $google_account_id, $file_id, $sheet_id);
            if (is_array($transactions)) {
                $transactionData = array_slice($transactions, 1);
                $rowsToDelete = [];
                if(count($transactionData)){
                    foreach ($transactionData as $index => $row) {
                        if (isset($row[1]) && !empty($row[1])) {
                            $transaction_date = strtotime($row[1]);
                            if ($transaction_date === false) {
                                log_message('error', "Invalid date format for transaction row: " . $bank_google_sheet_id);
                                continue;
                            }
                        
                            $current_time = time();
                            if (($current_time - $transaction_date) > $auto_delete_after) {
                                $rowsToDelete[] = $index + 1;
                            }
                        }
                    }
                    if (!empty($rowsToDelete)) {
                        $result = $bankGoogleSheetModel->deleteGoogleSheetTransactions($company_id, $bank_google_sheet_id, $google_account_id, $file_id, $sheet_id, $rowsToDelete);
                    }
                }
                
            }
        }
    }

    public function retry_zns_queue()
    {
        if (!is_cli()) {
            return FALSE;
        }

        $max_retries_count = 7;
        $max_retries_hours = 5;

        $znsQueueModel = model(ZnsQueueModel::class);
        $configZns = config(Zns::class);
        $latest_time_retry = date("Y-m-d H:i:s", strtotime("-{$max_retries_hours} hours"));

        $results = $znsQueueModel
            ->where('retryable', 1)
            ->where('created_at >=', $latest_time_retry)
            ->where('status', 'SoftFailed')
            ->orderBy('id', 'DESC')
            ->findAll();

        if (empty($results)) {
            echo "No retryable ZNS queue found.\n";
            return;
        }

        foreach ($results as $queue) {
            if ($queue->retry_count >= $max_retries_count) {
                $znsQueueModel->update($queue->id, ['status' => 'Failed']);
                continue;
            }

            helper('general');
            $can_retry = can_retry_job($queue->retry_count, $queue->last_retry_time);

            if (!$can_retry) continue;

            $raw_data = json_decode($queue->raw_data, true);
            $is_dry_run = false;

            // Check dry run conditions
            if ($queue->template_id === $configZns->zns_template_id_order_success && $configZns->zns_dry_run_order_success) {
                $is_dry_run = true;
            } elseif ($queue->template_id === $configZns->zns_template_id_invoice_recurring_next && $configZns->zns_dry_run_invoice_recurring_next) {
                $is_dry_run = true;
            } elseif ($queue->template_id === $configZns->zns_template_id_invoice_recurring_excess && $configZns->zns_dry_run_invoice_recurring_excess) {
                $is_dry_run = true;
            }

            if ($is_dry_run) {
                log_message('error', '[ZNS DRY RUN ' . $queue->template_id . '] Would send ZNS with phone number: ' . $queue->to . ' and data: ' . json_encode($raw_data));
            
                $retry_count = $queue->retry_count + 1;
                $znsQueueModel->update($queue->id, [
                    'status' => 'SoftFailed',
                    'retry_count' => $retry_count,
                    'last_retry_time' => date('Y-m-d H:i:s'),
                    'last_retry_log' => json_encode([
                        'status' => false,
                        'errorcode' => 'dry_run',
                        'description' => 'This is a dry run test. No real message sent.',
                    ]),
                ]);
                continue;
            }            

            $result = $znsQueueModel->sendZns($raw_data);

            $queue_status = 'SoftFailed';
            $queue_last_log = json_encode([
                'status' => $result['status'] ?? false,
                'errorcode' => $result['errorcode'] ?? '',
                'description' => $result['description'] ?? '',
            ]);
            $retry_count = $queue->retry_count + 1;

            if (!empty($result['status'])) {
                $queue_status = 'Sent';
            }

            $znsQueueModel->update($queue->id, [
                'status' => $queue_status,
                'last_retry_log' => $queue_last_log,
                'retry_count' => $retry_count,
                'last_retry_time' => date('Y-m-d H:i:s'),
            ]);

            sleep(2);
        }
    }

    public function send_zns_queue()
    {
        if (!is_cli()) {
            return FALSE;
        }
        
        $configZns = config(Zns::class);
        $znsQueueModel = model(ZnsQueueModel::class);

        $results = $znsQueueModel
        ->where('status', 'Pending')
        ->where('retryable', 1)
        ->orderBy('id', 'DESC')
        ->findAll();

        if (empty($results)) {
            echo "No pending ZNS queue found.\n";
            return;
        }

        foreach ($results as $queue) {
            $raw_data = json_decode($queue->raw_data, true);

            if ($configZns->zns_dry_run_invoice_recurring_next && $queue->template_id === $configZns->zns_template_id_invoice_recurring_next) {
                log_message('error', '[ZNS DRY RUN INVOICE RECURRING NEXT] Would send ZNS with phone number: ' . $queue->to . ' and data: ' . json_encode($raw_data));
                continue;
            }

            if ($configZns->zns_dry_run_invoice_recurring_excess && $queue->template_id === $configZns->zns_template_id_invoice_recurring_excess) {
                log_message('error', '[ZNS DRY RUN INVOICE RECURRING EXCESS] Would send ZNS with phone number: ' . $queue->to . ' and data: ' . json_encode($raw_data));
                continue;
            }

            $result = $znsQueueModel->sendZns($raw_data);

            $queue_status = 'SoftFailed';
            $retryable = $result['retryable'];

            if (!empty($result['status'])) {
                $queue_status = 'Sent';
            }

            $znsQueueModel->update($queue->id, [
                'status' => $queue_status,
                'retryable' => $retryable,
            ]);
        }   
    }

    public function retry_pg_ipn()
    {
        if (!is_cli()) return false;
        
        $max_retries_count = 7; // begin with 0
        $max_retries_hours = 5; // hours

        \CodeIgniter\CLI\CLI::write('Starting retry_pg_ipn cron job...');
        
        helper('general');
        
        $slavableModel = slavable_model(PgIpnQueueModel::class, 'Cron');
        
        while (1) {
            $pgIpnQueueModel = model(PgIpnQueueModel::class);
            $latestTimeRetry = date("Y-m-d H:i:s", strtotime("-" . $max_retries_hours . " hours"));

            $results = $slavableModel->where([
                'retries_count <=' => $max_retries_count,
                'status' => 'SoftFailed',
                'created_at >=' => $latestTimeRetry
            ])->orderBy('id', 'desc')->limit(10)->get()->getResult();

            \CodeIgniter\CLI\CLI::write(sprintf('Found %d pg_ipn queues to retry.', count($results)));
    
            foreach($results as $queue) {
                if ($queue->retries_count >= $max_retries_count) {
                    $pgIpnQueueModel->set(['status' => 'Failed'])->where(['id' => $queue->id])->update();
                    continue;
                }
                
                if (!can_retry_job($queue->retries_count, $queue->last_retry_time)) continue;
                
                $rabbitmq = new RabbitMQClient();

                if ($rabbitmq->connect()) {
                    $msg = new AMQPMessage(
                        json_encode(['queue_id' => $queue->id]),
                        ['delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT]
                    );

                    try {
                        $rabbitmq->queueDeclare('pg_retry_ipn');
                        $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('pg_retry_ipn', 'name'));
                        $pgIpnQueueModel->set(['status' => 'Pending'])->where(['id' => $queue->id])->update();
                    } catch (\Exception $e) {
                        log_message('error', sprintf('[Cron->retry_pg_ipn] Failed to publish message of pg_retry_ipn worker: %s | Trace: %s', $e->getMessage(), $e->getTraceAsString()));
                    }
                }
            }
            
            sleep(2);
        }
    }

    public function auto_renew_pg_agreement()
    {
        if (!is_cli()) {
            return false;
        }

        $pgAgreementModel = slavable_model(PgAgreementModel::class, 'Cron');

        $agreements = $pgAgreementModel
            ->select(['id', 'pg_merchant_id', 'minimum_days_between_payments'])
            ->where('active', 1)
            ->where('auto_renew', 1)
            ->where('next_due_date !=', null)
            ->where('DATEDIFF(next_due_date, NOW()) <=', 'minimum_days_between_payments')
            ->findAll();

        if (empty($agreements)) {
            CLI::write('No agreements found for auto-renewal.');
            return;
        }

        $rabbitmq = new RabbitMQClient();

        if (!$rabbitmq->connect()) {
            CLI::error('Failed to connect to RabbitMQ server.');
            return;
        }

        foreach ($agreements as $agreement) {
            $msg = new AMQPMessage(
                json_encode([
                    'pg_agreement_id' => $agreement->id,
                    'pg_merchant_id' => $agreement->pg_merchant_id
                ]),
                ['delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT]
            );

            try {
                $rabbitmq->queueDeclare('pg_renew_agreement');
                $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('pg_renew_agreement', 'name'));
            } catch (\Throwable $e) {
                log_message('error', sprintf('[Cron->auto_renew_pg_agreement] Failed to publish message of pg_renew_agreement worker: %s | Trace: %s', $e->getMessage(), $e->getTraceAsString()));
            }
        }

        CLI::write(sprintf('Published %d messages to pg_renew_agreement queue.', count($agreements)));
    }
}
