<?php

namespace App\Controllers;
use App\Models\EmailQueueModel;
use App\Models\UnsubscribeModel;

use CodeIgniter\Controller;

class Pub extends Controller
{

    public function u($uuid='') {
        helper(['form','general','url']);
  
        if(!ctype_alnum($uuid)) {
            $data =  [
                'status' => 'error',
                'message' => 'Đường dẫn không hợp lệ! Vui lòng liên hệ SePay để được hỗ trợ.'
            ];
            return view('unsubscribe/index', $data);
        }

        $emailQueueModel = model(EmailQueueModel::class);
        $unsubscribeModel = model(UnsubscribeModel::class);

        
        $result = $emailQueueModel->where(['uuid'=> $uuid])->get()->getRow();


        if(!is_object($result)) {
            $data =  [
                'status' => 'error',
                'message' => 'Không tìm thấy dữ liệu.'
            ];
        } else {

            $data =  [
                'status' => 'success',
            ];

            $check = $unsubscribeModel->where(['email' => $result->mail_to])->get()->getRow();

            if(!is_object($check))
                $unsubscribeModel->insert([
                    'email' => $result->mail_to,
                    'notification_id' => $result->data_id,
                ]);
        }

        return view('unsubscribe/index', $data);

 
    }

    public function e($uuid = '')
    {
        $emailQueueModel = model(EmailQueueModel::class);

        $emailQueueDetails = $emailQueueModel->where('uuid', $uuid)->first();

        if ($emailQueueDetails && $emailQueueDetails->is_read == 0) {
            $emailQueueModel->where('uuid', $uuid)->set(['is_read' => 1, 'reader_ip' => $this->request->getIPAddress()])->update();
        }

        return $this->response->setHeader('Content-Type', 'image/png')
            ->setBody(base64_decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABAQMAAAAl21bKAAAAA1BMVEUAAACnej3aAAAAAXRSTlMAQObYZgAAAApJREFUCNdjYAAAAAIAAeIhvDMAAAAASUVORK5CYII='));
    }
}