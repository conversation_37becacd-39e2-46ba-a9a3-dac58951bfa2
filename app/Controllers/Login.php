<?php

namespace App\Controllers;

use App\Models\UserModel;
use App\Models\LgTokenModel;
use App\Libraries\Captcha;
use CodeIgniter\Controller;
use Config\Services;

class Login extends Controller
{
    public function index()
    {
        helper(['form','general','url', 'cookie']);

        if ($this->request->getGet('onboarding') === 'loa') {
            set_cookie('onboarding', 'loa', 86400);
        }

        if(check_logged_user()) {
            return redirect()->to(base_url());
        }

        $captcha = new Captcha;

        $data['captcha_enabled'] = $captcha->getEnabled();
        $data['captcha_driver'] = $captcha->getDriver();
        $data['captcha_site_key'] = $captcha->getSiteKey();

        $securityConfig = config(\Config\Security::class);
        $data['enabled_login_with_phone_number'] = is_admin() ? true : ($securityConfig->enabledLoginWithPhoneNumber ?? is_admin());
        
        return view('login/index', $data);
    }

    public function do_login() {
        
        helper(['form','general','url']);
        
     
        $request = \Config\Services::request();

        if ($request->getMethod(true) != 'POST')
            return redirect()->to(base_url());
 
        if(check_logged_user()) {
            return redirect()->to(base_url());
        }
        
        $securityConfig = config(\Config\Security::class);
        $enabledLoginWithPhoneNumber = is_admin() ? true : ($securityConfig->enabledLoginWithPhoneNumber ?? is_admin());
        
        $validation =  \Config\Services::validation();

        $rules = [
            'email' => ["label" => "Email", "rules" => $enabledLoginWithPhoneNumber ? 'required' : 'required|valid_email'],
            'password' =>  ["label" => "Mật khẩu", "rules" => "required|max_length[1000]"]
        ];

        $captcha = new Captcha;

        if ($captcha->getEnabled()) {
            if ($captcha->getDriver() === 'turnstile') {
                $rules['cf-turnstile-response'] = ['label' => 'Captcha', 'rules' => 'required|valid_turnstile'];
            } else if ($captcha->getDriver() === 'recaptcha') {
                $rules['g-recaptcha-response'] = ["label" => "Recaptcha", "rules" => "required|valid_recaptcha"];
            }
        }

        if(! $this->validate($rules)) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode("<br>", $validation->getErrors())));
        } else {
            $securityConfig = config(\Config\Security::class);
            
            if ($securityConfig->enabledLoginRateLimiting) {
                $throttler = Services::throttler();

                if ($throttler->check(md5($request->getIPAddress()), 5, MINUTE) === false) {
                    return $this->response->setJSON(array("status"=>false,"message"=>"Bạn đã thao tác quá nhiều lần, vui lòng thử lại sau " . ($throttler->getTokentime() === 1 ? 'ít' : $throttler->getTokentime()) . " giây nữa."));
                }
            }

            $model = model(UserModel::class);

            $email = $request->getVar('email', FILTER_SANITIZE_EMAIL);
            $password = $request->getVar('password');

            $authen = $model->login($email, $password, $remember=FALSE);

            $redirectUrl = session()->get('redirect_url') ?: base_url();
            session()->remove('redirect_url');

            if($authen) {
                return $this->response->setJSON([
                    'status' => true,
                    'redirect_url' => $redirectUrl,
                ]);
            } else {
                return $this->response->setJSON(array("status"=>false,"message"=>"Thông tin đăng nhập không chính xác. Vui lòng thử lại."));
            }
        }
    }

    public function logout() {
        helper('general');

        auth()->logout();

        return redirect()->to(base_url('login'));
    }

    function token($token='') {
        helper(['form','general','url']);
        
        $request = \Config\Services::request();

        $client_ip = $request->getIPAddress();

        if($client_ip != '**************')
            show_404(); 

        if(strlen($token) !=36)
            show_404();
        
        $lgTokenModel = model(LgTokenModel::class);
        $userModel = model(UserModel::class);

        $lgtoken_details = $lgTokenModel->where(['token' => $token, 'user_id!=' => NULL])->get()->getRow();

        session()->remove('channel_partner');

        if (isset($lgtoken_details->channel_partner_officer_id)) {
            session()->set('channel_partner_officer_id', $lgtoken_details->channel_partner_officer_id);
        }

        $lgTokenModel->where(['token' => $token])->delete();

        if(!is_object($lgtoken_details))
            show_404();
        
        $user_id = $lgtoken_details->user_id;

        if(!is_numeric($user_id))
            show_404();

        $user_details = $userModel->find($user_id);

        if(!is_object($user_details))
            show_404();
        
        $email = $user_details->email;

        $result = $userModel->login_as_user($user_details->email);
        
        if($result)
            return redirect()->to(site_url());


    } 
    
    

}