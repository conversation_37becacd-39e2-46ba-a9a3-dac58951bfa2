<?php

namespace App\Controllers;

use App\Actions\PayCodeDetector;
use App\Libraries\ShopifyClient;
use App\Models\BankAccountModel;
use App\Models\BankSubAccountModel;
use App\Models\ConfigurationModel;
use App\Models\ShopifyModel;
use App\Models\WebhooksLogModel;

class Shopify extends BaseController
{

  
    public function index()
    { 
        $data = [
            'page_title' => 'Shopify',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        if(!has_permission('Webhooks', 'can_view_all'))
            show_404();

        $bankAccountModel = model(BankAccountModel::class);

        $data['bankAccounts'] = $bankAccountModel->select("tb_autopay_bank_account.id, tb_autopay_bank.brand_name, tb_autopay_bank_account.account_holder_name, tb_autopay_bank_account.account_number")->join("tb_autopay_bank","tb_autopay_bank.id=tb_autopay_bank_account.bank_id","left")->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id']])->get()->getResult();
        $data['paycodes'] = PayCodeDetector::getPayCodeList($this->user_session['company_id']);
        $data['count_integration'] = model(ShopifyModel::class)
        ->where(['company_id' => $this->user_session['company_id']])
        ->countAllResults();

        echo view('templates/autopay/header',$data);
        echo view('shopify/index',$data);
        echo view('templates/autopay/footer',$data);
    }

    public function ajax_shopify_list()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!has_permission('Webhooks', 'can_view_all'))
            show_404();
 
        $shopifyModel = model(ShopifyModel::class);
        $webhooksLogModel = slavable_model(WebhooksLogModel::class, 'Shopify');
        $list = $shopifyModel->getDatatables($this->user_session['company_id']);
        $paycodePrefix = model(ConfigurationModel::class)->getFirstPaycodePrefix($this->user_session['company_id']);

        $data = array();

        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');

        foreach ($list as $shopify) {
            $total_success = $webhooksLogModel->where(['webhook_id' => $shopify->id,'webhook_type'=>'Shopify', 'response_status_code<=' => 201, 'response_status_code>=' => 200, 'company_id' =>$this->user_session['company_id']])->countAllResults();
            $total_failed = $webhooksLogModel->where(['webhook_id' => $shopify->id, 'response_status_code>' => 201,'webhook_type'=>'shopify'])->countAllResults();

            $today_success = $webhooksLogModel->where(['webhook_id' => $shopify->id, 'response_status_code<=' => 201, 'response_status_code>=' => 200,'created_at>=' => date('Y-m-d 00:00:00'), 'webhook_type' => 'Shopify', 'company_id' =>$this->user_session['company_id']])->countAllResults();
            $today_failed = $webhooksLogModel->where(['webhook_id' => $shopify->id, 'response_status_code>' => 201, 'created_at >=' => date('Y-m-d 00:00:00'), 'webhook_type' => 'Shopify', 'company_id' =>$this->user_session['company_id']])->countAllResults();

            if(empty($shopify->script_tag_id)){
                $text_warning = "Đồng bộ ngay";
                $event_update = " <a href='javascript:;' onclick='update_new_shopify(" . $shopify->id . ")' class='text-danger mt-2'><small class='me-1' style='font-size:12px;'>$text_warning</small><i data-bs-toggle='tooltip' data-bs-placement='top' title='Lưu ý: Hãy mở quyền write_script_tags và read_script_tags trong ứng dụng SePay đã cấu hình trên Shopify để có thể cập nhật phiên bản mới' class='bi fs-5 text-info bi-exclamation-circle'></i></a> ";
            }else{
                $text_warning = "";
                $event_update = "";
            }

            $no++;
            $row = array();
            
            $row[] = $no;
            $row[] = esc($shopify->id);
            $row[] = "<a href='".base_url('shopify/details/' . $shopify->id)."'>" . esc($shopify->name) . "</a>"."</br><small class='text-danger'>$event_update</small>";
            $row[] = "<b>" . esc($shopify->api_store_url) . "</b>";

            $row[] = esc($shopify->brand_name) . ' <br> ' . esc($shopify->account_number) . ' <br>' . esc($shopify->account_holder_name);
            $row[] = esc($shopify->paycode_prefix ?: $paycodePrefix);

            $row[] = "Hôm nay: <a href='" . base_url('shopify/logs/' . $shopify->id). "' class='text-success'>". number_format($today_success) . "</a> / <a href='" . base_url('shopify/logs/' . $shopify->id). "' class='text-danger'>" . $today_failed ."</a><br>Tổng: <a href='" . base_url('shopify/logs/' . $shopify->id). "' class='text-success'>". number_format($total_success) . "</a> / <a href='" . base_url('shopify/logs/' . $shopify->id). "' class='text-danger'>" . $total_failed ."</a>";
            
            if($shopify->active == 1)
                $row[] = "<span class='text-success'>Kích hoạt</span>";
            else
                $row[] = "<span class='text-secondary'>Tạm ngưng</span>";
            $row[] = esc($shopify->created_at);
            $row[] = "
           
          <a href='javascript:;' onclick='edit_shopify(" . $shopify->id . ")' class='btn btn-sm btn-outline-warning ms-2 mt-2'><i class='bi bi-pencil'></i> Sửa</a> 
          <a href='javascript:;' onclick='delete_shopify(" . $shopify->id . ")' class='btn btn-sm btn-outline-danger ms-2 mt-2'><i class='bi bi-trash3'></i> Xóa</a>
          <a href='javascript:;' onclick='window.location.href = \"" . base_url() . "/shopify/embed/" . $shopify->id . "\"' class='btn btn-sm btn-outline-info ms-2 mt-2'><i class='bi bi-eye me-2'></i> Chi tiết</a>";

            $data[] = $row;
        }
 
        $output = array(
            "draw" => $draw,
            "recordsTotal" => $shopifyModel->countAll($this->user_session['company_id']),
            "recordsFiltered" => $shopifyModel->countFiltered($this->user_session['company_id']),
            "data" => $data,
        );

        return $this->response->setJSON($output);
    }

    public function ajax_shopify_add() 
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!has_permission('Webhooks', 'can_add'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền thêm tích hợp"));
 
        $validation = \Config\Services::validation();

        helper('text');

        $paramsRules = [];

        $hasAnyParamRequired = count(array_filter(ShopifyClient::$params, function($param) {
            return $param['required'];
        })) > 0;

        if ($hasAnyParamRequired) {
            $paramsRules['params'] = ['label' => 'Thông số', 'rules' => 'required'];
        }

        foreach (ShopifyClient::$params as $paramKey => $paramConfig) {
            $paramsRules['params.' . $paramKey] = ($paramConfig['required'] ? 'required' : 'permit_empty') . '|in_list[' . implode(',', $paramConfig['options']) . ']';
        }

        $rules = [
            'bank_account_id' =>['label' => 'Tài khoản ngân hàng', 'rules' => 'required|integer|is_natural'],
            'api_store_url' => ['label' => 'URL cửa hàng', 'rules' =>"required|valid_url_strict[https]|valid_shopify_url"],
            'name' => ['label' => 'Đặt tên', 'rules' => "required|max_length[250]"],
            'api_token' => ['label' => 'API Token', 'rules' => "required|min_length[10]|max_length[50]"],
            'active' => "required|in_list[0,1]",
            'paycode_prefix' => 'required',
        ];
        
        $rules = array_merge($rules, $paramsRules);
        
        if(! $this->validate($rules)) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        } 

        $paycodes = PayCodeDetector::getPayCodeList($this->user_session['company_id']);

        if (! in_array($this->request->getVar('paycode_prefix'), $paycodes)) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Mã thanh toán không hợp lệ',
            ]);
        }

        $bank_account_id = $this->request->getPost('bank_account_id');
        $va_id = $this->request->getPost('va_id');

        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $shopifyModel = model(ShopifyModel::class);

        // Fetch bank account details
        $bank_account_details = $bankAccountModel
            ->select("tb_autopay_bank_account.bank_api,tb_autopay_bank_account.account_holder_name, tb_autopay_bank_account.account_number, tb_autopay_bank.brand_name, tb_autopay_bank.full_name, tb_autopay_bank.bin, tb_autopay_bank.code, tb_autopay_bank.logo_path, tb_autopay_bank.icon_path,tb_autopay_bank.id bank_id")
            ->join("tb_autopay_bank", "tb_autopay_bank_account.bank_id=tb_autopay_bank.id")
            ->where([
                'tb_autopay_bank_account.id' => $bank_account_id,
                'company_id' => $this->user_session['company_id']
            ])
            ->get()
            ->getRow();
    
       
    
        
        if (!is_object($bank_account_details))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>'Không tìm thấy tài khoản ngân hàng mà bạn chọn'));

        if (is_numeric($va_id) && $va_id > 0) {
            $va_details = $bankSubAccountModel
            ->join("tb_autopay_bank_account", "tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id", "left")
            ->where([
                'tb_autopay_bank_sub_account.id' => $va_id,
                'tb_autopay_bank_sub_account.acc_type' => "Real",
                'tb_autopay_bank_account.company_id' => $this->user_session['company_id']
            ])
            ->get()
            ->getRow();
            if (!is_object($va_details))
                return $this->response->setJSON(array('status'=>FALSE,'message'=>'Không tìm thấy VA mà bạn chọn'));
        } else {
           
            if ($bank_account_details->bank_api == 1 && in_array($bank_account_details->bank_id, [9,10,17,18]))
                return $this->response->setJSON(array('status'=>FALSE, 'message'=> 'Với các ngân hàng kết nối qua API, bạn cần chọn VA để nhận thanh toán. Vui lòng chọn 1 VA'));
            else
                $va_id = 0;
        }

        $params = [];
        $incomingParams = $this->request->getVar('params');
        foreach (ShopifyClient::$params as $paramKey => $paramConfig) {
            if (!isset($incomingParams[$paramKey])) continue;
            $value = $incomingParams[$paramKey];
            if (!$paramConfig['required'] && !$value) continue;
            $params[$paramKey] = $incomingParams[$paramKey];
        }
        
        $data = array(
            'company_id' => $this->user_session['company_id'],
            'bank_account_id' => $this->request->getVar('bank_account_id'),
            'api_store_url' => trim($this->request->getVar('api_store_url')),
            'api_token' => trim($this->request->getVar('api_token')),
            'name' => xss_clean($this->request->getVar('name')),
            'active' => $this->request->getVar('active'),
            'va_id' => $va_id,
            'params' => json_encode($params),
            'paycode_prefix' => $this->request->getVar('paycode_prefix')
        );

        
    
        
        // Initialize account details
        $account_number = $bank_account_details->account_number;
        $account_holder_name = $bank_account_details->account_holder_name;
    
        // Check for virtual account details
           
    
        if (!empty($va_id)) {
            $account_number = $va_details->sub_account;
            $account_holder_name = $va_details->sub_holder_name;
        }
    
        // Prepare data for script tag
        $data_json_url = json_encode([
            'bank_bin' => $bank_account_details->bin,
            'bank_code' => $bank_account_details->code,
            'account_number' => $account_number,
            'prefix' => $data['paycode_prefix'],
            'bank_brand_name' => $bank_account_details->brand_name,
            'account_name' => $account_holder_name,
        ]);
    
        
        $client =  new ShopifyClient($data['api_store_url'], $data['api_token']);    

        $add_script = $client->addScriptTag($data_json_url);

        $conver_data_res_body = json_decode($add_script['body'], true);
         // Kiểm tra lỗi JSON
         if (json_last_error() !== JSON_ERROR_NONE) {
            log_message("error","Lỗi chuyển đổi json");
            add_user_log(array('data_id'=>0,'company_id' => $this->user_session['company_id'], 'data_type'=>'shopify_add','description'=>'Thêm tích hợp Shopify','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Failed'));
            return $this->response->setJSON(array("status"=>false,"message"=>"Không thể thêm webhook. Vui lòng liên hệ SePay để được hỗ trợ."));
        } 
        $script_tag_id = "";
        if ($add_script['code'] == 201) {
            $script_tag_id = $conver_data_res_body['script_tag']['id']; 
        }

        if(empty($script_tag_id)){
            log_message("error","Lỗi thực hiện api add script");
            add_user_log(array('data_id'=>0,'company_id' => $this->user_session['company_id'], 'data_type'=>'shopify_add','description'=>'Thêm tích hợp Shopify','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Failed'));
            return $this->response->setJSON(array("status"=>false,"message"=>$add_script['message']));
        }

        $data['script_tag_id'] = $script_tag_id;
        try {
            $result = $shopifyModel->insert($data);
        } catch (\Exception $e) {
            return $this->response->setJSON(array("status"=>false,"message"=>"API Token đã được cấu hình cho tài khoản ngân hàng trước đó."));
        }
        
        if ($result) {
            set_alert('success','Tạo tích hợp Shopify thành công');
            add_user_log(array('data_id'=>$result,'company_id' => $this->user_session['company_id'], 'data_type'=>'shopify_add','description'=>'Thêm tích hợp Shopify','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
            return $this->response->setJSON(array("status"=>true,"id"=>$result));
        } else {
            add_user_log(array('data_id'=>0,'company_id' => $this->user_session['company_id'], 'data_type'=>'shopify_add','description'=>'Thêm tích hợp Shopify','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Failed'));
            return $this->response->setJSON(array("status"=>false,"message"=>"Không thể thêm webhook. Vui lòng liên hệ SePay để được hỗ trợ."));
        }
    }

    public function ajax_get_shopify($id='')
    {
        if(!has_permission('Webhooks', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền sửa tích hợp"));

        if(!is_numeric($id)) 
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"ID tích hợp không hợp lệ"));
        
        $shopifyModel = model(ShopifyModel::class);
        
        $result = $shopifyModel->where(['id'=>$id, 'company_id' => $this->user_session['company_id']])->get()->getRow();
        $result->paycode_prefix = $result->paycode_prefix ?: model(ConfigurationModel::class)->getFirstPaycodePrefix($this->user_session['company_id']);

        if (!is_null($result->params)) {
            $result->params = json_decode($result->params);
        }
        
        if($result)
            return $this->response->setJSON(["status"=>TRUE, "data"=>$result]);
        else
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy tích hợp này"));
    }

    public function ajax_shopify_update()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!has_permission('Webhooks', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền sửa tích hợp"));

        $validation =  \Config\Services::validation();

        helper('text');

        $paramsRules = [];
        $hasAnyParamRequired = count(array_filter(ShopifyClient::$params, function($param) {
            return $param['required'];
        })) > 0;
        if ($hasAnyParamRequired) {
            $paramsRules['params'] = ['label' => 'Thông số', 'rules' => 'required'];
        }
        foreach (ShopifyClient::$params as $paramKey => $paramConfig) {
            $paramsRules['params.' . $paramKey] = ($paramConfig['required'] ? 'required' : 'permit_empty') . '|in_list[' . implode(',', $paramConfig['options']) . ']';
        }
       
        $rules = [
            'id' => ['label' => 'ID', 'rules' => 'required|integer|is_natural'],
            'bank_account_id' =>['label' => 'Tài khoản ngân hàng', 'rules' => 'required|integer|is_natural'],
            'api_store_url' => ['label' => 'URL cửa hàng', 'rules' =>"required|valid_url_strict[https]|valid_shopify_url"],
            'name' => ['label' => 'Đặt tên', 'rules' => "required|max_length[250]"],
            'api_token' => ['label' => 'API Key', 'rules' => "required|min_length[10]|max_length[50]"],
            'active' => "required|in_list[0,1]",
            'paycode_prefix' => 'required',
        ]; 

        $rules = array_merge($rules, $paramsRules);
         
        if (! $this->validate($rules)) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        } 

        $paycodes = PayCodeDetector::getPayCodeList($this->user_session['company_id']);

        if (! in_array($this->request->getVar('paycode_prefix'), $paycodes)) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Mã thanh toán không hợp lệ',
            ]);
        }

        $bank_account_id = $this->request->getPost('bank_account_id');
        $va_id = $this->request->getPost('va_id');

        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);

        $shopifyModel = model(ShopifyModel::class);
        $shopify_id = $this->request->getVar('id');
        
        $shopify_details = $shopifyModel->where(["id" =>$shopify_id,"company_id"=>$this->user_session['company_id']])->get()->getRow();
        $script_tag_id = $shopify_details->script_tag_id ?? "";


        // Fetch bank account details
        $bank_account_details = $bankAccountModel
            ->select("tb_autopay_bank_account.bank_api,tb_autopay_bank_account.account_holder_name, tb_autopay_bank_account.account_number, tb_autopay_bank.brand_name, tb_autopay_bank.full_name, tb_autopay_bank.bin, tb_autopay_bank.code, tb_autopay_bank.logo_path, tb_autopay_bank.icon_path,tb_autopay_bank.id bank_id")
            ->join("tb_autopay_bank", "tb_autopay_bank_account.bank_id=tb_autopay_bank.id")
            ->where([
                'tb_autopay_bank_account.id' => $bank_account_id,
                'company_id' => $this->user_session['company_id']
            ])
            ->get()
            ->getRow();
    
        if (!is_object($bank_account_details))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>'Không tìm thấy tài khoản ngân hàng mà bạn chọn'));

        if (is_numeric($va_id) && $va_id > 0) {
            $va_details = $bankSubAccountModel
            ->join("tb_autopay_bank_account", "tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id", "left")
            ->where([
                'tb_autopay_bank_sub_account.id' => $va_id,
                'tb_autopay_bank_sub_account.acc_type' => "Real",
                'tb_autopay_bank_account.company_id' => $this->user_session['company_id']
            ])
            ->get()
            ->getRow();
            if (!is_object($va_details))
                return $this->response->setJSON(array('status'=>FALSE,'message'=>'Không tìm thấy VA mà bạn chọn'));
        } else {
            if ($bank_account_details->bank_api == 1 && in_array($bank_account_details->bank_id, [9,10,17,18]))
                return $this->response->setJSON(array('status'=>FALSE, 'message'=> 'Với các ngân hàng kết nối qua API, bạn cần chọn VA để nhận thanh toán. Vui lòng chọn 1 VA'));
            else
                $va_id = 0;
        }

        $params = [];
        $incomingParams = $this->request->getVar('params');
        foreach (ShopifyClient::$params as $paramKey => $paramConfig) {
            if (!isset($incomingParams[$paramKey])) continue;
            $value = $incomingParams[$paramKey];
            if (!$paramConfig['required'] && !$value) continue;
            $params[$paramKey] = $incomingParams[$paramKey];
        }

        $data = array(
            'bank_account_id' => $this->request->getVar('bank_account_id'),
            'api_store_url' => trim($this->request->getVar('api_store_url')),
            'api_token' => trim($this->request->getVar('api_token')),
            'name' => xss_clean($this->request->getVar('name')),
            'script_tag_id' => $script_tag_id,
            'active' => $this->request->getVar('active'),
            'va_id' => $va_id,
            'params' => json_encode($params),
            'paycode_prefix' => $this->request->getVar('paycode_prefix'),
        );

         // Initialize account details
        $account_number = $bank_account_details->account_number;
        $account_holder_name = $bank_account_details->account_holder_name;
    
        // Check for virtual account details
           
    
        if (!empty($va_id)) {
            $account_number = $va_details->sub_account;
            $account_holder_name = $va_details->sub_holder_name;
        }
    
        // Prepare data for script tag
        $data_json_url = json_encode([
            'bank_bin' => $bank_account_details->bin,
            'bank_code' => $bank_account_details->code,
            'account_number' => $account_number,
            'prefix' => $data['paycode_prefix'],
            'bank_brand_name' => $bank_account_details->brand_name,
            'account_name' => $account_holder_name,
        ]);
    
       
    
        $client =  new ShopifyClient($data['api_store_url'], $data['api_token']);    
        $update_script = $client->updateScriptTag($script_tag_id,$data_json_url);


        if($update_script['code'] == 404 ){
            $add_script = $client->addScriptTag($data_json_url);

            $conver_data_res_body = json_decode($add_script['body'], true);
             // Kiểm tra lỗi JSON
             if (json_last_error() !== JSON_ERROR_NONE) {
                log_message("error","Lỗi chuyển đổi json");
                add_user_log(array('data_id'=>0,'company_id' => $this->user_session['company_id'], 'data_type'=>'shopify_add','description'=>'Thêm tích hợp Shopify','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Failed'));
                return $this->response->setJSON(array("status"=>false,"message"=>"Không thể cập nhật Shopify. Vui lòng liên hệ SePay để được hỗ trợ."));
            } 
            $script_tag_id = "";
            if ($add_script['code'] == 201) {
                $script_tag_id = $conver_data_res_body['script_tag']['id']; 
            }
    
            if(empty($script_tag_id)){
                log_message("error","Lỗi thực hiện api add script");
                add_user_log(array('data_id'=>0,'company_id' => $this->user_session['company_id'], 'data_type'=>'shopify_add','description'=>'Thêm tích hợp Shopify','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Failed'));
                return $this->response->setJSON(array("status"=>false,"message"=>$add_script['message']));
            }
    
            $data['script_tag_id'] = $script_tag_id;
            try {
                $result = $shopifyModel->set($data)->where(["id" =>$shopify_id,"company_id"=>$this->user_session['company_id']])->update();
                return $this->response->setJSON(array("status"=>true, 'id' => $shopify_id));
            } catch (\Exception $e) {
                return $this->response->setJSON(array("status"=>false,"message"=>"Lỗi hệ thống, hãy liên hệ SePay."));
            }

        }

        if ($update_script['code'] != 200) {
            log_message("error","Lỗi thực hiện api cập nhật script");
            add_user_log(array('data_id'=>0,'company_id' => $this->user_session['company_id'], 'data_type'=>'shopify_update','description'=>'Cập nhật tích hợp Shopify','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Failed'));
            return $this->response->setJSON(array("status"=>false,"message"=>$update_script['message']));
        }

            
        $result = $shopifyModel->set($data)->where(["id" =>$shopify_id,"company_id"=>$this->user_session['company_id']])->update();
        
        if ($result) { 
            add_user_log(array('data_id'=>$shopify_id,'company_id' => $this->user_session['company_id'], 'data_type'=>'shopify_update','description'=>'Sửa tích hợp Shopify','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
            return $this->response->setJSON(array("status"=>true, 'id' => $shopify_id));
        } else {
            add_user_log(array('data_id'=>$shopify_id,'company_id' => $this->user_session['company_id'], 'data_type'=>'shopify_update','description'=>'Sửa tích hợp Shopify','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Failed'));
            return $this->response->setJSON(array("status"=>false,"message"=>"Không thể cập nhật tích hợp!"));
        }
    }

    public function ajax_shopify_delete()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!has_permission('Webhooks', 'can_delete'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền xóa tích hợp"));

        $validation = \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'id' => 'required|integer|is_natural',
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }
        $shopify_id = $this->request->getPost('id');

        $shopifyModel = model(ShopifyModel::class);
        $shopify_details = $shopifyModel->where(['id'=>$shopify_id,'company_id'=>$this->user_session['company_id']])->get()->getRowArray();

        $script_tag_id = $shopify_details['script_tag_id'] ?? "";
        
        if(empty($shopify_details))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Không tìm thấy tích hợp này"));

        
        if (empty($script_tag_id)){
            // remove script old
            $shopifyModel->delete($shopify_id);
            add_user_log(array('data_id'=>$shopify_id,'company_id' => $this->user_session['company_id'], 'data_type'=>'shopify_delete','description'=>'Xóa tích hợp Shopify','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));    
            return $this->response->setJSON(array("status"=>true));
        }

        $client =  new ShopifyClient($shopify_details['api_store_url'], $shopify_details['api_token']);   
        $delete_script = $client->deleteScriptTag($script_tag_id);

        if($delete_script['code'] == 404){
            $shopifyModel->delete($shopify_id);
            add_user_log(array('data_id'=>$shopify_id,'company_id' => $this->user_session['company_id'], 'data_type'=>'shopify_delete','description'=>'Xóa tích hợp Shopify','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
            return $this->response->setJSON(array("status"=>true));
        }
       
        if ($delete_script['code'] != 200) {
            log_message("error","Lỗi thực hiện api xóa script");
            add_user_log(array('data_id'=>0,'company_id' => $this->user_session['company_id'], 'data_type'=>'shopify_update','description'=>'Xóa tích hợp Shopify','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Failed'));
            return $this->response->setJSON(array("status"=>false,"message"=>$delete_script['message']));
        }


        $shopifyModel->delete($shopify_id);

        add_user_log(array('data_id'=>$shopify_id,'company_id' => $this->user_session['company_id'], 'data_type'=>'shopify_delete','description'=>'Xóa tích hợp Shopify','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
    
        return $this->response->setJSON(array("status"=>true));
    }

    public function details($id = '')
    { 
        $data = [
            'page_title' => 'Shopify',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        if (!has_permission('Webhooks', 'can_view_all'))
            show_404();

        if (!is_numeric($id))
            show_404();

        $shopifyModel = model(ShopifyModel::class);
        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);

        $data['bankAccounts'] = $bankAccountModel->select("tb_autopay_bank_account.id, tb_autopay_bank.brand_name, tb_autopay_bank_account.account_holder_name, tb_autopay_bank_account.account_number")->join("tb_autopay_bank","tb_autopay_bank.id=tb_autopay_bank_account.bank_id","left")->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id']])->get()->getResult();

        $data['shopify_details'] = $shopifyModel->where(['id' => $id, 'company_id' => $this->user_session['company_id']])->get()->getRow();
        $data['shopify_details']->paycode_prefix = $data['shopify_details']->paycode_prefix ?: model(ConfigurationModel::class)->getFirstPaycodePrefix($this->user_session['company_id']);

        if(!is_object($data['shopify_details']))
            show_404();
        
        $data['bank_account_details'] = $bankAccountModel->select("tb_autopay_bank_account.account_holder_name,tb_autopay_bank_account.account_number,tb_autopay_bank.brand_name, tb_autopay_bank.full_name, tb_autopay_bank.logo_path, tb_autopay_bank.icon_path")->join("tb_autopay_bank","tb_autopay_bank_account.bank_id=tb_autopay_bank.id")->where(['tb_autopay_bank_account.id' => $data['shopify_details']->bank_account_id,'company_id' => $this->user_session['company_id']])->get()->getRow();

        if (!is_object($data['bank_account_details']))
            show_404();

        if (is_numeric($data['shopify_details']->va_id) && $data['shopify_details']->va_id > 0) {
            $data['va_details'] = $bankSubAccountModel->select("tb_autopay_bank_sub_account.sub_account, tb_autopay_bank_sub_account.sub_holder_name, tb_autopay_bank_sub_account.label")->join("tb_autopay_bank_account","tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id")->where(["tb_autopay_bank_account.company_id" => $this->user_session['company_id'],'tb_autopay_bank_sub_account.id' => $data['shopify_details']->va_id])->get()->getRow();
        } else
            $data['va_details'] = FALSE;
      
        echo view('templates/autopay/header',$data);
        echo view('shopify/details',$data);
        echo view('templates/autopay/footer',$data);
    }

    public function embed($id = '')
    { 
        $data = [
            'page_title' => 'Shopify',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        if(!has_permission('Webhooks', 'can_view_all'))
            show_404();

        if(!is_numeric($id))
            show_404();

        $shopifyModel = model(ShopifyModel::class);
        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);

        $data['bankAccounts'] = $bankAccountModel->select("tb_autopay_bank_account.id, tb_autopay_bank.brand_name, tb_autopay_bank_account.account_holder_name, tb_autopay_bank_account.account_number")->join("tb_autopay_bank","tb_autopay_bank.id=tb_autopay_bank_account.bank_id","left")->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id']])->get()->getResult();

        $data['shopify_details'] = $shopifyModel->where(['id' => $id, 'company_id' => $this->user_session['company_id']])->get()->getRow();

        if(!is_object($data['shopify_details']))
            show_404();
        
        $data['bank_account_details'] = $bankAccountModel->select("tb_autopay_bank_account.account_holder_name,tb_autopay_bank_account.account_number,tb_autopay_bank.brand_name, tb_autopay_bank.full_name,tb_autopay_bank.bin,tb_autopay_bank. code, tb_autopay_bank.logo_path, tb_autopay_bank.icon_path")->join("tb_autopay_bank","tb_autopay_bank_account.bank_id=tb_autopay_bank.id")->where(['tb_autopay_bank_account.id' => $data['shopify_details']->bank_account_id,'company_id' => $this->user_session['company_id']])->get()->getRow();

        if(!is_object($data['bank_account_details']))
            show_404();

        $payCodePrefix = $data['shopify_details']->paycode_prefix ?: model(ConfigurationModel::class)->getFirstPaycodePrefix($this->user_session['company_id']);

        $account_number = $data['bank_account_details']->account_number;
        $account_holder_name = $data['bank_account_details']->account_holder_name;

        if (is_numeric($data['shopify_details']->va_id) && $data['shopify_details']->va_id>0) {
            $va_details = $bankSubAccountModel->join("tb_autopay_bank_account", "tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id","left")->where(['tb_autopay_bank_sub_account.id' => $data['shopify_details']->va_id, 'tb_autopay_bank_account.company_id' => $this->user_session['company_id']])->get()->getRow();

            if(is_object($va_details)) {
                $account_number = $va_details->sub_account;
                $account_holder_name = $va_details->sub_holder_name;
            }
        } 

        $data['api_json_data'] = '{"bank_bin":'.$data['bank_account_details']->bin.',"bank_code":"'.$data['bank_account_details']->code.'","account_number":"'.$account_number.'","prefix":"'.$payCodePrefix.'","bank_brand_name":"'.$data['bank_account_details']->brand_name.'","account_name":"'.$account_holder_name.'"}';

        $data['api_data_encode'] = base64_encode($data['api_json_data']);

        echo view('templates/autopay/header',$data);
        echo view('shopify/embed',$data);
        echo view('templates/autopay/footer',$data);
    }

    public function logs($id = '')
    { 
        $data = [
            'page_title' => 'Shopify',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        if (!has_permission('Webhooks', 'can_view_all'))
            show_404();

        if (!is_numeric($id))
            show_404();

        $shopifyModel = model(ShopifyModel::class);
        $bankAccountModel = model(BankAccountModel::class);

        $data['shopify_details'] = $shopifyModel->where(['id' => $id, 'company_id' => $this->user_session['company_id']])->get()->getRow();

        if (!is_object($data['shopify_details']))
            show_404();

        echo view('templates/autopay/header',$data);
        echo view('shopify/logs',$data);
        echo view('templates/autopay/footer',$data);

    }

    function  ajax_update_new_shopify(){

        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!has_permission('Webhooks', 'can_add'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền thêm tích hợp"));

        $validation = \Config\Services::validation();
        if(! $this->validate([
            'id' => 'required|integer|is_natural',
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }
        $shopify_id = $this->request->getPost('id');    
        $shopifyModel = model(ShopifyModel::class);
        $shopify_details = $shopifyModel->where(['id'=>$shopify_id,'company_id'=>$this->user_session['company_id']])->get()->getRowArray();
        if(empty($shopify_details)){

            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Không tìm thấy tích hợp này"));
        }

        $data = array(
            'bank_account_id' => $shopify_details['bank_account_id'],
            'api_store_url' => $shopify_details['api_store_url'],
            'api_token' =>  $shopify_details['api_token'],
            'name' =>  $shopify_details['name'],
            'va_id' =>  $shopify_details['va_id'],
            'paycode_prefix' => $shopify_details['paycode_prefix']
        );

        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $shopifyModel = model(ShopifyModel::class);
        // Fetch bank account details
        $bank_account_details = $bankAccountModel
            ->select("tb_autopay_bank_account.account_holder_name, tb_autopay_bank_account.account_number, tb_autopay_bank.brand_name, tb_autopay_bank.full_name, tb_autopay_bank.bin, tb_autopay_bank.code, tb_autopay_bank.logo_path, tb_autopay_bank.icon_path")
            ->join("tb_autopay_bank", "tb_autopay_bank_account.bank_id=tb_autopay_bank.id")
            ->where([
                'tb_autopay_bank_account.id' => $data['bank_account_id'],
                'company_id' => $this->user_session['company_id']
            ])
            ->get()
            ->getRowArray();
    
        if (empty($bank_account_details)) {
           return ['code' => 404 , "message" => "Không tìm thấy ngân hàng!"];
        }
    
        // Initialize account details
        $account_number = $bank_account_details['account_number'];
        $account_holder_name = $bank_account_details['account_holder_name'];
    
        // Check for virtual account details
        if (is_numeric($data['va_id']) && $data['va_id'] > 0) {
            $va_details = $bankSubAccountModel
                ->join("tb_autopay_bank_account", "tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id", "left")
                ->where([
                    'tb_autopay_bank_sub_account.id' => $data['va_id'],
                    'tb_autopay_bank_account.company_id' => $this->user_session['company_id']
                ])
                ->get()
                ->getRowArray();
    
            if (!empty($va_details)) {
                $account_number = $va_details['sub_account'];
                $account_holder_name = $va_details['sub_holder_name'];
            }
        }
    
        // Prepare data for script tag
        $data_json_url = json_encode([
            'bank_bin' => $bank_account_details['bin'],
            'bank_code' => $bank_account_details['code'],
            'account_number' => $account_number,
            'prefix' => $data['paycode_prefix'],
            'bank_brand_name' => $bank_account_details['brand_name'],
            'account_name' => $account_holder_name,
        ]);
        $client =  new ShopifyClient($data['api_store_url'], $data['api_token']);    
        $add_script = $client->addScriptTag($data_json_url);

        $conver_data_res_body = json_decode($add_script['body'], true);
         // Kiểm tra lỗi JSON
         if (json_last_error() !== JSON_ERROR_NONE) {
            log_message("error","Lỗi chuyển đổi json");
            add_user_log(array('data_id'=>0,'company_id' => $this->user_session['company_id'], 'data_type'=>'shopify_add','description'=>'Thêm tích hợp Shopify','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Failed'));
            return $this->response->setJSON(array("status"=>false,"message"=>"Không thể thêm webhook. Vui lòng liên hệ SePay để được hỗ trợ."));
        } 
        $script_tag_id = "";
        if ($add_script['code'] == 201) {
            $script_tag_id = $conver_data_res_body['script_tag']['id']; 
        }

        if(empty($script_tag_id)){
            log_message("error","Lỗi thực hiện api add script");
            add_user_log(array('data_id'=>0,'company_id' => $this->user_session['company_id'], 'data_type'=>'shopify_add','description'=>'Thêm tích hợp Shopify','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Failed'));
            return $this->response->setJSON(array("status"=>false,"message"=>$add_script['message']));
        }

        $data['script_tag_id'] = $script_tag_id;

        $result = $shopifyModel->set($data)->where(["id" =>$shopify_id,"company_id"=>$this->user_session['company_id']])->update();
        
        
        if ($result) {
            set_alert('success','Đồng bộ tích hợp Shopify thành công');
            add_user_log(array('data_id'=>$result,'company_id' => $this->user_session['company_id'], 'data_type'=>'shopify_add','description'=>'Thêm tích hợp Shopify','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
            return $this->response->setJSON(array("status"=>true,"id"=>$result));
        } else {
            add_user_log(array('data_id'=>0,'company_id' => $this->user_session['company_id'], 'data_type'=>'shopify_add','description'=>'Thêm tích hợp Shopify','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Failed'));
            return $this->response->setJSON(array("status"=>false,"message"=>"Không thể đồng bộ webhook. Vui lòng liên hệ SePay để được hỗ trợ."));
        }

    }
}