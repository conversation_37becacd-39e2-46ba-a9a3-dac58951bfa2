<?php

namespace App\Controllers;

use App\Models\ShopModel;
use CodeIgniter\API\ResponseTrait;
use App\Controllers\BaseController;
use CodeIgniter\HTTP\Response;
use App\Features\Store\StoreFeature;
use App\Exceptions\StoreFeatureException;
use App\Features\ShopBilling\ShopBillingFeature;
use App\Filters\ShopBillingFilter;
use App\Models\AcbEnterpriseAccountModel;
use App\Models\BankAccountModel;
use App\Models\BidvEnterpriseAccountModel;
use App\Models\OcbEnterpriseAccountModel;
use App\Models\OcbEnterpriseAppModel;
use CodeIgniter\HTTP\ResponseInterface;

class Store extends BaseController
{
    use ResponseTrait;
    
    protected StoreFeature $storeFeature;
    
    public function __construct()
    {
        $this->storeFeature = new StoreFeature;
    }

    public function index(): void
    {
        $data = [
            'page_title' => 'Cửa hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        $storeModel = model(ShopModel::class);

        $data['store_total'] = $storeModel->where('company_id', $this->user_session['company_id'])->countAllResults();

        echo theme_view('templates/autopay/header',$data);
        echo theme_view('store/index', $data);
        echo theme_view('templates/autopay/footer',$data);
    }
    
    public function details(string $storeId = '')
    {
        $data = [
            'page_title' => 'Cửa hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
        ];
        
        if (! is_numeric($storeId)) {
            show_404();
        }
        
        $startDate = $this->request->getGet('start_date') ?? date('Y-m-d');
        $endDate = $this->request->getGet('end_date');
        
        try {
            $storeFeature = new StoreFeature($storeId);
            $storeFeature->withCompanyContext($this->user_session['company_id']);
            $store = $storeFeature->store;
            
            if (!in_array($this->company_details->role, ['SuperAdmin', 'Admin'])) {
                $availableStores = $storeFeature->getLinkedStaffStores($this->user_details->id);
                
                if (!in_array($store->id, array_column($availableStores, 'id'))) {
                    $store = null;
                }
            }
        } catch (StoreFeatureException $e) {
            $store = null;
        }
        
        if (!$store) {
            show_404();
        }
        
        if (!in_array($this->company_details->role, ['SuperAdmin', 'Admin'])) {
            return redirect()->to(base_url('store/transactions/' . $store->id));
        }

        $data['qrcodes'] = $storeFeature->qrCodeBuilder()->get()->getResult();
        
        if (count($data['qrcodes'])) {
            $data['revenue'] = $storeFeature->getStoreRevenues([$storeId], $startDate, $endDate)[0]['revenue'] ?? 0;
            $data['counter'] = $storeFeature->getStoreCounters([$storeId], $startDate, $endDate)[0] ?? ['total_transaction' => 0, 'in_transaction' => 0, 'out_transaction' => 0];
        } else {
            $data['revenue'] = 0;
            $data['counter'] = ['total_transaction' => 0, 'in_transaction' => 0, 'out_transaction' => 0];
        }
        
        $data['store'] = $storeFeature->store;
        
        echo theme_view('templates/autopay/header',$data);
        echo theme_view('store/details', $data);
        echo theme_view('templates/autopay/footer',$data);
    }
    
    public function qrcodes(string $storeId = ''): void
    {
        $action = $this->request->getGet('action');
        
        if ($action && !in_array($action, ['create'])) {
            $action = null;
        }
        
        if (! is_numeric($storeId)) {
            show_404();
        }
        
        try {
            $storeFeature = new StoreFeature($storeId);
            $storeFeature->withCompanyContext($this->user_session['company_id']);
            
            $store = $storeFeature->store;
            
            if (!in_array($this->company_details->role, ['SuperAdmin', 'Admin'])) {
                $availableStores = $storeFeature->getLinkedStaffStores($this->user_details->id);
                
                if (!in_array($store->id, array_column($availableStores, 'id'))) {
                    $store = null;
                }
            }
        } catch (StoreFeatureException $e) {
            $store = null;
        }
        
        if (!$store) {
            show_404();
        }
        
        $data = [
            'page_title' => 'Cửa hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'store' => $store
        ];
        
        $data['store'] = $storeFeature->store;
        $data['total_qrcode'] = $storeFeature->qrCodeBuilder()->countAllResults();
        
        echo theme_view('templates/autopay/header',$data);
        
        if ($action == 'create' && in_array($this->company_details->role, ['SuperAdmin', 'Admin'])) {
            $this->enableBankBox();
            
            $data['bank_accounts'] = $storeFeature->bankAccountBuilder('linkable')->findAll();
            
            echo theme_view('store/create-qrcode', $data);
        } else {
            echo theme_view('store/qrcodes', $data);
        }

        echo theme_view('templates/autopay/footer',$data);
    }
    
    public function bankaccounts(string $storeId = ''): void
    {
        if (!in_array($this->company_details->role, ['SuperAdmin', 'Admin'])) {
            show_404();
        }
        
        if (! is_numeric($storeId)) {
            show_404();
        }
        
        try {
            $storeFeature = new StoreFeature($storeId);
            $storeFeature->withCompanyContext($this->user_session['company_id']);
            
            $store = $storeFeature->store;
        } catch (StoreFeatureException $e) {
            $store = null;
        }
        
        if (!$store) {
            show_404();
        }
        
        $this->enableBankBox();
        
        $data = [
            'page_title' => 'Cửa hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'store' => $store
        ];
        
        $data['store'] = $storeFeature->store;
        $data['total_qrcode'] = $storeFeature->qrCodeBuilder()->countAllResults();
        $data['bank_accounts'] = $storeFeature->getBankAccounts([$data['store']->id]);
        $data['unlinkable_brand_name'] = $this->getUnlinkableBankBrandNames();
        
        echo theme_view('templates/autopay/header',$data);
        echo theme_view('store/bank-account', $data);
        echo theme_view('templates/autopay/footer',$data);
    }
    
    public function sharing(string $storeId = '')
    {
        if (!in_array($this->company_details->role, ['SuperAdmin', 'Admin'])) {
            show_404();
        }
        
        if (! is_numeric($storeId)) {
            show_404();
        }
        
        try {
            $storeFeature = new StoreFeature($storeId);
            $storeFeature->withCompanyContext($this->user_session['company_id']);
            
            $store = $storeFeature->store;
        } catch (StoreFeatureException $e) {
            $store = null;
        }
        
        if (!$store) {
            show_404();
        }
        
        $data = [
            'page_title' => 'Cửa hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'store' => $store
        ];
        
        $data['store'] = $storeFeature->store;

        $channel = $this->request->getGet('channel');

        if (!$channel || !in_array($channel, ['telegram', 'viber', 'lark-messenger', 'mobile-app', 'speaker'])) {
            return redirect()->to(base_url(sprintf('store/sharing/%s?channel=telegram', $data['store']->id)));
        }

        if ($channel == 'telegram') {
            $data['telegram_total'] = count($storeFeature->getStoreTelegrams([$data['store']->id]));
            $data['linkable_telegram_total'] = count($storeFeature->getStoreTelegrams([$data['store']->id], 'linkable'));
        } else if ($channel == 'viber') {
            $data['viber_total'] = count($storeFeature->getStoreVibers([$data['store']->id]));
            $data['linkable_viber_total'] = count($storeFeature->getStoreVibers([$data['store']->id], 'linkable'));
        } else if ($channel == 'lark-messenger') {
            $data['lark_messenger_total'] = count($storeFeature->getStoreLarkMessengers([$data['store']->id]));
            $data['linkable_lark_messenger_total'] = count($storeFeature->getStoreLarkMessengers([$data['store']->id], 'linkable'));
        } else if ($channel == 'mobile-app') {
            $data['staff_total'] = count($storeFeature->getStoreStaffs([$data['store']->id]));
            $data['linkable_staff_total'] = count($storeFeature->getStoreStaffs([$data['store']->id], 'linkable'));
        } else if ($channel == 'speaker') {
            $data['speaker_total'] = count($storeFeature->getStoreSpeakers([$data['store']->id]));
        }

        echo theme_view('templates/autopay/header',$data);
        echo theme_view('store/sharing', $data);
        echo theme_view('templates/autopay/footer',$data);
    }

    public function ajax_add_store(): Response
    {
        if ($this->request->getMethod(true) !== 'POST') {
            return $this->failNotFound();
        }
        
        if (!in_array($this->company_details->role, ['SuperAdmin', 'Admin'])) {
            return $this->failNotFound();
        }
        
        $rules = [
            'name' => ['required', 'string', 'max_length[100]'],
            'address' => ['required', 'string', 'max_length[255]']
        ];
        
        if (! $this->validateData($this->request->getPost(), $rules)) {
            return $this->failValidationErrors($this->validator->getErrors());
        }
        
        $storeModel = model(ShopModel::class);

        if ($storeModel->where(['company_id' => $this->company_details->id])->countAllResults() >= $this->company_details->shop_limit) {
            return $this->respond(['status' => false, 'message' => 'Bạn đã đạt giới hạn số lượng cửa hàng cho phép, bạn có thể nâng cấp gói hoặc xóa những cửa hàng không còn sử dụng']);
        }
        
        $storeData = [
            'name' => trim(xss_clean($this->request->getVar('name'))),
            'address' => trim(xss_clean($this->request->getVar('address'))),
            'company_id' => $this->user_session['company_id']
        ];
        
        $storeId = $storeModel->insert($storeData);
        
        if (!$storeId) {
            return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng thử lại sau']);
        }
        
        add_user_log(['data_id' => $storeId, 'company_id' => $this->user_session['company_id'], 'data_type' => 'store_add', 'description' => 'Thêm cửa hàng', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);
        
        return $this->respondCreated(['status' => true, 'message' => 'Thêm cửa hàng thành công', 'id' => $storeId]);
    }
    
    public function add($storeId = '')
    {
        $data = [
            'page_title' => 'Thêm cửa hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
        ];
        
        if (!in_array($this->company_details->role, ['SuperAdmin', 'Admin'])) {
            show_404();
        }

        $storeModel = model(ShopModel::class);
        
        $step = $this->request->getGet('step');
        
        if (!is_numeric($step) || $step < 1 || $step > 3) {
            $step = 1;
        } else {
            $step = trim($step);
        }
        
        if ($step > 1 && !is_numeric($step)) {
            show_404();
        }
        
        if ($storeModel->where(['company_id' => $this->company_details->id])->countAllResults() >= $this->company_details->shop_limit && $step === 1) {
            set_alert('error', 'Bạn đã đạt giới hạn số lượng cửa hàng cho phép, bạn có thể nâng cấp gói hoặc xóa những cửa hàng không còn sử dụng');
            return redirect()->to(base_url('company/change_plan'));
        }
        
        if ($storeId) {
            $storeFeature = new StoreFeature($storeId);
            $storeFeature->withCompanyContext($this->user_session['company_id']);
            $data['store'] = $storeFeature->store;
            
            if (!$data['store']) show_404();
        }
        
        if ($step == 2 && isset($storeFeature)) {
            $data['bank_accounts'] = $storeFeature->bankAccountBuilder('linkable')->findAll();
            $this->enableBankBox();
        }
        
        $data['step'] = $step;
        
        echo theme_view('templates/autopay/header',$data);
        echo theme_view('store/add', $data);
        echo theme_view('templates/autopay/footer',$data);
    }

    public function ajax_index_store(): Response
    {
        $storeFeature = new StoreFeature();
        $storeFeature->withCompanyContext($this->user_session['company_id']);
        
        
        if ($this->company_details->role == 'User') {
            $stores = $storeFeature->getLinkedStaffStores($this->user_details->id);
        } else {
            $stores = $storeFeature->getStores();
        }
        
        return $this->respond([
            'data' => $stores,
        ]);
    }

    public function ajax_index_bank_account(): Response
    {
        if (!in_array($this->company_details->role, ['SuperAdmin', 'Admin'])) {
            return $this->failNotFound();
        }
        
        $type = $this->request->getGet('type');
        
        $this->storeFeature->withCompanyContext($this->user_session['company_id']);
        $bankAccounts = $this->storeFeature->bankAccountBuilder($type)->findAll();
        
        return $this->respond([
            'status' => true,
            'data' => $bankAccounts,
        ]);
    }
    
    public function ajax_index_bank_sub_account(): Response
    {
        if (!in_array($this->company_details->role, ['SuperAdmin', 'Admin'])) {
            return $this->failNotFound();
        }
        
        $bankAccountId = $this->request->getGet('bank_account_id');
        $type = $this->request->getGet('type');
        
        if (!is_numeric($bankAccountId)) {
            return $this->failNotFound();
        }
        
        try {
            $this->storeFeature->withCompanyContext($this->user_session['company_id']);
            $this->storeFeature->withBankAccountContext((int) $bankAccountId);
            
            $data = $this->storeFeature->bankAccountContext()->bankSubAccountBuilder($type)->findAll();
        } catch (StoreFeatureException $e) {
            $data = [];
        }
        
        return $this->respond(['data' => $data]);
    }
    
    public function ajax_index_qrcode($storeId = ''): Response
    {
        if (!is_numeric($storeId)) {
            return $this->failNotFound();
        }
        
        try {
            $storeFeature = new StoreFeature($storeId);
            $storeFeature->withCompanyContext($this->user_session['company_id']);
        } catch (StoreFeatureException $e) {
            return $this->failNotFound();
        }
        
        try {
            $data = $storeFeature->qrCodeBuilder()->get()->getResult();
        } catch (StoreFeatureException $e) {
            $data = [];
        }
        
        foreach ($data as &$row) {
            $row->qrcode_url = $storeFeature->resolveQrCodeUrl($row->brand_name, $row->bin, $row->account_number, $row->sub_account, $row->acc_type);
            $row->bank_sub_account_label_alias = $storeFeature->aliasBankSubAccountLabelFriendly($row->brand_name);
        }
        
        return $this->respond(['data' => $data]);
    }
    
    public function ajax_create_qr($storeId = ''): Response
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }
        
        if (!in_array($this->company_details->role, ['SuperAdmin', 'Admin'])) {
            return $this->failNotFound();
        }
        
        if (!is_numeric($storeId)) {
            return $this->failNotFound();
        }
        
        $storeFeature = new StoreFeature($storeId);
        $storeFeature->withCompanyContext($this->user_session['company_id']);
        
        $bankAccountId = $this->request->getPost('bank_account_id');
        $bankSubAccountId = $this->request->getPost('bank_sub_account_id');
        
        if (!is_numeric($bankAccountId)) {
            return $this->failNotFound();
        }
        
        $bankAccountId = (int) $bankAccountId;
        $bankSubAccountId = is_numeric($bankSubAccountId) ? (int) $bankSubAccountId : null;
        
        $storeFeature->withBankAccountContext($bankAccountId);
        $bankAccount = $storeFeature->bankAccountContext()->bankAccount;
        
        try {
            $storeFeature->bankAccountContext()->linkToStore($bankSubAccountId);
            
            set_alert('success', 'Tạo mã QR nhận thanh toán thành công');
            
            return $this->respond(['status' => true]);
        } catch (StoreFeatureException $e) {
            $errCode = $e->getCode();
            $message = $this->defaultExceptionMessage();
            
            if ($errCode == 400) {
                $message = sprintf('Vui lòng chọn một %s', $storeFeature->aliasBankSubAccountLabelFriendly($bankAccount->brand_name));
            }
            
            if ($errCode == 409) {
                $message = sprintf('Mã QR nhận thanh toán đã tồn tại');
            }
            
            return $this->respond(['status' => false, 'message' => $message]);
        }
    }
    
    public function ajax_unlink_qrcode(): Response
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }
        
        if (false) {
            return $this->failNotFound();
        }
        
        $storeId = $this->request->getPost('store_id');
        $qrcodeId = $this->request->getPost('qrcode_id');
        
        if (!is_numeric($storeId) || !is_numeric($qrcodeId)) {
            return $this->failNotFound();
        }
        
        $qrcodeId = (int) $qrcodeId;
        $storeId = (int) $storeId;
        
        try {
            $storeFeature = new StoreFeature($storeId);
            $storeFeature->withCompanyContext($this->user_session['company_id']);
            
            $qrcodeCount = $storeFeature->qrCodeBuilder()->countAllResults();
            
            if ($qrcodeCount === 1) {
                return $this->respond(['status' => false, 'message' => 'Không thể gỡ QR nhận thanh toán cuối cùng của cửa hàng']);
            }
            
            $storeFeature->unlinkQrcode($qrcodeId);
            
            return $this->respond(['status' => true]);
        } catch (StoreFeatureException $e) {
            log_message('error', '[StoreFeature] Failed to unlink QR code: ' . $e->getMessage());
            
            $message = $this->defaultExceptionMessage();
            
            return $this->respond(['status' => false, 'message' => $message]);
        }
    }
    
    public function ajax_get_create_va_modal($id)
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }
        
        if (!in_array($this->company_details->role, ['SuperAdmin', 'Admin'])) {
            return $this->failNotFound();
        }
        
        if (! has_permission('BankAccount', 'can_add') && ! has_permission('BankAccount', 'can_edit')) {
            return $this->failNotFound();
        }
        
        $data = [];
        
        $bankAccount = model(BankAccountModel::class)
            ->select([
                'tb_autopay_bank_account.id',
                'tb_autopay_bank_account.account_number',
                'tb_autopay_bank_account.bank_id',
                'tb_autopay_bank_account.account_holder_name',
                'tb_autopay_bank_account.bank_sms_connected',
                'tb_autopay_bank_account.identification_number',
                'tb_autopay_bank_account.phone_number',
                'tb_autopay_bank.brand_name',
            ])
            ->join('tb_autopay_bank', 'tb_autopay_bank.id = tb_autopay_bank_account.bank_id')
            ->where('tb_autopay_bank_account.company_id', $this->user_session['company_id'])
            ->find($id);

        if (! $bankAccount) {
            return $this->failNotFound();
        }

        $data['bankAccount'] = $bankAccount;

        $realVaSupported = ['OCB', 'BIDV', 'MBBank', 'ACB', 'KienLongBank'];

        switch ($bankAccount->brand_name) {
            case 'ACB':
                $enterpriseAccount = model(AcbEnterpriseAccountModel::class)
                    ->where('bank_account_id', $bankAccount->id)
                    ->first();

                $isEnterpriseAccount = !! $enterpriseAccount;

                if ($isEnterpriseAccount) {
                    $config = config(Acb::class);
                    $data['vac'] = $enterpriseAccount->vac;
                    $data['va_minlength'] = $config->enterpriseVaMinlength ?? 4;
                    $data['va_maxlength'] = $config->enterpriseVaMaxlength ?? 18;
                }

                break;
            case 'BIDV':
                $enterpriseAccount = model(BidvEnterpriseAccountModel::class)
                    ->where('bank_account_id', $bankAccount->id)
                    ->first();
                
                $isEnterpriseAccount = !! $enterpriseAccount;
                $config = config(Bidv::class);
                
                if ($isEnterpriseAccount) {
                    $vaPrefix = $enterpriseAccount->va_prefix ?? $config->vaPrefix;

                    $data['prefixId'] = $vaPrefix . strlen($enterpriseAccount->prefix_id) . $enterpriseAccount->prefix_id . ($vaPrefix == '96' ? '88' : 'VA');
                    $data['vaSuffixMaxLength'] = $config->vaMaxlen - strlen($data['prefixId']);
                    $data['vaNameMinLength'] = $config->vaNameMinlen;
                    $data['vaNameMaxLength'] = $config->vaNameMaxlen;
                    $data['customVaName'] = $enterpriseAccount->custom_va_name ?? false;
                } else {
                    $data['vaPrefix'] = $config->personalVaPrefix;
                    $data['vaSuffixMaxLength'] = $config->personalVaMaxlen - strlen($data['vaPrefix']);
                    $data['vaCharType'] = $config->personalVaCharType;
                    $data['vaMaxLength'] = $config->personalVaMaxlen;
                }

                break;
            case 'OCB':
                $enterpriseAccount = model(OcbEnterpriseAccountModel::class)
                    ->where('bank_account_id', $bankAccount->id)
                    ->first();

                $isEnterpriseAccount = !! $enterpriseAccount;
                $config = config(Ocb::class);
                $data['vaOnlyNumber'] = $config->OcbVAOnlyNumber;
                $data['vaNumberMinLength'] = $config->OcbDefaultVANumberMinLength;

                if ($isEnterpriseAccount) {
                    $data['enterpriseAccount'] = $enterpriseAccount;
                    $data['apps'] = model(OcbEnterpriseAppModel::class)
                        ->where('bank_account_id', $bankAccount->id)
                        ->findAll();
                } else {
                    $data['vaPrefix'] = $config->OcbPrefixVA;
                }
                break;
            default:
                $isEnterpriseAccount = false;
                break;
        }

        if ($bankAccount->bank_sms_connected || ! in_array($bankAccount->brand_name, $realVaSupported) || ($bankAccount->brand_name == 'ACB') && !$isEnterpriseAccount) {
            return $this->response->setJSON([
                'status' => true,
                'data' => view('theme/shop-billing/store/_components/bank-sub-account/virual-account', $data),
            ]);
        }

        $viewName = strtolower($bankAccount->brand_name);

        if ($isEnterpriseAccount) {
            $viewName = 'enterprise/' . $viewName;
        }

        $viewPath = 'theme/shop-billing/store/_components/bank-sub-account/' . $viewName;

        if (! file_exists(APPPATH . 'Views/' . $viewPath . '.php')) {
            return $this->failNotFound();
        }
        
        return $this->respond([
            'status' => true,
            'data' => view($viewPath, $data),
        ]);
    }
    
    public function ajax_get_bank_account_delete_modal($id)
    {
        if ($this->request->getMethod(true) !== 'GET') {
            return $this->failNotFound();
        }
        
        if (! in_array($this->company_details->role, ['Admin', 'SuperAdmin'])) {
            return $this->failNotFound();
        }

        $bankAccount = model(BankAccountModel::class)
            ->select('
                tb_autopay_bank_account.id,
                tb_autopay_bank_account.label,
                tb_autopay_bank_account.account_holder_name,
                tb_autopay_bank_account.account_number,
                tb_autopay_bank.brand_name,
                tb_autopay_bank.short_name,
                tb_autopay_bank.logo_path,
                tb_autopay_bank.icon_path
            ')
            ->where('company_id', $this->user_session['company_id'])
            ->join('tb_autopay_bank', 'tb_autopay_bank.id = tb_autopay_bank_account.bank_id')
            ->find($id);

        if (! $bankAccount) {
            show_404();
        }

        if (! in_array($bankAccount->short_name, $this->getUnlinkableBankBrandNames())) {
            return $this->respond([
                'status' => false,
                'message' => 'Hiện chưa hỗ trợ xóa tài khoản ngân hàng này.',
            ]);
        }

        switch ($bankAccount->short_name) {
            case 'ACB':
                $isEnterpriseAccount = !! model(AcbEnterpriseAccountModel::class)
                    ->where('bank_account_id', $bankAccount->id)
                    ->first();
                break;
            default:
                $isEnterpriseAccount = false;
                break;
        }

        return $this->response->setJSON([
            'status' => true,
            'data' => view('outputdevice/delete-modal/' . strtolower($bankAccount->short_name), [
                'bankAccount' => $bankAccount,
                'isEnterpriseAccount' => $isEnterpriseAccount,
            ]),
        ]);
    }

    public function ajax_edit(string $storeId = '')
    {
        if ($this->request->getMethod(true) !== 'POST') {
            return $this->failNotFound();
        }
        
        if (! in_array($this->company_details->role, ['Admin', 'SuperAdmin'])) {
            return $this->failNotFound();
        }

        if (!is_numeric($storeId)) {
            return $this->failNotFound();
        }
        
        $data = [
            'name' => trim(xss_clean($this->request->getVar('name'))),
            'address' => trim(xss_clean($this->request->getVar('address'))),
        ];
        
        $rules = [
            'name' => ['permit_empty', 'string', 'max_length[100]'],
            'address' => ['permit_empty', 'string', 'max_length[255]']
        ];
        
        if (! $this->validateData($data, $rules)) {
            return $this->failValidationErrors($this->validator->getErrors());
        }

        try {
            $storeFeature = new StoreFeature($storeId);
            $storeFeature->withCompanyContext($this->user_session['company_id']);
            
            $store = $storeFeature->store;
        } catch (\Exception $e) {
            $store = null;
        }
        
        if (!$store) {
            return $this->failNotFound();
        }

        $storeFeature->editStore($data);
        
        return $this->respond([
            'status' => true,
            'message' => 'Cập nhật thông tin thành công'
        ]);
    }
    
    public function ajax_delete(string $storeId = '')
    {
        if ($this->request->getMethod(true) !== 'POST') {
            return $this->failNotFound();
        }

        if (! in_array($this->company_details->role, ['Admin', 'SuperAdmin'])) {
            return $this->failNotFound();
        }

        if (!is_numeric($storeId)) {
            return $this->failNotFound();
        }

        try {
            $shopBillingFeature = new ShopBillingFeature;
            $shopBillingFeature->withCompanyContext($this->user_session['company_id']);

            if ($shopBillingFeature->companyContext()->shopCountInUse() === 1) {
                return $this->respond([
                    'status' => false,
                    'message' => 'Bạn không thể gỡ cửa hàng duy nhất'
                ]);
            }
            
            $storeFeature = new StoreFeature($storeId);
            $storeFeature->withCompanyContext($this->user_session['company_id']);

            $storeFeature->deleteStore($storeId);
            
            add_user_log(['data_id' => $storeId, 'company_id' => $this->user_session['company_id'], 'data_type' => 'store_delete', 'description' => 'Gỡ cửa hàng', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);

            return $this->respond([
                'status' => true,
                'message' => 'Gỡ cửa hàng thành công'
            ]);
        } catch (StoreFeatureException $e) {
            return $this->respond([
                'status' => false,
                'message' => $this->defaultExceptionMessage()
            ]);
        }
    }
    
    public function ajax_transaction(string $storeId = ''): ResponseInterface
    {
        $storeFeature = new StoreFeature();
        $storeFeature->withCompanyContext($this->user_session['company_id']);
        
        $storeIds = $this->request->getGet('store_ids') ?? [];
        
        if ($storeId) {
            $storeIds[] = $storeId;
        }
        
        $filter = [
            'start_date' => $this->request->getGet('start_date'),
            'end_date' => $this->request->getGet('end_date'),
            'amount_in' => $this->request->getGet('amount_in'),
            'amount_out' => $this->request->getGet('amount_out'),
            'bank_account_id' => $this->request->getGet('bank_account_id'),
            'sub_code' => $this->request->getGet('sub_code'),
            'qrcode_ids' => $this->request->getGet('qrcode_ids'),
            'transfer_type' => $this->request->getVar('transfer_type'),
            'transaction_date' => $this->request->getGet('transaction_date'),
        ];

        // Get DataTables parameters
        $datatableParams = [
            'draw' => $this->request->getGet('draw'),
            'start' => $this->request->getGet('start'),
            'length' => $this->request->getGet('length'),
            'search' => [
                'value' => trim(xss_clean($this->request->getGet('search')['value'] ?? ''))
            ],
            'order' => []
        ];
        
        // Process order parameters
        if ($this->request->getGet('order')) {
            foreach ($this->request->getGet('order') as $idx => $order) {
                $datatableParams['order'][] = [
                    'column' => $order['column'] ?? 0,
                    'dir' => $order['dir'] ?? 'asc'
                ];
            }
        }
        
        if (!in_array($this->company_details->role, ['SuperAdmin', 'Admin'])) {
            $availableStores = $storeFeature->getLinkedStaffStores($this->user_details->id);
            
            $storeIds = empty($storeIds) ? array_column($availableStores, 'id') : array_intersect($storeIds, array_column($availableStores, 'id'));
        }
        
        $transactions = $storeFeature->getTransactions($this->user_details->id, $storeIds, $filter, $datatableParams);

        return $this->respond($transactions);
    }
    
    public function transactions(string $storeId = '')
    {
        if (! is_numeric($storeId)) {
            show_404();
        }

        try {
            $storeFeature = new StoreFeature($storeId);
            $storeFeature->withCompanyContext($this->user_session['company_id']);

            $store = $storeFeature->store;
            
            if (!in_array($this->company_details->role, ['SuperAdmin', 'Admin'])) {
                $availableStores = $storeFeature->getLinkedStaffStores($this->user_details->id);
                
                if (!in_array($store->id, array_column($availableStores, 'id'))) {
                    $store = null;
                }
            }
        } catch (StoreFeatureException $e) {
            $store = null;
        }

        if (!$store) {
            show_404();
        }

        $data = [
            'page_title' => 'Giao dịch',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'store' => $store
        ];

        $data['bank_accounts'] = $storeFeature->getBankAccounts([$store->id]);

        $startDate = $this->request->getGet('start_date') ?: date('Y-m-d', strtotime('-30 days'));
        $endDate = $this->request->getGet('end_date') ?: date('Y-m-d');

        $data['date_range'] = [
            'start_date' => $startDate,
            'end_date' => $endDate
        ];

        $data['qrcodes'] = $storeFeature->qrCodeBuilder()->get()->getResult();

        echo theme_view('templates/autopay/header', $data);
        echo theme_view('store/transactions', $data);
        echo theme_view('templates/autopay/footer', $data);
    }
    
    protected function defaultExceptionMessage(): string
    {
        return 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ.';
    }
    
    protected function enableBankBox(): void
    {
        if ($this->shop_billing) {
            config(\Config\BankBox::class)->enabled = true;
            is_bank_box_support(true);
        }
    }
    
    protected function getUnlinkableBankBrandNames(): array
    {
        return ['ACB', 'BIDV', 'MBBank', 'VPB', 'VietinBank'];
    }
}
