<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use CodeIgniter\API\ResponseTrait;
use App\Models\CreditLogModel;

class Creditlog extends BaseController
{
    use ResponseTrait;
    
    public function index(): void
    {
        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])) {
            show_404();
        }

        $creditLogModel = model(CreditLogModel::class);
        $creditLogTotal = $creditLogModel->where('company_id', $this->user_session['company_id'])->countAllResults();

        $data = [
            'page_title' => 'Lịch sử Credit',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'credit_log_total' => $creditLogTotal
        ];
      
        echo theme_view('templates/autopay/header', $data);
        echo theme_view('creditlog/index', $data);
        echo theme_view('templates/autopay/footer', $data);
    }

    public function ajax_logs_list()
    {
        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])) {
            return $this->failNotFound();
        }

        $creditLogModel = model(CreditLogModel::class);

        $builder = $creditLogModel
            ->select(['tb_autopay_credit_log.*', 'tb_autopay_user.email'])
            ->join('tb_autopay_user', 'tb_autopay_user.id = tb_autopay_credit_log.user_id', 'left')
            ->where('tb_autopay_credit_log.company_id', $this->user_session['company_id']);

        // Search
        $search = $this->request->getPost('search')['value'] ?? '';
        if ($search) {
            $builder->groupStart()
                    ->like('tb_autopay_credit_log.description', $search)
                    ->orLike('tb_autopay_user.email', $search)
                    ->orLike('tb_autopay_credit_log.amount_in', $search)
                    ->orLike('tb_autopay_credit_log.amount_out', $search)
                    ->orLike('tb_autopay_credit_log.accumulated', $search)
                    ->groupEnd();
        }

        // Order
        $order = $this->request->getPost('order');
        if ($order) {
            $columns = [
                0 => 'tb_autopay_credit_log.id',
                1 => 'tb_autopay_credit_log.description',
                3 => 'tb_autopay_credit_log.amount_in',
                4 => 'tb_autopay_credit_log.accumulated',
                5 => 'tb_autopay_credit_log.ip',
                6 => 'tb_autopay_credit_log.created_at'
            ];
            
            $builder->orderBy($columns[$order[0]['column']], $order[0]['dir']);
        } else {
            $builder->orderBy('tb_autopay_credit_log.created_at', 'DESC');
        }
        
        if (!empty($this->request->getPost('order'))) {
            foreach ($this->request->getPost('order') as $order) {
                $columnIndex = $order['column'];
                $direction = strtoupper($order['dir']);
                
                $columnMap = [
                    0 => 'tb_autopay_credit_log.id',
                    1 => 'tb_autopay_credit_log.description',
                    3 => 'tb_autopay_credit_log.amount_in',
                    4 => 'tb_autopay_credit_log.accumulated',
                    5 => 'tb_autopay_credit_log.ip',
                    6 => 'tb_autopay_credit_log.created_at'
                ];
                
                if (isset($columnMap[$columnIndex])) {
                    if ($columnMap[$columnIndex] == 'tb_autopay_credit_log.amount_in' && $direction === 'DESC') {
                        $builder->orderBy('tb_autopay_credit_log.amount_in', $direction)->orderBy('tb_autopay_credit_log.amount_out', 'ASC');
                    }
                    
                    if ($columnMap[$columnIndex] == 'tb_autopay_credit_log.amount_in' && $direction === 'ASC') {
                        $builder->orderBy('tb_autopay_credit_log.amount_in', $direction)->orderBy('tb_autopay_credit_log.amount_out', 'DESC');
                    }
                    
                    $builder->orderBy($columnMap[$columnIndex], $direction);
                }
            }
        } else {
            // Default sorting by ID descending
            $builder->orderBy('tb_autopay_sms_parsed.id', 'DESC');
        }

        // Limit & Offset
        $start = $this->request->getPost('start');
        $length = $this->request->getPost('length');
        $builder->limit($length, $start);
        
        // Get results
        $totalFiltered = $builder->countAllResults(false);
        $result = $builder->get()->getResult();
        $totalRecords = $creditLogModel->where('company_id', $this->user_session['company_id'])->countAllResults();

        $data = [];
        foreach ($result as $row) {
            $data[] = [
                'id' => $row->id,
                'user_id' => $row->user_id,
                'email' => $row->email,
                'description' => $row->description,
                'amount_in' => $row->amount_in,
                'amount_out' => $row->amount_out,
                'accumulated' => $row->accumulated,
                'ip' => $row->ip,
                'created_at' => $row->created_at
            ];
        }

        return $this->respond([
            'draw' => $this->request->getPost('draw'),
            'recordsTotal' => $totalRecords,
            'recordsFiltered' => $totalFiltered,
            'data' => $data
        ]);
    }

    protected function defaultExceptionMessage(): string
    {
        return 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ.';
    }
}
