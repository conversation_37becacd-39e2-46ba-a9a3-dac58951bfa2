<?php

namespace App\Controllers;
use App\Models\BankAccountModel;
use App\Models\BankSubAccountModel;
use App\Models\TransactionsModel;
use App\Models\BankModel;
use App\Models\CounterModel;
use App\Models\ShopModel;
use App\Models\BankSubAccountCashflowModel;

use CodeIgniter\Controller;

class Statistics extends BaseController
{ 
    public function cashflow()
    {
        $data = [
            'page_title' => 'Dòng tiền',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        if (!has_permission('StatisticsCashFlow', 'can_view_all')) {
            show_404();
        }

        $data['year'] = $this->request->getGet('year');
        $data['account_number'] = $this->request->getGet('account');
       
        if(!is_numeric($data['account_number']))
            $data['account_number'] = NULL;

        if(!is_numeric($data['year']) || $data['year'] > date("Y"))
            $data['year'] = date("Y");

        $start_date = date( $data['year'] . "-01-01 00:00:00");
         
        $end_date = date( $data['year'] . '-12-31 23:59:59');
  
        $data['start_date'] = $start_date;
        $data['end_date'] = $end_date;

 
        $transactionsModel = slavable_model(TransactionsModel::class, 'Statistics');
        $bankAccountModel = model(BankAccountModel::class);
        $userPermissionBankModel = model(UserPermissionBankModel::class);

        $data['full_view'] = FALSE;

        $all_bank_accounts = $bankAccountModel->select("tb_autopay_bank_account.id,tb_autopay_bank_account.bank_id,tb_autopay_bank_account.account_number,tb_autopay_bank.brand_name,tb_autopay_bank.short_name,tb_autopay_bank_account.label")->join("tb_autopay_bank","tb_autopay_bank.id=tb_autopay_bank_account.bank_id")->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id']])->orderBy('tb_autopay_bank_account.id','asc')->get()->getResult();
 
        if(in_array($this->company_details->role,['Admin','SuperAdmin'])) {
            $data['bank_accounts'] =  $all_bank_accounts;
            $data['full_view'] = TRUE;
        } else {
            $data['bank_accounts'] = $userPermissionBankModel->get_bank_accounts($this->user_details->id);
            if(count($all_bank_accounts) == count($data['bank_accounts']))
                $data['full_view'] = TRUE;

        }

        $where = ['tb_autopay_bank_account.company_id' => $this->user_session['company_id'], 'year(transaction_date)' =>$data['year'], 'tb_autopay_sms_parsed.deleted_at' => NULL];

        if($data['full_view'] === FALSE && count($data['bank_accounts']) > 0 && $data['account_number'] == NULL) {
            $where['tb_autopay_bank_account.id'] = $data['bank_accounts'][0]->id;
            return redirect()->to('statistics/cashflow?year=' . date("Y") . '&account='. $data['bank_accounts'][0]->id);
        }

        $data['bank_account_details'] = NULL;

        if(is_numeric($data['account_number'])) {
            $result = $bankAccountModel->where(['company_id' => $this->user_session['company_id'], 'id' => $data['account_number']])->get()->getRow();
            if(is_object($result)) {
                $where['tb_autopay_bank_account.id'] = $data['account_number'];
                $data['bank_account_details'] = $result;
            }
        }
     
        $transactions = $transactionsModel->select("month(transaction_date) as `m_data`,sum(amount_in) as `sum_in`, sum(amount_out) as `sum_out`")->join("tb_autopay_bank_account","tb_autopay_bank_account.id=tb_autopay_sms_parsed.bank_account_id")->where($where)->groupBy('m_data')->get()->getResult();
        
        $data['monthly_cashflow'] = [];
        for($i = 1; $i<=12; $i++) {
            $data['monthly_cashflow'][$i]['in'] = 0;
            $data['monthly_cashflow'][$i]['out'] = 0;
            $data['monthly_cashflow'][$i]['balance'] = 0;
            foreach($transactions as $transaction) {
                
                if(isset($transaction->m_data) && $transaction->m_data == $i) {
                    $data['monthly_cashflow'][$i]['in'] = $transaction->sum_in;
                    $data['monthly_cashflow'][$i]['out'] = $transaction->sum_out;
                    $data['monthly_cashflow'][$i]['balance'] = $transaction->sum_in - $transaction->sum_out;
                }

            }
        } 

        if($this->channel_partner)
        {
            $data['shop_id'] = $this->request->getGet('shop_id');
            $shopModel = model(ShopModel::class);
            $data['shops'] = $shopModel->where(['company_id' => $this->user_session['company_id']])->get()->getResult();
            
            $builder = slavable_model(TransactionsModel::class, 'Statistics')
                ->select([
                    'SUM(tb_autopay_sms_parsed.amount_in) as sum_in',
                    'SUM(tb_autopay_sms_parsed.amount_out) as sum_out',
                    'MONTH(tb_autopay_sms_parsed.transaction_date) as transaction_month'
                ])
                ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id = tb_autopay_sms_parsed.bank_account_id')
                ->join('tb_autopay_bank_sub_account', 'tb_autopay_bank_sub_account.sub_account = tb_autopay_sms_parsed.sub_account')
                ->join('tb_autopay_bank_shop_link', "tb_autopay_bank_shop_link.bank_account_id = tb_autopay_sms_parsed.bank_account_id AND tb_autopay_bank_shop_link.bank_sub_account_id = tb_autopay_bank_sub_account.id")
                ->join('tb_autopay_shop', 'tb_autopay_bank_shop_link.shop_id = tb_autopay_shop.id')
                ->where('tb_autopay_bank_account.company_id', $this->user_session['company_id'])
                ->where('tb_autopay_sms_parsed.parser_status', 'Success');

            if(is_numeric($data['shop_id'])){
                $builder->where('tb_autopay_bank_shop_link.shop_id', $data['shop_id']);
                $data['shop_details'] = $shopModel
                    ->where([
                        'company_id' => $this->user_session['company_id'],
                        'id' => $data['shop_id'],
                    ])->first();
            }

            if ($start_date && $end_date) {
                $builder->where([
                    'tb_autopay_sms_parsed.transaction_date >=' => $start_date,
                    'tb_autopay_sms_parsed.transaction_date <=' => $end_date
                ]);
            }
            $transactions = $builder->get()->getResult();
            
            for ($i = 1; $i <= 12; $i++) {
                $data['monthly_cashflow_shop'][$i]['in'] = 0;
                $data['monthly_cashflow_shop'][$i]['out'] = 0;
                $data['monthly_cashflow_shop'][$i]['balance'] = 0;
                foreach ($transactions as $transaction) {
                    if (isset($transaction->transaction_month) && $transaction->transaction_month == $i) {
                        $data['monthly_cashflow_shop'][$i]['in'] = $transaction->sum_in;
                        $data['monthly_cashflow_shop'][$i]['out'] = $transaction->sum_out;
                        $data['monthly_cashflow_shop'][$i]['balance'] = $transaction->sum_in - $transaction->sum_out;
                    }
                }
            }
        }


        echo theme_view('templates/autopay/header', $data);
        echo theme_view('statistics/cashflow', $data);
        echo theme_view('templates/autopay/footer', $data);
    }


    public function transaction()
    {
        $data = [
            'page_title' => 'Thống kê',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        if (!has_permission('StatisticsTransaction', 'can_view_all')) {
            show_404();
        }

        $data['year'] = $this->request->getGet('year');
        $data['account_number'] = $this->request->getGet('account');
       
        if(!is_numeric($data['account_number']))
            $data['account_number'] = NULL;

        if(!is_numeric($data['year']) || $data['year'] > date("Y"))
            $data['year'] = date("Y");

        $start_date = date( $data['year'] . "-01-01 00:00:00");
         
        $end_date = date( $data['year'] . '-12-31 23:59:59');
  
        $data['start_date'] = $start_date;
        $data['end_date'] = $end_date;

 
        $transactionsModel = slavable_model(TransactionsModel::class, 'Statistics');
        $bankAccountModel = model(BankAccountModel::class);
        $userPermissionBankModel = model(UserPermissionBankModel::class);

        $data['full_view'] = FALSE;

        $all_bank_accounts = $bankAccountModel->select("tb_autopay_bank_account.id,tb_autopay_bank_account.bank_id,tb_autopay_bank_account.account_number,tb_autopay_bank.brand_name,tb_autopay_bank.short_name,tb_autopay_bank_account.label")->join("tb_autopay_bank","tb_autopay_bank.id=tb_autopay_bank_account.bank_id")->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id']])->orderBy('tb_autopay_bank_account.id','asc')->get()->getResult();
 
        if(in_array($this->company_details->role,['Admin','SuperAdmin'])) {
            $data['bank_accounts'] =  $all_bank_accounts;
            $data['full_view'] = TRUE;
        } else {
            $data['bank_accounts'] = $userPermissionBankModel->get_bank_accounts($this->user_details->id);
            if(count($all_bank_accounts) == count($data['bank_accounts']))
                $data['full_view'] = TRUE;

        }

        $where = ['tb_autopay_bank_account.company_id' => $this->user_session['company_id'], 'year(transaction_date)' =>$data['year'],'tb_autopay_sms_parsed.deleted_at' => NULL];

        if($data['full_view'] === FALSE && count($data['bank_accounts']) > 0 && $data['account_number'] == NULL) {
            $where['tb_autopay_bank_account.id'] = $data['bank_accounts'][0]->id;
            return redirect()->to('statistics/transaction?year=' . date("Y") . '&account='. $data['bank_accounts'][0]->id);
        }

        $data['bank_account_details'] = NULL;

        if(is_numeric($data['account_number'])) {
            $result = $bankAccountModel->where(['company_id' => $this->user_session['company_id'], 'id' => $data['account_number']])->get()->getRow();
            if(is_object($result)) {
                $where['tb_autopay_bank_account.id'] = $data['account_number'];
                $data['bank_account_details'] = $result;
            }
        }
       
        $where['tb_autopay_sms_parsed.amount_in>'] = 0;
        $transactions_in = $transactionsModel->select("month(transaction_date) as `m_data`,count(tb_autopay_sms_parsed.id) as `count_in`")->join("tb_autopay_bank_account","tb_autopay_bank_account.id=tb_autopay_sms_parsed.bank_account_id")->where($where)->groupBy('m_data')->get()->getResult();

        unset($where['tb_autopay_sms_parsed.amount_in>']);
        $where['tb_autopay_sms_parsed.amount_out>'] = 0;
        $transactions_out = $transactionsModel->select("month(transaction_date) as `m_data`,count(tb_autopay_sms_parsed.id) as `count_out`")->join("tb_autopay_bank_account","tb_autopay_bank_account.id=tb_autopay_sms_parsed.bank_account_id")->where($where)->groupBy('m_data')->get()->getResult();
        
        $data['monthly_transaction'] = [];
        for($i = 1; $i<=12; $i++) {
            $data['monthly_transaction'][$i]['in'] = 0;
            $data['monthly_transaction'][$i]['out'] = 0;
            
            foreach($transactions_in as $transaction) {
                
                if(isset($transaction->m_data) && $transaction->m_data == $i) {
                    $data['monthly_transaction'][$i]['in'] = $transaction->count_in;                    
                }

            }
            foreach($transactions_out as $transaction) {
                
                if(isset($transaction->m_data) && $transaction->m_data == $i) {
                    $data['monthly_transaction'][$i]['out'] = $transaction->count_out;                    
                }

            }

            $data['monthly_transaction'][$i]['total'] = $data['monthly_transaction'][$i]['in'] + $data['monthly_transaction'][$i]['out'];    
        } 

        if($this->channel_partner)
        {
            $data['shop_id'] = $this->request->getGet('shop_id');
            
            $shopModel = model(ShopModel::class);
            $data['shops'] = $shopModel->where(['company_id' => $this->user_session['company_id']])->get()->getResult();
            
            $builder = slavable_model(TransactionsModel::class, 'Statistics')
                ->select([
                    'COUNT(CASE WHEN tb_autopay_sms_parsed.amount_in > 0 THEN 1 ELSE NULL END) as count_in',
                    'COUNT(CASE WHEN tb_autopay_sms_parsed.amount_out > 0 THEN 1 ELSE NULL END) as count_out',
                    'MONTH(tb_autopay_sms_parsed.transaction_date) as transaction_month'
                ])
                ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id = tb_autopay_sms_parsed.bank_account_id')
                ->join('tb_autopay_bank_sub_account', 'tb_autopay_bank_sub_account.sub_account = tb_autopay_sms_parsed.sub_account')
                ->join('tb_autopay_bank_shop_link', "tb_autopay_bank_shop_link.bank_account_id = tb_autopay_sms_parsed.bank_account_id AND tb_autopay_bank_shop_link.bank_sub_account_id = tb_autopay_bank_sub_account.id")
                ->join('tb_autopay_shop', 'tb_autopay_bank_shop_link.shop_id = tb_autopay_shop.id')
                ->where('tb_autopay_bank_account.company_id', $this->user_session['company_id'])
                ->where('tb_autopay_sms_parsed.parser_status', 'Success');

            if(is_numeric($data['shop_id'])){
                $builder->where('tb_autopay_bank_shop_link.shop_id', $data['shop_id']);
                $data['shop_details'] = $shopModel
                    ->where([
                        'company_id' => $this->user_session['company_id'],
                        'id' => $data['shop_id'],
                    ])->first();
            }

            if ($start_date && $end_date) {
                $builder->where([
                    'tb_autopay_sms_parsed.transaction_date >=' => $start_date,
                    'tb_autopay_sms_parsed.transaction_date <=' => $end_date
                ]);
            }

            $transactions = $builder->groupBy('transaction_month')->get()->getResult();

            $data['monthly_transaction_shop'] = [];
            for ($i = 1; $i <= 12; $i++) {
                $data['monthly_transaction_shop'][$i]['in'] = 0;
                $data['monthly_transaction_shop'][$i]['out'] = 0;
                $data['monthly_transaction_shop'][$i]['total'] = 0;
                foreach ($transactions as $transaction) {
                    if (isset($transaction->transaction_month) && $transaction->transaction_month == $i) {
                        $data['monthly_transaction_shop'][$i]['in'] = $transaction->count_in;
                        $data['monthly_transaction_shop'][$i]['out'] = $transaction->count_out;
                        $data['monthly_transaction_shop'][$i]['total'] = $transaction->count_in + $transaction->count_out;
                    }
                }
            }
        }

        echo theme_view('templates/autopay/header', $data);
        echo theme_view('statistics/transaction', $data);
        echo theme_view('templates/autopay/footer', $data);
    }

    public function counter() {
        $data = [
            'page_title' => 'Bộ đếm',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        if (!has_permission('StatisticsTransaction', 'can_view_all')) {
            show_404();
        }

        $requestData = $this->request->getGet(['period', 'start_date', 'end_date']);

        if (! $this->validateData($requestData, [
            'period' => 'permit_empty|string|in_list[today,week,month,year,quarter,custom]',
            'start_date' => 'permit_empty|string|valid_date[Y-m-d]',
            'end_date' => 'permit_empty|string|valid_date[Y-m-d]',
        ])) {
            return redirect()->to(base_url('statistics/counter'));
        }

        $data['period'] = $requestData['period'];

        if($data['period'] == 'custom') {
            $input_start = $requestData['start_date'];
            $input_end = $requestData['end_date'];

            if(!$input_end || !$input_start)
                $data['period'] = 'month';
            else {
                $start_date_obj = date_create_from_format('Y-m-d', $input_start);
                $end_date_obj = date_create_from_format('Y-m-d', $input_end);

                if(is_object($start_date_obj) && is_object($end_date_obj)) {
                    $start_date = $start_date_obj->format("Y-m-d");
                    $end_date = $end_date_obj->format("Y-m-d");
                    $data['period_text'] = '';
                    $data['period_days'] = round( (strtotime($end_date) - strtotime($start_date)) / (60 * 60 * 24) );
                    if(strtotime($start_date) > strtotime($end_date))
                        $data['period'] = 'month';
                
                } else
                    $data['period'] = 'month';
            
            }

        }
    

        if(!in_array($data['period'], ['month','year','quarter','week','custom']))
            $data['period'] = 'month';

        if($data['period'] == 'month') {
            $start_date = date("Y-m-01");
            $data['period_text'] = 'tháng này';
            $end_date = date('Y-m-d');
        } else if($data['period'] == 'week') {
            $start_date = date('Y-m-d', strtotime('monday this week'));
            $data['period_text'] = 'tuần này';
            $end_date = date('Y-m-d');
        } else if($data['period'] == 'quarter') {
            $start_date = firstDayOf('quarter')->format("Y-m-d");
            $data['period_text'] = 'quý này';
        $end_date = date('Y-m-d');
        } else if($data['period'] == 'year') {
            $start_date = date("Y-01-01");
            $data['period_text'] = 'năm này';
            
            $end_date = date('Y-m-d');
        }

        $data['start_date'] = $start_date;
        $data['end_date'] = $end_date;

        $data['period_days'] = round( (strtotime($end_date) - strtotime($start_date)) / (60 * 60 * 24) );
        $counterModel = model(CounterModel::class);

        $data['counter_data'] = $counterModel->where(['company_id' => $this->user_session['company_id'], 'date>=' => $start_date, 'date<=' => $end_date])->orderBy("date",'DESC')->get()->getResult();

    
 
        echo theme_view('templates/autopay/header', $data);
        echo view('statistics/counter', $data);
        echo theme_view('templates/autopay/footer', $data);
    }
    
    public function daily_cashflow()
    {
        $data = [
            'page_title' => 'Dòng tiền',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        if (!has_permission('StatisticsCashFlow', 'can_view_all')) {
            show_404();
        }
 
        $requestData = $this->request->getGet(['period', 'start_date', 'end_date']);

        if (! $this->validateData($requestData, [
            'period' => 'permit_empty|string|in_list[week,month,year,quarter,custom]',
            'start_date' => 'permit_empty|string|valid_date[d/m/Y]',
            'end_date' => 'permit_empty|string|valid_date[d/m/Y]',
        ])) {
            return redirect()->to(base_url('statistics/daily_cashflow'));
        }

        $data['period'] = $this->request->getGet('period');

        if($data['period'] == 'custom') {
            $input_start = $this->request->getGet('start_date');
            $input_end = $this->request->getGet('end_date');

            if(!$input_end || !$input_start)
                $data['period'] = 'month';
            else {
                $start_date_obj = date_create_from_format('d/m/Y', $input_start);
                $end_date_obj = date_create_from_format('d/m/Y', $input_end);

                if(is_object($start_date_obj) && is_object($end_date_obj)) {
                    $start_date = $start_date_obj->format("Y-m-d 00:00:00");
                    $end_date = $end_date_obj->format("Y-m-d 23:59:59");
                    $data['period_text'] = '';
                    $data['period_days'] = round( (strtotime($end_date) - strtotime($start_date)) / (60 * 60 * 24) );
                    if(strtotime($start_date) > strtotime($end_date))
                        $data['period'] = 'month';
                
                } else
                    $data['period'] = 'month';
            
            }

        }
    

        if(!in_array($data['period'], ['month','year','quarter','week','custom']))
            $data['period'] = 'month';

        if($data['period'] == 'month') {
            $start_date = date("Y-m-01 00:00:00");
            $data['period_text'] = 'tháng này';
            $end_date = date('Y-m-d H:i:s');
        } else if($data['period'] == 'week') {
            $start_date = date('Y-m-d 00:00:00', strtotime('monday this week'));
            $data['period_text'] = 'tuần này';
            $end_date = date('Y-m-d H:i:s');
        } else if($data['period'] == 'quarter') {
            $start_date = firstDayOf('quarter')->format("Y-m-d 00:00:00");
            $data['period_text'] = 'quý này';
        $end_date = date('Y-m-d H:i:s');
        } else if($data['period'] == 'year') {
            $start_date = date("Y-01-01 00:00:00");
            $data['period_text'] = 'năm này';
            
            $end_date = date('Y-m-d H:i:s');
        }

      
        $data['start_date'] = $start_date;
        $data['end_date'] = $end_date;

        $bankAccountModel = model(BankAccountModel::class);
        $transactionsModel = slavable_model(TransactionsModel::class, 'Statistics');
        $userPermissionBankModel = model(UserPermissionBankModel::class);

        $data['account_number'] = $this->request->getGet('account');
       
        if(!is_numeric($data['account_number']))
            $data['account_number'] = NULL;

            $data['full_view'] = FALSE;

        $all_bank_accounts = $bankAccountModel->select("tb_autopay_bank_account.id,tb_autopay_bank_account.bank_id,tb_autopay_bank_account.account_number,tb_autopay_bank.brand_name,tb_autopay_bank.short_name,tb_autopay_bank_account.label")->join("tb_autopay_bank","tb_autopay_bank.id=tb_autopay_bank_account.bank_id")->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id']])->orderBy('tb_autopay_bank_account.id','asc')->get()->getResult();
 
        if(in_array($this->company_details->role,['Admin','SuperAdmin'])) {
            $data['bank_accounts'] =  $all_bank_accounts;
            $data['full_view'] = TRUE;
        } else {
            $data['bank_accounts'] = $userPermissionBankModel->get_bank_accounts($this->user_details->id);
            if(count($all_bank_accounts) == count($data['bank_accounts']))
                $data['full_view'] = TRUE;

        }

        $where = ['tb_autopay_bank_account.company_id' => $this->user_session['company_id'],'tb_autopay_sms_parsed.parser_status' => 'Success','tb_autopay_sms_parsed.transaction_date>=' => $start_date, 'tb_autopay_sms_parsed.transaction_date<=' => $end_date,'tb_autopay_sms_parsed.deleted_at' => NULL];

        if($data['full_view'] === FALSE && count($data['bank_accounts']) > 0 && $data['account_number'] == NULL) {
            $where['tb_autopay_bank_account.id'] = $data['bank_accounts'][0]->did;
            return redirect()->to('statistics/daily_cashflow?period=custom&start_date=' . $start_date . '&end_date=' . $end_date . '&account='. $data['bank_accounts'][0]->id);
        }

        $data['bank_account_details'] = NULL;

        if(is_numeric($data['account_number'])) {
            $result = $bankAccountModel->where(['company_id' => $this->user_session['company_id'], 'id' => $data['account_number']])->get()->getRow();
            if(is_object($result)) {
                $where['tb_autopay_bank_account.id'] = $data['account_number'];
                $data['bank_account_details'] = $result;
            }
        }

        $data['results'] = $transactionsModel->select("sum(tb_autopay_sms_parsed.amount_in) as `sum_in`, sum(tb_autopay_sms_parsed.amount_out) as `sum_out`, date(tb_autopay_sms_parsed.transaction_date) as `trans_date`")
            ->join("tb_autopay_bank_account","tb_autopay_bank_account.id=tb_autopay_sms_parsed.bank_account_id")
            ->where($where)->groupBy('trans_date')->orderBy('trans_date','DESC')->get()->getResult();

        $data['daily_cashflow'] = [];

        foreach($data['results'] as $result) {
            $data['daily_cashflow'][$result->trans_date]['in'] = $result->sum_in;
            $data['daily_cashflow'][$result->trans_date]['out'] = $result->sum_out;
            $data['daily_cashflow'][$result->trans_date]['balance'] = $result->sum_in - $result->sum_out;
        }

        //ksort($data['daily_cashflow']);
       // var_dump($data['daily_cashflow']);

       if($this->channel_partner)
        {
            $data['shop_id'] = $this->request->getGet('shop_id');
            $shopModel = model(ShopModel::class);
            $data['shops'] = $shopModel->where(['company_id' => $this->user_session['company_id']])->get()->getResult();
            
            $builder = slavable_model(TransactionsModel::class, 'Statistics')
                ->select([
                    'SUM(tb_autopay_sms_parsed.amount_in) as sum_in',
                    'SUM(tb_autopay_sms_parsed.amount_out) as sum_out',
                    'DATE(tb_autopay_sms_parsed.transaction_date) as transaction_date'
                ])
                ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id = tb_autopay_sms_parsed.bank_account_id')
                ->join('tb_autopay_bank_sub_account', 'tb_autopay_bank_sub_account.sub_account = tb_autopay_sms_parsed.sub_account')
                ->join('tb_autopay_bank_shop_link', "tb_autopay_bank_shop_link.bank_account_id = tb_autopay_sms_parsed.bank_account_id AND tb_autopay_bank_shop_link.bank_sub_account_id = tb_autopay_bank_sub_account.id")
                ->join('tb_autopay_shop', 'tb_autopay_bank_shop_link.shop_id = tb_autopay_shop.id')
                ->where('tb_autopay_bank_account.company_id', $this->user_session['company_id'])
                ->where('tb_autopay_sms_parsed.parser_status', 'Success');

            if(is_numeric($data['shop_id'])){
                $builder->where('tb_autopay_bank_shop_link.shop_id', $data['shop_id']);
                $data['shop_details'] = $shopModel
                    ->where([
                        'company_id' => $this->user_session['company_id'],
                        'id' => $data['shop_id'],
                    ])->first();
            }

            if ($start_date && $end_date) {
                $builder->where([
                    'tb_autopay_sms_parsed.transaction_date >=' => $start_date,
                    'tb_autopay_sms_parsed.transaction_date <=' => $end_date
                ]);
            }
            
            $transactions = $builder->groupBy('transaction_date')->orderBy('transaction_date','DESC')->get()->getResult();
            
                $data['daily_cashflow_shop'] = [];
                foreach ($transactions as $transaction) {
                    if (isset($transaction->transaction_date)) {
                        $data['daily_cashflow_shop'][$transaction->transaction_date]['in'] = $transaction->sum_in;
                        $data['daily_cashflow_shop'][$transaction->transaction_date]['out'] = $transaction->sum_out;
                        $data['daily_cashflow_shop'][$transaction->transaction_date]['balance'] = $transaction->sum_in - $transaction->sum_out;
                    }
                }
        }
         
        echo theme_view('templates/autopay/header', $data);
        echo theme_view('statistics/daily_cashflow', $data);
        echo theme_view('templates/autopay/footer', $data);
    }
}
