<?php

namespace App\Controllers;
use App\Models\UserModel;
use CodeIgniter\Controller;
use App\Models\WebhooksModel;
use App\Models\BankAccountModel;
use CodeIgniter\Database\RawSql;
use App\Models\TransactionsModel;

use CodeIgniter\API\ResponseTrait;
use App\Models\BankSubAccountModel;
use App\Models\NotificationTelegramModel;
use App\Models\ShopModel;
use App\Models\NotificationTelegramQueueModel;

class Notificationtelegram extends BaseController
{
    use ResponseTrait;
    
    public function index()
    { 
        $data = [
            'page_title' => 'Telegram',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        if(!has_permission('NotificationTelegram', 'can_view_all'))
            show_404();

        $notificationTelegramModel = model(NotificationTelegramModel::class);
        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $webhooksModel = model(WebhooksModel::class);

        $data['bank_accounts'] = $bankAccountModel->select("tb_autopay_bank_account.id, tb_autopay_bank.brand_name, tb_autopay_bank_account.account_holder_name, tb_autopay_bank_account.account_number")->join("tb_autopay_bank","tb_autopay_bank.id=tb_autopay_bank_account.bank_id","left")->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id']])->get()->getResult();
        
        $data['bank_sub_accounts'] = $bankSubAccountModel->select("tb_autopay_bank_sub_account.id, tb_autopay_bank.brand_name, tb_autopay_bank_account.account_holder_name, tb_autopay_bank_account.account_number,tb_autopay_bank_sub_account.sub_account, tb_autopay_bank_sub_account.label")->join("tb_autopay_bank_account","tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id")->join("tb_autopay_bank","tb_autopay_bank.id=tb_autopay_bank_account.bank_id","left")->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id']])->get()->getResult();
        
        $data['webhooks_count'] = $webhooksModel->where(['company_id' => $this->user_session['company_id']])->countAllResults();

        $session = service('session');
        $session->remove('notification_telegram_integration');

        echo theme_view('templates/autopay/header',$data);
        echo theme_view('notificationtelegram/index',$data);
        echo theme_view('templates/autopay/footer',$data);

    }

    public function ajax_telegrams_list() {
     
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!has_permission('NotificationTelegram', 'can_view_all'))
            show_404();
 
        $notificationTelegramModel = model(NotificationTelegramModel::class);

        $telegrams = $notificationTelegramModel->getDatatables($this->user_session['company_id']);
         
        $data = array();

        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');

        foreach ($telegrams as $telegram) {

            $no++;
            $row = array();
            
            $row[] = $no;
            $row[] = esc($telegram->id);
            $row[] = "<b>" . esc($telegram->description) . "</b>";
          
            $event_text = '';
            if($telegram->transaction_type == "In_only")
                $event_text = $event_text. "<span class='text-info'>Khi có tiền vào</span>";
            else if($telegram->transaction_type == "Out_only")
                $event_text = $event_text. "<span class='text-info'>Khi có tiền ra</span>";
            else if($telegram->transaction_type == "All")
                $event_text = $event_text. "<span class='text-info'>Khi có tiền vào hoặc Tiền ra</span>";
             
            $row[] = $event_text;

            $condition_text = '<ul>';

            if(strlen($telegram->account_number) > 0)
                $condition_text = $condition_text. "<li><span class='text-info'>[Và] Khi tài khoản chính là <b>".esc($telegram->account_number) . ' ' . esc($telegram->brand_name)."</b></span> </li>";

            if(strlen($telegram->sub_account) > 0)
                $condition_text = $condition_text. "<li><span class='text-info'>[Và] Khi tài khoản phụ là <b>". esc($telegram->sub_account)."</b></span> </li>";

            
            if($telegram->webhook_type == "Success")
                $condition_text = $condition_text. "<li><span class='text-info'>[Và] Khi WebHooks thành công</span></li>";
            else if($telegram->webhook_type == "Failed")
                $condition_text = $condition_text. "<li><span class='text-info'>[Và] Khi WebHooks thất bại</span></li>";

            if($telegram->verify_payment == "Success")
                $condition_text = $condition_text. "<li><span class='text-info'>[Và] Khi WebHooks xác thực thanh toán thành công</span></li>";
            else if($telegram->verify_payment == "Failed")
                $condition_text = $condition_text. "<li><span class='text-info'>[Và] Khi WebHooks xác thực thanh toán thất bại</span></li>";
            else if($telegram->verify_payment == "Success_Or_Failed")
                $condition_text = $condition_text. "<li><span class='text-info'>[Và] Khi WebHooks xác thực thanh toán Thất bại hoặc Thành công</span></li>";
            
            if($telegram->ignore_phrases)
                $condition_text = $condition_text. "<li><span class='text-success'>[Bỏ qua] Khi WebHooks Có các từ: </span> " . esc(character_limiter($telegram->ignore_phrases,50,'...')) . "</li>";
             
            $condition_text =  $condition_text .'</ul>';

            $row[] = $condition_text;
            $row[] = esc($telegram->chat_id);

          
            if($telegram->active == 1)
                $row[] = "<span class='text-success'>Kích hoạt</span>";
            else
                $row[] = "<span class='text-secondary'>Tạm ngưng</span>";
            //$row[] = esc($telegram->created_at);
            $action_text = "";
            if(has_permission('NotificationTelegram', 'can_edit'))
                $action_text = "<a href='javascript:;' onclick='edit_telegram(" . $telegram->id. ")' class='btn btn-sm btn-outline-warning ms-2 me-2 mt-2'><i class='bi bi-pencil'></i> Sửa</a> <a href='".base_url('notificationtelegram/edit_message/' . $telegram->id)."' class='btn btn-sm btn-outline-info ms-2 me-2 mt-2'><i class='bi bi-chat-left-text'></i> Nội dung</a>";
            
            if(has_permission('NotificationTelegram', 'can_delete'))
                $action_text = $action_text . "<a href='javascript:;' onclick='delete_telegram(" . $telegram->id. ")' class='btn btn-sm btn-outline-danger ms-2 mt-2'><i class='bi bi-trash3'></i> Xóa</a>";

            $row[] = $action_text;
       
            $data[] = $row;
        }
 
        $output = array(
            "draw" => $draw,
            "recordsTotal" => $notificationTelegramModel->countAll($this->user_session['company_id']),
            "recordsFiltered" => $notificationTelegramModel->countFiltered($this->user_session['company_id']),
            "data" => $data,
        );
        return $this->response->setJSON($output);
    }

    public function ajax_telegram_add() {

        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!has_permission('NotificationTelegram', 'can_add'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền thêm"));

 
        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'bank_account_id' => 'integer|is_natural',
            //'sub_account_id' => 'integer|is_natural',
            'transaction_type' => "required|in_list[All,In_only,Out_only]",
            //'webhook_type' => "required|in_list[All,Success,Failed,No]",
            //'verify_payment' => "required|in_list[Skip,Success,Failed,Success_Or_Failed,No]",
            'contains_content' => ['label' => 'Khi nội dung thanh toán có từ', 'rules' => "max_length[5000]"],
            'description' => ['label' => 'Đặt tên', 'rules' => "required|min_length[2]|max_length[1000]"],
            'chat_id' => ['label' => 'Telegram Chat ID', 'rules' =>"required|min_length[3]|max_length[50]"],
            
            //'hide_accumulated' => "required|in_list[0,1]",
            //'hide_details_link' => "required|in_list[0,1]",
            'ignore_phrases' => ['label' => 'Bỏ qua nếu nội dung thanh toán có từ', 'rules' => "max_length[5000]"],
            'active' => "required|in_list[0,1]"
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        } 
        
        $bankAccountModel = model(BankAccountmodel::class);
        $bankSubAccountModel = model(BankSubAccountmodel::class);
        $notificationTelegramModel = model(NotificationTelegramModel::class);

        $bank_account_id = $this->request->getPost('bank_account_id');
        $sub_account_id = $this->request->getPost('sub_account_id');

        if(is_numeric($bank_account_id) && $bank_account_id > 0) {
            $bankAccountModel = model(BankAccountmodel::class);
            $bank_account = $bankAccountModel->where(['company_id' => $this->user_session['company_id'], 'id'=>$bank_account_id])->countAllResults();
            if($bank_account != 1)
                return $this->response->setJSON(array('status'=>FALSE,'message'=>'Không tìm thấy tài khoản ngân hàng chính mà bạn chọn'));    
        }

        $chat_id = trim($this->request->getVar('chat_id'));

        if(substr($chat_id,0,1) != "-")
            return $this->response->setJSON(array('status'=>FALSE,'message'=>'Chat ID phải bắt đầu bằng dấu trừ (-). Ví dụ -********'));    

        $amount_in_less_than_equal_to = $this->request->getPost('amount_in_less_than_equal_to');
        $amount_in_great_than_equal_to = $this->request->getPost('amount_in_great_than_equal_to');

        if(!is_numeric($amount_in_less_than_equal_to) || $amount_in_less_than_equal_to <= 0)
            $amount_in_less_than_equal_to = 0;

        if(!is_numeric($amount_in_great_than_equal_to) || $amount_in_great_than_equal_to <= 0)
            $amount_in_great_than_equal_to = 0;
            
        if($amount_in_less_than_equal_to != 0 && $amount_in_great_than_equal_to != 0) {
            if($amount_in_great_than_equal_to > $amount_in_less_than_equal_to) {
                return $this->response->setJSON(array('status'=>FALSE,'message'=>"Vui lòng nhập giá trị 'Tiền vào lớn hơn hoặc bằng' phải nhỏ hơn điều kiện 'Tiền vào nhỏ hơn hoặc bằng'"));

            }
        }

        $data = array(
            'company_id' => $this->user_session['company_id'],
            'bank_account_id' => $bank_account_id,
            'transaction_type' => $this->request->getVar('transaction_type'),
            'contains_content' => $this->request->getVar('contains_content'),
            'chat_id' => $chat_id,
            'description' => xss_clean($this->request->getVar('description')),
            'amount_in_less_than_equal_to' => $amount_in_less_than_equal_to,
            'amount_in_great_than_equal_to' => $amount_in_great_than_equal_to,
            'hide_accumulated' => 0,
            'hide_details_link' => 1,
            'ignore_phrases' => trim($this->request->getVar('ignore_phrases')),
            'active' => $this->request->getVar('active'),
        );

        $message_thread_id = trim($this->request->getVar('message_thread_id'));
        if(is_numeric($message_thread_id))
            $data['message_thread_id'] = $message_thread_id;
      
        $webhook_type = $this->request->getVar('webhook_type');
        $verify_payment = $this->request->getVar('verify_payment');

        if(!in_array($webhook_type, ['All','Success','Failed','No']))
            $data['webhook_type'] = "No";
        else
            $data['webhook_type'] = $webhook_type;

        if(!in_array($verify_payment, ['Skip','Success','Failed','Success_Or_Failed','No']))
            $data['verify_payment'] = "Skip";
        else
            $data['verify_payment'] = $verify_payment;


        if(is_numeric($sub_account_id) && $sub_account_id > 0) {
            $sub_account = $bankSubAccountModel->join("tb_autopay_bank_account","tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id")->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id'], 'tb_autopay_bank_sub_account.id'=>$sub_account_id])->countAllResults();
            if($sub_account != 1)
                return $this->response->setJSON(array('status'=>FALSE,'message'=>'Không tìm thấy tài khoản ngân hàng phụ mà bạn chọn'));
        
        }

        if(is_numeric($sub_account_id) && $sub_account_id > 0)
            $data['sub_account_id'] = $sub_account_id;
        else
            $data['sub_account_id'] = 0;
       
        $notificationTelegramModel = model(NotificationTelegramModel::class);
        $result = $notificationTelegramModel->insert($data);
        
        if($result) {
            add_user_log(array('data_id'=>$result,'company_id' => $this->user_session['company_id'], 'data_type'=>'notification_telegram_add','description'=>'Thêm tích hợp thông báo Telegram','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
            
           // set_alert('success','Tạo tích hợp Telegram thành công');

            return $this->response->setJSON(array("status"=>true,'id' => $result));
        } else {
            add_user_log(array('data_id'=>0,'company_id' => $this->user_session['company_id'], 'data_type'=>'notification_telegram_add','description'=>'Thêm tích hợp thông báo Telegram','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Failed'));
            return $this->response->setJSON(array("status"=>false,"message"=>"Không thể thêm điều kiện này. Vui lòng liên hệ SePay để được hỗ trợ."));
        }
    
    }


    public function ajax_get_telegram($id='') {
        
        if(!has_permission('NotificationTelegram', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền sửa dữ liệu này"));

        if(!is_numeric($id)) 
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"ID dữ liệu không hợp lệ"));
        
        $notificationTelegramModel = model(NotificationTelegramModel::class);
        
        $result = $notificationTelegramModel->where(['id'=>$id, 'company_id' => $this->user_session['company_id']])->get()->getRow();
        
        if($result)
            return $this->response->setJSON(["status"=>TRUE, "data"=>$result]);
        else
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy dữ liệu này"));
    }

    public function ajax_telegram_update() {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!has_permission('NotificationTelegram', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền sửa dữ liệu"));

 
        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'bank_account_id' => 'integer|is_natural',
            //'sub_account_id' => 'integer|is_natural',
            'id' => 'required|integer|is_natural',
            'transaction_type' => "required|in_list[All,In_only,Out_only]",
           // 'webhook_type' => "required|in_list[All,Success,Failed,No]",
            'contains_content' => ['label' => 'Khi nội dung thanh toán có từ', 'rules' => "max_length[5000]"],
           // 'verify_payment' => "required|in_list[Skip,Success,Failed,Success_Or_Failed,No]",
            'description' => ['label' => 'Đặt tên', 'rules' => "required|min_length[2]|max_length[1000]"],
            'chat_id' => ['label' => 'Telegram Chat ID', 'rules' =>"required|min_length[3]|max_length[50]"],
            //'hide_accumulated' => "required|in_list[0,1]",
            //'hide_details_link' => "required|in_list[0,1]",
            'ignore_phrases' => ['label' => 'Bỏ qua nếu nội dung thanh toán có từ', 'rules' => "max_length[5000]"],
            'active' => "required|in_list[0,1]"
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }

        $chat_id = trim($this->request->getVar('chat_id'));

        if(substr($chat_id,0,1) != "-")
            return $this->response->setJSON(array('status'=>FALSE,'message'=>'Chat ID phải bắt đầu bằng dấu trừ (-). Ví dụ -********'));    


        $bankAccountModel = model(BankAccountmodel::class);
        $bankSubAccountModel = model(BankSubAccountmodel::class);
        $notificationTelegramModel = model(NotificationTelegramModel::class);

        $bank_account_id = $this->request->getPost('bank_account_id');
        $sub_account_id = $this->request->getPost('sub_account_id');

        if(is_numeric($bank_account_id) && $bank_account_id > 0) {
            $bankAccountModel = model(BankAccountmodel::class);
            $bank_account = $bankAccountModel->where(['company_id' => $this->user_session['company_id'], 'id'=>$bank_account_id])->countAllResults();
            if($bank_account != 1)
                return $this->response->setJSON(array('status'=>FALSE,'message'=>'Không tìm thấy tài khoản ngân hàng chính mà bạn chọn'));    
        }
      
        $sub_account_id = $this->request->getPost('sub_account_id');

        if(is_numeric($sub_account_id) && $sub_account_id > 0) {
            $sub_account = $bankSubAccountModel->join("tb_autopay_bank_account","tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id")->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id'], 'tb_autopay_bank_sub_account.id'=>$sub_account_id])->countAllResults();
            if($sub_account != 1)
                return $this->response->setJSON(array('status'=>FALSE,'message'=>'Không tìm thấy tài khoản ngân hàng phụ mà bạn chọn'));
           
        
        }
        
        $telegram_id = $this->request->getPost('id');
        
        $telegram_details = $notificationTelegramModel->where(["id" =>$telegram_id,"company_id"=>$this->user_session['company_id']])->get()->getRow();

        if(!is_object($telegram_details))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>'Không tìm thấy dữ liệu cần cập nhật'));


        $amount_in_less_than_equal_to = $this->request->getPost('amount_in_less_than_equal_to');
        $amount_in_great_than_equal_to = $this->request->getPost('amount_in_great_than_equal_to');

        if(!is_numeric($amount_in_less_than_equal_to) || $amount_in_less_than_equal_to <= 0)
            $amount_in_less_than_equal_to = 0;

        if(!is_numeric($amount_in_great_than_equal_to) || $amount_in_great_than_equal_to <= 0)
            $amount_in_great_than_equal_to = 0;
            
        if($amount_in_less_than_equal_to != 0 && $amount_in_great_than_equal_to != 0) {
            if($amount_in_great_than_equal_to > $amount_in_less_than_equal_to) {
                return $this->response->setJSON(array('status'=>FALSE,'message'=>"Vui lòng nhập giá trị 'Số tiền lớp hơn hoặc bằng' phải nhỏ hơn điều kiện 'số tiền nhỏ hơn hoặc bằng'"));

            }
        }

        $data = array(
            'bank_account_id' => $this->request->getPost('bank_account_id'),
           // 'sub_account_id' => $this->request->getPost('sub_account_id'),
            'transaction_type' => $this->request->getVar('transaction_type'),
            //'webhook_type' => $this->request->getVar('webhook_type'),
            //'verify_payment' => $this->request->getVar('verify_payment'),
            'contains_content' => $this->request->getVar('contains_content'),
            'description' => xss_clean($this->request->getVar('description')),
            'chat_id' => trim($this->request->getVar('chat_id')),
            //'hide_accumulated' => $this->request->getVar('hide_accumulated'),
            //'hide_details_link' => $this->request->getVar('hide_details_link'),
            'amount_in_less_than_equal_to' => $amount_in_less_than_equal_to,
            'amount_in_great_than_equal_to' => $amount_in_great_than_equal_to,
            'ignore_phrases' => trim($this->request->getVar('ignore_phrases')),
            'active' => $this->request->getVar('active'),
        );

        $message_thread_id = trim($this->request->getVar('message_thread_id'));
        if(is_numeric($message_thread_id))
            $data['message_thread_id'] = $message_thread_id;
      

        if(is_numeric($sub_account_id) && $sub_account_id > 0)
            $data['sub_account_id'] = $sub_account_id;
        else
            $data['sub_account_id'] = 0;


        $webhook_type = $this->request->getVar('webhook_type');
        $verify_payment = $this->request->getVar('verify_payment');

        if(!in_array($webhook_type, ['All','Success','Failed','No']))
            $data['webhook_type'] = "No";
        else
            $data['webhook_type'] = $webhook_type;

        if(!in_array($verify_payment, ['Skip','Success','Failed','Success_Or_Failed','No']))
            $data['verify_payment'] = "Skip";
        else
            $data['verify_payment'] = $verify_payment;
            
        $result = $notificationTelegramModel->set($data)->where(["id" =>$telegram_id,"company_id"=>$this->user_session['company_id']])->update();
        
        if($result) { 
            add_user_log(array('data_id'=>$telegram_id,'company_id' => $this->user_session['company_id'], 'data_type'=>'notification_telegram_update','description'=>'Sửa tích hợp thông báo Telegram','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
            return $this->response->setJSON(array("status"=>true));
        } else {
            add_user_log(array('data_id'=>$telegram_id,'company_id' => $this->user_session['company_id'], 'data_type'=>'notification_telegram_update','description'=>'Sửa tích hợp thông báo Telegram','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Failed'));
            return $this->response->setJSON(array("status"=>false,"message"=>"Không thể cập nhật dữ liệu này!"));
        }
    }

    public function ajax_telegram_delete() {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!has_permission('NotificationTelegram', 'can_delete'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền xóa dữ liệu này"));

        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'id' => 'required|integer|is_natural',
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }
        $telegram_id = $this->request->getPost('id');

      
        $notificationTelegramModel = model(NotificationTelegramModel::class);
        $telegram_details = $notificationTelegramModel->where(['id'=>$telegram_id,'company_id'=>$this->user_session['company_id']])->get()->getRow();

       

        if(!is_object($telegram_details))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Không tìm thấy dữ liệu này này"));

        $notificationTelegramModel->delete($telegram_id);

        add_user_log(array('data_id'=>$telegram_id,'company_id' => $this->user_session['company_id'], 'data_type'=>'notification_telegram_delete','description'=>'Xóa tích hợp thông báo Telegram','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
    
        return $this->response->setJSON(array("status"=>true));

    }

    public function ajax_telegram_test() {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!has_permission('NotificationTelegram', 'can_add'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền thực hiện thao tác này"));

        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'chat_id' => 'required|min_length[3]',
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }
        $chat_id = $this->request->getPost('chat_id');

        if(substr($chat_id,0,1) != "-")
            return $this->response->setJSON(array('status'=>FALSE,'message'=>'Chat ID phải bắt đầu bằng dấu trừ (-). Ví dụ -********'));    

        $message_thread_id = $this->request->getPost('message_thread_id');

        if(!is_numeric($message_thread_id))
            $message_thread_id = 0;

        $notificationTelegramQueueModel = model(NotificationTelegramQueueModel::class);
        
        $message = "[Test] Có giao dịch mới. Thông tin cụ thể:

✳️ Tiền vào: <b>20,000,000</b>

➖ Tiền ra: 0

📦 Số dư: 100,000,000

⛪️ Tài khoản chính: ********* Vietcombank | Tài khoản phụ:

ℹ️ Nội dung thanh toán: Test giao dịch

#️⃣ Mã code thanh toán: 

⚓️ Mã tham chiếu: 0000.0000.0000.0001

⏰ Thời gian giao dịch: " . date('Y-m-d H:i:s');

        $result = $notificationTelegramQueueModel->sendTelegramMessage($message, $chat_id,'html',$message_thread_id);
        log_message('error', json_encode($result));
        $api_response = [];
        if(isset($result['success']) && $result['success'] == TRUE) {
            if(isset($result['response'])) {

                $queue_last_log = $result['response'];

                $api_response = json_decode($result['response']);

             //   var_dump($api_response);
                if($api_response) {
                    if(is_object($api_response) && isset($api_response->ok) && $api_response->ok == true) {
                        return $this->response->setJSON(array("status"=>true));
                    }
                }
            }
        }

        add_user_log(array('data_id'=>0,'company_id' => $this->user_session['company_id'], 'data_type'=>'notification_telegram_test','description'=>'Test gửi thông báo Telegram Chat ID ' . $chat_id,'user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));

        return $this->response->setJSON(array("status"=>FALSE,"message" => "Gửi tin nhắn thất bại","api_response" => $api_response));
    
    }
 

    public function edit_message($id='') {
        $data = [
            'page_title' => 'Telegram',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        if(!has_permission('NotificationTelegram', 'can_edit'))
            show_404();

        $notificationTelegramModel = model(NotificationTelegramModel::class);
        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $webhooksModel = model(WebhooksModel::class);

        if(!is_numeric($id)) 
            show_404();
        
        $notificationTelegramModel = model(NotificationTelegramModel::class);
        
        $data['telegram_details'] = $notificationTelegramModel->where(['id'=>$id, 'company_id' => $this->user_session['company_id']])->get()->getRow();
        
        if(!is_object( $data['telegram_details']))
            show_404();

        $data['is_notice'] = $this->request->getGet('is_notice');

       // if($data['is_notice'] == "yes")
       //     set_alert('success','Tạo tích hợp Telegram thành công');
            

                
        echo theme_view('templates/autopay/header',$data);
        echo theme_view('notificationtelegram/edit_message',$data);
        echo theme_view('templates/autopay/footer',$data);
    }
 
    public function ajax_message_update() {

        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!has_permission('NotificationTelegram', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền sửa dữ liệu"));

 
        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'id' => ['label' => 'ID', 'rules' => 'required|integer|is_natural'],
            'is_template_custom' => ['label' => 'Loại nội dung', 'rules' => "required|in_list[0,1]"],
            'select_template' => ['label' => 'Mẫu tin', 'rules' => "in_list[template_1,template_2,template_3]"],
            'content_custom' => ['label' => 'Tùy chỉnh nội dung', 'rules' => 'min_length[10]|max_length[1000]']
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }

        $notificationTelegramModel = model(NotificationTelegramModel::class);

        $id = $this->request->getVar('id');
        $is_template_custom = $this->request->getVar('is_template_custom');
        $hide_accumulated = $this->request->getVar('hide_accumulated');
        $hide_details_link = $this->request->getVar('hide_details_link');
        

        $telegram_details = $notificationTelegramModel->where(['id'=>$id, 'company_id' => $this->user_session['company_id']])->get()->getRow();

        if(!is_object($telegram_details))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>'Không tìm thấy tích hợp này'));    

        if($is_template_custom == 0) {
            if($hide_accumulated == "on")
                $is_hide_accumulated = 1;
            else 
                $is_hide_accumulated = 0;
            
            if($hide_details_link == "on")
                $is_hide_details_link = 1;
            else
                $is_hide_details_link = 0;
            
            $notificationTelegramModel->set(['is_template_custom' => 0, 'hide_accumulated' => $is_hide_accumulated, 'hide_details_link' => $is_hide_details_link])->where(['id'=>$id, 'company_id' => $this->user_session['company_id']])->update();

        } else if($is_template_custom == 1)   {
            if(! $this->validate([
                'content_custom' => ['label' => 'Tùy chỉnh nội dung', 'rules' => 'required|min_length[10]|max_length[1000]']
            ])) {
                return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
            }
            $select_template = $this->request->getVar('select_template');

            $content_custom = $this->request->getVar('content_custom');

            $content_custom = strip_tags($content_custom, '<code><a><b><i>');

            $valid_html = validHTML($content_custom);

            if($valid_html !== true)
                return $this->response->setJSON(array('status'=>FALSE,'message'=>'Lỗi: HTML không đúng định dạng. ' . $valid_html));    

            $notificationTelegramModel->set(['is_template_custom' => 1,'template_custom' => $content_custom, 'template_name' => $select_template])->where(['id'=>$id, 'company_id' => $this->user_session['company_id']])->update();
        }

        return $this->response->setJSON(array('status'=>TRUE));
    }

 
    public function step1()
    {
        if (!has_permission('NotificationTelegram', 'can_add')) show_404();
 
        $session = service('session');
        $session->remove('notification_telegram_integration');

        $data = [
            'page_title' => 'Telegram',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        echo theme_view('templates/autopay/header',$data);
        echo theme_view('notificationtelegram/integration/step1', $data);
        echo theme_view('templates/autopay/footer',$data);
    }

    public function ajax_step1()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!has_permission('NotificationTelegram', 'can_add'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền thực hiện thao tác này']);
 
        $data = [
            'chat_id' => trim($this->request->getPost('chat_id')),
            'description' => trim($this->request->getPost('description')),
            'message_thread_id' => trim($this->request->getPost('message_thread_id')),
            'transaction_type' => trim($this->request->getPost('transaction_type'))
        ];

        $rules = [
            'chat_id' => ['required', 'regex_match[/^\-\d+$/]'],
            'description' => ['required', 'max_length[100]'],
            'message_thread_id' => ['permit_empty'],
            'transaction_type' => ['required','in_list[All,In_only,Out_only]'],
        ];

        if (! $this->validateData($data, $rules)) {
            return $this->fail($this->validator->getErrors());
        }

        if (!$data['message_thread_id'] || !is_numeric($data['message_thread_id']))
            $data['message_thread_id'] = 0;

        $session = service('session');
        $session->set('notification_telegram_integration', $data);

        return $this->respond(['status' => true]);
    } 

    protected function testTelegramConnection($chatId, $messageThreadId, $message = null)
    {
        $notificationTelegramQueueModel = model(NotificationTelegramQueueModel::class);
        $message = $message ?? "✅ Kết nối thành công. Hãy đi tới bước tiếp theo!";
        $result = $notificationTelegramQueueModel->sendTelegramMessage($message, $chatId, 'html', $messageThreadId);

        if (isset($result['success']) && $result['success'] == true && isset($result['response'])) {
            $response = json_decode($result['response']);

            if (is_object($response) && isset($response->ok) && $response->ok) {
                return $this->response->setJSON(['status' => true, 'data' => [
                    'title' => $response->result->chat->title
                ]]);
            } else {
                if (strpos($response->description, 'chat not found') > -1) {
                    $message = 'Kết nối thất bại, vui lòng kiểm tra lại ID nhóm Telegram, đảm bảo SePay Bot đã được thêm vào nhóm Telegram của bạn';
                } else if (strpos($response->description, 'message thread not found') > -1) {
                    $message = 'Kết nối thất bại, vui lòng kiểm tra lại ID topic';
                } else {
                    $message = 'Đã có lỗi xảy ra, vui lòng thử lại sau';
                }

                return $this->respond([
                    'status' => false, 
                    'message' => $message
                ]);
            }
        }

        return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ']);
    }

    public function ajax_test_telegram_connection()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!has_permission('NotificationTelegram', 'can_add'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền thực hiện thao tác này']);

        $data = [
            'chat_id' => trim($this->request->getPost('chat_id')),
            'message_thread_id' => trim($this->request->getPost('message_thread_id'))
        ];

        $rules = [
            'chat_id' => ['required', 'regex_match[/^\-\d+$/]'],
            'message_thread_id' => ['permit_empty']
        ];

        if (! $this->validateData($data, $rules)) {
            return $this->fail($this->validator->getErrors());
        }

        if (!$data['message_thread_id'] || !is_numeric($data['message_thread_id']))
            $data['message_thread_id'] = 0;

        return $this->testTelegramConnection($data['chat_id'], $data['message_thread_id']);
    }

    public function step3()
    {
        if (!has_permission('NotificationTelegram', 'can_add')) show_404();

        $data = [
            'page_title' => 'Telegram',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        $session = service('session');

        $notificationTelegramIntegration = $session->get('notification_telegram_integration');

        if (!$notificationTelegramIntegration 
        || !array_key_exists('chat_id', $notificationTelegramIntegration) 
        || !array_key_exists('description', $notificationTelegramIntegration) 
        || !array_key_exists('message_thread_id', $notificationTelegramIntegration)
        || !array_key_exists('transaction_type', $notificationTelegramIntegration)
        || !array_key_exists('bank_account_id_list', $notificationTelegramIntegration)
        || !array_key_exists('shop_id_list', $notificationTelegramIntegration)) {
            return redirect()->to('notificationtelegram/step1');
        }

        $data['notification_telegram_integration'] = $notificationTelegramIntegration;

        echo theme_view('templates/autopay/header',$data);
        echo theme_view('notificationtelegram/integration/step3', $data);
        echo theme_view('templates/autopay/footer',$data);
    }

    public function ajax_test_telegram_message()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!has_permission('NotificationTelegram', 'can_add'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền thực hiện thao tác này']);

        $data = [
            'template_custom' => $this->request->getVar('template_custom'),
            'is_template_custom' => $this->request->getVar('is_template_custom'),
            'hide_accumulated' => $this->request->getVar('hide_accumulated'),
            'hide_details_link' => $this->request->getVar('hide_details_link')
        ];

        if ($data['is_template_custom'] && !trim($data['template_custom'])) {
            return $this->fail(['template_custom' => 'Nội dung tin nhắn là bắt buộc']);
        }

        $session = service('session');

        $notificationTelegramIntegration = $session->get('notification_telegram_integration');

        if (!$notificationTelegramIntegration 
        || !array_key_exists('chat_id', $notificationTelegramIntegration) 
        || !array_key_exists('description', $notificationTelegramIntegration) 
        || !array_key_exists('message_thread_id', $notificationTelegramIntegration)
        || !array_key_exists('transaction_type', $notificationTelegramIntegration)
        || !array_key_exists('bank_account_id_list', $notificationTelegramIntegration)
        || !array_key_exists('shop_id_list', $notificationTelegramIntegration)) {
            return redirect()->to('notificationtelegram/step1');
        }

        $transactionDetails = (object) [
            'id' => '103896',
            'accumulated' => '********',
            'amount_in' => '********',
            'amount_out' => 0,
            'account_number' => '*************',
            'brand_name' => 'Vietcombank',
            'transaction_content' => 'TRAN ANH DUONG chuyen tien',
            'code' => 'HD1029148',
            'reference_number' => '171298.050723.020002',
            'transaction_date' => '2023-07-05 13:59:48',
            'sub_account' => null,
        ];

        $telegramDetails = (object) [
            'template_custom' => $data['template_custom']
        ];

        if (!$data['is_template_custom']) {
            $accumulated = "

⛳️ Số dư: <code>" . number_format($transactionDetails->accumulated) . ' đ</code>
';
            $detailsLink = '
            
Xem <a href="https://my.sepay.vn">chi tiết</a>.
';

            if ($data['hide_accumulated'])
                $accumulated = "
";

            if ($data['hide_details_link'])
                $detailsLink = "
";

            $message = "--------- - ID: " . $transactionDetails->id . " ---------
Có giao dịch mới:

✳️ Tiền vào: <code>" . number_format($transactionDetails->amount_in) . " đ</code>

➖ Tiền ra: <code>" . number_format($transactionDetails->amount_out) . " đ</code>" . $accumulated . 
"
⛪️ Tài khoản chính: <code>" . $transactionDetails->account_number . "</code> " . $transactionDetails->brand_name . "

ℹ️ Nội dung thanh toán: <code>" . $transactionDetails->transaction_content . "</code>

#️⃣ Mã code thanh toán: <code>" . $transactionDetails->code . "</code>

⚓️ Mã tham chiếu: <code>" . $transactionDetails->reference_number . "</code>

⏰ Giao dịch lúc: <code>" . $transactionDetails->transaction_date . "</code>" .
$detailsLink .
"
------------------------------";
        } else {
            $message = model(NotificationTelegramModel::class)->getMessage($transactionDetails, $telegramDetails);
        }

        return $this->testTelegramConnection($notificationTelegramIntegration['chat_id'], $notificationTelegramIntegration['message_thread_id'], $message);
    }
    public function step2()
    {
        if (!has_permission('NotificationTelegram', 'can_add')) show_404();

        $data = [
            'page_title' => 'Telegram',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        $session = service('session');

        $notificationTelegramIntegration = $session->get('notification_telegram_integration');

        if (!$notificationTelegramIntegration 
        || !array_key_exists('chat_id', $notificationTelegramIntegration) 
        || !array_key_exists('description', $notificationTelegramIntegration) 
        || !array_key_exists('transaction_type', $notificationTelegramIntegration)
        || !array_key_exists('message_thread_id', $notificationTelegramIntegration)) {
            return redirect()->to('notificationtelegram/step1');
        }

        $shopModel = model(ShopModel::class);

        $data['shops'] = $shopModel->where([
            'company_id' => $this->company_details->id,
            'active' => 1
            ])
        ->orderBy('id', 'ASC')
        ->get()
        ->getResult();

        echo theme_view('templates/autopay/header',$data);
        echo theme_view('notificationtelegram/integration/step2', $data);
        echo theme_view('templates/autopay/footer',$data);
    }

    public function ajax_shop_step2()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!has_permission('NotificationTelegram', 'can_add'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền thực hiện thao tác này']);
 
        $data = [
            'shop_id_list' => $this->request->getPost('shop_id_list'),
        ];

        if (!count($data['shop_id_list'])) {
            return $this->response->setJSON(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng tải lại trang 1','test'=> $data]);
        }

        $session = service('session');

        $shopModel = model(ShopModel::class);

        $shops = $shopModel->select('tb_autopay_shop.name, tb_autopay_bank_shop_link.shop_id, tb_autopay_bank_shop_link.bank_account_id, tb_autopay_bank_shop_link.bank_sub_account_id, tb_autopay_shop.company_id')
        ->join('tb_autopay_bank_shop_link', 'tb_autopay_shop.id = tb_autopay_bank_shop_link.shop_id')
        ->where(['tb_autopay_shop.company_id' => $this->company_details->id])
        ->whereIn('tb_autopay_bank_shop_link.shop_id', $data['shop_id_list'])
        ->orderBy('tb_autopay_shop.id', 'ASC')
        ->get()
        ->getResult();

        if (!$shops) return $this->failNotFound();

        $notificationTelegramIntegration = $session->get('notification_telegram_integration');
        $notificationTelegramIntegration['shop_id_list'] = $data['shop_id_list'];
        $notificationTelegramIntegration['bank_account_id_list'] = $shops;

        $session->set('notification_telegram_integration', $notificationTelegramIntegration);

        return $this->respond(['status' => true]);
    }

    public function ajax_shop_step3()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!has_permission('NotificationTelegram', 'can_add'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền thực hiện thao tác này']);

        $validation =  \Config\Services::validation();
        helper('text');

        $data = [
            'is_template_custom' => $this->request->getVar('is_template_custom'),
            'template_custom' => $this->request->getVar('template_custom'),
            'template_name' => $this->request->getVar('template_name'),
            'hide_accumulated' => $this->request->getVar('hide_accumulated'),
            'hide_details_link' => $this->request->getVar('hide_details_link')
        ];

        if (!in_array($data['template_name'], ['template_1', 'template_2', 'template_3'])) {
            return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng tải lại trang']);
        }

        if ($data['is_template_custom'] && !trim($data['template_custom'])) {
            if(! $this->validate([
                'template_custom' => ['label' => 'Tùy chỉnh nội dung', 'rules' => 'min_length[10]|max_length[1000]'],
            ])) {
                return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
            }
        }

        $session = service('session');

        $notificationTelegramIntegration = $session->get('notification_telegram_integration');

        if (!$notificationTelegramIntegration 
            || !array_key_exists('chat_id', $notificationTelegramIntegration) 
            || !array_key_exists('description', $notificationTelegramIntegration) 
            || !array_key_exists('message_thread_id', $notificationTelegramIntegration)
            || !array_key_exists('transaction_type', $notificationTelegramIntegration)
            || !array_key_exists('bank_account_id_list', $notificationTelegramIntegration)
            || !array_key_exists('shop_id_list', $notificationTelegramIntegration)) {
            return redirect()->to('notificationtelegram/step1');
        }

        $data = array_merge($data, $notificationTelegramIntegration);

        foreach ($data['bank_account_id_list'] as $bankAccountId) {
            $notificationTelegramModel = model(NotificationTelegramModel::class);

            $existingNotification = $notificationTelegramModel->where([
                'company_id' => $this->user_session['company_id'],
                'bank_account_id' => $bankAccountId->bank_account_id,
                'sub_account_id' => $bankAccountId->bank_sub_account_id,
                'chat_id' => $data['chat_id'],
            ])->first();

            $safeNotificationTelegramData = [
                'company_id' => $this->user_session['company_id'],
                'bank_account_id' => $bankAccountId->bank_account_id,
                'sub_account_id' => $bankAccountId->bank_sub_account_id,
                'transaction_type' => $data['transaction_type'],
                'webhook_type' => 'No',
                'verify_payment' => 'No',
                'chat_id' => $data['chat_id'],
                'message_thread_id' => $data['message_thread_id'],
                'description' => $data['description'],
                'hide_accumulated' => $data['hide_accumulated'] ? 1 : 0,
                'hide_details_link' => $data['hide_details_link'] ? 1 : 0,
                'is_template_custom' => $data['is_template_custom'] ? 1 : 0,
                'template_custom' => $data['template_custom'],
                'template_name' => $data['template_name'],
            ];

            if ($existingNotification) {
                // Cập nhật bản ghi nếu tồn tại
                $notificationTelegramModel->update($existingNotification->id, $safeNotificationTelegramData);
                add_user_log(['data_id' => $existingNotification->id, 'company_id' => $this->user_session['company_id'], 'data_type' => 'notification_telegram_update', 'description' => 'Cập nhật tích hợp thông báo Telegram', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);
            } else {
                // Thêm mới nếu không tồn tại
                $notificationTelegramId = $notificationTelegramModel->insert($safeNotificationTelegramData);

                if ($notificationTelegramId) {
                    add_user_log(['data_id' => $notificationTelegramId, 'company_id' => $this->user_session['company_id'], 'data_type' => 'notification_telegram_add', 'description' => 'Thêm tích hợp thông báo Telegram', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);
                } else {
                    add_user_log(['data_id' => 0, 'company_id' => $this->user_session['company_id'], 'data_type' => 'notification_telegram_add', 'description' => 'Thêm tích hợp thông báo Telegram', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Failed']);
                }
            }
        }

        return $this->respond(['status' => true]);
    }
    
    public function ajax_step3()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!has_permission('NotificationTelegram', 'can_add'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền thực hiện thao tác này']);

        $data = [
            'is_template_custom' => $this->request->getVar('is_template_custom'),
            'template_custom' => $this->request->getVar('template_custom'),
            'template_name' => $this->request->getVar('template_name'),
            'hide_accumulated' => $this->request->getVar('hide_accumulated'),
            'hide_details_link' => $this->request->getVar('hide_details_link')
        ];

        if (!in_array($data['template_name'], ['template_1', 'template_2', 'template_3'])) {
            return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng tải lại trang']);
        }

        if ($data['is_template_custom'] && !trim($data['template_custom'])) {
            return $this->fail(['template_custom' => 'Nội dung tin nhắn là bắt buộc']);
        }

        $session = service('session');

        $notificationTelegramIntegration = $session->get('notification_telegram_integration');

        if (!$notificationTelegramIntegration 
        || !array_key_exists('chat_id', $notificationTelegramIntegration) 
        || !array_key_exists('description', $notificationTelegramIntegration) 
        || !array_key_exists('message_thread_id', $notificationTelegramIntegration)
        || !array_key_exists('transaction_type', $notificationTelegramIntegration)
        || !array_key_exists('bank_account_id_list', $notificationTelegramIntegration)
        || !array_key_exists('shop_id_list', $notificationTelegramIntegration)) {
            return redirect()->to('notificationtelegram/step1');
        }

        $data = array_merge($data, $notificationTelegramIntegration);

        foreach ($data['bank_account_id_list'] as $bankAccountId) {
            $notificationTelegramModel = model(NotificationTelegramModel::class);

            $safeNotificationTelegramData = [
                'company_id' => $this->user_session['company_id'],
                'bank_account_id' => $bankAccountId,
                'sub_account_id' => 0,
                'transaction_type' => $data['transaction_type'],
                'webhook_type' => 'No',
                'verify_payment' => 'No',
                'chat_id' => $data['chat_id'],
                'message_thread_id' => $data['message_thread_id'],
                'description' => $data['description'],
                'hide_accumulated' => $data['hide_accumulated'] ? 1 : 0,
                'hide_details_link' => $data['hide_details_link'] ? 1 : 0,
                'is_template_custom' => $data['is_template_custom'] ? 1 : 0,
                'template_custom' => $data['template_custom'],
                'template_name' => $data['template_name'],
            ];

            $notificationTelegramId = $notificationTelegramModel->insert($safeNotificationTelegramData);

            if ($notificationTelegramId) {
                add_user_log(['data_id' => $notificationTelegramId, 'company_id' => $this->user_session['company_id'], 'data_type' => 'notification_telegram_add', 'description' => 'Thêm tích hợp thông báo Telegram', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);
            } else {
                add_user_log(['data_id' => 0, 'company_id' => $this->user_session['company_id'], 'data_type' => 'notification_telegram_add', 'description' => 'Thêm tích hợp thông báo Telegram', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Failed']);
            }
        }

        return $this->respond(['status' => true]);
    }

    public function step4()
    {
        if (!has_permission('NotificationTelegram', 'can_add')) show_404();

        $data = [
            'page_title' => 'Telegram',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        $session = service('session');

        $notificationTelegramIntegration = $session->get('notification_telegram_integration');

        if (!$notificationTelegramIntegration 
        || !array_key_exists('chat_id', $notificationTelegramIntegration) 
        || !array_key_exists('description', $notificationTelegramIntegration) 
        || !array_key_exists('message_thread_id', $notificationTelegramIntegration)
        || !array_key_exists('bank_account_id_list', $notificationTelegramIntegration)
        || !array_key_exists('shop_id_list', $notificationTelegramIntegration)) {
            return redirect()->to('notificationtelegram/step1');
        }

        $shopModel = model(ShopModel::class);

        $data['shops'] = $shopModel->whereIn('id', $notificationTelegramIntegration['shop_id_list'])->get()->getResult();

        if (count($data['shops']) != count($notificationTelegramIntegration['shop_id_list'])) {
            return redirect()->to('notificationtelegram/step1');
        }

        echo theme_view('templates/autopay/header',$data);
        echo theme_view('notificationtelegram/integration/step4', $data);
        echo theme_view('templates/autopay/footer',$data);       
    }

    public function ajax_step4()
    {
        $shopId = trim(xss_clean($this->request->getGet('shop_id')));

        if (!$shopId || !is_numeric($shopId)) return $this->failNotFound();

        $shopModel = model(ShopModel::class);

        $shop = $shopModel->where('id', $shopId)->get()->getRow();

        if(!$shop) return $this->failNotFound();

        $vas = model(BankSubAccountModel::class)
        ->select(['tb_autopay_bank_sub_account.id', 'tb_autopay_bank_sub_account.label', 'tb_autopay_bank_sub_account.sub_holder_name', 'tb_autopay_bank_sub_account.sub_account', 'tb_autopay_bank_account.account_number'])
        ->join('tb_autopay_bank_shop_link', 'tb_autopay_bank_shop_link.bank_sub_account_id = tb_autopay_bank_sub_account.id')
        ->join('tb_autopay_bank_account', 'tb_autopay_bank_shop_link.bank_account_id = tb_autopay_bank_account.id')
        ->join('tb_autopay_shop', 'tb_autopay_shop.id = tb_autopay_bank_shop_link.shop_id')
        ->where('tb_autopay_shop.id', $shop->id)
        ->where('tb_autopay_shop.company_id', $this->user_session['company_id'])
        ->get()
        ->getResult();

        return $this->respond([
            'status' => TRUE,
            'data' => $vas
        ]);
    }

    public function ajax_shop_telegrams_list()
    {
        if ($this->request->getMethod(true) != 'POST')
        return '';

        if(!has_permission('NotificationTelegram', 'can_view_all'))
            show_404();

        $notificationTelegramModel = model(NotificationTelegramModel::class);

        $telegrams = $notificationTelegramModel->getDatatablesShop($this->user_session['company_id']);
        
        $data = [];

        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');

        foreach ($telegrams as $telegram) {

            $no++;
            $row = [];
            
            $row[] = $no;
            $row[] = esc($telegram->id);
            $row[] = "<b>" . esc($telegram->description) . "</b>";

            $condition_text = "";
            if(strlen($telegram->shop_names) > 0)
                $condition_text = "<span class='text-info'>".esc($telegram->shop_names)."</span>";

            $row[] = $condition_text;
        
            if($telegram->active == 1)
                $row[] = "<span class='text-success'>Kích hoạt</span>";
            else
                $row[] = "<span class='text-danger'>Tạm ngưng</span>";

            $action_text = "";
            if(has_permission('NotificationTelegram', 'can_edit'))
                $action_text = "<a href=". base_url('notificationtelegram/edit/'.$telegram->chat_id) ." class='btn btn-sm btn-outline-warning ms-2 me-2 mt-2'><i class='bi bi-pencil'></i> Sửa</a>";
            
            if(has_permission('NotificationTelegram', 'can_delete'))
                $action_text = $action_text . "<a href='javascript:;' onclick='delete_telegram(" . $telegram->chat_id. ")' class='btn btn-sm btn-outline-danger ms-2 mt-2'><i class='bi bi-trash3'></i> Xóa</a>";

            $row[] = $action_text;
    
            $data[] = $row;
        }

        $output = array(
            "draw" => $draw,
            "recordsTotal" => $notificationTelegramModel->countFilteredShop($this->user_session['company_id']),
            "recordsFiltered" => $notificationTelegramModel->countFilteredShop($this->user_session['company_id']),
            "data" => $data,
        );
        return $this->response->setJSON($output);
    }

    public function ajax_shop_telegram_delete() 
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!has_permission('NotificationTelegram', 'can_delete'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền xóa dữ liệu này"));

        $validation =  \Config\Services::validation();

        helper('text');
        $chat_id = trim($this->request->getPost('id'));

        if(substr($chat_id,0,1) != "-")
            return $this->response->setJSON(array('status'=>FALSE,'message'=>'Chat ID phải bắt đầu bằng dấu trừ (-). Ví dụ -********'));

        $notificationTelegramModel = model(NotificationTelegramModel::class);
        $telegram_details = $notificationTelegramModel->where(['chat_id'=>$chat_id,'company_id'=>$this->user_session['company_id']])->get()->getResult();

        if(!is_array($telegram_details))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Không tìm thấy dữ liệu này này"));

        foreach($telegram_details as $r)
        {
            $notificationTelegramModel->delete($r->id);
        }
    
        add_user_log(array('data_id'=> $r->id,'company_id' => $this->user_session['company_id'], 'data_type'=>'notification_telegram_delete','description'=>'Xóa tích hợp thông báo Telegram','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
        
        return $this->response->setJSON(array("status"=>true));
    }

    public function edit($chat_id = null)
    {
        if (!has_permission('NotificationTelegram', 'can_edit')) show_404();

        if(substr($chat_id,0,1) != "-") return show_404();

        $session = service('session');
        $session->remove('notification_telegram_integration');

        $data = [
            'page_title' => 'Telegram',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        $notificationTelegramModel = model(NotificationTelegramModel::class);
        $shopModel = model(ShopModel::class);
        $telegram_details = $notificationTelegramModel->where(['chat_id'=>$chat_id,'company_id'=>$this->user_session['company_id']])->first();

        if(!is_object($telegram_details))
            return redirect()->to('notificationtelegram');

        $shops = $shopModel
        ->select(['tb_autopay_shop.name', 'tb_autopay_shop.id', 'tb_autopay_shop.address'])
        ->where([
            'tb_autopay_shop.company_id' => $this->company_details->id,
            'tb_autopay_shop.active' => 1
            ])
        ->orderBy('tb_autopay_shop.id', 'DESC')
        ->get()
        ->getResult();

        $shops_old = $shopModel
        ->select(['tb_autopay_shop.id'])
        ->join('tb_autopay_bank_shop_link', 'tb_autopay_bank_shop_link.shop_id = tb_autopay_shop.id')
        ->join('tb_autopay_notification_telegram', 'tb_autopay_bank_shop_link.bank_account_id = tb_autopay_notification_telegram.bank_account_id AND tb_autopay_bank_shop_link.bank_sub_account_id = tb_autopay_notification_telegram.sub_account_id')
        ->where('tb_autopay_notification_telegram.chat_id', $chat_id)
        ->where('tb_autopay_shop.company_id', $this->user_session['company_id'])
        ->groupBy('tb_autopay_shop.id')
        ->get()
        ->getResultArray();

        $shops_old = array_column($shops_old, 'id');

        $data['list_telegram'] = $telegram_details;
        $data['shops'] = $shops;
        $data['shops_old'] = $shops_old;

        echo theme_view('templates/autopay/header',$data);
        echo theme_view('notificationtelegram/edit', $data);
        echo theme_view('templates/autopay/footer',$data);
    }

    public function ajax_edit_step1()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';
        
        if(!has_permission('NotificationTelegram', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền sửa dữ liệu"));

        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'id' => 'required|min_length[3]|max_length[50]',
            'contains_content' => ['label' => 'Khi nội dung thanh toán có từ', 'rules' => "max_length[5000]"],
            'description' => ['label' => 'Đặt tên', 'rules' => "required|min_length[2]|max_length[1000]"],
            'chat_id' => ['label' => 'Telegram Chat ID', 'rules' =>"required|min_length[3]|max_length[50]"],
            'transaction_type' => ['label' => 'Loại giao dịch', 'rules' => "required|in_list[All,In_only,Out_only]"],
            'ignore_phrases' => ['label' => 'Bỏ qua nếu nội dung thanh toán có từ', 'rules' => "max_length[5000]"],
            'active' => "required|in_list[0,1]"
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }
        $amount_in_less_than_equal_to = $this->request->getPost('amount_in_less_than_equal_to');
        $amount_in_great_than_equal_to = $this->request->getPost('amount_in_great_than_equal_to');

        if(!is_numeric($amount_in_less_than_equal_to) || $amount_in_less_than_equal_to <= 0)
            $amount_in_less_than_equal_to = 0;

        if(!is_numeric($amount_in_great_than_equal_to) || $amount_in_great_than_equal_to <= 0)
            $amount_in_great_than_equal_to = 0;
            
        if($amount_in_less_than_equal_to != 0 && $amount_in_great_than_equal_to != 0) {
            if($amount_in_great_than_equal_to > $amount_in_less_than_equal_to) {
                return $this->response->setJSON(array('status'=>FALSE,'message'=>"Vui lòng nhập giá trị 'Số tiền lớp hơn hoặc bằng' phải nhỏ hơn điều kiện 'số tiền nhỏ hơn hoặc bằng'"));
            }
        }

        $data = array(
            'id' => trim($this->request->getVar('id')),
            'contains_content' => $this->request->getVar('contains_content'),
            'description' => xss_clean($this->request->getVar('description')),
            'chat_id' => trim($this->request->getVar('chat_id')),
            'amount_in_less_than_equal_to' => $amount_in_less_than_equal_to,
            'amount_in_great_than_equal_to' => $amount_in_great_than_equal_to,
            'ignore_phrases' => trim($this->request->getVar('ignore_phrases')),
            'transaction_type' => trim($this->request->getVar('transaction_type')),
            'active' => $this->request->getVar('active'),
            'message_thread_id' => trim($this->request->getVar('message_thread_id'))
        );

        if(substr($data['chat_id'],0,1) != "-")
            return $this->response->setJSON(array('status'=>FALSE,'message'=>'Chat ID phải bắt đầu bằng dấu trừ (-). Ví dụ -********'));

        if (!$data['message_thread_id'] || !is_numeric($data['message_thread_id']))
            $data['message_thread_id'] = 0;
            $notificationTelegramModel = model(NotificationTelegramModel::class);

            $id = $data['id'];

        $telegram_details = $notificationTelegramModel->where(['chat_id'=>$id,'company_id'=>$this->user_session['company_id']])->get()->getResult();
        
        if(!is_array($telegram_details))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>'Không tìm thấy tích hợp Telegram'));

            foreach ($telegram_details as $t)
            {
                $result = $notificationTelegramModel->set($data)->where(["id" =>$t->id,"company_id"=>$this->user_session['company_id']])->update();
        
                if($result) { 
                    add_user_log(array('data_id'=>$t->id,'company_id' => $this->user_session['company_id'], 'data_type'=>'notification_telegram_update','description'=>'Sửa tích hợp thông báo Telegram','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
                } else {
                    add_user_log(array('data_id'=>$t->id,'company_id' => $this->user_session['company_id'], 'data_type'=>'notification_telegram_update','description'=>'Sửa tích hợp thông báo Telegram','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Failed'));
                    return $this->response->setJSON(array("status"=>false,"message"=>"Không thể cập nhật dữ liệu này!"));
                }
            }
            return $this->response->setJSON(array("status"=>true));
    }

    public function ajax_edit_step2()
    {
        if ($this->request->getMethod(true) != 'POST') {
            return '';
        }

        if (!has_permission('NotificationTelegram', 'can_edit')) {
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền sửa dữ liệu']);
        }

        $validation = \Config\Services::validation();

        if (! $this->validate([
            'shop_id' => 'required|min_length[1]|max_length[50]',
            'chat_id' => ['label' => 'Telegram Chat ID', 'rules' => "required|min_length[3]|max_length[50]"]
        ])) {
            return $this->response->setJSON(['status' => false, 'message' => implode(". ", $validation->getErrors())]);
        }

        $data = [
            'shop_id' => trim($this->request->getVar('shop_id')),
            'chat_id' => trim($this->request->getVar('chat_id'))
        ];

        if (substr($data['chat_id'], 0, 1) != "-") {
            return $this->response->setJSON(['status' => false, 'message' => 'Chat ID phải bắt đầu bằng dấu trừ (-). Ví dụ -********']);
        }

        $shopId = $data['shop_id'];
        $chatId = $data['chat_id'];

        $notificationTelegramModel = model(NotificationTelegramModel::class);
        $shopModel = model(ShopModel::class);

        $shops = $shopModel
            ->select([
                'tb_autopay_bank_shop_link.bank_account_id',
                'tb_autopay_bank_shop_link.bank_sub_account_id'
            ])
            ->join('tb_autopay_bank_shop_link', 'tb_autopay_bank_shop_link.shop_id = tb_autopay_shop.id')
            ->where('tb_autopay_shop.company_id', $this->user_session['company_id'])
            ->where('tb_autopay_bank_shop_link.shop_id', $shopId)
            ->get()
            ->getResult();

        if (!empty($shops)) {
            foreach ($shops as $s) {
                $telegram_details = $notificationTelegramModel->where([
                    'bank_account_id' => $s->bank_account_id,
                    'sub_account_id' => $s->bank_sub_account_id,
                    'chat_id' => $chatId,
                    'company_id' => $this->user_session['company_id']
                ])->get()->getRow();

                if ($telegram_details) {
                    $notificationTelegramModel->delete($telegram_details->id);
                    add_user_log([
                        'data_id' => $telegram_details->id,
                        'company_id' => $this->user_session['company_id'],
                        'data_type' => 'notification_telegram_update',
                        'description' => 'Sửa tích hợp thông báo Telegram',
                        'user_id' => $this->user_details->id,
                        'ip' => $this->request->getIPAddress(),
                        'user_agent' => $this->request->getUserAgent()->getAgentString(),
                        'status' => 'Success'
                    ]);

                    $telegram_details = $notificationTelegramModel->where([
                        'chat_id' => $chatId,
                        'company_id' => $this->user_session['company_id']
                    ])->first();

                    if (!is_object($telegram_details)) {
                        return $this->response->setJSON(['status' => true,
                        'url' => base_url('notificationtelegram'),
                        'message' => 'Đã xóa hết cửa hàng. Vui lòng thêm lại tích hợp với Telegram']);
                    }
                } else {
                    $telegram_details = $notificationTelegramModel->where([
                        'chat_id' => $chatId,
                        'company_id' => $this->user_session['company_id']
                    ])->first();

                    $data = [
                        'company_id' => $telegram_details->company_id ?? $this->user_session['company_id'],
                        'transaction_type' => 'In_only',
                        'bank_account_id' => $s->bank_account_id,
                        'sub_account_id' => $s->bank_sub_account_id,
                        'contains_content' => $telegram_details->contains_content ?? '',
                        'description' => $telegram_details->description ?? '',
                        'chat_id' => $chatId,
                        'amount_in_less_than_equal_to' => $telegram_details->amount_in_less_than_equal_to ?? 0,
                        'amount_in_great_than_equal_to' => $telegram_details->amount_in_great_than_equal_to ?? 0,
                        'ignore_phrases' => $telegram_details->ignore_phrases ?? '',
                        'active' => $telegram_details->active ?? 1,
                        'message_thread_id' => $telegram_details->message_thread_id ?? 0,
                        'verify_payment' => $telegram_details->verify_payment ?? 'No',
                        'is_template_custom' => $telegram_details->is_template_custom ?? 0,
                        'template_custom' => $telegram_details->template_custom ?? '',
                        'template_name' => $telegram_details->template_name ?? 'template_1',
                        'hide_accumulated' => $telegram_details->hide_accumulated ?? 0,
                        'hide_details_link' => $telegram_details->hide_details_link ?? 0
                    ];

                    $teleId = $notificationTelegramModel->insert($data);
                    if ($teleId === false) {
                        return $this->response->setJSON(['status' => false, 'message' => 'Có lỗi xảy ra trong quá trình chèn dữ liệu']);
                    }

                    add_user_log([
                        'data_id' => $teleId,
                        'company_id' => $this->user_session['company_id'],
                        'data_type' => 'notification_telegram_update',
                        'description' => 'Sửa tích hợp thông báo Telegram',
                        'user_id' => $this->user_details->id,
                        'ip' => $this->request->getIPAddress(),
                        'user_agent' => $this->request->getUserAgent()->getAgentString(),
                        'status' => 'Success'
                    ]);
                }
            }
            return $this->response->setJSON(['status' => true]);
        }

        return $this->response->setJSON(['status' => false, 'message' => 'Không tìm thấy cửa hàng']);
    }

    public function ajax_edit_step3()
    {
        if ($this->request->getMethod(true) != 'POST')
        return '';

        if(!has_permission('NotificationTelegram', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền sửa dữ liệu"));


        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'id' => ['label' => 'ID', 'rules' => 'required|min_length[3]|max_length[50]'],
            'is_template_custom' => ['label' => 'Loại nội dung', 'rules' => "required|in_list[0,1]"],
            'template_name' => ['label' => 'Mẫu tin', 'rules' => "in_list[template_1,template_2,template_3]"]
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }

        $notificationTelegramModel = model(NotificationTelegramModel::class);

        $id = xss_clean($this->request->getVar('id'));
        $is_template_custom = trim($this->request->getVar('is_template_custom'));
        $hide_accumulated = trim(xss_clean($this->request->getVar('hide_accumulated')));
        $hide_details_link = trim(xss_clean($this->request->getVar('hide_details_link')));

        if (substr($id, 0, 1) != "-") {
            return $this->response->setJSON(['status' => false, 'message' => 'Chat ID phải bắt đầu bằng dấu trừ (-). Ví dụ -********']);
        }
        
        $telegram_details = $notificationTelegramModel->where(['chat_id'=>$id, 'company_id' => $this->user_session['company_id']])->get()->getResult();

        if(!is_array($telegram_details))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>'Không tìm thấy tích hợp này'));    

        foreach($telegram_details as $t)
        {
            if($is_template_custom == 0) {
                if($hide_accumulated == "on")
                    $is_hide_accumulated = 1;
                else 
                    $is_hide_accumulated = 0;
                
                if($hide_details_link == "on")
                    $is_hide_details_link = 1;
                else
                    $is_hide_details_link = 0;
                
                $notificationTelegramModel->set(['is_template_custom' => 0, 'hide_accumulated' => $is_hide_accumulated, 'hide_details_link' => $is_hide_details_link])->where(['id'=>$t->id, 'company_id' => $this->user_session['company_id']])->update();
    
            } else if($is_template_custom == 1)   {
                if(! $this->validate([
                    'template_custom' => ['label' => 'Tùy chỉnh nội dung', 'rules' => 'required|min_length[10]|max_length[1000]']
                ])) {
                    return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
                }
                $template_name = $this->request->getVar('template_name');
    
                $template_custom = $this->request->getVar('template_custom');
    
                $template_custom = strip_tags($template_custom, '<code><a><b><i>');
    
                $valid_html = validHTML($template_custom);
    
                if($valid_html !== true)
                    return $this->response->setJSON(array('status'=>FALSE,'message'=>'Lỗi: HTML không đúng định dạng. ' . $valid_html));    
    
                $notificationTelegramModel->set(['is_template_custom' => 1,'template_custom' => $template_custom, 'template_name' => $template_name])->where(['id'=>$t->id, 'company_id' => $this->user_session['company_id']])->update();
            }
        }

        add_user_log([
            'data_id' => $id,
            'company_id' => $this->user_session['company_id'],
            'data_type' => 'notification_telegram_update',
            'description' => 'Sửa tích hợp thông báo Telegram',
            'user_id' => $this->user_details->id,
            'ip' => $this->request->getIPAddress(),
            'user_agent' => $this->request->getUserAgent()->getAgentString(),
            'status' => 'Success'
        ]);

        return $this->response->setJSON(array('status'=>TRUE));
    }

    // custom ui telegram mysepay

    public function ajax_telegrams_list_data() {
     
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!has_permission('NotificationTelegram', 'can_view_all'))
            show_404();
 
        $notificationTelegramModel = model(NotificationTelegramModel::class);

        $telegrams = $notificationTelegramModel->getDatatables($this->user_session['company_id']);
        $data = array();

        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');
        
        foreach ($telegrams as $telegram) {
            $no++;
            $row = array();
            
            $row[] = $no;
            $row[] = esc($telegram->id);
            $row[] = "<b>" . esc($telegram->description) . "</b>";
        
            $condition_text_bank = '<ul>';

            if(strlen($telegram->account_number ??"") > 0){
                $condition_text_bank = $condition_text_bank. "<li><span class='text-info'>[Và] Khi tài khoản chính là <b>".esc($telegram->account_number) . ' ' . esc($telegram->brand_name)."</b></span> </li>";
            }else{
                $condition_text_bank = $condition_text_bank. "<li><span class='text-info'>[Và] Khi tài khoản Chính là <b> Tất cả ngân hàng</b></span> </li>";
            }

            if(strlen($telegram->sub_account ?? "") > 0 && strlen($telegram->account_number ?? "") > 0 ){
                $condition_text_bank = $condition_text_bank. "<li><span class='text-info'>[Và] Khi tài khoản phụ là <b>". esc($telegram->sub_account??"")."</b></span> </li>";
            }elseif(strlen($telegram->sub_account ?? "") == 0 && strlen($telegram->account_number ?? "") > 0){
                $condition_text_bank = $condition_text_bank. "<li><span class='text-info'>[Và] Khi tài khoản phụ là <b> Không chọn VA</b></span> </li>";
            }else{
                $condition_text_bank = $condition_text_bank. "<li><span class='text-info'>[Và] Khi tài khoản phụ là <b> Tất cả VA</b></span> </li>";
            }
            $condition_text_bank =  $condition_text_bank .'</ul>';
            $row[] = $condition_text_bank;

            $condition_text = '<ul>';

            if($telegram->transaction_type == "In_only")
                $condition_text = $condition_text. "<li><span class='text-info'>[Loại giao dịch] Khi có tiền vào</span></li>";
            else if($telegram->transaction_type == "Out_only")
                $condition_text = $condition_text. "<li><span class='text-info'>[Loại giao dịch] Khi có tiền ra</span></li>";
            else if($telegram->transaction_type == "All")
                $condition_text = $condition_text. "<li><span class='text-info'>[Loại giao dịch] Khi có tiền vào hoặc Tiền ra</span></li>";

            if($telegram->webhook_type == "Success")
                $condition_text = $condition_text. "<li><span class='text-info'>[Và] Khi WebHooks thành công</span></li>";
            else if($telegram->webhook_type == "Failed")
                $condition_text = $condition_text. "<li><span class='text-info'>[Và] Khi WebHooks thất bại</span></li>";

            if($telegram->verify_payment == "Success")
                $condition_text = $condition_text. "<li><span class='text-info'>[Và] Khi WebHooks xác thực thanh toán thành công</span></li>";
            else if($telegram->verify_payment == "Failed")
                $condition_text = $condition_text. "<li><span class='text-info'>[Và] Khi WebHooks xác thực thanh toán thất bại</span></li>";
            else if($telegram->verify_payment == "Success_Or_Failed")
                $condition_text = $condition_text. "<li><span class='text-info'>[Và] Khi WebHooks xác thực thanh toán Thất bại hoặc Thành công</span></li>";
            
            if($telegram->ignore_phrases)
                $condition_text = $condition_text. "<li><span class='text-success'>[Bỏ qua] Khi WebHooks Có các từ: </span> " . esc(character_limiter($telegram->ignore_phrases,50,'...')) . "</li>";
             
            if($telegram->contains_content)
                $condition_text = $condition_text. "<li><span class='text-success'>[Và] Khi WebHooks Có các từ: </span> " . esc(character_limiter($telegram->contains_content,50,'...')) . "</li>";
                 
            if (intval($telegram->amount_in_great_than_equal_to) > 0) {
                $amount = number_format($telegram->amount_in_great_than_equal_to, 0, ',', '.'); // Format số
                $condition_text .= "<li><span class='text-success'>[Và] Khi số tiền lớn hơn hoặc bằng: </span> " 
                    . esc(character_limiter($amount . ' đ', 50, '...')) 
                    . "</li>";
            }    
            if (intval($telegram->amount_in_less_than_equal_to) > 0) {
                $amount = number_format($telegram->amount_in_less_than_equal_to, 0, ',', '.'); // Format số
                $condition_text .= "<li><span class='text-success'>[Và] Khi số tiền nhỏ hơn hoặc bằng: </span> " 
                    . esc(character_limiter($amount . ' đ', 50, '...')) 
                    . "</li>";
            }    

            $condition_text =  $condition_text .'</ul>';

            $row[] = $condition_text;
            
            $row[] = esc($telegram->chat_id);

          
            if($telegram->active == 1)
                $row[] = "<span class='text-success'>Kích hoạt</span>";
            else
                $row[] = "<span class='text-secondary'>Tạm ngưng</span>";
            //$row[] = esc($telegram->created_at);
            $action_text = "<div>";
            if(has_permission('NotificationTelegram', 'can_edit'))
                // $action_text = "<a href='javascript:;' onclick='edit_telegram(" . $telegram->id. ")' class='btn btn-sm btn-outline-warning ms-2 me-2 mt-2'><i class='bi bi-pencil'></i> Sửa</a> <a href='".base_url('notificationtelegram/edit_message/' . $telegram->id)."' class='btn btn-sm btn-outline-info ms-2 me-2 mt-2'><i class='bi bi-chat-left-text'></i> Nội dung</a>";
                $action_text = $action_text. "<a href='/notificationtelegram/edit_/$telegram->id' class='btn btn-sm btn-outline-info ms-2 mt-2'><i class='bi bi-pencil'></i> Sửa </a> <a href='".base_url('notificationtelegram/edit_message/' . $telegram->id)."' class='btn btn-sm btn-outline-info ms-2 me-2 mt-2 d-none'><i class='bi bi-chat-left-text'></i> Nội dung</a>";
            
            if(has_permission('NotificationTelegram', 'can_delete'))
                $action_text = $action_text . "<a href='javascript:;' onclick='delete_telegram(" . $telegram->id. ")' class='btn btn-sm btn-outline-danger ms-2 mt-2'><i class='bi bi-trash3'></i> Xóa</a>";

            $action_text =  $action_text .'</div>';
            $row[] = $action_text;
       
            $data[] = $row;
        }

        foreach ($data as &$row) {
            if (($row[4])=="<ul></ul>") {
                $row[4] = "<ul><li><span class='text-warning'>[Bỏ qua cấu hình nâng cao] </span> </li></ul>";
            }
            
        }

        $output = array(
            "draw" => $draw,
            "recordsTotal" => $notificationTelegramModel->countAll($this->user_session['company_id']),
            "recordsFiltered" => $notificationTelegramModel->countFiltered($this->user_session['company_id']),
            "data" => $data,
        );
        return $this->response->setJSON($output);
    
    
    }

    public function edit_($id=null){
        if(empty($id)){
            show_404();
        }
        if (!has_permission('NotificationTelegram', 'can_edit')) {
            session()->setFlashdata('error_message', 'Bạn không có quyền sửa tích hợp!');
            return redirect()->to('notificationtelegram');
        }

        // Lấy mô hình NotificationTelegramModel
        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $notificationTelegramModel = model(NotificationTelegramModel::class);

        $data_integration = $notificationTelegramModel
            ->where('id', $id)
            ->where('company_id', $this->company_details->id ?? "")
            ->get() // Thực hiện truy vấn
            ->getRowArray(); // Trả về kết quả dưới dạng mảng

        if(empty($data_integration)){
            show_404();
        }
        $data_integration['amount_in_great_than_equal_to'] = intval($data_integration['amount_in_great_than_equal_to']);
        $data_integration['amount_in_less_than_equal_to'] = intval($data_integration['amount_in_less_than_equal_to']);
        // Lấy danh sách tài khoản ngân hàng
        $data['bank_accounts'] = $bankAccountModel
        ->select("tb_autopay_bank_account.id, tb_autopay_bank.brand_name, tb_autopay_bank_account.account_holder_name, tb_autopay_bank_account.account_number")
        ->join("tb_autopay_bank", "tb_autopay_bank.id = tb_autopay_bank_account.bank_id", "left")
        ->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id']])
        ->get()->getResultArray();

        // Lấy danh sách tài khoản phụ
        $data['bank_sub_accounts'] = $bankSubAccountModel
        ->select("tb_autopay_bank_sub_account.id, tb_autopay_bank_account.id as bank_account_id, tb_autopay_bank.brand_name, tb_autopay_bank_account.account_holder_name, tb_autopay_bank_account.account_number, tb_autopay_bank_sub_account.sub_account, tb_autopay_bank_sub_account.label")
        ->join("tb_autopay_bank_account", "tb_autopay_bank_account.id = tb_autopay_bank_sub_account.bank_account_id")
        ->join("tb_autopay_bank", "tb_autopay_bank.id = tb_autopay_bank_account.bank_id", "left")
        ->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id']])
        ->get()->getResultArray();

       // Tạo mảng kết quả đã nhóm
        $groupedBanks = [];

        foreach ($data['bank_accounts'] as $account) {
            $bank_account_number = $account['account_number'];
            $bank_account_id = $account['id'];
        
            // Khởi tạo mảng con subaccount nếu chưa tồn tại
            if (!isset($groupedBanks[$bank_account_id])) {
                $groupedBanks[$bank_account_id] = [
                    'id' => $bank_account_id,
                    'account_number' => $bank_account_number,
                    'brand_name' => $account['brand_name'], 
                    'subaccounts' => [] // Mảng chứa tài khoản phụ
                ];
            }
        
            // Lọc các tài khoản phụ (sub-accounts) tương ứng với tài khoản ngân hàng
            foreach ($data['bank_sub_accounts'] as $subAccount) {
                if ($subAccount['bank_account_id'] == $bank_account_id) {
                    $groupedBanks[$bank_account_id]['subaccounts'][] = $subAccount;
                }
            }
        }


        // Chuyển đổi dữ liệu đã nhóm thành một mảng

        $data = [
            'page_title' => 'Telegram',
            'data_integration' => $data_integration,
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];
        $data['grouped_bank_accounts'] = array_values($groupedBanks);
       
        echo view('templates/autopay/header',$data);
        echo view('notificationtelegram/edit_tab', $data);
        echo view('templates/autopay/footer',$data);
    }
 
    public function step_1()
    {
        if (!has_permission('NotificationTelegram', 'can_add')) show_404();
        
        $session = service('session');

        // Kiểm tra nếu dữ liệu đã tồn tại trong session, nếu có thì lấy ra và cập nhật
        $session_data_integration = $session->get('notification_telegram_integration');
        if ($session_data_integration === null) {
            // Nếu không có dữ liệu trước đó trong session, tạo mới mảng dữ liệu
            $session_data_integration = [
                'company_id' => $this->company_details->id ?? "",
                'bank_account_id' => 0,
                'transaction_type' => "All",
                'contains_content' => null,
                'chat_id' => null,
                'description' => null,
                'amount_in_less_than_equal_to' => null,
                'amount_in_great_than_equal_to' => null,
                'hide_accumulated' => 0,
                'hide_details_link' => 0,
                'ignore_phrases' => null,
                'active' => 1,
                'webhook_type' => 'No',
                'verify_payment' => 'Skip',
                'sub_account_id' => 0,
                'is_connect' => 0,
                'is_config' => 0,
            ];
        }   

        // Lưu mảng vào session với tên 'notification_telegram_integration'
        $session->set('notification_telegram_integration', $session_data_integration);


        $data = [
            'page_title' => 'Telegram',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        echo view('templates/autopay/header',$data);
        echo view('notificationtelegram/integration/step_1', $data);
        echo view('templates/autopay/footer',$data);
    }

    public function step_2()
    {
        if (!has_permission('NotificationTelegram', 'can_add')) show_404();

        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $webhooksModel = model(WebhooksModel::class);
        $data = [
            'page_title' => 'Telegram',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        $session = service('session');

        $notificationTelegramIntegration = $session->get('notification_telegram_integration');
        if(empty($notificationTelegramIntegration['chat_id'])){
            return redirect()->to('notificationtelegram/step_1');
        }
        if(empty($notificationTelegramIntegration['description'])){
            return redirect()->to('notificationtelegram/step_1');
        }
        if(empty($notificationTelegramIntegration['is_connect'])){
            return redirect()->to('notificationtelegram/step_1');
        }
        

        // Lấy danh sách tài khoản ngân hàng
        $data['bank_accounts'] = $bankAccountModel
        ->select("tb_autopay_bank_account.id, tb_autopay_bank.brand_name, tb_autopay_bank_account.account_holder_name, tb_autopay_bank_account.account_number")
        ->join("tb_autopay_bank", "tb_autopay_bank.id = tb_autopay_bank_account.bank_id", "left")
        ->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id']])
        ->get()->getResultArray();

        // Lấy danh sách tài khoản phụ
        $data['bank_sub_accounts'] = $bankSubAccountModel
        ->select("tb_autopay_bank_sub_account.id, tb_autopay_bank_account.id as bank_account_id, tb_autopay_bank.brand_name, tb_autopay_bank_account.account_holder_name, tb_autopay_bank_account.account_number, tb_autopay_bank_sub_account.sub_account, tb_autopay_bank_sub_account.label")
        ->join("tb_autopay_bank_account", "tb_autopay_bank_account.id = tb_autopay_bank_sub_account.bank_account_id")
        ->join("tb_autopay_bank", "tb_autopay_bank.id = tb_autopay_bank_account.bank_id", "left")
        ->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id']])
        ->get()->getResultArray();

        $groupedBanks = [];

        foreach ($data['bank_accounts'] as $account) {
            $bank_account_number = $account['account_number'];
            $bank_account_id = $account['id'];
        
            // Khởi tạo mảng con subaccount nếu chưa tồn tại
            if (!isset($groupedBanks[$bank_account_id])) {
                $groupedBanks[$bank_account_id] = [
                    'id' => $bank_account_id,
                    'account_number' => $bank_account_number,
                    'brand_name' => $account['brand_name'], 
                    'subaccounts' => [] // Mảng chứa tài khoản phụ
                ];
            }
        
            // Lọc các tài khoản phụ (sub-accounts) tương ứng với tài khoản ngân hàng
            foreach ($data['bank_sub_accounts'] as $subAccount) {
                if ($subAccount['bank_account_id'] == $bank_account_id) {
                    $groupedBanks[$bank_account_id]['subaccounts'][] = $subAccount;
                }
            }
        }

        // Chuyển đổi dữ liệu đã nhóm thành một mảng
        $data['grouped_bank_accounts'] = array_values($groupedBanks);

        $data['webhooks_count'] = $webhooksModel->where(['company_id' => $this->user_session['company_id']])->countAllResults();

       

        echo view('templates/autopay/header',$data);
        echo view('notificationtelegram/integration/step_2', $data);
        echo view('templates/autopay/footer',$data);
    }

    public function step_3()
    {
        if (!has_permission('NotificationTelegram', 'can_add')) show_404();

        $data = [
            'page_title' => 'Telegram',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        $session = service('session');

        $notificationTelegramIntegration = $session->get('notification_telegram_integration');

        if(empty($notificationTelegramIntegration['chat_id'])){
            return redirect()->to('notificationtelegram/step_1');
        }
        if(empty($notificationTelegramIntegration['description'])){
            return redirect()->to('notificationtelegram/step_1');
        }
        if(empty($notificationTelegramIntegration['is_connect'])){
            return redirect()->to('notificationtelegram/step_1');
        }
        if(empty($notificationTelegramIntegration['is_config'])){
            return redirect()->to('notificationtelegram/step_2');
        }


        $data['notification_telegram_integration'] = $notificationTelegramIntegration;

        echo view('templates/autopay/header',$data);
        echo view('notificationtelegram/integration/step_3', $data);
        echo view('templates/autopay/footer',$data);
    }

    public function step_4()
    {
        if (!has_permission('NotificationTelegram', 'can_add')) show_404();

        $data = [
            'page_title' => 'Telegram',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        $session = service('session');

        $notificationTelegramIntegration = $session->get('notification_telegram_integration');

        if(empty($notificationTelegramIntegration['chat_id'])){
            return redirect()->to('notificationtelegram/step_1');
        }
        if(empty($notificationTelegramIntegration['description'])){
            return redirect()->to('notificationtelegram/step_1');
        }
        if(empty($notificationTelegramIntegration['is_connect'])){
            return redirect()->to('notificationtelegram/step_1');
        }
        if(empty($notificationTelegramIntegration['is_config'])){
            return redirect()->to('notificationtelegram/step_2');
        }


        $session = service('session');
        $notification = $session->get('notification_telegram_integration');
        // Gọi hàm cấu hình Telegram
        $result_add = $this->config_telegram_add($notification);
        if (is_array($result_add) || is_object($result_add)) {
            log_message("debug", json_encode($result_add));
        } else {
            log_message("debug", "Kết quả trả về không phải là mảng hoặc đối tượng");
        }

        if (empty($result_add['status'])) {
            return $this->respond(['status' => false, 'message' => "Lỗi hệ thống thêm tích hợp, hãy liên hệ kỹ thuật"]);
        }

        $session->remove('notification_telegram_integration');

        echo view('templates/autopay/header',$data);
        echo view('notificationtelegram/integration/step_4', $data);
        echo view('templates/autopay/footer',$data);       
    }


    public function ajax_test_telegram_connection_()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!has_permission('NotificationTelegram', 'can_add'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền thực hiện thao tác này']);

        $data = [
            'chat_id' => trim($this->request->getPost('chat_id')),
            'message_thread_id' => trim($this->request->getPost('message_thread_id'))?? 0
        ];

        $rules = [
            'chat_id' => ['required', 'regex_match[/^\-\d+$/]'],
            'message_thread_id' => ['permit_empty']
        ];

        if (! $this->validateData($data, $rules)) {
            return $this->fail($this->validator->getErrors());
        }

        if (!$data['message_thread_id'] || !is_numeric($data['message_thread_id']))
            $data['message_thread_id'] = 0;
  

        return $this->testTelegramConnection_($data['chat_id'], $data['message_thread_id']);
    }

    protected function testTelegramConnection_($chatId, $messageThreadId, $message = null)
    {
        $notificationTelegramQueueModel = model(NotificationTelegramQueueModel::class);
        $message = $message ?? "✅ Kết nối thành công!";
        $result = $notificationTelegramQueueModel->sendTelegramMessage($message, $chatId, 'html', $messageThreadId);

        if (isset($result['success']) && $result['success'] == true && isset($result['response'])) {
            $response = json_decode($result['response']);

            $session = service("session");
            $session_data_integration = $session->get('notification_telegram_integration');
            if (is_object($response) && isset($response->ok) && $response->ok) {

                $session_data_integration['is_connect'] = 1;
                $session_data_integration['chat_id'] = $chatId;
                
                $session->set('notification_telegram_integration', $session_data_integration);

                return $this->response->setJSON(['status' => true, 'data' => [
                    'title' => $response->result->chat->title
                ]]);
            } else {
                $session_data_integration['is_connect'] = 0;
                $session_data_integration['chat_id'] = $chatId;
                $session->set('notification_telegram_integration', $session_data_integration);

                if (strpos($response->description, 'chat not found') > -1) {
                    $message = 'Kết nối thất bại, vui lòng kiểm tra lại ID nhóm Telegram, đảm bảo SePay Bot đã được thêm vào nhóm Telegram của bạn';
                } else if (strpos($response->description, 'message thread not found') > -1) {
                    $message = 'Kết nối thất bại, vui lòng kiểm tra lại ID topic';
                } else {
                    $message = 'Đã có lỗi xảy ra, vui lòng thử lại sau';
                }

                return $this->respond([
                    'status' => false, 
                    'message' => $message
                ]);
            }
        }

        return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ']);
    }


    public function ajax_config_step_1()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!has_permission('NotificationTelegram', 'can_add'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền thực hiện thao tác này']);
 
        $data = [
            'description' => trim($this->request->getPost('description')),
            'message_thread_id' => trim($this->request->getPost('message_thread_id'))
        ];

        $rules = [
            'description' => ['required', 'max_length[100]'],
            'message_thread_id' => ['permit_empty']
        ];

        if (! $this->validateData($data, $rules)) {
            return $this->fail($this->validator->getErrors());
        }

        if (!$data['message_thread_id'] || !is_numeric($data['message_thread_id']))
            $data['message_thread_id'] = 0;

        $session = service("session");
        $session_data_integration = $session->get('notification_telegram_integration');
        $session_data_integration['description'] = allowed_tags($data['description']);
        $session_data_integration['message_thread_id'] = $data['message_thread_id'];

        $session->set('notification_telegram_integration', $session_data_integration);

        return $this->respond(['status' => true]);
    } 

    public function ajax_config_step_2()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!has_permission('NotificationTelegram', 'can_add'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền thực hiện thao tác này']);
 
        $data_input = $this->request->getPost();
        if (!empty($data_input['contains_content'])) {
            $data_input['contains_content'] = allowed_tags($data_input['contains_content']);
            $data_input['contains_content'] = str_replace(['"', ','], ['', '|'], $data_input['contains_content']);
        }
        
        if (!empty($data_input['ignore_phrases'])) {
            $data_input['ignore_phrases'] = str_replace(['"', ','], ['', '|'], $data_input['ignore_phrases']);
            $data_input['ignore_phrases'] = allowed_tags($data_input['ignore_phrases']);
        }
        $data_clean = array_map(function($item){
            return allowed_tags($item);
        },$data_input);
        $validationRules = [
            "bank_account_id" => [
                "label" => "ID ngân hàng",
                "rules" => "required",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                ],
            ],
            "transaction_type" => [
                "label" => "Loại giao dịch",
                "rules" => "required|in_list[All,In_only,Out_only]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "in_list" => "Giá trị của {field} phải là 'All', 'In_only' hoặc 'Out_only'.",
                ],
            ],
            "webhook_type" => [
                "label" => "Loại webhook",
                "rules" => "required|in_list[No,All,Success,Failed]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "in_list" => "Giá trị của {field} phải là 'No', 'All', 'Success' hoặc 'Failed'.",
                ],
            ],
            "verify_payment" => [
                "label" => "Loại xác thực",
                "rules" => "required|in_list[No,Success_Or_Failed,Success,Failed,Skip]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "in_list" => "Giá trị của {field} phải là 'No', 'Success_Or_Failed', 'Success', 'Failed' hoặc 'Skip'.",
                ],
            ],
            
        ];
        
    
        // Load validation service
        $validation = service("validation");
    
        // Set validation rules
        $validation->setRules($validationRules);
    
        // Check data
        if (!$validation->run($data_clean)) {
            $errors = $validation->getErrors();
            log_message("error","Lỗi xác thực dữ liệu khi nhập cấu hình ".print_r($errors,true));
            return $this->respond(['status' => false]);
        }

        $session = service('session');
        $notificationTelegramIntegration = $session->get('notification_telegram_integration');
        if(empty($notificationTelegramIntegration)){
            log_message("error","Lỗi mất session thông tin tích hợp telegram!");
            return $this->respond(['status' => false]);
        }
  
        $notificationTelegramIntegration['is_config']=1;

        $session->set('notification_telegram_integration', array_merge($notificationTelegramIntegration,$data_clean));

        return $this->respond(['status' => true]);
    }

    public function ajax_config_step_3()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!has_permission('NotificationTelegram', 'can_add'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền thực hiện thao tác này']);

        $data = [
            'is_template_custom' => $this->request->getPost('is_template_custom'),
            'template_custom' => $this->request->getPost('template_custom'),
            'template_name' => $this->request->getPost('template_name'),
            'hide_accumulated' => $this->request->getPost('hide_accumulated') == "on" ? 1:0,
            'hide_details_link' => $this->request->getPost('hide_details_link')== "on" ? 1:0,
        ];

        if (!in_array($data['template_name'], ['template_1', 'template_2', 'template_3'])) {
            return $this->respond(['status' => false, 'message' => 'template không hợp lệ']);
        }

        if ($data['is_template_custom'] && !trim($data['template_custom'])) {
            return $this->fail(['template_custom' => 'Nội dung tin nhắn là bắt buộc']);
        }
        
        if (!empty($data['template_custom'])) {
            $data['template_custom'] = allowed_tags($data['template_custom']);
        }
        $validation =  \Config\Services::validation();
        helper('text');
    
        // Set validation rules
        $validation->setRules([
           
            
            'template_name' => [
                'label' => 'Mẫu tin',
                'rules' => 'permit_empty|in_list[template_1,template_2,template_3]',
                'errors' => [
                    'in_list' => '{field} phải là một trong các giá trị: template_1, template_2, template_3.'
                ]
            ],
            'template_custom' => [
                'label' => 'Tùy chỉnh nội dung',
                'rules' => 'permit_empty|min_length[10]|max_length[1000]',
                'errors' => [
                    'min_length' => '{field} phải có ít nhất 10 ký tự.',
                    'max_length' => '{field} không được vượt quá 1000 ký tự.'
                ]
            ]
        ]);
        
    
        // Check data
        if (!$validation->run($data)) {
            return $this->respond(['status' => false, 'message' => 'Dữ liệu không hợp lệ','data' => $validation->getErrors()]);
        }

        

        $session = service('session');

        $notificationTelegramIntegration = $session->get('notification_telegram_integration');

        

        $session->set('notification_telegram_integration', array_merge($notificationTelegramIntegration, $data));

        return $this->respond(['status' => true]);
    }
    

    public function infobank()
    {
        // Kiểm tra các điều kiện trong phiên
        if (
            empty($notification['chat_id']) ||
            empty($notification['description']) ||
            empty($notification['is_connect']) ||
            empty($notification['is_config'])
        ) {
            return $this->respond(['status' => false]);
        }

        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);

        $bank_account_id = $notification['bank_account_id'] ?? null;
        $sub_account_id = $notification['sub_account_id'] ?? null;
        $company_id = $notification['company_id'] ?? null;

        // Truy vấn dữ liệu tài khoản ngân hàng
        $data_bank = null;

        $baseQuery = $bankSubAccountModel
            ->select(
            "tb_autopay_bank_sub_account.id, tb_autopay_bank.brand_name, tb_autopay_bank_account.account_holder_name, tb_autopay_bank_account.account_number, tb_autopay_bank_sub_account.sub_account, tb_autopay_bank_sub_account.label"
            )
            ->join("tb_autopay_bank_account", "tb_autopay_bank_account.id = tb_autopay_bank_sub_account.bank_account_id")
            ->join("tb_autopay_bank", "tb_autopay_bank.id = tb_autopay_bank_account.bank_id", "left")
            ->where(['tb_autopay_bank_account.company_id' => $company_id]);

        if (empty($sub_account_id) && empty($bank_account_id)) {
            $data_bank = $baseQuery->get()->getRowArray();
        } elseif (empty($sub_account_id) && !empty($bank_account_id)) {
            $data_bank = $bankAccountModel
                ->select(
                    "tb_autopay_bank_account.id, tb_autopay_bank.brand_name, tb_autopay_bank_account.account_holder_name, tb_autopay_bank_account.account_number"
                )
                ->join("tb_autopay_bank", "tb_autopay_bank.id = tb_autopay_bank_account.bank_id", "left")
                ->where([
                    'tb_autopay_bank_account.company_id' => $company_id,
                    'tb_autopay_bank_account.id' => $bank_account_id,
                ])
                ->get()
                ->getRowArray();
        } elseif (!empty($sub_account_id) && !empty($bank_account_id)) {
            $data_bank = $baseQuery
                ->where([
                 'tb_autopay_bank_sub_account.id' => $sub_account_id,
                 'tb_autopay_bank_sub_account.bank_account_id' => $bank_account_id,
                ])
                ->get()
                ->getRowArray();
        }

        if (empty($data_bank)) {
            return $this->respond(['status' => false, 'message' => "Không tìm thấy thông tin tài khoản, hãy liên hệ sepay"]);
        }

        return $this->respond(['status' => true, 'data' => $data_bank]);
    }
    protected function config_telegram_add($data_session) {

        if(!has_permission('NotificationTelegram', 'can_add'))
            return array("status" => FALSE, "message" => "Bạn không có quyền thêm");
    
        $validation =  \Config\Services::validation();
        helper('text');
    
        // Set validation rules
        $validation->setRules([
            'bank_account_id' => [
                'label' => 'ID ngân hàng',
                'rules' => 'required|is_natural',  
                'errors' => [
                    'required' => 'Vui lòng nhập {field}.',
                    'is_natural' => '{field} phải là số',
                ]
            ],
            'transaction_type' => [
                'label' => 'Loại giao dịch',
                'rules' => 'required|in_list[All,In_only,Out_only]',
                'errors' => [
                    'required' => 'Vui lòng nhập {field}.', 
                    'in_list' => '{field} phải là một trong các giá trị: All, In_only, Out_only.',
                ]
            ],
            
            'template_name' => [
                'label' => 'Mẫu tin',
                'rules' => 'permit_empty|in_list[template_1,template_2,template_3]',
                'errors' => [
                    'in_list' => '{field} phải là một trong các giá trị: template_1, template_2, template_3.'
                ]
            ],
            'template_custom' => [
                'label' => 'Tùy chỉnh nội dung',
                'rules' => 'permit_empty|min_length[10]|max_length[1000]',
                'errors' => [
                    'min_length' => '{field} phải có ít nhất 10 ký tự.',
                    'max_length' => '{field} không được vượt quá 1000 ký tự.'
                ]
            ]
        ]);
        
    
        // Check data
        if (!$validation->run($data_session)) {
            return ['status' => false, 'data' => $validation->getErrors()];
        }
    
        $bankAccountModel = model(BankAccountmodel::class);
        $bankSubAccountModel = model(BankSubAccountmodel::class);
        $notificationTelegramModel = model(NotificationTelegramModel::class);
    
        $bank_account_id = $data_session['bank_account_id'];
        $sub_account_id = $data_session['sub_account_id'];
    
        if(is_numeric($bank_account_id) && $bank_account_id > 0) {
            $bank_account = $bankAccountModel->where(['company_id' => $this->user_session['company_id'], 'id'=>$bank_account_id])->countAllResults();
            if($bank_account != 1)
                return array('status' => FALSE, 'message' => 'Không tìm thấy tài khoản ngân hàng chính mà bạn chọn');
        }
    
        $chat_id = trim($data_session['chat_id']);
        if(substr($chat_id, 0, 1) != "-")
            return array('status' => FALSE, 'message' => 'Chat ID phải bắt đầu bằng dấu trừ (-). Ví dụ -********');
    
        $amount_in_less_than_equal_to = $data_session['amount_in_less_than_equal_to'];
        $amount_in_great_than_equal_to = $data_session['amount_in_great_than_equal_to'];
    
        if(!is_numeric($amount_in_less_than_equal_to) || $amount_in_less_than_equal_to <= 0)
            $amount_in_less_than_equal_to = 0;
    
        if(!is_numeric($amount_in_great_than_equal_to) || $amount_in_great_than_equal_to <= 0)
            $amount_in_great_than_equal_to = 0;
    
        if($amount_in_less_than_equal_to != 0 && $amount_in_great_than_equal_to != 0) {
            if($amount_in_great_than_equal_to > $amount_in_less_than_equal_to) {
                return array('status' => FALSE, 'message' => "Vui lòng nhập giá trị 'Tiền vào lớn hơn hoặc bằng' phải nhỏ hơn điều kiện 'Tiền vào nhỏ hơn hoặc bằng'");
            }
        }
    
        $data = [
            'company_id' => $this->user_session['company_id'],
            'bank_account_id' => $bank_account_id,
            'transaction_type' => $data_session['transaction_type'],
            'contains_content' => $data_session['contains_content'],
            'chat_id' => $chat_id,
            'description' => xss_clean($data_session['description']),
            'amount_in_less_than_equal_to' => $amount_in_less_than_equal_to,
            'amount_in_great_than_equal_to' => $amount_in_great_than_equal_to,
            'hide_accumulated' => $data_session['hide_accumulated'] ??"",
            'hide_details_link' => $data_session['hide_details_link']??"",
            'ignore_phrases' => trim($data_session['ignore_phrases'])??"",
            'active' => $data_session['active'] ?? "",
            'is_template_custom' => $data_session['is_template_custom']?? 0,
            'template_name' => $data_session['template_name'] ?? "template_1",
            'template_custom' => $data_session['template_custom'] ?? "",
            
        ];
        if (!empty($data['template_custom'])) {
            $data['template_custom'] = allowed_tags($data['template_custom']);
        }

       if (!empty($data['contains_content'])) {
            $data['contains_content'] = allowed_tags($data['contains_content']);
            $data['contains_content'] = str_replace(['"', ','], ['', '|'], $data['contains_content']);
        }

        if (!empty($data['ignore_phrases'])) {
            $data['ignore_phrases'] = allowed_tags($data['ignore_phrases']);
            $data['ignore_phrases'] = str_replace(['"', ','], ['', '|'], $data['ignore_phrases']);
        }


        
        
    
        $message_thread_id = $data_session['message_thread_id'];
        if(is_numeric($message_thread_id))
            $data['message_thread_id'] = $message_thread_id;
    
        $webhook_type = $data_session['webhook_type'];
        $verify_payment = $data_session['verify_payment'];
    
        if(!in_array($webhook_type, ['All','Success','Failed','No']))
            $data['webhook_type'] = "No";
        else
            $data['webhook_type'] = $webhook_type;
    
        if(!in_array($verify_payment, ['Skip','Success','Failed','Success_Or_Failed','No']))
            $data['verify_payment'] = "Skip";
        else
            $data['verify_payment'] = $verify_payment;
    
        if(is_numeric($sub_account_id) && $sub_account_id > 0) {
            $sub_account = $bankSubAccountModel->join("tb_autopay_bank_account","tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id")
                ->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id'], 'tb_autopay_bank_sub_account.id'=>$sub_account_id])->countAllResults();
            if($sub_account != 1)
                return array('status' => FALSE, 'message' => 'Không tìm thấy tài khoản ngân hàng phụ mà bạn chọn');
        }
    
        if(is_numeric($sub_account_id) && $sub_account_id > 0)
            $data['sub_account_id'] = $sub_account_id;
        else
            $data['sub_account_id'] = 0;
    
        $result = $notificationTelegramModel->insert($data);
        if($result) {
            add_user_log([
                'data_id' => $result,
                'company_id' => $this->user_session['company_id'],
                'data_type' => 'notification_telegram_add',
                'description' => 'Thêm tích hợp thông báo Telegram',
                'user_id' => $this->user_details->id,
                'ip' => $this->request->getIPAddress(),
                'user_agent' => $this->request->getUserAgent()->getAgentString(),
                'status' => 'Success'
            ]);
    
            return array("status" => true, 'id' => $result);
        } else {
            add_user_log([
                'data_id' => 0,
                'company_id' => $this->user_session['company_id'],
                'data_type' => 'notification_telegram_add',
                'description' => 'Thêm tích hợp thông báo Telegram',
                'user_id' => $this->user_details->id,
                'ip' => $this->request->getIPAddress(),
                'user_agent' => $this->request->getUserAgent()->getAgentString(),
                'status' => 'Failed'
            ]);
    
            return array("status" => false, "message" => "Không thể thêm điều kiện này. Vui lòng liên hệ SePay để được hỗ trợ.");
        }
    }

    public function update_integration($id){

        $data_input = $this->request->getPost();
        $NotificationTelegramModel = model(NotificationTelegramModel::class);
        $check_exits = $NotificationTelegramModel->where(['id'=>$id,'company_id'=>$this->company_details->id])->find();
        if(empty($check_exits)){
            return $this->respond(['status' => false,'message'=>"Không được chỉnh thông tin này!"]);
        }
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!has_permission('NotificationTelegram', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền sửa dữ liệu"));

 
        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'bank_account_id' => 'integer|is_natural',
            //'sub_account_id' => 'integer|is_natural',
            'id' => 'required|integer|is_natural',
            'transaction_type' => "required|in_list[All,In_only,Out_only]",
           // 'webhook_type' => "required|in_list[All,Success,Failed,No]",
            'contains_content' => ['label' => 'Khi nội dung thanh toán có từ', 'rules' => "max_length[5000]"],
           // 'verify_payment' => "required|in_list[Skip,Success,Failed,Success_Or_Failed,No]",
            'description' => ['label' => 'Đặt tên', 'rules' => "required|min_length[2]|max_length[1000]"],
            'chat_id' => ['label' => 'Telegram Chat ID', 'rules' =>"required|min_length[3]|max_length[50]"],
            //'hide_accumulated' => "required|in_list[0,1]",
            //'hide_details_link' => "required|in_list[0,1]",
            'ignore_phrases' => ['label' => 'Bỏ qua nếu nội dung thanh toán có từ', 'rules' => "max_length[5000]"],
            'active' => "required|in_list[0,1]"
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }

        $chat_id = trim($this->request->getVar('chat_id'));

        if(substr($chat_id,0,1) != "-")
            return $this->response->setJSON(array('status'=>FALSE,'message'=>'Chat ID phải bắt đầu bằng dấu trừ (-). Ví dụ -********'));    


        $bankAccountModel = model(BankAccountmodel::class);
        $bankSubAccountModel = model(BankSubAccountmodel::class);
        $notificationTelegramModel = model(NotificationTelegramModel::class);

        $bank_account_id = $this->request->getPost('bank_account_id');
        $sub_account_id = $this->request->getPost('sub_account_id');

        if(is_numeric($bank_account_id) && $bank_account_id > 0) {
            $bankAccountModel = model(BankAccountmodel::class);
            $bank_account = $bankAccountModel->where(['company_id' => $this->user_session['company_id'], 'id'=>$bank_account_id])->countAllResults();
            if($bank_account != 1)
                return $this->response->setJSON(array('status'=>FALSE,'message'=>'Không tìm thấy tài khoản ngân hàng chính mà bạn chọn'));    
        }
      
        $sub_account_id = $this->request->getPost('sub_account_id');

        if(is_numeric($sub_account_id) && $sub_account_id > 0) {
            $sub_account = $bankSubAccountModel->join("tb_autopay_bank_account","tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id")->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id'], 'tb_autopay_bank_sub_account.id'=>$sub_account_id])->countAllResults();
            if($sub_account != 1)
                return $this->response->setJSON(array('status'=>FALSE,'message'=>'Không tìm thấy tài khoản ngân hàng phụ mà bạn chọn'));
           
        
        }
        
        $telegram_id = $this->request->getPost('id');
        
        $telegram_details = $notificationTelegramModel->where(["id" =>$telegram_id,"company_id"=>$this->user_session['company_id']])->get()->getRow();

        if(!is_object($telegram_details))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>'Không tìm thấy dữ liệu cần cập nhật'));


        $amount_in_less_than_equal_to = $this->request->getPost('amount_in_less_than_equal_to');
        $amount_in_great_than_equal_to = $this->request->getPost('amount_in_great_than_equal_to');

        if(!is_numeric($amount_in_less_than_equal_to) || $amount_in_less_than_equal_to <= 0)
            $amount_in_less_than_equal_to = 0;

        if(!is_numeric($amount_in_great_than_equal_to) || $amount_in_great_than_equal_to <= 0)
            $amount_in_great_than_equal_to = 0;
            
        if($amount_in_less_than_equal_to != 0 && $amount_in_great_than_equal_to != 0) {
            if($amount_in_great_than_equal_to > $amount_in_less_than_equal_to) {
                return $this->response->setJSON(array('status'=>FALSE,'message'=>"Vui lòng nhập giá trị 'Số tiền lớn hơn hoặc bằng' phải nhỏ hơn điều kiện 'số tiền nhỏ hơn hoặc bằng'"));

            }
        }

        $data = array(
            'bank_account_id' => $this->request->getPost('bank_account_id'),
           // 'sub_account_id' => $this->request->getPost('sub_account_id'),
            'transaction_type' => $this->request->getVar('transaction_type'),
            //'webhook_type' => $this->request->getVar('webhook_type'),
            //'verify_payment' => $this->request->getVar('verify_payment'),
            'contains_content' => $this->request->getVar('contains_content'),
            'description' => xss_clean($this->request->getVar('description')),
            'chat_id' => trim($this->request->getVar('chat_id')),
            //'hide_accumulated' => $this->request->getVar('hide_accumulated'),
            //'hide_details_link' => $this->request->getVar('hide_details_link'),
            'amount_in_less_than_equal_to' => $amount_in_less_than_equal_to,
            'amount_in_great_than_equal_to' => $amount_in_great_than_equal_to,
            'ignore_phrases' => trim($this->request->getVar('ignore_phrases')),
            'active' => $this->request->getVar('active'),
        );

        if (!empty($data['template_custom'])) {
            $data['template_custom'] = allowed_tags($data['template_custom']);
        }

        if (!empty($data['contains_content'])) {
            $data['contains_content'] = allowed_tags($data['contains_content']);
            $data['contains_content'] = str_replace(['"', ','], ['', '|'], $data['contains_content']);
        }
        
        if (!empty($data['ignore_phrases'])) {
            $data['ignore_phrases'] = str_replace(['"', ','], ['', '|'], $data['ignore_phrases']);
            $data['ignore_phrases'] = allowed_tags($data['ignore_phrases']);
        }


       

        $message_thread_id = trim($this->request->getVar('message_thread_id'));
        if(is_numeric($message_thread_id))
            $data['message_thread_id'] = $message_thread_id;
      

        if(is_numeric($sub_account_id) && $sub_account_id > 0)
            $data['sub_account_id'] = $sub_account_id;
        else
            $data['sub_account_id'] = 0;


        $webhook_type = $this->request->getVar('webhook_type');
        $verify_payment = $this->request->getVar('verify_payment');

        if(!in_array($webhook_type, ['All','Success','Failed','No']))
            $data['webhook_type'] = "No";
        else
            $data['webhook_type'] = $webhook_type;

        if(!in_array($verify_payment, ['Skip','Success','Failed','Success_Or_Failed','No']))
            $data['verify_payment'] = "Skip";
        else
            $data['verify_payment'] = $verify_payment;
            
        $result = $notificationTelegramModel->set($data)->where(["id" =>$telegram_id,"company_id"=>$this->user_session['company_id']])->update();
        
        if($result) { 
            add_user_log(array('data_id'=>$telegram_id,'company_id' => $this->user_session['company_id'], 'data_type'=>'notification_telegram_update','description'=>'Sửa tích hợp thông báo Telegram','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
            
            // update thông tin tích hợp message custom
            if(! $this->validate([
                'id' => ['label' => 'ID', 'rules' => 'required|integer|is_natural'],
                'is_template_custom' => ['label' => 'Loại nội dung', 'rules' => "required|in_list[0,1]"],
                'template_name' => ['label' => 'Mẫu tin', 'rules' => "in_list[template_1,template_2,template_3]"],
                'template_custom' => ['label' => 'Tùy chỉnh nội dung', 'rules' => 'min_length[10]|max_length[1000]']
            ])) {
                log_message("debug",json_encode(implode(". ", $validation->getErrors())));
                return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
            }
    
            $notificationTelegramModel = model(NotificationTelegramModel::class);
    
            $id = $this->request->getVar('id');
            $is_template_custom = $this->request->getVar('is_template_custom');
            $hide_accumulated = $this->request->getVar('hide_accumulated');
            $hide_details_link = $this->request->getVar('hide_details_link');
            
    
            $telegram_details = $notificationTelegramModel->where(['id'=>$id, 'company_id' => $this->user_session['company_id']])->get()->getRow();
    
            if(!is_object($telegram_details))
                return $this->response->setJSON(array('status'=>FALSE,'message'=>'Không tìm thấy tích hợp này'));    
    
            if($is_template_custom == 0) {
                if($hide_accumulated == "on")
                    $is_hide_accumulated = 1;
                else 
                    $is_hide_accumulated = 0;
                
                if($hide_details_link == "on")
                    $is_hide_details_link = 1;
                else
                    $is_hide_details_link = 0;
                
                $notificationTelegramModel->set(['is_template_custom' => 0, 'hide_accumulated' => $is_hide_accumulated, 'hide_details_link' => $is_hide_details_link])->where(['id'=>$id, 'company_id' => $this->user_session['company_id']])->update();
    
            } else if($is_template_custom == 1)   {
                if(! $this->validate([
                    'template_custom' => ['label' => 'Tùy chỉnh nội dung', 'rules' => 'required|min_length[10]|max_length[1000]']
                ])) {
                    return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
                }
                $select_template = $this->request->getVar('template_name');
    
                $content_custom = $this->request->getVar('template_custom');
    
                $content_custom = strip_tags($content_custom, '<code><a><b><i>');
    
                $valid_html = validHTML($content_custom);
    
                if($valid_html !== true)
                    return $this->response->setJSON(array('status'=>FALSE,'message'=>'Lỗi: HTML không đúng định dạng. ' . $valid_html));    
    
                $notificationTelegramModel->set(['is_template_custom' => 1,'template_custom' => $content_custom, 'template_name' => $select_template])->where(['id'=>$id, 'company_id' => $this->user_session['company_id']])->update();
    
    
            }
            
            return $this->response->setJSON(['status' => true,'message'=>"Cập nhật thành công!",'data'=>$data_input]);
        } else {
            add_user_log(array('data_id'=>$telegram_id,'company_id' => $this->user_session['company_id'], 'data_type'=>'notification_telegram_update','description'=>'Sửa tích hợp thông báo Telegram','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Failed'));
            return $this->response->setJSON(array("status"=>false,"message"=>"Không thể cập nhật dữ liệu này!"));
        }

    }
    
    // end custom ui telegram
}
