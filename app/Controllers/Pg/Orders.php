<?php

namespace App\Controllers\Pg;

use App\Controllers\BaseController;
use App\Enums\PgOrderStatus;
use App\Models\PgOrderModel;

class Orders extends BaseController
{
    public function index()
    {
        if (! in_array($this->company_details->role, ['Admin', 'SuperAdmin'])) {
            show_404();
        }

        if ($this->request->isAJAX()) {
            $orderModel = model(PgOrderModel::class)->setCompanyId($this->company_details->id);
            $orders = $orderModel->getDatatables($this->request);
            $data = [];

            foreach ($orders as $order) {
                $data[] = [
                    $order->order_id,
                    format_currency($order->order_amount),
                    $order->order_currency,
                    PgOrderStatus::toHtml($order->order_status),
                    $order->order_description,
                    date('H:i:s d/m/Y', strtotime($order->created_at)),
                ];
            }

            return $this->response->setJSON([
                'draw' => $this->request->getPost('draw'),
                'recordsTotal' => $orderModel->countAll(),
                'recordsFiltered' => $orderModel->countFiltered($this->request),
                'data' => $data,
            ]);
        }

        $merchants = model(PgOrderModel::class)
            ->distinct()
            ->select('tb_autopay_pg_merchant.id, tb_autopay_pg_merchant.name')
            ->join('tb_autopay_pg_merchant', 'tb_autopay_pg_merchant.id = tb_autopay_pg_order.pg_merchant_id')
            ->where('tb_autopay_pg_order.company_id', $this->company_details->id)
            ->where('tb_autopay_pg_order.pg_merchant_id IS NOT NULL')
            ->orderBy('tb_autopay_pg_order.pg_merchant_id', 'ASC')
            ->findAll();

        $currencies = model(PgOrderModel::class)
            ->distinct()
            ->select('order_currency')
            ->where('company_id', $this->company_details->id)
            ->orderBy('order_currency', 'ASC')
            ->findAll();

        return view('pg/orders/index', [
            'page_title' => 'Đơn hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'merchants' => $merchants,
            'currencies' => array_map(fn($currency) => $currency->order_currency, $currencies),
        ]);
    }

    public function show($orderId)
    {
        if (! in_array($this->company_details->role, ['Admin', 'SuperAdmin'])) {
            show_404();
        }

        $order = model(PgOrderModel::class)
            ->where('order_id', $orderId)
            ->where('company_id', $this->company_details->id)
            ->first();

        if (! $order) {
            show_404();
        }

        return view('pg/orders/show', [
            'page_title' => sprintf('Đơn hàng %s', $order->order_id),
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'order' => $order,
        ]);
    }
}
