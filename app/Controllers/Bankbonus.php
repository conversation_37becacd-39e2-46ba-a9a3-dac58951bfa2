<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\BankModel;
use App\Models\BankBonusModel;
use App\Models\ProductModel;
class Bankbonus extends BaseController
{
    public function __construct()
    {
        helper('general');
        $config = config(\Config\BankBonus::class);
       
        if ($config->visibility && !is_admin()) {
            show_404();
        }
    }

    public function index()
    {
        $data = [
            'page_title' => 'Mở mới TK nhận giao dịch',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        if(!in_array($this->company_details->role,['Admin', 'SuperAdmin']))
            show_404();

        $configBankBonus = config(\Config\BankBonus::class);
        $bankModel = model(BankModel::class);

        $data['max_bank_bonus'] = $configBankBonus->maxBankBonus;

        $data['banks'] = $bankModel->where([
            'bank_bonus !=' => 0,
            'active' => 1
        ])->orderBy('bank_bonus', 'DESC')->get()->getResult();

        echo view('templates/autopay/header',$data);
        echo view('bankbonus/index',$data);
        echo view('templates/autopay/footer',$data);
    }

    public function ajax_bank_bonus()
    {
        if(!in_array($this->company_details->role, ['Admin', 'SuperAdmin']))
            show_404();
     
        if ($this->request->getMethod(true) != 'POST')
            return '';

        $bankBonusModel = model(BankBonusModel::class);

        $bank_bonus = $bankBonusModel->getDatatables($this->company_details->company_id);

        $data = array();

        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');
 
        foreach ($bank_bonus as $bonus) {
            $no++;
            $row = array();

            $row[] = $no;
            $row[] = '<img src="'.base_url('assets/images/banklogo').'/'. esc($bonus->icon_path) .'" width="30px" height="30px" />' .' '. esc($bonus->brand_name);
            $row[] = esc($bonus->account_number);
            $row[] = '<span class="text-success">+'. esc($bonus->bank_bonus) .'</span>';
            $row[] = $bonus->created_at;

            $data[] = $row;
        }
 
        $output = array(
            "draw" => $draw,
            "recordsTotal" => $bankBonusModel->countAll($this->company_details->company_id),
            "recordsFiltered" => $bankBonusModel->countFiltered($this->company_details->company_id),
            "data" => $data,
        );

        return $this->response->setJSON($output);
    }

    public function ajax_get_bankbonus($id = '')
    {
        if(!is_numeric($id)) 
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy ngân hàng này"));

        if(!has_permission('Config', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không chưa được cấp quyền"));
 
        
        $bankModel = model(BankModel::class);
        
        $result = $bankModel->find($id);
        
        if($result)
            return $this->response->setJSON(["status"=>TRUE, "data"=>$result]);
        else
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy dữ liệu"));
    }

    public function ajax_update_status()
    {
        if(!in_array($this->company_details->role, ['Admin', 'SuperAdmin']))
            show_404();

        if ($this->request->getMethod(true) != 'POST')
            return '';

        $id = xss_clean($this->request->getPost('company_id'));

        if (!is_numeric($id)){
            return $this->response->setJSON(array('status'=>FALSE,'message'=> 'Dữ liệu không hợp lệ'));
        }

        if($id != $this->company_details->id){
            return $this->response->setJSON(array('status'=>FALSE,'message'=> 'Dữ liệu không hợp lệ'));
        }

        $companyModel = model(CompanyModel::class);

        $result = $companyModel->find($id);

        if($result){
            $companyModel->update($id, [
                'bank_bonus_active' => true,
            ]);
            return $this->response->setJSON(array('status'=>TRUE,'message'=> 'Kích hoạt chương trình thành công !'));
        }else{
            return $this->response->setJSON(array('status'=>FALSE,'message'=> 'Không tìm thấy dữ liệu'));
        }
    }
}
