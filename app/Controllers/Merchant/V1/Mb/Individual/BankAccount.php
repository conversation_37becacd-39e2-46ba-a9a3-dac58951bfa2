<?php

namespace App\Controllers\Merchant\V1\Mb\Individual;

use Exception;
use App\Models\SimModel;
use App\Libraries\MbbClient;
use App\Models\BankAccountModel;
use App\Controllers\BaseController;
use App\Builders\Company\CompanyBuilder;
use App\Actions\Webhook\CreateWebhookAction;
use App\Builders\BankAccount\BankAccountBuilder;
use App\Controllers\Merchant\V1\Mb\MbController;
use App\Actions\Company\SetCompanyConfigurationAction;
use App\Actions\Merchant\CreateMerchantApiRequestAction;
use App\Actions\Merchant\DeleteMerchantApiRequestAction;
use App\Actions\BankAccount\DeleteCompanyBankAccountAction;
use App\Actions\BankAccount\ActiveBankAccountApiConnectionAction;

class BankAccount extends MbController
{
    public function lookUpAccountHolderName()
    {
        if ($this->determineIfMethodIsNotPost()) {
            return $this->respondNotFoundResponse();
        }

        $data = [
            'account_number' => trim(xss_clean($this->request->getVar('account_number')))
        ];

        $rules = [
            'account_number' => ['label' => 'Số tài khoản', 'rules' => ['required', 'max_length[20]']]
        ];

        if (! $this->validateData($data, $rules)) {
            return $this->respondBadRequestResponse($this->validator->getErrors());
        }

        try {
            $client = new MbbClient;
        } catch (Exception $e) {
            return $this->handleMbClientException($e);
        }

        try {
            $response = $client->getBankAccountInfo($data['account_number'], 'ACCOUNT', 'INHOUSE');
            $responseData = json_decode($response->getBody(), true);

            $errorCode = $responseData['errorCode'] ?? $responseData['soaErrorCode'];
            $errorDesc = $responseData['errorDesc'] ?? $responseData['soaErrorDesc'];

            if ($errorCode == '000') {
                return $this->respond(['code' => 200, 'data' => ['account_holder_name' => $responseData['data']['accountName']]]);
            }

            if (in_array($errorCode, ['3014', '3001', '200'])) {
                return $this->respondBadRequestResponse(['account_number' => 'Số tài khoản không tồn tại trên hệ thống ngân hàng MB.'], 4001);
            }

            throw new Exception($errorCode . ' - ' . implode(', ', $errorDesc));
        } catch (Exception $e) {
            return $this->handleMbClientException($e, $client);
        }
    }

    public function create()
    {
        if ($this->determineIfMethodIsNotPost()) {
            return $this->respondNotFoundResponse();
        }

        $data = (array) $this->request->getJSON();

        $rules = [
            'company_id' => [
                'label' => 'ID công ty/tổ chức',
                'rules' => ['required'],
            ],
            'account_holder_name' => [
                'label' => 'Tên chủ tài khoản',
                'rules' => ['required'],
            ],
            'account_number' => [
                'label' => 'Số tài khoản',
                'rules' => ['required', 'max_length[20]'],
            ],
            'identification_number' => [
                'label' => 'Số CCCD/CMND',
                'rules' => ['required', 'max_length[100]']
            ],
            'phone_number' => [
                'label' => 'Số điện thoại',
                'rules' => ['required', 'min_length[10]', 'max_length[20]']
            ],
            'label' => [
                'label' => 'Tên gợi nhớ',
                'rules' => ['permit_empty', 'max_length[100]']
            ]
        ];

        if (! $this->validateData($data, $rules)) {
            return $this->respondBadRequestResponse($this->validator->getErrors());
        }

        $company = CompanyBuilder::make()->whereMerchantId($this->merchant->id)->whereId($data['company_id'])->first();

        if (!$company) {
            return $this->respondBadRequestResponse(['company_id' => static::COMPANY_ID_NOT_EXIST_MESSAGE]);
        }

        $safeBankAccountData = [
            'company_id' => $company->id,
            'account_holder_name' => trim(xss_clean($data['account_holder_name'])),
            'account_number' => trim(xss_clean($this->request->getVar('account_number'))),
            'bank_id' => static::BANK_ID,
            'label' => trim(xss_clean($this->request->getVar('label'))),
            'active' => 1,
            'bank_api' => 1,
            'bank_api_connected' => 0,
            'identification_number' => trim(xss_clean($this->request->getVar('identification_number'))),
            'phone_number' => trim(xss_clean($this->request->getVar('phone_number'))),
            'merchant_id' => $this->merchant->id,
        ];

        if (BankAccountBuilder::make()->whereBankId(static::BANK_ID)->whereAccountNumber($safeBankAccountData['account_number'])->count()) {
            return $this->respondBadRequestResponse(['account_number' => 'Số tài khoản này đã tồn tại trên hệ thống SePay.'], 4001);
        }

        try {
            $client = new MbbClient;
        } catch (Exception $e) {
            return $this->handleMbClientException($e);
        }

        try {
            $response = $client->requestPushTransactionMessageSubscribe(
                $safeBankAccountData['identification_number'],
                $safeBankAccountData['account_number'],
                $safeBankAccountData['account_holder_name'],
                $safeBankAccountData['phone_number'],
                'SMS',
                $this->merchant && $this->merchant->trans_type ? $this->merchant->trans_type : 'DC',
                'WEB_APP'
            );
            $responseData = json_decode($response->getBody(), true);

            $errorCode = $responseData['errorCode'] ?? $responseData['soaErrorCode'];
            $errorDesc = $responseData['errorDesc'] ?? $responseData['soaErrorDesc'];

            // SIMULATE
            // $errorCode = '000';
            // $responseData['data']['requestId'] = uniqid();
            // SIMULATE

            if (in_array($errorCode, ['000', '40504'])) {
                $bankAccountModel = model(BankAccountModel::class);
                $bankAccountId = $bankAccountModel->insert($safeBankAccountData);

                $bankAccount = BankAccountBuilder::make()
                    ->select(BankAccountBuilder::DETAIL_SELECT)
                    ->whereMerchantId($this->merchant->id)
                    ->whereCompanyId($company->id)
                    ->whereId($bankAccountId)->first();

                CreateWebhookAction::run($bankAccount, $company, $this->merchant);

                if ($errorCode == '000') {
                    $requestId = CreateMerchantApiRequestAction::run($this->merchant->id, [
                        'mb_request_id' => $responseData['data']['requestId'],
                        'bank_account_id' => $bankAccountId,
                        'company_id' => $safeBankAccountData['company_id']
                    ]);
    
                    return $this->respond([
                        'code' => 2011,
                        'message' => 'Đã thêm tài khoản ngân hàng và gửi OTP xác thực liên kết API.',
                        'id' => $bankAccountId,
                        'data' => [
                            'request_id' => $requestId,
                        ]
                    ], 201);
                }

                // MB bank account has been connected to API yet.
                ActiveBankAccountApiConnectionAction::run($merchantApiRequestPayload['bank_account_id']);
                SetCompanyConfigurationAction::run($merchantApiRequestPayload['company_id'], 'BankSubAccount', true);

                return $this->respond([
                    'code' => 2012,
                    'message' => 'Đã liên kết API tài khoản ngân hàng thành công.',
                    'id' => $bankAccountId,
                ], 201);
            }

            if ($errorCode == '410')
                return $this->respondBadRequestResponse([
                    'identification_number' => 'Số CCCD/CMND không được đăng ký cho tài khoản ngân hàng.',
                    'phone_number' => 'Số điện thoại không được đăng ký cho tài khoản ngân hàng.'
                ], 4002);

            if ($errorCode == '293')
                return $this->respondBadRequestResponse(['identification_number' =>  'Số CCCD/CMND không được đăng ký cho tài khoản ngân hàng.'], 4003);

            if ($errorCode == '1020')
                return $this->respondBadRequestResponse(['account_number' => 'Số tài khoản không tồn tại trên hệ thống ngân hàng.'], 4004);

            if ($errorCode == '40600')
                return $this->respondBadRequestResponse(['account_holder_name' => 'Tên chủ tài khoản không khớp thông tin với tài khoản ngân hàng.'], 4005);

            if ($errorCode == '219')
                return $this->respondBadRequestResponse(['phone_number' => 'Số điện thoại không được đăng ký cho tài khoản ngân hàng.'], 4006);

            throw new Exception($errorCode . ' - ' . $errorDesc);
        } catch (Exception $e) {
            return $this->handleMbClientException($e, $client);
        }
    }

    public function requestApiConnection($bankAccountId = null)
    {
        if ($this->determineIfMethodIsNotPost()) {
            return $this->respondNotFoundResponse();
        }

        $bankAccountId = trim(xss_clean($bankAccountId));

        if (!$bankAccountId || !is_numeric($bankAccountId)) {
            return $this->respondNotFoundResponse();
        }

        $bankAccount = BankAccountBuilder::make()
            ->select(BankAccountBuilder::DETAIL_SELECT)
            ->whereMerchantId($this->merchant->id)
            ->whereId($bankAccountId)
            ->whereBankId(static::BANK_ID)->first();

        if (!$bankAccount) {
            return $this->respondNotFoundResponse(static::BANK_ACCOUNT_ID_NOT_EXIST_MESSAGE);
        }

        if ($bankAccount->bank_api_connected) {
            return $this->respond([
                'code' => 409,
                'message' => 'Tài khoản ngân hàng đã được liên kết API trước đó.'
            ], 409);
        }

        try {
            $client = new MbbClient;
        } catch (Exception $e) {
            return $this->handleMbClientException($e);
        }

        try {
            $response = $client->requestPushTransactionMessageSubscribe(
                $bankAccount->identification_number,
                $bankAccount->account_number,
                $bankAccount->account_holder_name,
                $bankAccount->phone_number,
                'SMS',
                $this->merchant && $this->merchant->trans_type ? $this->merchant->trans_type : 'DC',
                'WEB_APP'
            );
            $responseData = json_decode($response->getBody(), true);

            $errorCode = $responseData['errorCode'] ?? $responseData['soaErrorCode'];
            $errorDesc = $responseData['errorDesc'] ?? $responseData['soaErrorDesc'];

            // SIMULATE
            // $errorCode = '000';
            // $responseData['data']['requestId'] = uniqid();
            // SIMULATE

            if ($errorCode == '000') {
                $requestId = CreateMerchantApiRequestAction::run($this->merchant->id, [
                    'mb_request_id' => $responseData['data']['requestId'],
                    'bank_account_id' => $bankAccount->id, 
                    'company_id' => $bankAccount->id
                ]);

                return $this->respond([
                    'code' => 200,
                    'message' => 'Đã gửi OTP xác thực liên kết API.',
                    'data' => [
                        'request_id' => $requestId,
                    ]
                ]);
            }

            if ($errorCode == '410')
                return $this->respondBadRequestResponse([
                    'identification_number' => 'Số CCCD/CMND không được đăng ký cho tài khoản ngân hàng.',
                    'phone_number' => 'Số điện thoại không được đăng ký cho tài khoản ngân hàng.'
                ]);

            if ($errorCode == '293')
                return $this->respondBadRequestResponse(['identification_number' =>  'Số CCCD/CMND không được đăng ký cho tài khoản ngân hàng.']);

            if ($errorCode == '1020')
                return $this->respondBadRequestResponse(['account_number' => 'Số tài khoản không tồn tại trên hệ thống ngân hàng.']);

            if ($errorCode == '40600')
                return $this->respondBadRequestResponse(['account_holder_name' => 'Tên chủ tài khoản không khớp thông tin với tài khoản ngân hàng.']);

            if ($errorCode == '219')
                return $this->respondBadRequestResponse(['phone_number' => 'Số điện thoại không được đăng ký cho tài khoản ngân hàng.']);

            throw new Exception($errorCode . ' - ' . $errorDesc);
        } catch (Exception $e) {
            return $this->handleMbClientException($e, $client);
        }
    }

    public function confirmApiConnection()
    {
        if ($this->determineIfMethodIsNotPost()) {
            return $this->respondNotFoundResponse();
        }

        $merchantApiRequest = $this->findMerchantApiRequest();

        if (!$merchantApiRequest) {
            return $this->respondInvalidRequestIdResponse();
        }

        $data = (array) $this->request->getJSON();

        $rules = [
            'otp' => [
                'label' => 'OTP',
                'rules' => ['required', 'regex_match[/^[0-9]{8}$/]']
            ]
        ];

        if (! $this->validateData($data, $rules)) {
            return $this->respondBadRequestResponse($this->validator->getErrors());
        }

        $merchantApiRequestPayload = json_decode($merchantApiRequest->payload, true);

        try {
            $client = new MbbClient;
        } catch (Exception $e) {
            return $this->handleMbClientException($e);
        }

        try {
            $response = $client->confirmPushTransactionMessageSubscribe(
                $merchantApiRequestPayload['mb_request_id'],
                trim($data['otp']),
                'SMS',
                'WEB_APP'
            );
            $responseData = json_decode($response->getBody(), true);
                
            $errorCode = $responseData['errorCode'] ?? $responseData['soaErrorCode'];
            $errorDesc = $responseData['errorDesc'] ?? $responseData['soaErrorDesc'];

            // SIMUATE
            // $errorCode = '000';
            // SIMULATE

            if ($errorCode == '000') {
                ActiveBankAccountApiConnectionAction::run($merchantApiRequestPayload['bank_account_id']);
                SetCompanyConfigurationAction::run($merchantApiRequestPayload['company_id'], 'BankSubAccount', true);
                DeleteMerchantApiRequestAction::run($merchantApiRequest->request_id);

                return $this->respond([
                    'code' => 200,
                    'message' => 'Đã liên kết API tài khoản ngân hàng MB thành công.',
                ]);
            }

            if (in_array($errorCode, ['40507', '237'])) {
                return $this->respondBadRequestResponse(['otp' => 'OTP không chính xác hoặc đã hết hiệu lực.'], 4001);
            }

            throw new Exception($errorCode . ' - ' . $errorDesc);
        } catch (Exception $e) {
            return $this->handleMbClientException($e, $client);
        }
    }

    public function requestDelete($bankAccountId = null)
    {
        if ($this->determineIfMethodIsNotPost()) {
            return $this->respondNotFoundResponse();
        }

        $bankAccountId = trim(xss_clean($bankAccountId));

        if (!$bankAccountId || !is_numeric($bankAccountId)) {
            return $this->respondNotFoundResponse();
        }

        $bankAccount = BankAccountBuilder::make()
            ->select(BankAccountBuilder::DETAIL_SELECT)
            ->whereMerchantId($this->merchant->id)
            ->whereId($bankAccountId)
            ->whereBankId(static::BANK_ID)->first();

        if (!$bankAccount) {
            return $this->respondNotFoundResponse(static::BANK_ACCOUNT_ID_NOT_EXIST_MESSAGE);
        }

        if (!$bankAccount->bank_api_connected) {
            return $this->respond([
                'code' => 409,
                'message' => 'Tài khoản ngân hàng chưa được liên kết API trước đó.'
            ], 409);
        }

        try {
            $client = new MbbClient;
        } catch (Exception $e) {
            return $this->handleMbClientException($e);
        }

        try {
            $response = $client->requestPushTransactionMessageUnsubscribe(
                $bankAccount->account_number,
                'SMS',
                'WEB_APP'
            );
            $responseData = json_decode($response->getBody(), true);

            $errorCode = $responseData['errorCode'] ?? $responseData['soaErrorCode'];
            $errorDesc = $responseData['errorDesc'] ?? $responseData['soaErrorDesc'];

            // SIMULATE
            // $errorCode = '000';
            // $responseData['data']['requestId'] = uniqid();
            // SIMULATE

            if ($errorCode == '000') {
                $requestId = CreateMerchantApiRequestAction::run($this->merchant->id, [
                    'mb_request_id' => $responseData['data']['requestId'],
                    'bank_account_id' => $bankAccount->id, 
                    'company_id' => $bankAccount->company_id
                ]);

                return $this->respond([
                    'code' => 200,
                    'message' => 'Đã gửi OTP hủy liên kết API.',
                    'data' => [
                        'request_id' => $requestId
                    ]
                ]);
            }

            if (in_array($errorCode, ['432123', '432124'])) {
                $bankAccountModel = model(BankAccountModel::class);
                $bankAccountModel->where('id', $bankAccount->id)->set(['bank_api_connected' => 0])->update();

                return $this->respond([
                    'code' => 409,
                    'message' => 'Tài khoản ngân hàng chưa được liên kết API trước đó.'
                ], 409);
            }

            throw new Exception($errorCode . ' - ' . $errorDesc);
        } catch (Exception $e) {
            return $this->handleMbClientException($e, $client);
        }
    }

    public function confirmDelete()
    {
        if ($this->determineIfMethodIsNotPost()) {
            return $this->respondNotFoundResponse();
        }

        $merchantApiRequest = $this->findMerchantApiRequest();

        if (!$merchantApiRequest) {
            return $this->respondInvalidRequestIdResponse();
        }

        $data = (array) $this->request->getJSON();

        $rules = [
            'otp' => [
                'label' => 'OTP',
                'rules' => ['required', 'regex_match[/^[0-9]{8}$/]']
            ]
        ];

        if (! $this->validateData($data, $rules)) {
            return $this->respondBadRequestResponse($this->validator->getErrors());
        }

        $merchantApiRequestPayload = json_decode($merchantApiRequest->payload, true);

        $company = CompanyBuilder::make()
            ->select(CompanyBuilder::DETAIL_SELECT)
            ->whereMerchantId($this->merchant->id)
            ->whereId($merchantApiRequestPayload['company_id'])->first();

        $bankAccount = BankAccountBuilder::make()
            ->select(['*'])
            ->whereMerchantId($this->merchant->id)
            ->whereId($merchantApiRequestPayload['bank_account_id'])
            ->whereBankId(static::BANK_ID)->first();

        try {
            $client = new MbbClient;
        } catch (Exception $e) {
            return $this->handleMbClientException($e);
        }

        try {
            $response = $client->confirmPushTransactionMessageUnsubscribe(
                $merchantApiRequestPayload['mb_request_id'],
                trim($data['otp']),
                'SMS',
                'WEB_APP'
            );
            $responseData = json_decode($response->getBody(), true);
                
            $errorCode = $responseData['errorCode'] ?? $responseData['soaErrorCode'];
            $errorDesc = $responseData['errorDesc'] ?? $responseData['soaErrorDesc'];

            // SIMUATE
            // $errorCode = '000';
            // SIMULATE

            if ($errorCode == '000') {
                DeleteCompanyBankAccountAction::run($bankAccount, $company, $this->request->getIPAddress(), self::class);
                DeleteMerchantApiRequestAction::run($merchantApiRequest->request_id);

                return $this->respond([
                    'code' => 200,
                    'message' => 'Đã hủy liên kết API thành công.',
                ]);
            }

            if (in_array($errorCode, ['40507', '237'])) {
                return $this->respondBadRequestResponse(['otp' => 'OTP không chính xác hoặc đã hết hạn.'], 4001);
            }

            throw new Exception($errorCode . ' - ' . $errorDesc);
        } catch (Exception $e) {
            return $this->handleMbClientException($e, $client);
        }
    }

    public function forceDelete($bankAccountId = null)
    {
        if ($this->determineIfMethodIsNotPost()) {
            return $this->respondNotFoundResponse();
        }

        $bankAccountId = trim(xss_clean($bankAccountId));

        if (!$bankAccountId || !is_numeric($bankAccountId)) {
            return $this->respondNotFoundResponse();
        }

        $bankAccount = BankAccountBuilder::make()
            ->select(['*'])
            ->whereMerchantId($this->merchant->id)
            ->whereId($bankAccountId)
            ->whereBankId(static::BANK_ID)->first();

        if (!$bankAccount) {
            return $this->respondNotFoundResponse(static::BANK_ACCOUNT_ID_NOT_EXIST_MESSAGE);
        }

        if ($bankAccount->bank_api_connected) {
            return $this->respond([
                'code' => 400,
                'message' => 'Tài khoản ngân hàng này đang liên kết API, vui lòng hủy liên kết theo luồng OTP.'
            ], 400);
        }

        $company = CompanyBuilder::make()
            ->select(CompanyBuilder::DETAIL_SELECT)
            ->whereMerchantId($this->merchant->id)
            ->whereId($bankAccount->company_id)->first();

        DeleteCompanyBankAccountAction::run($bankAccount, $company, $this->request->getIPAddress(), self::class);

        return $this->respond([
            'code' => 200,
            'message' => 'Đã gỡ tài khoản ngân hàng thành công.',
        ]);
    }
}
