<?php

namespace App\Controllers\Merchant\V1\Mb;

use CodeIgniter\Controller;
use App\Models\CompanyModel;
use Psr\Log\LoggerInterface;
use CodeIgniter\HTTP\CLIRequest;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use App\Controllers\Merchant\V1\BaseController;

class MbController extends BaseController
{
    protected $mbConfig;

    protected const BANK_ID = 8;

    public function initController(RequestInterface $request, ResponseInterface $response, LoggerInterface $logger)
    {
        parent::initController($request, $response, $logger);

        $this->mbConfig = config(\Config\Mbb::class);
    }

    protected function handleMbClientException($e, $client = null)
    {
        log_message('error', 'MB Client Error (Controller): ' . $e->getMessage() . ' - ' . $e->getTraceAsString());

        if ($client) $client->logRequest();

        if (strpos($e->getMessage(), 'Operation timed out') !== false)
            return $this->respond([
                'code' => 504,
                'message' => 'Hệ thống ngân hàng MB đang bận, vui lòng thử lại sau.'
            ], 504);

        if (strpos($e->getMessage(), '40509 - Fail to subscribe to transaction notification') !== false)
            return $this->respond([
                'code' => 400,
                'message' => 'Tài khoản ngân hàng MB này đang liên kết ở nền tảng khác, vui lòng liên hệ SePay để được hỗ trợ.'
            ], 400);

        if ($e instanceof DisableBankClientException)
            return $this->respond([
                'code' => 503, 
                'message' => 'Hệ thống ngân hàng MB đang bảo trì, vui lòng liên hệ SePay để biết thêm thông tin chi tiết.'
            ], 503);    

        return $this->respond([
            'code' => 500, 
            'message' => 'Đã có lỗi xảy ra, vui lòng thử lại sau hoặc liên hệ SePay để được hỗ trợ.'
        ], 500);
    }
}
