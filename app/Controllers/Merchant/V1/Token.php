<?php

namespace App\Controllers\Merchant\V1;

use App\Models\MerchantModel;
use CodeIgniter\API\ResponseTrait;
use App\Models\MerchantAccessTokenModel;
use App\Controllers\Merchant\V1\BaseController;

class Token extends BaseController
{
    use ResponseTrait;

    public function create()
    {
        if ($this->determineIfMethodIsNotPost()) {
            return $this->respondNotFoundResponse();
        }

        $authorizationHeader = $this->request->getServer('HTTP_AUTHORIZATION');
        $authorizationHeaderPortion = explode(' ', $authorizationHeader);
        $token = trim($authorizationHeaderPortion[1] ?? '');

        if ($authorizationHeaderPortion[0] !== 'Basic' || !$token) {
            return $this->respondUnauthenticatedResponse();
        }

        $tokenPortion = explode(':', base64_decode($token));

        if (count($tokenPortion) !== 2) {
            return $this->respondUnauthenticatedResponse();
        }

        [$clientId, $clientSecret] = $tokenPortion;

        if (!$clientId || !$clientSecret) {
            return $this->respondUnauthenticatedResponse();
        }

        $merchantModel = model(MerchantModel::class);
        $merchantAccessTokenModel = model(MerchantAccessTokenModel::class);

        $merchant = $merchantModel->where([
            'client_id' => $clientId,
            'client_secret' => $clientSecret,
            'active' => 1
        ])->get()->getRow();

        if (!$merchant) {
            return $this->respondUnauthenticatedResponse();
        }

        $ttl = $this->merchantConfig->accessTokenTTL;
        $accessToken = sha1($token . microtime() . uniqid());

        $merchantAccessTokenModel->insert([
            'merchant_id' => $merchant->id,
            'token' => $accessToken,
            'ttl' => $ttl,
            'expires_at' => date('Y-m-d H:i:s', strtotime("+$ttl seconds"))
        ]);

        return $this->respond([
            'code' => 201,
            'message' => 'Resource created',
            'data' => [
                'access_token' => $accessToken,
                'ttl' => $ttl
            ],
        ], 201);
    }
}
