<?php

namespace App\Controllers\Merchant\V1;

use CodeIgniter\Controller;
use App\Models\CompanyModel;
use Psr\Log\LoggerInterface;
use App\Models\BankAccountModel;
use CodeIgniter\HTTP\CLIRequest;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;
use App\Models\MerchantApiRequestModel;
use CodeIgniter\HTTP\ResponseInterface;
use App\Builders\BankSubAccount\BankSubAccountBuilder;

abstract class BaseController extends Controller
{
    use ResponseTrait;
    
    /**
     * Instance of the main Request object.
     *
     * @var CLIRequest|IncomingRequest
     */
    protected $request;

    protected $helpers = ['general'];

    protected $merchant;

    protected $merchantConfig;

    public const BANK_ACCOUNT_ID_NOT_EXIST_MESSAGE = 'ID tài khoản ngân hàng không tồn tại.';
    public const BANK_SUB_ACCOUNT_ID_NOT_EXIST_MESSAGE = 'ID VA không tồn tại.';
    public const COMPANY_ID_NOT_EXIST_MESSAGE = 'ID công ty (tổ chức) không tồn tại.';
    public const TRANSACTION_ID_NOT_EXIST_MESSAGE = 'ID giao dịch không tồn tại.';
  
    public function initController(RequestInterface $request, ResponseInterface $response, LoggerInterface $logger)
    {
        parent::initController($request, $response, $logger);

        $this->uri = new \CodeIgniter\HTTP\URI();

        $this->merchant = $request->merchant ?? null;
        $this->merchantConfig = config(\App\Config\Merchant::class);
        $this->merchantConfig->accessTokenTTL = $this->merchantConfig->accessTokenTTL ?? 60;
    }

    protected function respondBadRequestResponse($errors = [], $code = 400, $message = 'Bad request')
    {
        return $this->respond([
            'code' => $code,
            'message' => $message,
            'errors' => $errors,
        ], 400);
    }

    protected function respondNotFoundResponse($message = 'Not found', $code = 404)
    {
        return $this->respond([
            'code' => $code,
            'message' => $message,
        ], 404);
    }

    protected function respondUnauthenticatedResponse($code = 401, $message = 'Unauthenticated')
    {
        return $this->respond([
            'code' => $code,
            'message' => $message,
        ], 401);
    }

    protected function respondServiceUnavailabelResponse() 
    {
        return $this->respond([
            'code' => 503,
            'message' => 'Service unavailable',
        ], 503);
    }

    protected function determineIfMethodIsNotPost()
    {
        return $this->request->getMethod(true) != 'POST';
    }

    protected function respondMerchantCompanyNotExistsResponse()
    {
        return $this->respondBadRequestResponse(['company_id' => 'ID công ty/tổ chức không tồn tại']);
    }

    protected function respondMerchantBankAccountNotExistsResponse()
    {
        return $this->respondBadRequestResponse(['bank_account_id' => 'ID tài khoản ngân hàng không tồn tại']);
    }

    protected function findMerchantApiRequest()
    {
        $requestId = $this->request->getHeaderLine('Request-Id');
        
        if (!$requestId) return null;

        return model(MerchantApiRequestModel::class)->where([
            'request_id' => $requestId, 
            'merchant_id' => $this->merchant->id,
            'expires_at >' => date('Y-m-d H:i:s')
        ])->get()->getRow(); 
    }

    protected function respondInvalidRequestIdResponse()
    {
        return $this->respond(['code' => 400, 'message' => 'Request-Id is invalid or expired'], 400);
    }
}
