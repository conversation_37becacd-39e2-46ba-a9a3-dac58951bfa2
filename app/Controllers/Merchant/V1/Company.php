<?php

namespace App\Controllers\Merchant\V1;

use Exception;
use App\Models\UserModel;
use App\Models\CompanyModel;
use App\Models\CompanyUserModel;
use CodeIgniter\API\ResponseTrait;
use App\Builders\Company\CompanyBuilder;
use App\Builders\Company\CounterBuilder;
use App\Builders\Company\ConfigurationBuilder;
use App\Controllers\Merchant\V1\BaseController;
use App\Actions\Company\GetCompanyConfigurationAction;
use App\Actions\Company\SetCompanyConfigurationAction;
use App\Actions\Company\InitCompanyConfigurationAction;

class Company extends BaseController
{
    public const CONFIGURATION_SETTING_ALIASES = [
        'PayCode' => 'payment_code',
        'PayCodePrefix' => 'payment_code_prefix',
        'PayCodeSuffixFrom' => 'payment_code_suffix_from',
        'PayCodeSuffixTo' => 'payment_code_suffix_to',
        'PayCodeSuffixCharacterType' => 'payment_code_suffix_character_type',
        'DataStorageTime' => 'data_storage_time',
        'TransactionAmount' => 'transaction_amount'
    ];

    public function create()
    {
        if ($this->determineIfMethodIsNotPost()) {
            return $this->respondNotFoundResponse();
        }
        
        $data = (array) $this->request->getJSON();

        $rules = [
            'full_name' => [
                'label' => 'Tên đầy đủ công ty/tổ chức',
                'rules' => ['required', 'max_length[200]']
            ],
            'short_name' => [
                'label' => 'Tên viết tắt công ty/tổ chức',
                'rules' => ['required', 'max_length[20]']
            ],
        ];

        if (! $this->validateData($data, $rules)) {
            return $this->respondBadRequestResponse($this->validator->getErrors());
        }

        $safeCompanyData = [
            'full_name' => trim(xss_clean($data['full_name'])),
            'short_name' => trim(xss_clean($data['short_name'])),
            'merchant_id' => $this->merchant->id,
        ];

        $companyModel = model(CompanyModel::class);

        $companyId = $companyModel->insert($safeCompanyData);

        if (!$companyId) {
            return $this->respondServiceUnavailabelResponse();
        }

        InitCompanyConfigurationAction::run($companyId, $this->merchant->id);

        return $this->respond([
            'code' => 201,
            'message' => 'Đã tạo công ty (tổ chức) thành công.',
            'id' => $companyId
        ]);
    }

    public function index()
    {
        $data = [
            'per_page' => $this->request->getGet('per_page') ?? 20,
            'q' => trim(xss_clean($this->request->getGet('q'))),
            'status' => trim(xss_clean($this->request->getGet('status'))),
            'sort' => [
                'created_at' => trim(xss_clean($this->request->getGet('sort')['created_at'] ?? 'desc')) 
            ]
        ];

        $builder = CompanyBuilder::make()
            ->select(CompanyBuilder::INDEX_SELECT)
            ->search($data['q'])
            ->whereMerchantId($this->merchant->id)
            ->whereStatus($data['status'])
            ->sortCreatedAt($data['sort']['created_at'])
            ->paginate($data['per_page']);

        return $this->respond([
            'data' => $builder->data(),
            'meta' => $builder->meta(),
        ]);
    }

    public function edit($companyId = null)
    {
        if ($this->determineIfMethodIsNotPost()) {
            return $this->respondNotFoundResponse();
        }

        if (!$companyId || !is_numeric($companyId)) {
            return $this->respondNotFoundResponse();
        }

        $companyModel = model(CompanyModel::class);

        if (! CompanyBuilder::make()->whereMerchantId($this->merchant->id)->whereId($companyId)->count()) {
            return $this->respondNotFoundResponse(static::COMPANY_ID_NOT_EXIST_MESSAGE);
        }

        $data = (array) $this->request->getJSON();

        $rules = [
            'full_name' => [
                'label' => 'Tên đầy đủ công ty/tổ chức',
                'rules' => ['if_exist', 'required', 'max_length[200]']
            ],
            'short_name' => [
                'label' => 'Tên viết tắt công ty/tổ chức',
                'rules' => ['if_exist', 'required', 'max_length[20]']
            ],
            'status' => [
                'label' => 'Trạng thái', 
                'rules' => ['if_exist', 'required', 'in_list[Pending,Active,Suspended,Terminated,Cancelled,Fraud]'],
            ],
        ];

        if (! $this->validateData($data, $rules)) {
            return $this->respondBadRequestResponse($this->validator->getErrors());
        }

        $safeCompanyData = [];

        if (isset($data['full_name'])) {
            $safeCompanyData['full_name'] = trim(xss_clean($data['full_name']));
        }

        if (isset($data['short_name'])) {
            $safeCompanyData['short_name'] = trim(xss_clean($data['short_name']));
        }

        if (isset($data['status'])) {
            $safeCompanyData['status'] = $data['status'];
        }

        if (count($safeCompanyData)) {
            $companyModel->update($companyId, $safeCompanyData);
        }

        return $this->respond([
            'code' => 200,
            'message' => 'Đã cập nhật thông tin công ty (tổ chức) thành công.',
        ], 200);
    }

    public function details($companyId = null)
    {
        if (!$companyId || !is_numeric($companyId)) {
            return $this->respondNotFoundResponse();
        }

        $company = CompanyBuilder::make()
            ->select(CompanyBuilder::DETAIL_SELECT)
            ->whereMerchantId($this->merchant->id)
            ->whereId($companyId)
            ->first();

        if (! $company) {
            return $this->respondNotFoundResponse(static::COMPANY_ID_NOT_EXIST_MESSAGE);
        }

        return $this->respond([
            'data' => [
                'id' => $company->id,
                'full_name' => $company->full_name,
                'short_name' => $company->short_name,
                'status' => $company->status,
                'active' => $company->active,
                'created_at' => $company->created_at,
                'updated_at' => $company->updated_at,   
                'merchant_id' => $company->merchant_id            
            ]
        ], 200);
    }

    public function configuration($companyId)
    {
        if (!$companyId || !is_numeric($companyId)) {
            return $this->respondNotFoundResponse();
        }

        if (! CompanyBuilder::make()->whereMerchantId($this->merchant->id)->whereId($companyId)->count()) {
            return $this->respondNotFoundResponse(static::COMPANY_ID_NOT_EXIST_MESSAGE);
        }

        if ($this->determineIfMethodIsNotPost()) {
            return $this->configurationDetails($companyId);
        }

        return $this->editConfiguration($companyId);
    }

    protected function configurationDetails($companyId)
    {
        $configuration = ConfigurationBuilder::make()->select(ConfigurationBuilder::INDEX_SELECT)
            ->whereMerchantId($this->merchant->id)
            ->whereCompanyId($companyId)->get()->data();

        $hashmapConfiguration = [];
        $aliases = static::CONFIGURATION_SETTING_ALIASES;

        foreach ($configuration as $configItem) {
            if (isset($aliases[$configItem['setting']])) {
                $hashmapConfiguration[$aliases[$configItem['setting']]] = $configItem['value'];
            }
        }
        
        return $this->respond([
            'data' => $hashmapConfiguration
        ], 200);
    }

    protected function editConfiguration($companyId)
    {
        $aliases = static::CONFIGURATION_SETTING_ALIASES;
        $data = (array) $this->request->getJSON();

        $rules = [
            $aliases['PayCode'] => [
                'label' => 'Cấu hình nhận diện mã thanh toán',
                'rules' => ['if_exist', 'in_list[on,off]']
            ],
            $aliases['PayCodePrefix'] => [
                'label' => 'Cấu hình tiền tố mã thanh toán',
                'rules' => ['if_exist', 'alpha', 'min_length[2]', 'max_length[5]']
            ],
            $aliases['PayCodeSuffixFrom'] => [
                'label' => 'Cấu hình độ dài tối thiểu hậu tố mã thanh toán',
                'rules' => ['if_exist', 'is_natural', 'less_than_equal_to[30]', 'greater_than_equal_to[1]']
            ],
            $aliases['PayCodeSuffixTo'] => [
                'label' => 'Cấu hình độ dài tối đa hậu tố mã thanh toán',
                'rules' => ['if_exist', 'is_natural', 'less_than_equal_to[30]', 'greater_than_equal_to[1]']
            ],
            $aliases['PayCodeSuffixCharacterType'] => [
                'label' => 'Cấu hình kiểu ký tự hậu tố mã thanh toán',
                'rules' => ['if_exist', 'in_list[NumberAndLetter,NumberOnly]']
            ],
            $aliases['TransactionAmount'] => [
                'label' => 'Cấu hình số lượng giao dịch',
                'rules' => ['if_exist']
            ],
        ];

        if (! $this->validateData($data, $rules)) {
            return $this->respondBadRequestResponse($this->validator->getErrors());
        }

        if (isset($data[$aliases['PayCodeSuffixFrom']]) && ! isset($data[$aliases['PayCodeSuffixTo']])) {
            $paymentCodeSuffixTo = GetCompanyConfigurationAction::run($companyId, 'PayCodeSuffixTo')->value;

            if ($data[$aliases['PayCodeSuffixFrom']] > $paymentCodeSuffixTo) {
                return $this->respondBadRequestResponse([$aliases['PayCodeSuffixFrom'] => 'Cấu hình độ dài tối thiểu hậu tố mã thanh toán phải nhỏ hơn hoặc bằng cấu hình độ dài tối đa hậu tố mã thanh toán.']);
            }
        }

        if (isset($data[$aliases['PayCodeSuffixTo']]) && ! isset($data[$aliases['PayCodeSuffixFrom']])) {
            $paymentCodeSuffixFrom = GetCompanyConfigurationAction::run($companyId, 'PayCodeSuffixFrom')->value;

            if ($data[$aliases['PayCodeSuffixTo']] < $paymentCodeSuffixTo) {
                return $this->respondBadRequestResponse([$aliases['PayCodeSuffixFrom'] => 'Cấu hình độ dài tối đa hậu tố mã thanh toán phải lớn hơn hoặc bằng cấu hình độ dài tối thiểu hậu tố mã thanh toán.']);
            }
        }

        if (isset($data[$aliases['PayCodeSuffixTo']]) && isset($data[$aliases['PayCodeSuffixFrom']])) {
            if ($data[$aliases['PayCodeSuffixFrom']] > $data[$aliases['PayCodeSuffixTo']]) {
                return $this->respondBadRequestResponse([$aliases['PayCodeSuffixFrom'] => 'Cấu hình độ dài tối thiểu hậu tố mã thanh toán phải nhỏ hơn hoặc bằng cấu hình độ dài tối đa hậu tố mã thanh toán.']);
            }
        }

        if (isset($data[$aliases['TransactionAmount']])) {
            if ((is_numeric($data[$aliases['TransactionAmount']]) && $data[$aliases['TransactionAmount']] < 0) || (!is_numeric($data[$aliases['TransactionAmount']]) && $data[$aliases['TransactionAmount']] !== 'Unlimited')) {
                return $this->respondBadRequestResponse([$aliases['TransactionAmount'] => 'Cấu hình số lượng giao dịch phải là số nguyên dương hoặc "Unlimited".']);
            }
        }

        $updated = false;

        foreach ($data as $setting => $value) {
            try {
                $key = array_flip($aliases)[$setting];
                $updated = SetCompanyConfigurationAction::run($companyId, $key, trim(xss_clean($value)));
            } catch (Exception $e) {
                continue;
            }
        }
        
        SetCompanyConfigurationAction::run($companyId, 'PayCodeStructures', '');

        return $this->respond([
            'code' => 200,
            'message' => $updated ? 'Đã cập nhật cấu hình công ty (tổ chức) thành công.' : 'Chưa cập nhật thông tin nào.',
        ], 200);
    }

    public function counter($companyId)
    {
        $data = [
            'date' => trim(xss_clean($this->request->getGet('date'))),
        ];

        if (!$companyId || !is_numeric($companyId)) {
            return $this->respondNotFoundResponse();
        }

        if (! CompanyBuilder::make()->whereMerchantId($this->merchant->id)->whereId($companyId)->count()) {
            return $this->respondNotFoundResponse(static::COMPANY_ID_NOT_EXIST_MESSAGE);
        }

        $builder = CounterBuilder::make()
            ->select(CounterBuilder::INDEX_SELECT)
            ->whereMerchantId($this->merchant->id)
            ->whereCompanyId($companyId)
            ->whereDate($data['date'])
            ->get();

        $data = $builder->data();

        return $this->respond([
            'data' => [
                'dates' => $data,
                'total' => [
                    'transaction' => array_sum(array_column($data, 'transaction')),
                    'transaction_in' => array_sum(array_column($data, 'transaction_in')),
                    'transaction_out' => array_sum(array_column($data, 'transaction_out')),
                ],
            ]
        ]);
    }
}
