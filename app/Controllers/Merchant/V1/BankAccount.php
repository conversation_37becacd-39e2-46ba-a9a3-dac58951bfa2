<?php

namespace App\Controllers\Merchant\V1;

use App\Controllers\Merchant\V1\BaseController;
use App\Builders\BankAccount\BankAccountBuilder;

class BankAccount extends BaseController
{
    public function index()
    {
        $data = [
            'per_page' => $this->request->getGet('per_page') ?? 20,
            'company_id' => trim(xss_clean($this->request->getGet('company_id'))),
            'bank_id' => trim(xss_clean($this->request->getGet('bank_id'))),
            'q' => trim(xss_clean($this->request->getGet('q')))
        ];

        $builder = BankAccountBuilder::make()
            ->select(BankAccountBuilder::INDEX_SELECT)
            ->search($data['q'])
            ->whereMerchantId($this->merchant->id)
            ->whereCompanyId($data['company_id'])
            ->whereBankId($data['bank_id'])
            ->paginate($data['per_page']);

        return $this->respond([
            'data' => $builder->data(),
            'meta' => $builder->meta(),
        ]);
    }

    public function details($bankAccountId = null)
    {
        $bankAccountId = trim(xss_clean($bankAccountId));

        if (!$bankAccountId || !is_numeric($bankAccountId)) {
            return $this->respondNotFoundResponse();
        }

        $bankAccount = BankAccountBuilder::make()
            ->select(BankAccountBuilder::DETAIL_SELECT)
            ->whereMerchantId($this->merchant->id)
            ->whereId($bankAccountId)
            ->first();

        if (!$bankAccount) {
            return $this->respondNotFoundResponse(static::BANK_ACCOUNT_ID_NOT_EXIST_MESSAGE);
        }

        return $this->respond([
            'data' => [
                'id' => $bankAccount->id,
                'company_id' => $bankAccount->company_id,
                'bank_id' => $bankAccount->bank_id,
                'account_holder_name' => $bankAccount->account_holder_name,
                'account_number' => $bankAccount->account_number,
                'accumulated' => $bankAccount->accumulated,
                'label' => $bankAccount->label,
                'bank_api_connected' => $bankAccount->bank_api_connected,
                'active' => $bankAccount->active,
                'last_transaction' => $bankAccount->last_transaction,
                'created_at' => $bankAccount->created_at,
                'updated_at' => $bankAccount->updated_at,
                'merchant_id' => $bankAccount->merchant_id,
            ]
        ]);
    }
}
