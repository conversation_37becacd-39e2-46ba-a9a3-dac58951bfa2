<?php

namespace App\Controllers\Merchant\V1\Klb;

use Exception;
use src\KPayClient;
use App\Libraries\OcbClient;
use App\Models\BankAccountModel;
use App\Models\ConfigurationModel;
use App\Models\BankSubAccountModel;
use App\Builders\Company\CompanyBuilder;
use App\Models\OcbBankSubAccountMetaData;
use App\Builders\BankAccount\BankAccountBuilder;
use App\Controllers\Merchant\V1\Klb\KlbController;
use App\Controllers\Merchant\V1\Ocb\OcbController;
use App\Actions\Company\GetCompanyConfigurationAction;
use App\Actions\Company\SetCompanyConfigurationAction;
use App\Builders\BankSubAccount\BankSubAccountBuilder;
use App\Actions\Merchant\CreateMerchantApiRequestAction;
use App\Actions\Merchant\DeleteMerchantApiRequestAction;
use src\virtualaccount\request\EnableVirtualAccountRequest;
use src\virtualaccount\request\DisableVirtualAccountRequest;
use App\Actions\BankAccount\ActiveBankAccountApiConnectionAction;

class VA extends KlbController
{
    public function create()
    {
        if ($this->determineIfMethodIsNotPost()) {
            return $this->respondNotFoundResponse();
        }

        $data = (array) $this->request->getJSON();

        $defaultVANumberMinLength = 1;

        $rules = [
            'company_id' => [
                'label' => 'ID công ty/tổ chức',
                'rules' => ['required'],
            ],
            'bank_account_id' => [
                'label' => 'ID tài khoản ngân hàng',
                'rules' => ['required']
            ],
            'label' => [
                'label' => 'Tên gợi nhớ',
                'rules' => ['permit_empty', 'max_length[100]']
            ]
        ];

        if (! $this->validateData($data, $rules)) {
            return $this->respondBadRequestResponse($this->validator->getErrors());
        }

        $safeBankSubAccountData = [
            'merchant_id' => $this->merchant->id,
            'bank_account_id' => trim(xss_clean($data['bank_account_id'])),
            'acc_type' => 'Real',
            'label' => trim(xss_clean($data['label'] ?? '')),
            'acc_type' => 'Real',
        ];

        $company = CompanyBuilder::make()
            ->whereMerchantId($this->merchant->id)
            ->whereId(trim(xss_clean($data['company_id'])))->first();

        if (!$company) {
            return $this->respondMerchantCompanyNotExistsResponse();
        }

        $bankAccount = BankAccountBuilder::make()
            ->select(BankAccountBuilder::DETAIL_SELECT)
            ->whereMerchantId($this->merchant->id)
            ->whereId($safeBankSubAccountData['bank_account_id'])
            ->whereBankId(static::BANK_ID)->first();

        if (!$bankAccount) {
            return $this->respondMerchantBankAccountNotExistsResponse();
        }

        $bankSubAccountModel = model(BankSubAccountModel::class);

        if ($bankSubAccountModel->where('bank_account_id', $bankAccount->id)->countAllResults() >= 100) {
            return $this->respond([
                'code' => 400,
                'message' => 'Số lượng VA đã đạt giới hạn tối đa cho tài khoản ngân hàng này.',
            ], 400);
        }

        try {
            $client = new KPayClient($this->klbPacker);
        } catch (Exception $e) {
            return $this->handleKlbClientException($e);
        }

        try {
            $configurationModel = model(ConfigurationModel::class);

            if (!$configurationModel->where(['company_id' => 0, 'setting' => 'KLBVaOrderLastNumber'])->countAllResults()) {
                $configurationModel->insert(['company_id' => 0, 'setting' => 'KLBVaOrderLastNumber', 'value' => 0]);
            }

            $safeBankSubAccountData['sub_holder_name'] = $bankAccount->account_holder_name;
            $safeBankSubAccountData['va_order'] = GetCompanyConfigurationAction::run(0, 'KLBVaOrderLastNumber', 0)->value + 1;

            $timeout = 0;
            $fixAmount = 0;
            $fixContent = '';
            $request = new EnableVirtualAccountRequest($safeBankSubAccountData['va_order'], $timeout, $fixAmount, $fixContent, $bankAccount->account_number);
            $response = $client->enableVirtualAccount($request);

            if (!is_object($response) || (is_object($response) && !property_exists($response, 'virtualAccount'))) {
                throw new Exception('VA create failed');
            }

            $safeBankSubAccountData['sub_account'] = $response->virtualAccount;

            $bankSubAccountId = $bankSubAccountModel->insert($safeBankSubAccountData);

            SetCompanyConfigurationAction::run(0, 'KLBVaOrderLastNumber', $safeBankSubAccountData['va_order']);

            if (!$bankSubAccountId) {
                log_message('error', 'BankSubAccountModel insert failed - ' . json_encode($safeBankSubAccountData));

                return $this->respondServiceUnavailabelResponse();
            }

            return $this->respond([
                'code' => 201,
                'message' => 'Đã tạo VA thành công.',
                'id' => $bankSubAccountId
            ]);
        } catch (Exception $e) {
            return $this->handleKlbClientException($e, $client);
        }
    }

    public function index()
    {
        $data = [
            'per_page' => $this->request->getGet('per_page') ?? 20,
            'company_id' => $this->request->getGet('company_id'),
            'bank_account_id' => $this->request->getGet('bank_account_id'),
            'q' => trim(xss_clean($this->request->getGet('q')))
        ];

        $builder = BankSubAccountBuilder::make()
            ->select(BankSubAccountBuilder::INDEX_SELECT)
            ->search($data['q'])
            ->whereMerchantId($this->merchant->id)
            ->whereBankId(static::BANK_ID)
            ->whereCompanyId($data['company_id'])
            ->whereBankAccountId($data['bank_account_id'])
            ->paginate($data['per_page']);

        return $this->respond([
            'data' => $builder->data(),
            'meta' => $builder->meta()
        ]);
    }

    public function details($bankSubAccountId = '')
    {
        $bankSubAccount = BankSubAccountBuilder::make()
            ->select(BankSubAccountBuilder::DETAIL_SELECT)
            ->whereId($bankSubAccountId)
            ->whereBankId(static::BANK_ID)
            ->whereMerchantId($this->merchant->id)->first();

        if (! $bankSubAccount) {
            return $this->respondNotFoundResponse('ID VA không tồn tại trên hệ thống.');
        }

        return $this->respond([
            'data' => [
                'id' => $bankSubAccount->id,
                'company_id' => $bankSubAccount->company_id,
                'bank_account_id' => $bankSubAccount->bank_account_id,
                'va' => $bankSubAccount->va,
                'label' => $bankSubAccount->label,
                'active' => $bankSubAccount->active,
                'created_at' => $bankSubAccount->created_at,
                'updated_at' => $bankSubAccount->updated_at,
            ]
        ]);
    }

    public function enable($bankSubAccountId = '')
    {
        if ($this->determineIfMethodIsNotPost()) {
            return $this->respondNotFoundResponse();
        }
        
        $bankSubAccount = BankSubAccountBuilder::make()
            ->select(BankSubAccountBuilder::DETAIL_SELECT)
            ->whereId($bankSubAccountId)
            ->whereBankId(static::BANK_ID)
            ->whereMerchantId($this->merchant->id)->first();

        if (! $bankSubAccount) {
            return $this->respondNotFoundResponse('ID VA không tồn tại trên hệ thống.');
        }

        if ($bankSubAccount->va_active) {
            return $this->respond([
                'code' => 400,
                'message' => 'VA đang hoạt động, không thể kích hoạt nữa',
            ], 400);
        }

        $bankSubAccountModel = model(BankSubAccountModel::class);
        
        $bankAccount = BankAccountBuilder::make()
            ->select(BankAccountBuilder::DETAIL_SELECT)
            ->whereMerchantId($this->merchant->id)
            ->whereId($bankSubAccount->bank_account_id)
            ->whereBankId(static::BANK_ID)->first();

        if (!$bankAccount) {
            return $this->respondNotFoundResponse('ID VA không tồn tại trên hệ thống.');
        }

        try {
            $client = new KPayClient($this->klbPacker);
        } catch (Exception $e) {
            return $this->handleKlbClientException($e);
        }
        
        try {
            $timeout = 0;
            $fixAmount = 0;
            $fixContent = ''; 
            $request = new EnableVirtualAccountRequest($bankSubAccount->va_order, $timeout, $fixAmount, $fixContent, $bankAccount->account_number);
            $response = $client->enableVirtualAccount($request);

            if (!is_object($response) || (is_object($response) && !property_exists($response, 'virtualAccount'))) {
                throw new Exception('VA enable failed');
            }

            $updated = $bankSubAccountModel->where(['id' => $bankSubAccount->id])->set(['va_active' => 1])->update();

            return $this->respond([
                'code' => 200,
                'message' => 'Đã kích hoạt lại VA thành công',
            ]);
        } catch (Exception $e) {
            return $this->handleKlbClientException($e);
        } 
    }

    public function disable($bankSubAccountId = '')
    {
        if ($this->determineIfMethodIsNotPost()) {
            return $this->respondNotFoundResponse();
        }

        $bankSubAccount = BankSubAccountBuilder::make()
            ->select(BankSubAccountBuilder::DETAIL_SELECT)
            ->whereId($bankSubAccountId)
            ->whereBankId(static::BANK_ID)
            ->whereMerchantId($this->merchant->id)->first();

        if (! $bankSubAccount) {
            return $this->respondNotFoundResponse('ID VA không tồn tại trên hệ thống.');
        }

        if (!$bankSubAccount->va_active) {
            return $this->respond([
                'code' => 400,
                'message' => 'VA đã bị vô hiệu hóa, không thể thực hiện lại nữa',
            ], 400);
        }

        $bankSubAccountModel = model(BankSubAccountModel::class);

        try {
            $client = new KPayClient($this->klbPacker);
        } catch (Exception $e) {
            return $this->handleKlbClientException($e);
        }
        
        try {
            $request = new DisableVirtualAccountRequest($bankSubAccount->va_order);
            $response = $client->disableVirtualAccount($request);   

            if (!is_object($response) || (is_object($response) && !property_exists($response, 'success'))) {
                throw new Exception('VA disable failed');
            }

            $updated = $bankSubAccountModel->where(['id' => $bankSubAccountId])->set(['va_active' => 0])->update();

            return $this->respond([
                'code' => 200,
                'message' => 'Đã vô hiệu hóa VA thành công',
            ]);
        } catch (Exception $e) {
            return $this->handleKlbClientException($e);
        } 
    }
}
