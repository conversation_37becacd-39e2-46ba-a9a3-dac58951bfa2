<?php

namespace App\Controllers\Merchant\V1\Klb;

use src\KPayPacker;
use CodeIgniter\Controller;
use App\Models\CompanyModel;
use Psr\Log\LoggerInterface;
use CodeIgniter\HTTP\CLIRequest;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use App\Controllers\Merchant\V1\BaseController;

class KlbController extends BaseController
{
    protected $klbConfig;
    
    protected $klbPacker;

    protected const BANK_ID = 17;

    public function initController(RequestInterface $request, ResponseInterface $response, LoggerInterface $logger)
    {
        parent::initController($request, $response, $logger);

        $this->klbConfig = config(\Config\Klb::class);

        $this->klbPacker = new KPayPacker(
            $this->klbConfig->KlbClientID,
            $this->klbConfig->KlbEncryptKey,
            $this->klbConfig->KlbSecretKey,
            $this->klbConfig->KlbAcceptTimeDiff,
            $this->klbConfig->KlbHost,
        );
    }

    protected function handleKlbClientException($e, $client = null)
    {
        log_message('error', 'KLB Client Error (Controller): ' . $e->getMessage() . ' - ' . $e->getTraceAsString());

        if (strpos($e->getMessage(), 'Operation timed out') !== false) {
            return $this->respond([
                'code' => 504,
                'message' => 'Hệ thống ngân hàng KienLongBank đang bận, vui lòng thử lại sau.'
            ], 504);
        }

        if ($e instanceof DisableBankClientException) {
            return $this->respond([
                'code' => 503, 
                'message' => 'Hệ thống ngân hàng KienLongBank đang bảo trì, vui lòng liên hệ SePay để biết thêm thông tin chi tiết.'
            ], 503);
        }

        return $this->respond([
            'code' => 500, 
            'message' => 'Đã có lỗi xảy ra, vui lòng thử lại sau hoặc liên hệ SePay để được hỗ trợ.'
        ], 500);
    }
}
