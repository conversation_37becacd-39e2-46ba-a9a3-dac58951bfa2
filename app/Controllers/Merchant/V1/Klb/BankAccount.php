<?php

namespace App\Controllers\Merchant\V1\Klb;

use Exception;
use src\KPayClient;
use App\Models\SimModel;
use App\Libraries\MbbClient;
use App\Models\BankAccountModel;
use App\Controllers\BaseController;
use App\Builders\Company\CompanyBuilder;
use App\Actions\Webhook\CreateWebhookAction;
use App\Builders\BankAccount\BankAccountBuilder;
use App\Controllers\Merchant\V1\Klb\KlbController;
use src\verifyaccountno\request\LinkAccountRequest;
use App\Actions\Company\SetCompanyConfigurationAction;
use src\verifyaccountno\request\CheckAccountNoRequest;
use App\Actions\Merchant\CreateMerchantApiRequestAction;
use App\Actions\Merchant\DeleteMerchantApiRequestAction;
use src\verifyaccountno\request\VerifyLinkAccountRequest;
use App\Actions\BankAccount\DeleteCompanyBankAccountAction;
use App\Actions\BankAccount\ActiveBankAccountApiConnectionAction;

class BankAccount extends KlbController
{
    public function lookUpAccountHolderName()
    {
        if ($this->determineIfMethodIsNotPost()) {
            return $this->respondNotFoundResponse();
        }

        $data = [
            'account_number' => trim(xss_clean($this->request->getVar('account_number')))
        ];

        $rules = [
            'account_number' => ['label' => 'Số tài khoản', 'rules' => ['required', 'max_length[20]']]
        ];

        if (! $this->validateData($data, $rules)) {
            return $this->respondBadRequestResponse($this->validator->getErrors());
        }

        try {
            $client = new KPayClient($this->klbPacker);
        } catch (Exception $e) {
            return $this->handleKlbClientException($e);
        }

        try {
            $request = new CheckAccountNoRequest($data['account_number']);
           
            try {
                $response = $client->checkAccountNo($request);
            } catch (Exception $e) {
                $response = null;
            }

            if (is_object($response) && method_exists($response, 'getAccountName')) {
                return $this->respond(['code' => 200, 'data' => ['account_holder_name' => $response->getAccountName()]]);
            }

            return $this->respondBadRequestResponse(['account_number' => 'Số tài khoản không tồn tại trên hệ thống ngân hàng KienLongBank.'], 4001);
        } catch (Exception $e) {
            return $this->handleKlbClientException($e, $client);
        }
    }

    public function create()
    {
        if ($this->determineIfMethodIsNotPost()) {
            return $this->respondNotFoundResponse();
        }

        $data = (array) $this->request->getJSON();

        $rules = [
            'company_id' => [
                'label' => 'ID công ty/tổ chức',
                'rules' => ['required'],
            ],
            'account_number' => [
                'label' => 'Số tài khoản',
                'rules' => ['required', 'max_length[20]'],
            ],
            'label' => [
                'label' => 'Tên gợi nhớ',
                'rules' => ['permit_empty', 'max_length[100]']
            ]
        ];

        if (! $this->validateData($data, $rules)) {
            return $this->respondBadRequestResponse($this->validator->getErrors());
        }

        $company = CompanyBuilder::make()->whereMerchantId($this->merchant->id)->whereId($data['company_id'])->first();

        if (!$company) {
            return $this->respondMerchantCompanyNotExistsResponse();
        }

        $safeBankAccountData = [
            'company_id' => $company->id,
            'account_number' => trim(xss_clean($this->request->getVar('account_number'))),
            'bank_id' => static::BANK_ID,
            'label' => trim(xss_clean($this->request->getVar('label'))),
            'active' => 1,
            'bank_api' => 1,
            'bank_api_connected' => 0,
            'merchant_id' => $this->merchant->id,
        ];

        $bankAccountModel = model(BankAccountModel::class);

        if ($bankAccountModel->where([
            'account_number' => $safeBankAccountData['account_number'],
            'bank_id' => static::BANK_ID
        ])->countAllResults()) {
            return $this->respondBadRequestResponse(['account_number' => 'Số tài khoản này đã tồn tại trên hệ thống SePay'], 4001);
        }

        try {
            $client = new KPayClient($this->klbPacker);
        } catch (Exception $e) {
            return $this->handleKlbClientException($e);
        }

        try {
            try {
                $lookupAccountHolderNameRequest = new CheckAccountNoRequest($data['account_number']);
                $lookupAccountHolderNameResponse = $client->checkAccountNo($lookupAccountHolderNameRequest);
            } catch (Exception $e) {
                return $this->respondBadRequestResponse(['account_number' => 'Số tài khoản không tồn tại trên hệ thống ngân hàng KienLongBank'], 4001);
            }

            $safeBankAccountData['account_holder_name'] = $lookupAccountHolderNameResponse->getAccountName();

            $linkAccountRequest = new LinkAccountRequest($safeBankAccountData['account_number']);
            $linkAccountResponse = $client->linkAccountNo($linkAccountRequest);
            
            $bankAccountId = $bankAccountModel->insert($safeBankAccountData);

            $bankAccount = BankAccountBuilder::make()
                ->select(BankAccountBuilder::DETAIL_SELECT)
                ->whereMerchantId($this->merchant->id)
                ->whereCompanyId($company->id)
                ->whereId($bankAccountId)->first();

            CreateWebhookAction::run($bankAccount, $company, $this->merchant);

            $requestId = CreateMerchantApiRequestAction::run($this->merchant->id, [
                'klb_request_id' => $linkAccountResponse->getSessionId(),
                'bank_account_id' => $bankAccountId,
                'company_id' => $safeBankAccountData['company_id']
            ]);

            return $this->respond([
                'code' => 2011,
                'message' => 'Đã thêm tài khoản ngân hàng và gửi OTP xác thực liên kết API.',
                'id' => $bankAccountId,
                'data' => [
                    'request_id' => $requestId,
                ]
            ], 201);
        } catch (Exception $e) {
            return $this->handleKlbClientException($e);
        }
    }

    public function requestApiConnection($bankAccountId = null)
    {
        if ($this->determineIfMethodIsNotPost()) {
            return $this->respondNotFoundResponse();
        }

        $bankAccountId = trim(xss_clean($bankAccountId));

        if (!$bankAccountId || !is_numeric($bankAccountId)) {
            return $this->respondNotFoundResponse();
        }

        $bankAccount = BankAccountBuilder::make()
            ->select(BankAccountBuilder::DETAIL_SELECT)
            ->whereMerchantId($this->merchant->id)
            ->whereId($bankAccountId)
            ->whereBankId(static::BANK_ID)->first();

        if (!$bankAccount) {
            return $this->respondNotFoundResponse(static::BANK_ACCOUNT_ID_NOT_EXIST_MESSAGE);
        }

        if ($bankAccount->bank_api_connected) {
            return $this->respond([
                'code' => 409,
                'message' => 'Tài khoản ngân hàng đã được liên kết API trước đó.'
            ], 409);
        }

        try {
            $client = new KPayClient($this->klbPacker);
        } catch (Exception $e) {
            return $this->handleKlbClientException($e);
        }

        try {
            $linkAccountRequest = new LinkAccountRequest($bankAccount->account_number);
            $linkAccountResponse = $client->linkAccountNo($linkAccountRequest);

            $requestId = CreateMerchantApiRequestAction::run($this->merchant->id, [
                'klb_request_id' => $linkAccountResponse->getSessionId(),
                'bank_account_id' => $bankAccount->id, 
                'company_id' => $bankAccount->company_id
            ]);

            return $this->respond([
                'code' => 200,
                'message' => 'Đã gửi OTP xác thực liên kết API.',
                'data' => [
                    'request_id' => $requestId,
                ]
            ]);
        } catch (Exception $e) {
            return $this->handleKlbClientException($e, $client);
        }
    }

    public function confirmApiConnection()
    {
        if ($this->determineIfMethodIsNotPost()) {
            return $this->respondNotFoundResponse();
        }

        $merchantApiRequest = $this->findMerchantApiRequest();

        if (!$merchantApiRequest) {
            return $this->respondInvalidRequestIdResponse();
        }

        $data = (array) $this->request->getJSON();

        $rules = [
            'otp' => [
                'label' => 'OTP',
                'rules' => ['required', 'regex_match[/^[0-9]{6}$/]']
            ]
        ];

        if (! $this->validateData($data, $rules)) {
            return $this->respondBadRequestResponse($this->validator->getErrors());
        }

        $merchantApiRequestPayload = json_decode($merchantApiRequest->payload, true);

        $bankAccountId = $merchantApiRequestPayload['bank_account_id'] ?? null;

        if (!$bankAccountId || !is_numeric($bankAccountId)) {
            return $this->respondNotFoundResponse();
        }

        $bankAccount = BankAccountBuilder::make()
            ->select(BankAccountBuilder::DETAIL_SELECT)
            ->whereMerchantId($this->merchant->id)
            ->whereId($bankAccountId)
            ->whereBankId(static::BANK_ID)->first();

        if (!$bankAccount) {
            return $this->respondNotFoundResponse(static::BANK_ACCOUNT_ID_NOT_EXIST_MESSAGE);
        }

        if ($bankAccount->bank_api_connected) {
            return $this->respond([
                'code' => 409,
                'message' => 'Tài khoản ngân hàng đã được liên kết API trước đó.'
            ], 409);
        }

        try {
            $client = new KPayClient($this->klbPacker);
        } catch (Exception $e) {
            return $this->handleKlbClientException($e);
        }

        try {
            $request = new VerifyLinkAccountRequest(
                $merchantApiRequestPayload['klb_request_id'], 
                $bankAccount->account_number, 
                trim($data['otp'])
            );

            $response = $client->verifyLinkAccountNo($request);

            if ($response->isSuccess()) {
                ActiveBankAccountApiConnectionAction::run($merchantApiRequestPayload['bank_account_id']);
                SetCompanyConfigurationAction::run($merchantApiRequestPayload['company_id'], 'BankSubAccount', true);
                DeleteMerchantApiRequestAction::run($merchantApiRequest->request_id);

                return $this->respond([
                    'code' => 200,
                    'message' => 'Đã liên kết API tài khoản ngân hàng KienLongBank thành công.',
                ]);
            }

            return $this->respondBadRequestResponse(['otp' => 'OTP không chính xác hoặc đã hết hiệu lực.'], 4001);
        } catch (Exception $e) {
            return $this->respondBadRequestResponse(['otp' => 'OTP không chính xác hoặc đã hết hiệu lực.'], 4001);
        }
    }

    public function forceDelete($bankAccountId = null)
    {
        if ($this->determineIfMethodIsNotPost()) {
            return $this->respondNotFoundResponse();
        }

        $bankAccountId = trim(xss_clean($bankAccountId));

        if (!$bankAccountId || !is_numeric($bankAccountId)) {
            return $this->respondNotFoundResponse();
        }

        $bankAccount = BankAccountBuilder::make()
            ->select(['*'])
            ->whereMerchantId($this->merchant->id)
            ->whereId($bankAccountId)
            ->whereBankId(static::BANK_ID)->first();

        if (!$bankAccount) {
            return $this->respondNotFoundResponse(static::BANK_ACCOUNT_ID_NOT_EXIST_MESSAGE);
        }

        if ($bankAccount->bank_api_connected) {
            return $this->respond([
                'code' => 400,
                'message' => 'Tài khoản ngân hàng này đang liên kết API, hiện KienLongBank chưa hỗ trợ hủy liên kết tài khoản.'
            ], 400);
        }

        $company = CompanyBuilder::make()
            ->select(CompanyBuilder::DETAIL_SELECT)
            ->whereMerchantId($this->merchant->id)
            ->whereId($bankAccount->company_id)->first();

        DeleteCompanyBankAccountAction::run($bankAccount, $company, $this->request->getIPAddress(), self::class);

        return $this->respond([
            'code' => 200,
            'message' => 'Đã gỡ tài khoản ngân hàng thành công.',
        ]);
    }
}
