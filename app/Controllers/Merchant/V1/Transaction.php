<?php

namespace App\Controllers\Merchant\V1;

use App\Controllers\Merchant\V1\BaseController;
use App\Builders\Transaction\TransactionBuilder;

class Transaction extends BaseController
{
    public function index()
    {
        $data = [
            'per_page' => $this->request->getGet('per_page') ?? 20,
            'company_id' => trim(xss_clean($this->request->getGet('company_id'))),
            'bank_id' => trim(xss_clean($this->request->getGet('bank_id'))),
            'bank_account_id' => trim(xss_clean($this->request->getGet('bank_account_id'))),
            'transaction_date' => trim(xss_clean($this->request->getGet('transaction_date'))),
            'start_transaction_date' => trim(xss_clean($this->request->getGet('start_transaction_date'))),
            'end_transaction_date' => trim(xss_clean($this->request->getGet('end_transaction_date'))),
            'transfer_type' => trim(xss_clean($this->request->getGet('transfer_type'))),
            'webhook_success' => !is_null($this->request->getGet('webhook_success')) ? ($this->request->getGet('webhook_success') == '0' ? 0 : 1) : null,
            'va_id' => trim(xss_clean($this->request->getGet('va_id'))),
            'q' => trim(xss_clean($this->request->getGet('q')))
        ];

        $builder = TransactionBuilder::make()
            ->select(TransactionBuilder::INDEX_SELECT)
            ->search($data['q'])
            ->whereMerchantId($this->merchant->id)
            ->whereCompanyId($data['company_id'])
            ->whereWebhookSuccess($data['webhook_success'])
            ->whereBankId($data['bank_id'])
            ->whereBankAccountId($data['bank_account_id'])
            ->whereTransactionDate($data['transaction_date'])
            ->whereStartTransactionDate($data['start_transaction_date'])
            ->whereEndTransactionDate($data['end_transaction_date'])
            ->whereTransferType($data['transfer_type'])
            ->whereVaId($data['va_id'])
            ->paginate($data['per_page']);

        return $this->respond([
            'data' => $builder->data(),
            'meta' => $builder->meta(),
        ]);
    }

    public function details($transactionId = null)
    {
        $transactionId = trim(xss_clean($transactionId));

        if (!$transactionId) {
            return $this->respondNotFoundResponse();
        }

        $transaction = TransactionBuilder::make()->select(TransactionBuilder::DETAIL_SELECT)->whereMerchantId($this->merchant->id)->whereId($transactionId)->first();

        if (!$transaction) {
            return $this->respondNotFoundResponse(static::TRANSACTION_ID_NOT_EXIST_MESSAGE);
        }

        return $this->respond([
            'data' => $transaction
        ], 200);
    }
}
