<?php

namespace App\Controllers\Merchant\V1;

use App\Models\BankModel;
use CodeIgniter\API\ResponseTrait;
use App\Controllers\Merchant\V1\BaseController;

class Bank extends BaseController
{
    use ResponseTrait;

    public function index()
    {
        $bankModel = model(BankModel::class);
        $banks = $bankModel->select([
            'id', 'brand_name', 'full_name', 'short_name', 'code', 'bin', 'logo_path', 'icon_path', 'active'
        ])->whereIn('code', ['OCB', 'MB', 'ACB', 'KLB', 'ICB'])->get()->getResult();

        $banks = array_map(function($bank) {
            $bank->logo_path = base_url('assets/images/banklogo/' . $bank->logo_path);
            $bank->icon_path = base_url('assets/images/banklogo/' . $bank->icon_path);

            return $bank;
        }, $banks);

        return $this->respond(['data' => $banks], 200);    
    }
}
