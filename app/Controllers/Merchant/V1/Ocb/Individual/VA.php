<?php

namespace App\Controllers\Merchant\V1\Ocb\Individual;

use Exception;
use App\Libraries\OcbClient;
use App\Models\BankAccountModel;
use App\Models\BankSubAccountModel;
use App\Builders\Company\CompanyBuilder;
use App\Models\OcbBankSubAccountMetaData;
use App\Builders\BankAccount\BankAccountBuilder;
use App\Controllers\Merchant\V1\Ocb\OcbController;
use App\Actions\Company\GetCompanyConfigurationAction;
use App\Actions\Company\SetCompanyConfigurationAction;
use App\Builders\BankSubAccount\BankSubAccountBuilder;
use App\Actions\Merchant\CreateMerchantApiRequestAction;
use App\Actions\Merchant\DeleteMerchantApiRequestAction;
use App\Actions\BankAccount\ActiveBankAccountApiConnectionAction;

class VA extends OcbController
{
    public function requestCreate()
    {
        if ($this->determineIfMethodIsNotPost()) {
            return $this->respondNotFoundResponse();
        }

        $data = (array) $this->request->getJSON();

        $data['mcc'] = '0004';

        $defaultVANumberMinLength = 1;

        $rules = [
            'company_id' => [
                'label' => 'ID công ty/tổ chức',
                'rules' => ['required'],
            ],
            'bank_account_id' => [
                'label' => 'ID tài khoản ngân hàng',
                'rules' => ['required']
            ],
            'email' => [
                'label' => 'Địa chỉ e-mail',
                'rules' => ['required', 'valid_email']
            ],
            'merchant_name' => [
                'label' => 'Tên điểm bán',
                'rules' => ['required', 'min_length[1]', 'max_length[500]', 'regex_match[/^[a-zA-Z0-9\s\-]+$/]']
            ],
            'merchant_address' => [
                'label' => 'Địa chỉ điểm bán',
                'rules' => ['required', 'min_length[1]', 'max_length[1000]', 'regex_match[/^[a-zA-Z0-9\s\-]+$/]']
            ],
            'mcc' => [
                'label' => 'Mã danh mục người bán',
                'rules' => ['required', 'min_length[1]', 'max_length[20]', 'regex_match[/^[0-9]+$/]']
            ],
            'va' => [
                'label' => 'Số VA',
                'rules' => $this->ocbConfig->OcbVAOnlyNumber 
                ? ['required', 'min_length[' . $defaultVANumberMinLength .']', 'max_length[15]', 'regex_match[/^[0-9]+$/]'] 
                : ['required', 'min_length[' . $defaultVANumberMinLength .']', 'max_length[15]', 'regex_match[/^[A-Z0-9]+$/]'],
            ],
            'label' => [
                'label' => 'Tên gợi nhớ',
                'rules' => ['permit_empty', 'max_length[100]']
            ]
        ];

        if (! $this->validateData($data, $rules)) {
            return $this->respondBadRequestResponse($this->validator->getErrors());
        }

        $safeBankSubAccountData = [
            'company_id' => trim(xss_clean($data['company_id'])),
            'bank_account_id' => trim(xss_clean($data['bank_account_id'])),
            'email' => trim($data['email']),
            'merchant_name' => trim(xss_clean(remove_accents($data['merchant_name'], true))),
            'merchant_address' => trim(xss_clean(remove_accents($data['merchant_address'], true))),
            'mcc' => trim(xss_clean($data['mcc'])),
            'va' => trim(xss_clean(strtoupper($data['va']))),
            'label' => trim(xss_clean($data['label'] ?? '')),
        ];

        $company = CompanyBuilder::make()
            ->whereMerchantId($this->merchant->id)
            ->whereId($safeBankSubAccountData['company_id'])->first();

        if (!$company) {
            return $this->respondMerchantCompanyNotExistsResponse();
        }

        $bankAccount = BankAccountBuilder::make()
            ->select(BankAccountBuilder::DETAIL_SELECT)
            ->whereMerchantId($this->merchant->id)
            ->whereId($safeBankSubAccountData['bank_account_id'])
            ->whereBankId(static::BANK_ID)->first();

        if (!$bankAccount) {
            return $this->respondMerchantBankAccountNotExistsResponse();
        }

        $bankSubAccountModel = model(BankSubAccountModel::class);
        
        if ($bankSubAccountModel->where(['sub_account' => $this->ocbConfig->OcbPartnerCode . $safeBankSubAccountData['va']])->countAllResults()) {
            return $this->respondBadRequestResponse(['va' => 'Số VA đã được sử dụng'], 4001);
        }
        
        if (preg_match('/^' . $this->ocbConfig->OcbPartnerCode . '9[a-zA-Z0-9]+$/i', $this->ocbConfig->OcbPartnerCode . $safeBankSubAccountData['va'])) {
            return $this->respondBadRequestResponse(['va' => 'Số VA đã được sử dụng'], 4001);
        }

        try {
            $client = new OcbClient;
        } catch (Exception $e) {
            return $this->handleOcbClientException($e);
        }

        try {
            $response = $client->registerMerchantStep1(
                $bankAccount->identification_number,
                $bankAccount->phone_number,
                $bankAccount->account_number,
                $client->getPartnerCode(),
                $safeBankSubAccountData['email'],
                remove_accents($bankAccount->account_holder_name, true),
                remove_accents($safeBankSubAccountData['merchant_name'], true),
                $safeBankSubAccountData['mcc'],
                $safeBankSubAccountData['merchant_address'],
                $safeBankSubAccountData['va']
            );
            $responseData = json_decode($response->getBody(), true);

            // SIMULATE
            // $responseData['trace']['bankRefNo'] = uniqid();
            // unset($responseData['error']);
            // SIMULATE

            if (isset($responseData['trace']['bankRefNo']) && !isset($responseData['error'])) {
                $requestId = CreateMerchantApiRequestAction::run(
                    $this->merchant->id, 
                    array_merge(['ocb_request_id' => $responseData['trace']['bankRefNo']], $safeBankSubAccountData)
                );

                return $this->respond([
                    'code' => 200,
                    'message' => 'Đã gửi OTP xác thực tạo VA thành công',
                    'data' => ['request_id' => $requestId],
                ]);
            }

            if ($responseData['error']['code'] == '41776') {
                return $this->respondBadRequestResponse(['va' => 'Số VA đã được sử dụng'], 4001);
            }

            if ($responseData['error']['code'] == '41749') {
                return $this->respondBadRequestResponse([], 400, 'Số điện thoại không khớp hoặc đã bị thay đổi ở phía ngân hàng');
            }

            if ($responseData['error']['code'] == '41744') {
                return $this->respondBadRequestResponse([], 400, 'Số giấy tờ tùy thân không khớp hoặc đã bị thay đổi ở phía ngân hàng');
            }

            if ($responseData['error']['code'] == '400') {
                return $this->respondBadRequestResponse([], 400, 'Số điện thoại hoặc giấy tờ tùy thân không khớp hoặc đã bị thay đổi ở phía ngân hàng');
            }

            if ($responseData['error']['code'] == '504') {
                throw new Exception('Operation timed out');
            }

            throw new Exception;
        } catch (Exception $e) {
            return $this->handleOcbClientException($e);
        }
    }

    public function confirmCreate()
    {
        if ($this->determineIfMethodIsNotPost()) {
            return $this->respondNotFoundResponse();
        }

        $merchantApiRequest = $this->findMerchantApiRequest();

        if (!$merchantApiRequest) {
            return $this->respondInvalidRequestIdResponse();
        }

        $data = (array) $this->request->getJSON();

        $rules = [
            'otp' => [
                'label' => 'OTP',
                'rules' => ['required', 'regex_match[/^[0-9]{6}$/]']
            ]
        ];

        if (! $this->validateData($data, $rules)) {
            return $this->respondBadRequestResponse($this->validator->getErrors());
        }

        $merchantApiRequestPayload = json_decode($merchantApiRequest->payload, true);

        if (!$merchantApiRequestPayload['ocb_request_id']) {
            throw new Exception;
        }

        $bankSubAccountModel = model(BankSubAccountModel::class);
        $ocbBankSubAccountMetaData = model(OcbBankSubAccountMetaData::class);

        $bankAccount = BankAccountBuilder::make()
            ->select(['id', 'bank_api_connected', 'account_holder_name'])
            ->whereMerchantId($this->merchant->id)
            ->whereId($merchantApiRequestPayload['bank_account_id'])
            ->whereBankId(static::BANK_ID)
            ->first();

        try {
            $client = new OcbClient;
        } catch (Exception $e) {
            return $this->handleOcbClientException($e);
        }

        $safeBankSubAccountData = [
            'bank_account_id' => $merchantApiRequestPayload['bank_account_id'],
            'sub_account' => $merchantApiRequestPayload['va'],
            'label' => $merchantApiRequestPayload['label'],
            'acc_type' => 'Real',
            'sub_holder_name' => $bankAccount->account_holder_name,
            'merchant_id' => $this->merchant->id,
        ];

        try {
            $response = $client->registerMerchantStep2(
                $client->getPartnerCode(),
                trim($data['otp']),
                $merchantApiRequestPayload['ocb_request_id']
            );
            $responseData = json_decode($response->getBody(), true);

            // SIMULATE
            // $responseData['trace']['bankRefNo'] = uniqid();
            // unset($responseData['error']);
            // SIMULATE

            $otpVerified = isset($responseData['trace']['bankRefNo']) && !isset($responseData['error']);

            if ($otpVerified) {
                if (!$bankAccount->bank_api_connected) {
                    ActiveBankAccountApiConnectionAction::run($merchantApiRequestPayload['bank_account_id']);
                    SetCompanyConfigurationAction::run($merchantApiRequestPayload['company_id'], 'BankSubAccount', true);
                }
                
                $safeBankSubAccountData['va_order'] = GetCompanyConfigurationAction::run($merchantApiRequestPayload['company_id'], 'OCBVaOrderLastNumber', 0) + 1;
                $safeBankSubAccountData['sub_account'] = $responseData['data']['merchantInfo']['vaAccountNumber'];

                $bankSubAccountId = $bankSubAccountModel->insert($safeBankSubAccountData);

                $ocbBankSubAccountMetaData->insert([
                    'bank_account_id' => $bankAccount->id,
                    'sub_account_id' => $bankSubAccountId,
                    'email' => $merchantApiRequestPayload['email'],
                    'merchant_name' => $merchantApiRequestPayload['merchant_name'],
                    'mcc' => $merchantApiRequestPayload['mcc'],
                    'merchant_address' => $merchantApiRequestPayload['merchant_address'],
                ]);

                SetCompanyConfigurationAction::run($merchantApiRequestPayload['company_id'], 'OCBVaOrderLastNumber', $safeBankSubAccountData['va_order']);
                DeleteMerchantApiRequestAction::run($merchantApiRequest->request_id);

                return $this->respond([
                    'code' => 201,
                    'message' => 'Đã tạo VA thành công.',
                    'id' => $bankSubAccountId
                ]);
            }

            if ($responseData['error']['code'] == '41731') {
                throw new Exception;
            }

            if (in_array($responseData['error']['code'], ['41723', '41724'])) {
                return $this->respondBadRequestResponse(['otp' => 'OTP không chính xác'], 4001);
            }

            if ($responseData['error']['code'] == '41726') {
                return $this->respondBadRequestResponse(['otp' => 'OTP đã hết hiệu lực'], 4002);
            }

            if ($responseData['error']['code'] == '504') {
                throw new Exception('Operation timed out');
            }

            throw new Exception;
        } catch (Exception $e) {
            return $this->handleOcbClientException($e);
        }
    }

    public function index()
    {
        $data = [
            'per_page' => $this->request->getGet('per_page') ?? 20,
            'company_id' => $this->request->getGet('company_id'),
            'bank_account_id' => $this->request->getGet('bank_account_id'),
            'q' => trim(xss_clean($this->request->getGet('q')))
        ];

        $builder = BankSubAccountBuilder::make()
            ->select(BankSubAccountBuilder::INDEX_SELECT)
            ->search($data['q'])
            ->whereMerchantId($this->merchant->id)
            ->whereBankId(static::BANK_ID)
            ->whereCompanyId($data['company_id'])
            ->whereBankAccountId($data['bank_account_id'])
            ->paginate($data['per_page']);

        return $this->respond([
            'data' => $builder->data(),
            'meta' => $builder->meta()
        ]);
    }

    public function details($bankSubAccountId = '')
    {
        $bankSubAccount = BankSubAccountBuilder::make()
            ->select(BankSubAccountBuilder::DETAIL_SELECT)
            ->whereId($bankSubAccountId)
            ->whereBankId(static::BANK_ID)
            ->whereMerchantId($this->merchant->id)->first();

        if (! $bankSubAccount) {
            return $this->respondNotFoundResponse('ID VA không tồn tại trên hệ thống.');
        }

        $ocbBankSubAccountMetaData = model(OcbBankSubAccountMetaData::class)
            ->select(['email', 'merchant_name', 'mcc', 'merchant_address'])
            ->where(['sub_account_id' => $bankSubAccount->id])->get()->getRow();

        $bankSubAccount->meta = $ocbBankSubAccountMetaData;

        return $this->respond([
            'data' => [
                'id' => $bankSubAccount->id,
                'company_id' => $bankSubAccount->company_id,
                'bank_account_id' => $bankSubAccount->bank_account_id,
                'label' => $bankSubAccount->label,
                'active' => $bankSubAccount->active,
                'email' => $bankSubAccount->meta->email,
                'merchant_name' => $bankSubAccount->meta->merchant_name,
                'merchant_address' => $bankSubAccount->meta->merchant_address,
                'created_at' => $bankSubAccount->created_at,
                'updated_at' => $bankSubAccount->updated_at,
            ]
        ]);
    }
}
