<?php

namespace App\Controllers\Merchant\V1\Ocb\Individual;

use Exception;
use App\Libraries\OcbClient;
use App\Models\BankAccountModel;
use App\Controllers\BaseController;
use App\Models\BankSubAccountModel;
use App\Builders\Company\CompanyBuilder;
use App\Actions\Webhook\CreateWebhookAction;
use App\Builders\BankAccount\BankAccountBuilder;
use App\Controllers\Merchant\V1\Ocb\OcbController;
use App\Actions\Merchant\CreateMerchantApiRequestAction;

class BankAccount extends OcbController
{
    public function lookUpAccountHolderName()
    {
        if ($this->determineIfMethodIsNotPost()) {
            return $this->respondNotFoundResponse();
        }

        $data = [
            'account_number' => trim(xss_clean($this->request->getVar('account_number')))
        ];

        $rules = [
            'account_number' => ['label' => 'Số tài khoản', 'rules' => ['required', 'max_length[20]']]
        ];

        if (! $this->validateData($data, $rules)) {
            return $this->respondBadRequestResponse($this->validator->getErrors());
        }

        try {
            $client = new OcbClient;
        } catch (Exception $e) {
            return $this->handleOcbClientException($e);
        }

        try {
            $response = $client->retrieveOCBRecipientName($data['account_number']);
            $responseData = json_decode($response->getBody(), true);

            if (isset($responseData['trace']['bankRefNo']) && !isset($responseData['error'])) {
                return $this->respond([
                    'code' => 200,
                    'message' => 'OK',
                    'data' => [
                        'account_holder_name' => $responseData['data']['queryResult']['accountName']
                    ]
                ]);
            }
            
            if (in_array($responseData['error']['code'], ['41715', '400'])) {
                return $this->respondBadRequestResponse(['account_number' => 'Số tài khoản không tồn tại trên hệ thống ngân hàng OCB.']);
            }

            if ($responseData['error']['code'] == '504') {
                throw new Exception('Operation timed out');
            }

            throw new Exception;
        } catch (Exception $e) {
            return $this->handleOcbClientException($e);
        }
    }

    public function create()
    {
        if ($this->determineIfMethodIsNotPost()) {
            return $this->respondNotFoundResponse();
        }

        $data = (array) $this->request->getJSON();

        $rules = [
            'company_id' => [
                'label' => 'ID công ty/tổ chức',
                'rules' => ['required'],
            ],
            'account_holder_name' => [
                'label' => 'Tên chủ tài khoản',
                'rules' => ['required'],
            ],
            'account_number' => [
                'label' => 'Số tài khoản',
                'rules' => ['required', 'max_length[20]'],
            ],
            'identification_number' => [
                'label' => 'Số CCCD/CMND',
                'rules' => ['required', 'max_length[100]']
            ],
            'phone_number' => [
                'label' => 'Số điện thoại',
                'rules' => ['required', 'min_length[10]', 'max_length[20]']
            ],
            'label' => [
                'label' => 'Tên gợi nhớ',
                'rules' => ['permit_empty', 'max_length[100]']
            ]
        ];

        if (! $this->validateData($data, $rules)) {
            return $this->respondBadRequestResponse($this->validator->getErrors());
        }

        $company = CompanyBuilder::make()->whereMerchantId($this->merchant->id)->whereId($data['company_id'])->first();

        if (!$company) {
            return $this->respondMerchantCompanyNotExistsResponse();
        }

        $safeBankAccountData = [
            'company_id' => $company->id,
            'account_holder_name' => trim(xss_clean($data['account_holder_name'])),
            'account_number' => trim(xss_clean($this->request->getVar('account_number'))),
            'bank_id' => static::BANK_ID,
            'label' => trim(xss_clean($this->request->getVar('label'))),
            'active' => 1,
            'bank_api' => 1,
            'bank_api_connected' => 0,
            'identification_number' => trim(xss_clean($this->request->getVar('identification_number'))),
            'phone_number' => trim(xss_clean($this->request->getVar('phone_number'))),
            'merchant_id' => $this->merchant->id,
        ];

        $bankAccountModel = model(BankAccountModel::class);

        if ($bankAccountModel->where([
            'account_number' => $safeBankAccountData['account_number'],
            'bank_id' => static::BANK_ID
        ])->countAllResults()) {
            return $this->respondBadRequestResponse(['account_number' => 'Số tài khoản này đã tồn tại trên hệ thống SePay'], 4001);
        }

        try {
            $client = new OcbClient;
        } catch (Exception $e) {
            return $this->handleOcbClientException($e);
        }

        try {
            $response = $client->verifyMerchantInformation(
                $safeBankAccountData['identification_number'],
                $safeBankAccountData['phone_number'],
                $safeBankAccountData['account_number']
            );
            $responseData = json_decode($response->getBody(), true);

            if ($this->ocbConfig->skipVerifyMerchantInformation) {
                $responseData['trace'] = 'SKIP_VERIFY_MERCHANT_INFO_' . uniqid();
                unset($responseData['error']);
                $responseData['data']['merchantVerifyResult']['identificationNumberValidation'] = 1;
                $responseData['data']['merchantVerifyResult']['mobilePhoneValidation'] = 1;
            }            
            
            if (isset($responseData['trace']) && !isset($responseData['error'])) {
                if ($responseData['data']['merchantVerifyResult']['identificationNumberValidation'] != '1') {
                    return $this->respondBadRequestResponse(['identification_number' => 'Số CCCD/CMND không được đăng ký cho tài khoản ngân hàng'], 4002);
                }

                if ($responseData['data']['merchantVerifyResult']['mobilePhoneValidation'] != '1') {
                    return $this->respondBadRequestResponse(['identification_number' => 'Số điện thoại không được đăng ký cho tài khoản ngân hàng MB'], 4003);
                }

                $bankAccountId = $bankAccountModel->insert($safeBankAccountData);

                $bankAccount = BankAccountBuilder::make()
                    ->select(BankAccountBuilder::DETAIL_SELECT)
                    ->whereMerchantId($this->merchant->id)
                    ->whereCompanyId($company->id)
                    ->whereId($bankAccountId)->first();

                CreateWebhookAction::run($bankAccount, $company, $this->merchant);

                return $this->respond([
                    'code' => 201,
                    'message' => 'Đã thêm tài khoản ngân hàng thành công.',
                    'id' => $bankAccountId
                ], 201);
            }

            if (in_array($responseData['error']['code'], ['41766', '41748'])) {
                return $this->respondBadRequestResponse(['account_number' => 'Số tài khoản không tồn tại trên hệ thống ngân hàng OCB'], 4004);
            }

            if ($responseData['error']['code'] == '504') {
                throw new Exception('Operation timed out');
            }

            throw new Exception;
        } catch (Exception $e) {
            return $this->handleOcbClientException($e);
        }
    }

    public function edit($bankAccountId = null)
    {
        // This is temporary when OCB $skipVerifyMerchantInformation is true
        if ($this->determineIfMethodIsNotPost() || !$this->ocbConfig->skipVerifyMerchantInformation) {
            return $this->respondNotFoundResponse();
        }

        if (!$bankAccountId || !is_numeric($bankAccountId)) {
            return $this->respondNotFoundResponse();
        }

        $bankAccount = BankAccountBuilder::make()
            ->select(BankAccountBuilder::DETAIL_SELECT)
            ->whereMerchantId($this->merchant->id)
            ->whereBankId(static::BANK_ID)
            ->whereId($bankAccountId)
            ->first();

        if (!$bankAccount) {
            return $this->respondNotFoundResponse(static::BANK_ACCOUNT_ID_NOT_EXIST_MESSAGE);
        }

        $data = (array) $this->request->getJSON();

        $rules = [
            'identification_number' => [
                'label' => 'Số CCCD/CMND',
                'rules' => ['if_exist', 'required', 'max_length[100]']
            ],
            'phone_number' => [
                'label' => 'Số điện thoại',
                'rules' => ['if_exist', 'required', 'min_length[10]', 'max_length[20]']
            ],
        ];

        if (! $this->validateData($data, $rules)) {
            return $this->respondBadRequestResponse($this->validator->getErrors());
        }

        $safeBankAccountEditData = [];

        if (isset($data['identification_number'])) {
            $safeBankAccountEditData['identification_number'] = trim(xss_clean($data['identification_number']));
        }
        
        if (isset($data['phone_number'])) {
            $safeBankAccountEditData['phone_number'] = trim(xss_clean($data['phone_number']));
        }

        if (count($safeBankAccountEditData)) {
            model(BankAccountModel::class)->where([
                'id' =>  $bankAccount->id,
                'merchant_id' => $this->merchant->id,
            ])->set($safeBankAccountEditData)->update();
        }

        return $this->respond([
            'code' => 200,
            'message' => 'Đã cập nhật thông tin tài khoản ngân hàng thành công.',
        ], 200);
    }
}
