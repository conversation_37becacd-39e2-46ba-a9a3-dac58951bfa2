<?php

namespace App\Controllers\Merchant\V1\Ocb;

use CodeIgniter\Controller;
use App\Models\CompanyModel;
use Psr\Log\LoggerInterface;
use CodeIgniter\HTTP\CLIRequest;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use App\Controllers\Merchant\V1\BaseController;

abstract class OcbController extends BaseController
{
    protected $ocbConfig;

    protected const BANK_ID = 18;

    public function initController(RequestInterface $request, ResponseInterface $response, LoggerInterface $logger)
    {
        parent::initController($request, $response, $logger);

        $this->ocbConfig = config(\Config\Ocb::class);
        $this->ocbConfig->OcbVAOnlyNumber = $this->ocbConfig->OcbVAOnlyNumber ?? false; 
        $this->ocbConfig->OcbPartnerCode = $this->ocbConfig->OcbPartnerCode ?? 'SEP';
        $this->ocbConfig->skipVerifyMerchantInformation = $this->ocbConfig->skipVerifyMerchantInformation ?? false;
    }

    protected function handleOcbClientException($e) {
        if (strpos($e->getMessage(), 'Operation timed out') !== false) {
            return $this->respond([
                'message' => 'Hệ thống OCB đang bận, vui lòng thử lại sau',
                'code' => 504
            ], 504);
        }

        log_message('error', $e->getMessage() . ' ' . $e->getTraceAsString());

        return $this->respond([
            'code' => 500,
            'message' => 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ'
        ], 500);
    }
}
