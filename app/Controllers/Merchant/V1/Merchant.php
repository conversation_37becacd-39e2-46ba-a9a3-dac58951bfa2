<?php

namespace App\Controllers\Merchant\V1;

use App\Builders\Company\CounterBuilder;
use App\Controllers\Merchant\V1\BaseController;

class Merchant extends BaseController
{
    public function counter()
    {
        $data = [
            'date' => trim(xss_clean($this->request->getGet('date'))),
        ];

        $builder = CounterBuilder::make()
            ->select(CounterBuilder::INDEX_SELECT)
            ->whereMerchantId($this->merchant->id)
            ->whereDate($data['date'])
            ->get();

        $data = $builder->data();

        return $this->respond([
            'data' => [
                'dates' => $data,
                'total' => [
                    'transaction' => array_sum(array_column($data, 'transaction')),
                    'transaction_in' => array_sum(array_column($data, 'transaction_in')),
                    'transaction_out' => array_sum(array_column($data, 'transaction_out')),
                ],
            ]
        ]);
    }
}
