<?php

namespace App\Controllers\Merchant\V1\Vietinbank\Individual;

use Exception;
use App\Models\SimModel;
use App\Libraries\MbbClient;
use App\Models\BankAccountModel;
use App\Controllers\BaseController;
use App\Libraries\VietinbankClient;
use App\Builders\Company\CompanyBuilder;
use App\Actions\Webhook\CreateWebhookAction;
use App\Builders\BankAccount\BankAccountBuilder;
use App\Actions\Company\SetCompanyConfigurationAction;
use App\Actions\Merchant\CreateMerchantApiRequestAction;
use App\Actions\Merchant\DeleteMerchantApiRequestAction;
use App\Actions\BankAccount\DeleteCompanyBankAccountAction;
use App\Controllers\Merchant\V1\Vietinbank\VietinbankController;
use App\Actions\BankAccount\ActiveBankAccountApiConnectionAction;

class BankAccount extends VietinbankController
{
    public function create()
    {
        return $this->respondNotFoundResponse();

        if ($this->determineIfMethodIsNotPost()) {
            return $this->respondNotFoundResponse();
        }

        $data = (array) $this->request->getJSON();

        $rules = [
            'company_id' => [
                'label' => 'ID công ty/tổ chức',
                'rules' => ['required'],
            ],
            'account_holder_name' => [
                'label' => 'Tên chủ tài khoản',
                'rules' => ['required'],
            ],
            'account_number' => [
                'label' => 'Số tài khoản',
                'rules' => ['required', 'max_length[20]'],
            ],
            'identification_number' => [
                'label' => 'Số CCCD/CMND',
                'rules' => ['required', 'max_length[100]']
            ],
            'phone_number' => [
                'label' => 'Số điện thoại',
                'rules' => ['required', 'min_length[10]', 'max_length[20]']
            ],
            'label' => [
                'label' => 'Tên gợi nhớ',
                'rules' => ['permit_empty', 'max_length[100]']
            ]
        ];

        if (! $this->validateData($data, $rules)) {
            return $this->respondBadRequestResponse($this->validator->getErrors());
        }

        $company = CompanyBuilder::make()->whereMerchantId($this->merchant->id)->whereId($data['company_id'])->first();

        if (!$company) {
            return $this->respondBadRequestResponse(['company_id' => static::COMPANY_ID_NOT_EXIST_MESSAGE]);
        }

        $safeBankAccountData = [
            'company_id' => $company->id,
            'account_holder_name' => trim(xss_clean($data['account_holder_name'])),
            'account_number' => trim(xss_clean($this->request->getVar('account_number'))),
            'bank_id' => static::BANK_ID,
            'label' => trim(xss_clean($this->request->getVar('label'))),
            'active' => 1,
            'bank_api' => 1,
            'bank_api_connected' => 0,
            'identification_number' => trim(xss_clean($this->request->getVar('identification_number'))),
            'phone_number' => trim(xss_clean($this->request->getVar('phone_number'))),
            'merchant_id' => $this->merchant->id,
        ];

        if (BankAccountBuilder::make()->whereBankId(static::BANK_ID)->whereAccountNumber($safeBankAccountData['account_number'])->count()) {
            return $this->respondBadRequestResponse(['account_number' => 'Số tài khoản này đã tồn tại trên hệ thống SePay.'], 4001);
        }

        try {
            $client = new VietinbankClient;
        } catch (Exception $e) {
            return $this->handleVietinbankClientException($e);
        }

        try {
            try {
                $response = $client->alertRegisterOTP(
                    '2', // personal type
                    $safeBankAccountData['account_holder_name'],
                    $safeBankAccountData['account_number'],
                    $safeBankAccountData['identification_number'],
                    $safeBankAccountData['phone_number'],
                );

                $responseData = json_decode($response->getBody());
                $errorCode = $responseData->status->code;
            } catch (Exception $e) {
                if ($e->getCode() != '13') throw $e;

                $responseData = null;
                $errorCode = '13';
            }

            if (in_array($errorCode, ['00', '13'])) {
                $bankAccountModel = model(BankAccountModel::class);
                $bankAccountId = $bankAccountModel->insert($safeBankAccountData);

                $bankAccount = BankAccountBuilder::make()
                    ->select(BankAccountBuilder::DETAIL_SELECT)
                    ->whereMerchantId($this->merchant->id)
                    ->whereCompanyId($company->id)
                    ->whereId($bankAccountId)->first();

                CreateWebhookAction::run($bankAccount, $company, $this->merchant);

                if ($errorCode == '00') {
                    $requestId = CreateMerchantApiRequestAction::run($this->merchant->id, [
                        'vietinbank_request_id' => $responseData->requestId,
                        'bank_account_id' => $bankAccountId,
                        'company_id' => $safeBankAccountData['company_id']
                    ]);
    
                    return $this->respond([
                        'code' => 2011,
                        'message' => 'Đã thêm tài khoản ngân hàng và gửi OTP xác thực liên kết API.',
                        'id' => $bankAccountId,
                        'data' => [
                            'request_id' => $requestId,
                        ]
                    ], 201);
                }

                // Vietinbank bank account has been connected to API yet.
                ActiveBankAccountApiConnectionAction::run($merchantApiRequestPayload['bank_account_id']);
                SetCompanyConfigurationAction::run($merchantApiRequestPayload['company_id'], 'BankSubAccount', true);

                return $this->respond([
                    'code' => 2012,
                    'message' => 'Đã liên kết API tài khoản ngân hàng thành công.',
                    'id' => $bankAccountId,
                ], 201);
            }
        } catch (Exception $e) {
            $errorCode = $e->getCode();

            if ($errorCode == '2')
                return $this->respondBadRequestResponse(['account_number' => 'Số tài khoản không tồn tại trên hệ thống ngân hàng.'], 4004);

            if ($errorCode == '4')
                return $this->respondBadRequestResponse(['account_holder_name' => 'Tên chủ tài khoản không khớp thông tin với tài khoản ngân hàng.'], 4005);

            if ($errorCode == '75')
                return $this->respondBadRequestResponse(['identification_number' =>  'Số CCCD/CMND không được đăng ký cho tài khoản ngân hàng.'], 4003);

            if ($errorCode == '76')
                return $this->respondBadRequestResponse(['phone_number' => 'Số điện thoại không được đăng ký cho tài khoản ngân hàng.'], 4006);

            return $this->handleVietinbankClientException($e, $client);
        }
    }

    public function requestApiConnection($bankAccountId = null)
    {
        return $this->respondNotFoundResponse();

        if ($this->determineIfMethodIsNotPost()) {
            return $this->respondNotFoundResponse();
        }

        $bankAccountId = trim(xss_clean($bankAccountId));

        if (!$bankAccountId || !is_numeric($bankAccountId)) {
            return $this->respondNotFoundResponse();
        }

        $bankAccount = BankAccountBuilder::make()
            ->select(BankAccountBuilder::DETAIL_SELECT)
            ->whereMerchantId($this->merchant->id)
            ->whereId($bankAccountId)
            ->whereBankId(static::BANK_ID)->first();

        if (!$bankAccount) {
            return $this->respondNotFoundResponse(static::BANK_ACCOUNT_ID_NOT_EXIST_MESSAGE);
        }

        if ($bankAccount->bank_api_connected) {
            return $this->respond([
                'code' => 409,
                'message' => 'Tài khoản ngân hàng đã được liên kết API trước đó.'
            ], 409);
        }

        try {
            $client = new VietinbankClient;
        } catch (Exception $e) {
            return $this->handleVietinbankClientException($e);
        }

        try {
            $response = $client->alertRegisterOTP(
                '2', // personal type
                $bankAccount->account_holder_name,
                $bankAccount->account_number,
                $bankAccount->identification_number,
                $bankAccount->phone_number,
            );

            $responseData = json_decode($response->getBody());
            $errorCode = $responseData->status->code;
        
            $requestId = CreateMerchantApiRequestAction::run($this->merchant->id, [
                'vietinbank_request_id' => $responseData->requestId,
                'bank_account_id' => $bankAccount->id, 
                'company_id' => $bankAccount->id
            ]);

            return $this->respond([
                'code' => 200,
                'message' => 'Đã gửi OTP xác thực liên kết API.',
                'data' => [
                    'request_id' => $requestId,
                ]
            ]);
        } catch (Exception $e) {
            $errorCode = $e->getCode();

            if ($errorCode == '2')
                return $this->respondBadRequestResponse(['account_number' => 'Số tài khoản không tồn tại trên hệ thống ngân hàng.'], 4004);

            if ($errorCode == '4')
                return $this->respondBadRequestResponse(['account_holder_name' => 'Tên chủ tài khoản không khớp thông tin với tài khoản ngân hàng.'], 4005);

            if ($errorCode == '75')
                return $this->respondBadRequestResponse(['identification_number' =>  'Số CCCD/CMND không được đăng ký cho tài khoản ngân hàng.'], 4003);

            if ($errorCode == '76')
                return $this->respondBadRequestResponse(['phone_number' => 'Số điện thoại không được đăng ký cho tài khoản ngân hàng.'], 4006);

            if ($errorCode == '13')
                return $this->respond([
                    'code' => 409,
                    'message' => 'Tài khoản ngân hàng đã được liên kết API trước đó.'
                ], 409);

            return $this->handleVietinbankClientException($e, $client);
        }
    }

    public function confirmApiConnection()
    {
        return $this->respondNotFoundResponse();

        if ($this->determineIfMethodIsNotPost()) {
            return $this->respondNotFoundResponse();
        }

        $merchantApiRequest = $this->findMerchantApiRequest();

        if (!$merchantApiRequest) {
            return $this->respondInvalidRequestIdResponse();
        }

        $data = (array) $this->request->getJSON();

        $rules = [
            'otp' => [
                'label' => 'OTP',
                'rules' => ['required', 'regex_match[/^[0-9]{6}$/]']
            ]
        ];

        if (! $this->validateData($data, $rules)) {
            return $this->respondBadRequestResponse($this->validator->getErrors());
        }

        $merchantApiRequestPayload = json_decode($merchantApiRequest->payload, true);

        try {
            $client = new VietinbankClient;
        } catch (Exception $e) {
            return $this->handleVietinbankClientException($e);
        }

        try {
            $response = $client->alertVerifyOTP($merchantApiRequestPayload['vietinbank_request_id'], trim($data['otp']));

            ActiveBankAccountApiConnectionAction::run($merchantApiRequestPayload['bank_account_id']);
            SetCompanyConfigurationAction::run($merchantApiRequestPayload['company_id'], 'BankSubAccount', true);
            DeleteMerchantApiRequestAction::run($merchantApiRequest->request_id);

            return $this->respond([
                'code' => 200,
                'message' => 'Đã liên kết API tài khoản ngân hàng thành công.',
            ]);
        } catch (Exception $e) {
            $errorCode = $e->getCode();

            if (in_array($errorCode, ['3', '31', '33'])) {
                return $this->respondBadRequestResponse(['otp' => 'OTP không chính xác hoặc đã hết hiệu lực.'], 4001);
            }

            return $this->handleVietinbankClientException($e, $client);
        }
    }

    public function requestDelete($bankAccountId = null)
    {
        if ($this->determineIfMethodIsNotPost()) {
            return $this->respondNotFoundResponse();
        }

        $bankAccountId = trim(xss_clean($bankAccountId));

        if (!$bankAccountId || !is_numeric($bankAccountId)) {
            return $this->respondNotFoundResponse();
        }

        $bankAccount = BankAccountBuilder::make()
            ->select(BankAccountBuilder::DETAIL_SELECT)
            ->whereMerchantId($this->merchant->id)
            ->whereId($bankAccountId)
            ->whereBankId(static::BANK_ID)->first();

        if (!$bankAccount) {
            return $this->respondNotFoundResponse(static::BANK_ACCOUNT_ID_NOT_EXIST_MESSAGE);
        }

        if (!$bankAccount->bank_api_connected) {
            return $this->respond([
                'code' => 409,
                'message' => 'Tài khoản ngân hàng chưa được liên kết API trước đó.'
            ], 409);
        }

        try {
            $client = new VietinbankClient;
        } catch (Exception $e) {
            return $this->handleVietinbankClientException($e);
        }

        try {
            $response = $client->alertRevokeOTP(
                '2', // personal type
                $bankAccount->account_holder_name,
                $bankAccount->account_number,
                $bankAccount->identification_number,
                $bankAccount->phone_number
            );
            $responseData = json_decode($response->getBody());

            $requestId = CreateMerchantApiRequestAction::run($this->merchant->id, [
                'vietinbank_request_id' => $responseData->requestId,
                'bank_account_id' => $bankAccount->id, 
                'company_id' => $bankAccount->company_id
            ]);

            return $this->respond([
                'code' => 200,
                'message' => 'Đã gửi OTP hủy liên kết API.',
                'data' => [
                    'request_id' => $requestId
                ]
            ]);
        } catch (Exception $e) {
            if ($e->getMessage() == 'Tai khoan chua duoc dang ky') {
                $bankAccountModel = model(BankAccountModel::class);
                $bankAccountModel->where('id', $bankAccount->id)->set(['bank_api_connected' => 0])->update();

                return $this->respond([
                    'code' => 409,
                    'message' => 'Tài khoản ngân hàng chưa được liên kết API trước đó.'
                ], 409);
            }
            
            return $this->handleVietinbankClientException($e, $client);
        }
    }

    public function confirmDelete()
    {
        if ($this->determineIfMethodIsNotPost()) {
            return $this->respondNotFoundResponse();
        }

        $merchantApiRequest = $this->findMerchantApiRequest();

        if (!$merchantApiRequest) {
            return $this->respondInvalidRequestIdResponse();
        }

        $data = (array) $this->request->getJSON();

        $rules = [
            'otp' => [
                'label' => 'OTP',
                'rules' => ['required', 'regex_match[/^[0-9]{6}$/]']
            ]
        ];

        if (! $this->validateData($data, $rules)) {
            return $this->respondBadRequestResponse($this->validator->getErrors());
        }

        $merchantApiRequestPayload = json_decode($merchantApiRequest->payload, true);

        $company = CompanyBuilder::make()
            ->select(CompanyBuilder::DETAIL_SELECT)
            ->whereMerchantId($this->merchant->id)
            ->whereId($merchantApiRequestPayload['company_id'])->first();

        $bankAccount = BankAccountBuilder::make()
            ->select(['*'])
            ->whereMerchantId($this->merchant->id)
            ->whereId($merchantApiRequestPayload['bank_account_id'])
            ->whereBankId(static::BANK_ID)->first();

        try {
            $client = new VietinbankClient;
        } catch (Exception $e) {
            return $this->handleVietinbankClientException($e);
        }

        try {
            $response = $client->alertVerifyOTP($merchantApiRequestPayload['vietinbank_request_id'], trim($data['otp']));

            DeleteCompanyBankAccountAction::run($bankAccount, $company, $this->request->getIPAddress(), self::class);
            DeleteMerchantApiRequestAction::run($merchantApiRequest->request_id);

            return $this->respond([
                'code' => 200,
                'message' => 'Đã hủy liên kết API thành công.',
            ]);
        } catch (Exception $e) {
            $errorCode = $e->getCode();
            
            if (in_array($errorCode, ['3', '31', '33'])) {
                return $this->respondBadRequestResponse(['otp' => 'OTP không chính xác hoặc đã hết hạn.'], 4001);
            }

            return $this->handleVietinbankClientException($e, $client);
        }
    }
    
    public function forceDelete($bankAccountId = null)
    {
        if ($this->determineIfMethodIsNotPost()) {
            return $this->respondNotFoundResponse();
        }

        $bankAccountId = trim(xss_clean($bankAccountId));

        if (!$bankAccountId || !is_numeric($bankAccountId)) {
            return $this->respondNotFoundResponse();
        }

        $bankAccount = BankAccountBuilder::make()
            ->select(['*'])
            ->whereMerchantId($this->merchant->id)
            ->whereId($bankAccountId)
            ->whereBankId(static::BANK_ID)->first();

        if (!$bankAccount) {
            return $this->respondNotFoundResponse(static::BANK_ACCOUNT_ID_NOT_EXIST_MESSAGE);
        }

        if ($bankAccount->bank_api_connected) {
            return $this->respond([
                'code' => 400,
                'message' => 'Tài khoản ngân hàng này đang liên kết API, vui lòng hủy liên kết theo luồng OTP.'
            ], 400);
        }

        $company = CompanyBuilder::make()
            ->select(CompanyBuilder::DETAIL_SELECT)
            ->whereMerchantId($this->merchant->id)
            ->whereId($bankAccount->company_id)->first();

        DeleteCompanyBankAccountAction::run($bankAccount, $company, $this->request->getIPAddress(), self::class);

        return $this->respond([
            'code' => 200,
            'message' => 'Đã gỡ tài khoản ngân hàng thành công.',
        ]);
    }
}
