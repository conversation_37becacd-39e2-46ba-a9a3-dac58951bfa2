<?php

namespace App\Controllers\Merchant\V1\Vietinbank;

use CodeIgniter\Controller;
use App\Models\CompanyModel;
use Psr\Log\LoggerInterface;
use CodeIgniter\HTTP\CLIRequest;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use App\Controllers\Merchant\V1\BaseController;

class VietinbankController extends BaseController
{
    protected $vietinbankConfig;

    protected const BANK_ID = 6;

    public function initController(RequestInterface $request, ResponseInterface $response, LoggerInterface $logger)
    {
        parent::initController($request, $response, $logger);

        $this->vietinbankConfig = config(\Config\Vietinbank::class);
    }

    protected function handleVietinbankClientException($e, $client = null)
    {
        log_message('error', 'VietinBank Client Error (Controller): ' . $e->getMessage() . ' - ' . $e->getTraceAsString());

        if ($client) $client->forceDebug();

        if (strpos($e->getMessage(), 'Operation timed out') !== false)
            return $this->respond([
                'code' => 504,
                'message' => 'Hệ thống ngân hàng VietinBank đang bận, vui lòng thử lại sau.'
            ], 504);

        if ($e instanceof DisableBankClientException)
            return $this->respond([
                'code' => 503, 
                'message' => 'Hệ thống ngân hàng VietinBank đang bảo trì, vui lòng liên hệ SePay để biết thêm thông tin chi tiết.'
            ], 503);    

        return $this->respond([
            'code' => 500, 
            'message' => 'Đã có lỗi xảy ra, vui lòng thử lại sau hoặc liên hệ SePay để được hỗ trợ.'
        ], 500);
    }
}
