<?php

namespace App\Controllers;

use Config\Mobile as MobileConfig;
use CodeIgniter\API\ResponseTrait;
use App\Controllers\BaseController;

class Mobilesharing extends BaseController
{
    use ResponseTrait;
    
    protected MobileConfig $mobileConfig;
    
    public function __construct()
    {
        $this->mobileConfig = config(MobileConfig::class);
        $this->mobileConfig->visibility = $this->mobileConfig->visibility ?? false;
        $this->mobileConfig->downloadOneLinkUrl = $this->mobileConfig->downloadOneLinkUrl ?? '';
        $this->mobileConfig->downloadOneLinkQrUrl = $this->mobileConfig->downloadOneLinkQrUrl ?? '';
    }

    public function index()
    {
        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])) {
            show_404();
        }

        $data = [
            'page_title' => 'Mobile app',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'mobile_config' => $this->mobileConfig
        ];

        echo theme_view('templates/autopay/header', $data);
        echo theme_view('mobile/index', $data);
        echo theme_view('templates/autopay/footer', $data);
    }
}
