<?php

namespace App\Controllers;
use App\Models\BankAccountModel;
use App\Models\WebhooksModel;
use App\Models\WebhooksLogModel;
use App\Models\TransactionsModel;
use App\Models\UserModel;
use App\Models\WebhooksBankSubAccountModel;
use CodeIgniter\Controller;

class Webhooks extends BaseController
{
    public function index()
    { 
        $data = [
            'page_title' => 'Webhooks',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        if(!has_permission('Webhooks', 'can_view_all'))
            show_404();

        $webhooksModel = model(WebhooksModel::class);
        $bankAccountModel = model(BankAccountModel::class);

        $data['bankAccounts'] = $bankAccountModel->select("tb_autopay_bank_account.id, tb_autopay_bank.brand_name, tb_autopay_bank_account.account_holder_name, tb_autopay_bank_account.account_number")->join("tb_autopay_bank","tb_autopay_bank.id=tb_autopay_bank_account.bank_id","left")->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id']])->get()->getResult();
        
      
        echo view('templates/autopay/header',$data);
        echo view('webhooks/index',$data);
        echo view('templates/autopay/footer',$data);

    }

    public function ajax_webhooks_list() {
     
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!has_permission('Webhooks', 'can_view_all'))
            show_404();
 
        $webhooksModel = slavable_model(WebhooksModel::class, 'Webhooks');
        $webhooksLogModel = slavable_model(WebhooksLogModel::class, 'Webhooks');
        $webhooks = $webhooksModel->getDatatables($this->user_session['company_id']);
         
        $data = array();

        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');

        foreach ($webhooks as $webhook) {
            $total_success = $webhooksLogModel->where(['webhook_id' => $webhook->id, 'response_status_code<=' => 201,'webhook_type'=>'Webhooks', 'response_status_code>=' => 200])->countAllResults();
            $total_failed = $webhooksLogModel->where(['webhook_id' => $webhook->id, 'response_status_code>' => 201,'webhook_type'=>'Webhooks'])->countAllResults();

            $today_success = $webhooksLogModel->where(['webhook_id' => $webhook->id, 'response_status_code<=' => 201,'webhook_type'=>'Webhooks', 'response_status_code>=' => 200,'created_at>=' => date('Y-m-d 00:00:00')])->countAllResults();
            $today_failed = $webhooksLogModel->where(['webhook_id' => $webhook->id, 'response_status_code>' => 201,'webhook_type'=>'Webhooks', 'created_at >=' => date('Y-m-d 00:00:00')])->countAllResults();


            $no++;
            $row = array();
            
            $row[] = $no;
            $row[] = esc($webhook->id);
            if($webhook->is_verify_payment == 1)
                $row[] = "<b>" . esc($webhook->name) . "</b><div class='mt-1'><span class='badge bg-primary'><i class='bi bi-patch-check-fill'></i> Loại: Xác thực thanh toán</span></div>";
            else
                $row[] = "<b>" . esc($webhook->name) . "</b>";
            if($webhook->event_type == "In_only")
                $row[] = "<span class='text-success'>Có tiền vào</span>";
            else if($webhook->event_type == "Out_only")
                $row[] = "<span class='text-danger'>Có tiền ra</span>";
            else if($webhook->event_type == "All")
                $row[] = "<span class='text-primary'>Tiền vào và Tiền ra</span>";
            else
                $row[] = "";
            $row[] = esc($webhook->brand_name) . ' <br> ' . esc($webhook->account_number) . ' <br>' . esc($webhook->account_holder_name);
            
            $row[] = "Hôm nay: <a href='" . base_url('webhookslog'). "' class='text-success'>". number_format($today_success) . "</a> / <a href='" . base_url('webhookslog'). "' class='text-danger'>" . $today_failed ."</a><br>Tổng: <a href='" . base_url('webhookslog'). "' class='text-success'>". number_format($total_success) . "</a> / <a href='" . base_url('webhookslog'). "' class='text-danger'>" . $total_failed ."</a>";
            
            $row[] = esc($webhook->webhook_url);

            $row[] = esc($webhook->authen_type);
            if($webhook->active == 1)
                $row[] = "<span class='text-success'>Kích hoạt</span>";
            else
                $row[] = "<span class='text-secondary'>Tạm ngưng</span>";
           // $row[] = esc($webhook->created_at);
            $row[] = "<a href='javascript:;' onclick='edit_webhook(" . $webhook->id. ")' class='btn btn-sm btn-outline-warning ms-2 me-2 mt-2'><i class='bi bi-pencil'></i> Sửa</a> <a href='javascript:;' onclick='delete_webhook(" . $webhook->id. ")' class='btn btn-sm btn-outline-danger ms-2 mt-2'><i class='bi bi-trash3'></i> Xóa</a>";
       
            $data[] = $row;
        }
 
        $output = array(
            "draw" => $draw,
            "recordsTotal" => $webhooksModel->countAll($this->user_session['company_id']),
            "recordsFiltered" => $webhooksModel->countFiltered($this->user_session['company_id']),
            "data" => $data,
        );
        return $this->response->setJSON($output);
    
    
    }

    public function ajax_webhook_add() {

        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!has_permission('Webhooks', 'can_add'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền thêm webhooks"));

 
        $validation =  \Config\Services::validation();

        helper('text');

        $webhooksModel = model(Webhooksmodel::class);

        $rules = [
            'bank_account_id' =>['label' => 'Tài khoản ngân hàng', 'rules' => 'required|integer|is_natural'],
            'only_va' => ['label' => 'Chỉ nhận giao dịch từ tài khoản ảo', 'rules' => 'if_exist|in_list[1]'],
            'bank_sub_account_ids' => [
                'label' => 'Tài khoản ảo',
                'rules' => 'required_with[only_va]|valid_bank_sub_account_ids[]',
                'errors' => [
                    'required_with' => 'Hãy chọn tài khoản ảo nếu bạn chọn "Chỉ nhận giao dịch từ tài khoản ảo (VA) của tài khoản ngân hàng này"',
                ]
            ],
            'event_type' => ['label' => 'Bắn WebHooks khi', 'rules' => "required|in_list[All,In_only,Out_only]"],
            'authen_type' => ['label' => 'Kiểu chứng thực', 'rules' => "required|in_list[No_Authen,OAuth2.0,Secret_Key,Api_Key]"],
            'webhook_url' => ['label' => 'Gọi đến URL', 'rules' =>"required|valid_url_strict|valid_webhooks_url"],
            'name' => ['label' => 'Đặt tên', 'rules' => "required|max_length[250]"],
            'is_verify_payment' => "required|in_list[0,1]",
            'skip_if_no_code' => "required|in_list[0,1]",
            'active' => "required|in_list[0,1]",
            'retry_conditions' => 'if_exist|in_list[' . implode(',', array_keys($webhooksModel->defaultRetryConditions)) .']'
        ];

        $authen_type = $this->request->getPost('authen_type');

        if($authen_type == "OAuth2.0") {
            $rules['oauth2_access_token_url'] = ['label' => 'OAuth 2.0 Access Token URL', 'rules' => 'required|max_length[1000]'];
            $rules['oauth2_client_id'] = ['label' => 'OAuth 2.0 Client Id', 'rules' => 'required|max_length[200]'];
            $rules['oauth2_client_secret'] = ['label' => 'OAuth 2.0 Client Secret', 'rules' => 'required|max_length[200]'];
        } else if($authen_type == "Secret_Key") {
            $rules['secret_key'] = ['label' => 'Secret Key', 'rules' => 'required|max_length[1000]'];
        } else if($authen_type == "Api_Key") {
            //$rules['api_id'] = ['label' => 'API Id', 'rules' => 'required|max_length[1000]'];
            $rules['api_key'] = ['label' => 'API Key', 'rules' => 'required|max_length[1000]'];
        } else if($authen_type == "No_Authen") {
            $rules['request_content_type'] = ['label' => 'Request Content type', 'rules' => 'required|in_list[Json,multipart_form-data]'];    
        }
  
        
        if(! $this->validate($rules)) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        } 
        
        $bank_account_id = $this->request->getPost('bank_account_id');

        $bankAccountModel = model(BankAccountmodel::class);
        $bank_account = $bankAccountModel->where(['company_id' => $this->user_session['company_id'], 'id'=>$bank_account_id])->countAllResults();
        if($bank_account != 1)
            return $this->response->setJSON(array('status'=>FALSE,'message'=>'Không tìm thấy tài khoản ngân hàng mà bạn chọn'));
        
        $retryConditions = $webhooksModel->defaultRetryConditions;
        $incomingRequestRetryConditions = $this->request->getVar('retry_conditions') ?? [];

        foreach ($retryConditions as $condition => $value) {
            if (in_array($condition, $incomingRequestRetryConditions)) {
                $retryConditions[$condition] = 1;
            } else {
                $retryConditions[$condition] = 0;
            }
        }

        $data = array(
            'company_id' => $this->user_session['company_id'],
            'bank_account_id' => $this->request->getPost('bank_account_id'),
            'event_type' => $this->request->getVar('event_type'),
            'authen_type' => $this->request->getVar('authen_type'),
            'webhook_url' => trim($this->request->getVar('webhook_url')),
            'name' => trim(xss_clean($this->request->getVar('name'))),
            //'oauth2_client_id' => trim($this->request->getVar('oauth2_client_id')),
            //'oauth2_client_secret' => trim($this->request->getVar('oauth2_client_secret')),
            //'oauth2_access_token_url' => trim($this->request->getVar('oauth2_access_token_url')),
            'is_verify_payment' => $this->request->getVar('is_verify_payment'),
            'skip_if_no_code' => $this->request->getVar('skip_if_no_code'),
            //'request_content_type' => $this->request->getVar('request_content_type'),
            'active' => $this->request->getVar('active'),
            'retry_conditions' => json_encode($retryConditions),
            'only_va' => $this->request->getVar('only_va') ?? 0,
        );

        if($authen_type == 'OAuth2.0') {
            $data['oauth2_client_id'] = trim($this->request->getVar('oauth2_client_id'));
            $data['oauth2_client_secret'] = trim($this->request->getVar('oauth2_client_secret'));
            $data['oauth2_access_token_url'] = trim($this->request->getVar('oauth2_access_token_url'));
        } else if($authen_type == 'Api_Key') {
            $data['api_id'] = trim($this->request->getVar('api_id'));
            $data['api_key'] = trim($this->request->getVar('api_key'));
            $data['request_content_type'] = trim($this->request->getVar('request_content_type'));
        } else if($authen_type == 'Secret_Key') {
            $data['secret_key'] = trim($this->request->getVar('secret_key'));
            $data['request_content_type'] = trim($this->request->getVar('request_content_type'));
        } else if($authen_type == 'No_Authen') {
            $data['request_content_type'] = trim($this->request->getVar('request_content_type'));
        }

        $result = $webhooksModel->insert($data);

        if($result) {
            $webhooksModel->syncBankSubAccounts($result, $this->request->getPost('bank_sub_account_ids') ?? []);
            add_user_log(array('data_id'=>$result,'company_id' => $this->user_session['company_id'], 'data_type'=>'webhook_add','description'=>'Thêm WebHooks','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
            return $this->response->setJSON(array("status"=>true));
        } else {
            add_user_log(array('data_id'=>0,'company_id' => $this->user_session['company_id'], 'data_type'=>'webhook_add','description'=>'Thêm WebHooks','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Failed'));
            return $this->response->setJSON(array("status"=>false,"message"=>"Không thể thêm webhook. Vui lòng liên hệ 123HOST để được hỗ trợ."));
        }
    
    }


    public function ajax_get_webhook($id='') {
        
        if(!has_permission('Webhooks', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền sửa webhooks"));

        if(!is_numeric($id)) 
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"ID webhook không hợp lệ"));
        
        $webhooksModel = model(Webhooksmodel::class);
        $webhooksBankSubAccountModel = model(WebhooksBankSubAccountModel::class);
        
        $result = $webhooksModel->where(['id'=>$id, 'company_id' => $this->user_session['company_id']])->get()->getRow();

        if ($result) {
            $result->retry_conditions = is_null($result->retry_conditions) ? null : json_decode($result->retry_conditions);
            $result->bank_sub_accounts = $webhooksBankSubAccountModel->where('webhook_id', $id)->findColumn('bank_sub_account_id');

            return $this->response->setJSON(["status"=>TRUE, "data"=>$result]);
        }

        return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy webhook này"));
    }

    public function ajax_webhook_update() {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!has_permission('Webhooks', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền sửa webhooks"));

 
        $validation =  \Config\Services::validation();

        helper('text');
        $webhooksModel = model(Webhooksmodel::class);

        $rules = [
            'id' => 'required|integer|is_natural',
            'bank_account_id' => 'required|integer|is_natural',
            'only_va' => ['label' => 'Chỉ nhận giao dịch từ tài khoản ảo', 'rules' => 'if_exist|in_list[1]'],
            'bank_sub_account_ids' => [
                'label' => 'Tài khoản ảo',
                'rules' => 'required_with[only_va]|valid_bank_sub_account_ids[]',
                'errors' => [
                    'required_with' => 'Hãy chọn tài khoản ảo nếu bạn chọn "Chỉ nhận giao dịch từ tài khoản ảo (VA) của tài khoản ngân hàng này"',
                ]
            ],
            'event_type' => "required|in_list[All,In_only,Out_only]",
            'authen_type' => "required|in_list[No_Authen,OAuth2.0,Secret_Key,Api_Key]",
            'webhook_url' => "required|valid_url_strict|valid_webhooks_url",
            'name' => "required|max_length[250]",
            'is_verify_payment' => "required|in_list[0,1]",
            'skip_if_no_code' => "required|in_list[0,1]",
            'active' => "required|in_list[0,1]",
            'retry_conditions' => 'if_exist|in_list[' . implode(',', array_keys($webhooksModel->defaultRetryConditions)) .']'
        ];

        $authen_type = $this->request->getPost('authen_type');

        if($authen_type == "OAuth2.0") {
            $rules['oauth2_access_token_url'] = ['label' => 'OAuth 2.0 Access Token URL', 'rules' => 'required|max_length[1000]'];
            $rules['oauth2_client_id'] = ['label' => 'OAuth 2.0 Client Id', 'rules' => 'required|max_length[200]'];
            $rules['oauth2_client_secret'] = ['label' => 'OAuth 2.0 Client Secret', 'rules' => 'required|max_length[200]'];
        } else if($authen_type == "Secret_Key") {
            $rules['secret_key'] = ['label' => 'Secret Key', 'rules' => 'required|max_length[1000]'];
        } else if($authen_type == "Api_Key") {
           // $rules['api_id'] = ['label' => 'API Id', 'rules' => 'required|max_length[1000]'];
            $rules['api_key'] = ['label' => 'API Key', 'rules' => 'required|max_length[1000]'];
            $rules['request_content_type'] = ['label' => 'Request Content type', 'rules' => 'required|in_list[Json,multipart_form-data]'];    

        } else if($authen_type == "No_Authen") {
            $rules['request_content_type'] = ['label' => 'Request Content type', 'rules' => 'required|in_list[Json,multipart_form-data]'];    
        }

        if(! $this->validate($rules)) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        } 

        $bank_account_id = $this->request->getPost('bank_account_id');

        $bankAccountModel = model(BankAccountmodel::class);
        $bank_account = $bankAccountModel->where(['company_id' => $this->user_session['company_id'], 'id'=>$bank_account_id])->countAllResults();
        if($bank_account != 1)
            return $this->response->setJSON(array('status'=>FALSE,'message'=>'Không tìm thấy tài khoản ngân hàng mà bạn chọn'));
        
        $webhook_id = $this->request->getPost('id');

        $webhook_details = $webhooksModel->where(["id" =>$webhook_id,"company_id"=>$this->user_session['company_id']])->get()->getRow();

        if(!is_object($webhook_details))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>'Không tìm thấy webhook này'));

        $retryConditions = $webhooksModel->defaultRetryConditions;
        $incomingRequestRetryConditions = $this->request->getVar('retry_conditions') ?? [];

        foreach ($retryConditions as $condition => $value) {
            if (in_array($condition, $incomingRequestRetryConditions)) {
                $retryConditions[$condition] = 1;
            } else {
                $retryConditions[$condition] = 0;
            }
        }

        $data = array(
            'bank_account_id' => $this->request->getPost('bank_account_id'),
            'event_type' => $this->request->getVar('event_type'),
            'authen_type' => $this->request->getVar('authen_type'),
            'webhook_url' => trim($this->request->getVar('webhook_url')),
            //'oauth2_client_id' => trim($this->request->getVar('oauth2_client_id')),
            //'oauth2_client_secret' => trim($this->request->getVar('oauth2_client_secret')),
            //'oauth2_access_token_url' => trim($this->request->getVar('oauth2_access_token_url')),
            'name' => trim(xss_clean($this->request->getVar('name'))),
            'is_verify_payment' => $this->request->getVar('is_verify_payment'),
            'skip_if_no_code' => $this->request->getVar('skip_if_no_code'),
            'active' => $this->request->getVar('active'),
            'retry_conditions' => json_encode($retryConditions),
            'only_va' => $this->request->getVar('only_va') ?? false,
        );

        if($authen_type == 'OAuth2.0') {
            $data['oauth2_client_id'] = trim($this->request->getVar('oauth2_client_id'));
            $data['oauth2_client_secret'] = trim($this->request->getVar('oauth2_client_secret'));
            $data['oauth2_access_token_url'] = trim($this->request->getVar('oauth2_access_token_url'));
        } else if($authen_type == 'Api_Key') {
            $data['api_id'] = trim($this->request->getVar('api_id'));
            $data['api_key'] = trim($this->request->getVar('api_key'));
            $data['request_content_type'] = trim($this->request->getVar('request_content_type'));

        } else if($authen_type == 'Secret_Key') {
            $data['secret_key'] = trim($this->request->getVar('secret_key'));
            $data['request_content_type'] = trim($this->request->getVar('request_content_type'));

        } else if($authen_type == 'No_Authen') {
            $data['request_content_type'] = trim($this->request->getVar('request_content_type'));
        }
            
        $result = $webhooksModel->set($data)->where(["id" =>$webhook_id,"company_id"=>$this->user_session['company_id']])->update();

        if($result) { 
            $webhooksModel->syncBankSubAccounts($webhook_id, $this->request->getPost('bank_sub_account_ids') ?? []);
            add_user_log(array('data_id'=>$webhook_id,'company_id' => $this->user_session['company_id'], 'data_type'=>'webhook_update','description'=>'Sửa WebHooks','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
            return $this->response->setJSON(array("status"=>true));
        } else {
            add_user_log(array('data_id'=>$webhook_id,'company_id' => $this->user_session['company_id'], 'data_type'=>'webhook_update','description'=>'Sửa WebHooks','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Failed'));
            return $this->response->setJSON(array("status"=>false,"message"=>"Không thể cập nhật webhook!"));
        }
            

    
    }

    public function ajax_webhook_delete() {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!has_permission('Webhooks', 'can_delete'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền xóa webhooks"));

 
        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'id' => 'required|integer|is_natural',
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }
        $webhook_id = $this->request->getPost('id');

        $webhooksModel = model(Webhooksmodel::class);
        $webhooksBankSubAccountModel = model(WebhooksBankSubAccountModel::class);
        $webhook_details = $webhooksModel->where(['id'=>$webhook_id,'company_id'=>$this->user_session['company_id']])->get()->getRow();

        if(!is_object($webhook_details))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Không tìm thấy webhook này"));

        $webhooksModel->delete($webhook_id);
        $webhooksBankSubAccountModel->where('webhook_id', $webhook_id)->delete();

        add_user_log(array('data_id'=>$webhook_id,'company_id' => $this->user_session['company_id'], 'data_type'=>'webhook_delete','description'=>'Xóa WebHooks','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
    
        return $this->response->setJSON(array("status"=>true));
         
    
    }
 

 
    
}