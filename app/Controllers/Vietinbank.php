<?php

namespace App\Controllers;

use App\Models\OutputDeviceDecalModel;
use Exception;
use App\Models\ShopModel;
use App\Models\SimModel;
use App\Models\ProductModel;
use App\Models\SimCompanyModel;
use App\Models\BankAccountModel;
use App\Models\BankShopLinkModel;
use App\Models\TransactionsModel;
use App\Models\UserPermissionBankSubModel;
use App\Models\UserPermissionFeatureModel;
use App\Models\UserPermissionBankModel;
use App\Models\NotificationTelegramModel;
use App\Models\NotificationLarkMessengerModel;
use CodeIgniter\API\ResponseTrait;
use App\Controllers\BaseController;
use App\Libraries\VietinbankClient;
use App\Models\BankSubAccountModel;
use App\Models\CompanySubscriptionModel;
use App\Exceptions\DisableBankClientException;
use App\Models\NotificationTelegramQueueModel;
use App\Models\VietinbankEnterpriseAccountModel;
use App\Actions\BankAccount\DeleteCompanyBankAccountAction;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use Psr\Log\LoggerInterface;

class Vietinbank extends BaseController
{
    use ResponseTrait;

    protected $vietinbankConfig;

    const BANK_ID = 6;

    public function __construct()
    {
        $this->vietinbankConfig = config(\App\Config\Vietinbank::class);
        $this->vietinbankConfig->allowed_switch_api_connection = $this->vietinbankConfig->allowed_switch_api_connection ?? false;
    }

    public function initController(RequestInterface $request, ResponseInterface $response, LoggerInterface $logger)
    {
        parent::initController($request, $response, $logger);

        is_bank_supported_by_promotion(self::BANK_ID, $this->company_details->company_id);
    }

    protected function handleVietinbankClientException($e, $client = null)
    {
        log_message('error', 'Vietinbank Controller Error: ' . $e->getMessage() . ' - ' . $e->getTraceAsString());

        if ($client) $client->forceDebug();

        if (strpos($e->getMessage(), 'timed out') !== false)
            return $this->response->setJSON(['status' => false, 'message' => 'Hệ thống ngân hàng VietinBank đang bận, vui lòng thử lại sau.']);

        if ($e instanceof DisableBankClientException)
            return $this->response->setJSON(['status' => false, 'message' => 'Hệ thống ngân hàng VietinBank đang bảo trì, vui lòng liên hệ SePay để biết thêm thông tin chi tiết.']);    

        return $this->response->setJSON(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng thử lại sau hoặc liên hệ SePay để được hỗ trợ.']);
    }

    public function step1()
    {
        $data = [
            'page_title' => 'Ngân hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'allowed_switch_api_connection' => $this->vietinbankConfig->allowed_switch_api_connection
        ];

        if (!has_permission('BankAccount', 'can_add')) 
            return show_404();

        $session = service('session');
        $bankAccountId = $session->get('vietinbank_request_switch_api_connection');
        $data['bank_account_details'] = null;

        if ($this->vietinbankConfig->allowedSwitchApiConnection && $bankAccountId) {
            $data['bank_account_details'] = $this->getSwitchableApiConnectionBankAccount($bankAccountId);
        }

        $session->remove('vietinbank_request_switch_api_connection');

        echo view('templates/autopay/header', $data);
        echo view('vietinbank/step1', $data);
        echo view('templates/autopay/footer', $data);
    }

    public function ajax_step1()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!has_permission('BankAccount', 'can_add'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền thêm tài khoản ngân hàng']);

        $session = service('session');

        $data = [
            'id' => trim(xss_clean($this->request->getVar('id'))),
            'account_number' => trim(xss_clean($this->request->getVar('account_number'))),
            'account_holder_name' => trim(remove_accents($this->request->getVar('account_holder_name'), TRUE)),
            'identification_number' => trim(xss_clean($this->request->getVar('identification_number'))),
            'phone_number' => trim(xss_clean($this->request->getVar('phone_number'))),
            'label' => trim(xss_clean($this->request->getVar('label'))),
        ];

        $rules = [
            'account_number' => [
                'label' => 'số tài khoản',
                'rules' => ['required', 'max_length[100]'],
            ],
            'account_holder_name' => [
                'label' => 'tên chủ tài khoản',
                'rules' => ['required', 'max_length[100]'], 
            ],
            'identification_number' => [
                'label' => 'số CMND/CCCD',
                'rules' => ['required', 'max_length[100]']
            ],
            'phone_number' => [
                'label' => 'số điện thoại',
                'rules' => ['required', 'min_length[10]', 'max_length[20]']
            ],
            'label' => [
                'label' => 'tên gợi nhớ',
                'rules' => ['permit_empty', 'max_length[100]']
            ],
        ];

        if (! $this->validateData($data, $rules)) return $this->fail($this->validator->getErrors());

        $bankAccountModel = model(BankAccountModel::class);
        $bankAccountDetails = $data['id'] ? $this->getSwitchableApiConnectionBankAccount($data['id']) : null;

        // Skip validation account number exist when switch bank account to api conenction
        if ($this->vietinbankConfig->allowedSwitchApiConnection && $bankAccountDetails) {
            $data['account_number'] = $bankAccountDetails->account_number;
        } else {
            if ($bankAccountDetails = $bankAccountModel->where(['account_number' => $data['account_number'], 'bank_id' => Vietinbank::BANK_ID, 'company_id' => $this->user_session['company_id'], 'bank_sms' => 1])->get()->getRow()) {
                return $this->fail(['account_number' => 'Tài khoản này của bạn đang sử dụng phương thức kết nối SMS Banking', 'bank_account_id' => $bankAccountDetails->id]);
            }

            if ($bankAccountModel->where(['account_number' => $data['account_number'], 'bank_id' => Vietinbank::BANK_ID])->countAllResults()) {
                return $this->fail(['account_number' => 'Số tài khoản này đã tồn tại trên hệ thống', 'owner' => false]);
            }
        }

        try {
            $client = new VietinbankClient;
        } catch (Exception $e) {
            return $this->handleVietinbankClientException($e);
        }

        try {
            try {
                $response = $client->alertRegisterOTP(
                    '2', // personal type
                    $data['account_holder_name'],
                    $data['account_number'],
                    $data['identification_number'],
                    $data['phone_number'],
                );

                $responseData = json_decode($response->getBody());
                $errorCode = $responseData->status->code;
            } catch (Exception $e) {
                if ($e->getCode() != '13') throw $e;

                $responseData = null;
                $errorCode = '13';
            }

            if (in_array($errorCode, ['00', '13']) && !$bankAccountDetails) {
                $simModel = model(SimModel::class);
                $simCompanyModel = model(SimCompanyModel::class);

                $safeBankAccountData = array(
                    'company_id' => $this->user_session['company_id'],
                    'account_holder_name' => $data['account_holder_name'],
                    'account_number' => $data['account_number'],
                    'bank_id' => Vietinbank::BANK_ID,
                    'label' => $data['label'],
                    'active' => 1,
                    'bank_api' => 1,
                    'bank_api_connected' => 0,
                    'identification_number' => $data['identification_number'],
                    'phone_number' => $data['phone_number']
                );

                $sims = $simCompanyModel
                    ->select('tb_autopay_sim.id, tb_autopay_sim.sim_phonenumber')
                    ->join('tb_autopay_sim', 'tb_autopay_sim.id=tb_autopay_sim_company.sim_id')
                    ->where([
                        'tb_autopay_sim_company.company_id' => $this->user_session['company_id'], 
                        'tb_autopay_sim.active' => 1
                    ])
                    ->orderBy('tb_autopay_sim_company.created_at', 'ASC')
                    ->get()->getResult();

                if (count($sims) > 0) {
                    $safeBankAccountData['sim_id'] = $sims[0]->id;
                }

                $bankAccountId = $bankAccountModel->insert($safeBankAccountData);

                if ($errorCode == '13') {
                    $session->set('vietinbank_skip_step_2', true);
                    return $this->response->setJSON(['status' => true, 'id' => $bankAccountId]);
                }

                if ($bankAccountId) {
                    add_user_log(['data_id' => $bankAccountId, 'company_id' => $this->user_session['company_id'], 'data_type' => 'bank_account_add', 'description' => 'Thêm tài khoản ngân hàng VietinBank API', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);

                    $session->set('vietinbank_step_1_request_id', $responseData->requestId);
                    set_alert('success', 'Đã gửi OTP đến số điện thoại.');

                    return $this->response->setJSON(['status' => true, 'id' => $bankAccountId]);
                }

                add_user_log(['data_id' => 0, 'company_id' => $this->user_session['company_id'], 'data_type' => 'bank_account_add', 'description' => 'Thêm tài khoản ngân hàng VietinBank API', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Failed']);
                return $this->response->setJSON(['status' => false, 'message' => 'Không thể thêm tài khoản ngân hàng. Vui lòng liên hệ SePay để được hỗ trợ.']);
            }

            // Switch VietinBank bank account to API connection
            if (in_array($errorCode, ['00', '13']) && $bankAccountDetails) {
                $bankAccountUpdated = $bankAccountModel->set([
                    'account_holder_name' => $data['account_holder_name'],
                    'identification_number' => $data['identification_number'],
                    'phone_number' => $data['phone_number'],
                ])->where(['id' => $bankAccountDetails->id])->update();

                if ($errorCode == '13') {
                    $session->set('vietinbank_skip_step_2', true);
                    return $this->response->setJSON(['status' => true, 'id' => $bankAccountDetails->id]);
                }

                if ($bankAccountUpdated) {
                    $session->set('vietinbank_step_1_request_id', $responseData->requestId);
                    set_alert('success', 'Đã gửi OTP đến số điện thoại.');

                    return $this->response->setJSON(['status' => true, 'id' => $bankAccountDetails->id]);
                }

                return $this->response->setJSON(['status' => false, 'message' => 'Không thể chuyển sang phương thức kết nối API, vui lòng liên hệ SePay để được hỗ trợ.']);
            }
        } catch (Exception $e) {
            $errorCode = $e->getCode();

             if ($errorCode == '2')
                return $this->fail(['account_number' => 'Số tài khoản không tồn tại hoặc đã bị khóa trên hệ thống ngân hàng VietinBank.']);

            if ($errorCode == '4')
                return $this->fail(['account_holder_name' =>  'Thông tin tên chủ tài khoản không trùng khớp.']);
            
            if ($errorCode == '75')
                return $this->fail(['identification_number' =>  'Số CCCD/CMND không được đăng ký cho tài khoản trên.']);

            if ($errorCode == '76')
                return $this->fail(['phone_number' => 'Số điện thoại không được đăng ký cho tài khoản trên, vui lòng liên hệ SePay để được hỗ trợ.']);

            return $this->handleVietinbankClientException($e);
        }
    }

    public function step2($id = '')
    {
        $data = [
            'page_title' => 'Ngân hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
        ];

        if (!has_permission('BankAccount', 'can_add'))
            show_404();

        if (!is_numeric($id))
            show_404();

        $bankAccountModel = model(BankAccountModel::class);
        $bankAccountDetails = $bankAccountModel
            ->select('tb_autopay_bank_account.id, tb_autopay_bank_account.account_number, tb_autopay_bank_account.label, tb_autopay_bank_account.active, tb_autopay_bank_account.created_at, tb_autopay_bank.full_name, tb_autopay_bank.brand_name, tb_autopay_bank_account.account_number, tb_autopay_bank_account.account_holder_name, tb_autopay_bank.icon_path, tb_autopay_bank.logo_path, tb_autopay_bank_account.last_transaction, tb_autopay_bank_account.accumulated,tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.bank_id,tb_autopay_bank_account.bank_sms, tb_autopay_bank_account.bank_sms_connected, tb_autopay_bank_account.bank_api, tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.identification_number, tb_autopay_bank_account.phone_number, tb_autopay_bank_account.sim_id')
            ->join('tb_autopay_bank', 'tb_autopay_bank.id=tb_autopay_bank_account.bank_id')
            ->where([
                'tb_autopay_bank_account.company_id' => $this->user_session['company_id'], 
                'tb_autopay_bank_account.id' => $id,
                'tb_autopay_bank_account.bank_id' => Vietinbank::BANK_ID
            ])->get()->getRow();

        if (!is_object($bankAccountDetails))
            show_404();

        $data['bank_account_details'] = $bankAccountDetails;

        $session = session();
        $data['request_id'] = $session->get('vietinbank_step_1_request_id');
        
        if ($session->get('vietinbank_skip_step_2')) {
            $bankAccountModel->set([
                'bank_api_connected' => 1, 
                'bank_api' => 1,
                'bank_sms' => 0,
                'bank_sms_connected' => 0,
            ])->update($bankAccountDetails->id);
            
            add_user_log(['data_id' => $bankAccountDetails->id, 'company_id' => $this->user_session['company_id'], 'data_type' => 'bank_account_linked', 'description' => 'Chuyển đổi phương thức kết nối sang API Banking', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent'=> $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);

            set_alert('success', 'Tài khoản đã liên kết ngân hàng trước đó!');
            $session->remove('vietinbank_skip_step_2');

            return redirect()->to('vietinbank/settings/' . $id);
        }
        
        $transactionsModel = slavable_model(TransactionsModel::class, 'Vietinbank');
        $data['count_transactions'] = $transactionsModel->where([
            'id' => $bankAccountDetails->id, 
            'deleted_at' => null
        ])->countAllResults();

        if ($bankAccountDetails->bank_api_connected == 1) {
            set_alert('error', 'Tài khoản này đã mở API rồi. Bạn không cần phải liên kết lại.');
            return redirect()->to('vietinbank/settings/' . $id);
        }

        if ($bankAccountDetails->bank_sms == 1 && !$data['request_id']) {
            set_alert('error', 'Tài khoản này đang kết nối bằng phương thức SMS Banking.');
            return redirect()->to('bankaccount/details/' . $id);
        }
        
        $session->remove('vietinbank_step_1_request_id');

        echo view('templates/autopay/header', $data);
        echo view('vietinbank/step2', $data);
        echo view('templates/autopay/footer', $data);
    }

    public function ajax_step2()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        if (!has_permission('BankAccount', 'can_add'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn chưa được cấp quyền để thực hiện thao tác này']);

        $bankAccountModel = model(BankAccountmodel::class);
        $bankAccountId = trim(xss_clean($this->request->getPost('id')));
        $session = session();

        $bankAccountDetails = $bankAccountModel->where([
            'id' => $bankAccountId, 
            'company_id' => $this->user_session['company_id'],
            'bank_id' => Vietinbank::BANK_ID
        ])->get()->getRow();
        
        if (!is_object($bankAccountDetails))
            return $this->response->setJSON(['status' => false, 'message' => 'Không tìm thấy tài khoản ngân hàng này']);

        $data = [
            'otp' => trim(xss_clean($this->request->getVar('otp'))),
            'request_id' => trim($this->request->getVar('request_id')),
        ];

        $rules = [
            'otp' => 'required',
            'request_id' => 'required'
        ];

        if (! $this->validateData($data, $rules)) return $this->fail($this->validator->getErrors());

        try {
            $client = new VietinbankClient;
        } catch (Exception $e) {
            return $this->handleVietinbankClientException($e);
        }

        try {
            $response = $client->alertVerifyOTP($data['request_id'], $data['otp']);

            $isSwitchToApiConnection = $bankAccountDetails->bank_sms == 1;

            $bankAccountModel->set([
                'bank_api_connected' => 1, 
                'bank_api' => 1,
                'bank_sms' => 0,
                'bank_sms_connected' => 0,
            ])->update($bankAccountId);

            if ($isSwitchToApiConnection) {
                add_user_log(['data_id' => $bankAccountId, 'company_id' => $this->user_session['company_id'], 'data_type' => 'bank_account_linked', 'description' => 'Chuyển đổi phương thức kết nối sang API Banking', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent'=> $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);
            } else {
                add_user_log(['data_id' => $bankAccountId, 'company_id' => $this->user_session['company_id'], 'data_type'=> 'bank_account_linked', 'description' => 'Liên kết tài khoản ngân hàng', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);
            }
            
            $bankSubAccountConfig = get_configuration('BankSubAccount');
            
            if ($bankSubAccountConfig == 'off') {
                $configurationModel = model(ConfigurationModel::class);
                $configurationModel->set([
                    'value' => 'on',
                ])->where([
                    'company_id' => $this->user_session['company_id'],
                    'setting' => 'BankSubAccount'
                ])->update();
            }

            set_alert('success', 'Liên kết ngân hàng thành công!');

            return $this->response->setJSON(['status' => true]);
        } catch (Exception $e) {
            $errorCode = $e->getCode();

            if (in_array($errorCode, ['3', '31', '33'])) {
                return $this->fail(['otp' => 'OTP không chính xác hoặc đã hết hiệu lực.']);
            }

            return $this->handleVietinbankClientException($e, $client);
        }
    }

    public function ajax_resend_otp()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        if (!has_permission('BankAccount', 'can_add'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn chưa được cấp quyền để thực hiện thao tác này']);

        $bankAccountModel = model(BankAccountmodel::class);
        $bankAccountId = trim(xss_clean($this->request->getPost('id')));

        $bankAccountDetails = $bankAccountModel->where([
            'id' => $bankAccountId, 
            'company_id' => $this->user_session['company_id'],
            'bank_id' => Vietinbank::BANK_ID
        ])->get()->getRow();
        
        if (!is_object($bankAccountDetails))
            return $this->response->setJSON(['status' => false, 'message' => 'Không tìm thấy tài khoản ngân hàng này']);

        try {
            $client = new VietinbankClient;
        } catch (Exception $e) {
            return $this->handleVietinbankClientException($e);
        }

        try {
            $response = $client->alertRegisterOTP(
                '2', // personal type
                $bankAccountDetails->account_holder_name,
                $bankAccountDetails->account_number,
                $bankAccountDetails->identification_number,
                $bankAccountDetails->phone_number,
            );

            $responseData = json_decode($response->getBody());

            return $this->response->setJSON([
                'status' => true, 
                'message' => "Đã gửi OTP đến số điện thoại.",
                'request_id' => $responseData->requestId
            ]);
        } catch (Exception $e) {
            return $this->handleVietinbankClientException($e);
        }
    }

    public function ajax_unlink($id = '')
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        if (!has_permission('BankAccount', 'can_add'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn chưa được cấp quyền để thực hiện thao tác này']);

        $bankAccountModel = model(BankAccountmodel::class);
        $bankAccountId = trim(xss_clean($this->request->getPost('id')));

        $bankAccountDetails = $bankAccountModel->where([
            'id' => $bankAccountId, 
            'company_id' => $this->user_session['company_id'],
            'bank_id' => Vietinbank::BANK_ID
        ])->get()->getRow();
        
        if (!is_object($bankAccountDetails))
            return $this->response->setJSON(['status' => false, 'message' => 'Không tìm thấy tài khoản ngân hàng này']);

        DeleteCompanyBankAccountAction::run($bankAccountDetails, $this->company_details, $this->request->getIPAddress(), self::class);

        set_alert('success', 'Đã xóa tài khoản ngân hàng.');

        return $this->response->setJSON([
            'status' => true, 
        ]);
    }

    public function step3($id = '')
    {
        $data = [
            'page_title' => 'Ngân hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'notify_detection_code' => $this->vietinbankConfig->notifyDetectionCode,
        ];

        if (!has_permission('BankAccount', 'can_add'))
            show_404();

        if (!is_numeric($id))
            show_404();

        $bankAccountModel = model(BankAccountModel::class);
        $bankAccountDetails = $bankAccountModel
            ->select('tb_autopay_bank_account.id, tb_autopay_bank_account.account_number, tb_autopay_bank_account.label, tb_autopay_bank_account.active, tb_autopay_bank_account.created_at, tb_autopay_bank.full_name, tb_autopay_bank.brand_name, tb_autopay_bank_account.account_number, tb_autopay_bank_account.account_holder_name, tb_autopay_bank.icon_path, tb_autopay_bank.logo_path, tb_autopay_bank_account.last_transaction, tb_autopay_bank_account.accumulated,tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.bank_id,tb_autopay_bank_account.bank_sms, tb_autopay_bank_account.bank_sms_connected, tb_autopay_bank_account.bank_api, tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.identification_number, tb_autopay_bank_account.phone_number, tb_autopay_bank_account.sim_id')
            ->join('tb_autopay_bank', 'tb_autopay_bank.id=tb_autopay_bank_account.bank_id')
            ->where([
                'tb_autopay_bank_account.company_id' => $this->user_session['company_id'], 
                'tb_autopay_bank_account.id' => $id,
                'tb_autopay_bank_account.bank_id' => Vietinbank::BANK_ID
            ])->get()->getRow();

        if (!$bankAccountDetails)
            show_404();

        $data['bank_account_details'] = $bankAccountDetails;

        echo view('templates/autopay/header', $data);
        echo view('vietinbank/step3', $data);
        echo view('templates/autopay/footer', $data);
    }
    
    public function settings($id)
    {
        $data = [
            'page_title' => 'Cấu hình',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
        ];

        if (! has_permission('BankAccount', 'can_add')) {
            show_404();
        }

        if (! is_numeric($id)) {
            show_404();
        }

        $bankAccount = model(BankAccountModel::class)
            ->select('tb_autopay_bank_account.id, tb_autopay_bank_account.account_number, tb_autopay_bank_account.label, tb_autopay_bank_account.active, tb_autopay_bank_account.created_at, tb_autopay_bank.full_name, tb_autopay_bank.brand_name, tb_autopay_bank_account.account_number, tb_autopay_bank_account.account_holder_name, tb_autopay_bank.icon_path, tb_autopay_bank.logo_path, tb_autopay_bank_account.last_transaction, tb_autopay_bank_account.accumulated,tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.bank_id,tb_autopay_bank_account.bank_sms, tb_autopay_bank_account.bank_sms_connected, tb_autopay_bank_account.bank_api, tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.identification_number, tb_autopay_bank_account.phone_number, tb_autopay_bank_account.sim_id')
            ->join('tb_autopay_bank', 'tb_autopay_bank.id=tb_autopay_bank_account.bank_id')
            ->where('tb_autopay_bank_account.company_id', $this->user_session['company_id'])
            ->where('tb_autopay_bank_account.id', $id)
            ->where('tb_autopay_bank_account.bank_id', Vietinbank::BANK_ID)
            ->first();

        if (! $bankAccount) {
            show_404();
        }

        $data['bankAccount'] = $bankAccount;
        $data['settings'] = bank_account_settings($bankAccount->id);
        
        echo view('templates/autopay/header', $data);
        echo view('vietinbank/settings', $data);
        echo view('templates/autopay/footer', $data);
    }

    public function ajax_check_trans() 
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!has_permission('BankAccount', 'can_add'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền thực hiện thao tác này']);

        $bankAccountId = trim(xss_clean($this->request->getVar('bank_account_id')));

        if (!is_numeric($bankAccountId))
            show_404();

        $bankAccountModel = slavable_model(BankAccountModel::class, 'Vietinbank');
        $bankAccountDetails = $bankAccountModel
            ->select('tb_autopay_bank_account.id, tb_autopay_bank_account.account_number, tb_autopay_bank_account.label, tb_autopay_bank_account.active, tb_autopay_bank_account.created_at, tb_autopay_bank.brand_name, tb_autopay_bank_account.account_holder_name, tb_autopay_bank.icon_path, tb_autopay_bank.logo_path, tb_autopay_bank_account.last_transaction, tb_autopay_bank_account.accumulated, tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.bank_id')
            ->join('tb_autopay_bank', 'tb_autopay_bank.id=tb_autopay_bank_account.bank_id')
            ->where([
                'tb_autopay_bank_account.company_id' => $this->user_session['company_id'], 
                'tb_autopay_bank_account.id' => $bankAccountId,
                'tb_autopay_bank_account.bank_id' => Vietinbank::BANK_ID
            ])
            ->get()->getRow();

        if (!is_object($bankAccountDetails))
            show_404();

        $session = session();

        if (!$session->get('checking_transaction_date'))
            $session->set('checking_transaction_date', date('Y-m-d H:i:s'));

        $checkingTransactionDate = $session->get('checking_transaction_date');
        $transactionsModel = slavable_model(TransactionsModel::class, 'Vietinbank');
        $lastTransaction = $transactionsModel
            ->select('id, amount_in, account_number, sub_account, transaction_content, transaction_date')
            ->where([
                'deleted_at' => null, 
                'parser_status' => 'Success', 
                'bank_account_id' => $bankAccountId, 
                'source' => 'BankAPINotify', 
                'transaction_date >= ' => $checkingTransactionDate
            ])
            ->orderBy('id', 'DESC')
            ->get()->getRow();

        if (!is_object($lastTransaction))
            return $this->response->setJSON(['status' => false]);

        $session->remove('checking_transaction_date');

        return $this->response->setJSON(['status' => true, 'last_transaction' => $lastTransaction]);
    }

    public function details($id = '')
    {
        $data = [
            'page_title' => 'Ngân hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'allowed_delete_bank_account' => $this->vietinbankConfig->allowedDeleteBankAccount ?? false,
            'allowed_switch_sms_connection' => $this->vietinbankConfig->allowedSwitchSmsConnection ?? false,
            'allowed_switch_api_connection' => $this->vietinbankConfig->allowedSwitchApiConnection ?? false,
            'notify_detection_code' => $this->vietinbankConfig->notifyDetectionCode ?? '',
            'api_connection_visibility' => $this->vietinbankConfig->visibility ?? false,
            'allowed_enterprise_connection' => $this->vietinbankConfig->allowedEnterpriseConnection ?? false
        ];

        if (!has_permission('BankAccount', 'can_view_all'))
            show_404();

        if (!is_numeric($id))
            show_404();

        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $transactionsModel = slavable_model(TransactionsModel::class, 'Vietinbank');
        $simCompanyModel = model(SimCompanyModel::class);
        $vietinbankEnterpriseAccountModel = model(VietinbankEnterpriseAccountModel::class);

        $bankAccountDetails = $bankAccountModel
            ->select('tb_autopay_bank_account.id, tb_autopay_bank_account.account_number, tb_autopay_bank_account.label, tb_autopay_bank_account.active, tb_autopay_bank_account.created_at, tb_autopay_bank.full_name, tb_autopay_bank.brand_name, tb_autopay_bank_account.account_number, tb_autopay_bank_account.account_holder_name, tb_autopay_bank.icon_path, tb_autopay_bank.logo_path, tb_autopay_sim.sim_phonenumber, tb_autopay_bank_account.last_transaction, tb_autopay_bank_account.accumulated,tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.bank_id,tb_autopay_bank_account.bank_sms, tb_autopay_bank_account.bank_sms_connected, tb_autopay_bank_account.bank_api, tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.identification_number, tb_autopay_bank_account.phone_number, tb_autopay_bank_account.sim_id')
            ->join('tb_autopay_bank', 'tb_autopay_bank.id=tb_autopay_bank_account.bank_id')
            ->join('tb_autopay_sim', 'tb_autopay_sim.id=tb_autopay_bank_account.sim_id', 'left')
            ->where([
                'tb_autopay_bank_account.company_id' => $this->user_session['company_id'], 
                'tb_autopay_bank_account.id' => $id,
                'tb_autopay_bank_account.bank_id' => Vietinbank::BANK_ID
            ])->get()->getRow();
        
        if (!is_object($bankAccountDetails))
            show_404();
       
        if ($bankAccountDetails->bank_api_connected == 0 && $bankAccountDetails->bank_api == 1)
            return redirect()->to(base_url('vietinbank/step2/' . $bankAccountDetails->id));

        $data['bank_account_details'] = $bankAccountDetails;

        $enterpriseAccountDetails = $vietinbankEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->get()->getRow();

        $data['bank_account_details'] = $bankAccountDetails;

        $data['is_enterprise_account'] = $enterpriseAccountDetails;

        $data['count_subs'] = $bankSubAccountModel->where(['bank_account_id' => $id, 'deleted_at' => null])->countAllResults();
       
        $data['bank_sms_info'] = get_bank_sms_info($data['bank_account_details']->brand_name);
       
        $data['count_transactions'] = $transactionsModel->where(['bank_account_id' => $data['bank_account_details']->id, 'deleted_at' => NULL])->countAllResults();
       
        $data['count_sms_transactions'] = $transactionsModel->where(['bank_account_id' => $data['bank_account_details']->id, 'deleted_at' => NULL, 'source' => 'SMS'])->countAllResults();
       
        $data['sims'] = $simCompanyModel
            ->select('tb_autopay_sim.id, tb_autopay_sim.sim_phonenumber')
            ->join('tb_autopay_sim', 'tb_autopay_sim.id=tb_autopay_sim_company.sim_id')
            ->where(['tb_autopay_sim_company.company_id' => $this->user_session['company_id'], 'tb_autopay_sim.active' => 1])
            ->orderBy('tb_autopay_sim_company.created_at', 'DESC')
            ->get()->getResult();

        // data QR
        $data['bank_sub_accounts_custom']=[];
        if(!empty($data['bank_account_details'])){    
            $data['bank_sub_accounts_custom'] = $bankSubAccountModel->select("tb_autopay_bank_sub_account.id,tb_autopay_bank_sub_account.acc_type, tb_autopay_bank.brand_name,tb_autopay_bank.icon_path, tb_autopay_bank_account.account_holder_name, tb_autopay_bank_account.account_number, tb_autopay_bank_sub_account.sub_account, tb_autopay_bank_sub_account.label")
                ->join("tb_autopay_bank_account", "tb_autopay_bank_account.id = tb_autopay_bank_sub_account.bank_account_id")
                ->join("tb_autopay_bank", "tb_autopay_bank.id = tb_autopay_bank_account.bank_id", "left")
                ->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id']])
                ->where('tb_autopay_bank_account.id',$data['bank_account_details']->id) 
                ->get()
                ->getResultArray();
        }    

        echo view('templates/autopay/header', $data);
        if ($data['bank_account_details']->bank_api && $data['is_enterprise_account']) {
            $data['vac'] = $this->makeEnterpriseVac($bankAccountDetails, $enterpriseAccountDetails);
            $data['va_minlength'] = $this->vietinbankConfig->enterpriseVaMinlength ?? 5;
            $data['va_maxlength'] = $this->vietinbankConfig->enterpriseVaMaxlength ?? 19;
            $data['va_name_minlength'] = $this->vietinbankConfig->enterpriseVaNameMinlength ?? 1;
            $data['va_name_maxlength'] = $this->vietinbankConfig->enterpriseVaNameMaxlength ?? 70;
            $data['prefix_received_va_name'] = $enterpriseAccountDetails->prefix_received_name;

            echo view('vietinbank/enterprise/details', $data);
        } else if ($data['bank_account_details']->bank_api && !$data['is_enterprise_account']) {
            echo view('vietinbank/details', $data);
        } else {
            echo view('vietinbank/sms/details', $data);
        }
        echo view('templates/autopay/footer', $data);
    }

    public function ajax_va_list()
    {
        if (!has_permission('BankAccount', 'can_view_all'))
            show_404();
     
        if ($this->request->getMethod(true) != 'POST')
            return '';

        $bankAccountId = $this->request->getGet('bank_account_id');

        if (!is_numeric($bankAccountId))
            $bankAccountId = false;

        if ($bankAccountId) {
            $bankAccountModel = model(BankAccountModel::class);
            $bankAccountDetails = $bankAccountModel->where('id', $bankAccountId)->get()->getRow();
            $enterpriseAccountDetails = model(VietinbankEnterpriseAccountModel::class)->where('bank_account_id', $bankAccountId)->first();
            $accType = $bankAccountDetails ? ($bankAccountDetails->bank_api ? 'Real' : 'Virtual') : null;
        } else {
            $bankAccountDetails = null;
            $accType = null;
            $enterpriseAccountDetails = null;
        }
 
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $bankSubAccounts = $bankSubAccountModel->getDatatables($this->user_session['company_id'], $bankAccountId);
        $data = [];
        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');
        $canEdit = has_permission('BankAccount', 'can_edit');
        $canDelete = has_permission('BankAccount', 'can_delete');
 
        foreach ($bankSubAccounts as $bankSubAccount) {
            $no++;
            $row = array();
            $actionsBtnHtml = '<div class="d-flex align-items-center" style="gap: 0.5rem;">';

            if ($canEdit) {
                if ($accType == 'Real') {
                    $actionsBtnHtml .=  '<a href="' . base_url('banksubaccount/qrcode/' . $bankSubAccount->id . '?print=yes') . '" target="_blank" class="btn btn-sm btn-outline-primary">
                        <i class="bi bi-printer mt-2"></i> In Standee QR
                    </a>';
                }

                if ($bankSubAccount->acc_type == 'Virtual' || ($bankSubAccount->acc_type == 'Real' && $bankAccountDetails->bank_api == 1)) {
                    $actionsBtnHtml .= "<a href='javascript:;' onclick='edit_va(" . esc($bankSubAccount->id) . ")' class='btn btn-sm btn-outline-warning'><i class='bi bi-pencil'></i> Sửa</a>";
                }

                if ($bankSubAccount->acc_type == 'Real') {
                    if ($bankSubAccount->va_active) {
                        $actionsBtnHtml .=  "<button type='button' onclick='disable_va(" . esc($bankSubAccount->id) . ")' class='d-inline-flex align-items-center btn btn-sm btn-outline-secondary btn-disable-va-".$bankSubAccount->id."' style='gap: 0.25rem'><div class='spinner-border text-danger loader' style='width: 10px; height: 10px; display: none;' role='status'></div> <i class='bi bi-toggle-off'></i> Vô hiệu hoá</button>";
                    } else {
                        $actionsBtnHtml .=  "<button type='button' onclick='enable_va(" . esc($bankSubAccount->id) . ")' class='d-inline-flex align-items-center btn btn-sm btn-outline-primary btn-enable-va-".$bankSubAccount->id."' style='gap: 0.25rem'><div class='spinner-border text-primary loader' style='width: 10px; height: 10px; display: none;' role='status'></div> <i class='bi bi-toggle-off'></i> Kích hoạt</button>";
                    }
                }
            }

            if ($canDelete && ($bankSubAccount->acc_type == 'Virtual' || ($bankSubAccount->acc_type == 'Real' && $bankAccountDetails->bank_api == 1)))
                $actionsBtnHtml .= "<a href='javascript:;' onclick='delete_va(" . esc($bankSubAccount->id) . ")' class='btn btn-sm btn-outline-danger'><i class='bi bi-trash3'></i> Xóa</a>";
                 
            $actionsBtnHtml .= '</div>';
                 
            $row[] = $no;
            $row[] = $bankSubAccount->id;
            $row[] = "<a href='javascript:;' onclick='view_va("  . esc($bankSubAccount->id) . ")'>" . esc($bankSubAccount->sub_account) . ($bankSubAccount->acc_type == 'Real' ? " <i class='bi bi-patch-check-fill'></i>" : '') . "</a>";
          
            if ($bankSubAccount->va_active == 1)
                $row[] = "<span class='text-success'>Hoạt động</span>";
            else
                $row[] = "<span class='text-danger'>Tạm ngưng</span>";

            if ($accType == 'Real' && $enterpriseAccountDetails) {
                $row[] = esc($bankSubAccount->sub_holder_name);
            } else {
                $row[] = esc($bankSubAccount->label);
            }

            $row[] = esc($bankSubAccount->created_at);
            $row[] = $actionsBtnHtml;
            $data[] = $row;
        }
 
        return $this->response->setJSON([
            'draw' => $draw,
            'recordsTotal' => $bankSubAccountModel->countAll($this->user_session['company_id'], $bankAccountId),
            'recordsFiltered' => $bankSubAccountModel->countFiltered($this->user_session['company_id'], $bankAccountId),
            'data' => $data,
        ]);
    }

    public function ajax_view_va($id = '')
    {
        if (!has_permission('BankAccount', 'can_view_all'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền xem tài khoản ngân hàng']);
        
        if (!is_numeric($id)) 
            return $this->response->setJSON(['status' => false, 'message' => 'ID tài khoản ngân ảo không hợp lệ']);

        $bankSubAccountModel = model(BankSubAccountModel::class);
        $transactionsModel = slavable_model(TransactionsModel::class, 'Vietinbank');
        $vietinbankEnterpriseAccountModel = model(VietinbankEnterpriseAccountModel::class);

        $data['va_details'] = $bankSubAccountModel
            ->select('tb_autopay_bank_sub_account.id, tb_autopay_bank_sub_account.sub_account, tb_autopay_bank_sub_account.acc_type, tb_autopay_bank_sub_account.bank_account_id, tb_autopay_bank_sub_account.label, tb_autopay_bank_account.account_number, tb_autopay_bank_account.active, tb_autopay_bank_account.created_at, tb_autopay_bank.brand_name, tb_autopay_bank_account.account_number, tb_autopay_bank_account.account_holder_name, tb_autopay_bank.icon_path, tb_autopay_bank.logo_path, tb_autopay_bank.full_name, tb_autopay_bank_sub_account.sub_holder_name, tb_autopay_bank_sub_account.va_active')
            ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id')
            ->join('tb_autopay_bank', 'tb_autopay_bank.id=tb_autopay_bank_account.bank_id')
            ->where([
                'tb_autopay_bank_sub_account.id' => $id, 
                'tb_autopay_bank_account.company_id' => $this->user_session['company_id'],
                'tb_autopay_bank_account.bank_id' => Vietinbank::BANK_ID
            ])->get()->getRow();

        $data['notify_detection_code'] = $this->vietinbankConfig->notifyDetectionCode ?? false;

        $data['count_transactions'] = $transactionsModel->where([
            'sub_account' => $data['va_details']->sub_account
        ])->countAllResults();
        
        $data['last_transaction'] = $transactionsModel->where([
            'sub_account' => $data['va_details']->sub_account, 
            'accumulated != ' => ''
        ])->orderBy('transaction_date', 'DESC')->get()->getRow();

        $enterpriseAccountDetails = $vietinbankEnterpriseAccountModel->where(['bank_account_id' => $data['va_details']->bank_account_id])->first();
        
        if ($data['va_details']) {
            $html = view($enterpriseAccountDetails ? 'vietinbank/enterprise/va_view' : 'vietinbank/va_view', $data);
            return $this->response->setJSON(['status' => true, 'html' => $html]);
        }

        return $this->response->setJSON(['status' => false, 'message' => 'Không tìm thấy tài khoản ảo này']);
    }

    public function ajax_bank_account_update() 
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        if (!has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền sửa tài khoản ngân hàng']);
 
        $data = [
            'id' => trim(xss_clean($this->request->getVar('id'))),
            'label' => trim(xss_clean($this->request->getVar('label'))),
        ];

        $rules = [
            'id' => ['required', 'integer', 'is_natural'],
            'label' => [
                'label' => 'Tên gợi nhớ',
                'rules' => ['permit_empty', 'max_length[100]']
            ]
        ];

        if (! $this->validateData($data, $rules))
            return $this->response->setJSON(['status' => false, 'message' => implode(". ", $this->validator->getErrors())]);

        $bankAccountModel = model(BankAccountmodel::class);

        $bankAccountDetails = $bankAccountModel->where(['id' => $data['id'], 'company_id' => $this->user_session['company_id']])->get()->getRow();
        
        if (!is_object($bankAccountDetails))
            return $this->response->setJSON(['status' => false, 'message' => 'Không tìm thấy tài khoản ngân hàng này']);

        $updated = $bankAccountModel->set(['label' => $data['label']])->where('id', $bankAccountDetails->id)->update();
        
        if ($updated) { 
            add_user_log(['data_id' => $data['id'], 'company_id' => $this->user_session['company_id'], 'data_type' => 'bank_account_update', 'description' => 'Sửa tài khoản ngân hàng', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);
            return $this->response->setJSON(['status' => true]);
        }
         
        return $this->response->setJSON(['status' => false, 'message' => 'Không thể cập nhật tài khoản ngân hàng!']);
    }

    public function ajax_add_va()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!has_permission('BankAccount', 'can_add'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền thêm tài khoản ngân hàng']);
 
        $data = [
            'sub_account' =>  trim(xss_clean($this->request->getVar('sub_account'))),
            'bank_account_id' => trim(xss_clean($this->request->getVar('bank_account_id'))),
            'label' => trim(xss_clean($this->request->getVar('label'))),
        ];

        $rules = [
            'bank_account_id' => ['label' => 'Tài khoản chính', 'rules' => 'integer|is_natural'],
            'sub_account' => ['label' => 'Số tài khoản ảo', 'rules' => 'required|min_length[2]|max_length[3]|regex_match[/^[A-Z0-9]{2,3}$/]'],
            'label' => ['label' => 'Tên gợi nhớ', 'rules' => 'max_length[100]'],
        ];

        if (!$this->validateData($data, $rules)) {
            return $this->response->setJSON(['status' => false, 'message' => implode('. ', $this->validator->getErrors())]);
        }  

        $bankAccountModel = model(BankAccountModel::class);
        $bankAccountDetails = $bankAccountModel
            ->select('id, active, company_id, bank_id')
            ->where([
                'active' => 1,
                'bank_id' => Vietinbank::BANK_ID,
                'id' => $data['bank_account_id'], 
                'company_id' => $this->user_session['company_id'],
            ])->get()->getRow();
        
        if (!is_object($bankAccountDetails))
            show_404();

        $bankSubAccountModel = model(BankSubAccountModel::class);

        $existOwnBankSubAccount = $bankSubAccountModel
            ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id')
            ->where([
                'tb_autopay_bank_account.company_id' => $this->user_session['company_id'], 
                'tb_autopay_bank_sub_account.sub_account' => $data['sub_account']
            ])
            ->get()->getRow();

        if (is_object($existOwnBankSubAccount))
            return $this->fail(['sub_account' => 'Tài khoản ảo này đã tồn tại. Bạn đã tạo tài khoản ảo này rồi.']);
          
        $existBankSubAccount = $bankSubAccountModel->where(['tb_autopay_bank_sub_account.sub_account' => $data['sub_account']])->get()->getRow();

        if (is_object($existBankSubAccount))
            return $this->fail(['sub_account' => 'Tài khoản ảo này đã tồn tại trên hệ thống. Vui lòng chọn tên khác.']);
            
        if (!is_speaker_billing_subscription() && preg_match('/^L[a-zA-Z0-9]+$/i', $data['sub_account'])) {
            return $this->fail(['sub_account' => 'Tài khoản ảo này đã tồn tại trên hệ thống. Vui lòng chọn tên khác.']);
        }
        
        $outputDeviceDecalModel = model(OutputDeviceDecalModel::class);
        
        if ($outputDeviceDecalModel->where('virtual_account_number', $data['sub_account'])->countAllResults() > 0) {
            return $this->fail(['sub_account' => 'Tài khoản ảo này đã tồn tại trên hệ thống. Vui lòng chọn tên khác.']);
        }
     
        $bankSubAccountId = $bankSubAccountModel->insert([
            'sub_account' =>  $data['sub_account'],
            'bank_account_id' => $data['bank_account_id'],
            'label' => $data['label'],
            'acc_type' => 'Virtual',
        ]);
        
        if (!$bankSubAccountId)
            return $this->response->setJSON(['status' => false, 'message' => 'Không thể thêm tài khoản ảo này. Vui lòng liên hệ SePay để được hỗ trợ.']);

        return $this->response->setJSON(['status' => true, 'id' => $bankSubAccountId]);
    }

    public function ajax_request_delete_bank_account()
    {
        if (!$this->vietinbankConfig->allowedDeleteBankAccount)
            return $this->failNotFound();

        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        if (!has_permission('BankAccount', 'can_delete'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn chưa được cấp quyền để thực hiện thao tác này']);

        $bankAccountModel = model(BankAccountmodel::class);
        $bankAccountId = trim(xss_clean($this->request->getPost('id')));

        $bankAccountDetails = $bankAccountModel->where([
            'id' => $bankAccountId, 
            'company_id' => $this->user_session['company_id'],
            'bank_id' => Vietinbank::BANK_ID
        ])->get()->getRow();
        
        if (!is_object($bankAccountDetails))
            return $this->response->setJSON(['status' => false, 'message' => 'Không tìm thấy tài khoản ngân hàng này']);

        try {
            $client = new VietinbankClient;
        } catch (Exception $e) {
            return $this->handleVietinbankClientException($e);
        }

        try {
            $response = $client->alertRevokeOTP(
                '2', // personal type
                $bankAccountDetails->account_holder_name,
                $bankAccountDetails->account_number,
                $bankAccountDetails->identification_number,
                $bankAccountDetails->phone_number
            );
            $responseData = json_decode($response->getBody());

            return $this->response->setJSON([
                'status' => true, 
                'message' => 'Đã gửi OTP đến số điện thoại.',
                'request_id' => $responseData->requestId
            ]);
        } catch (Exception $e) {
            $errorCode = $e->getCode();

            if ($errorCode == '96') {
                DeleteCompanyBankAccountAction::run($bankAccountDetails, $this->company_details, $this->request->getIPAddress(), self::class);
            
                add_user_log(['data_id' => $bankAccountId, 'company_id' => $this->user_session['company_id'], 'data_type' => 'bank_account_delete', 'description' => 'Xóa tài khoản ngân hàng ' . $bankAccountDetails->account_number, 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);
                set_alert('success', 'SePay đã xóa tài khoản ngân hàng vì tài khoản ngân hàng đã hủy liên kết trước đó.');

                return $this->response->setJSON(['status' => true, 'force_delete' => true]);
            }

            return $this->handleVietinbankClientException($e, $client);
        }
    }

    public function ajax_confirm_delete_bank_account()
    {
        if (!$this->vietinbankConfig->allowedDeleteBankAccount) return $this->failNotFound();
        
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        if (!has_permission('BankAccount', 'can_delete'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn chưa được cấp quyền để thực hiện thao tác này']);

        $bankAccountModel = model(BankAccountModel::class);
        $bankAccountId = trim(xss_clean($this->request->getPost('id')));

        $bankAccountDetails = $bankAccountModel->where([
            'id' => $bankAccountId, 
            'company_id' => $this->user_session['company_id'],
            'bank_id' => Vietinbank::BANK_ID
        ])->get()->getRow();
        
        if (!is_object($bankAccountDetails))
            return $this->response->setJSON(['status' => false, 'message' => 'Không tìm thấy tài khoản ngân hàng này']);

        $data = [
            'otp' => trim(xss_clean($this->request->getVar('otp'))),
            'request_id' => trim($this->request->getVar('request_id')),
        ];

        $rules = [
            'otp' => 'required',
            'request_id' => 'required'
        ];

        if (! $this->validateData($data, $rules)) return $this->fail($this->validator->getErrors());

        try {
            $client = new VietinbankClient;
        } catch (Exception $e) {
            return $this->handleVietinbankClientException($e);
        }

        try {
            $response = $client->alertVerifyOTP($data['request_id'], $data['otp']);

            DeleteCompanyBankAccountAction::run($bankAccountDetails, $this->company_details, $this->request->getIPAddress(), self::class);
            
            add_user_log(['data_id' => $bankAccountId, 'company_id' => $this->user_session['company_id'], 'data_type' => 'bank_account_delete', 'description' => 'Xóa tài khoản ngân hàng ' . $bankAccountDetails->account_number, 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);
            set_alert('success', 'Đã hủy liên kết và xóa tài khoản ngân hàng thành công.');

            return $this->response->setJSON(['status' => true]);
        } catch (Exception $e) {
            $errorCode = $e->getCode();

            if (in_array($errorCode, ['3', '31', '33'])) {
                return $this->fail(['otp' => 'OTP không chính xác hoặc đã hết hiệu lực.']);
            }

            return $this->handleVietinbankClientException($e, $client);
        }
    }

    public function ajax_request_switch_sms_connection()
    {
        if (!$this->vietinbankConfig->allowedSwitchSmsConnection)
            return $this->failNotFound();
        
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        if (!has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn chưa được cấp quyền để thực hiện thao tác này']);

        $bankAccountId = trim(xss_clean($this->request->getPost('id')));
        $bankAccountDetails = $this->getSwitchableSmsConnectionBankAccount($bankAccountId);
        
        if (!is_object($bankAccountDetails))
            return $this->response->setJSON(['status' => false, 'message' => 'Tài khoản ngân hàng không đủ điều kiện']);

        if (!$this->determineIfAbleSwitchToSmsConnection())
            return $this->response->setJSON(['status' => false, 'message' => 'Gói dịch vụ của bạn không cho phép kết nối Ngân hàng qua SMS báo số dư. Vui lòng nâng cấp gói hoặc liên hệ SePay để được hỗ trợ.']);

        return $this->response->setJSON(['status' => true]);
    }

    public function ajax_confirm_switch_sms_connection()
    {
        if (!$this->vietinbankConfig->allowedSwitchSmsConnection) 
            return $this->failNotFound();
        
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        if (!has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn chưa được cấp quyền để thực hiện thao tác này']);

        $bankAccountId = trim(xss_clean($this->request->getPost('id')));
        $bankAccountDetails = $this->getSwitchableSmsConnectionBankAccount($bankAccountId);
        
        if (!is_object($bankAccountDetails))
            return $this->response->setJSON(['status' => false, 'message' => 'Tài khoản ngân hàng không đủ điều kiện']);

        if (!$this->determineIfAbleSwitchToSmsConnection())
            return $this->response->setJSON(['status' => false, 'message' => 'Gói dịch vụ của bạn không cho phép kết nối Ngân hàng qua SMS báo số dư. Vui lòng nâng cấp gói hoặc liên hệ SePay để được hỗ trợ.']);

        $bankAccountModel = model(BankAccountModel::class);
        $simCompanyModel = model(SimCompanyModel::class);

        $simId = trim(xss_clean($this->request->getPost('sim_id')));
        $bankAccountUpdateData = [
            'bank_sms' => 1,
            'bank_sms_connected' => 0,
            'bank_api' => 0,
            'bank_api_connected' => 0
        ];

        if (is_numeric($simId)) {
            $simDetails = $simCompanyModel->select('tb_autopay_sim.id')
                ->join('tb_autopay_sim', 'tb_autopay_sim.id=tb_autopay_sim_company.sim_id')
                ->where([
                    'tb_autopay_sim_company.company_id' => $this->user_session['company_id'], 
                    'tb_autopay_sim.active' => 1, 
                    'tb_autopay_sim.id' => $simId
                ])
                ->get()->getRow();

            if (!is_object($simDetails))
                return $this->response->setJSON(['status' => false, 'message' => 'SIM nhận SMS mà bạn chọn không khả dụng']);

            $bankAccountUpdateData['sim_id'] = $simId;
        }

        $bankAccountUpdated = $bankAccountModel->set($bankAccountUpdateData)->update($bankAccountId);

        if (!$bankAccountUpdated)
            return $this->response->setJSON(['status' => false, 'message' => 'Không thể chuyển đổi phương thức kết nối sang SMS Banking, vui lòng liên hệ SePay để được hỗ trợ.']);
        
        add_user_log(['data_id' => $bankAccountId, 'company_id' => $this->user_session['company_id'], 'data_type' => 'bank_account_update', 'description' => 'Chuyển đổi phương thức kết nối sang SMS Banking', 'user_id' => $this->user_details->id, 'ip'=>$this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);
        return $this->response->setJSON(['status' => true]);
    }

    public function ajax_request_switch_personal_api_connection()
    {
        if (!$this->vietinbankConfig->allowedSwitchApiConnection)
            return $this->failNotFound();
        
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        if (!has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn chưa được cấp quyền để thực hiện thao tác này']);

        $bankAccountId = trim(xss_clean($this->request->getPost('bank_account_id')));
        $bankAccountDetails = $this->getSwitchableApiConnectionBankAccount($bankAccountId);
        
        if (!is_object($bankAccountDetails))
            return $this->response->setJSON(['status' => false, 'message' => 'Tài khoản ngân hàng không đủ điều kiện']);

        $bankAccountModel = model(BankAccountModel::class);

        $session = session();
        $session->set('vietinbank_request_switch_api_connection', $bankAccountDetails->id);
        $redirectTo = base_url('vietinbank/step1');
        
        return $this->response->setJSON(['status' => true, 'redirect_to' => $redirectTo]);
    }

    public function ajax_send_enterprise_connection_request()
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }

        if (!$this->vietinbankConfig->allowedEnterpriseConnection) {
            return $this->failNotFound();
        }

        if (!has_permission('BankAccount', 'can_add')) {
            return $this->respond(['status' => false, 'message' => 'Bạn chưa được cấp quyền để thực hiện thao tác này']);
        }
    
        if (! $this->validate([
            'company_name' => 'required|min_length[5]|max_length[100]',
            'has_account' => 'required',
            'phone_number' => 'required|min_length[10]|max_length[20]',
            'account_number' => [
                'rules' => $this->request->getVar('has_account') == 1 ? 'required|max_length[50]' : 'permit_empty|max_length[50]',
            ],
            'branch_name' => [
                'rules' => $this->request->getVar('has_account') == 1 ? 'required|max_length[100]' : 'permit_empty|max_length[100]',
            ],
            'notes' => 'permit_empty'
        ])) return $this->fail($this->validator->getErrors());

        $data = [
            'company_name' => trim($this->request->getVar('company_name')),
            'phone_number' => trim($this->request->getVar('phone_number')),
            'has_account' => $this->request->getVar('has_account'),
            'account_number' => trim($this->request->getVar('account_number')),
            'branch_name' => trim($this->request->getVar('branch_name')),
            'notes' => trim($this->request->getVar('notes'))
        ];

        $message = '
------------------------------

Có yêu cầu kết nối API VietinBank doanh nghiệp mới:

#️⃣ Tên cá nhân/tổ chức: ' . $data['company_name'] . '

ℹ️ Đã có tài khoản VietinBank: ' . ($data['has_account'] ? 'Đã có' : 'Chưa') . '

📞 Số điện thoại liên hệ: ' . $data['phone_number'] . '

' . ($data['has_account'] ? '🏦 Số tài khoản VietinBank: ' . $data['account_number'] . '

📍 Chi nhánh: ' . $data['branch_name'] . '

' : '') . '⏰ Thực hiện lúc: ' . date("Y-m-d H:i:s") . '

✍🏻 Ghi chú: ' . $data['notes'] . '

------------------------------
        ';

        $telegramQueueModel = model(NotificationTelegramQueueModel::class);
        $telegramQueueModel->insert([
            'chat_id' => $this->vietinbankConfig->telegramChatId,
            'status' => 'Pending',
            'message' => $message
        ]);

        return $this->respond([
            'status' => true,
            'message' => 'SePay đã nhận được yêu cầu của bạn và sẽ liên hệ lại trong thời gian sớm nhất',
        ]);
    }

    public function ajax_connect_enterprise()
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }

        if (!$this->vietinbankConfig->allowedEnterpriseConnection) {
            return $this->failNotFound();
        }

        if (!has_permission('BankAccount', 'can_add')) {
            return $this->respond(['status' => false, 'message' => 'Bạn chưa được cấp quyền để thực hiện thao tác này']);
        }

        if (! $this->validate([
            'account_number' => [
                'label' => 'số tài khoản',
                'rules' => ['required', 'max_length[250]'],
            ],
            'account_holder_name' => [
                'label' => 'tên chủ tài khoản',
                'rules' => ['required', 'max_length[250]']
            ],
            'va_prefix' => [
                'label' => 'đầu định danh',
                'rules' => ['required', 'in_list[0,1,2,3,4,5,6,7,8,9]'],
            ],
            'prefix_id' => [
                'label' => 'mã định danh',
                'rules' => ['required', 'regex_match[/^(?=.*[A-Z])[A-Z0-9]{3}$/]']
            ],
            'prefix_received_name' => [
                'label' => 'tiền tố tên thụ hưởng',
                'rules' => ['required', 'regex_match[/^[A-Za-z\s0-9]{1,70}$/]']
            ],
            'label' => [
                'label' => 'tên gợi nhớ',
                'rules' => ['permit_empty', 'max_length[100]']
            ]
        ])) return $this->fail($this->validator->getErrors());

        $bankAccountModel = model(BankAccountModel::class);
        $enterpriseAccountModel = model(VietinbankEnterpriseAccountModel::class);
        $vaPrefix = trim(xss_clean(strtoupper($this->request->getVar('va_prefix'))));
        $prefixReceivedName = trim(xss_clean(strtoupper($this->request->getVar('prefix_received_name'))));
        $prefixId = trim(xss_clean(strtoupper($this->request->getVar('prefix_id'))));

        $accountNumber = trim(xss_clean($this->request->getVar('account_number')));

        if ($bankAccountModel->where(['bank_id' => Vietinbank::BANK_ID, 'account_number' => $accountNumber])->countAllResults()) {
            return $this->fail(['account_number' => 'Số tài khoản đã tồn tại.']);
        }
        
        if ($enterpriseAccountModel->where(['va_prefix' => $vaPrefix, 'prefix_id' => $prefixId])->countAllResults()) {
            return $this->respond(['status' => false, 'message' => 'Tiền tố định danh đã tồn tại, vui lòng chọn lại đầu định danh hoặc mã định danh khác.']);
        }

        $bankAccountId = $bankAccountModel->insert([
            'company_id' => $this->user_session['company_id'],
            'account_holder_name' => remove_accents(trim(xss_clean($this->request->getVar('account_holder_name'))), true),
            'account_number' => $accountNumber,
            'bank_id' => Vietinbank::BANK_ID,
            'label' => trim(xss_clean($this->request->getVar('label'))),
            'active' => 1,
            'bank_api' => 1,
            'bank_api_connected' => 1,
        ]);
            
        if ($bankAccountId) {
            $enterpriseAccountModel->insert([
                'bank_account_id' => $bankAccountId, 
                'prefix_id' => $prefixId, 
                'va_prefix' => $vaPrefix, 
                'prefix_received_name' => $prefixReceivedName
            ]);
            
            add_user_log(['data_id' => $bankAccountId, 'company_id' => $this->user_session['company_id'], 'data_type' => 'bank_account_add', 'description' => 'Thêm tài khoản ngân hàng VietinBank API', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);
            return $this->respond(['status' => true, 'id' => $bankAccountId]);
        }

        add_user_log(['data_id' => 0, 'company_id' => $this->user_session['company_id'], 'data_type' => 'bank_account_add', 'description' => 'Thêm tài khoản ngân hàng VietinBank API', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Failed']);
        return $this->respond(['status' => false, 'message' => 'Không thể thêm tài khoản ngân hàng. Vui lòng liên hệ SePay để được hỗ trợ.']);
    }

    public function ajax_add_enterprise_va()
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }

        if (! has_permission('BankAccount', 'can_add')) {
            return $this->respond(['status' => false, 'message' => 'Bạn không có quyền thêm tài khoản ngân hàng']);
        }

        $data = [
            'bank_account_id' => trim(xss_clean($this->request->getVar('bank_account_id'))),
            'sub_id' => strtoupper(trim(xss_clean($this->request->getVar('sub_id')))),
            'sub_holder_name' => trim(xss_clean($this->request->getVar('sub_holder_name'))),
        ];
        
        $bankAccountModel = model(BankAccountModel::class);
        $bankAccountDetails = $bankAccountModel
            ->select('id, active, company_id, bank_id')
            ->where([
                'active' => 1,
                'bank_id' => Vietinbank::BANK_ID,
                'id' => $data['bank_account_id'],
                'bank_api' => 1,
                'bank_api_connected' => 1,
                'company_id' => $this->user_session['company_id'],
            ])->first();
        
        if (!is_object($bankAccountDetails)) {
            return $this->failNotFound();
        }

        $vietinbankEnterpriseAccountModel = model(VietinbankEnterpriseAccountModel::class);
        $enterpriseAccountDetails = $vietinbankEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->first();

        if (! $enterpriseAccountDetails) {
            return $this->respond(['status' => false, 'message' => 'Chưa hỗ trợ thêm VA cho tài khoản ngân hàng này. Vui lòng liên hệ SePay để được hỗ trợ.']);
        }

        $vac = $this->makeEnterpriseVac($bankAccountDetails, $enterpriseAccountDetails);
        $vaMinlength = $this->vietinbankConfig->enterpriseVaMinlength ?? 5;
        $vaMaxlength = $this->vietinbankConfig->enterpriseVaMaxlength ?? 19;
        $vaNameMaxlength = $this->vietinbankConfig->enterpriseVaNamelength ?? 70;
        $subIdRule = 'regex_match[/^[A-Z0-9]{' . ($vaMinlength - strlen($vac)) . ',' . ($vaMaxlength - strlen($vac)) . '}$/]';
        $subHolderNameRule = 'regex_match[/^[A-Za-z0-9\s]{1,' . ($vaNameMaxlength - strlen($enterpriseAccountDetails->prefix_received_name) - 1) . '}$/]';

        $rules = [
            'bank_account_id' => ['label' => 'Tài khoản chính', 'rules' => 'integer|is_natural'],
            'sub_id' => ['label' => 'Số tài khoản ảo', 'rules' => ['required', $subIdRule]],
            'sub_holder_name' => ['label' => 'Tên hiển thị', 'rules' => ['required', $subHolderNameRule]],
        ];

        if (!$this->validateData($data, $rules)) {
            return $this->fail($this->validator->getErrors());
        }  

        $data['sub_account'] = $vac . $data['sub_id'];

        $bankSubAccountModel = model(BankSubAccountModel::class);

        $existOwnBankSubAccount = $bankSubAccountModel
            ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id')
            ->where([
                'tb_autopay_bank_account.company_id' => $this->user_session['company_id'], 
                'tb_autopay_bank_sub_account.sub_account' => $data['sub_account']
            ])
            ->first();

        if (is_object($existOwnBankSubAccount)) {
            return $this->fail(['sub_id' => 'Tài khoản ảo này đã tồn tại. Bạn đã tạo tài khoản ảo này rồi.']);
        }

        $existBankSubAccount = $bankSubAccountModel->where(['tb_autopay_bank_sub_account.sub_account' => $data['sub_account']])->first();

        if (is_object($existBankSubAccount)) {
            return $this->fail(['sub_id' => 'Tài khoản ảo này đã tồn tại trên hệ thống, vui lòng chọn tên khác.']);
        }

        $bankSubAccountId = $bankSubAccountModel->insert([
            'sub_account' =>  $data['sub_account'],
            'sub_holder_name' => $this->makeEnterpriseVaName($enterpriseAccountDetails, $data['sub_holder_name']),
            'bank_account_id' => $data['bank_account_id'],
            'acc_type' => 'Real',
        ]);
        
        if (!$bankSubAccountId) {
            return $this->respond(['status' => false, 'message' => 'Không thể thêm tài khoản ảo này. Vui lòng liên hệ SePay để được hỗ trợ.']);
        }

        return $this->respond(['status' => true, 'id' => $bankSubAccountId]);
    }

    public function ajax_edit_enterprise_va()
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }

        $id = trim($this->request->getPost('id'));
        $bankAccountId = trim($this->request->getPost('bank_account_id'));

        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);

        if (! is_numeric($id)) {
            return $this->failNotFound();
        }

        if (! has_permission('BankAccount', 'can_edit')) {
            return $this->respond(['status' => false, 'message' => 'Bạn không có quyền thực hiện thao tác này']);
        }

        $bankAccountDetails = $bankAccountModel->where(['id' => $bankAccountId, 'company_id' => $this->user_session['company_id']])->first();
        $bankSubAccountDetails = $bankSubAccountModel->where(['id' => $id])->first();

        if (!$bankAccountDetails || !$bankSubAccountDetails) {
            return $this->respond(['status' => false, 'message' => 'Không tìm thấy tài khoản ảo này']);
        }

        $vietinbankEnterpriseAccountModel = model(VietinbankEnterpriseAccountModel::class);
        $enterpriseAccountDetails = $vietinbankEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->first();

        if (! $enterpriseAccountDetails) {
            return $this->respond(['status' => false, 'message' => 'Chưa hỗ trợ sửa VA cho tài khoản ngân hàng này. Vui lòng liên hệ SePay để được hỗ trợ.']);
        }

        $rawData = [
            'sub_holder_name' => trim(xss_clean($this->request->getVar('sub_holder_name')))
        ];

        $vaNameMaxlength = $this->vietinbankConfig->enterpriseVaNamelength ?? 70;
        $subHolderNameRule = 'regex_match[/^[A-Za-z0-9\s]{1,' . ($vaNameMaxlength - strlen($enterpriseAccountDetails->prefix_received_name) - 1) . '}$/]';

        $rules = [
            'sub_holder_name' => ['label' => 'Tên hiển thị', 'rules' => ['required', $subHolderNameRule]],
        ];

        if (! $this->validateData($rawData, $rules)) {
            return $this->fail($this->validator->getErrors());
        }

        $rawData['sub_holder_name'] = $this->makeEnterpriseVaName($enterpriseAccountDetails, $rawData['sub_holder_name']);

        $updated = $bankSubAccountModel->update($bankSubAccountDetails->id, $rawData);

        if (!$updated) {
            return $this->respond([
                'status' => false,
                'message' => 'Đã có lỗi xảy ra, vui lòng thử lại sau hoặc liên hệ SePay để được hỗ trợ.'
            ]);
        }

        return $this->respond([
            'status' => true,
            'message' => 'Đã cập nhật VA'
        ]);
    }

    public function ajax_close_enterprise_va()
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }

        $id = trim($this->request->getPost('id'));
        $bankAccountId = trim($this->request->getPost('bank_account_id'));

        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);

        if (! is_numeric($id)) {
            return $this->failNotFound();
        }

        if (! has_permission('BankAccount', 'can_edit')) {
            return $this->respond(['status' => false, 'message' => 'Bạn không có quyền thực hiện thao tác này']);
        }

        $bankAccountDetails = $bankAccountModel->where(['id' => $bankAccountId, 'company_id' => $this->user_session['company_id']])->first();
        $bankSubAccountDetails = $bankSubAccountModel->where(['id' => $id])->first();

        if (!$bankAccountDetails || !$bankSubAccountDetails) {
            return $this->respond(['status' => false, 'message' => 'Không tìm thấy tài khoản ảo này']);
        }

        $vietinbankEnterpriseAccountModel = model(VietinbankEnterpriseAccountModel::class);
        $enterpriseAccountDetails = $vietinbankEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->first();

        if (! $enterpriseAccountDetails) {
            return $this->respond(['status' => false, 'message' => 'Chưa hỗ trợ đóng VA cho tài khoản ngân hàng này. Vui lòng liên hệ SePay để được hỗ trợ.']);
        }

        $updated = $bankSubAccountModel->update($bankSubAccountDetails->id, ['va_active' => 0]);

        if (!$updated) {
            return $this->respond([
                'status' => false,
                'message' => 'Đã có lỗi xảy ra, vui lòng thử lại sau hoặc liên hệ SePay để được hỗ trợ.'
            ]);
        }

        return $this->respond([
            'status' => true,
            'message' => 'Đã vô hiệu hoá VA'
        ]);
    }

    public function ajax_active_enterprise_va()
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }

        $id = trim($this->request->getPost('id'));
        $bankAccountId = trim($this->request->getPost('bank_account_id'));

        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);

        if (! is_numeric($id)) {
            return $this->failNotFound();
        }

        if (! has_permission('BankAccount', 'can_edit')) {
            return $this->respond(['status' => false, 'message' => 'Bạn không có quyền thực hiện thao tác này']);
        }

        $bankAccountDetails = $bankAccountModel->where(['id' => $bankAccountId, 'company_id' => $this->user_session['company_id']])->first();
        $bankSubAccountDetails = $bankSubAccountModel->where(['id' => $id])->first();

        if (!$bankAccountDetails || !$bankSubAccountDetails) {
            return $this->respond(['status' => false, 'message' => 'Không tìm thấy tài khoản ảo này']);
        }

        $vietinbankEnterpriseAccountModel = model(VietinbankEnterpriseAccountModel::class);
        $enterpriseAccountDetails = $vietinbankEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->first();

        if (! $enterpriseAccountDetails) {
            return $this->respond(['status' => false, 'message' => 'Chưa hỗ trợ kích hoạt VA cho tài khoản ngân hàng này. Vui lòng liên hệ SePay để được hỗ trợ.']);
        }

        $updated = $bankSubAccountModel->update($bankSubAccountDetails->id, ['va_active' => 1]);

        if (!$updated) {
            return $this->respond([
                'status' => false,
                'message' => 'Đã có lỗi xảy ra, vui lòng thử lại sau hoặc liên hệ SePay để được hỗ trợ.'
            ]);
        }

        return $this->respond([
            'status' => true,
            'message' => 'Đã vô hiệu hoá VA'
        ]);
    }

    public function ajax_delete_enterprise_va()
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }

        $id = trim($this->request->getPost('id'));
        $bankAccountId = trim($this->request->getPost('bank_account_id'));

        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);

        if (! is_numeric($id)) {
            return $this->failNotFound();
        }

        if (! has_permission('BankAccount', 'can_edit')) {
            return $this->respond(['status' => false, 'message' => 'Bạn không có quyền thực hiện thao tác này']);
        }

        $bankAccountDetails = $bankAccountModel->where(['id' => $bankAccountId, 'company_id' => $this->user_session['company_id']])->first();
        $bankSubAccountDetails = $bankSubAccountModel->where(['id' => $id])->first();

        if (!$bankAccountDetails || !$bankSubAccountDetails) {
            return $this->respond(['status' => false, 'message' => 'Không tìm thấy tài khoản ảo này']);
        }

        $vietinbankEnterpriseAccountModel = model(VietinbankEnterpriseAccountModel::class);
        $enterpriseAccountDetails = $vietinbankEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->first();

        if (! $enterpriseAccountDetails) {
            return $this->respond(['status' => false, 'message' => 'Chưa hỗ trợ kích hoạt VA cho tài khoản ngân hàng này. Vui lòng liên hệ SePay để được hỗ trợ.']);
        }

        $bankSubAccountModel->delete($id);
    
        return $this->respond(['status' => true]);
    }

    public function ajax_delete_enterprise_bank_account()
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }

        $allowedDeleteBankAccount = $this->vietinbankConfig->allowedDeleteBankAccount ?? true;

        if (!$allowedDeleteBankAccount) {
            return $this->respond(['status' => false, 'message' => 'Chưa hỗ trợ xóa tài khoản ngân hàng này. Vui lòng liên hệ SePay để được hỗ trợ.']);
        }

        if (! has_permission('BankAccount', 'can_delete')) {
            return $this->respond(['status' => false, 'message' => 'Bạn chưa được cấp quyền để thực hiện thao tác này']);
        }
        
        $bankAccountModel = model(BankAccountmodel::class);
        $bankAccountId = trim(xss_clean($this->request->getPost('id')));

        $bankAccountDetails = $bankAccountModel->where([
            'id' => $bankAccountId, 
            'company_id' => $this->user_session['company_id'],
            'bank_id' => Vietinbank::BANK_ID
        ])->first();
        
        if (!is_object($bankAccountDetails)) {
            return $this->respond(['status' => false, 'message' => 'Không tìm thấy tài khoản ngân hàng này']);
        }

        $vietinbankEnterpriseAccountModel = model(VietinbankEnterpriseAccountModel::class);
        $enterpriseAccountDetails = $vietinbankEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->first();

        if (!$enterpriseAccountDetails) {
            return $this->respond(['status' => false, 'message' => 'Chưa hỗ trợ xóa tài khoản ngân hàng này. Vui lòng liên hệ SePay để được hỗ trợ.']);
        }

        DeleteCompanyBankAccountAction::run($bankAccountDetails, $this->company_details, $this->request->getIPAddress(), self::class);
        $vietinbankEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->delete();
                    
        add_user_log(['data_id' => $bankAccountId, 'company_id' => $this->user_session['company_id'], 'data_type' => 'bank_account_delete', 'description' => 'Xóa tài khoản ngân hàng ' . $bankAccountDetails->account_number, 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);
        set_alert('success', 'Đã xóa tài khoản ngân hàng thành công.');

        return $this->respond(['status' => true]);
    }

    public function ajax_switch_enterprise_api_connection()
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }

        $allowedSwitchApiConnection = ($this->vietinbankConfig->allowedSwitchApiConnection ?? false) && ($this->vietinbankConfig->allowedEnterpriseConnection ?? false);

        if (!$allowedSwitchApiConnection) {
            return $this->failNotFound();
        }
 
        if (!has_permission('BankAccount', 'can_edit')) {
            return $this->respond(['status' => false, 'message' => 'Bạn chưa được cấp quyền để thực hiện thao tác này']);
        }

        $bankAccountId = trim(xss_clean($this->request->getPost('bank_account_id')));
        $bankAccountDetails = $this->getSwitchableApiConnectionBankAccount($bankAccountId);
        
        if (!is_object($bankAccountDetails))
            return $this->response->setJSON(['status' => false, 'message' => 'Tài khoản ngân hàng không đủ điều kiện']);

        $bankAccountModel = model(BankAccountModel::class);
        $vietinbankEnterpriseAccountModel = model(VietinbankEnterpriseAccountModel::class);
        $isEnterpriseAccountDetails = $vietinbankEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->first();

        if (!$isEnterpriseAccountDetails) {
            if (! $this->validate([
                'va_prefix' => [
                    'label' => 'đầu định danh',
                    'rules' => ['required', 'in_list[0,1,2,3,4,5,6,7,8,9]'],
                ],
                'prefix_id' => [
                    'label' => 'mã định danh',
                    'rules' => ['required', 'regex_match[/^[A-Z0-9]{3}$/]']
                ],
                'prefix_received_name' => [
                    'label' => 'tiền tố tên thụ hưởng',
                    'rules' => ['required', 'regex_match[/^[A-Za-z\s0-9]{1,70}$/]']
                ],
                'label' => [
                    'label' => 'tên gợi nhớ',
                    'rules' => ['permit_empty', 'max_length[100]']
                ]
            ])) return $this->fail($this->validator->getErrors());
    
            $bankAccountModel = model(BankAccountModel::class);
            $enterpriseAccountModel = model(VietinbankEnterpriseAccountModel::class);
            $vaPrefix = trim(xss_clean(strtoupper($this->request->getVar('va_prefix'))));
            $prefixReceivedName = trim(xss_clean(strtoupper($this->request->getVar('prefix_received_name'))));
            $prefixId = trim(xss_clean(strtoupper($this->request->getVar('prefix_id'))));
    
            if ($enterpriseAccountModel->where(['va_prefix' => $vaPrefix, 'prefix_id' => $prefixId])->countAllResults()) {
                return $this->respond(['status' => false, 'message' => 'Tiền tố định danh đã tồn tại, vui lòng chọn lại đầu định danh hoặc mã định danh khác.']);
            }
    
            $enterpriseAccountModel->insert([
                'bank_account_id' => $bankAccountId, 
                'prefix_id' => $prefixId, 
                'va_prefix' => $vaPrefix, 
                'prefix_received_name' => $prefixReceivedName
            ]);
        }

        $updated = $bankAccountModel
            ->set([
                'bank_api_connected' => 1, 
                'bank_api' => 1, 
                'bank_sms' => 0,
                'accumulated' => 0,
            ])
            ->where(['id' => $bankAccountDetails->id])->update();

        if ($updated) {
            add_user_log(['data_id' => $bankAccountDetails->id, 'company_id' => $this->user_session['company_id'], 'data_type' => 'bank_account_add', 'description' => 'Chuyển phương thức kết nối sang API', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);
            $redirectTo = base_url('vietinbank/details/' . $bankAccountDetails->id);

            return $this->respond(['status' => true, 'redirect_to' => $redirectTo]);
        }

        return $this->respond(['status' => false]);
    }

    protected function getSwitchableApiConnectionBankAccount($id)
    {
        return model(BankAccountModel::class)->where([
            'id' => $id, 
            'company_id' => $this->user_session['company_id'],
            'bank_id' => Vietinbank::BANK_ID,
            'bank_sms' => 1,
            'bank_api' => 0,
            'bank_api_connected' => 0,
            'active' => 1,
        ])->get()->getRow();
    }

    protected function getSwitchableSmsConnectionBankAccount($id)
    {
        return model(BankAccountModel::class)->where([
            'id' => $id, 
            'company_id' => $this->user_session['company_id'],
            'bank_id' => Vietinbank::BANK_ID,
            'bank_sms' => 0,
            'bank_api' => 1,
            'active' => 1,
        ])->get()->getRow();
    }

    protected function determineIfAbleSwitchToSmsConnection()
    {
        $subscriptionDetails = model(CompanySubscriptionModel::class)->where(['company_id' => $this->user_session['company_id']])->get()->getRow();
        $productDetails = model(ProductModel::class)->where(['id' => $subscriptionDetails->plan_id])->get()->getRow();

        return is_object($subscriptionDetails) && $productDetails->sms_allow == 1;        
    }

    protected function makeEnterpriseVac($bankAccountDetails, $enterpriseAccountDetails)
    {
        return $enterpriseAccountDetails->va_prefix . $enterpriseAccountDetails->prefix_id;
    }

    protected function makeEnterpriseVaName($enterpriseAccountDetails, $holderName)
    {
        return $enterpriseAccountDetails->prefix_received_name . ' ' . $holderName;
    }

    public function ajax_request_create_qr_shop($shopId = null)
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (! has_permission('BankAccount', 'can_create'))
            return $this->respond(['status ' => false, 'message' => 'Bạn không đủ quyền.']);
        
        $shopModel = model(ShopModel::class);
        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);

        $shopId = trim(xss_clean($shopId));

        if (!$shopId || !is_numeric($shopId)) return $this->failNotFound();

        $shopDetails = $shopModel->where(['id' => $shopId, 'company_id' => $this->company_details->id])->first();

        if (!$shopDetails) return $this->failNotFound();

        $data = [
            'account_number' => trim(xss_clean($this->request->getVar('account_number'))),
            'account_holder_name' => trim(remove_accents($this->request->getVar('account_holder_name'), TRUE)),
            'identification_number' => trim(xss_clean($this->request->getVar('identification_number'))),
            'phone_number' => trim(xss_clean($this->request->getVar('phone_number'))),
            'bank_account_id' => trim(xss_clean($this->request->getVar('bank_account_id'))),
            'label' => trim(xss_clean($this->request->getVar('label'))),
        ];

        $bankAccountDetails = $data['bank_account_id'] ? $bankAccountModel->where(['id' => $data['bank_account_id']])->first() : null;

        if ($bankAccountDetails) {
            $data['account_number'] = $bankAccountDetails->account_number;
            $data['account_holder_name'] = $bankAccountDetails->account_holder_name;
            $data['identification_number'] = $bankAccountDetails->identification_number;
            $data['phone_number'] = $bankAccountDetails->phone_number;
        }

        $rules = is_null($bankAccountDetails) ? [
            'account_number' => ['required', 'max_length[100]'],
            'account_holder_name' => ['required', 'max_length[100]'],
            'identification_number' => ['required', 'max_length[100]'],
            'phone_number' => ['required', 'max_length[20]'],
        ] : [];

        $rules['label'] = ['required', 'max_length[100]'];

        if (! $this->validateData($data, $rules)) {
            return $this->fail($this->validator->getErrors());
        }

        if (!$bankAccountDetails && $bankAccountModel->where(['account_number' => $data['account_number'], 'bank_id' => Vietinbank::BANK_ID])->countAllResults()) {
            return $this->fail(['account_number' => 'Số tài khoản ngân hàng này đã tồn tại trên hệ thống']);
        }

        do {
            $subAccount = '';
            $length = rand(2, 3);

            for ($i = 0; $i < $length; $i++) {
                $subAccount .= rand(0, 1) ? chr(rand(65, 90)) : rand(0, 9);
            }
        } while ($bankSubAccountModel->where(['sub_account' => $subAccount])->countAllResults() > 0);        

        if ($bankAccountDetails) {
            $accountHolderName = $bankAccountDetails->account_holder_name;
        } else {
            $accountHolderName = $data['account_holder_name'];
        }

        try {
            $client = new VietinbankClient;
        } catch (Exception $e) {
            return $this->handleVietinbankClientException($e);
        }

        try {
            $response = $client->alertRegisterOTP(
                '2', // personal type
                $accountHolderName,
                $data['account_number'],
                $data['identification_number'],
                $data['phone_number'],
            );

             $responseData = json_decode($response->getBody());

            //SIMULATE
            // $responseData = (object)[
            //     'requestId' => uniqid()
            // ];

            return $this->response->setJSON([
                'status' => true, 
                'message' => "OTP đã được gửi đi",
                'sub_account' => $subAccount,
                'request_id' => $responseData->requestId
            ]);

        } catch (Exception $e) {
            $errorCode = $e->getCode();

             if ($errorCode == '2')
                return $this->fail(['account_number' => 'Số tài khoản không tồn tại hoặc đã bị khóa trên hệ thống ngân hàng VietinBank.']);

            if ($errorCode == '4')
                return $this->fail(['account_holder_name' =>  'Thông tin tên chủ tài khoản không trùng khớp.']);
            
            if ($errorCode == '75')
                return $this->fail(['identification_number' =>  'Số CCCD/CMND không được đăng ký cho tài khoản trên.']);

            if ($errorCode == '76')
                return $this->fail(['phone_number' => 'Số điện thoại không được đăng ký cho tài khoản trên, vui lòng liên hệ SePay để được hỗ trợ.']);

            return $this->handleVietinbankClientException($e);
        }
    }

    public function ajax_confirm_create_qr_shop_with_new_bank_account($shopId = null)
    {
        if ($this->request->getMethod(true) != 'POST') {
            return '';
        }

        if (!has_permission('BankAccount', 'can_create')) {
            return $this->respond(['status ' => false, 'message' => 'Bạn không đủ quyền.']);
        }

        $shopModel = model(ShopModel::class);
        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $bankShopLinkModel = model(BankShopLinkModel::class);

        $shopId = trim(xss_clean($shopId));
        if (!$shopId || !is_numeric($shopId)) return $this->failNotFound();

        $shopDetails = $shopModel->where(['id' => $shopId, 'company_id' => $this->company_details->id])->first();
        if (!$shopDetails) return $this->failNotFound();

        $data = [
            'bank_account_id' => trim(xss_clean($this->request->getVar('bank_account_id'))),
            'account_number' => trim(xss_clean($this->request->getVar('account_number'))),
            'account_holder_name' => trim(remove_accents($this->request->getVar('account_holder_name'), TRUE)),
            'identification_number' => trim(xss_clean($this->request->getVar('identification_number'))),
            'phone_number' => trim(xss_clean($this->request->getVar('phone_number'))),
            'otp' => trim(xss_clean($this->request->getVar('otp'))),
            'request_id' => $this->request->getVar('request_id'),
            'sub_account' => trim(xss_clean($this->request->getVar('sub_account'))),
            'label' => trim(xss_clean($this->request->getVar('label')))
        ];

        $bankAccountDetails = $data['bank_account_id'] ? $bankAccountModel->where([
            'id' => $data['bank_account_id'],
            'company_id' => $this->user_session['company_id']
        ])->first() : null;

        if (!$bankAccountDetails) {
            $rules = [
                'account_number' => ['required', 'max_length[100]'],
                'account_holder_name' => ['required', 'max_length[100]'],
                'identification_number' => ['required', 'max_length[100]'],
                'phone_number' => ['required', 'max_length[20]'],
                'sub_account' => [
                    'rules' => 'required|regex_match[/^[A-Z0-9]{2,3}$/]',
                    'errors' => [
                        'required' => 'Đã có lỗi xảy ra, vui lòng tải lại trang',
                        'regex_match' => 'Đã có lỗi xảy ra, vui lòng tải lại trang',
                    ]
                ],
                'label' => ['required', 'max_length[100]'],
                'otp' => ['required', 'max_length[6]'],
                'request_id' => ['required'],
            ];

            if (!$this->validateData($data, $rules)) {
                return $this->fail($this->validator->getErrors());
            }
        } else {
            $rules = [
                'label' => ['required', 'max_length[100]'],
            ];

            if (!$this->validateData($data, $rules)) {
                return $this->fail($this->validator->getErrors());
            }
        }

        if (!$bankAccountDetails) {
            try {
                $client = new VietinbankClient;
                $response = $client->alertVerifyOTP($data['request_id'], $data['otp']);
            } catch (Exception $e) {
                $errorCode = $e->getCode();
                if (in_array($errorCode, ['3', '31', '33'])) {
                    return $this->fail(['otp' => 'OTP không chính xác hoặc đã hết hiệu lực.']);
                }
                return $this->handleVietinbankClientException($e);
            }

            // Create a new bank account
            $safeCreateBankAccountData = [
                'company_id' => $this->user_session['company_id'],
                'account_holder_name' => $data['account_holder_name'],
                'account_number' => $data['account_number'],
                'bank_id' => Vietinbank::BANK_ID,
                'active' => 1,
                'bank_api' => 1,
                'bank_api_connected' => 1,
                'identification_number' => $data['identification_number'],
                'phone_number' => $data['phone_number']
            ];

            $bankAccountId = $bankAccountModel->insert($safeCreateBankAccountData);
            if (!$bankAccountId) {
                log_message('error', '[VietinbankController->ajax_confirm_create_qr_shop_with_new_bank_account]: Create bank account failed - ' . json_encode($safeCreateBankAccountData));
                return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng thử lại sau.']);
            }
            $accountHolderName = $data['account_holder_name'];
        } else {
            $bankAccountId = $bankAccountDetails->id;
            $accountHolderName = $bankAccountDetails->account_holder_name;

            do {
                $subAccount = '';
                $length = rand(2, 3);
    
                for ($i = 0; $i < $length; $i++) {
                    $subAccount .= rand(0, 1) ? chr(rand(65, 90)) : rand(0, 9);
                }
            } while ($bankSubAccountModel->where(['sub_account' => $subAccount])->countAllResults() > 0);

            $data['sub_account'] = $subAccount;
        }


        $safeCreateBankSubAccountData = [
            'sub_account' => $data['sub_account'],
            'bank_account_id' => $bankAccountId,
            'sub_holder_name' => $accountHolderName,
            'label' => $data['label'],
            'acc_type' => 'Virtual',
        ];

        $bankSubAccountId = $bankSubAccountModel->insert($safeCreateBankSubAccountData);
        if (!$bankSubAccountId) {
            log_message('error', '[VietinbankController->ajax_confirm_create_qr_shop_with_new_bank_account]: Create sub bank account failed - ' . json_encode($safeCreateBankSubAccountData));
            return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng thử lại sau.']);
        }

        $safeBankShopLinkData = [
            'shop_id' => $shopDetails->id,
            'bank_account_id' => $bankAccountId,
            'bank_sub_account_id' => $bankSubAccountId,
        ];

        $linked = $bankShopLinkModel->insert($safeBankShopLinkData);
        if (!$linked) {
            log_message('error', '[VietinbankController->ajax_confirm_create_qr_shop_with_new_bank_account]: Link bank shop failed - ' . json_encode($safeBankShopLinkData));
            return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng thử lại sau.']);
        }

        $this->handleVaShop($shopDetails->id, $bankAccountId, $bankSubAccountId);

        return $this->respond([
            'status' => true,
            'va_id' => $bankSubAccountId,
            'message' => 'Tạo QR nhận thanh toán thành công',
        ]);
    }
    
    protected function handleVaShop($shopId, $bankAccountId, $bankSubAccountId)
    {
        $shopModel = model(ShopModel::class);

        $checkActive = $shopModel
        ->where([
            'id' => $shopId,
            'company_id' => $this->company_details->id,
            'active' => 0
            ])
        ->first();

        if($checkActive)
            $shopModel->update($shopId, ['active' => 1]);

        $noticationTelegramModel = model(NotificationTelegramModel::class);
        $telegrams = $noticationTelegramModel
        ->select([
            'tb_autopay_notification_telegram.*',
        ])
        ->join("tb_autopay_bank_shop_link", "tb_autopay_bank_shop_link.bank_account_id = tb_autopay_notification_telegram.bank_account_id AND tb_autopay_bank_shop_link.bank_sub_account_id = tb_autopay_notification_telegram.sub_account_id")
        ->join("tb_autopay_shop", "tb_autopay_shop.id = tb_autopay_bank_shop_link.shop_id")
        ->where("tb_autopay_bank_shop_link.shop_id", $shopId)
        ->where('tb_autopay_shop.company_id', $this->user_session['company_id'])
        ->groupBy('tb_autopay_notification_telegram.chat_id')->get()->getResult();

        if(!empty($telegrams)){
            foreach($telegrams as $t)
            {
                $data = [
                    'company_id' => $t->company_id ?? $this->user_session['company_id'],
                    'transaction_type' => $t->transaction_type,
                    'bank_account_id' => $bankAccountId,
                    'sub_account_id' => $bankSubAccountId,
                    'contains_content' => $t->contains_content ?? '',
                    'description' => $t->description ?? '',
                    'chat_id' => $t->chat_id,
                    'amount_in_less_than_equal_to' => $t->amount_in_less_than_equal_to ?? 0,
                    'amount_in_great_than_equal_to' => $t->amount_in_great_than_equal_to ?? 0,
                    'ignore_phrases' => $t->ignore_phrases ?? '',
                    'active' => $t->active ?? 1,
                    'message_thread_id' => $t->message_thread_id ?? 0,
                    'verify_payment' => $t->verify_payment ?? 'No',
                    'is_template_custom' => $t->is_template_custom ?? 0,
                    'template_custom' => $t->template_custom ?? '',
                    'template_name' => $t->template_name ?? 'template_1',
                    'hide_accumulated' => $t->hide_accumulated ?? 0,
                    'hide_details_link' => $t->hide_details_link ?? 0
                ];
                $noticationTelegramModel->insert($data);
            }
        }

        $noticationLarkMessengerModel = model(NotificationLarkMessengerModel::class);
        $larkmessenger = $noticationLarkMessengerModel
        ->select([
                'tb_autopay_notification_larkmessenger.*'
        ])
        ->join("tb_autopay_bank_shop_link", "tb_autopay_bank_shop_link.bank_account_id = tb_autopay_notification_larkmessenger.bank_account_id AND tb_autopay_bank_shop_link.bank_sub_account_id = tb_autopay_notification_larkmessenger.sub_account_id")
        ->join("tb_autopay_shop", "tb_autopay_shop.id = tb_autopay_bank_shop_link.shop_id")
        ->where("tb_autopay_bank_shop_link.shop_id", $shopId)
        ->where('tb_autopay_shop.company_id', $this->user_session['company_id'])
        ->groupBy('tb_autopay_notification_larkmessenger.bot_webhook_url')->get()->getResult();

        if(!empty($larkmessenger)){
            foreach($larkmessenger as $l)
            {
                $data = [
                    'company_id' => $l->company_id ?? $this->user_session['company_id'],
                    'transaction_type' => $l->transaction_type,
                    'bank_account_id' => $bankAccountId,
                    'sub_account_id' => $bankSubAccountId,
                    'contains_content' => $l->contains_content ?? '',
                    'description' => $l->description ?? '',
                    'bot_webhook_url' => $l->bot_webhook_url,
                    'amount_in_less_than_equal_to' => $l->amount_in_less_than_equal_to ?? 0,
                    'amount_in_great_than_equal_to' => $l->amount_in_great_than_equal_to ?? 0,
                    'ignore_phrases' => $l->ignore_phrases ?? '',
                    'active' => $l->active ?? 1,
                    'message_thread_id' => $l->message_thread_id ?? 0,
                    'verify_payment' => $l->verify_payment ?? 'No',
                    'is_template_custom' => $l->is_template_custom ?? 0,
                    'template_custom' => $l->template_custom ?? '',
                    'template_name' => $l->template_name ?? 'template_1',
                    'hide_accumulated' => $l->hide_accumulated ?? 0,
                    'hide_details_link' => $l->hide_details_link ?? 0
                ];
                $noticationLarkMessengerModel->insert($data);
            }
        }

        $userPermissionBankSubModel = model(UserPermissionBankSubModel::class);
        $userIdList = $userPermissionBankSubModel
        ->select([
                'tb_autopay_user_permission_bank_sub.user_id',
            ])
        ->join('tb_autopay_user', 'tb_autopay_user_permission_bank_sub.user_id = tb_autopay_user.id')
        ->join('tb_autopay_bank_shop_link', 'tb_autopay_bank_shop_link.bank_sub_account_id = tb_autopay_user_permission_bank_sub.sub_account_id AND tb_autopay_bank_shop_link.shop_id = ' . $shopId)
        ->join('tb_autopay_shop', 'tb_autopay_shop.id = tb_autopay_bank_shop_link.shop_id')
        ->where('tb_autopay_shop.company_id', $this->user_session['company_id'])
        ->where('tb_autopay_bank_shop_link.shop_id', $shopId)
        ->groupBy('tb_autopay_user_permission_bank_sub.user_id')->get()->getResult();

        if(!empty($userIdList)){
            foreach($userIdList as $u)
            {
                $userData = [
                    'permissions' => [
                        'hide_amount_out' => 1,
                        'hide_accumulated' => 1,
                        'hide_reference_number' => 1,
                        'hide_transaction_content' => 1,
                    ]
                ];

                $this->assignPosPermissionToUser(
                    $u->user_id, 
                    $this->user_session['company_id'], 
                    $bankAccountId, 
                    $bankSubAccountId, 
                    $userData['permissions'], 
                );
            }
        }
    }
    protected function assignPosPermissionToUser($userId, $companyId, $bankAccountId, $bankSubAccountId, $bankPermissions = [], $pushMobileTransactionNotificationFeature = 1)
    {
        $bankPermissions['hide_amount_out'] = isset($bankPermissions['hide_amount_out']) ? $bankPermissions['hide_amount_out'] : 0;
        $bankPermissions['hide_accumulated'] = isset($bankPermissions['hide_accumulated']) ? $bankPermissions['hide_accumulated'] : 0;
        $bankPermissions['hide_reference_number'] = isset($bankPermissions['hide_reference_number']) ? $bankPermissions['hide_reference_number'] : 0;
        $bankPermissions['hide_transaction_content'] = isset($bankPermissions['hide_transaction_content']) ? $bankPermissions['hide_transaction_content'] : 0;
        
        $userPermissionFeatureModel = model(UserPermissionFeatureModel::class);
        $userPermissionBankModel = model(UserPermissionBankModel::class);
        $userPermissionBankSubModel = model(UserPermissionBankSubModel::class);

        $transactionFeaturePermission = $userPermissionFeatureModel->where(['feature_slug' => 'Transactions', 'user_id' => $userId])->first();
        $pushMobileTransactionNotificationFeaturePermission = $userPermissionFeatureModel->where(['feature_slug' => 'PushMobileTransactionNotification', 'user_id' => $userId])->first();

        if (! $transactionFeaturePermission) {
            $userPermissionFeatureModel->insert([
                'user_id' => $userId,
                'company_id' => $companyId,
                'feature_slug' => 'Transactions',
                'can_view_all' => 1,
            ]);
        } else if ($transactionFeaturePermission && $transactionFeaturePermission->can_view_all == 0) {
            $userPermissionFeatureModel
                ->where(['id' => $transactionFeaturePermission->id])
                ->set(['can_view_all' => 1])
                ->update();
        }

        if ($pushMobileTransactionNotificationFeature && ! $pushMobileTransactionNotificationFeaturePermission) {
            $userPermissionFeatureModel->insert([
                'user_id' => $userId,
                'feature_slug' => 'PushMobileTransactionNotification',
                'can_view_all' => 1,
            ]);
        } else if ($pushMobileTransactionNotificationFeature && $pushMobileTransactionNotificationFeaturePermission && $pushMobileTransactionNotificationFeaturePermission->can_view_all == 0) {
            $userPermissionFeatureModel
                ->where(['id' => $pushMobileTransactionNotificationFeaturePermission->id])
                ->set(['can_view_all' => 1])
                ->update();
        } else if (!$pushMobileTransactionNotificationFeature && $pushMobileTransactionNotificationFeaturePermission) {
            $userPermissionFeatureModel
                ->where(['id' => $pushMobileTransactionNotificationFeaturePermission->id])
                ->set(['can_view_all' => 0])
                ->update();
        }

        $bankAccountPermission = $userPermissionBankModel->where(['user_id' => $userId, 'bank_account_id' => $bankAccountId])->first();
        $bankSubAccountPermission = $userPermissionBankSubModel->where(['user_id' => $userId, 'sub_account_id' => $bankSubAccountId])->first();

        if (! $bankAccountPermission) {
            $userPermissionBankModel->insert(array_merge($bankPermissions, [
                'user_id' => $userId,
                'bank_account_id' => $bankAccountId
            ]));
        } else if ($bankAccountPermission) {
            $userPermissionBankModel->where(['id' => $bankAccountPermission->id])
                ->set($bankPermissions)->update();
        }

        if (! $bankSubAccountPermission) {
            $userPermissionBankSubModel->insert([
                'user_id' => $userId,
                'sub_account_id' => $bankSubAccountId
            ]);
        }

        model(UserPermissionFeatureModel::class)->set(['can_view_all' => 1])
            ->where(['user_id' => $userId, 'company_id' => $companyId])
            ->whereIn('feature_slug', ['Transactions', 'BankAccount'])
            ->update();

        return true;
    }
}
