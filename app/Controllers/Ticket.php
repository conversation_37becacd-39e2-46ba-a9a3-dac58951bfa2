<?php

namespace App\Controllers;
use App\Models\UserLogModel;
use App\Models\UserModel;
use App\Models\TicketModel;
use App\Models\TicketReplyModel;

use CodeIgniter\Controller;

class Ticket extends BaseController
{
    public function index()
    { 
        $data = [
            'page_title' => 'Hỗ trợ',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        
        if(!in_array($this->company_details->role,['Admin', 'SuperAdmin']))
            show_404();
      
        echo theme_view('templates/autopay/header',$data);
        echo view('ticket/index',$data);
        echo theme_view('templates/autopay/footer',$data);

    }

    public function ajax_ticket_list() {

        if(!in_array($this->company_details->role,['Admin', 'SuperAdmin']))
            show_404();
     
        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        $ticketModel = model(TicketModel::class);

        $tickets = $ticketModel->getDatatables($this->user_session['company_id']);
         
        $data = array();

        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');

        foreach ($tickets as $ticket) {
         
            $no++;
            $row = array();

            $user_read_class = "";
            if($ticket->user_read == 0)
                $user_read_class = "fw-bold";
            
            $row[] = $no;
            $row[] = "<a class='".$user_read_class."' href='" . base_url('ticket/details/' . esc($ticket->id)) . "'>" . esc($ticket->subject) . "</a>";
                 
          
            $row[] = get_ticket_status_badge($ticket->status);  
            
            $row[] =  timespan(esc($ticket->created_at),1);
            $data[] = $row;
        }
 
        $output = array(
            "draw" => $draw,
            "recordsTotal" => $ticketModel->countAll($this->user_session['company_id']),
            "recordsFiltered" => $ticketModel->countFiltered($this->user_session['company_id']),
            "data" => $data,
        );
        return $this->response->setJSON($output);
    
    
    }

    public function details($ticket_id='') {
        $data = [
            'page_title' => 'Hỗ trợ',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        if(!in_array($this->company_details->role,['Admin', 'SuperAdmin']))
            show_404();

        if(!is_numeric($ticket_id))
            show_404();

        $ticketModel = model(ticketModel::class);
        $ticketReplyModel = model(TicketReplyModel::class);

        $data['ticket_details'] = $ticketModel->where(['id' => $ticket_id ,'company_id' =>$this->user_session['company_id']])->get()->getRow();

        if(!is_object($data['ticket_details']))
            show_404();

        $data['ticket_replies'] = $ticketReplyModel->where(['ticket_id' => $ticket_id])->orderBy('id','DESC')->get()->getResult();

        $ticketModel->set(['user_read' => 1])->where(['id' => $ticket_id ,'company_id' =>$this->user_session['company_id']])->update();

        
        echo theme_view('templates/autopay/header',$data);
        echo view('ticket/details',$data);
        echo theme_view('templates/autopay/footer',$data);

  
    } 
 

    public function create() {
        $data = [
            'page_title' => 'Hỗ trợ',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        if(!in_array($this->company_details->role,['Admin', 'SuperAdmin']))
            show_404();
 
        $ticketModel = model(ticketModel::class);
  
        echo theme_view('templates/autopay/header',$data);
        echo view('ticket/create');
        echo theme_view('templates/autopay/footer',$data);

  
    } 

    public function ajax_create() {
        $validation =  \Config\Services::validation();

        if(!in_array($this->company_details->role,['Admin', 'SuperAdmin']))
            show_404();

        if(! $this->validate([
            'subject' => ['label' => 'Tiêu đề', "rules" => "required|string|max_length[2000]"],
            'body' => ['label' => 'Nội dung yêu cầu', "rules" => "required|string"],
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        } 

        $subject = $this->request->getVar('subject');
        $body = $this->request->getVar('body');

        $ticketModel = model(TicketModel::class);

        $ticket_id = $ticketModel->insert([
            'company_id' => $this->user_session['company_id'], 
            'subject' => $subject, 
            'body' => $body, 
            'status' => 'Open', 
            'name' => $this->user_details->lastname . ' ' . $this->user_details->firstname, 
            'email' => $this->user_details->email, 
            'owner_type' => 'User', 
            'owner_id' => $this->user_details->id, 
            'lastreply' => date("Y-m-d H:i:s"), 
            'user_read' => 1
        ]);

        // Xử lý upload ảnh
        $this->handleImageUploads($ticket_id);

        add_user_log(array('data_id'=>$ticket_id,'company_id' => $this->user_session['company_id'], 'data_type'=>'ticket_add','description'=>'Tạo ticket mới','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));

        set_alert("success", "Đã gửi yêu cầu hỗ trợ");


        return $this->response->setJSON(array('status'=>TRUE,'id'=>$ticket_id));

    }

    private function handleImageUploads($ticketId, $replyId = null) {
        $images = $this->request->getFiles();
        
        if (empty($images) || !isset($images['images'])) {
            log_message('error', '[S3 Upload #' . $ticketId . '] No images found in request. Files received: ' . json_encode(array_keys($images)));
            return;
        }
        
        $s3Config = config('AwsS3');
        $s3Service = new \App\Libraries\AwsS3Service();
        $ticketAttachmentModel = model(\App\Models\TicketAttachmentModel::class);
        
        $imageFiles = $images['images'];

        if (count($imageFiles) > $s3Config->maxFiles) {
            log_message('error', '[S3 Upload #' . $ticketId . '] Upload failed: Too many files (' . count($imageFiles) . '). Maximum allowed: ' . $s3Config->maxFiles);
            return;
        }

        foreach ($imageFiles as $file) {
            $fileName = basename($file->getClientName());

            if ($file->getError() > 0) {
                log_message('error', '[S3 Upload #' . $ticketId . '] Upload failed for file "' . $fileName . '": PHP upload error code ' . $file->getError());
                continue;
            }

            if ($file->getSize() > $s3Config->maxFileSize) {
                log_message('error', '[S3 Upload #' . $ticketId . '] Upload failed for file "' . $fileName . '": File size ' . $file->getSize() . ' bytes exceeds maximum allowed size of ' . $s3Config->maxFileSize . ' bytes');
                continue;
            }
            
            $fileInfo = new \finfo(FILEINFO_MIME_TYPE);
            $actualMime = $fileInfo->file($file->getTempName());

            if (!in_array($actualMime, $s3Config->allowedTypes)) {
                log_message('error', '[S3 Upload #' . $ticketId . '] Upload failed for file "' . $fileName . '": Invalid MIME type "' . $actualMime . '". Allowed types: ' . implode(', ', $s3Config->allowedTypes));
                continue;
            }
            
            if (in_array($actualMime, $s3Config->allowedTypes)) {
                list($width, $height) = getimagesize($file->getTempName());
                if ($width <= 0 || $height <= 0) {
                    log_message('error', '[S3 Upload #' . $ticketId . '] Upload failed for file "' . $fileName . '": Invalid image dimensions (' . $width . 'x' . $height . ')');
                    continue;
                }

                try {
                    $image = imagecreatefromstring(file_get_contents($file->getTempName()));
                    if ($image === false) {
                        log_message('error', '[S3 Upload #' . $ticketId . '] Upload failed for file "' . $fileName . '": Failed to create image from file contents');
                        continue;
                    }
                    imagedestroy($image);
                } catch (\Exception $e) {
                    log_message('error', '[S3 Upload #' . $ticketId . '] Upload failed for file "' . $fileName . '": ' . $e->getMessage());
                    continue;
                }
            }
            
            $newName = bin2hex(random_bytes(16));
            
            $s3Key = $s3Config->ticketAttachmentPrefix . '/' . 
                     date('Y-m-d') . '/' . 
                     $this->user_session['company_id'] . '/' . 
                     $ticketId . '/' . 
                     $newName;
            
            try {
                $fileUrl = $s3Service->upload(
                    $file->getTempName(),
                    $s3Key,
                    false,
                    $actualMime
                );

                $safeFileName = $this->sanitizeFileName($fileName);

                $ticketAttachmentModel->insert([
                    'ticket_id' => $ticketId,
                    'reply_id' => $replyId,
                    'file_name'   => $safeFileName,
                    'file_path' => $fileUrl,
                    'file_type' => $actualMime,
                    'file_size' => $file->getSize(),
                    'company_id' => $this->user_session['company_id'],
                    'created_by' => $this->user_details->id,
                    's3_key' => $s3Key
                ]);

                if (!unlink($file->getTempName())) {
                    log_message('error', '[S3 Upload #' . $ticketId . '] Warning: Failed to delete temp file: ' . $file->getTempName());
                }

            } catch (\Exception $e) {
                log_message('error', '[S3 Upload #' . $ticketId . '] S3 upload failed for file "' . $fileName . '": ' . $e->getMessage() . "\nStack trace: " . $e->getTraceAsString());
            }
        }
    }
    
    private function sanitizeFileName($fileName) {
        return preg_replace('/[^a-zA-Z0-9-_\.]/', '', basename($fileName));
    }

    public function ajax_reply() {
        $validation =  \Config\Services::validation();

        if(!in_array($this->company_details->role,['Admin', 'SuperAdmin']))
            show_404();

        if(! $this->validate([
            'ticket_id' => ['label' => 'Ticket ID', "rules" => "required|integer|is_natural"],
            'body' => ['label' => 'Nội dung phản hồi', "rules" => "required"],
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        } 

        $ticketModel = model(TicketModel::class);
        $ticketReplyModel = model(TicketReplyModel::class);

        $ticket_id = $this->request->getVar('ticket_id');

        $ticket_details = $ticketModel->where(['id' => $ticket_id, 'company_id' => $this->user_session['company_id']])->get()->getRow();

        if(!is_object($ticket_details))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>'Không tìm thấy ticket này'));


        $body = $this->request->getVar('body');


        $reply_id = $ticketReplyModel->insert(['ticket_id' => $ticket_id,'company_id' => $this->user_session['company_id'], 'body' => $body, 'name' => $this->user_details->lastname . ' ' . $this->user_details->firstname, 'email' => $this->user_details->email,  'owner_type' => 'User', 'owner_id' => $this->user_details->id]);

        $this->handleImageUploads($ticket_id, $reply_id);

        $ticketModel->set(['status' => 'ClientReply', 'lastreply' => date("Y-m-d H:i:s")])->where(['id' => $ticket_id, 'company_id' => $this->user_session['company_id']])->update();

        add_user_log(array('data_id'=>$ticket_id,'company_id' => $this->user_session['company_id'], 'data_type'=>'ticket_reply','description'=>'Trả lời ticket','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));

        set_alert("success", "Đã gửi trả lời");

        return $this->response->setJSON(array('status'=>TRUE,'id'=>$ticket_id));

    }


    public function ajax_set_status() {
        $validation =  \Config\Services::validation();

        if(!in_array($this->company_details->role,['Admin', 'SuperAdmin']))
            show_404();

        if(! $this->validate([
            'ticket_id' => ['label' => 'Ticket ID', "rules" => "required|integer|is_natural"],
            'status' => ['label' => 'Trạng thái', "rules" => "required|in_list[Open,Closed]"],
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        } 

        $ticketModel = model(TicketModel::class);

        $ticket_id = $this->request->getVar('ticket_id');
        $ticket_status = $this->request->getVar('status');

        $ticket_details = $ticketModel->where(['id' => $ticket_id, 'company_id' => $this->user_session['company_id']])->get()->getRow();

        if(!is_object($ticket_details))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>'Không tìm thấy ticket này'));

            
        $ticketModel->set(['status' => $ticket_status])->where(['id' => $ticket_id, 'company_id' => $this->user_session['company_id']])->update();

        add_user_log(array('data_id'=>$ticket_id,'company_id' => $this->user_session['company_id'], 'data_type'=>'ticket_change_status','description'=>'Thay đổi trạng thái yêu cầu hỗ trợ sang ' . $ticket_status,'user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));

        set_alert("success", "Đã thay đổi trạng thái yêu cầu hỗ trợ");

        return $this->response->setJSON(array('status'=>TRUE));

    }

 
    
}