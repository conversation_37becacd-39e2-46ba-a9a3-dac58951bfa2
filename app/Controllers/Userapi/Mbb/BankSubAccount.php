<?php

namespace App\Controllers\Userapi\Mbb;

use Exception;
use App\Libraries\MbbMmsClient;
use App\Models\BankAccountModel;
use CodeIgniter\API\ResponseTrait;
use App\Controllers\BaseController;
use App\Models\MbbMmsTerminalModel;
use App\Models\BankSubAccountMetadataModel;

class BankSubAccount extends BaseController
{
    use ResponseTrait;

    const BANK_ID = 8;

    protected function handleMbbMmsClientException($e)
    {
        log_message('error', 'MBB MMS Client: ' . $e->getMessage() . ' - ' . $e->getTraceAsString());

        return $this->respond([
            'status' => 500,
            'error' => 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ',
            'messages' => [
                'success' => false,
            ],
        ]);
    }

    public function index()
    {
        return $this->failNotFound('Resource not found');

        $bankAccountId = trim($this->request->getGet('bank_account_id'));

        if (! is_numeric($bankAccountId) || $bankAccountId < 0) {
            return $this->failNotFound('Resource not found');
        }

        $bankSubAccountModel = model(BankSubAccountModel::class);
        $bankAccountModel = model(BankAccountModel::class);

        $bankAccountDetails = $bankAccountModel->where(['id' => $bankAccountId, 'bank_id' => self::BANK_ID, 'company_id' => $this->request->company_id])->first();

        if (! $bankAccountDetails) {
            return $this->failNotFound('Resource not found');
        }

        $bankSubAccountList = $bankSubAccountModel
            ->join('tb_autopay_bank_sub_account_metadata', 'tb_autopay_bank_sub_account_metadata.bank_sub_account_id = tb_autopay_bank_sub_account.id')
            ->select(['tb_autopay_bank_sub_account.id', 'tb_autopay_bank_sub_account.sub_account', 'tb_autopay_bank_sub_account.sub_holder_name', 'tb_autopay_bank_sub_account.content', 'tb_autopay_bank_sub_account_metadata.bill_id', 'tb_autopay_bank_sub_account_metadata.amount', 'tb_autopay_bank_sub_account_metadata.expires_at', 'tb_autopay_bank_sub_account_metadata.paid_amount', 'tb_autopay_bank_sub_account.label', 'tb_autopay_bank_sub_account.active', 'tb_autopay_bank_sub_account.created_at', 'tb_autopay_bank_sub_account.updated_at'])
            ->where(['tb_autopay_bank_sub_account.bank_account_id' => $bankAccountId, 'acc_type' => 'Real', 'va_type' => 'Dynamic'])
            ->get()->getResult();

        return $this->respond([
            'status' => 200,
            'error' => null,
            'messages' => [
                'success' => true,
            ],
            'data' => $bankSubAccountList
        ]);
    }

    public function createDynamicVa()
    {
        return $this->failNotFound('Resource not found');

        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound('Resource not found');
        }

        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $mbbMmsTerminalModel = model(MbbMmsTerminalModel::class);
        $bankSubAccountMetadataModel = model(BankSubAccountMetadataModel::class);
        
        $data = [
            'bank_account_id' => trim(xss_clean($this->request->getVar('bank_account_id'))),
            'bill_id' => trim(xss_clean($this->request->getVar('bill_id'))),
            'content' => trim($this->request->getVar('content')),
            'amount' => trim($this->request->getVar('amount')),
            'duration' => trim($this->request->getVar('duration')),
        ];

        $rules = [
            'bank_account_id' => ['required', 'is_natural'],
            'bill_id' => ['required', 'string', 'regex_match[/^[0-9A-Za-z]+$/]', 'max_length[20]'],
            'content' => ['permit_empty', 'string', 'max_length[25]', 'regex_match[/^[0-9A-Za-z\s\-]+$/]'],
            'amount' => ['required', 'is_natural', 'greater_than[0]', 'max_length[13]'],
            'duration' => ['permit_empty', 'is_natural', 'greater_than[0]', 'max_length[5]']
        ];

        if (! $this->validateData($data, $rules)) {
            return $this->fail($this->validator->getErrors());
        }

        $bankAccountDetails = $bankAccountModel
            ->where([
                'id' => $data['bank_account_id'], 
                'bank_id' => self::BANK_ID, 
                'company_id' => $this->request->company_id, 
                'active' => 1
            ])
            ->first();

        if (! $bankAccountDetails) {
            return $this->failNotFound('Resource not found');
        }

        $mbbMmsTerminalDetails = $mbbMmsTerminalModel->where(['bank_account_id' => $bankAccountDetails->id])->first();
        
        if (! $mbbMmsTerminalDetails) {
            return $this->failNotFound('Resource not found');
        }

        try {
            $client = new MbbMmsClient;
            $client->setMerchantById($mbbMmsTerminalDetails->merchant_id);
        } catch (Exception $e) {
            return $this->handleMbbMmsClientException($e);  
        }

        try {
            $response = $client->createQrCode(
                $mbbMmsTerminalDetails->terminal_id,
                $qrcodeType = 4,
                $partnerType = 2,
                $initMethod = 12,
                $data['amount'],
                $billNumber = null,
                $additionalAddress = 0,
                $additionalMobile = 0,
                $additionalEmail = 0,
                $referenceLabelTime = date('YmdHis'),
                $data['bill_id'],
                $data['content'],
                $data['duration'],
                $addInfo = null
            );

            $responseData = json_decode($response->getBody());

            $vaNumber = $responseData->data->accountQR;
            $qrCode = $responseData->data->qrcode;

            $bankSubAccountId = $bankSubAccountModel->insert([
                'bank_account_id' => $bankAccountDetails->id,
                'sub_holder_name' => $bankAccountDetails->account_holder_name,
                'sub_account' => $vaNumber,
                'acc_type' => 'Real',
                'va_type' => 'Dynamic',
                'content' => $data['content'],
            ]);

            if (!$bankSubAccountId) {
                $client->deleteQrCode($qrCode);

                return $this->respond([
                    'status' => 503,
                    'error' => 'Đã có lỗi xảy ra, vui lòng thử lại',
                    'messages' => [
                        'success' => false,
                    ],
                ]);
            }

            $bankSubAccountMetadataModel->insert([
                'bank_account_id' => $bankAccountDetails->id,
                'bank_sub_account_id' => $bankSubAccountId,
                'bill_id' => $data['bill_id'],
                'amount' => $data['amount'],
                'qrcode' => $qrCode,
                'expires_at' => $data['duration'] ? date('Y-m-d H:i:s', strtotime('+' . $data['duration'] . ' minutes')) : null,
            ]);

            return $this->respond([
                'status' => 200,
                'error' => null,
                'messages' => [
                    'success' => true,
                ],
                'data' => [
                    'va_number' => $vaNumber,
                ],
            ]);
        } catch (Exception $e) {
            $errorCode = $e->getCode();

            if ($errorCode == '404') {
                return $this->fail(['bill_id' => 'Mã hóa đơn đã được sử dụng']);
            }

            return $this->handleMbbMmsClientException($e);  
        }
    }

    public function revokeDynamicVa($bankSubAccountId = '')
    {
        return $this->failNotFound('Resource not found');

        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound('Resource not found');
        }

        if (! is_numeric($bankSubAccountId) || $bankSubAccountId < 0) {
            return $this->failNotFound('Resource not found');
        }

        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $mbbMmsTerminalModel = model(MbbMmsTerminalModel::class);
        
        $bankSubAccountDetails = $bankSubAccountModel
            ->select(['tb_autopay_bank_sub_account.id', 'tb_autopay_bank_sub_account_metadata.qrcode', 'tb_autopay_bank_sub_account.bank_account_id'])
            ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id = tb_autopay_bank_sub_account.bank_account_id')
            ->join('tb_autopay_bank_sub_account_metadata', 'tb_autopay_bank_sub_account_metadata.bank_sub_account_id = tb_autopay_bank_sub_account.id', 'left')
            ->where([
                'tb_autopay_bank_sub_account.id' => $bankSubAccountId,
                'tb_autopay_bank_sub_account.acc_type' => 'Real',
                'tb_autopay_bank_sub_account.va_type' => 'Dynamic',
                'tb_autopay_bank_sub_account.va_active' => 1,
                'tb_autopay_bank_sub_account.active' => 1,
                'tb_autopay_bank_sub_account_metadata.bill_id != ' => null,
                'tb_autopay_bank_sub_account_metadata.amount != ' => null,
                'tb_autopay_bank_sub_account_metadata.qrcode != ' => null,
                'tb_autopay_bank_account.company_id' => $this->request->company_id,
                'tb_autopay_bank_account.bank_id' => self::BANK_ID,
            ])->first();

        if (! $bankSubAccountDetails) {
            return $this->failNotFound('Resource not found');
        }

        $mbbMmsTerminalDetails = $mbbMmsTerminalModel->where(['bank_account_id' => $bankSubAccountDetails->bank_account_id])->first();
        
        if (! $mbbMmsTerminalDetails) {
            return $this->failNotFound('Resource not found');
        }
        
        try {
            $client = new MbbMmsClient;
            $client->setMerchantById($mbbMmsTerminalDetails->merchant_id);
        } catch (Exception $e) {
            return $this->handleMbbMmsClientException($e);  
        }

        try {
            $response = $client->deleteQrCode($bankSubAccountDetails->qrcode);

            $responseData = json_decode($response->getBody());

            $isSuccess = $responseData->data->isSuccess;

            if (!$isSuccess) {
                return $this->respond([
                    'status' => 503,
                    'error' => 'Đã có lỗi xảy ra, vui lòng thử lại',
                    'messages' => [
                        'success' => false,
                    ],
                ]);
            }
            
            $bankSubAccountModel->where('id', $bankSubAccountDetails->id)->set(['va_active' => 0, 'active' => 0])->update();

            return $this->respond([
                'status' => 200,
                'error' => null,
                'messages' => [
                    'success' => true,
                ],
            ]);
        } catch (Exception $e) {
            $errorCode = $e->getCode();

            if ($errorCode == '4320') {
                
                return $this->failNotFound('Resource not found');
            }

            return $this->handleMbbMmsClientException($e);  
        }
    }
}
