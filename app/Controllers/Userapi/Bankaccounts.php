<?php

namespace App\Controllers\Userapi;
use App\Models\BankAccountModel;

use CodeIgniter\RESTful\ResourceController;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\Controller;
use Config\Services;

class Bankaccounts extends ResourceController
{
    use ResponseTrait;

    private function _checkAuthen() {

        $throttler = \Config\Services::throttler();

        $capacity = 2;

        $token_left = $throttler->check(md5($this->request->getIPAddress()), $capacity, SECOND);

        if ($token_left === false) {
            $time_left = $throttler->getTokentime();
            return Services::response()->setHeader('X-SePay-UserApi-Retry-After', $time_left)->setStatusCode(429);
        }
        $authen_header = $this->request->getServer('HTTP_AUTHORIZATION');

        $arr1 = explode(" ", $authen_header);

        if(count($arr1) == 2 && $arr1[0] == "Bearer" && strlen($arr1[1]) == 64) {
            $api_key = $arr1[1];
            if(!ctype_alnum($api_key))
                return $this->failUnauthorized("Invalid Auth token");

            // check api key map with company id. return company id or false
            $companyApiModel = model(CompanyApiModel::class);

            $result = $companyApiModel->select("tb_autopay_company_api.company_id")->join("tb_autopay_company","tb_autopay_company.id=tb_autopay_company_api.company_id")->where(['tb_autopay_company_api.api_key' => $api_key,'tb_autopay_company_api.active' => 1,'tb_autopay_company.active' => 1,'tb_autopay_company.status'=>'Active'])->get()->getRow();

            if(is_object($result) && is_numeric($result->company_id))
                return $result->company_id;
            else
                return $this->failUnauthorized("Unauthorized Access");
        } else {
            return $this->failUnauthorized("Invalid Auth token");
        }
        

    }
 
    public function details($id='') {

        $request = \Config\Services::request();

        $client_ip = $request->getIPAddress();

        //if(!in_array($client_ip, ['**************', '**************']))
        //    return $this->failNotFound("Resource Not Found");
        
        if ($this->request->getMethod(true) != 'GET')
            return $this->failNotFound("Resource Not Found");

        if(!is_numeric($id) || $id <= 0)
            return $this->failNotFound("Resource Not Found");

        $company_id = $this->_checkAuthen();

        if(!is_numeric($company_id))
            return '';

        $bankAccountModel = model(BankAccountModel::class);
        // check id
        $bank_account_details = $bankAccountModel->select("tb_autopay_bank_account.id,tb_autopay_bank_account.company_id,tb_autopay_bank_account.bank_id,tb_autopay_bank_account.sim_id,tb_autopay_bank_account.account_holder_name,tb_autopay_bank_account.account_number,tb_autopay_bank_account.accumulated,tb_autopay_bank_account.last_transaction,tb_autopay_bank_account.label,tb_autopay_bank_account.active,tb_autopay_bank_account.created_at, tb_autopay_bank.short_name, tb_autopay_bank.full_name, tb_autopay_bank.bin, tb_autopay_bank.code")->join("tb_autopay_bank","tb_autopay_bank_account.bank_id=tb_autopay_bank.id")->where(['tb_autopay_bank_account.company_id' => $company_id, 'tb_autopay_bank_account.id' => $id, 'tb_autopay_bank_account.deleted_at' => NULL])->get()->getRow();
        

        if(is_object($bank_account_details)) {
            $response = [
                'status'   => 200,
                'error'    => null,
                'messages' => [
                    'success' => true,
                ],
                'bankaccount' => [
                    'id' => $bank_account_details->id,
                    'account_holder_name' => $bank_account_details->account_holder_name,
                    'account_number' => $bank_account_details->account_number,
                    'accumulated' => $bank_account_details->accumulated,
                    'last_transaction' => $bank_account_details->last_transaction,
                    'label' => $bank_account_details->label,
                    'active' => $bank_account_details->active,
                    'created_at' => $bank_account_details->created_at,
                    'bank_short_name' => $bank_account_details->short_name,
                    'bank_full_name' => $bank_account_details->full_name,
                    'bank_bin' => $bank_account_details->bin,
                    'bank_code' => $bank_account_details->code,
                ]
            ];
            return $this->respond($response, 200);

        } else {
            return $this->failNotFound("This bank account was not found");
        }
    }

    public function list() {

        $request = \Config\Services::request();

        $client_ip = $request->getIPAddress();

        //if(!in_array($client_ip, ['**************', '**************']))
        //    return $this->failNotFound("Resource Not Found");
        
        if ($this->request->getMethod(true) != 'GET')
            return $this->failNotFound("Resource Not Found");
 
        $company_id = $this->_checkAuthen();

        if(!is_numeric($company_id))
            return '';

        $short_name = $this->request->getGet('short_name');
        $last_transaction_date_min = $this->request->getGet('last_transaction_date_min');
        $last_transaction_date_max = $this->request->getGet('last_transaction_date_max');
        $since_id = $this->request->getGet('since_id');
        $limit = $this->request->getGet('limit');
        $accumulated_min = $this->request->getGet('accumulated_min');
        $accumulated_max = $this->request->getGet('accumulated_max');


        $where = [
            'tb_autopay_bank_account.company_id' => $company_id,
            'tb_autopay_bank_account.deleted_at' => NULL
        ];

        if($short_name)
            $where['tb_autopay_bank.short_name'] = $short_name;
            
        if($last_transaction_date_min && strtotime($last_transaction_date_min))
            $where['tb_autopay_bank_account.last_transaction>='] = $last_transaction_date_min;

        if($last_transaction_date_max && strtotime($last_transaction_date_max))
            $where['tb_autopay_bank_account.last_transaction<='] = $last_transaction_date_max;
        
        if(is_numeric($since_id))
            $where['tb_autopay_bank_account.id>='] = $since_id;
        
        if(is_numeric($accumulated_min) && $accumulated_min>=0)
            $where['tb_autopay_bank_account.accumulated>='] = $accumulated_min;
           
        if(is_numeric($accumulated_max) && $accumulated_max >=0)
            $where['tb_autopay_sms_parsed.amount_out<='] = $accumulated_max;
        
        if(!is_numeric($limit))
            $limit = 100;
        
        $bankAccountModel = model(BankAccountModel::class);
        
        $bank_accounts = $bankAccountModel->select("tb_autopay_bank_account.id,tb_autopay_bank_account.account_holder_name,tb_autopay_bank_account.account_number,tb_autopay_bank_account.accumulated,tb_autopay_bank_account.last_transaction,tb_autopay_bank_account.label,tb_autopay_bank_account.active,tb_autopay_bank_account.created_at, tb_autopay_bank.short_name as `bank_short_name`, tb_autopay_bank.full_name as `bank_full_name`, tb_autopay_bank.bin as `bank_bin`, tb_autopay_bank.code as `bank_code`")->join("tb_autopay_bank","tb_autopay_bank_account.bank_id=tb_autopay_bank.id")->where($where)->orderBy('tb_autopay_bank_account.id','DESC')->get()->getResult();
       


        $response = [
            'status'   => 200,
            'error'    => null,
            'messages' => [
                'success' => true,
            ],
            'bankaccounts' => $bank_accounts
        ];
        return $this->respond($response, 200);
       
    }


    public function count() {

        $request = \Config\Services::request();

        $client_ip = $request->getIPAddress();

        //if(!in_array($client_ip, ['**************', '**************']))
        //    return $this->failNotFound("Resource Not Found");
        
        if ($this->request->getMethod(true) != 'GET')
            return $this->failNotFound("Resource Not Found");
 
        $company_id = $this->_checkAuthen();

        if(!is_numeric($company_id))
            return '';

        $short_name = $this->request->getGet('short_name');
        $last_transaction_date_min = $this->request->getGet('last_transaction_date_min');
        $last_transaction_date_max = $this->request->getGet('last_transaction_date_max');
        $since_id = $this->request->getGet('since_id');
        $accumulated_min = $this->request->getGet('accumulated_min');
        $accumulated_max = $this->request->getGet('accumulated_max');


        $where = [
            'tb_autopay_bank_account.company_id' => $company_id,
            'tb_autopay_bank_account.deleted_at' => NULL
        ];

        if($short_name)
            $where['tb_autopay_bank.short_name'] = $short_name;
            
        if($last_transaction_date_min && strtotime($last_transaction_date_min))
            $where['tb_autopay_bank_account.last_transaction>='] = $last_transaction_date_min;

        if($last_transaction_date_max && strtotime($last_transaction_date_max))
            $where['tb_autopay_bank_account.last_transaction<='] = $last_transaction_date_max;
        
        if(is_numeric($since_id))
            $where['tb_autopay_bank_account.id>='] = $since_id;
        
        if(is_numeric($accumulated_min) && $accumulated_min>=0)
            $where['tb_autopay_bank_account.accumulated>='] = $accumulated_min;
            
        if(is_numeric($accumulated_max) && $accumulated_max >=0)
            $where['tb_autopay_bank_account.accumulated<='] = $accumulated_max;
        
       
       
        $bankAccountModel = model(BankAccountModel::class);
    
        $count_bank_accounts = $bankAccountModel->select("tb_autopay_bank_account.id,tb_autopay_bank_account.company_id,tb_autopay_bank_account.account_holder_name,tb_autopay_bank_account.account_number,tb_autopay_bank_account.accumulated,tb_autopay_bank_account.last_transaction,tb_autopay_bank_account.label,tb_autopay_bank_account.active,tb_autopay_bank_account.created_at, tb_autopay_bank.short_name, tb_autopay_bank.full_name, tb_autopay_bank.bin, tb_autopay_bank.code")->join("tb_autopay_bank","tb_autopay_bank_account.bank_id=tb_autopay_bank.id")->where($where)->countAllResults();
            


        $response = [
            'status'   => 200,
            'error'    => null,
            'messages' => [
                'success' => true,
            ],
            'count_bankaccounts' => $count_bank_accounts
        ];
        return $this->respond($response, 200);
       
    }

}