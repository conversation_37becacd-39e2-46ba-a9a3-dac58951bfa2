<?php

namespace App\Controllers\Userapi;
use Config\Services;
use App\Models\SapoModel;
use CodeIgniter\Controller;
use App\Models\HaravanModel;
use App\Models\GoHighLevelModel;
use App\Models\ShopifyModel;
use App\Models\SmsParserModel;
use App\Models\WebhooksLogModel;
use App\Models\TransactionsModel;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\RESTful\ResourceController;
use App\Models\BankAccountModel;
use App\Models\BankSubAccountModel;
use App\Libraries\GoHighLevelClient;

class Transactions extends ResourceController
{
    use ResponseTrait;

    protected $helpers = ['general'];

    private function _checkAuthen() {

        $throttler = \Config\Services::throttler();

        $capacity = 2;

        $token_left = $throttler->check(md5($this->request->getIPAddress()), $capacity, SECOND);

        if ($token_left === false) {
            $time_left = $throttler->getTokentime();
            return Services::response()->setHeader('X-SePay-UserApi-Retry-After', $time_left)->setStatusCode(429);
        }
        $authen_header = $this->request->getServer('HTTP_AUTHORIZATION');

        $arr1 = explode(" ", $authen_header);

        if(count($arr1) == 2 && $arr1[0] == "Bearer" && strlen($arr1[1]) == 64) {
            $api_key = $arr1[1];
            if(!ctype_alnum($api_key))
                return $this->failUnauthorized("Invalid Auth token");

            // check api key map with company id. return company id or false
            $companyApiModel = model(CompanyApiModel::class);

            $result = $companyApiModel->select("tb_autopay_company_api.company_id")->join("tb_autopay_company","tb_autopay_company.id=tb_autopay_company_api.company_id")->where(['tb_autopay_company_api.api_key' => $api_key,'tb_autopay_company_api.active' => 1,'tb_autopay_company.active' => 1,'tb_autopay_company.status'=>'Active'])->get()->getRow();

            if(is_object($result) && is_numeric($result->company_id))
                return $result->company_id;
            else
                return $this->failUnauthorized("Unauthorized Access");
        } else {
            return $this->failUnauthorized("Invalid Auth token");
        }
        

    }
 
    public function details($id='') {

        $request = \Config\Services::request();

        $client_ip = $request->getIPAddress();

        //if(!in_array($client_ip, ['**************', '**************']))
        //    return $this->failNotFound("Resource Not Found");
        
        if ($this->request->getMethod(true) != 'GET')
            return $this->failNotFound("Resource Not Found");

        if(!is_numeric($id) || $id <= 0)
            return $this->failNotFound("Resource Not Found");

        $company_id = $this->_checkAuthen();

        if(!is_numeric($company_id))
            return '';

        $transactionsModel = slavable_model(TransactionsModel::class, 'Userapi');
        // check id
        $transaction_details  = $transactionsModel->select("tb_autopay_sms_parsed.id,tb_autopay_bank.brand_name as `bank_brand_name`, tb_autopay_bank_account.account_number,tb_autopay_bank_account.account_holder_name, tb_autopay_sms_parsed.transaction_date, tb_autopay_sms_parsed.amount_out,tb_autopay_sms_parsed.amount_in,tb_autopay_sms_parsed.accumulated,tb_autopay_sms_parsed.transaction_content,tb_autopay_sms_parsed.reference_number,tb_autopay_bank_account.company_id, tb_autopay_sms_parsed.code,tb_autopay_sms_parsed.sub_account, tb_autopay_bank_account.id as `bank_account_id`,tb_autopay_sms_parsed.datecreated,tb_autopay_sms_parsed.body")
            ->join("tb_autopay_bank_account","tb_autopay_bank_account.id=tb_autopay_sms_parsed.bank_account_id")
            ->join("tb_autopay_bank","tb_autopay_bank.brand_name=tb_autopay_sms_parsed.gateway")
            ->where(['tb_autopay_bank_account.company_id' => $company_id, 'tb_autopay_sms_parsed.id' => $id,'tb_autopay_sms_parsed.parser_status' => 'Success'])->get()->getRow();

        if(is_object($transaction_details)) {
            $response = [
                'status'   => 200,
                'error'    => null,
                'messages' => [
                    'success' => true,
                ],
                'transaction' => [
                    'id' => $transaction_details->id,
                    'transaction_date' => $transaction_details->transaction_date,
                    'account_number' => $transaction_details->account_number,
                    'sub_account' => $transaction_details->sub_account,
                    'amount_in' => $transaction_details->amount_in,
                    'amount_out' => $transaction_details->amount_out,
                    'accumulated' => $transaction_details->accumulated,
                    'code' => $transaction_details->code,
                    'transaction_content' => $transaction_details->transaction_content,
                    'reference_number' => $transaction_details->reference_number,
                    'bank_brand_name' => $transaction_details->bank_brand_name,
                    'bank_account_id' => $transaction_details->bank_account_id

                ]
            ];
            return $this->respond($response, 200);

        } else {
            return $this->failNotFound("This transaction was not found");
        }
    }

    public function list() {

        $request = \Config\Services::request();

        $client_ip = $request->getIPAddress();

        //if(!in_array($client_ip, ['**************', '**************']))
        //    return $this->failNotFound("Resource Not Found");
        
        if ($this->request->getMethod(true) != 'GET')
            return $this->failNotFound("Resource Not Found");
 
        $company_id = $this->_checkAuthen();

        if(!is_numeric($company_id))
            return '';

        $account_number = $this->request->getGet('account_number');
        $transaction_date_min = $this->request->getGet('transaction_date_min');
        $transaction_date_max = $this->request->getGet('transaction_date_max');
        $since_id = $this->request->getGet('since_id');
        $limit = $this->request->getGet('limit');
        $reference_number = $this->request->getGet('reference_number');
        $amount_in = $this->request->getGet('amount_in');
        $amount_out = $this->request->getGet('amount_out');


        $where = [
            'tb_autopay_bank_account.company_id' => $company_id,
            'tb_autopay_sms_parsed.parser_status' => 'Success'
        ];

        if($account_number && is_numeric($account_number))
            $where['tb_autopay_sms_parsed.account_number'] = $account_number;
            
        if($reference_number)
            $where['tb_autopay_sms_parsed.reference_number'] = $reference_number;

        if($transaction_date_min && strtotime($transaction_date_min))
            $where['tb_autopay_sms_parsed.transaction_date>='] = $transaction_date_min;

        if($transaction_date_max && strtotime($transaction_date_max))
            $where['tb_autopay_sms_parsed.transaction_date<='] = $transaction_date_max;
        
        if(is_numeric($since_id))
            $where['tb_autopay_sms_parsed.id>='] = $since_id;
        
        if(is_numeric($amount_in) && $amount_in>=0)
            $where['tb_autopay_sms_parsed.amount_in'] = $amount_in;
           
        if(is_numeric($amount_out) && $amount_out >=0)
            $where['tb_autopay_sms_parsed.amount_out'] = $amount_out;
        
        if(!is_numeric($limit))
            $limit = 5000;
        
        $transactionsModel = slavable_model(TransactionsModel::class, 'Userapi');
        // check id
        $transactions  = $transactionsModel->select("tb_autopay_sms_parsed.id,tb_autopay_bank.brand_name as `bank_brand_name`, tb_autopay_bank_account.account_number, tb_autopay_sms_parsed.transaction_date, tb_autopay_sms_parsed.amount_out,tb_autopay_sms_parsed.amount_in,tb_autopay_sms_parsed.accumulated,tb_autopay_sms_parsed.transaction_content,tb_autopay_sms_parsed.reference_number, tb_autopay_sms_parsed.code,tb_autopay_sms_parsed.sub_account, tb_autopay_bank_account.id as `bank_account_id`")
            ->join("tb_autopay_bank_account","tb_autopay_bank_account.id=tb_autopay_sms_parsed.bank_account_id")
            ->join("tb_autopay_bank","tb_autopay_bank.brand_name=tb_autopay_sms_parsed.gateway")
            ->where($where)->orderBy('id','DESC')->limit($limit)->get()->getResult();


        $response = [
            'status'   => 200,
            'error'    => null,
            'messages' => [
                'success' => true,
            ],
            'transactions' => $transactions
        ];
        return $this->respond($response, 200);
       
    }


    public function count() {

        $request = \Config\Services::request();

        $client_ip = $request->getIPAddress();

        //if(!in_array($client_ip, ['**************', '**************']))
        //    return $this->failNotFound("Resource Not Found");
        
        if ($this->request->getMethod(true) != 'GET')
            return $this->failNotFound("Resource Not Found");
 
        $company_id = $this->_checkAuthen();

        if(!is_numeric($company_id))
            return '';

        $account_number = $this->request->getGet('account_number');
        $transaction_date_min = $this->request->getGet('transaction_date_min');
        $transaction_date_max = $this->request->getGet('transaction_date_max');
        $since_id = $this->request->getGet('since_id');

        $where = [
            'tb_autopay_bank_account.company_id' => $company_id,
            'tb_autopay_sms_parsed.parser_status' => 'Success'
        ];

        if($account_number && is_numeric($account_number))
            $where['tb_autopay_sms_parsed.account_number'] = $account_number;
      
        if($transaction_date_min && strtotime($transaction_date_min))
            $where['tb_autopay_sms_parsed.transaction_date>='] = $transaction_date_min;

        if($transaction_date_max && strtotime($transaction_date_max))
            $where['tb_autopay_sms_parsed.transaction_date<='] = $transaction_date_max;
        
        if($since_id && is_numeric($since_id))
            $where['tb_autopay_sms_parsed.id>='] = $since_id;
           
       
       
        $transactionsModel = slavable_model(TransactionsModel::class, 'Userapi');
        // check id
        $count_transactions  = $transactionsModel->select("tb_autopay_sms_parsed.id,tb_autopay_bank.brand_name as `bank_brand_name`, tb_autopay_bank_account.account_number, tb_autopay_sms_parsed.transaction_date, tb_autopay_sms_parsed.amount_out,tb_autopay_sms_parsed.amount_in,tb_autopay_sms_parsed.accumulated,tb_autopay_sms_parsed.transaction_content,tb_autopay_sms_parsed.reference_number, tb_autopay_sms_parsed.code,tb_autopay_sms_parsed.sub_account, tb_autopay_bank_account.id as `bank_account_id`")
            ->join("tb_autopay_bank_account","tb_autopay_bank_account.id=tb_autopay_sms_parsed.bank_account_id")
            ->join("tb_autopay_bank","tb_autopay_bank.brand_name=tb_autopay_sms_parsed.gateway")
            ->where($where)->orderBy('id','DESC')->countAllResults();

        $response = [
            'status'   => 200,
            'error'    => null,
            'messages' => [
                'success' => true,
            ],
            'count_transactions' => $count_transactions
        ];
        return $this->respond($response, 200);
       
    }


    public function check_transaction_status() {

        $request = \Config\Services::request();

        $this->response->setHeader('access-control-allow-origin', '*');

        if ($this->request->getMethod(true) != 'POST')
            return $this->failNotFound();

        $model = slavable_model(SmsParserModel::class, 'Userapi', 'read1');
        $haravanModel = slavable_model(HaravanModel::class, 'Userapi', 'read1');
        $sapoModel = slavable_model(SapoModel::class, 'Userapi', 'read1');
        $shopifyModel = slavable_model(ShopifyModel::class, 'Userapi', 'read1');
        $bankAccountModel = slavable_model(BankAccountModel::class, 'Userapi', 'read1');
        $bankSubAccountModel = slavable_model(BankSubAccountModel::class, 'Userapi', 'read1');
        $webhookLogsModel = slavable_model(WebhooksLogModel::class, 'Userapi', 'read1');

        $validation =  \Config\Services::validation();
 
        if(! $this->validate(
            [
            'account_number'=>'required|alpha_numeric|max_length[20]|min_length[3]',
            'amount'=>'required|integer|is_natural',
            'code'=>'required|alpha_numeric|max_length[20]|min_length[3]'
        ]))
            return $this->failValidationError(implode(". ", $validation->getErrors()));

        $account_number = $this->request->getVar('account_number');
        $amount_in = $this->request->getVar('amount');
        $code = $this->request->getVar('code');
        /*

        $result_haravan = $haravanModel->join('tb_autopay_bank_account','tb_autopay_bank_account.id=tb_autopay_haravan.bank_account_id')->where(['tb_autopay_bank_account.account_number' => $account_number])->get()->getRow();
        $result_sapo = $sapoModel->join('tb_autopay_bank_account','tb_autopay_bank_account.id=tb_autopay_sapo.bank_account_id')->where(['tb_autopay_bank_account.account_number' => $account_number])->get()->getRow();


        if(!$result_haravan && !$result_sapo)
            return $this->failNotFound();        

        */

        $va_details = FALSE;

        // check account_number in bank_account
        $bank_account_details = $bankAccountModel->where(['account_number' => $account_number])->get()->getRow();

        if(!is_object($bank_account_details)) {
            $va_details = $bankSubAccountModel->where(['sub_account' => $account_number])->get()->getRow();

            if(!is_object($va_details)) {
                return $this->failNotFound();        
            }
        }

        if(is_object($bank_account_details)) {
            $result_haravan = $haravanModel->join('tb_autopay_bank_account','tb_autopay_bank_account.id=tb_autopay_haravan.bank_account_id')->where(['tb_autopay_bank_account.account_number' => $account_number])->get()->getRow();
            $result_sapo = $sapoModel->join('tb_autopay_bank_account','tb_autopay_bank_account.id=tb_autopay_sapo.bank_account_id')->where(['tb_autopay_bank_account.account_number' => $account_number])->get()->getRow();
            $result_shopify = $shopifyModel->join('tb_autopay_bank_account','tb_autopay_bank_account.id=tb_autopay_shopify.bank_account_id')->where(['tb_autopay_bank_account.account_number' => $account_number])->get()->getRow();
    
    
            if(!$result_haravan && !$result_sapo && !$result_shopify)
                return $this->failNotFound();    
        }

        // check account_number in bank_sub_account
        if(is_object($va_details)) {
            $result_haravan = $haravanModel->join("tb_autopay_bank_sub_account", "tb_autopay_bank_sub_account.id=tb_autopay_haravan.va_id")->where(['tb_autopay_bank_sub_account.sub_account' => $account_number])->get()->getRow();
            $result_sapo = $sapoModel->join("tb_autopay_bank_sub_account", "tb_autopay_bank_sub_account.id=tb_autopay_sapo.va_id")->where(['tb_autopay_bank_sub_account.sub_account' => $account_number])->get()->getRow();
            $result_shopify = $shopifyModel->join("tb_autopay_bank_sub_account", "tb_autopay_bank_sub_account.id=tb_autopay_shopify.va_id")->where(['tb_autopay_bank_sub_account.sub_account' => $account_number])->get()->getRow();
    
            if(!$result_haravan && !$result_sapo && !$result_shopify)
                return $this->failNotFound();    
        }

        if(is_object($bank_account_details)) {
            
            $result = $model->where(['account_number' => $account_number, 'amount_in' => $amount_in])->like("transaction_content",$code)->get()->getRow();
    
            
            if(is_object($result)) {
                
                return $this->respond([
                    'status' => 200,
                    'error' => null,
                    'pay_status' => TRUE,
                ], 200);
                
            }
        }

        if(is_object($va_details)) {
            $where = [
                'sub_account' => $account_number,
                'amount_in' => $this->request->getVar('amount'),
            ];
    
            
            $result = $model->where(['sub_account' => $account_number, 'amount_in' => $amount_in])->like("transaction_content",$code)->get()->getRow();
    
            
            if(is_object($result)) {
 
                return $this->respond([
                    'status' => 200,
                    'error' => null,
                    'pay_status' => TRUE,
                ], 200);
                
            }
        }

        $particallyPaidTransaction = null;
        
        if (is_object($bank_account_details)) {
            $particallyPaidTransaction = $model->where(['account_number' => $account_number, 'amount_in > ' => 0, 'parser_status' => true, 'webhooks_verify_payment' => 'Success'])->like('transaction_content', $code)->orderBy('id', 'desc')->first();
        }

        if (is_object($va_details)) {
            $particallyPaidTransaction = $model->where(['sub_account' => $account_number, 'amount_in > ' => 0, 'parser_status' => true, 'webhooks_verify_payment' => 'Success'])->like('transaction_content', $code)->orderBy('id', 'desc')->first();
        }

        if (!$particallyPaidTransaction) {
            return $this->respond([
                'status' => 200,
                'error' => null,
                'pay_status' => FALSE,
            ], 200);
        }

        $shopifyTransactionLog = $webhookLogsModel->where(['webhook_type' => 'Shopify', 'connect_success' => 1, 'request_method' => 'POST', 'sms_parsed_id' => $particallyPaidTransaction->id])->like('request_url', 'transactions.json')->orderBy('id', 'desc')->first();

        if (!$shopifyTransactionLog) {
            return $this->respond([
                'status' => 200,
                'error' => null,
                'pay_status' => FALSE,
            ], 200);
        };

        $shopifyTransactionResponse = json_decode($shopifyTransactionLog->response_body);

        if (!is_object($shopifyTransactionResponse)) {
            return $this->respond([
                'status' => 200,
                'error' => null,
                'pay_status' => FALSE,
            ], 200);
        };

        try {
            $presentmentMoney = intval($shopifyTransactionResponse->transaction->total_unsettled_set->presentment_money->amount);

            if ($presentmentMoney == 0) {
                return $this->respond([
                    'status' => 200,
                    'error' => null,
                    'pay_status' => TRUE,
                ], 200);
            }

            return $this->respond([
                'status' => 200,
                'error' => null,
                'presentment_money' => $presentmentMoney,
                'pay_status' => FALSE,
            ], 200);
        } catch (\Exception $e) {
            return $this->respond([
                'status' => 200,
                'error' => null,
                'pay_status' => FALSE,
            ], 200);
        }
    }

    // url getcode GHL
    public function get_code()
    {
        $request = \Config\Services::request(); 
        $response = \Config\Services::response(); 
    
        // Xử lý dữ liệu GET
        $data = $request->getGet('code');
        $session = service('session');
    
        if (empty($data)) {
            $session->setFlashdata('alert', [
                'type' => 'danger', // Loại thông báo (success, danger, warning, info)
                'message' => 'Code không tồn tại, không thể xác thực người dùng!',
                'code' => $data,
            ]);
    
            return "
                <script>
                    window.opener.location.href='/gohighlevel';
                    window.close();
                </script>";
        }
        
    
        // Nếu có code, lưu thông báo thành công
        $session->setFlashdata('alert', [
            'type' => 'success',
            'message' => 'Code đã được xử lý thành công!',
            'code' => $data,
        ]);
    
        return "
            <script>
                window.opener.location.href='/gohighlevel';
                window.close();
            </script>";
    }   

    // url render template GHL
    public function payment_url()
    {

        if (strtoupper($this->request->getMethod()) != 'GET') {
            return $this->response->setStatusCode(404)->setJSON(['message' => "Phương thức URL không hợp lệ!"]);
        }
        
        $param_d = $this->request->getGet("d") ?? "";


        if (empty($param_d)) {
            return $this->response->setBody("");
        }
        
        // Decode Base64
        $decoded_data = base64_decode($param_d);
        
        // Check JSON validity
        $data_json_url = json_decode($decoded_data, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            $html = <<<HTML
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Payment Gateway</title>
                <style>
                    .popup {
                        position: fixed;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        background-color: #fff;
                        padding: 20px;
                        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
                        border-radius: 8px;
                        text-align: center;
                        z-index: 1000;
                        font-family: "Helvetica Neue", Arial, sans-serif;
                       
                    }
                    .overlay {
                        position: fixed;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background-color: rgba(0, 0, 0, 0.5);
                        z-index: 999;
                    }
                </style>
            </head>
            <body>
            <div class="overlay">
                <div class="popup">
                    <h2>Thông báo</h2>
                    <p>
                        Lỗi hệ thống thanh toán, hãy liên hệ kỹ thuật.
                    </p>
                </div>
            </div>

        
              
            </body>
            </html>
            HTML;
        
            return $this->response->setBody($html);
        }
        
        // Thiết lập validation rules
        $rules = [
            'account_number'  => 'required',
            'bank_bin'        => 'required',
            'bank_brand_name' => 'required',
            'account_name'    => 'required',
            'prefix'          => 'required',
            'bank_code'       => 'required',
            'site_url'       => 'required',
        ];
        $validation = \Config\Services::validation(); // Gọi service validation
        $validation->setRules($rules);


        if (!$validation->run($data_json_url)) {
            $html = <<<HTML
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Payment Gateway</title>
                <style>
                    .popup {
                        font-family: "Helvetica Neue", Arial, sans-serif;
                        position: fixed;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        background-color: #fff;
                        padding: 20px;
                        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
                        border-radius: 8px;
                        text-align: center;
                        z-index: 1000;
                       
                    }
                    .overlay {
                        position: fixed;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background-color: rgba(0, 0, 0, 0.5);
                        z-index: 999;
                    }
                </style>
            </head>
            <body>
            <div class="overlay">
                <div class="popup">
                    <h2>Thông báo</h2>
                    <p>
                        Lỗi hệ thống xác thực thông tin.  
                        Hãy liên hệ kỹ thuật để được hỗ trợ.
                    </p>
                </div>
            </div>

        
              
            </body>
            </html>
            HTML;
        
            return $this->response->setBody($html);
        }
        

        // Access the elements from the decoded array
        $account_number = $data_json_url['account_number'] ?? "";
        $bank_bin = $data_json_url['bank_bin'] ?? "";
        $bank_brand_name = $data_json_url['bank_brand_name'] ?? "";
        $account_name = $data_json_url['account_name'] ?? "";
        $content_prefix = $data_json_url['prefix'] ?? "";
        $bank_code = $data_json_url['bank_code'] ?? "";
        $site_url = $data_json_url['site_url'] ?? "";
        $base_url = base_url();
        $bank_logo_url = "https://qr.sepay.vn/assets/img/banklogo/" . rawurlencode($bank_code) . ".png";


        // check intergration
        $goHighLevelModel = model(GoHighLevelModel::class);

        $result_gohighlevel_va = $goHighLevelModel
        ->join("tb_autopay_bank_sub_account", "tb_autopay_bank_sub_account.id=tb_autopay_gohighlevel.va_id")
        ->where(['tb_autopay_gohighlevel.active' => 1,
        'tb_autopay_bank_sub_account.sub_account' => $account_number,
        'tb_autopay_gohighlevel.paycode_prefix' => $content_prefix
        ])
        ->get()->getRow();

        $result_gohighlevel_accbank = $goHighLevelModel
        ->join('tb_autopay_bank_account','tb_autopay_bank_account.id=tb_autopay_gohighlevel.bank_account_id')
        ->where(['tb_autopay_gohighlevel.active' => 1,
        'tb_autopay_bank_account.account_number' => $account_number,
        'tb_autopay_gohighlevel.paycode_prefix' => $content_prefix
        ])
        ->get()->getRow();
        
        if(!is_object($result_gohighlevel_accbank) && !is_object($result_gohighlevel_va)){ 
            $html = <<<HTML
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Payment Gateway</title>
                <style>
                    .popup {
                        font-family: "Helvetica Neue", Arial, sans-serif;
                        position: fixed;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        background-color: #fff;
                        padding: 20px;
                        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
                        border-radius: 8px;
                        text-align: center;
                        z-index: 1000;
                       
                    }
                    .overlay {
                        position: fixed;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background-color: rgba(0, 0, 0, 0.5);
                        z-index: 999;
                    }
                </style>
            </head>
            <body>
            <div class="overlay">
                <div class="popup">
                    <h2>Thông báo</h2>
                    <p>
                        Tính năng này hiện đang tạm ngưng.  
                        Quý khách vui lòng liên hệ quản trị viên để biết thêm thông tin chi tiết.
                    </p>
                </div>
            </div>

        
              
            </body>
            </html>
            HTML;
        
            return $this->response->setBody($html);
        }
        
        // content ck
        $remark = "";
        if ($bank_code === 'ICB') {
            $remark = 'SEVQR ';
        }



       
        $html = <<<HTML
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Payment Gateway</title>
            <style>
                .popup {
                    font-family: "Helvetica Neue", Arial, sans-serif;
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background-color: #fff;
                    padding: 20px;
                    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
                    border-radius: 8px;
                    text-align: center;
                    z-index: 1000;
                   
                }
                .overlay {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background-color: rgba(0, 0, 0, 0.5);
                    z-index: 999;
                }
            </style>
        </head>
        <body>
            <div class="overlay">
                <div class="popup">
                    <div style="display: flex; flex-direction: column; align-items: center; justify-content: center;">
                        <img src="https://qr.sepay.vn/assets/img/loading.gif" alt="Loading..." style="width:40px; margin-bottom: 16px;">
                        <span style="font-size: 18px; color: #419dd4;">Đang tải trang thanh toán...</span>
                    </div>
                </div>
            </div>
    
            <script>
                
                let pay_status = "Unpaid";

                setTimeout(() => {
                    const popupElement = document.querySelector("#sepay-box-element");
                    if(!popupElement){
                        window.location.reload();
                    }
                }, 500);


                function closePopup() {
                    const eventData = {
                        type: 'custom_element_close_response'
                    };
    
                    try {
                        const eventDataString = JSON.stringify(eventData);
                        window.parent.postMessage(eventDataString, "${site_url}");
                    } catch (error) {
                        console.error("Error sending success event:", error);
                    }
                }
    
                function sendReadyEvent() {
                    const eventData = {
                        type: 'custom_provider_ready',
                        loaded: true
                    };
    
                    try {
                        const eventDataString = JSON.stringify(eventData);
                        window.parent.postMessage(eventDataString, "${site_url}");
                    } catch (error) {
                        console.error("Error sending ready event:", error);
                    }
                }
                
                window.addEventListener("load", sendReadyEvent);
    
                window.addEventListener("message", (event) => {
                    try {
                        const data = typeof event.data === "string" ? JSON.parse(event.data) : event.data;
                        if (data && (data.orderId || data.invoiceId)) {
                            renderTemplate(data);
                        } else {
                            console.error("orderId hoặc invoiceId không tồn tại:", data);
                        }
                    } catch (error) {
                        console.error("Lỗi phân tích dữ liệu sự kiện:", error);
                    }
                });
    
                async function renderTemplate(data_order){
                   
                    
                    let account_number = "$account_number";
                    let bank_logo_url = "$bank_logo_url";
                    let bank_bin = "$bank_bin";
                    let bank_brand_name = "$bank_brand_name";
                    let account_name = "$account_name";
                    let content_prefix = "$content_prefix";
                    let amount = data_order.amount;
                    let remark = "$remark";                      
                    let order_id = data_order.orderId || data_order.invoiceId;
                    let transaction_id = data_order.transactionId;

                    let content_ck = remark + content_prefix + order_id;

                 
                    let popupElement = document.querySelector(".popup");
                    
                    await (popupElement.innerHTML = `
                            <style>
                            .popup{
                                 width: 60%;
                            }
                            @media (max-width: 520px) {
                            .popup {
                                width: 80%;
                                overflow-y: auto;
                                height: 95%;
                            }
                            .col--md-two {
                                width: 100% !important;
                            }
                            .col--md-two.border-end {
                                border-right: none;
                            }
                            .sepay-pay-info {
                                display: block !important;
                            }
                            .col--md-two {
                                padding: 1rem !important;
                            }
                            }
                           

                            @media (max-width: 750px) {
                            .popup {
                                width: 80%;
                                overflow-y: auto;
                                height: 95%;
                            }
                            .sepay-box table tr td {
                                display: block;
                                padding: 0;
                            }
                            } 

                            @media (max-width: 760px) {
                                .popup {
                                width: 80%;
                                }
                            }
                             @media (max-width: 1025px) {
                                .popup {
                                width: 90%;
                                }
                            }
                            @media (max-width: 1270) {
                                .popup {
                                width: 90%;
                                }
                            }

                            .sepay-box table tr.border {
                            border-bottom: 1px solid rgba(175,175,175,.34);
                            }

                            .col--md-two {
                            padding: 1rem;
                            box-sizing: border-box;
                            }

                            .sepay-box a {
                                color: #2a9dcc;
                                text-decoration: none;
                                cursor: pointer;
                            }

                            .col--md-two.border-end {
                            display: flex;
                            flex-direction: column;
                            justify-content: space-between;
                            }

                            .sepay-pay-info {
                            display: flex;
                            }

                            .text-center {
                                text-align: center!important;
                            }
                        
                            .text-end {
                                text-align: right!important;
                            }

                            .text-start {
                                text-align: left!important;
                            }
                            
                            .box-title {
                                padding-top: 12px;
                                padding-bottom: 12px;
                                border-bottom: 1px solid rgba(175,175,175,.34);
                            }

                            .border-end {
                                border-right: 1px solid rgba(175,175,175,.34);
                            }

                            .lh-lg {
                                line-height: 2!important;
                            }
                            .sepay-box {
                                margin-top: 20px;
                                container-type: inline-size;
                                container-name: sepay;
                                background-color: white;
                                border: 1px solid rgba(175,175,175,.34);
                            }

                            .text-muted {
                                color: #6c757d!important;
                            }

                            .font-weight-bold {
                                font-weight: 700!important;
                            }

                            img.qr-image {
                                max-width: 220px;
                            }

                            img.bank-logo {
                                max-width: 150px;
                                width:120px;
                            }

                            .mt-3 {
                                margin-top: 1rem!important;
                            }

                            .mb-2 {
                                margin-bottom: 2rem;
                            }

                            .text-success {
                                color: #28a745!important;
                            }

                            .qr-element {
                                position: relative;
                                padding: 6px;
                                aspect-ratio: 1;
                                max-width: max-content;
                                margin: 10px auto;
                            }
                            .qr-top-border,
                            .qr-bottom-border {
                                position: absolute;
                                display: flex;
                                justify-content: space-between;
                                width: 100%;
                                height: 30%;
                                left: 0;
                            }
                            .qr-top-border {
                                top: 0;
                            }
                            .qr-bottom-border {
                                bottom: 0;
                            }
                            .qr-top-border:after,
                            .qr-top-border:before,
                            .qr-bottom-border:after,
                            .qr-bottom-border:before {
                                content: '';
                                width: 30%;
                                height: 100%;
                            }
                            .qr-top-border:after,
                            .qr-top-border:before {
                                border-top: 1px solid green;
                            }
                            .qr-bottom-border:after,
                            .qr-bottom-border:before {
                                border-bottom: 1px solid green;
                            }
                            .qr-top-border:before,
                            .qr-bottom-border:before {
                                border-left: 1px solid green;
                            }
                            .qr-top-border:after,
                            .qr-bottom-border:after {
                                border-right: 1px solid green;
                            }
                            .button-cancel {
                            margin-top: 12px;
                            cursor: pointer;
                            background-color: #f44336; /* Màu đỏ nhạt */
                            color: white; 
                            border: none;
                            padding: 10px 20px; 
                            border-radius: 8px; 
                            font-size: 14px; 
                            font-weight: bold;
                            transition: background-color 0.3s ease, transform 0.2s ease;
                            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); 
                        }

                        .button-cancel:hover {
                            background-color: #d32f2f; /* Đậm hơn khi hover */
                            transform: scale(1.05); /* Hiệu ứng phóng to nhẹ */
                        }

                        .button-cancel:active {
                            transform: scale(0.95); /* Nhấn nút sẽ thu nhỏ nhẹ */
                            background-color: #b71c1c; /* Màu tối hơn khi nhấn */
                        }

                            
                            </style>
                            <div class="sepay-box section__content section__content--bordered" id="sepay-box-element">
                                <div class="box-title">
                                <h3 class="text-center">Hướng dẫn thanh toán qua chuyển khoản ngân hàng</h3>
                                <div class="alert alert-warning text-center" style="color: #419dd4;font-weight: 400;" role="alert">
                                    Vui lòng giữ nguyên trang thanh toán sau khi quét mã QR để đơn hàng được xác thực!
                                </div>
                                </div>
                                <div class="sepay-message"></div>
                                <div class="row sepay-pay-info">
                                    <div class="col col--md-two border-end">
                        <h4 class="text-center font-weight-bold">Cách 1: Mở app ngân hàng và quét mã QR</h4>
                        <div class="text-center">
                        <div class="qr-element">
                            <a class="qr-image-a" download target="_blank" rel="noopener" href="https://qr.sepay.vn/img?acc=${account_number}&bank=${bank_bin}&amount=&des=${remark}&template=compact&download=yes" style="z-index: 1; display: flex; align-items: center; justify-content: center; width: 20px; height: 20px; border: 1px solid #0ea5e9; position: absolute; bottom: 9px; right: 5px;">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"><path fill="#0ea5e9" d="M4 22v-2h16v2zm8-4L5 9h4V2h6v7h4z"/></svg>
                            </a>
                            <div class="qr-top-border"></div>
                            <div class="qr-bottom-border"></div>
                            <div class="qr-content"><img class="qr-image" src="https://qr.sepay.vn/img?acc=${account_number}&bank=${bank_bin}&amount=&des=${remark}&template=compact">
                        </div>
                        </div>
                        </div>
                        <div class="sepay-pay-footer" style="display: flex; justify-content: center; align-items: center;">Trạng thái: Chờ thanh toán...<img src="https://qr.sepay.vn/assets/img/loading.gif" style="width:17px; margin-left: 0.5rem;"></div>

                                    </div>
                                    <div class="col col--md-two">
                            <h4 class="text-center font-weight-bold">Cách 2: Chuyển khoản thủ công theo thông tin</h4>
                            <table class="mt-3 mx-auto table-sm lh-lg" style="width:100%"> 
                            <tbody>
                                <tr>
                                <td colspan="3" class="text-center">
                                    <img class="bank-logo" src="${bank_logo_url}">
                                </td>
                                </tr>
                                <tr>
                                <td colspan="3" class="text-center">
                                    <b>Ngân hàng ${bank_brand_name}</b><br>
                                </td>
                                </tr>
                                <tr class="border">
                                <td style="width: 120px; vertical-align: top;display: flex;justify-content:flex-start;">Chủ tài khoản:</td>
                                <td colspan="2"  class="text-start"><span class="ml-1 font-weight-bold" id="copy_accholder">${account_name}</span>
                                </td>
                                </tr>
                                <tr class="border">
                                <td style="width: 120px; vertical-align: top;display: flex;justify-content:flex-start;">Số tài khoản:</td>
                                <td>
                                    <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <span class="ml-1 font-weight-bold text-start" id="copy_accno">${account_number}</span>
                                    <span id="sepay_copy_account_number"><a id="sepay_copy_account_number_btn" style="display:none" href="javascript:;">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"><path fill="#888888" d="M19 3h-4.18C14.4 1.84 13.3 1 12 1s-2.4.84-2.82 2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2m-7 0a1 1 0 0 1 1 1a1 1 0 0 1-1 1a1 1 0 0 1-1-1a1 1 0 0 1 1-1M7 7h10V5h2v14H5V5h2z"/></svg>
                                    </a></span>
                                    </div>
                                </td>
                                </tr>
                                <tr class="border">
                                <td style="width: 120px; vertical-align: top;display: flex;justify-content:flex-start;">Số tiền:</td>
                                <td>
                                    <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <span class="ml-1  font-weight-bold text-start" id="copy_amount"></span>
                                    <span id="sepay_copy_amount"><a id="sepay_copy_amount_btn" style="display:none" href="javascript:;">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"><path fill="#888888" d="M19 3h-4.18C14.4 1.84 13.3 1 12 1s-2.4.84-2.82 2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2m-7 0a1 1 0 0 1 1 1a1 1 0 0 1-1 1a1 1 0 0 1-1-1a1 1 0 0 1 1-1M7 7h10V5h2v14H5V5h2z"/></svg>
                                    </a></span>
                                    </div>
                                </td>
                                </tr>
                                <tr class="border">
                                <td style="width: 120px; vertical-align: top;display: flex;justify-content:flex-start;">Nội dung CK:</td>
                                <td>
                                    <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <span id="copy_memo" class="ml-1 font-weight-bold text-start">${remark}</span>
                                    <span id="sepay_copy_transfer_content"><a id="sepay_copy_transfer_content_btn" style="display:none" href="javascript:;">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"><path fill="#888888" d="M19 3h-4.18C14.4 1.84 13.3 1 12 1s-2.4.84-2.82 2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2m-7 0a1 1 0 0 1 1 1a1 1 0 0 1-1 1a1 1 0 0 1-1-1a1 1 0 0 1 1-1M7 7h10V5h2v14H5V5h2z"/></svg>
                                    </a></span>
                                    </div>
                                </td>
                                </tr>
                        <tr></tbody></table>

                        <p class="text-muted" style="font-size: 13px; background: #fffbeb; padding: 0.5rem 1rem">Lưu ý: Vui lòng giữ nguyên nội dung <b>${remark}</b> khi chuyển khoản để hệ thống tự xác nhận thanh toán.</p>

                        </div>
                                </div>
                            </div>
                       <button class="button-cancel" onclick="closePopup()">Hủy</button>

                    `);

                    // render amount QR

                    let qrImageLink = document.querySelector(".qr-image-a");
                    let qrImage = document.querySelector(".qr-content .qr-image");
                    if (qrImageLink) {
                        let url = new URL(qrImageLink.href);

                        url.searchParams.set("amount", amount);
                        url.searchParams.set("des", content_ck);

                        qrImageLink.href = url.toString();
                        qrImage.src = url.toString();
                    } else {
                        console.warn("Không tìm thấy thẻ <a> có class 'qr-image-a'.");
                    }


                    // render amount text
                    let copyAmountElement = document.querySelector("#copy_amount");

                    // Kiểm tra phần tử tồn tại và định dạng số tiền
                    if (copyAmountElement) {
                        copyAmountElement.textContent = amount.toLocaleString('vi-VN', { style: 'currency', currency: 'VND' });
                    } else {
                        console.warn("#copy_amount không tồn tại.");
                    }

                    document.querySelector("#copy_memo").textContent = content_ck;
                    
                    
                    setInterval( function () {
                        if(pay_status = "Unpaid"){

                            check_invoice_status(account_number,content_ck,amount,order_id,transaction_id);
                        }
                    }, 5000 );

                   

                    
                }

                function check_invoice_status(account_number,content_ck,amount,order_id,transaction_id) {
                    const form = new FormData()
                    form.append('type', "verify");
                    form.append('account_number', account_number);
                    form.append('code', content_ck);
                    form.append('amount', parseInt(amount));
                    form.append('chargeId', order_id);
                    form.append('transaction_id', transaction_id);
                    form.append('subscription_id', order_id);

                    fetch("${base_url}/userapi/transactions/query_url", {
                        method: "POST",
                        body: form,
                        }).then(async (res) => {
                        const data = await res.json();
                        if(data.success == "true") {
                            paymentSuccess(data.chargeId)
                            pay_status = "Paid";
                        }else{
                            console.warn("Chưa xác thực thanh toán");
                        }
                        
                    
                    })
                }

    
                function paymentSuccess(chargeId) {
                    const eventData = {
                        chargeId: chargeId,
                        type: 'custom_element_success_response'
                    };
    
                    try {
                        const eventDataString = JSON.stringify(eventData);
                        window.parent.postMessage(eventDataString, "${site_url}");
                    } catch (error) {
                        console.error("Error sending success event:", error);
                    }
                }
            </script>
        </body>
        </html>
        HTML;
    
        return $this->response->setBody($html);
    }

    
    // url veryfi payment GHL
    public function query_url(){
        
        $data = array(
            "type" => $this->request->getPost('type'),
            "account_number" => $this->request->getPost('account_number'),
            "code" => $this->request->getPost('code'),
            "amount" => $this->request->getPost('amount'),
            "chargeId" => $this->request->getPost('chargeId'),
            "transaction_id" => $this->request->getPost('transaction_id'),
            "subscription_id" => $this->request->getPost('subscription_id'),
        );
        
        $rules = [
            'type'  => 'required',
            'account_number'        => 'required',
            'code' => 'required',
            'amount'    => 'required',
            'chargeId'          => 'required',
            'transaction_id'       => 'required',
            'subscription_id'       => 'required',
        ];
        
        $validation = \Config\Services::validation(); // Gọi service validation
        $validation->setRules($rules);

       

        if (!$validation->run($data)) {
            return $this->response->setJSON(['success' => 'false','message'=>'params invalid']);
        }

        $transactionModel = slavable_model(SmsParserModel::class, 'Userapi');
        $goHighLevelModel = slavable_model(GoHighLevelModel::class, 'Userapi');
        $bankAccountModel = slavable_model(BankAccountModel::class, 'Userapi');
        $bankSubAccountModel = slavable_model(BankSubAccountModel::class, 'Userapi');
        $webhookLogsModel = slavable_model(WebhooksLogModel::class, 'Userapi');

        // bảng
        $smsParserModel = model(SmsParserModel::class);

        $account_number = $data['account_number'];
        $amount_in = $data['amount'];
        $code = $data['code'];
        $charge_id = $data['chargeId'];


        // check account_number in bank_account
        $va_details = FALSE;

        $bank_account_details = $bankAccountModel->where(['account_number' => $account_number])->get()->getRow();
        if(!is_object($bank_account_details)) {
            $va_details = $bankSubAccountModel->where(['sub_account' => $account_number])->get()->getRow();        
            if(!is_object($va_details)) {
                return $this->response->setJSON(['success' => 'false','message'=>'params invalid']);      
            }
        }

       
     

       
        if (is_object($bank_account_details)) {
            // Lấy tất cả các tích hợp phù hợp
            $results_gohighlevel = $goHighLevelModel
                ->select('tb_autopay_gohighlevel.*')
                ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id = tb_autopay_gohighlevel.bank_account_id')
                ->where([
                    'tb_autopay_gohighlevel.active' => 1,
                    'tb_autopay_bank_account.account_number' => $account_number
                ])
                ->orderBy('tb_autopay_gohighlevel.id', 'DESC')
                ->get()
                ->getResult();

            // Nếu không có tích hợp nào, trả về lỗi
            if (empty($results_gohighlevel)) {
                return $this->response->setJSON([
                    'success' => 'false',
                    'message' => 'integration not find'
                ]);
            }

            // Lặp qua từng tích hợp
            foreach ($results_gohighlevel as $result_gohighlevel) {
                $result = $transactionModel
                    ->where([
                        'account_number' => $account_number,
                        'amount_in' => $amount_in
                    ])
                    ->like("transaction_content", $code)
                    ->orderBy('id', 'DESC')
                    ->get()
                    ->getRow();
                log_message("debug", "result" . json_encode($result_gohighlevel));     
                if (is_object($result)) {
                    $gohightlevel_client = new GoHighLevelClient();
                    $data_order_ghl = $gohightlevel_client->orderDetail($result_gohighlevel, $charge_id);

                    if (empty($data_order_ghl['status'])) {
                        if ($result->webhooks_verify_payment != "Success" && $result->webhooks_verify_payment != "Failed") {
                            $smsParserModel
                                ->set(['webhooks_verify_payment' => 'Failed'])
                                ->where(['id' => $result->id])
                                ->update();

                            $createTransactionWebhookLog = [
                                'request_method' => $data_order_ghl['request']['method'],
                                'request_url' => $data_order_ghl['request']['url'],
                                'request_header' => json_encode($data_order_ghl['request']['headers']),
                                'request_body' => json_encode($data_order_ghl['request']['json']),
                                'response_body' => $data_order_ghl['raw_response'] ?? ($data_order_ghl['response'] ?? ''),
                                'status_code' => $data_order_ghl['status_code'],
                                'response_status_code' => $data_order_ghl['status_code'],
                                'connect_success' => in_array($data_order_ghl['status_code'], [200, 201])
                            ];

                            $gohightlevel_client->logGhlWebhook($result_gohighlevel, $result->id, $createTransactionWebhookLog);

                            return $this->response->setJSON([
                                'success' => 'false',
                                'pay_status' => 'false',
                                'message' => "Error verify payment"
                            ]);
                        }

                        continue; // sang tích hợp khác
                    }

                    if ($amount_in != $data_order_ghl['amount']) {
                        return $this->response->setJSON([
                            'success' => 'false',
                            'message' => 'amount not match'
                        ]);
                    }

                    $createTransactionWebhookLog = [
                        'request_method' => $data_order_ghl['request']['method'],
                        'request_url' => $data_order_ghl['request']['url'],
                        'request_header' => json_encode($data_order_ghl['request']['headers']),
                        'request_body' => json_encode($data_order_ghl['request']['json']),
                        'response_body' => $data_order_ghl['response'],
                        'status_code' => $data_order_ghl['status_code'],
                        'response_status_code' => $data_order_ghl['status_code'],
                        'connect_success' => in_array($data_order_ghl['status_code'], [200, 201])
                    ];

                    log_message("debug", "data_order_ghl" . json_encode($createTransactionWebhookLog));
                    $gohightlevel_client->logGhlWebhook($result_gohighlevel, $result->id, $createTransactionWebhookLog);

                    if ($result->webhooks_verify_payment != "Success") {
                        if (!empty($result->id)) {
                            $status_update = $smsParserModel
                                ->set(['webhooks_verify_payment' => 'Success'])
                                ->where(['id' => $result->id])
                                ->update();

                            if ($status_update) {
                                return $this->response->setJSON([
                                    'success' => 'true',
                                    'pay_status' => 'true',
                                    'chargeId' => $result->reference_number
                                ]);
                            }
                        } else {
                            log_message("debug", "ID không hợp lệ cho bản ghi: " . json_encode($result));
                        }
                    }

                    return $this->response->setJSON([
                        'success' => 'true',
                        'pay_status' => 'true',
                        'chargeId' => $result->reference_number
                    ]);
                }
            }
        }

        
       if (is_object($va_details)) {
            $results_gohighlevel = $goHighLevelModel
                ->select('tb_autopay_gohighlevel.*')
                ->join('tb_autopay_bank_sub_account', 'tb_autopay_bank_sub_account.id = tb_autopay_gohighlevel.va_id')
                ->where([
                    'tb_autopay_gohighlevel.active' => 1,
                    'tb_autopay_bank_sub_account.sub_account' => $account_number
                ])
                ->orderBy('tb_autopay_gohighlevel.id', 'DESC')
                ->get()
                ->getResult();

            if (empty($results_gohighlevel)) {
                return $this->response->setJSON([
                    'success' => 'false',
                    'message' => 'integration not find'
                ]);
            }

            foreach ($results_gohighlevel as $result_gohighlevel) {
                $result = $transactionModel
                    ->where([
                        'sub_account' => $account_number,
                        'amount_in' => $amount_in
                    ])
                    ->like("transaction_content", $code)
                    ->orderBy('id', 'DESC')
                    ->get()
                    ->getRow();
                log_message("debug", "result" . json_encode($result_gohighlevel));        
                if (!is_object($result)) {
                    continue;
                }

                $gohightlevel_client = new GoHighLevelClient();
                $data_order_ghl = $gohightlevel_client->orderDetail($result_gohighlevel, $charge_id);

                if (empty($data_order_ghl['status'])) {
                    if ($result->webhooks_verify_payment != "Success" && $result->webhooks_verify_payment != "Failed") {
                        $smsParserModel
                            ->set(['webhooks_verify_payment' => 'Failed'])
                            ->where(['id' => $result->id])
                            ->update();

                        $createTransactionWebhookLog = [
                            'request_method' => $data_order_ghl['request']['method'],
                            'request_url' => $data_order_ghl['request']['url'],
                            'request_header' => json_encode($data_order_ghl['request']['headers']),
                            'request_body' => json_encode($data_order_ghl['request']['json']),
                            'response_body' => $data_order_ghl['raw_response'] ?? ($data_order_ghl['response'] ?? ''),
                            'status_code' => $data_order_ghl['status_code'],
                            'response_status_code' => $data_order_ghl['status_code'],
                            'connect_success' => in_array($data_order_ghl['status_code'], [200, 201])
                        ];

                        $gohightlevel_client->logGhlWebhook($result_gohighlevel, $result->id, $createTransactionWebhookLog);

                        return $this->response->setJSON([
                            'success' => 'false',
                            'pay_status' => 'false',
                            'message' => "Error verify payment"
                        ]);
                    }

                    continue;
                }

                if ($amount_in != $data_order_ghl['amount']) {
                    return $this->response->setJSON([
                        'success' => 'false',
                        'message' => 'amount not match'
                    ]);
                }

                $createTransactionWebhookLog = [
                    'request_method' => $data_order_ghl['request']['method'],
                    'request_url' => $data_order_ghl['request']['url'],
                    'request_header' => json_encode($data_order_ghl['request']['headers']),
                    'request_body' => json_encode($data_order_ghl['request']['json']),
                    'response_body' => $data_order_ghl['response'],
                    'status_code' => $data_order_ghl['status_code'],
                    'response_status_code' => $data_order_ghl['status_code'],
                    'connect_success' => in_array($data_order_ghl['status_code'], [200, 201])
                ];

                log_message("debug", "data_order_ghl" . json_encode($createTransactionWebhookLog));
                $gohightlevel_client->logGhlWebhook($result_gohighlevel, $result->id, $createTransactionWebhookLog);

                if ($result->webhooks_verify_payment != "Success") {
                    if (!empty($result->id)) {
                        $status_update = $smsParserModel
                            ->set(['webhooks_verify_payment' => 'Success'])
                            ->where(['id' => $result->id])
                            ->update();

                        if ($status_update) {
                            return $this->response->setJSON([
                                'success' => 'true',
                                'pay_status' => 'true',
                                'chargeId' => $result->reference_number
                            ]);
                        }
                    } else {
                        log_message("debug", "ID không hợp lệ cho bản ghi: " . json_encode($result));
                    }
                }

                return $this->response->setJSON([
                    'success' => 'true',
                    'pay_status' => 'true',
                    'chargeId' => $result->reference_number
                ]);
            }
        }
        return $this->response->setJSON(['success' => 'false','pay_status' => 'false']);
    }    
}