<?php

namespace App\Controllers\Userapi\Bidv;

use App\Exceptions\TooManyRequestsException;
use App\Models\BankAccountModel;
use App\Models\BidvEnterpriseAccountModel;
use App\Models\CompanyApiModel;
use App\Models\VAModel;
use App\Models\VAOrderModel;
use CodeIgniter\HTTP\Exceptions\HTTPException;
use CodeIgniter\RESTful\ResourceController;
use Config\Bidv;
use Config\Services;
use tttran\viet_qr_generator\Generator;

class Order extends ResourceController
{
    protected Bidv $config;

    protected VAModel $vaModel;

    protected VAOrderModel $vaOrderModel;

    protected $companyId;

    public function __construct()
    {
        $this->config = config(Bidv::class);
        $this->vaModel = model(VAModel::class);
        $this->vaOrderModel = model(VAOrderModel::class);
    }

    public function index($bankAccountId = null)
    {
        try {
            $this->companyId = $this->authorize();
        } catch (TooManyRequestsException $e) {
            return $this->response
                ->setHeader('X-SePay-UserApi-Retry-After', $e->getTimeLeft())
                ->setStatusCode(429);
        }

        if (! is_numeric($this->companyId)) {
            return $this->failUnauthorized();
        }

        $bankAccount = $this->getBankAccount($bankAccountId);

        if (! $bankAccount) {
            return $this->failNotFound();
        }

        $data = [];

        $orders = $this->vaOrderModel
            ->select(['id', 'order_code', 'amount', 'paid_amount', 'status', 'created_at'])
            ->where('company_id', $this->companyId)
            ->where('bank_account_id', $bankAccount->id)
            ->orderBy('created_at', 'DESC')
            ->paginate();

        if (empty($orders)) {
            return $this->respond(['status' => 'success', 'data' => []]);
        }

        $orderVas = $this->vaModel
            ->whereIn('order_id', array_column($orders, 'id'))
            ->findAll();

        $vasGroupedByOrderId = [];
        foreach ($orderVas as $va) {
            $vasGroupedByOrderId[$va->order_id][] = [
                'va_number' => $va->va_number,
                'va_holder_name' => $va->va_holder_name ?: $bankAccount->account_holder_name,
                'amount' => $va->amount === null ? null : (float) $va->amount,
                'status' => $va->status,
                'expired_at' => $va->expired_at,
                'paid_at' => $va->paid_at,
            ];
        }

        $data = array_map(function ($order) use ($bankAccount, $vasGroupedByOrderId) {
            return [
                'id' => $order->id,
                'order_code' => $order->order_code,
                'amount' => $order->amount === null ? null : (float) $order->amount,
                'paid_amount' => (float) $order->paid_amount,
                'status' => $order->status,
                'created_at' => $order->created_at,
                'bank_name' => $bankAccount->bank_name,
                'account_number' => $bankAccount->account_number,
                'account_holder_name' => $bankAccount->account_holder_name,
                'va' => $vasGroupedByOrderId[$order->id] ?? [],
            ];
        }, $orders);

        $pager = $this->vaOrderModel->pager;

        return $this->respond([
            'status' => 'success',
            'data' => [
                'orders' => $data,
                'pagination' => [
                    'total' => $pager->getTotal(),
                    'per_page' => $pager->getPerPage(),
                    'current_page' => $pager->getCurrentPage(),
                    'last_page' => $pager->getPageCount(),
                ],
            ],
        ]);
    }

    public function create($bankAccountId = null)
    {
        try {
            $this->companyId = $this->authorize();
        } catch (TooManyRequestsException $e) {
            return $this->response
                ->setHeader('X-SePay-UserApi-Retry-After', $e->getTimeLeft())
                ->setStatusCode(429);
        }

        if (! is_numeric($this->companyId)) {
            return $this->failUnauthorized();
        }

        $bankAccount = $this->getBankAccount($bankAccountId);

        if (! $bankAccount) {
            return $this->failNotFound();
        }

        $data = [
            'order_code' => trim($this->request->getVar('order_code')),
            'amount' => $this->request->getVar('amount') !== null ? floatval(trim($this->request->getVar('amount'))) : null,
            'va_holder_name' => $this->request->getVar('va_holder_name'),
            'duration' => $this->request->getVar('duration') ? trim($this->request->getVar('duration')) : null,
            'with_qrcode' => $this->request->getVar('with_qrcode'),
            'qrcode_template' => $this->request->getVar('qrcode_template'),
        ];

        $maxDuration = $this->config->vaOrderLifeTime ?? ********;

        $rules = [
            'order_code' => 'permit_empty|alpha_numeric|min_length[6]|max_length[50]',
            'amount' => 'permit_empty|is_natural_no_zero|max_length[12]',
            'duration' => 'permit_empty|is_natural_no_zero|max_length[8]|less_than_equal_to[' . $maxDuration . ']',
            'with_qrcode' => 'permit_empty|in_list[0,1]',
            'qrcode_template' => 'permit_empty|in_list[compact,qronly]',
        ];

        $bidvEnterpriseAccount = model(BidvEnterpriseAccountModel::class)
            ->where('bank_account_id', $bankAccount->id)
            ->first();

        if (! $bidvEnterpriseAccount) {
            return $this->respond([
                'status' => 'error',
                'message' => 'The selected bank account is must be Enterprise account',
            ], 400);
        }

        if ($bidvEnterpriseAccount->custom_va_name) {
            $rules['va_holder_name'] = [
                'label' => 'Tên hiển thị',
                'rules' => ['permit_empty', 'regex_match[/^[A-Z0-9\s]{' . $this->config->vaNameMinlen . ',' . $this->config->vaNameMaxlen . '}$/]'],
            ];
        }

        if (! $this->validateData($data, $rules)) {
            return $this->respond([
                'status' => 'error',
                'message' => 'Invalid data',
                'errors' => $this->validator->getErrors(),
            ], 400);
        }

        $existingOrderCode = $this->vaOrderModel
            ->where('company_id', $this->companyId)
            ->where('order_code', $data['order_code'])
            ->first();

        if ($existingOrderCode) {
            log_message('error', '[BIDV Order] Order code already exists: ' . $data['order_code']);

            return $this->respond([
                'status' => 'error',
                'message' => 'Order code already exists',
            ], 400);
        }

        helper(['general', 'text']);

        $prefixId = $this->getPrefixId($bidvEnterpriseAccount);
        $duration = $data['duration'] ? intval($data['duration']) : null;
        $expiredAt = $duration ? date('Y-m-d H:i:s', strtotime('+' . $duration . ' seconds')) : null;

        $vaNumber = $this->generateUniqueVaNumber($prefixId);
        $orderCode = ! empty($data['order_code']) ? $data['order_code'] : $this->generateUniqueOrderCode();
        $orderId = uuid();

        $this->vaOrderModel->insert([
            'id' => $orderId,
            'company_id' => $this->companyId,
            'bank_account_id' => $bankAccount->id,
            'order_code' => $orderCode,
            'amount' => $data['amount'],
            'status' => 'Pending',
        ]);

        $this->vaModel->insert([
            'id' => uuid(),
            'order_id' => $orderId,
            'va_number' => $vaNumber,
            'va_holder_name' => ($bidvEnterpriseAccount->custom_va_name && ! empty($data['va_holder_name'])) ? $data['va_holder_name'] : null,
            'amount' => $data['amount'],
            'status' => 'Unpaid',
            'expired_at' => $expiredAt,
        ]);

        $this->cacheVa($vaNumber, $duration ?? DECADE);

        $data = [
            'order_id' => $orderId,
            'order_code' => $orderCode,
            'va_number' => $vaNumber,
            'va_holder_name' => ($bidvEnterpriseAccount->custom_va_name && ! empty($data['va_holder_name'])) ? $data['va_holder_name'] : $bankAccount->account_holder_name,
            'amount' => $data['amount'],
            'status' => 'Pending',
            'bank_name' => $bankAccount->bank_name,
            'account_holder_name' => $bankAccount->account_holder_name,
            'account_number' => $bankAccount->account_number,
            'expired_at' => $expiredAt,
        ];

        if ($this->request->getVar('with_qrcode')) {
            $qrCodeGenerator = new Generator();

            $qrCode = $qrCodeGenerator
                ->bankId('BIDV')
                ->accountNo($vaNumber);

            if ($data['amount'] !== null) {
                $qrCode->amount($data['amount']);
            }

            $qrCode = $qrCode->returnText(false)->generate();

            $encodedQrCode = null;
            $qrCode = json_decode($qrCode, true);

            if ($qrCode && isset($qrCode['data'])) {
                $encodedQrCode = $qrCode['data'];
            }

            $data['qr_code'] = $encodedQrCode;
            $data['qr_code_url'] = 'https://qr.sepay.vn/img?' . http_build_query(array_filter([
                'acc' => $vaNumber,
                'bank' => $bankAccount->bank_name,
                'amount' => $data['amount'],
                'template' => $this->request->getVar('qrcode_template') ?: 'compact',
            ]));
        }

        return $this->respondCreated([
            'status' => 'success',
            'message' => 'Order created successfully',
            'data' => $data,
        ]);
    }

    public function show($bankAccountId = null, $id = null)
    {
        try {
            $this->companyId = $this->authorize();
        } catch (TooManyRequestsException $e) {
            return $this->response
                ->setHeader('X-SePay-UserApi-Retry-After', $e->getTimeLeft())
                ->setStatusCode(429);
        }

        if (! is_numeric($this->companyId)) {
            return $this->failUnauthorized();
        }

        $bankAccount = $this->getBankAccount($bankAccountId);

        if (! $bankAccount) {
            return $this->failNotFound();
        }

        $order = $this->vaOrderModel
            ->select([
                'tb_autopay_va_order.id',
                'tb_autopay_va_order.order_code',
                'tb_autopay_va_order.amount',
                'tb_autopay_va_order.paid_amount',
                'tb_autopay_va_order.status',
                'tb_autopay_va_order.created_at',
            ])
            ->where('tb_autopay_va_order.company_id', $this->companyId)
            ->where('tb_autopay_va_order.bank_account_id', $bankAccount->id)
            ->where('tb_autopay_va_order.id', $id)
            ->first();

        if (! $order) {
            return $this->failNotFound();
        }

        $orderVas = $this->vaModel
            ->where('order_id', $order->id)
            ->findAll();

        $data = [
            'id' => $order->id,
            'order_code' => $order->order_code,
            'amount' => $order->amount !== null ? (float) $order->amount : null,
            'paid_amount' => $order->paid_amount,
            'status' => $order->status,
            'created_at' => $order->created_at,
            'bank_name' => $bankAccount->bank_name,
            'account_number' => $bankAccount->account_number,
            'account_holder_name' => $bankAccount->account_holder_name,
            'va' => [],
        ];

        foreach ($orderVas as $va) {
            $data['va'][] = [
                'va_number' => $va->va_number,
                'va_holder_name' => $va->va_holder_name ?: $bankAccount->account_holder_name,
                'amount' => $va->amount !== null ? (float) $va->amount : null,
                'status' => $va->status,
                'expired_at' => $va->expired_at,
                'paid_at' => $va->paid_at,
            ];
        }

        return $this->respond([
            'status' => 'success',
            'data' => $data,
        ]);
    }

    public function delete($bankAccountId = null, $id = null)
    {
        try {
            $this->companyId = $this->authorize();
        } catch (TooManyRequestsException $e) {
            return $this->response
                ->setHeader('X-SePay-UserApi-Retry-After', $e->getTimeLeft())
                ->setStatusCode(429);
        }

        if (! is_numeric($this->companyId)) {
            return $this->failUnauthorized();
        }

        $bankAccount = $this->getBankAccount($bankAccountId);

        if (! $bankAccount) {
            return $this->failNotFound();
        }

        $order = $this->vaOrderModel
            ->select(['tb_autopay_va_order.id', 'tb_autopay_va_order.status'])
            ->where('tb_autopay_va_order.company_id', $this->companyId)
            ->where('tb_autopay_va_order.bank_account_id', $bankAccount->id)
            ->where('tb_autopay_va_order.id', $id)
            ->first();

        if (! $order) {
            return $this->failNotFound();
        }

        $va = $this->vaModel
            ->select('va_number')
            ->where('order_id', $order->id)
            ->where('status', 'Unpaid')
            ->orderBy('created_at', 'DESC')
            ->first();

        if ($order->status !== 'Pending') {
            return $this->respond([
                'status' => 'error',
                'message' => 'Current order status cannot be cancelled',
            ]);
        }

        $status = 'Cancelled';

        $this->vaOrderModel->update($order->id, ['status' => $status]);

        Services::cache()->delete("bidv_va_{$va->va_number}");

        return $this->respond([
            'status' => 'success',
            'message' => 'Order cancelled successfully',
            'data' => [
                'status' => $status,
            ],
        ]);
    }

    public function createVa($bankAccountId = null, $id = null)
    {
        try {
            $this->companyId = $this->authorize();
        } catch (TooManyRequestsException $e) {
            return $this->response
                ->setHeader('X-SePay-UserApi-Retry-After', $e->getTimeLeft())
                ->setStatusCode(429);
        }

        if (! is_numeric($this->companyId)) {
            return $this->failUnauthorized();
        }

        $bankAccount = $this->getBankAccount($bankAccountId);

        if (! $bankAccount) {
            return $this->failNotFound();
        }

        $order = $this->vaOrderModel
            ->select(['tb_autopay_va_order.id', 'tb_autopay_va_order.status', 'tb_autopay_va_order.amount', 'tb_autopay_va_order.paid_amount'])
            ->where('tb_autopay_va_order.company_id', $this->companyId)
            ->where('tb_autopay_va_order.bank_account_id', $bankAccount->id)
            ->where('tb_autopay_va_order.id', $id)
            ->first();

        if (! $order) {
            return $this->failNotFound();
        }

        if (! in_array($order->status, ['Pending', 'Partially'])) {
            return $this->respond([
                'status' => 'error',
                'message' => 'Current order status cannot create VA code',
            ]);
        }

        $data = [
            'amount' => $this->request->getVar('amount') !== null ? floatval(trim($this->request->getVar('amount'))) : null,
            'va_holder_name' => $this->request->getVar('va_holder_name'),
            'duration' => $this->request->getVar('duration') ? trim($this->request->getVar('duration')) : null,
        ];

        $bidvEnterpriseAccount = model(BidvEnterpriseAccountModel::class)
            ->where('bank_account_id', $bankAccount->id)
            ->first();

        if (! $bidvEnterpriseAccount) {
            return $this->respond([
                'status' => 'error',
                'message' => 'The selected bank account is must be Enterprise account',
            ], 400);
        }

        if ($bidvEnterpriseAccount->custom_va_name) {
            $subHolderNameRule = 'regex_match[/^[A-Z0-9\s]{' . $this->config->vaNameMinlen . ',' . $this->config->vaNameMaxlen . '}$/]';
            $rules['va_holder_name'] = ['label' => 'Tên hiển thị', 'rules' => ['required', $subHolderNameRule]];
        }

        $maxDuration = $this->config->vaOrderLifeTimeRule ?? ********;

        $rules = [
            'amount' => "permit_empty|is_natural|max_length[12]|less_than_equal_to[{$order->amount}]",
            'duration' => "permit_empty|is_natural_no_zero|max_length[8]|less_than_equal_to[{$maxDuration}]",
        ];

        $messages = [
            'amount' => [
                'less_than_equal_to' => 'VA amount must not exceed order amount',
            ],
        ];

        if (! $this->validateData($data, $rules, $messages)) {
            return $this->respond([
                'status' => 'error',
                'message' => 'Invalid data',
                'errors' => $this->validator->getErrors(),
            ]);
        }

        helper(['general', 'text']);

        $prefixId = $this->getPrefixId($bidvEnterpriseAccount);
        $vaNumber = $this->generateUniqueVaNumber($prefixId);
        $duration = $data['duration'] ? intval($data['duration']) : null;
        $expiredAt = $duration ? date('Y-m-d H:i:s', strtotime('+' . $duration . ' seconds')) : null;

        $this->vaModel->insert([
            'id' => uuid(),
            'order_id' => $order->id,
            'va_number' => $vaNumber,
            'va_holder_name' => $bidvEnterpriseAccount->custom_va_name ? $data['va_holder_name'] : null,
            'amount' => $data['amount'],
            'status' => 'Unpaid',
            'expired_at' => $expiredAt,
        ]);

        $this->cacheVa($vaNumber, $duration ?? DECADE);

        return $this->respondCreated([
            'status' => 'success',
            'message' => 'VA created successfully',
            'data' => [
                'va_number' => $vaNumber,
                'va_holder_name' => $bidvEnterpriseAccount->custom_va_name ? $data['va_holder_name'] : null,
                'amount' => $data['amount'],
                'status' => 'Unpaid',
                'expired_at' => $expiredAt,
            ],
        ]);
    }

    public function deleteVa($bankAccountId = null, $orderId = null, $vaNumber = null)
    {
        try {
            $this->companyId = $this->authorize();
        } catch (TooManyRequestsException $e) {
            return $this->response
                ->setHeader('X-SePay-UserApi-Retry-After', $e->getTimeLeft())
                ->setStatusCode(429);
        }

        if (! is_numeric($this->companyId)) {
            return $this->failUnauthorized();
        }

        $bankAccount = $this->getBankAccount($bankAccountId);

        if (! $bankAccount) {
            return $this->failNotFound();
        }

        $order = $this->vaOrderModel
            ->select(['tb_autopay_va_order.id', 'tb_autopay_va_order.status'])
            ->where('tb_autopay_va_order.company_id', $this->companyId)
            ->where('tb_autopay_va_order.bank_account_id', $bankAccount->id)
            ->where('tb_autopay_va_order.id', $orderId)
            ->first();

        if (! $order) {
            return $this->failNotFound();
        }

        $va = $this->vaModel
            ->select(['tb_autopay_va.id', 'tb_autopay_va.status'])
            ->where('tb_autopay_va.order_id', $order->id)
            ->where('tb_autopay_va.va_number', $vaNumber)
            ->first();

        if (! $va) {
            return $this->failNotFound();
        }

        if ($va->status !== 'Unpaid') {
            return $this->respond([
                'status' => 'error',
                'message' => 'Current VA status cannot be cancelled',
            ]);
        }

        $status = 'Cancelled';

        $this->vaModel->update($va->id, ['status' => $status]);

        Services::cache()->delete("bidv_va_$vaNumber");

        return $this->respond([
            'status' => 'success',
            'message' => 'VA cancelled successfully',
            'data' => [
                'status' => $status,
            ],
        ]);
    }

    protected function generateUniqueOrderCode()
    {
        $orderCode = strtoupper(random_string('alnum', 10));

        if ($this->vaOrderModel->where('company_id', $this->companyId)->where('order_code', $orderCode)->first()) {
            return $this->generateUniqueOrderCode();
        }

        return $orderCode;
    }

    protected function generateUniqueVaNumber($prefixId)
    {
        $prefixId .= $this->config->orderCodePrefix ?: 'ORD';
        $vaNumber = $prefixId . strtoupper(random_string('alnum', $this->config->vaMaxlen - strlen($prefixId)));

        if ($this->vaModel->where('va_number', $vaNumber)->first()) {
            return $this->generateUniqueVaNumber($prefixId);
        }

        return $vaNumber;
    }

    protected function getPrefixId($enterpriseAccount)
    {
        $vaPrefix = $enterpriseAccount->va_prefix ?: $this->config->vaPrefix;

        return $vaPrefix . strlen($enterpriseAccount->prefix_id) . $enterpriseAccount->prefix_id;
    }

    protected function getBankAccount($id = null)
    {
        if (! $id) {
            return null;
        }

        $bankAccount = model(BankAccountModel::class)
            ->select([
                'tb_autopay_bank_account.id',
                'tb_autopay_bank_account.account_number',
                'tb_autopay_bank_account.account_holder_name',
                'tb_autopay_bank.brand_name as bank_name',
            ])
            ->join('tb_autopay_bidv_enterprise_account', 'tb_autopay_bank_account.id = tb_autopay_bidv_enterprise_account.bank_account_id')
            ->join('tb_autopay_bank', 'tb_autopay_bank_account.bank_id = tb_autopay_bank.id')
            ->where('tb_autopay_bank_account.bank_id', 9)
            ->where('tb_autopay_bank_account.company_id', $this->companyId)
            ->where('tb_autopay_bank_account.active', true)
            ->where('tb_autopay_bank_account.id', $id)
            ->first();

        return $bankAccount;
    }

    protected function authorize()
    {
        $throttler = Services::throttler();
        $capacity = $this->config->vaOrderThrottleCapacity ?? 2;

        $tokenLeft = $throttler->check(md5($this->request->getIPAddress()), $capacity, SECOND);

        if ($tokenLeft === false) {
            $timeLeft = $throttler->getTokentime();

            throw TooManyRequestsException::make($timeLeft);
        }

        $authorizationHeader = $this->request->getServer('HTTP_AUTHORIZATION');

        $arr1 = explode(' ', $authorizationHeader);

        if (count($arr1) == 2 && $arr1[0] == 'Bearer' && strlen($arr1[1]) == 64) {
            $apiKey = $arr1[1];

            if (! ctype_alnum($apiKey)) {
                return $this->failUnauthorized('Invalid Auth token');
            }

            $companyApi = model(CompanyApiModel::class)
                ->select('tb_autopay_company_api.company_id')
                ->join('tb_autopay_company', 'tb_autopay_company.id = tb_autopay_company_api.company_id')
                ->where('tb_autopay_company_api.api_key', $apiKey)
                ->where('tb_autopay_company_api.active', true)
                ->where('tb_autopay_company.active', true)
                ->where('tb_autopay_company.status', 'Active')
                ->first();

            if (is_object($companyApi) && is_numeric($companyApi->company_id)) {
                return $companyApi->company_id;
            }

            return $this->failUnauthorized('Unauthorized Access');
        }
        
        return $this->failUnauthorized('Invalid Auth token');
    }

    protected function cacheVa(string $vaNumber, int $duration = 600)
    {
        $va = $this->vaModel
            ->select('tb_autopay_va.id, tb_autopay_va.va_number, tb_autopay_va.expired_at, tb_autopay_va.amount, tb_autopay_va.va_holder_name, tb_autopay_bank_account.account_holder_name, tb_autopay_va.status, tb_autopay_va_order.status as order_status')
            ->join('tb_autopay_va_order', 'tb_autopay_va_order.id = tb_autopay_va.order_id')
            ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id = tb_autopay_va_order.bank_account_id')
            ->where('tb_autopay_va.va_number', $vaNumber)
            ->first();

        if (! $va) {
            return;
        }

        Services::cache()->save("bidv_va_$vaNumber", $va, $duration);
    }
}
