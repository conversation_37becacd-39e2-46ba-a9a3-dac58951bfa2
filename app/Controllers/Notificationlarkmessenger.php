<?php

namespace App\Controllers;
use App\Models\BankAccountModel;
use App\Models\WebhooksModel;
use App\Models\TransactionsModel;
use App\Models\BankSubAccountModel;
use App\Models\NotificationLarkMessengerModel;
use App\Models\UserModel;
use App\Models\NotificationLarkMessengerQueueModel;
use App\Models\NotificationTelegramModel;
use App\Models\ShopModel;
use CodeIgniter\API\ResponseTrait;

use CodeIgniter\Controller;

class Notificationlarkmessenger extends BaseController
{
    use ResponseTrait;

    public function index()
    { 
        $data = [
            'page_title' => 'Lark Messenger',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        if(!has_permission('NotificationLarkMessenger', 'can_view_all'))
            show_404();

        $notificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);
        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $webhooksModel = model(WebhooksModel::class);

        $data['bank_accounts'] = $bankAccountModel->select("tb_autopay_bank_account.id, tb_autopay_bank.brand_name, tb_autopay_bank_account.account_holder_name, tb_autopay_bank_account.account_number")->join("tb_autopay_bank","tb_autopay_bank.id=tb_autopay_bank_account.bank_id","left")->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id']])->get()->getResult();
        
        $data['bank_sub_accounts'] = $bankSubAccountModel->select("tb_autopay_bank_sub_account.id, tb_autopay_bank.brand_name, tb_autopay_bank_account.account_holder_name, tb_autopay_bank_account.account_number,tb_autopay_bank_sub_account.sub_account, tb_autopay_bank_sub_account.label")->join("tb_autopay_bank_account","tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id")->join("tb_autopay_bank","tb_autopay_bank.id=tb_autopay_bank_account.bank_id","left")->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id']])->get()->getResult();
        
        $data['webhooks_count'] = $webhooksModel->where(['company_id' => $this->user_session['company_id']])->countAllResults();

        $session = service('session');
        $session->remove('notification_larkmessenger_integration');

        echo theme_view('templates/autopay/header',$data);
        echo theme_view('notificationlarkmessenger/index',$data);
        echo theme_view('templates/autopay/footer',$data);

    }

    public function all_ajax_list() {
     
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!has_permission('NotificationLarkMessenger', 'can_view_all'))
            show_404();
 
        $notificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);

        $larks = $notificationLarkMessengerModel->getDatatables($this->user_session['company_id']);
         
        $data = array();

        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');

        foreach ($larks as $lark) {

            $no++;
            $row = array();
            
            $row[] = $no;
            $row[] = esc($lark->id);
            $row[] = "<b>" . esc($lark->description) . "</b>";
          
            $event_text = '';
            if($lark->transaction_type == "In_only")
                $event_text = $event_text. "<span class='text-info'>Khi có tiền vào</span>";
            else if($lark->transaction_type == "Out_only")
                $event_text = $event_text. "<span class='text-info'>Khi có tiền ra</span>";
            else if($lark->transaction_type == "All")
                $event_text = $event_text. "<span class='text-info'>Khi có tiền vào hoặc Tiền ra</span>";
             
            $row[] = $event_text;

            $condition_text = '<ul>';

            if(strlen($lark->account_number) > 0)
                $condition_text = $condition_text. "<li><span class='text-info'>[Và] Khi tài khoản chính là <b>".esc($lark->account_number) . ' ' . esc($lark->brand_name)."</b></span> </li>";

            if(strlen($lark->sub_account) > 0)
                $condition_text = $condition_text. "<li><span class='text-info'>[Và] Khi tài khoản phụ là <b>". esc($lark->sub_account)."</b></span> </li>";

            
            if($lark->webhook_type == "Success")
                $condition_text = $condition_text. "<li><span class='text-info'>[Và] Khi WebHooks thành công</span></li>";
            else if($lark->webhook_type == "Failed")
                $condition_text = $condition_text. "<li><span class='text-info'>[Và] Khi WebHooks thất bại</span></li>";

            if($lark->verify_payment == "Success")
                $condition_text = $condition_text. "<li><span class='text-info'>[Và] Khi WebHooks xác thực thanh toán thành công</span></li>";
            else if($lark->verify_payment == "Failed")
                $condition_text = $condition_text. "<li><span class='text-info'>[Và] Khi WebHooks xác thực thanh toán thất bại</span></li>";
            else if($lark->verify_payment == "Success_Or_Failed")
                $condition_text = $condition_text. "<li><span class='text-info'>[Và] Khi WebHooks xác thực thanh toán Thất bại hoặc Thành công</span></li>";
            
            if($lark->ignore_phrases)
                $condition_text = $condition_text. "<li><span class='text-success'>[Bỏ qua] Khi WebHooks Có các từ: </span> " . esc(character_limiter($lark->ignore_phrases,50,'...')) . "</li>";
             
            $condition_text =  $condition_text .'</ul>';

            $row[] = $condition_text;
           // $row[] = esc($lark->bot_webhook_url);

          
            if($lark->active == 1)
                $row[] = "<span class='text-success'>Kích hoạt</span>";
            else
                $row[] = "<span class='text-secondary'>Tạm ngưng</span>";
            //$row[] = esc($lark->created_at);

            $action_text = "";
            if(has_permission('NotificationLarkMessenger', 'can_edit'))
                $action_text = "<a href='javascript:;' onclick='edit_lark(" . $lark->id. ")' class='btn btn-sm btn-outline-warning ms-2 me-2 mt-2'><i class='bi bi-pencil'></i> Sửa</a> <a href='".base_url('notificationlarkmessenger/edit_message/' . $lark->id)."' class='btn btn-sm btn-outline-info ms-2 me-2 mt-2'><i class='bi bi-chat-left-text'></i> Nội dung</a>";
            
            if(has_permission('NotificationLarkMessenger', 'can_delete'))
                $action_text = $action_text . "<a href='javascript:;' onclick='delete_lark(" . $lark->id. ")' class='btn btn-sm btn-outline-danger ms-2 mt-2'><i class='bi bi-trash3'></i> Xóa</a>";

            $row[] = $action_text;
       
       
            $data[] = $row;
        }
 
        $output = array(
            "draw" => $draw,
            "recordsTotal" => $notificationLarkMessengerModel->countAll($this->user_session['company_id']),
            "recordsFiltered" => $notificationLarkMessengerModel->countFiltered($this->user_session['company_id']),
            "data" => $data,
        );
        return $this->response->setJSON($output);
    
    
    }

    public function ajax_add() {

        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!has_permission('NotificationLarkMessenger', 'can_add'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền thêm"));

 
        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'bank_account_id' => 'integer|is_natural',
            //'sub_account_id' => 'integer|is_natural',
            'transaction_type' => "required|in_list[All,In_only,Out_only]",
            //'webhook_type' => "required|in_list[All,Success,Failed,No]",
            //'verify_payment' => "required|in_list[Skip,Success,Failed,Success_Or_Failed,No]",
            'contains_content' => ['label' => 'Khi nội dung thanh toán có từ', 'rules' => "max_length[5000]"],
            'description' => ['label' => 'Đặt tên', 'rules' => "required|min_length[2]|max_length[1000]"],
            'bot_webhook_url' => ['label' => 'Bot WebHook URL', 'rules' =>"required|min_length[50]|max_length[500]"],
            //'hide_accumulated' => "required|in_list[0,1]",
            //'hide_details_link' => "required|in_list[0,1]",
            'ignore_phrases' => ['label' => 'Bỏ qua nếu nội dung thanh toán có từ', 'rules' => "max_length[5000]"],
            'active' => "required|in_list[0,1]"
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        } 
        
        $bankAccountModel = model(BankAccountmodel::class);
        $bankSubAccountModel = model(BankSubAccountmodel::class);
        $notificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);

        $bank_account_id = $this->request->getPost('bank_account_id');
        $sub_account_id = $this->request->getPost('sub_account_id');

        if(is_numeric($bank_account_id) && $bank_account_id > 0) {
            $bankAccountModel = model(BankAccountmodel::class);
            $bank_account = $bankAccountModel->where(['company_id' => $this->user_session['company_id'], 'id'=>$bank_account_id])->countAllResults();
            if($bank_account != 1)
                return $this->response->setJSON(array('status'=>FALSE,'message'=>'Không tìm thấy tài khoản ngân hàng chính mà bạn chọn'));    
        }
      
        $sub_account_id = $this->request->getPost('sub_account_id');

        if(is_numeric($sub_account_id) && $sub_account_id > 0) {
            $sub_account = $bankSubAccountModel->join("tb_autopay_bank_account","tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id")->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id'], 'tb_autopay_bank_sub_account.id'=>$sub_account_id])->countAllResults();
            if($sub_account != 1)
                return $this->response->setJSON(array('status'=>FALSE,'message'=>'Không tìm thấy tài khoản ngân hàng phụ mà bạn chọn'));
        
        }

        $amount_in_less_than_equal_to = $this->request->getPost('amount_in_less_than_equal_to');
        $amount_in_great_than_equal_to = $this->request->getPost('amount_in_great_than_equal_to');

        if(!is_numeric($amount_in_less_than_equal_to) || $amount_in_less_than_equal_to <= 0)
            $amount_in_less_than_equal_to = 0;

        if(!is_numeric($amount_in_great_than_equal_to) || $amount_in_great_than_equal_to <= 0)
            $amount_in_great_than_equal_to = 0;
            
        if($amount_in_less_than_equal_to != 0 && $amount_in_great_than_equal_to != 0) {
            if($amount_in_great_than_equal_to > $amount_in_less_than_equal_to) {
                return $this->response->setJSON(array('status'=>FALSE,'message'=>"Vui lòng nhập giá trị 'Tiền vào lớn hơn hoặc bằng' phải nhỏ hơn điều kiện 'Tiền vào nhỏ hơn hoặc bằng'"));

            }
        }

       
        $data = array(
            'company_id' => $this->user_session['company_id'],
            'bank_account_id' => $this->request->getPost('bank_account_id'),
            //'sub_account_id' => $this->request->getPost('sub_account_id'),
            'transaction_type' => $this->request->getVar('transaction_type'),
            //'webhook_type' => $this->request->getVar('webhook_type'),
            //'verify_payment' => $this->request->getVar('verify_payment'),
            'contains_content' => $this->request->getVar('contains_content'),
            'bot_webhook_url' => trim($this->request->getVar('bot_webhook_url')),
            'description' => xss_clean($this->request->getVar('description')),
            //'hide_accumulated' => $this->request->getVar('hide_accumulated'),
            //'hide_details_link' => $this->request->getVar('hide_details_link'),
            'amount_in_less_than_equal_to' => $amount_in_less_than_equal_to,
            'amount_in_great_than_equal_to' => $amount_in_great_than_equal_to,
            'hide_accumulated' => 0,
            'hide_details_link' => 1,
            'ignore_phrases' => trim($this->request->getVar('ignore_phrases')),
            'active' => $this->request->getVar('active'),
        );

        if(is_numeric($sub_account_id) && $sub_account_id > 0)
            $data['sub_account_id'] = $sub_account_id;
        else
            $data['sub_account_id'] = 0;


        $webhook_type = $this->request->getVar('webhook_type');
        $verify_payment = $this->request->getVar('verify_payment');

        if(!in_array($webhook_type, ['All','Success','Failed','No']))
            $data['webhook_type'] = "No";
        else
            $data['webhook_type'] = $webhook_type;

        if(!in_array($verify_payment, ['Skip','Success','Failed','Success_Or_Failed','No']))
            $data['verify_payment'] = "Skip";
        else
            $data['verify_payment'] = $verify_payment;

        
                    
        $notificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);
        $result = $notificationLarkMessengerModel->insert($data);
        
        if($result) {
            add_user_log(array('data_id'=>$result,'company_id' => $this->user_session['company_id'], 'data_type'=>'notification_lark_mesenger_add','description'=>'Thêm tích hợp thông báo Lark Messenger','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
            return $this->response->setJSON(array("status"=>true,'id' => $result));
        } else {
            add_user_log(array('data_id'=>0,'company_id' => $this->user_session['company_id'], 'data_type'=>'notification_lark_mesenger_add','description'=>'Thêm tích hợp thông báo Lark Messenger','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Failed'));
            return $this->response->setJSON(array("status"=>false,"message"=>"Không thể thêm điều kiện này. Vui lòng liên hệ 123HOST để được hỗ trợ."));
        }
    
    }


    public function ajax_get($id='') {
        
        if(!has_permission('NotificationLarkMessenger', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền sửa dữ liệu này"));

        if(!is_numeric($id)) 
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"ID dữ liệu không hợp lệ"));
        
        $notificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);
        
        $result = $notificationLarkMessengerModel->where(['id'=>$id, 'company_id' => $this->user_session['company_id']])->get()->getRow();
        
        if($result)
            return $this->response->setJSON(["status"=>TRUE, "data"=>$result]);
        else
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Không tìm thấy dữ liệu này"));
    }

    public function ajax_update() {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!has_permission('NotificationLarkMessenger', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền sửa dữ liệu"));

 
        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'bank_account_id' => 'integer|is_natural',
            //'sub_account_id' => 'integer|is_natural',
            'id' => 'required|integer|is_natural',
            'transaction_type' => "required|in_list[All,In_only,Out_only]",
            //'webhook_type' => "required|in_list[All,Success,Failed,No]",
            'contains_content' => ['label' => 'Khi nội dung thanh toán có từ', 'rules' => "max_length[5000]"],
            //'verify_payment' => "required|in_list[Skip,Success,Failed,Success_Or_Failed,No]",
            'description' => ['label' => 'Đặt tên', 'rules' => "required|min_length[2]|max_length[1000]"],
            'bot_webhook_url' => ['label' => 'Bot Webhook URL', 'rules' =>"required|min_length[50]|max_length[500]"],
            //'hide_accumulated' => "required|in_list[0,1]",
            //'hide_details_link' => "required|in_list[0,1]",
          
            'ignore_phrases' => ['label' => 'Bỏ qua nếu nội dung thanh toán có từ', 'rules' => "max_length[5000]"],
            'active' => "required|in_list[0,1]"
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }

        $bankAccountModel = model(BankAccountmodel::class);
        $bankSubAccountModel = model(BankSubAccountmodel::class);
        $notificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);

        $bank_account_id = $this->request->getPost('bank_account_id');
        $sub_account_id = $this->request->getPost('sub_account_id');

        if(is_numeric($bank_account_id) && $bank_account_id > 0) {
            $bankAccountModel = model(BankAccountmodel::class);
            $bank_account = $bankAccountModel->where(['company_id' => $this->user_session['company_id'], 'id'=>$bank_account_id])->countAllResults();
            if($bank_account != 1)
                return $this->response->setJSON(array('status'=>FALSE,'message'=>'Không tìm thấy tài khoản ngân hàng chính mà bạn chọn'));    
        }
      
        $sub_account_id = $this->request->getPost('sub_account_id');

        if(is_numeric($sub_account_id) && $sub_account_id > 0) {
            $sub_account = $bankSubAccountModel->join("tb_autopay_bank_account","tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id")->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id'], 'tb_autopay_bank_sub_account.id'=>$sub_account_id])->countAllResults();
            if($sub_account != 1)
                return $this->response->setJSON(array('status'=>FALSE,'message'=>'Không tìm thấy tài khoản ngân hàng phụ mà bạn chọn'));
        
        }
        
        $lark_id = $this->request->getPost('id');
        
        $lark_details = $notificationLarkMessengerModel->where(["id" =>$lark_id,"company_id"=>$this->user_session['company_id']])->get()->getRow();

        if(!is_object($lark_details))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>'Không tìm thấy dữ liệu cần cập nhật'));

        $amount_in_less_than_equal_to = $this->request->getPost('amount_in_less_than_equal_to');
        $amount_in_great_than_equal_to = $this->request->getPost('amount_in_great_than_equal_to');

        if(!is_numeric($amount_in_less_than_equal_to) || $amount_in_less_than_equal_to <= 0)
            $amount_in_less_than_equal_to = 0;

        if(!is_numeric($amount_in_great_than_equal_to) || $amount_in_great_than_equal_to <= 0)
            $amount_in_great_than_equal_to = 0;
            
        if($amount_in_less_than_equal_to != 0 && $amount_in_great_than_equal_to != 0) {
            if($amount_in_great_than_equal_to > $amount_in_less_than_equal_to) {
                return $this->response->setJSON(array('status'=>FALSE,'message'=>"Vui lòng nhập giá trị 'Số tiền lớp hơn hoặc bằng' phải nhỏ hơn điều kiện 'số tiền nhỏ hơn hoặc bằng'"));

            }
        }

        $data = array(
            'bank_account_id' => $this->request->getPost('bank_account_id'),
            //'sub_account_id' => $this->request->getPost('sub_account_id'),
            'transaction_type' => $this->request->getVar('transaction_type'),
            //'webhook_type' => $this->request->getVar('webhook_type'),
            //'verify_payment' => $this->request->getVar('verify_payment'),
            'contains_content' => $this->request->getVar('contains_content'),
            'description' => xss_clean($this->request->getVar('description')),
            'bot_webhook_url' => trim($this->request->getVar('bot_webhook_url')),
            //'hide_accumulated' => $this->request->getVar('hide_accumulated'),
            //'hide_details_link' => $this->request->getVar('hide_details_link'),
            'amount_in_less_than_equal_to' => $amount_in_less_than_equal_to,
            'amount_in_great_than_equal_to' => $amount_in_great_than_equal_to,
            'ignore_phrases' => trim($this->request->getVar('ignore_phrases')),
            'active' => $this->request->getVar('active'),
        );

        if(is_numeric($sub_account_id) && $sub_account_id > 0)
            $data['sub_account_id'] = $sub_account_id;
        else
            $data['sub_account_id'] = 0;


        $webhook_type = $this->request->getVar('webhook_type');
        $verify_payment = $this->request->getVar('verify_payment');

        if(!in_array($webhook_type, ['All','Success','Failed','No']))
            $data['webhook_type'] = "No";
        else
            $data['webhook_type'] = $webhook_type;

        if(!in_array($verify_payment, ['Skip','Success','Failed','Success_Or_Failed','No']))
            $data['verify_payment'] = "Skip";
        else
            $data['verify_payment'] = $verify_payment;
            
        $result = $notificationLarkMessengerModel->set($data)->where(["id" =>$lark_id,"company_id"=>$this->user_session['company_id']])->update();
        
        if($result) { 
            add_user_log(array('data_id'=>$lark_id,'company_id' => $this->user_session['company_id'], 'data_type'=>'notification_lark_messenger_update','description'=>'Sửa tích hợp thông báo Lark Messenger','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
            return $this->response->setJSON(array("status"=>true));
        } else {
            add_user_log(array('data_id'=>$lark_id,'company_id' => $this->user_session['company_id'], 'data_type'=>'notification_lark_messenger_update','description'=>'Sửa tích hợp thông báo Lark Messenger','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Failed'));
            return $this->response->setJSON(array("status"=>false,"message"=>"Không thể cập nhật dữ liệu này!"));
        }
            

    
    }

    public function ajax_delete() {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!has_permission('NotificationLarkMessenger', 'can_delete'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền xóa dữ liệu này"));

 
        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'id' => 'required|integer|is_natural',
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }
        $lark_id = $this->request->getPost('id');

      
        $notificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);
        $lark_details = $notificationLarkMessengerModel->where(['id'=>$lark_id,'company_id'=>$this->user_session['company_id']])->get()->getRow();

       

        if(!is_object($lark_details))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Không tìm thấy dữ liệu này này"));

        $notificationLarkMessengerModel->delete($lark_id);

        add_user_log(array('data_id'=>$lark_id,'company_id' => $this->user_session['company_id'], 'data_type'=>'notification_lark_messenger_delete','description'=>'Xóa tích hợp thông báo Lark Messenger','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
    
        return $this->response->setJSON(array("status"=>true));
         
    
    }

    public function ajax_lark_test() {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!has_permission('NotificationLarkMessenger', 'can_add'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền thực hiện thao tác này"));

 
        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'bot_webhook_url' => 'required|min_length[3]',
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }
        $bot_webhook_url = $this->request->getPost('bot_webhook_url');

      
        $notificationLarkMessengerQueueModel = model(NotificationLarkMessengerQueueModel::class);
        
        $message = "[Test] Có giao dịch mới:

✳️ Tiền vào: 20,000,000 đ

➖ Tiền ra: 0 đ

📦 Số dư: 100,000,000 đ

⛪️ Tài khoản chính: ********* Vietcombank | Tài khoản phụ:

ℹ️ Nội dung thanh toán: Test giao dịch

#️⃣ Mã code thanh toán: 

⚓️ Mã tham chiếu: 0000.0000.0000.0001

⏰ Thời gian giao dịch: " . date('Y-m-d H:i:s');

        $result = $notificationLarkMessengerQueueModel->sendMessage($message, $bot_webhook_url,'text');

        if(isset($result['success']) && $result['success'] == TRUE) {
            if(isset($result['response'])) {

                $queue_last_log = $result['response'];

                $api_response = json_decode($result['response']);

                if($api_response) {
                    if(is_object($api_response) && isset($api_response->StatusMessage) && $api_response->StatusMessage == "success") {
                        return $this->response->setJSON(array("status"=>true));
                    }
                }
            }
        }

        add_user_log(array('data_id'=>0,'company_id' => $this->user_session['company_id'], 'data_type'=>'notification_lark_messenger_test','description'=>'Test gửi thông báo Lark Messenger','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));

        return $this->response->setJSON(array("status"=>FALSE,"message" => "Gửi tin nhắn đến Lark Messenger thất bại"));
    
         
    }

    public function edit_message($id='') {
        $data = [
            'page_title' => 'Lark Messenger',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        if(!has_permission('NotificationLarkMessenger', 'can_edit'))
            show_404();

        $notificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);
        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $webhooksModel = model(WebhooksModel::class);

        if(!is_numeric($id)) 
            show_404();
        
        $notificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);
        
        $data['lark_details'] = $notificationLarkMessengerModel->where(['id'=>$id, 'company_id' => $this->user_session['company_id']])->get()->getRow();
        
        if(!is_object( $data['lark_details']))
            show_404();

        $data['is_notice'] = $this->request->getGet('is_notice');
 
                
        echo view('templates/autopay/header',$data);
        echo view('notificationlarkmessenger/edit_message',$data);
        echo view('templates/autopay/footer',$data);
    }
 
    public function ajax_message_update() {

        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!has_permission('NotificationLarkMessenger', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền sửa dữ liệu"));

 
        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'id' => ['label' => 'ID', 'rules' => 'required|integer|is_natural'],
            'is_template_custom' => ['label' => 'Loại nội dung', 'rules' => "required|in_list[0,1]"],
            'select_template' => ['label' => 'Mẫu tin', 'rules' => "in_list[template_1,template_2,template_3]"],
            'content_custom' => ['label' => 'Tùy chỉnh nội dung', 'rules' => 'min_length[10]|max_length[1000]']
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }

        $notificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);

        $id = $this->request->getVar('id');
        $is_template_custom = $this->request->getVar('is_template_custom');
        $hide_accumulated = $this->request->getVar('hide_accumulated');
        $hide_details_link = $this->request->getVar('hide_details_link');
        

        $lark_details = $notificationLarkMessengerModel->where(['id'=>$id, 'company_id' => $this->user_session['company_id']])->get()->getRow();

        if(!is_object($lark_details))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>'Không tìm thấy tích hợp này'));    

        if($is_template_custom == 0) {
            if($hide_accumulated == "on")
                $is_hide_accumulated = 1;
            else 
                $is_hide_accumulated = 0;
            
            if($hide_details_link == "on")
                $is_hide_details_link = 1;
            else
                $is_hide_details_link = 0;
            
            $notificationLarkMessengerModel->set(['is_template_custom' => 0, 'hide_accumulated' => $is_hide_accumulated, 'hide_details_link' => $is_hide_details_link])->where(['id'=>$id, 'company_id' => $this->user_session['company_id']])->update();

        } else if($is_template_custom == 1)   {
            if(! $this->validate([
                'content_custom' => ['label' => 'Tùy chỉnh nội dung', 'rules' => 'required|min_length[10]|max_length[1000]']
            ])) {
                return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
            }
            $select_template = $this->request->getVar('select_template');

            $content_custom = $this->request->getVar('content_custom');

            $content_custom = strip_tags($content_custom);

          
            $notificationLarkMessengerModel->set(['is_template_custom' => 1,'template_custom' => $content_custom, 'template_name' => $select_template])->where(['id'=>$id, 'company_id' => $this->user_session['company_id']])->update();


        }
        return $this->response->setJSON(array('status'=>TRUE));
    }

    public function ajax_test_larkmessenger_connection()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!has_permission('NotificationLarkMessenger', 'can_add'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền thực hiện thao tác này"));

 
        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'bot_webhook_url' => 'required|min_length[3]',
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }
        $bot_webhook_url = $this->request->getPost('bot_webhook_url');

        return $this->testLarkMessengerConnection($bot_webhook_url);

        add_user_log(array('data_id'=>0,'company_id' => $this->user_session['company_id'], 'data_type'=>'notification_lark_messenger_test','description'=>'Test gửi thông báo Lark Messenger','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
    }

    public function step1()
    {
        if (!has_permission('NotificationLarkMessenger', 'can_add')) show_404();
 
        $session = service('session');
        $session->remove('notification_larkmessenger_integration');

        $data = [
            'page_title' => 'Lark Messenger',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        echo theme_view('templates/autopay/header',$data);
        echo theme_view('notificationlarkmessenger/integration/step1', $data);
        echo theme_view('templates/autopay/footer',$data);
    }

    public function ajax_step1()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!has_permission('NotificationLarkMessenger', 'can_add'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền thực hiện thao tác này']);
 
        $data = [
            'bot_webhook_url' => trim($this->request->getPost('bot_webhook_url')),
            'description' => trim($this->request->getPost('description')),
            'transaction_type' => trim($this->request->getPost('transaction_type')),
        ];

        $rules = [
            'bot_webhook_url' => ['required','min_length[50]','max_length[500]'],
            'description' => ['required', 'max_length[100]'],
            'transaction_type' => ['required','in_list[All,In_only,Out_only]'],
        ];

        if (! $this->validateData($data, $rules)) {
            return $this->fail($this->validator->getErrors());
        }

        $session = service('session');
        $session->set('notification_larkmessenger_integration', $data);

        return $this->respond(['status' => true]);
    } 

    public function step2()
    {
        if (!has_permission('NotificationLarkMessenger', 'can_add')) show_404();

        $data = [
            'page_title' => 'Lark Messenger',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        $session = service('session');

        $notificationLarkMessengerIntegration = $session->get('notification_larkmessenger_integration');

        if (!$notificationLarkMessengerIntegration 
        || !array_key_exists('bot_webhook_url', $notificationLarkMessengerIntegration) 
        || !array_key_exists('transaction_type', $notificationLarkMessengerIntegration)
        || !array_key_exists('description', $notificationLarkMessengerIntegration)) {
            return redirect()->to('notificationlarkmessenger/step1');
        }

        $shopModel = model(ShopModel::class);

        $data['shops'] = $shopModel
        ->select(['tb_autopay_shop.name', 'tb_autopay_shop.id', 'tb_autopay_shop.address'])
        ->where([
            'tb_autopay_shop.company_id' => $this->company_details->id,
            'tb_autopay_shop.active' => 1
            ])
        ->orderBy('tb_autopay_shop.id', 'DESC')
        ->get()
        ->getResult();

        echo theme_view('templates/autopay/header',$data);
        echo theme_view('notificationlarkmessenger/integration/step2', $data);
        echo theme_view('templates/autopay/footer',$data);
    }

    public function ajax_shop_step2()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!has_permission('NotificationLarkMessenger', 'can_add'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền thực hiện thao tác này']);
 
        $data = [
            'shop_id_list' => $this->request->getPost('shop_id_list'),
        ];

        if (!count($data['shop_id_list'])) {
            return $this->response->setJSON(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng tải lại trang 1','test'=> $data]);
        }

        $session = service('session');

        $shopModel = model(ShopModel::class);

        $shops = $shopModel->select('tb_autopay_shop.name, tb_autopay_bank_shop_link.shop_id, tb_autopay_bank_shop_link.bank_account_id, tb_autopay_bank_shop_link.bank_sub_account_id, tb_autopay_shop.company_id')
        ->join('tb_autopay_bank_shop_link', 'tb_autopay_shop.id = tb_autopay_bank_shop_link.shop_id')
        ->where(['tb_autopay_shop.company_id' => $this->company_details->id])
        ->whereIn('tb_autopay_bank_shop_link.shop_id', $data['shop_id_list'])
        ->orderBy('tb_autopay_shop.id', 'ASC')
        ->get()
        ->getResult();

        if (!$shops) return $this->failNotFound();

        $notificationLarkMessengerIntegration = $session->get('notification_larkmessenger_integration');
        $notificationLarkMessengerIntegration['shop_id_list'] = $data['shop_id_list'];
        $notificationLarkMessengerIntegration['bank_account_id_list'] = $shops;

        $session->set('notification_larkmessenger_integration', $notificationLarkMessengerIntegration);

        return $this->respond(['status' => true]);
    }

    public function ajax_shop_step3()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!has_permission('NotificationLarkMessenger', 'can_add'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền thực hiện thao tác này']);

        $validation =  \Config\Services::validation();
        helper('text');

        $data = [
            'is_template_custom' => $this->request->getVar('is_template_custom'),
            'template_custom' => $this->request->getVar('template_custom'),
            'template_name' => $this->request->getVar('template_name'),
            'hide_accumulated' => $this->request->getVar('hide_accumulated'),
            'hide_details_link' => $this->request->getVar('hide_details_link')
        ];

        if (!in_array($data['template_name'], ['template_1', 'template_2', 'template_3'])) {
            return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng tải lại trang']);
        }

        if ($data['is_template_custom'] && !trim($data['template_custom'])) {
            if(! $this->validate([
                'template_custom' => ['label' => 'Tùy chỉnh nội dung', 'rules' => 'min_length[10]|max_length[1000]'],
            ])) {
                return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
            }
        }

        $session = service('session');

        $notificationLarkMessengerIntegration = $session->get('notification_larkmessenger_integration');

        if (!$notificationLarkMessengerIntegration 
            || !array_key_exists('bot_webhook_url', $notificationLarkMessengerIntegration) 
            || !array_key_exists('description', $notificationLarkMessengerIntegration) 
            || !array_key_exists('transaction_type', $notificationLarkMessengerIntegration)
            || !array_key_exists('bank_account_id_list', $notificationLarkMessengerIntegration)
            || !array_key_exists('shop_id_list', $notificationLarkMessengerIntegration)) {
            return redirect()->to('notificationlarkmessenger/step1');
        }

        $data = array_merge($data, $notificationLarkMessengerIntegration);

        foreach ($data['bank_account_id_list'] as $bankAccountId) {
            $notificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);

            $existingNotification = $notificationLarkMessengerModel->where([
                'company_id' => $this->user_session['company_id'],
                'bank_account_id' => $bankAccountId->bank_account_id,
                'sub_account_id' => $bankAccountId->bank_sub_account_id,
                'bot_webhook_url' => $data['bot_webhook_url'],
            ])->first();

            $safeNotificationLarkMessengerData = [
                'company_id' => $this->user_session['company_id'],
                'bank_account_id' => $bankAccountId->bank_account_id,
                'sub_account_id' => $bankAccountId->bank_sub_account_id,
                'transaction_type' => $data['transaction_type'],
                'webhook_type' => 'No',
                'verify_payment' => 'No',
                'bot_webhook_url' => $data['bot_webhook_url'],
                'description' => $data['description'],
                'hide_accumulated' => $data['hide_accumulated'] ? 1 : 0,
                'hide_details_link' => $data['hide_details_link'] ? 1 : 0,
                'is_template_custom' => $data['is_template_custom'] ? 1 : 0,
                'template_custom' => strip_tags($data['template_custom']),
                'template_name' => $data['template_name'],
            ];

            if ($existingNotification) {
                // Cập nhật bản ghi nếu tồn tại
                $notificationLarkMessengerModel->update($existingNotification->id, $safeNotificationLarkMessengerData);
                add_user_log(['data_id' => $existingNotification->id, 'company_id' => $this->user_session['company_id'], 'data_type' => 'notification_lark_messenger_update', 'description' => 'Cập nhật tích hợp thông báo Lark Messenger', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);
            } else {
                // Thêm mới nếu không tồn tại
                $notificationLarkMessengerId = $notificationLarkMessengerModel->insert($safeNotificationLarkMessengerData);

                if ($notificationLarkMessengerId) {
                    add_user_log(['data_id' => $notificationLarkMessengerId, 'company_id' => $this->user_session['company_id'], 'data_type' => 'notification_lark_messenger_add', 'description' => 'Thêm tích hợp thông báo Lark Messenger', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);
                } else {
                    add_user_log(['data_id' => 0, 'company_id' => $this->user_session['company_id'], 'data_type' => 'notification_lark_messenger_add', 'description' => 'Thêm tích hợp thông báo Lark Messenger', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Failed']);
                }
            }
        }

        return $this->respond(['status' => true]);
    }

    public function step3()
    {
        if (!has_permission('NotificationLarkMessenger', 'can_add')) show_404();

        $data = [
            'page_title' => 'Lark Messenger',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        $session = service('session');

        $notificationLarkMessengerIntegration = $session->get('notification_larkmessenger_integration');

        if (!$notificationLarkMessengerIntegration 
        || !array_key_exists('bot_webhook_url', $notificationLarkMessengerIntegration) 
        || !array_key_exists('description', $notificationLarkMessengerIntegration) 
        || !array_key_exists('transaction_type', $notificationLarkMessengerIntegration)
        || !array_key_exists('bank_account_id_list', $notificationLarkMessengerIntegration)
        || !array_key_exists('shop_id_list', $notificationLarkMessengerIntegration)) {
            return redirect()->to('notificationlarkmessenger/step1');
        }

        $data['notification_larkmessenger_integration'] = $notificationLarkMessengerIntegration;

        echo theme_view('templates/autopay/header',$data);
        echo theme_view('notificationlarkmessenger/integration/step3', $data);
        echo theme_view('templates/autopay/footer',$data);
    }

    public function ajax_test_larkmessenger_message()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!has_permission('NotificationLarkMessenger', 'can_add'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền thực hiện thao tác này']);

        $data = [
            'template_custom' => $this->request->getVar('template_custom'),
            'is_template_custom' => $this->request->getVar('is_template_custom'),
            'hide_accumulated' => $this->request->getVar('hide_accumulated'),
            'hide_details_link' => $this->request->getVar('hide_details_link')
        ];

        if ($data['is_template_custom'] && !trim($data['template_custom'])) {
            return $this->fail(['template_custom' => 'Nội dung tin nhắn là bắt buộc']);
        }

        $session = service('session');

        $notificationLarkMessengerIntegration = $session->get('notification_larkmessenger_integration');

        if (!$notificationLarkMessengerIntegration 
        || !array_key_exists('bot_webhook_url', $notificationLarkMessengerIntegration) 
        || !array_key_exists('description', $notificationLarkMessengerIntegration) 
        || !array_key_exists('bank_account_id_list', $notificationLarkMessengerIntegration)
        || !array_key_exists('shop_id_list', $notificationLarkMessengerIntegration)) {
            return redirect()->to('notificationtelegram/step1');
        }

        $transactionDetails = (object) [
            'id' => '103896',
            'accumulated' => '********',
            'amount_in' => '********',
            'amount_out' => 0,
            'account_number' => '*************',
            'brand_name' => 'Vietcombank',
            'transaction_content' => 'TRAN ANH DUONG chuyen tien',
            'code' => 'HD1029148',
            'reference_number' => '171298.050723.020002',
            'transaction_date' => '2023-07-05 13:59:48',
            'sub_account' => null,
        ];

        $telegramDetails = (object) [
            'template_custom' => strip_tags($data['template_custom'])
        ];

        if (!$data['is_template_custom']) {
            $accumulated = "

⛳️ Số dư: " . number_format($transactionDetails->accumulated) . ' đ
';
            $detailsLink = '
            
Xem <a href="https://my.sepay.vn">chi tiết</a>.
';

            if ($data['hide_accumulated'])
                $accumulated = "
";

            if ($data['hide_details_link'])
                $detailsLink = "
";

$message = "--------- - ID: " . $transactionDetails->id . " ---------
Có giao dịch mới:

✳️ Tiền vào: " . number_format($transactionDetails->amount_in) . " đ

➖ Tiền ra: " . number_format($transactionDetails->amount_out) . " đ" . $accumulated . 
"
⛪️ Tài khoản chính: " . $transactionDetails->account_number . " " . $transactionDetails->brand_name . "

ℹ️ Nội dung thanh toán: " . $transactionDetails->transaction_content . "

#️⃣ Mã code thanh toán: " . $transactionDetails->code . "

⚓️ Mã tham chiếu: " . $transactionDetails->reference_number . "

⏰ Giao dịch lúc: " . $transactionDetails->transaction_date . "
------------------------------";
        } else {
            $message = model(NotificationLarkMessengerModel::class)->getMessage($transactionDetails, $telegramDetails);
        }

        return $this->testLarkMessengerConnection($notificationLarkMessengerIntegration['bot_webhook_url'], $message);
    }

    protected function testLarkMessengerConnection($bot_webhook_url, $message = null)
    {
        $notificationLarkMessengerQueueModel = model(NotificationLarkMessengerQueueModel::class);

        $message = $message ?? "✅ Kết nối thành công. Hãy đi tới bước tiếp theo!";
        $result = $notificationLarkMessengerQueueModel->sendMessage($message, $bot_webhook_url, 'text');

        if(isset($result['success']) && $result['success'] == TRUE) {
            if(isset($result['response'])) {
                $api_response = json_decode($result['response']);

                if($api_response) {
                    if(is_object($api_response) && isset($api_response->StatusMessage) && $api_response->StatusMessage == "success") {
                        return $this->response->setJSON(array("status"=>true));
                    }
                }
            }
        }

        return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ']);
    }

    public function ajax_step3()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!has_permission('NotificationLarkMessenger', 'can_add'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền thực hiện thao tác này']);

        $data = [
            'is_template_custom' => $this->request->getVar('is_template_custom'),
            'template_custom' => $this->request->getVar('template_custom'),
            'template_name' => $this->request->getVar('template_name'),
            'hide_accumulated' => $this->request->getVar('hide_accumulated'),
            'hide_details_link' => $this->request->getVar('hide_details_link')
        ];

        if (!in_array($data['template_name'], ['template_1', 'template_2', 'template_3'])) {
            return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng tải lại trang']);
        }

        if ($data['is_template_custom'] && !trim($data['template_custom'])) {
            return $this->fail(['template_custom' => 'Nội dung tin nhắn là bắt buộc']);
        }

        $session = service('session');

        $notificationLarkMessengerIntegration = $session->get('notification_larkmessenger_integration');

        if (!$notificationLarkMessengerIntegration 
        || !array_key_exists('bot_webhook_url', $notificationLarkMessengerIntegration) 
        || !array_key_exists('description', $notificationLarkMessengerIntegration) 
        || !array_key_exists('transaction_type', $notificationLarkMessengerIntegration)
        || !array_key_exists('bank_account_id_list', $notificationLarkMessengerIntegration)
        || !array_key_exists('shop_id_list', $notificationLarkMessengerIntegration)) {
            return redirect()->to('notificationlarkmessenger/step1');
        }

        $data = array_merge($data, $notificationLarkMessengerIntegration);

        foreach ($data['bank_account_id_list'] as $bankAccountId) {
            $notificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);

            $safeNotificationLarkMessengerData = [
                'company_id' => $this->user_session['company_id'],
                'bank_account_id' => $bankAccountId,
                'sub_account_id' => 0,
                'transaction_type' => $data['transaction_type'],
                'webhook_type' => 'No',
                'verify_payment' => 'No',
                'bot_webhook_url' => $data['bot_webhook_url'],
                'message_thread_id' => $data['message_thread_id'],
                'description' => $data['description'],
                'hide_accumulated' => $data['hide_accumulated'] ? 1 : 0,
                'hide_details_link' => $data['hide_details_link'] ? 1 : 0,
                'is_template_custom' => $data['is_template_custom'] ? 1 : 0,
                'template_custom' => strip_tags($data['template_custom']),
                'template_name' => $data['template_name'],
            ];

            $notificationLarkMessengerId = $notificationLarkMessengerModel->insert($safeNotificationLarkMessengerData);

            if ($notificationLarkMessengerId) {
                add_user_log(['data_id' => $notificationLarkMessengerId, 'company_id' => $this->user_session['company_id'], 'data_type' => 'notification_lark_messenger_add', 'description' => 'Thêm tích hợp thông báo Lark Messenger', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);
            } else {
                add_user_log(['data_id' => 0, 'company_id' => $this->user_session['company_id'], 'data_type' => 'notification_lark_messenger_add', 'description' => 'Thêm tích hợp thông báo Lark Messenger', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Failed']);
            }
        }

        return $this->respond(['status' => true]);
    }

    public function step4()
    {
        if (!has_permission('NotificationLarkMessenger', 'can_add')) show_404();

        $data = [
            'page_title' => 'Lark Messenger',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        $session = service('session');

        $notificationLarkMessengerIntegration = $session->get('notification_larkmessenger_integration');

        if (!$notificationLarkMessengerIntegration 
        || !array_key_exists('bot_webhook_url', $notificationLarkMessengerIntegration) 
        || !array_key_exists('description', $notificationLarkMessengerIntegration) 
        || !array_key_exists('bank_account_id_list', $notificationLarkMessengerIntegration)
        || !array_key_exists('shop_id_list', $notificationLarkMessengerIntegration)) {
            return redirect()->to('notificationlarkmessenger/step1');
        }

        $shopModel = model(ShopModel::class);

        $data['shops'] = $shopModel->whereIn('id', $notificationLarkMessengerIntegration['shop_id_list'])->get()->getResult();

        if (count($data['shops']) != count($notificationLarkMessengerIntegration['shop_id_list'])) {
            return redirect()->to('notificationlarkmessenger/step1');
        }

        echo theme_view('templates/autopay/header',$data);
        echo theme_view('notificationlarkmessenger/integration/step4', $data);
        echo theme_view('templates/autopay/footer',$data);       
    }

    public function ajax_step4()
    {
        $shopId = trim(xss_clean($this->request->getGet('shop_id')));

        if (!$shopId || !is_numeric($shopId)) return $this->failNotFound();

        $shopModel = model(ShopModel::class);

        $shop = $shopModel->where('id', $shopId)->get()->getRow();

        if(!$shop) return $this->failNotFound();

        $vas = model(BankSubAccountModel::class)
        ->select(['tb_autopay_bank_sub_account.id', 'tb_autopay_bank_sub_account.label', 'tb_autopay_bank_sub_account.sub_holder_name', 'tb_autopay_bank_sub_account.sub_account', 'tb_autopay_bank_account.account_number'])
        ->join('tb_autopay_bank_shop_link', 'tb_autopay_bank_shop_link.bank_sub_account_id = tb_autopay_bank_sub_account.id')
        ->join('tb_autopay_bank_account', 'tb_autopay_bank_shop_link.bank_account_id = tb_autopay_bank_account.id')
        ->join('tb_autopay_shop', 'tb_autopay_shop.id = tb_autopay_bank_shop_link.shop_id')
        ->where('tb_autopay_shop.id', $shop->id)
        ->where('tb_autopay_shop.company_id', $this->user_session['company_id'])
        ->get()
        ->getResult();

        return $this->respond([
            'status' => TRUE,
            'data' => $vas
        ]);
    }
    
    public function ajax_shop_larkmessengers_list()
    {
        if ($this->request->getMethod(true) != 'POST')
        return '';

        if(!has_permission('NotificationLarkMessenger', 'can_view_all'))
            show_404();

        $notificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);

        $larkMessengers = $notificationLarkMessengerModel->getDatatablesShop($this->user_session['company_id']);
        
        $data = [];

        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');

        foreach ($larkMessengers as $larkMessenger) {

            $no++;
            $row = [];
            
            $row[] = $no;
            $row[] = esc($larkMessenger->id);
            $row[] = "<b>" . esc($larkMessenger->description) . "</b>";

            $condition_text = "";
            if(strlen($larkMessenger->shop_names) > 0)
                $condition_text = "<span class='text-info'>".esc($larkMessenger->shop_names)."</span>";

            $row[] = $condition_text;
        
            if($larkMessenger->active == 1)
                $row[] = "<span class='text-success'>Kích hoạt</span>";
            else
                $row[] = "<span class='text-danger'>Tạm ngưng</span>";

            $action_text = "";
            if(has_permission('NotificationLarkMessenger', 'can_edit'))
                $encoded_url = base64_encode($larkMessenger->bot_webhook_url);
                $action_text = "<a href=". base_url('notificationlarkmessenger/edit/'.$encoded_url) ." class='btn btn-sm btn-outline-warning ms-2 me-2 mt-2'><i class='bi bi-pencil'></i> Sửa</a>";
            
            if (has_permission('NotificationLarkMessenger', 'can_delete')) {
                $action_text .= "<a href='javascript:;' onclick='delete_larkmessenger(\"" .$larkMessenger->bot_webhook_url . "\")' class='btn btn-sm btn-outline-danger ms-2 mt-2'><i class='bi bi-trash3'></i> Xóa</a>";
            }                

            $row[] = $action_text;
    
            $data[] = $row;
        }

        $output = array(
            "draw" => $draw,
            "recordsTotal" => $notificationLarkMessengerModel->countFilteredShop($this->user_session['company_id']),
            "recordsFiltered" => $notificationLarkMessengerModel->countFilteredShop($this->user_session['company_id']),
            "data" => $data,
        );
        return $this->response->setJSON($output);
    }
    
    public function ajax_shop_larkmessenger_delete() 
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!has_permission('NotificationLarkMessenger', 'can_delete'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền xóa dữ liệu này"));

            $validation =  \Config\Services::validation();

            helper('text');
    
            if(! $this->validate([
                'id' => ['label' => 'Bot WebHook URL', 'rules' =>"required|min_length[50]|max_length[500]"]
            ])) {
                return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }
        $bot_webhook_url = trim($this->request->getPost('id'));

        $notificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);
        $larkmessenger_details = $notificationLarkMessengerModel->where(['bot_webhook_url' => $bot_webhook_url,'company_id'=>$this->user_session['company_id']])->get()->getResult();

        if(!is_array($larkmessenger_details))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Không tìm thấy dữ liệu này này"));

        foreach($larkmessenger_details as $r)
        {
            $notificationLarkMessengerModel->delete($r->id);
        }
        
        add_user_log(array('data_id'=> $r->id,'company_id' => $this->user_session['company_id'], 'data_type'=>'notification_lark_messenger_delete','description'=>'Xóa tích hợp thông báo Lark Messenger','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));

        return $this->response->setJSON(array("status"=>true));
    }

    public function edit($bot_webhook_url = null)
    {
        if (!has_permission('NotificationLarkMessenger', 'can_edit')) show_404();

        $bot_webhook_url = base64_decode($bot_webhook_url);

        if (!is_string($bot_webhook_url) || !filter_var($bot_webhook_url, FILTER_VALIDATE_URL)) 
            return show_404();
        

        $session = service('session');
        $session->remove('notification_larkmessenger_integration');

        $data = [
            'page_title' => 'Lark Messenger',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        $notificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);
        $shopModel = model(ShopModel::class);
        $larkmessenger_details = $notificationLarkMessengerModel->where(['bot_webhook_url'=>$bot_webhook_url,'company_id'=>$this->user_session['company_id']])->first();

        if(!is_object($larkmessenger_details))
            return redirect()->to('notificationlarkmessenger');

        $shops = $shopModel
        ->select(['tb_autopay_shop.name', 'tb_autopay_shop.id', 'tb_autopay_shop.address'])
        ->where([
            'tb_autopay_shop.company_id' => $this->company_details->id,
            'tb_autopay_shop.active' => 1
            ])
        ->orderBy('tb_autopay_shop.id', 'DESC')
        ->get()
        ->getResult();

        $shops_old = $shopModel
        ->select(['tb_autopay_shop.id'])
        ->join('tb_autopay_bank_shop_link', 'tb_autopay_bank_shop_link.shop_id = tb_autopay_shop.id')
        ->join('tb_autopay_notification_larkmessenger', 'tb_autopay_bank_shop_link.bank_account_id = tb_autopay_notification_larkmessenger.bank_account_id AND tb_autopay_bank_shop_link.bank_sub_account_id = tb_autopay_notification_larkmessenger.sub_account_id')
        ->where('tb_autopay_notification_larkmessenger.bot_webhook_url', $bot_webhook_url)
        ->where('tb_autopay_shop.company_id', $this->user_session['company_id'])
        ->groupBy('tb_autopay_shop.id')
        ->get()
        ->getResultArray();

        $shops_old = array_column($shops_old, 'id');

        $data['list_larkmessenger'] = $larkmessenger_details;
        $data['shops'] = $shops;
        $data['shops_old'] = $shops_old;

        echo theme_view('templates/autopay/header',$data);
        echo theme_view('notificationlarkmessenger/edit', $data);
        echo theme_view('templates/autopay/footer',$data);
    }
    public function ajax_edit_step1()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';
        
        if(!has_permission('NotificationLarkMessenger', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền sửa dữ liệu"));

        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'id' => 'required|min_length[50]|max_length[500]',
            'contains_content' => ['label' => 'Khi nội dung thanh toán có từ', 'rules' => "max_length[5000]"],
            'description' => ['label' => 'Đặt tên', 'rules' => "required|min_length[2]|max_length[1000]"],
            'transaction_type' => ['label' => 'Loại giao dịch', 'rules' => "required|in_list[All,In_only,Out_only]"],
            'bot_webhook_url' => ['label' => 'Bot WebHook URL', 'rules' =>"required|min_length[50]|max_length[500]"],
            'ignore_phrases' => ['label' => 'Bỏ qua nếu nội dung thanh toán có từ', 'rules' => "max_length[5000]"],
            'active' => "required|in_list[0,1]"
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }
        $amount_in_less_than_equal_to = $this->request->getPost('amount_in_less_than_equal_to');
        $amount_in_great_than_equal_to = $this->request->getPost('amount_in_great_than_equal_to');

        if(!is_numeric($amount_in_less_than_equal_to) || $amount_in_less_than_equal_to <= 0)
            $amount_in_less_than_equal_to = 0;

        if(!is_numeric($amount_in_great_than_equal_to) || $amount_in_great_than_equal_to <= 0)
            $amount_in_great_than_equal_to = 0;
            
        if($amount_in_less_than_equal_to != 0 && $amount_in_great_than_equal_to != 0) {
            if($amount_in_great_than_equal_to > $amount_in_less_than_equal_to) {
                return $this->response->setJSON(array('status'=>FALSE,'message'=>"Vui lòng nhập giá trị 'Số tiền lớp hơn hoặc bằng' phải nhỏ hơn điều kiện 'số tiền nhỏ hơn hoặc bằng'"));
            }
        }

        $data = array(
            'id' => trim($this->request->getVar('id')),
            'contains_content' => $this->request->getVar('contains_content'),
            'description' => xss_clean($this->request->getVar('description')),
            'transaction_type' => trim($this->request->getVar('transaction_type')),
            'bot_webhook_url' => trim($this->request->getVar('bot_webhook_url')),
            'amount_in_less_than_equal_to' => $amount_in_less_than_equal_to,
            'amount_in_great_than_equal_to' => $amount_in_great_than_equal_to,
            'ignore_phrases' => trim($this->request->getVar('ignore_phrases')),
            'active' => $this->request->getVar('active')
        );

        if (!is_string($data['bot_webhook_url']) || !filter_var($data['bot_webhook_url'], FILTER_VALIDATE_URL))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>'Bot WebHook URL không hợp lệ'));

            $notificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);

            $id = $data['id'];

        $larkmessenger_details = $notificationLarkMessengerModel->where(['bot_webhook_url'=>$id,'company_id'=>$this->user_session['company_id']])->get()->getResult();
        
        if(!is_array($larkmessenger_details))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>'Không tìm thấy tích hợp Lark Messenger'));

            foreach ($larkmessenger_details as $t)
            {
                $result = $notificationLarkMessengerModel->set($data)->where(["id" =>$t->id,"company_id"=>$this->user_session['company_id']])->update();
        
                if($result) { 
                    add_user_log(array('data_id'=>$t->id,'company_id' => $this->user_session['company_id'], 'data_type'=>'notification_lark_messenger_update','description'=>'Sửa tích hợp thông báo Lark Messenger','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
                } else {
                    add_user_log(array('data_id'=>$t->id,'company_id' => $this->user_session['company_id'], 'data_type'=>'notification_lark_messenger_update','description'=>'Sửa tích hợp thông báo Lark Messenger','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Failed'));
                    return $this->response->setJSON(array("status"=>false,"message"=>"Không thể cập nhật dữ liệu này!"));
                }
            }
            return $this->response->setJSON(array("status"=>true));
    }

    public function ajax_edit_step2()
    {
        if ($this->request->getMethod(true) != 'POST') {
            return '';
        }

        if (!has_permission('NotificationLarkMessenger', 'can_edit')) {
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền sửa dữ liệu']);
        }

        $validation = \Config\Services::validation();

        if (! $this->validate([
            'shop_id' => 'required|min_length[1]|max_length[50]',
            'bot_webhook_url' => ['label' => 'Bot WebHook URL', 'rules' => "required|min_length[50]|max_length[500]"]
        ])) {
            return $this->response->setJSON(['status' => false, 'message' => implode(". ", $validation->getErrors())]);
        }

        $data = [
            'shop_id' => trim($this->request->getVar('shop_id')),
            'bot_webhook_url' => trim($this->request->getVar('bot_webhook_url'))
        ];

        if (!is_string($data['bot_webhook_url']) || !filter_var($data['bot_webhook_url'], FILTER_VALIDATE_URL))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>'Bot WebHook URL không hợp lệ'));

        $shopId = $data['shop_id'];
        $botWebhookUrl = $data['bot_webhook_url'];

        $notificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);
        $shopModel = model(ShopModel::class);

        $shops = $shopModel
            ->select([
                'tb_autopay_bank_shop_link.bank_account_id',
                'tb_autopay_bank_shop_link.bank_sub_account_id'
            ])
            ->join('tb_autopay_bank_shop_link', 'tb_autopay_bank_shop_link.shop_id = tb_autopay_shop.id')
            ->where('tb_autopay_shop.company_id', $this->user_session['company_id'])
            ->where('tb_autopay_bank_shop_link.shop_id', $shopId)
            ->get()
            ->getResult();

        if (!empty($shops)) {
            foreach ($shops as $s) {
                $larkmessenger_details = $notificationLarkMessengerModel->where([
                    'bank_account_id' => $s->bank_account_id,
                    'sub_account_id' => $s->bank_sub_account_id,
                    'bot_webhook_url' => $botWebhookUrl,
                    'company_id' => $this->user_session['company_id']
                ])->get()->getRow();

                if ($larkmessenger_details) {
                    $notificationLarkMessengerModel->delete($larkmessenger_details->id);
                    add_user_log([
                        'data_id' => $larkmessenger_details->id,
                        'company_id' => $this->user_session['company_id'],
                        'data_type' => 'notification_telegram_update',
                        'description' => 'Sửa tích hợp thông báo Telegram',
                        'user_id' => $this->user_details->id,
                        'ip' => $this->request->getIPAddress(),
                        'user_agent' => $this->request->getUserAgent()->getAgentString(),
                        'status' => 'Success'
                    ]);

                    $larkmessenger_details = $notificationLarkMessengerModel->where([
                        'bot_webhook_url' => $botWebhookUrl,
                        'company_id' => $this->user_session['company_id']
                    ])->first();

                    if (!is_object($larkmessenger_details)) {
                        return $this->response->setJSON(['status' => true,
                        'url' => base_url('notificationlarkmessenger'),
                        'message' => 'Đã xóa hết cửa hàng. Vui lòng thêm lại tích hợp với Telegram']);
                    }
                } else {
                    $larkmessenger_details = $notificationLarkMessengerModel->where([
                        'bot_webhook_url' => $botWebhookUrl,
                        'company_id' => $this->user_session['company_id']
                    ])->first();

                    $data = [
                        'company_id' => $larkmessenger_details->company_id ?? $this->user_session['company_id'],
                        'transaction_type' => 'In_only',
                        'bank_account_id' => $s->bank_account_id,
                        'sub_account_id' => $s->bank_sub_account_id,
                        'contains_content' => $larkmessenger_details->contains_content ?? '',
                        'description' => $larkmessenger_details->description ?? '',
                        'bot_webhook_url' => $botWebhookUrl,
                        'amount_in_less_than_equal_to' => $larkmessenger_details->amount_in_less_than_equal_to ?? 0,
                        'amount_in_great_than_equal_to' => $larkmessenger_details->amount_in_great_than_equal_to ?? 0,
                        'ignore_phrases' => $larkmessenger_details->ignore_phrases ?? '',
                        'active' => $larkmessenger_details->active ?? 1,
                        'message_thread_id' => $larkmessenger_details->message_thread_id ?? 0,
                        'verify_payment' => $larkmessenger_details->verify_payment ?? 'No',
                        'is_template_custom' => $larkmessenger_details->is_template_custom ?? 0,
                        'template_custom' => $larkmessenger_details->template_custom ?? '',
                        'template_name' => $larkmessenger_details->template_name ?? 'template_1',
                        'hide_accumulated' => $larkmessenger_details->hide_accumulated ?? 0,
                        'hide_details_link' => $larkmessenger_details->hide_details_link ?? 0
                    ];

                    $larkId = $notificationLarkMessengerModel->insert($data);
                    if ($larkId === false) {
                        return $this->response->setJSON(['status' => false, 'message' => 'Có lỗi xảy ra trong quá trình chèn dữ liệu']);
                    }

                    add_user_log([
                        'data_id' => $larkId,
                        'company_id' => $this->user_session['company_id'],
                        'data_type' => 'notification_lark_messenger_update',
                        'description' => 'Sửa tích hợp thông báo Lark Messenger',
                        'user_id' => $this->user_details->id,
                        'ip' => $this->request->getIPAddress(),
                        'user_agent' => $this->request->getUserAgent()->getAgentString(),
                        'status' => 'Success'
                    ]);
                }
            }
            return $this->response->setJSON(['status' => true]);
        }

        return $this->response->setJSON(['status' => false, 'message' => 'Không tìm thấy cửa hàng']);
    }

    public function ajax_edit_step3()
    {
        if ($this->request->getMethod(true) != 'POST')
        return '';

        if(!has_permission('NotificationLarkMessenger', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền sửa dữ liệu"));


        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'id' => ['label' => 'ID', 'rules' => 'required|min_length[50]|max_length[500]'],
            'is_template_custom' => ['label' => 'Loại nội dung', 'rules' => "required|in_list[0,1]"],
            'template_name' => ['label' => 'Mẫu tin', 'rules' => "in_list[template_1,template_2,template_3]"]
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }

        $notificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);

        $id = $this->request->getVar('id');
        $is_template_custom = trim(xss_clean($this->request->getVar('is_template_custom')));
        $hide_accumulated = trim(xss_clean($this->request->getVar('hide_accumulated')));
        $hide_details_link = trim($this->request->getVar('hide_details_link'));

        if (!is_string($id) || !filter_var($id, FILTER_VALIDATE_URL))
        return $this->response->setJSON(array('status'=>FALSE,'message'=>'Bot WebHook URL không hợp lệ'));
        
        $larkmessenger_details = $notificationLarkMessengerModel->where(['bot_webhook_url'=>$id, 'company_id' => $this->user_session['company_id']])->get()->getResult();

        if(!is_array($larkmessenger_details))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>'Không tìm thấy tích hợp này'));    

        foreach($larkmessenger_details as $t)
        {
            if($is_template_custom == 0) {
                if($hide_accumulated == "on")
                    $is_hide_accumulated = 1;
                else 
                    $is_hide_accumulated = 0;
                
                if($hide_details_link == "on")
                    $is_hide_details_link = 1;
                else
                    $is_hide_details_link = 0;
                
                $notificationLarkMessengerModel->set(['is_template_custom' => 0, 'hide_accumulated' => $is_hide_accumulated, 'hide_details_link' => $is_hide_details_link])->where(['id'=>$t->id, 'company_id' => $this->user_session['company_id']])->update();
    
            } else if($is_template_custom == 1)   {
                if(! $this->validate([
                    'template_custom' => ['label' => 'Tùy chỉnh nội dung', 'rules' => 'required|min_length[10]|max_length[1000]']
                ])) {
                    return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
                }
                $template_name = $this->request->getVar('template_name');
    
                $template_custom = $this->request->getVar('template_custom');
    
                $template_custom = strip_tags($template_custom);
    
                $valid_html = validHTML($template_custom);
    
                if($valid_html !== true)
                    return $this->response->setJSON(array('status'=>FALSE,'message'=>'Lỗi: HTML không đúng định dạng. ' . $valid_html));    
    
                $notificationLarkMessengerModel->set(['is_template_custom' => 1,'template_custom' => $template_custom, 'template_name' => $template_name])->where(['id'=>$t->id, 'company_id' => $this->user_session['company_id']])->update();
            }
        }

        add_user_log([
            'data_id' => $id,
            'company_id' => $this->user_session['company_id'],
            'data_type' => 'notification_lark_messenger_update',
            'description' => 'Sửa tích hợp thông báo Lark Messenger',
            'user_id' => $this->user_details->id,
            'ip' => $this->request->getIPAddress(),
            'user_agent' => $this->request->getUserAgent()->getAgentString(),
            'status' => 'Success'
        ]);

        return $this->response->setJSON(array('status'=>TRUE));
    }



    // custom ui lark mysepay


    public function all_ajax_list_lark_messenger() {
     
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!has_permission('NotificationLarkMessenger', 'can_view_all'))
            show_404();
 
        $notificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);

        $larks = $notificationLarkMessengerModel->getDatatables($this->user_session['company_id']);
         
        $data = array();

        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');

        foreach ($larks as $lark) {

            $no++;
            $row = array();
            
            $row[] = $no;
            $row[] = esc($lark->id);
            $row[] = "<b>" . esc($lark->description) . "</b>";
          
         
            
            

            $condition_text_bank = '<ul>';

            if(strlen($lark->account_number ?? "") > 0){

                $condition_text_bank = $condition_text_bank. "<li><span class='text-info'>[Và] Khi tài khoản chính là <b>".esc($lark->account_number??"") . ' ' . esc($lark->brand_name)."</b></span> </li>";
            }else{
                $condition_text_bank = $condition_text_bank. "<li><span class='text-info'>[Và] Khi tài khoản chính là <b>Tất cả ngân hàng</b></span> </li>";

            }
            if(strlen($lark->sub_account ?? "") > 0 && strlen($lark->account_number ?? "") > 0 ){
                $condition_text_bank = $condition_text_bank. "<li><span class='text-info'>[Và] Khi tài khoản phụ là <b>". esc($lark->sub_account??"")."</b></span> </li>";
            }elseif(strlen($lark->sub_account ?? "") == 0 && strlen($lark->account_number ?? "") > 0){
                $condition_text_bank = $condition_text_bank. "<li><span class='text-info'>[Và] Khi tài khoản phụ là <b> Không chọn VA</b></span> </li>";
            }else{
                $condition_text_bank = $condition_text_bank. "<li><span class='text-info'>[Và] Khi tài khoản phụ là <b> Tất cả VA</b></span> </li>";
            }
            $condition_text_bank = $condition_text_bank .'</ul>';
            $row[] = $condition_text_bank;


            $condition_text = '<ul>';
            if($lark->transaction_type == "In_only")
                $condition_text = $condition_text. "<li><span class='text-info'>[Loại giao dịch] Khi có tiền vào</span></li>";
            else if($lark->transaction_type == "Out_only")
                $condition_text = $condition_text. "<li><span class='text-info'>[Loại giao dịch] Khi có tiền ra</span></li>";
            else if($lark->transaction_type == "All")
                $condition_text = $condition_text. "<li><span class='text-info'>[Loại giao dịch] Khi có tiền vào hoặc Tiền ra</span></li>";
             

           

            
            if($lark->webhook_type == "Success")
                $condition_text = $condition_text. "<li><span class='text-info'>[Và] Khi WebHooks thành công</span></li>";
            else if($lark->webhook_type == "Failed")
                $condition_text = $condition_text. "<li><span class='text-info'>[Và] Khi WebHooks thất bại</span></li>";

            if($lark->verify_payment == "Success")
                $condition_text = $condition_text. "<li><span class='text-info'>[Và] Khi WebHooks xác thực thanh toán thành công</span></li>";
            else if($lark->verify_payment == "Failed")
                $condition_text = $condition_text. "<li><span class='text-info'>[Và] Khi WebHooks xác thực thanh toán thất bại</span></li>";
            else if($lark->verify_payment == "Success_Or_Failed")
                $condition_text = $condition_text. "<li><span class='text-info'>[Và] Khi WebHooks xác thực thanh toán Thất bại hoặc Thành công</span></li>";
            
            if($lark->ignore_phrases)
                $condition_text = $condition_text. "<li><span class='text-success'>[Bỏ qua] Khi WebHooks Có các từ: </span> " . esc(character_limiter($lark->ignore_phrases,50,'...')) . "</li>";
             
            if($lark->contains_content)
                $condition_text = $condition_text. "<li><span class='text-success'>[Và] Khi WebHooks Có các từ: </span> " . esc(character_limiter($lark->contains_content,50,'...')) . "</li>";
            
            if (intval($lark->amount_in_great_than_equal_to) > 0) {
                $amount = number_format($lark->amount_in_great_than_equal_to, 0, ',', '.'); // Format số
                $condition_text .= "<li><span class='text-success'>[Và] Khi số tiền lớn hơn hoặc bằng: </span> " 
                    . esc(character_limiter($amount . ' đ', 50, '...')) 
                    . "</li>";
            }

            if (intval($lark->amount_in_less_than_equal_to) > 0) {
                $amount = number_format($lark->amount_in_less_than_equal_to, 0, ',', '.'); // Format số
                $condition_text .= "<li><span class='text-success'>[Và] Khi số tiền nhỏ hơn hoặc bằng: </span> " 
                    . esc(character_limiter($amount . ' đ', 50, '...')) 
                    . "</li>";
            }
                
                
            $condition_text =  $condition_text .'</ul>';

            $row[] = $condition_text;
            // Lấy phần token từ cuối URL
            $token = basename($lark->bot_webhook_url);

            $row[] = esc($token);

          
            if($lark->active == 1)
                $row[] = "<span class='text-success'>Kích hoạt</span>";
            else
                $row[] = "<span class='text-secondary'>Tạm ngưng</span>";
            //$row[] = esc($lark->created_at);

            $action_text = "<div class='d-flex'>";
            if(has_permission('NotificationLarkMessenger', 'can_edit'))
                // $action_text = "<a href='javascript:;' onclick='edit_lark(" . $lark->id. ")' class='btn btn-sm btn-outline-warning ms-2 me-2 mt-2'><i class='bi bi-pencil'></i> Sửa</a> <a href='".base_url('notificationlarkmessenger/edit_message/' . $lark->id)."' class='btn btn-sm btn-outline-info ms-2 me-2 mt-2'><i class='bi bi-chat-left-text'></i> Nội dung</a>";
                $action_text = "<a href='/notificationlarkmessenger/edit_/$lark->id' class='btn btn-sm btn-outline-info ms-2 mt-2'><i class='bi bi-pencil'></i> Sửa</a> <a href='".base_url('notificationlarkmessenger/edit_message/' . $lark->id)."' class='btn btn-sm btn-outline-info ms-2 me-2 mt-2 d-none'><i class='bi bi-chat-left-text'></i> Nội dung</a>";
            
            if(has_permission('NotificationLarkMessenger', 'can_delete'))
                $action_text = $action_text . "<a href='javascript:;' onclick='delete_lark(" . $lark->id. ")' class='btn btn-sm btn-outline-danger ms-2 mt-2'><i class='bi bi-trash3'></i> Xóa</a>";

            $action_text = $action_text ."</div>";
            $row[] = $action_text;
       
       
            $data[] = $row;
        }
        foreach ($data as &$row) {
            if (($row[4])=="<ul></ul>") {
                $row[4] = "<ul><li><span class='text-warning'>[Bỏ qua cấu hình nâng cao] </span> </li></ul>";
            }
            
        }
 
        $output = array(
            "draw" => $draw,
            "recordsTotal" => $notificationLarkMessengerModel->countAll($this->user_session['company_id']),
            "recordsFiltered" => $notificationLarkMessengerModel->countFiltered($this->user_session['company_id']),
            "data" => $data,
        );
        return $this->response->setJSON($output);
    
    
    }

     // custom ui lark messenger

     public function edit_($id=null){
        if(empty($id)){
            show_404();
        }
        if (!has_permission('NotificationLarkMessenger', 'can_edit')) {
            session()->setFlashdata('error_message', 'Bạn không có quyền sửa tích hợp!');
            return redirect()->to('notificationlarkmessenger');
        }
        // Lấy mô hình NotificationlarkmessengerModel
        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $notificationLarkMessengerModel = model(notificationLarkMessengerModel::class);

        $data_integration = $notificationLarkMessengerModel
            ->where('id', $id)
            ->where('company_id', $this->company_details->id ?? "")
            ->get() // Thực hiện truy vấn
            ->getRowArray(); // Trả về kết quả dưới dạng mảng

        if(empty($data_integration)){
            show_404();
        }
        $data_integration['amount_in_great_than_equal_to'] = intval($data_integration['amount_in_great_than_equal_to']);
        $data_integration['amount_in_less_than_equal_to'] = intval($data_integration['amount_in_less_than_equal_to']);
        // Lấy danh sách tài khoản ngân hàng
        $data['bank_accounts'] = $bankAccountModel
        ->select("tb_autopay_bank_account.id, tb_autopay_bank.brand_name, tb_autopay_bank_account.account_holder_name, tb_autopay_bank_account.account_number")
        ->join("tb_autopay_bank", "tb_autopay_bank.id = tb_autopay_bank_account.bank_id", "left")
        ->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id']])
        ->get()->getResultArray();

        // Lấy danh sách tài khoản phụ
        $data['bank_sub_accounts'] = $bankSubAccountModel
        ->select("tb_autopay_bank_sub_account.id, tb_autopay_bank_account.id as bank_account_id, tb_autopay_bank.brand_name, tb_autopay_bank_account.account_holder_name, tb_autopay_bank_account.account_number, tb_autopay_bank_sub_account.sub_account, tb_autopay_bank_sub_account.label")
        ->join("tb_autopay_bank_account", "tb_autopay_bank_account.id = tb_autopay_bank_sub_account.bank_account_id")
        ->join("tb_autopay_bank", "tb_autopay_bank.id = tb_autopay_bank_account.bank_id", "left")
        ->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id']])
        ->get()->getResultArray();

        // Tạo mảng kết quả đã nhóm
        $groupedBanks = [];

        foreach ($data['bank_accounts'] as $account) {
            $bank_account_number = $account['account_number'];
            $bank_account_id = $account['id'];
        
            // Khởi tạo mảng con subaccount nếu chưa tồn tại
            if (!isset($groupedBanks[$bank_account_id])) {
                $groupedBanks[$bank_account_id] = [
                    'id' => $bank_account_id,
                    'account_number' => $bank_account_number,
                    'brand_name' => $account['brand_name'], 
                    'subaccounts' => [] // Mảng chứa tài khoản phụ
                ];
            }
        
            // Lọc các tài khoản phụ (sub-accounts) tương ứng với tài khoản ngân hàng
            foreach ($data['bank_sub_accounts'] as $subAccount) {
                if ($subAccount['bank_account_id'] == $bank_account_id) {
                    $groupedBanks[$bank_account_id]['subaccounts'][] = $subAccount;
                }
            }
        }
        // Chuyển đổi dữ liệu đã nhóm thành một mảng

        $data = [
            'page_title' => 'LarkMessenger',
            'data_integration' => $data_integration,
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];
        $data['grouped_bank_accounts'] = array_values($groupedBanks);


        echo view('templates/autopay/header',$data);
        echo view('notificationlarkmessenger/edit_tab', $data);
        echo view('templates/autopay/footer',$data);
    }
 
    public function step_1()
    {
        if (!has_permission('NotificationLarkMessenger', 'can_add')) show_404();
        
        $session = service('session');

        // Kiểm tra nếu dữ liệu đã tồn tại trong session, nếu có thì lấy ra và cập nhật
        $session_data_integration = $session->get('notification_larkmessenger_integration');
        if ($session_data_integration === null) {
            // Nếu không có dữ liệu trước đó trong session, tạo mới mảng dữ liệu
            $session_data_integration = [
                'company_id' => $this->company_details->id ?? "",
                'bank_account_id' => 0,
                'transaction_type' => "All",
                'contains_content' => null,
                'bot_webhook_url' => null,
                'description' => null,
                'amount_in_less_than_equal_to' => null,
                'amount_in_great_than_equal_to' => null,
                'hide_accumulated' => 0,
                'hide_details_link' => 0,
                'ignore_phrases' => null,
                'active' => 1,
                'webhook_type' => 'No',
                'verify_payment' => 'Skip',
                'sub_account_id' => 0,
                'is_connect' => 0,
                'is_config' => 0,
            ];
        }   

        // Lưu mảng vào session với tên 'notification_larkmessenger_integration'
        $session->set('notification_larkmessenger_integration', $session_data_integration);


        $data = [
            'page_title' => 'Larkmessenger',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        echo view('templates/autopay/header',$data);
        echo view('notificationlarkmessenger/integration/step_1', $data);
        echo view('templates/autopay/footer',$data);
    }

    public function step_2()
    {
        if (!has_permission('NotificationLarkMessenger', 'can_add')) show_404();

        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $webhooksModel = model(WebhooksModel::class);
        $data = [
            'page_title' => 'LarkMessenger',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        $session = service('session');

        $notificationLarkMessengerIntegration = $session->get('notification_larkmessenger_integration');
        if(empty($notificationLarkMessengerIntegration['bot_webhook_url'])){
            return redirect()->to('notificationlarkmessenger/step_1');
        }
        if(empty($notificationLarkMessengerIntegration['description'])){
            return redirect()->to('notificationlarkmessenger/step_1');
        }
        if(empty($notificationLarkMessengerIntegration['is_connect'])){
            return redirect()->to('notificationlarkmessenger/step_1');
        }
        

        // Lấy danh sách tài khoản ngân hàng
        $data['bank_accounts'] = $bankAccountModel
        ->select("tb_autopay_bank_account.id, tb_autopay_bank.brand_name, tb_autopay_bank_account.account_holder_name, tb_autopay_bank_account.account_number")
        ->join("tb_autopay_bank", "tb_autopay_bank.id = tb_autopay_bank_account.bank_id", "left")
        ->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id']])
        ->get()->getResultArray();

        // Lấy danh sách tài khoản phụ
        $data['bank_sub_accounts'] = $bankSubAccountModel
        ->select("tb_autopay_bank_sub_account.id, tb_autopay_bank_account.id as bank_account_id, tb_autopay_bank.brand_name, tb_autopay_bank_account.account_holder_name, tb_autopay_bank_account.account_number, tb_autopay_bank_sub_account.sub_account, tb_autopay_bank_sub_account.label")
        ->join("tb_autopay_bank_account", "tb_autopay_bank_account.id = tb_autopay_bank_sub_account.bank_account_id")
        ->join("tb_autopay_bank", "tb_autopay_bank.id = tb_autopay_bank_account.bank_id", "left")
        ->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id']])
        ->get()->getResultArray();

        $groupedBanks = [];

        foreach ($data['bank_accounts'] as $account) {
            $bank_account_number = $account['account_number'];
            $bank_account_id = $account['id'];
        
            // Khởi tạo mảng con subaccount nếu chưa tồn tại
            if (!isset($groupedBanks[$bank_account_id])) {
                $groupedBanks[$bank_account_id] = [
                    'id' => $bank_account_id,
                    'account_number' => $bank_account_number,
                    'brand_name' => $account['brand_name'], 
                    'subaccounts' => [] // Mảng chứa tài khoản phụ
                ];
            }
        
            // Lọc các tài khoản phụ (sub-accounts) tương ứng với tài khoản ngân hàng
            foreach ($data['bank_sub_accounts'] as $subAccount) {
                if ($subAccount['bank_account_id'] == $bank_account_id) {
                    $groupedBanks[$bank_account_id]['subaccounts'][] = $subAccount;
                }
            }
        }

        // Chuyển đổi dữ liệu đã nhóm thành một mảng
        $data['grouped_bank_accounts'] = array_values($groupedBanks);

        $data['webhooks_count'] = $webhooksModel->where(['company_id' => $this->user_session['company_id']])->countAllResults();

       

        echo view('templates/autopay/header',$data);
        echo view('notificationlarkmessenger/integration/step_2', $data);
        echo view('templates/autopay/footer',$data);
    }

    public function step_3()
    {
        if (!has_permission('NotificationLarkMessenger', 'can_add')) show_404();

        $data = [
            'page_title' => 'LarkMessenger',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        $session = service('session');

        $notificationLarkMessengerIntegration = $session->get('notification_larkmessenger_integration');

        if(empty($notificationLarkMessengerIntegration['bot_webhook_url'])){
            return redirect()->to('notificationlarkmessenger/step_1');
        }
        if(empty($notificationLarkMessengerIntegration['description'])){
            return redirect()->to('notificationlarkmessenger/step_1');
        }
        if(empty($notificationLarkMessengerIntegration['is_connect'])){
            return redirect()->to('notificationlarkmessenger/step_1');
        }
        if(empty($notificationLarkMessengerIntegration['is_config'])){
            return redirect()->to('notificationlarkmessenger/step_2');
        }


        $data['notification_larkmessenger_integration'] = $notificationLarkMessengerIntegration;

        echo view('templates/autopay/header',$data);
        echo view('notificationlarkmessenger/integration/step_3', $data);
        echo view('templates/autopay/footer',$data);
    }

    public function step_4()
    {
        if (!has_permission('NotificationLarkMessenger', 'can_add')) show_404();

        $data = [
            'page_title' => 'LarkMessenger',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        $session = service('session');

        $notificationLarkMessengerIntegration = $session->get('notification_larkmessenger_integration');

        if(empty($notificationLarkMessengerIntegration['bot_webhook_url'])){
            return redirect()->to('notificationlarkmessenger/step_1');
        }
        if(empty($notificationLarkMessengerIntegration['description'])){
            return redirect()->to('notificationlarkmessenger/step_1');
        }
        if(empty($notificationLarkMessengerIntegration['is_connect'])){
            return redirect()->to('notificationlarkmessenger/step_1');
        }
        if(empty($notificationLarkMessengerIntegration['is_config'])){
            return redirect()->to('notificationlarkmessenger/step_2');
        }


        $session = service('session');
        $notification = $session->get('notification_larkmessenger_integration');
        // Gọi hàm cấu hình lark messenger
        $result_add = $this->config_larkmessenger_add($notification);
        if (is_array($result_add) || is_object($result_add)) {
            log_message("debug", json_encode($result_add));
        } else {
            log_message("debug", "Kết quả trả về không phải là mảng hoặc đối tượng");
        }
    
        if (empty($result_add['status'])) {
            return $this->respond(['status' => false, 'message' => "Lỗi hệ thống thêm tích hợp, hãy liên hệ kỹ thuật"]);
        }
    
        $session->remove('notification_larkmessenger_integration');


        echo view('templates/autopay/header',$data);
        echo view('notificationlarkmessenger/integration/step_4', $data);
        echo view('templates/autopay/footer',$data);       
    }

    public function ajax_test_larkmessenger_connection_()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!has_permission('NotificationLarkMessenger', 'can_add'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền thực hiện thao tác này"));

 
        $validation =  \Config\Services::validation();

        helper('text');

        if (! $this->validate([
            'bot_webhook_url' => [
                'rules' => 'required|regex_match[/^https:\/\/open\.larksuite\.com\/open-apis\/bot\/v2\/hook\/[a-zA-Z0-9\-]{36}$/]',
                'errors' => [
                    'required' => 'Webhook URL là bắt buộc.',
                    'regex_match' => 'Webhook URL không đúng định dạng Lark.',
                ]
            ],
        ])) {
            return $this->response->setJSON([
                'status' => false,
                'message' => implode(". ", $this->validator->getErrors())
            ]);
        }
        
        
        
        $bot_webhook_url = $this->request->getPost('bot_webhook_url');

        return $this->testLarkMessengerConnection_($bot_webhook_url);

        add_user_log(array('data_id'=>0,'company_id' => $this->user_session['company_id'], 'data_type'=>'notification_lark_messenger_test','description'=>'Test gửi thông báo Lark Messenger','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
    }

    protected function testLarkMessengerConnection_($bot_webhook_url, $message = null)
    {
        $notificationLarkMessengerQueueModel = model(NotificationLarkMessengerQueueModel::class);

        $message = $message ?? "✅ Kết nối thành công!";
        $result = $notificationLarkMessengerQueueModel->sendMessage($message, $bot_webhook_url, 'text');

        if(isset($result['success']) && $result['success'] == TRUE) {

            $session = service("session");
            $session_data_integration = $session->get('notification_larkmessenger_integration');
            if(isset($result['response'])) {
                $api_response = json_decode($result['response']);

                if($api_response) {
                    if(is_object($api_response) && isset($api_response->StatusMessage) && $api_response->StatusMessage == "success") {

                        $session_data_integration['is_connect'] = 1;
                        $session_data_integration['bot_webhook_url'] = $bot_webhook_url;
                
                        $session->set('notification_larkmessenger_integration', $session_data_integration);

                        return $this->response->setJSON(array("status"=>true));
                    }
                }
            }
        }

        $session_data_integration['is_connect'] = 0;
        $session_data_integration['bot_webhook_url'] = $bot_webhook_url;
        $session->set('notification_larkmessenger_integration', $session_data_integration);


        return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ']);
    }

    public function ajax_config_step_1()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!has_permission('NotificationLarkMessenger', 'can_add'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền thực hiện thao tác này']);
 
            $data = [
                'bot_webhook_url' => trim($this->request->getPost('bot_webhook_url')),
                'description' => trim(xss_clean($this->request->getPost('description')))
            ];
            
            $rules = [
                'bot_webhook_url' => [
                    'label'  => 'Webhook URL',
                    'rules'  => 'required|min_length[50]|max_length[1000]|regex_match[/^https:\/\/open\.larksuite\.com\/open-apis\/bot\/v2\/hook\/[a-zA-Z0-9\-]{36}$/]',
                    'errors' => [
                        'required'       => 'Webhook URL là bắt buộc.',
                        'min_length'     => 'Webhook URL phải có ít nhất {param} ký tự.',
                        'max_length'     => 'Webhook URL không được vượt quá {param} ký tự.',
                        'regex_match'    => 'Webhook URL không đúng định dạng Lark.',
                    ]
                ],
                'description' => [
                    'label'  => 'Mô tả',
                    'rules'  => 'required|max_length[100]',
                    'errors' => [
                        'required'   => 'Mô tả là bắt buộc.',
                        'max_length' => 'Mô tả không được vượt quá {param} ký tự.',
                    ]
                ]
            ];
            
            // Kiểm tra dữ liệu
            if (! $this->validate($rules)) {
                return $this->fail($this->validator->getErrors());
            }
            

        $session = service('session');
        $session_data_integration = $session->get('notification_larkmessenger_integration');
        $session->set('notification_larkmessenger_integration', array_merge($session_data_integration,$data));

        return $this->respond(['status' => true]);
    } 

    public function ajax_config_step_2()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!has_permission('NotificationLarkMessenger', 'can_add'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền thực hiện thao tác này']);
 
        $data_input = $this->request->getPost();
        if (!empty($data_input['contains_content'])) {
            $data_input['contains_content'] = allowed_tags($data_input['contains_content']);
            $data_input['contains_content'] = str_replace(['"', ','], ['', '|'], $data_input['contains_content']);
        }            
        if (!empty($data_input['ignore_phrases'])) {
            $data_input['ignore_phrases'] = str_replace(['"', ','], ['', '|'], $data_input['ignore_phrases']);
            $data_input['ignore_phrases'] = allowed_tags($data_input['ignore_phrases']);
        }
        $data_clean = array_map(function($item){
            return allowed_tags($item);
        },$data_input);
        $validationRules = [
            "bank_account_id" => [
                "label" => "ID ngân hàng",
                "rules" => "required",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                ],
            ],
            "transaction_type" => [
                "label" => "Loại giao dịch",
                "rules" => "required|in_list[All,In_only,Out_only]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "in_list" => "Giá trị của {field} phải là 'All', 'In_only' hoặc 'Out_only'.",
                ],
            ],
            "webhook_type" => [
                "label" => "Loại webhook",
                "rules" => "required|in_list[No,All,Success,Failed]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "in_list" => "Giá trị của {field} phải là 'No', 'All', 'Success' hoặc 'Failed'.",
                ],
            ],
            "verify_payment" => [
                "label" => "Loại xác thực",
                "rules" => "required|in_list[No,Success_Or_Failed,Success,Failed,Skip]",
                "errors" => [
                    "required" => "Vui lòng truyền {field}.",
                    "in_list" => "Giá trị của {field} phải là 'No', 'Success_Or_Failed', 'Success', 'Failed' hoặc 'Skip'.",
                ],
            ],
            
        ];
        
    
        // Load validation service
        $validation = service("validation");
    
        // Set validation rules
        $validation->setRules($validationRules);
    
        // Check data
        if (!$validation->run($data_clean)) {
            $errors = $validation->getErrors();
            log_message("error","Lỗi xác thực dữ liệu khi nhập cấu hình ".print_r($errors,true));
            return $this->respond(['status' => false]);
        }

        $session = service('session');
        $notificationLarkMessengerIntegration = $session->get('notification_larkmessenger_integration');
        if(empty($notificationLarkMessengerIntegration)){
            log_message("error","Lỗi mất session thông tin tích hợp larkmessenger!");
            return $this->respond(['status' => false]);
        }
  
        $notificationLarkMessengerIntegration['is_config']=1;

        $session->set('notification_larkmessenger_integration', array_merge($notificationLarkMessengerIntegration,$data_clean));

        return $this->respond(['status' => true]);
    }
    public function ajax_config_step_3()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!has_permission('NotificationLarkMessenger', 'can_add'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền thực hiện thao tác này']);

        $data = [
            'is_template_custom' => $this->request->getPost('is_template_custom'),
            'template_custom' => $this->request->getPost('template_custom'),
            'template_name' => $this->request->getPost('template_name'),
            'hide_accumulated' => $this->request->getPost('hide_accumulated') == "on" ? 1:0,
            'hide_details_link' => $this->request->getPost('hide_details_link')== "on" ? 1:0,
        ];

        if (!in_array($data['template_name'], ['template_1', 'template_2', 'template_3'])) {
            return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng tải lại trang']);
        }

        if ($data['is_template_custom'] && !trim($data['template_custom'])) {
            return $this->fail(['template_custom' => 'Nội dung tin nhắn là bắt buộc']);
        }
        
        if (!empty($data['template_custom'])) {
            $data['template_custom'] = allowed_tags($data['template_custom']);
        }
        $validation =  \Config\Services::validation();
        // Set validation rules
        $validation->setRules([
           
            
            'template_name' => [
                'label' => 'Mẫu tin',
                'rules' => 'permit_empty|in_list[template_1,template_2,template_3]',
                'errors' => [
                    'in_list' => '{field} phải là một trong các giá trị: template_1, template_2, template_3.'
                ]
            ],
            'template_custom' => [
                'label' => 'Tùy chỉnh nội dung',
                'rules' => 'permit_empty|min_length[10]|max_length[1000]',
                'errors' => [
                    'min_length' => '{field} phải có ít nhất 10 ký tự.',
                    'max_length' => '{field} không được vượt quá 1000 ký tự.'
                ]
            ]
        ]);
        
    
        // Check data
        if (!$validation->run($data)) {
            return $this->respond(['status' => false, 'message' => 'Dữ liệu không hợp lệ','data' => $validation->getErrors()]);
        }

        $session = service('session');

        $notificationLarkMessengerIntegration = $session->get('notification_larkmessenger_integration');

        

        $session->set('notification_larkmessenger_integration', array_merge($notificationLarkMessengerIntegration, $data));

        return $this->respond(['status' => true]);
    }

    public function infobank()
    {
    

        // Kiểm tra các điều kiện trong phiên
        if (
            empty($notification['bot_webhook_url']) ||
            empty($notification['description']) ||
            empty($notification['is_connect']) ||
            empty($notification['is_config'])
        ) {
            return $this->respond(['status' => false]);
        }

        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);

        $bank_account_id = $notification['bank_account_id'] ?? null;
        $sub_account_id = $notification['sub_account_id'] ?? null;
        $company_id = $notification['company_id'] ?? null;

    
        // Truy vấn dữ liệu tài khoản ngân hàng
        $data_bank = null;

        if (empty($sub_account_id) && empty($bank_account_id)) {
            $data_bank = $bankSubAccountModel
                ->select(
                    "tb_autopay_bank_sub_account.id, tb_autopay_bank.brand_name, tb_autopay_bank_account.account_holder_name, tb_autopay_bank_account.account_number, tb_autopay_bank_sub_account.sub_account, tb_autopay_bank_sub_account.label"
                )
                ->join("tb_autopay_bank_account", "tb_autopay_bank_account.id = tb_autopay_bank_sub_account.bank_account_id")
                ->join("tb_autopay_bank", "tb_autopay_bank.id = tb_autopay_bank_account.bank_id", "left")
                ->where(['tb_autopay_bank_account.company_id' => $company_id])
                ->get()
                ->getRowArray();
        } elseif (empty($sub_account_id) && !empty($bank_account_id)) {
            $data_bank = $bankAccountModel
                ->select(
                 "tb_autopay_bank_account.id, tb_autopay_bank.brand_name, tb_autopay_bank_account.account_holder_name, tb_autopay_bank_account.account_number"
                )
                ->join("tb_autopay_bank", "tb_autopay_bank.id = tb_autopay_bank_account.bank_id", "left")
                ->where([
                    'tb_autopay_bank_account.company_id' => $company_id,
                    'tb_autopay_bank_account.id' => $bank_account_id,
                ])
                ->get()
                ->getRowArray();
        } elseif (!empty($sub_account_id) && !empty($bank_account_id)) {
            $data_bank = $bankSubAccountModel
                ->select(
                    "tb_autopay_bank_sub_account.id, tb_autopay_bank.brand_name, tb_autopay_bank_account.account_holder_name, tb_autopay_bank_account.account_number, tb_autopay_bank_sub_account.sub_account, tb_autopay_bank_sub_account.label"
                )
                ->join("tb_autopay_bank_account", "tb_autopay_bank_account.id = tb_autopay_bank_sub_account.bank_account_id")
                ->join("tb_autopay_bank", "tb_autopay_bank.id = tb_autopay_bank_account.bank_id", "left")
                ->where([
                    'tb_autopay_bank_account.company_id' => $company_id,
                    'tb_autopay_bank_sub_account.id' => $sub_account_id,
                    'tb_autopay_bank_sub_account.bank_account_id' => $bank_account_id,
                ])
                ->get()
                ->getRowArray();
        }

        if (empty($data_bank)) {
            return $this->respond(['status' => false, 'message' => "Không tìm thấy thông tin tài khoản, hãy liên hệ sepay"]);
        }

        return $this->respond(['status' => true, 'data' => $data_bank]);
    }


    public function config_larkmessenger_add($data_session) {

        if(!has_permission('NotificationLarkMessenger', 'can_add'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền thêm"));

 
         // Load validation service
        $validation = \Config\Services::validation();
        helper('text');

        // Thiết lập các rules cho `$data_session`
        $rules = [
            'bank_account_id' => 'integer|is_natural',
            'transaction_type' => 'required|in_list[All,In_only,Out_only]',
            'contains_content' => [
                'label' => 'Khi nội dung thanh toán có từ',
                'rules' => 'max_length[5000]'
            ],
            'description' => [
                'label' => 'Đặt tên',
                'rules' => 'required|min_length[2]|max_length[1000]'
            ],
            'bot_webhook_url' => [
                'label' => 'Bot WebHook URL',
                'rules' => 'required|min_length[50]|max_length[500]'
            ],
            'ignore_phrases' => [
                'label' => 'Bỏ qua nếu nội dung thanh toán có từ',
                'rules' => 'max_length[5000]'
            ],
            'active' => 'required|in_list[0,1]',

            'template_name' => [
                'label' => 'Mẫu tin',
                'rules' => 'permit_empty|in_list[template_1,template_2,template_3]',
                'errors' => [
                    'in_list' => '{field} phải là một trong các giá trị: template_1, template_2, template_3.'
                ]
            ],
            'template_custom' => [
                'label' => 'Tùy chỉnh nội dung',
                'rules' => 'permit_empty|min_length[10]|max_length[1000]',
                'errors' => [
                    'min_length' => '{field} phải có ít nhất 10 ký tự.',
                    'max_length' => '{field} không được vượt quá 1000 ký tự.'
                ]
            ]
            
        ];

        // Áp dụng validation với `$data_session`
        $validation->setRules($rules);

        if (!$validation->run($data_session)) {
            return [
                'status' => false,
                'message' => implode('. ', $validation->getErrors())
            ];
        }
        
        $bankAccountModel = model(BankAccountmodel::class);
        $bankSubAccountModel = model(BankSubAccountmodel::class);
        $notificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);

        $bank_account_id =  $data_session['bank_account_id'];
        $sub_account_id =  $data_session['sub_account_id'];

        if(is_numeric($bank_account_id) && $bank_account_id > 0) {
            $bankAccountModel = model(BankAccountmodel::class);
            $bank_account = $bankAccountModel->where(['company_id' => $this->user_session['company_id'], 'id'=>$bank_account_id])->countAllResults();
            if($bank_account != 1)
            return array('status' => FALSE, 'message' => 'Không tìm thấy tài khoản ngân hàng chính mà bạn chọn');
        }
      
        $sub_account_id =  $data_session['sub_account_id'];

        if(is_numeric($sub_account_id) && $sub_account_id > 0) {
            $sub_account = $bankSubAccountModel->join("tb_autopay_bank_account","tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id")->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id'], 'tb_autopay_bank_sub_account.id'=>$sub_account_id])->countAllResults();
            if($sub_account != 1)
            return array('status' => FALSE, 'message' => 'Không tìm thấy tài khoản ngân hàng VA mà bạn chọn');
        
        }

        $amount_in_less_than_equal_to = $data_session['amount_in_less_than_equal_to'];
        $amount_in_great_than_equal_to = $data_session['amount_in_great_than_equal_to'];

        if(!is_numeric($amount_in_less_than_equal_to) || $amount_in_less_than_equal_to <= 0)
            $amount_in_less_than_equal_to = 0;

        if(!is_numeric($amount_in_great_than_equal_to) || $amount_in_great_than_equal_to <= 0)
            $amount_in_great_than_equal_to = 0;
            
        if($amount_in_less_than_equal_to != 0 && $amount_in_great_than_equal_to != 0) {
            if($amount_in_great_than_equal_to > $amount_in_less_than_equal_to) {
                return array('status' => FALSE, 'message' => "Vui lòng nhập giá trị 'Tiền vào lớn hơn hoặc bằng' phải nhỏ hơn điều kiện 'Tiền vào nhỏ hơn hoặc bằng'");

            }
        }

       
        $data = array(
            'company_id' => $this->user_session['company_id'],
            'bank_account_id' => $data_session['bank_account_id'],
            //'sub_account_id' => $this->request->getPost('sub_account_id'),
            'transaction_type' => $data_session['transaction_type'],
            //'webhook_type' => $this->request->getVar('webhook_type'),
            //'verify_payment' => $this->request->getVar('verify_payment'),
            'contains_content' => $data_session['contains_content'],
            'bot_webhook_url' => trim($data_session['bot_webhook_url']),
            'description' => xss_clean($data_session['description']),
            //'hide_accumulated' => $this->request->getVar('hide_accumulated'),
            //'hide_details_link' => $this->request->getVar('hide_details_link'),
            'amount_in_less_than_equal_to' => $amount_in_less_than_equal_to,
            'amount_in_great_than_equal_to' => $amount_in_great_than_equal_to,
            'hide_accumulated' =>  trim($data_session['hide_accumulated']),
            'hide_details_link' =>  trim($data_session['hide_details_link']),
            'ignore_phrases' => trim($data_session['ignore_phrases']),
            'active' => $data_session['active'],
            'is_template_custom' => $data_session['is_template_custom'] ?? 0,
            'template_name' => $data_session['template_name'] ?? "template_1",
            'template_custom' => $data_session['template_custom'] ?? "",
        );

        if (!empty($data['template_custom'])) {
            $data['template_custom'] = preg_replace('/<[^>]+>/', '', $data['template_custom']);
            $data['template_custom'] = allowed_tags($data['template_custom']);
        }

        if (!empty($data['contains_content'])) {
            $data['contains_content'] = str_replace([',', '"'], ['|', ''], $data['contains_content']);
            $data['contains_content'] = allowed_tags($data['contains_content']);
        }
        
        if (!empty($data['ignore_phrases'])) {
            $data['ignore_phrases'] = str_replace([',', '"'], ['|', ''], $data['ignore_phrases']);
            $data['ignore_phrases'] = allowed_tags($data['ignore_phrases']);
        }


        if(is_numeric($sub_account_id) && $sub_account_id > 0)
            $data['sub_account_id'] = $sub_account_id;
        else
            $data['sub_account_id'] = 0;


        $webhook_type = $this->request->getVar('webhook_type');
        $verify_payment = $this->request->getVar('verify_payment');

        if(!in_array($webhook_type, ['All','Success','Failed','No']))
            $data['webhook_type'] = "No";
        else
            $data['webhook_type'] = $webhook_type;

        if(!in_array($verify_payment, ['Skip','Success','Failed','Success_Or_Failed','No']))
            $data['verify_payment'] = "Skip";
        else
            $data['verify_payment'] = $verify_payment;

        
                    
        $notificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);
        $result = $notificationLarkMessengerModel->insert($data);
        if($result) {
            add_user_log(array('data_id'=>$result,'company_id' => $this->user_session['company_id'], 'data_type'=>'notification_lark_mesenger_add','description'=>'Thêm tích hợp thông báo Lark Messenger','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
            return array("status" => true, 'id' => $result);
        } else {
            add_user_log(array('data_id'=>0,'company_id' => $this->user_session['company_id'], 'data_type'=>'notification_lark_mesenger_add','description'=>'Thêm tích hợp thông báo Lark Messenger','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Failed'));
            return array("status" => false, "message" => "Không thể thêm điều kiện này. Vui lòng liên hệ SePay để được hỗ trợ.");
        }
    
    }

    public function update_integration($id){

        $data_input = $this->request->getPost();
        $data_input['template_custom'] = str_ireplace('script', '', $data_input['template_custom']);
        $NotificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);
        $check_exits = $NotificationLarkMessengerModel->where(['id'=>$id,'company_id'=>$this->company_details->id])->find();
        if(empty($check_exits)){
            return $this->respond(['status' => false,'message'=>"Không được chỉnh thông tin này!"]);
        }
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if(!has_permission('NotificationLarkMessenger', 'can_edit'))
            return $this->response->setJSON(array("status"=>FALSE,"message"=>"Bạn không có quyền sửa dữ liệu"));

 
        $validation =  \Config\Services::validation();

        helper('text');

        if(! $this->validate([
            'bank_account_id' => 'integer|is_natural',
            //'sub_account_id' => 'integer|is_natural',
            'id' => 'required|integer|is_natural',
            'transaction_type' => "required|in_list[All,In_only,Out_only]",
           // 'webhook_type' => "required|in_list[All,Success,Failed,No]",
            'contains_content' => ['label' => 'Khi nội dung thanh toán có từ', 'rules' => "max_length[5000]"],
           // 'verify_payment' => "required|in_list[Skip,Success,Failed,Success_Or_Failed,No]",
            'description' => ['label' => 'Đặt tên', 'rules' => "required|min_length[2]|max_length[1000]"],
            'bot_webhook_url' => ['label' => 'URL BOT Lark', 'rules' =>"required|min_length[3]|max_length[1000]|regex_match[/^https:\/\/open\.larksuite\.com\/open-apis\/bot\/v2\/hook\/[a-zA-Z0-9\-]{36}$/]"],
            //'hide_accumulated' => "required|in_list[0,1]",
            //'hide_details_link' => "required|in_list[0,1]",
            'ignore_phrases' => ['label' => 'Bỏ qua nếu nội dung thanh toán có từ', 'rules' => "max_length[5000]"],
            'active' => "required|in_list[0,1]",
            'template_custom' => 'permit_empty|min_length[10]|max_length[1000]',
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>'Thông tin cấu hình không hợp lệ','data' => $validation->getErrors()));
        }


        $bankAccountModel = model(BankAccountmodel::class);
        $bankSubAccountModel = model(BankSubAccountmodel::class);
        $notificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);

        $bank_account_id = $this->request->getPost('bank_account_id');
        $sub_account_id = $this->request->getPost('sub_account_id');

        if(is_numeric($bank_account_id) && $bank_account_id > 0) {
            $bankAccountModel = model(BankAccountmodel::class);
            $bank_account = $bankAccountModel->where(['company_id' => $this->user_session['company_id'], 'id'=>$bank_account_id])->countAllResults();
            if($bank_account != 1)
                return $this->response->setJSON(array('status'=>FALSE,'message'=>'Không tìm thấy tài khoản ngân hàng chính mà bạn chọn'));    
        }
      
        $sub_account_id = $this->request->getPost('sub_account_id');

        if(is_numeric($sub_account_id) && $sub_account_id > 0) {
            $sub_account = $bankSubAccountModel->join("tb_autopay_bank_account","tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id")->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id'], 'tb_autopay_bank_sub_account.id'=>$sub_account_id])->countAllResults();
            if($sub_account != 1)
                return $this->response->setJSON(array('status'=>FALSE,'message'=>'Không tìm thấy tài khoản ngân hàng phụ mà bạn chọn'));
           
        
        }
        
        $telegram_id = $this->request->getPost('id');
        
        $telegram_details = $notificationLarkMessengerModel->where(["id" =>$telegram_id,"company_id"=>$this->user_session['company_id']])->get()->getRow();

        if(!is_object($telegram_details))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>'Không tìm thấy dữ liệu cần cập nhật'));


        $amount_in_less_than_equal_to = $this->request->getPost('amount_in_less_than_equal_to');
        $amount_in_great_than_equal_to = $this->request->getPost('amount_in_great_than_equal_to');

        if(!is_numeric($amount_in_less_than_equal_to) || $amount_in_less_than_equal_to <= 0)
            $amount_in_less_than_equal_to = 0;

        if(!is_numeric($amount_in_great_than_equal_to) || $amount_in_great_than_equal_to <= 0)
            $amount_in_great_than_equal_to = 0;
            
        if($amount_in_less_than_equal_to != 0 && $amount_in_great_than_equal_to != 0) {
            if($amount_in_great_than_equal_to > $amount_in_less_than_equal_to) {
                return $this->response->setJSON(array('status'=>FALSE,'message'=>"Vui lòng nhập giá trị 'Số tiền lớn hơn hoặc bằng' phải nhỏ hơn điều kiện 'số tiền nhỏ hơn hoặc bằng'"));

            }
        }

        $data = array(
            'bank_account_id' => $this->request->getPost('bank_account_id'),
           // 'sub_account_id' => $this->request->getPost('sub_account_id'),
            'transaction_type' => $this->request->getVar('transaction_type'),
            //'webhook_type' => $this->request->getVar('webhook_type'),
            //'verify_payment' => $this->request->getVar('verify_payment'),
            'contains_content' => $this->request->getVar('contains_content'),
            'description' => xss_clean($this->request->getVar('description')),
            'bot_webhook_url' => trim($this->request->getVar('bot_webhook_url')),
            //'hide_accumulated' => $this->request->getVar('hide_accumulated'),
            //'hide_details_link' => $this->request->getVar('hide_details_link'),
            'amount_in_less_than_equal_to' => $amount_in_less_than_equal_to,
            'amount_in_great_than_equal_to' => $amount_in_great_than_equal_to,
            'ignore_phrases' => trim($this->request->getVar('ignore_phrases')),
            'active' => $this->request->getVar('active'),
        );
       

        if (!empty($data['template_custom'])) {
            $data['template_custom'] = preg_replace('/<[^>]+>/', '', $data['template_custom']);
            $data['template_custom'] = allowed_tags($data['template_custom']);
        }

        if (!empty($data['contains_content'])) {
            $data['contains_content'] = str_replace([',', '"'], ['|', ''], $data['contains_content']);
            $data['contains_content'] = allowed_tags($data['contains_content']);
        }

        if (!empty($data['ignore_phrases'])) {
            $data['ignore_phrases'] = str_replace([',', '"'], ['|', ''], $data['ignore_phrases']);
            $data['ignore_phrases'] = allowed_tags($data['ignore_phrases']);
        }


        if(is_numeric($sub_account_id) && $sub_account_id > 0)
            $data['sub_account_id'] = $sub_account_id;
        else
            $data['sub_account_id'] = 0;


        $webhook_type = $this->request->getVar('webhook_type');
        $verify_payment = $this->request->getVar('verify_payment');

        if(!in_array($webhook_type, ['All','Success','Failed','No']))
            $data['webhook_type'] = "No";
        else
            $data['webhook_type'] = $webhook_type;

        if(!in_array($verify_payment, ['Skip','Success','Failed','Success_Or_Failed','No']))
            $data['verify_payment'] = "Skip";
        else
            $data['verify_payment'] = $verify_payment;
            
        $result = $notificationLarkMessengerModel->set($data)->where(["id" =>$telegram_id,"company_id"=>$this->user_session['company_id']])->update();
        
        if($result) { 
            add_user_log(array('data_id'=>$telegram_id,'company_id' => $this->user_session['company_id'], 'data_type'=>'notification_telegram_update','description'=>'Sửa tích hợp thông báo Telegram','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));
            
            // update thông tin tích hợp message custom
            if(! $this->validate([
                'id' => ['label' => 'ID', 'rules' => 'required|integer|is_natural'],
                'is_template_custom' => ['label' => 'Loại nội dung', 'rules' => "required|in_list[0,1]"],
                'template_name' => ['label' => 'Mẫu tin', 'rules' => "in_list[template_1,template_2,template_3]"],
                'template_custom' => ['label' => 'Tùy chỉnh nội dung', 'rules' => 'min_length[10]|max_length[1000]']
            ])) {
                log_message("debug",json_encode(implode(". ", $validation->getErrors())));
                return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
            }
    
            $notificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);
    
            $id = $this->request->getVar('id');
            $is_template_custom = $this->request->getVar('is_template_custom');
            $hide_accumulated = $this->request->getVar('hide_accumulated');
            $hide_details_link = $this->request->getVar('hide_details_link');
            
    
            $telegram_details = $notificationLarkMessengerModel->where(['id'=>$id, 'company_id' => $this->user_session['company_id']])->get()->getRow();
    
            if(!is_object($telegram_details))
                return $this->response->setJSON(array('status'=>FALSE,'message'=>'Không tìm thấy tích hợp này'));    
    
            if($is_template_custom == 0) {
                if($hide_accumulated == "on")
                    $is_hide_accumulated = 1;
                else 
                    $is_hide_accumulated = 0;
                
                if($hide_details_link == "on")
                    $is_hide_details_link = 1;
                else
                    $is_hide_details_link = 0;
                
                $notificationLarkMessengerModel->set(['is_template_custom' => 0, 'hide_accumulated' => $is_hide_accumulated, 'hide_details_link' => $is_hide_details_link])->where(['id'=>$id, 'company_id' => $this->user_session['company_id']])->update();
    
            } else if($is_template_custom == 1)   {
                if(! $this->validate([
                    'template_custom' => ['label' => 'Tùy chỉnh nội dung', 'rules' => 'required|min_length[10]|max_length[1000]']
                ])) {
                    return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
                }
                $select_template = $this->request->getVar('template_name');
    
                $content_custom = $this->request->getVar('template_custom');
    
                $content_custom = strip_tags($content_custom, '<code><a><b><i>');
                $content_custom = preg_replace('/<[^>]+>/', '', $content_custom);
                $valid_html = validHTML($content_custom);
                
                if($valid_html !== true)
                    return $this->response->setJSON(array('status'=>FALSE,'message'=>'Lỗi: HTML không đúng định dạng. ' . $valid_html));    
    
                $notificationLarkMessengerModel->set(['is_template_custom' => 1,'template_custom' => $content_custom, 'template_name' => $select_template])->where(['id'=>$id, 'company_id' => $this->user_session['company_id']])->update();
    
    
            }
            
            return $this->response->setJSON(['status' => true,'message'=>"Cập nhật thành công!",'data'=>$data_input]);
        } else {
            add_user_log(array('data_id'=>$telegram_id,'company_id' => $this->user_session['company_id'], 'data_type'=>'notification_larkmessenger_update','description'=>'Sửa tích hợp thông báo Lark messenger','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Failed'));
            return $this->response->setJSON(array("status"=>false,"message"=>"Không thể cập nhật dữ liệu này!"));
        }

    }
 
    // end custom ui lark messenger
}

