<?php

namespace App\Controllers\Internalapi\V1;

use App\Controllers\Internalapi\V1\BaseController;
use App\Models\AddonModel;
use App\Models\ChannelPartnerCommission;
use App\Models\ChannelPartnerCommissionModel;
use App\Models\ChannelPartnerCompanyModel;
use App\Models\ChannelPartnerModel;
use App\Models\ChannelPartnerOfficerModel;
use App\Models\CompanySubscriptionModel;
use App\Models\InvoiceModel;
use App\Models\OrderModel;
use App\Models\ProductModel;
use Config\InternalApi;

class Cpacommission extends BaseController
{
  

     // [POST] method insert data partner commisson
     public function create(){

        $invoice_id = json_decode($this->request->getBody(), true)['invoice_id']??"";
        $validation = service('validation');

        if (!$validation->setRules([
            'invoice_id' => 'required|numeric|max_length[11]|is_not_unique[tb_autopay_invoice.id]'
        ])->run(['invoice_id' => $invoice_id])) {
            return $this->responseResult(422, $validation->getErrors());
        }
        
        $InvoiceModel = model(InvoiceModel::class);
        $OrderModel = model(OrderModel::class);
        $ChannelPartnerCompanyModel = model(ChannelPartnerCompanyModel::class);
        $ChannelPartnerOfficerModel = model(ChannelPartnerOfficerModel::class);
        $CompanySubscriptionModel = model(CompanySubscriptionModel::class);
        $ChannelPartnerModel = model(ChannelPartnerModel::class);
        $configInternalApi = config(InternalApi::class);
        $array_invoice_allow_commission = $configInternalApi->invoiceAllowCommission ?? [];
        
        // check status invoice
        $data_invoice = $InvoiceModel->where(['id'=>$invoice_id])->get()->getRowArray();
        if($data_invoice['status']!="Paid"){
            return $this->responseResult(422,["invoice_id"=>" $invoice_id -  Đơn hàng chưa được thanh toán"]);
        }

        if (!in_array($data_invoice['type'], $array_invoice_allow_commission)) {
            return $this->responseResult(422, [
                "invoice_id" => " $invoice_id - Đơn hàng không hợp lệ để tính hoa hồng. Các loại đơn hàng hợp lệ: " . implode(', ', $array_invoice_allow_commission)
            ]);
        }
        
        //  check exits invoice
        $data_channle_partner_company = $ChannelPartnerCompanyModel->where(['company_id'=>$data_invoice['company_id']])->get()->getRowArray();
        if(empty($data_channle_partner_company)){
            return $this->responseResult(422,["invoice_id"=>" $invoice_id - Đơn hàng không thuộc hệ thống CPA"]);
        }

        // Tìm order ID từ bảng OrderModel
        $orderData = $OrderModel->where(['invoice_id' => $invoice_id])->get()->getRowArray();
        $order_id = $orderData['id'] ?? "";

        // Nếu không tìm thấy trong OrderModel, tìm từ bảng CompanySubscriptionModel
        if (empty($order_id)) {
            $subscriptionData = $CompanySubscriptionModel->select('order_id')
                ->where(['company_id' => $data_invoice['company_id']])
                ->get()
                ->getRowArray();
            $order_id = $subscriptionData['order_id'] ?? "";
        }

       

        // filter rate
        $rate =  $ChannelPartnerOfficerModel->where(['id'=>$data_channle_partner_company['channel_partner_officer_id']])->get()->getRowArray()['commission_rate']??"";
        if(number_format($rate)==0){
            $rate = $ChannelPartnerModel->where(['id'=>$data_channle_partner_company['channel_partner_id']])->get()->getRowArray()['default_commission_rate']??"";
        }

        //  calc commission
        $commission_amount = intval(($data_invoice['total'] * $rate) / 100);
        
        $data_commission = array(
            'channel_partner_id'    => $data_channle_partner_company['channel_partner_id'],
            'channel_partner_officer_id'    => $data_channle_partner_company['channel_partner_officer_id'],
            'order_id'    => $order_id,
            'invoice_id'    => $invoice_id,
            'type'          => 'Register',
            'rate' => $rate,
            'commission_amount' => $commission_amount,
            'notes'=>"Hoa hồng đơn hàng"
        );

        $validationRules = [
            'channel_partner_id'    => 'required|max_length[11]|is_not_unique[tb_autopay_channel_partner.id]',
            'channel_partner_officer_id'    => 'required|max_length[11]|is_not_unique[tb_autopay_channel_partner_officer.id]',
            'order_id'    => 'required|max_length[11]|is_not_unique[tb_autopay_order.id]',
            'invoice_id'    => 'required|max_length[11]|is_not_unique[tb_autopay_invoice.id]|is_unique[tb_autopay_channel_partner_commission.invoice_id]',
            'type'          => 'required|in_list[Register,Renewal]',
            'rate' => 'required|numeric',
            'commission_amount' => 'required|numeric',

        ];
    
        $datalog = array(
            'channel_partner_id' => $data_channle_partner_company['channel_partner_id'] ?? "",
            'data_type' => "json",
            'ip' => $this->request->getIPAddress() ?? "",
            'user_agent' => trim(xss_clean($this->request->getUserAgent()->getAgentString())),
            'description' => "",
            'status' =>  "",
            'data_id' => null,
        );


        $validation = service('validation');
    
        if (!$validation->setRules($validationRules)->run($data_commission)) {
            return $this->responseResult(422, $validation->getErrors());
        }
    
        try{
            //  add commisson
            $ChannelPartnerComissonModel = model(ChannelPartnerCommissionModel::class);
            $insert_data = $ChannelPartnerComissonModel->insert($data_commission);

            if($insert_data){
                $id_insert = $ChannelPartnerComissonModel->insertID();
                
                // savelog
                $datalog['description'] = "Tạo Commission!";
                $datalog['status'] = "Success";
                $datalog['data_id'] = $id_insert;
                Cpalog::savelog($datalog);
                
                return $this->responseResult(200,"Thêm hoa hồng thành công!",['channel_partner_commisson_id' => $id_insert]);
               
            }else{

                // savelog
                $datalog['description'] = "Tạo Commission!";
                $datalog['status'] = "Failed";
                $datalog['data_id'] = null;
                Cpalog::savelog($datalog);

                return $this->responseResult(500,"Lỗi hệ thống, không thể lấy id insert Commission, Hãy liên hệ kỹ thuật!");
            }

        }catch(\Exception $e){
            // savelog
            $datalog['description'] = "Tạo Commission!";
            $datalog['status'] = "Failed";
            $datalog['data_id'] = null;
            Cpalog::savelog($datalog);
            
            log_message("error","Lỗi hệ thống thêm channnel partner comission: ".$e->getMessage());
            return $this->responseResult(500,"Lỗi hệ thống, không thể thêm Commission, Hãy liên hệ kỹ thuật!");

        }



    }

    // [GET]
    public function all() {
        $request = service('request');

        if (strtoupper($request->getMethod()) !== 'GET') {
            return $this->responseResult(405, "Phương thức URL không hợp lệ!");
        }

        $ChannelPartnerComissonModel = model(ChannelPartnerCommissionModel::class);

        $channel_partner_id = $request->getGet('channel_partner_id');
        $channel_partner_officer_id = $request->getGet('channel_partner_officer_id');
        $status = $request->getGet('status');

        $builder = $ChannelPartnerComissonModel->select('
        tb_autopay_channel_partner_officer.name channel_partner_officer_name,
        tb_autopay_channel_partner_officer.email channel_partner_officer_email,

        tb_autopay_invoice.company_id company_id,

        tb_autopay_company.full_name company_full_name,
        tb_autopay_company.short_name company_short_name,
        tb_autopay_company.status company_status,
        
        tb_autopay_channel_partner_commission.*,
        ')
        ->join('tb_autopay_channel_partner_officer','tb_autopay_channel_partner_officer.id = tb_autopay_channel_partner_commission.channel_partner_officer_id','left')
        ->join('tb_autopay_invoice','tb_autopay_invoice.id = tb_autopay_channel_partner_commission.invoice_id','left')
        ->join('tb_autopay_company','tb_autopay_company.id = tb_autopay_invoice.company_id','left')
        
        ->orderBy('tb_autopay_channel_partner_commission.id desc');

        if ($channel_partner_id !== null) {
            $builder->where('tb_autopay_channel_partner_commission.channel_partner_id', $channel_partner_id);
        }
    
        if ($channel_partner_officer_id !== null) {
            $builder->where('tb_autopay_channel_partner_commission.channel_partner_officer_id', $channel_partner_officer_id);
        }

        if ($status !== null) {
            $builder->where('tb_autopay_channel_partner_commission.status', $status);
        }

        $data = $builder->get()->getResultArray();

        if(!empty($data)){
            foreach($data as &$val){
                $val['commission_id'] = $val['id'];
            }
            unset($val);
        }

        return $this->responseResult(200, "Danh sách commission!", $data);
    }

    // [GET]
    public function detail($id) {
        $request = service('request');

        if (strtoupper($request->getMethod()) !== 'GET') {
            return $this->responseResult(405, "Phương thức URL không hợp lệ!");
        }

        if(empty($id)){
            return $this->responseResult(404,"ID không được phép rỗng");
        }

        $ChannelPartnerComissonModel = model(ChannelPartnerCommissionModel::class);

       
        $channel_partner_id = $request->getGet('channel_partner_id');
        $channel_partner_officer_id = $request->getGet('channel_partner_officer_id');
        $condition = array();

        // Thêm điều kiện nếu có channel_partner_officer_id
        if (isset($channel_partner_officer_id)) {
            $condition['tb_autopay_channel_partner_commission.channel_partner_officer_id'] = $channel_partner_officer_id;
        }

        // Thêm điều kiện nếu có channel_partner_id
        if (isset($channel_partner_id)) {
            $condition['tb_autopay_channel_partner_commission.channel_partner_id'] = $channel_partner_id;
        }

        // Thêm điều kiện company_id
        $condition['tb_autopay_channel_partner_commission.id'] = $id;

        $builder = $ChannelPartnerComissonModel->select('
        tb_autopay_channel_partner_commission.id commission_id,
        tb_autopay_channel_partner_commission.rate commission_rate,
        tb_autopay_channel_partner_commission.type commission_type,
        tb_autopay_channel_partner_commission.status commission_status,
        tb_autopay_channel_partner_commission.commission_amount,
        tb_autopay_channel_partner_commission.notes commission_note,
        tb_autopay_channel_partner_commission.rejection_reasons commission_rejection_reasons,
        tb_autopay_channel_partner_commission.created_at commission_created_at,
        tb_autopay_channel_partner_commission.updated_at commission_updated_at,
        
        tb_autopay_channel_partner.id channel_partner_id,
        tb_autopay_channel_partner.name channel_partner_name,
        tb_autopay_channel_partner.logo_path channel_partner_logo,
        tb_autopay_channel_partner.active channel_partner_active,
        tb_autopay_channel_partner.default_commission_rate channel_partner_default_rate,


        tb_autopay_channel_partner_officer.id channel_partner_officer_id,
        tb_autopay_channel_partner_officer.name channel_partner_officer_name,
        tb_autopay_channel_partner_officer.email channel_partner_officer_email,
        tb_autopay_channel_partner_officer.active channel_partner_officer_active,
        tb_autopay_channel_partner_officer.role channel_partner_officer_role,
        tb_autopay_channel_partner_officer.commission_rate channel_partner_officer_commission_rate,
        tb_autopay_channel_partner_officer.balance_amount channel_partner_officer_balance_amount,

        tb_autopay_order.id order_id,
        tb_autopay_order.total order_total,
        tb_autopay_order.status order_status,
        tb_autopay_order.created_at order_created_at,

        tb_autopay_invoice.id invoice_id,
        tb_autopay_invoice.total invoice_total,
        tb_autopay_invoice.subtotal invoice_subtotal,
        tb_autopay_invoice.status invoice_status,
        tb_autopay_invoice.type invoice_type,
        tb_autopay_invoice.tax_rate invoice_tax_rate,
        tb_autopay_invoice.tax invoice_tax,
        tb_autopay_invoice.created_at invoice_created_at,
        tb_autopay_invoice.public_note invoice_public_note,

        tb_autopay_invoice_item.type invoice_item_type,
        tb_autopay_invoice_item.item_id invoice_item_id,
        tb_autopay_invoice_item.description invoice_item_description,
        tb_autopay_invoice_item.amount invoice_item_amount,

        tb_autopay_company.id company_id,
        tb_autopay_company.full_name company_fullname,
        tb_autopay_company.short_name company_shortname,
        tb_autopay_company.status company_status,
        tb_autopay_company.created_at company_created_at,

        ')
        ->join('tb_autopay_channel_partner','tb_autopay_channel_partner.id = tb_autopay_channel_partner_commission.channel_partner_id')
        ->join('tb_autopay_channel_partner_officer','tb_autopay_channel_partner_officer.id = tb_autopay_channel_partner_commission.channel_partner_officer_id')
        ->join('tb_autopay_order','tb_autopay_order.id = tb_autopay_channel_partner_commission.order_id')
        ->join('tb_autopay_invoice','tb_autopay_invoice.id = tb_autopay_channel_partner_commission.invoice_id')
        ->join('tb_autopay_invoice_item','tb_autopay_invoice_item.invoice_id = tb_autopay_channel_partner_commission.invoice_id')

        ->join('tb_autopay_company','tb_autopay_company.id = tb_autopay_invoice.company_id')
        ->orderBy('tb_autopay_channel_partner_commission.id  desc')
        ->where($condition);
        $data = $builder->get()->getRowArray()??[];

        if(!empty($data)){
            if($data['invoice_item_type']=="Product"){
                $Productmodel = model(ProductModel::class);
                $info_product = $Productmodel->where(['id'=>$data['invoice_item_id']])->get()->getRowArray();
                $data['product_name'] = $info_product['name']??"NA/N";
                $data['product_description'] = $info_product['description']??"NA/N";
            }else{
                $Addonmodel = model(AddonModel::class);
                $info_product = $Addonmodel->where(['id'=>$data['invoice_item_id']])->get()->getRowArray();
                $data['product_name'] = $data['name']??"NA/N";
                $data['product_description'] = $data['description']??"NA/N";
            }
        }

        $data_response = array(
            'commission' => [],
            'channel_partner' => [],
            'channel_partner_officer' => [],
            'company' => [],
            'order' => [],
            'invoice' => []
            
        );
        if(!empty($data)){
            $data_response = [
                'commission' => [
                    'id' => $data['commission_id'] ?? null,
                    'rate' => $data['commission_rate'] ?? null,
                    'type' => $data['commission_type'] ?? null,
                    'status' => $data['commission_status'] ?? null,
                    'amount' => $data['commission_amount'] ?? null,
                    'notes' => $data['commission_note'] ?? null,
                    'rejection_reasons' => $data['commission_rejection_reasons'] ?? null,
                    'created_at' => $data['commission_created_at'] ?? null,
                    'updated_at' => $data['commission_updated_at'] ?? null,
                ],
                'channel_partner' => [
                    'id' => $data['channel_partner_id'] ?? null,
                    'name' => $data['channel_partner_name'] ?? null,
                    'logo_path' => $data['channel_partner_logo'] ?? null,
                    'active' => $data['channel_partner_active'] ?? null,
                    'default_commission_rate' => $data['channel_partner_default_rate'] ?? null,
                ],
                'channel_partner_officer' => [
                    'id' => $data['channel_partner_officer_id'] ?? null,
                    'name' => $data['channel_partner_officer_name'] ?? null,
                    'email' => $data['channel_partner_officer_email'] ?? null,
                    'active' => $data['channel_partner_officer_active'] ?? null,
                    'role' => $data['channel_partner_officer_role'] ?? null,
                    'commission_rate' => $data['channel_partner_officer_commission_rate'] ?? null,
                    'balance_amount' => $data['channel_partner_officer_balance_amount'] ?? null,
                ],
                'company' => [
                    'id' => $data['company_id'] ?? null,
                    'fullname' => $data['company_fullname'] ?? null,
                    'shortname' => $data['company_shortname'] ?? null,
                    'status' => $data['company_status'] ?? null,
                    'created_at' => $data['company_created_at'] ?? null,
                ],
                'order' => [
                    'id' => $data['order_id'] ?? null,
                    'total' => $data['order_total'] ?? null,
                    'status' => $data['order_status'] ?? null,
                    'created_at' => $data['order_created_at'] ?? null,
                ],
                'invoice' => [
                    'id' => $data['invoice_id'] ?? null,
                    'description' => $data['invoice_item_description'] ?? null,
                    'total' => $data['invoice_total'] ?? null,
                    'subtotal' => $data['invoice_subtotal'] ?? null,
                    'status' => $data['invoice_status'] ?? null,
                    'type' => $data['invoice_type'] ?? null,
                    'tax_rate' => $data['invoice_tax_rate'] ?? null,
                    'rate' => $data['invoice_rate'] ?? null,
                    'created_at' => $data['invoice_created_at'] ?? null,
                    'public_note' => $data['invoice_public_note'] ?? null,
                    
                    'product_id' => $data['invoice_item_id'] ?? null,
                    'product_name' => $data['product_name'] ?? "NA/N",
                    'product_description' => $data['product_description'] ?? "NA/N",
                    'product_type' => $data['invoice_item_type'] ?? null,
                    'product_amount' => $data['invoice_item_amount'] ?? null,
                    
                ]
            ];
            
        }
            
        return $this->responseResult(200, "Chi tiết commission!", $data_response);        
    }

    // [POST]
    public function update_status(){

        
        $input = $this->request->getBody();

        $data = json_decode($input, true);

        $ChannelPartnerCommissonModel = model(ChannelPartnerCommissionModel::class);

        
        $validationRules = [
            'commission_id'    => 'required|max_length[11]|is_not_unique[tb_autopay_channel_partner_commission.id]',
            'status'     => 'required|in_list[Approved,Rejected]',
            'channel_partner_id' =>'required|max_length[11]|is_not_unique[tb_autopay_channel_partner_commission.channel_partner_id]',
        ];
    
        $validation = service('validation');
    
        if (!$validation->setRules($validationRules)->run($data)) {
            return $this->responseResult(422, $validation->getErrors());
        }

        if($data['status']=="Rejected"){
            if(!$validation->setRules(['rejection_reasons'=>'required'])->run(['rejection_reasons'=>$data['rejection_reasons']??""])){
                return $this->responseResult(422, $validation->getErrors());
            }

        }

        $check_approved = $ChannelPartnerCommissonModel->where(['id'=>$data['commission_id']])->get()->getRowArray();
        if($check_approved['status']=="Approved"){
            return $this->responseResult(500,"Commission đã được Approved, Không thể chỉnh lại trạng thái");
        }
        
        //  filter data
        $data_update = array(
            'status' => $data['status'],
            'rejection_reasons' => $data['status']=="Rejected" ? $data['rejection_reasons'] : null
        );

        try{
            $update = $ChannelPartnerCommissonModel->update($data['commission_id'],$data_update);
            if(!empty($update)){

                if($data_update['status']=="Rejected"){
                    return $this->responseResult(200,"Cập nhật trạng thái hoa hồng thành công!");
                }


                //  update balance status Approved
                if($data_update['status']=="Approved"){
                    $data_commission = $ChannelPartnerCommissonModel->where(['id'=>$data['commission_id']])->get()->getRowArray();
                    $update_balance = $this->update_balance_amount($data_commission['channel_partner_officer_id'],$data_commission['commission_amount']);
                    if(!empty($update_balance)){
    
                        return $this->responseResult(200,"Cập nhật trạng thái hoa hồng thành công!");
                    }else{
                        return $this->responseResult(500,"Cập nhật trạng thái hoa hồng thất bại!, lỗi cập nhật balance amount officer ");
    
                    }
                }

                return $this->responseResult(500,"Cập nhật sai dữ liệu, Hãy liên hệ kỹ thuật!");
                

            }else{
                return $this->responseResult(500,"Cập nhật trạng thái hoa hồng thất bại, hãy liên hệ kĩ thuật!");

            }

        }catch(\Exception $e){
            log_message("error","Lỗi hệ thống ".$e->getMessage());
            return $this->responseResult(500,"Lỗi cập nhật trạng thái commission, hãy liên hệ kĩ thuật");
        }

    }

    // update amount
    protected function update_balance_amount($id,$amount){
        $ChannelPartnerOfficerModel = model(ChannelPartnerOfficerModel::class);
        $data_officer = $ChannelPartnerOfficerModel->where(['id'=>$id])->get()->getRowArray();
        $old_balance_amount = $data_officer['balance_amount'];
        $new_balance_amount = $old_balance_amount + $amount;
        log_message("info","balace:" .$new_balance_amount);
        try{
            $update_balance = $ChannelPartnerOfficerModel->update($id,['balance_amount'=>$new_balance_amount]);
            if(!empty($update_balance)){
                return true;
            }else{
                return false;
            }

        }catch(\Exception $e){
            log_message("error","Lỗi hệ thống ".$e->getMessage());
            return false;
        }
    }
}
