<?php

namespace App\Controllers\Internalapi\V1;

use App\Controllers\Internalapi\V1\BaseController;
use App\Models\ChannelPartnerOfficerModel;
use App\Models\ChannelPartnerWithdrawModel;

class Cpawithdraw extends BaseController
{
   
    // [POST] 
    public function create(){

            $request = service('request');
            // data clean by filter
            $input = $request->getBody();
            $data = json_decode($input, true);
           
            $validationRules = [
                'channel_partner_id'    => 'required|max_length[11]|is_not_unique[tb_autopay_channel_partner.id]',
                'channel_partner_officer_id'    => 'required|max_length[11]|is_not_unique[tb_autopay_channel_partner_officer.id]',
                'status'          => 'required|in_list[Pending]',
                'withdraw_amount'    => 'required|decimal',
                'notes'    => 'required|min_length[11]',
            ];
        
    
            $validation = service('validation');
        
            if (!$validation->setRules($validationRules)->run($data)) {
                return $this->responseResult(422, $validation->getErrors());
            }
            $ChannelPartnerWithdrawModel = model(ChannelPartnerWithdrawModel::class);
            $ChannelPartnerOfficerModel = model(ChannelPartnerOfficerModel::class);

            $data_officer = $ChannelPartnerOfficerModel->where(['id'=>$data['channel_partner_officer_id']])->get()->getRowArray();
            
            if($data['withdraw_amount']>$data_officer['balance_amount']){
                return $this->responseResult(500,"Số dư hoa hồng hiện tại không đủ, hãy kiểm tra lại!");
            }

            if($data['withdraw_amount']<=0){
                return $this->responseResult(500,"Số tiền rút phải lớn hơn 0, hãy kiểm tra lại!");
            }

            $check_exit_req_pending = $ChannelPartnerWithdrawModel->where(['status'=>"Pending",'channel_partner_officer_id'=>$data['channel_partner_officer_id']])->get()->getRowArray();

            if(!empty($check_exit_req_pending)){
                return $this->responseResult(500,"Bạn không thể tạo thêm yêu cầu khi yêu cầu trước đó chưa được phê duyệt, hãy kiểm tra lại!");
            }

            $check_exit_req_progress = $ChannelPartnerWithdrawModel->where(['status'=>"InProgress",'channel_partner_officer_id'=>$data['channel_partner_officer_id']])->get()->getRowArray();
            if(!empty($check_exit_req_progress)){
                return $this->responseResult(500,"Bạn không thể tạo thêm yêu cầu khi yêu cầu trước đó đang xem xét, hãy kiểm tra lại!");
            }

            $datalog = array(
                'channel_partner_id' => $data['channel_partner_id'] ?? "",
                'data_type' => "json",
                'ip' => $this->request->getIPAddress() ?? "",
                'user_agent' => trim(xss_clean($this->request->getUserAgent()->getAgentString())),
                'description' => "",
                'status' =>  "",
                'data_id' => null,
            );

            try{
                $data_add = array(
                    'channel_partner_id' => $data['channel_partner_id'],
                    'channel_partner_officer_id' => $data['channel_partner_officer_id'],
                    'status' => trim(xss_clean($data['status'])),
                    'withdraw_amount' => trim(xss_clean($data['withdraw_amount'])),
                    'notes' => trim(($data['notes'])),
                );

                $insert_data = $ChannelPartnerWithdrawModel->insert($data_add);

                if($insert_data){

                    $id_insert = $ChannelPartnerWithdrawModel->insertID();    

                        // savelog
                        $datalog['description'] = "Tạo Withdraw!";
                        $datalog['status'] = "Success";
                        $datalog['data_id'] = $id_insert;
                        Cpalog::savelog($datalog);

                    return $this->responseResult(200,"Tạo yêu cầu thành công!",['withdraw_id' => $id_insert]);

                }else{
                     // savelog
                     $datalog['description'] = "Tạo Withdraw!";
                     $datalog['status'] = "Failed";
                     $datalog['data_id'] = null;
                     Cpalog::savelog($datalog);

                    return $this->responseResult(500,"Lỗi hệ thống, không thể lấy id insert Withdraw, Hãy liên hệ kỹ thuật!");
                }
    
            }catch(\Exception $e){
                 // savelog
                 $datalog['description'] = "Tạo Withdraw!";
                 $datalog['status'] = "Failed";
                 $datalog['data_id'] = null;
                 Cpalog::savelog($datalog);

                log_message("error","Lỗi hệ thông insert channnel partner Withdraw: ".$e->getMessage());
                return $this->responseResult(500,"Lỗi hệ thống, không thể thêm Withdraw, Hãy liên hệ kỹ thuật!");
    
            }
    
    
    
    }

     // [POST]
     public function update_status(){

        
        $input = $this->request->getBody();

        $data = json_decode($input, true);
        $ChannelPartnerWithdrawModel = model(ChannelPartnerWithdrawModel::class);
        $validationRules = [
            'channel_partner_withdraw_id'    => 'required|max_length[11]|is_not_unique[tb_autopay_channel_partner_withdraw.id]',
            'channel_partner_officer_id'    => 'permit_empty|max_length[11]|is_not_unique[tb_autopay_channel_partner_officer.id]',
            'channel_partner_id'    => 'required|max_length[11]|is_not_unique[tb_autopay_channel_partner_withdraw.channel_partner_id]',
            'status'     => 'required|in_list[InProgress,Approved,Rejected,Closed]',
        ];
         // Thêm quy tắc xác thực nếu có channel_partner_officer_id
        $channel_partner_officer_id = $data['channel_partner_officer_id']??"";
        if (!empty($channel_partner_officer_id)) {
            $conditon = array(
                'channel_partner_id' => $data['channel_partner_id'],
                'id' => $data['channel_partner_withdraw_id']??"",
                'channel_partner_officer_id' => $channel_partner_officer_id??"",
            );

            $result_check = $ChannelPartnerWithdrawModel->where($conditon)->findAll();
            if(empty($result_check)){
                return $this->responseResult(404,"Dữ liệu không thuộc hệ thống!");
            }
        }

    
        $validation = service('validation');
        
        if (!$validation->setRules($validationRules)->run($data)) {
            return $this->responseResult(422, $validation->getErrors());
        }


        if($data['status']=="Rejected"){
            if(!$validation->setRules(['rejection_reasons'=>'required'])->run(['rejection_reasons'=>$data['rejection_reasons']??""])){
                return $this->responseResult(422, $validation->getErrors());
            }

        }

        $data_check = array(
            'id' => $data['channel_partner_withdraw_id']
        );
        
        // Thêm điều kiện nếu có channel_partner_officer_id
        if (!empty($channel_partner_officer_id)) {
            $data_check['channel_partner_officer_id'] = $channel_partner_officer_id;
        }
        
        // Thêm điều kiện nếu có channel_partner_id
        if (isset($channel_partner_id)) {
            $data_check['channel_partner_id'] = $channel_partner_id;
        }
        // Kiểm tra dữ liệu trong db

        $check_db = $ChannelPartnerWithdrawModel->where($data_check)->get()->getRowArray();
        if (empty($check_db)) {
            
            return $this->responseResult(404,"Không tìm thấy yêu cầu trên hệ thống!");
        } 

        $check_data_withdraw = $ChannelPartnerWithdrawModel->where(['id'=>$data['channel_partner_withdraw_id']])->get()->getRowArray();
        if($check_data_withdraw['status']=="Approved" || $check_data_withdraw['status']=="Closed" || $check_data_withdraw['status']=="Rejected"){
            return $this->responseResult(500,"Yêu cầu đã được Approved, Rejected hoặc Closed, Không thể chỉnh lại trạng thái");
        }
        
        $data_update = array(
            'status' => trim(xss_clean($data['status'])),
            'rejection_reasons' => $data['status']=="Rejected" ? trim(xss_clean($data['rejection_reasons'])) : null
        );

        if($data['status']=="Closed"){
            if($check_data_withdraw['status']!="Pending"){
                return $this->responseResult(500,"Yêu cầu không phải trạng thái Pending, Không thể hủy yêu cầu");
            }
        }

        $datalog = array(
            'channel_partner_id' => $channel_partner_id ?? "",
            'data_type' => "json",
            'ip' => $this->request->getIPAddress() ?? "",
            'user_agent' => trim(xss_clean($this->request->getUserAgent()->getAgentString())),
            'description' => "",
            'status' =>  "",
            'data_id' => null,
        );

        try{
            $data_update = $ChannelPartnerWithdrawModel->update($data['channel_partner_withdraw_id'],$data_update);
            if(!empty($data_update)){

                if($data['status']=="Approved"){

                    $data_withdraw = $ChannelPartnerWithdrawModel->where(['id'=>$data['channel_partner_withdraw_id']])->get()->getRowArray();
                    $update_balance = $this->update_balance_amount($data_withdraw['channel_partner_officer_id'],$data_withdraw['withdraw_amount']);
                    if(!empty($update_balance)){

                         // savelog
                        $datalog['description'] = "Cập nhật trạng thái Withdraw!";
                        $datalog['status'] = "Success";
                        $datalog['data_id'] = null;
                        Cpalog::savelog($datalog);

                        return $this->responseResult(200,"Cập nhật trạng thái thành công!");
                    }else{
                         // savelog
                         $datalog['description'] = "Cập nhật trạng thái Withdraw!";
                         $datalog['status'] = "Failed";
                         $datalog['data_id'] = null;
                         Cpalog::savelog($datalog);

                        return $this->responseResult(500,"Cập nhật trạng thái thất bại!, lỗi cập nhật balance amount officer ");
    
                    }

                }else{
                     // savelog
                     $datalog['description'] = "Cập nhật trạng thái Withdraw!";
                     $datalog['status'] = "Success";
                     $datalog['data_id'] = null;
                     Cpalog::savelog($datalog);
                    return $this->responseResult(200,"Cập nhật trạng thái thành công!");
                }


            }else{
                 // savelog
                 $datalog['description'] = "Cập nhật trạng thái Withdraw!";
                 $datalog['status'] = "Failed";
                 $datalog['data_id'] = null;
                 Cpalog::savelog($datalog);
                return $this->responseResult(500,"Cập nhật trạng thái thất bại, hãy liên hệ kĩ thuật!");

            }

        }catch(\Exception $e){

             // savelog
             $datalog['description'] = "Cập nhật trạng thái Withdraw!";
             $datalog['status'] = "Failed";
             $datalog['data_id'] = null;
             Cpalog::savelog($datalog);
            log_message("error","Lỗi hệ thống ".$e->getMessage());
            return $this->responseResult(500,"Lỗi cập nhật trạng thái, hãy liên hệ kĩ thuật");
        }

    }

    // update amount
    protected function update_balance_amount($id,$amount){
        $ChannelPartnerOfficerModel = model(ChannelPartnerOfficerModel::class);
        $data_officer = $ChannelPartnerOfficerModel->where(['id'=>$id])->get()->getRowArray();
        $old_balance_amount = $data_officer['balance_amount'];
        $new_balance_amount = $old_balance_amount - $amount;
        log_message("info","balace:" .$new_balance_amount);
        try{
            $update_balance = $ChannelPartnerOfficerModel->update($id,['balance_amount'=>$new_balance_amount]);
            if(!empty($update_balance)){
                return true;
            }else{
                return false;
            }

        }catch(\Exception $e){
            log_message("error","Lỗi hệ thống ".$e->getMessage());
            return false;
        }
    }

    // [GET]
    public function all() {
        $request = service('request');

        if (strtoupper($request->getMethod()) !== 'GET') {
            return $this->responseResult(405, "Phương thức URL không hợp lệ!");
        }

        $ChannelPartnerWithdrawModel = model(ChannelPartnerWithdrawModel::class);

        $channel_partner_id = $request->getGet('channel_partner_id');
        $channel_partner_officer_id = $request->getGet('channel_partner_officer_id');
        $status = $request->getGet('status');

        $builder = $ChannelPartnerWithdrawModel->select('
        tb_autopay_channel_partner_officer.name channel_partner_officer_name,
        tb_autopay_channel_partner_officer.email channel_partner_officer_email,
        
        tb_autopay_channel_partner_withdraw.*,
        ')
        ->join('tb_autopay_channel_partner_officer','tb_autopay_channel_partner_officer.id = tb_autopay_channel_partner_withdraw.channel_partner_officer_id','left')
        ->orderBy('tb_autopay_channel_partner_withdraw.id desc');

        if ($channel_partner_id !== null) {
            $builder->where('tb_autopay_channel_partner_withdraw.channel_partner_id', $channel_partner_id);
        }
    
        if ($channel_partner_officer_id !== null) {
            $builder->where('tb_autopay_channel_partner_withdraw.channel_partner_officer_id', $channel_partner_officer_id);
        }

        if ($status !== null) {
            $builder->where('tb_autopay_channel_partner_withdraw.status', $status);
        }

        $data = $builder->get()->getResultArray();
        if(!empty($data)){
            foreach($data as &$val){
                $val['withdraw_id'] = $val['id'];
            }
            unset($val);
        }

        return $this->responseResult(200, "Danh sách rút hoa hồng!", $data);
    }

    // [GET]
    public function detail($id) {
        $request = service('request');

        if (strtoupper($request->getMethod()) !== 'GET') {
            return $this->responseResult(405, "Phương thức URL không hợp lệ!");
        }

        if(empty($id)){
            return $this->responseResult(404,"ID không được phép rỗng");
        }
        
        $channel_partner_officer_id = $this->request->getGet('channel_partner_officer_id');
        $channel_partner_id = $this->request->getGet('channel_partner_id');

        $condition = array();

        // Thêm điều kiện nếu có channel_partner_officer_id
        if (isset($channel_partner_officer_id)) {
            $condition['tb_autopay_channel_partner_withdraw.channel_partner_officer_id'] = $channel_partner_officer_id;
        }

        // Thêm điều kiện nếu có channel_partner_id
        if (isset($channel_partner_id)) {
            $condition['tb_autopay_channel_partner_withdraw.channel_partner_id'] = $channel_partner_id;
        }

        // Thêm điều kiện company_id
        $condition['tb_autopay_channel_partner_withdraw.id'] = $id;

        
        $ChannelPartnerWithdrawModel = model(ChannelPartnerWithdrawModel::class);

        $builder = $ChannelPartnerWithdrawModel->select('
        tb_autopay_channel_partner_withdraw.*,

        tb_autopay_channel_partner.id channel_partner_id,
        tb_autopay_channel_partner.name channel_partner_name,
        tb_autopay_channel_partner.logo_path channel_partner_logo,
        tb_autopay_channel_partner.active channel_partner_active,
        tb_autopay_channel_partner.default_commission_rate channel_partner_default_rate,


        tb_autopay_channel_partner_officer.id channel_partner_officer_id,
        tb_autopay_channel_partner_officer.name channel_partner_officer_name,
        tb_autopay_channel_partner_officer.email channel_partner_officer_email,
        tb_autopay_channel_partner_officer.active channel_partner_officer_active,
        tb_autopay_channel_partner_officer.role channel_partner_officer_role,
        tb_autopay_channel_partner_officer.commission_rate channel_partner_officer_commission_rate,
        tb_autopay_channel_partner_officer.balance_amount channel_partner_officer_balance_amount,


        ')
        ->join('tb_autopay_channel_partner','tb_autopay_channel_partner.id = tb_autopay_channel_partner_withdraw.channel_partner_id')
        ->join('tb_autopay_channel_partner_officer','tb_autopay_channel_partner_officer.id = tb_autopay_channel_partner_withdraw.channel_partner_officer_id')
        ->orderBy('tb_autopay_channel_partner_withdraw.id  desc')
        ->where($condition);
        $data = $builder->get()->getRowArray()??[];

        $data_response = array(
            'withdraw' => [],
            'channel_partner' => [],
            'channel_partner_officer' => [],
            
            
        );
        if(!empty($data)){
            $data_response = [
                'withdraw' => [
                    'id' => $data['id'] ?? null,
                    'status' => $data['status'] ?? null,
                    'withdraw_amount' => $data['withdraw_amount'] ?? null,
                    'notes' => $data['notes'] ?? null,
                    'rejection_reasons' => $data['rejection_reasons'] ?? null,
                    'created_at' => $data['created_at'] ?? null,
                    'updated_at' => $data['updated_at'] ?? null,
                ],
                'channel_partner' => [
                    'id' => $data['channel_partner_id'] ?? null,
                    'name' => $data['channel_partner_name'] ?? null,
                    'logo_path' => $data['channel_partner_logo'] ?? null,
                    'active' => $data['channel_partner_active'] ?? null,
                    'default_commission_rate' => $data['channel_partner_default_rate'] ?? null,
                ],
                'channel_partner_officer' => [
                    'id' => $data['channel_partner_officer_id'] ?? null,
                    'name' => $data['channel_partner_officer_name'] ?? null,
                    'email' => $data['channel_partner_officer_email'] ?? null,
                    'active' => $data['channel_partner_officer_active'] ?? null,
                    'role' => $data['channel_partner_officer_role'] ?? null,
                    'commission_rate' => $data['channel_partner_officer_commission_rate'] ?? null,
                    'balance_amount' => $data['channel_partner_officer_balance_amount'] ?? null,
                ],
                
            ];
            
        }
            
        return $this->responseResult(200, "Chi tiết rút hoa hồng!", $data_response);        
    }
}
