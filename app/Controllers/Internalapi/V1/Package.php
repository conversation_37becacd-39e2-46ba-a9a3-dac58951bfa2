<?php

namespace App\Controllers\Internalapi\V1;

use App\Controllers\Internalapi\V1\BaseController;
use App\Models\AddonModel;
use App\Models\ChannelPartnerCompanyModel;
use App\Models\ChannelPartnerModel;
use App\Models\CompanyModel;
use App\Models\CompanySubscriptionModel;
use App\Models\CompanyUserModel;
use App\Models\InvoiceItemModel;
use App\Models\InvoiceModel;
use App\Models\OrderModel;
use App\Models\ProductModel;

use PHPUnit\Util\Xml\Validator;

use function PHPUnit\Framework\returnCallback;

class Package extends BaseController
{
   
    // [POST] 
    public function create_product(){

        $request = service('request');
        // data clean by filter
        $input = $request->getBody();
        return $this->responseResult(500,"Đã bị khóa!");

        $ProductModel = model(ProductModel::class);
        $data = json_decode($input, true);
        $validationRules = [
            'channel_partner_id'    => 'required|max_length[11]|is_not_unique[tb_autopay_channel_partner.id]',
            
        ];
    

        $validation = service('validation');
    
        if (!$validation->setRules($validationRules)->run($data)) {
            return $this->responseResult(422, $validation->getErrors());
        }
        $ProductModel->insert($data);
        $id_insert = $ProductModel->insertID();
        return $this->responseResult(200,"Thêm gói dịch vụ mẫu thành công!",['product_id' => $id_insert]);
    }
    
    // [GET] 
    public function all(){
        $request = service('request');
    
        if (strtoupper($request->getMethod()) !== 'GET') {
            return $this->responseResult(405, "Phương thức URL không hợp lệ!");
        }

        $ProductModel = model(ProductModel::class);

        $channel_partner_id = $request->getGet('channel_partner_id');
        $active = $request->getGet('active');
        $product_id = $request->getGet('product_id');
    
        $builder = $ProductModel->orderBy('id desc');

        if (isset($channel_partner_id)) {
            $builder->where(['channel_partner_id'=>$channel_partner_id]);
        } 
        if (isset($product_id)) {
            $builder->where(['id'=>$product_id]);
        } 

        if (isset($active)) {
        $builder->where(['active'=>$active]);
        } 


        $data = $builder->get()->getResultArray();

        if(!empty($data)){
            foreach($data as &$val){
                $val['product_id'] = $val['id'];
            }
            unset($val);
        }


        return $this->responseResult(200, "Danh sách sản phẩm (gói dịch vụ)!", $data);
    }

    // [POST] 
    public function create(){
        $input = $this->request->getBody();
        $method = $this->request->getMethod();

        if(strtoupper($method)!="POST"){
            return $this->responseResult(405,"Phương thức URL không hợp lệ!");
        }

        $validationRules = [
            'company_id' => 'required|max_length[11]|is_not_unique[tb_autopay_company.id]',
            'channel_partner_id' => 'required|max_length[11]|is_not_unique[tb_autopay_channel_partner.id]',
            'product_id' => 'required|max_length[11]',
            'billing_cycle' => 'required|in_list[annually,monthly,free]',
        ];

        $validation = service('validation');
        $validation->setRules($validationRules);
        
        $data = json_decode($input,true);
        if( !$validation->run($data)){
            return $this->responseResult(422,$validation->getErrors());
        }
        
        $ProductModel = model(ProductModel::class);
        $AddonModels = model(AddonModel::class);
        $CompanySubscriptionModel = model(CompanySubscriptionModel::class);
        $ChannelPartnerCompanyModel = model(ChannelPartnerCompanyModel::class);

        $data_product = $ProductModel->where(['id'=>$data['product_id'],'channel_partner_id'=>$data['channel_partner_id']])->get()->getRowArray();
        
        if(empty($data_product)){
            $data_product = $AddonModels->where(['id'=>$data['product_id'],'channel_partner_id'=>$data['channel_partner_id']])->get()->getRowArray();            
        }

        if(empty($data_product)){
            return $this->responseResult("404","Không tìm thấy thông tin dịch vụ trên hệ thống!");
        }
        //
        if(empty($data_product['channel_partner_id'])){
            return $this->responseResult("403","Gói dịch vụ này không thuộc hệ thống Channel Partner Company!");
        }
        $info_link_channel_company = $ChannelPartnerCompanyModel->where(['company_id'=>$data['company_id']])->get()->getRowArray();

        if(empty($info_link_channel_company)){
            return $this->responseResult("404","Gói dịch vụ không thuộc về hệ thống Channel Partner Company!");
        }

        
        $subscription_details = $CompanySubscriptionModel
        ->select('tb_autopay_company_subscription.*')
        ->where(['company_id' => $data['company_id'],'plan_id'=>$data['product_id'],'billing_cycle'=>$data['billing_cycle']])->get()->getRowArray();
        
        if(!empty($subscription_details)){
            return $this->responseResult(422,"Bạn đang sử dụng gói dịch vụ này, vui lòng chọn một gói dịch vụ khác!");
        }

        $array_price_product = array(
            'monthly' => $data_product['price_monthly'] ?? 0,
            'annually' => ($data_product['price_annually'] ?? 0) * 12,
        );        
        $array_type_product_invoice = array(
            'monthly' => "Theo tháng",
            'annually' => "Theo năm",
        );
        $array_type_product_invoice = $array_type_product_invoice[$data['billing_cycle']]??'Không xác định!';
        $price_product = $array_price_product[$data['billing_cycle']]??'0';
        if(empty($price_product)){
            return $this->responseResult("404","Giá tiền gói dịch vụ không thuộc về hệ thống Channel Partner Company!");
        }

        $datalog = array(
            'channel_partner_id' => $data['channel_partner_id'] ?? "",
            'data_type' => "json",
            'ip' => $this->request->getIPAddress() ?? "",
            'user_agent' => trim(xss_clean($this->request->getUserAgent()->getAgentString())),
            'description' => "",
            'status' =>  "",
            'data_id' => null,
        );

        if($data['billing_cycle']!="free"){

            //  Tạo hóa đơn chưa thanh toán
            $data_invoice = array(
                'status' => 'Unpaid',
                    'type' => 'NewOrder',
                    'company_id' => $data['company_id'],
                    'date' => date("Y-m-d"),
                    'duedate' => date('Y-m-d', strtotime('+7 days')),
                    'paybefore' => date('Y-m-d', strtotime('+7 days')),
                    'subtotal' => $price_product,
                    'total' => $price_product,
            );
            $id_invoice = $this->createInvoice($data_invoice);
            if(empty($id_invoice)){
                return $this->responseResult(500,"Tạo hóa đơn thất bại, vui lòng liên hệ kỹ thuật");
            }
        }

        $start_date = date("Y-m-d");
        
        if($data['billing_cycle'] == "annually")  {
            $end_date = date("Y-m-d", strtotime('+1 year'));
        }
        else{
            $end_date = date("Y-m-d", strtotime('+1 month'));

        }
        $end_date = date("Y-m-d", strtotime('-1 day', strtotime($end_date)));


        if($data['billing_cycle'] !="free"){

            // Tạo chi tiết hóa đơn    
            $data_invoice_item = array(
                'invoice_id' => $id_invoice,
                'type' => 'Product',
                'description' => 'SePay - ' . ($data_product['name'] ?? "OCB") . ' (' . $start_date . ' ' . date('H:i:s') . ' - ' . $end_date . ' 23:59:59)',

                'item_id' => $data_product['id'],
                'amount' => $price_product,
                'start_date' => $start_date,
                'end_date' => $end_date
            );
            
            $id_invoice_item = $this->createInvoiceItem($data_invoice_item);
            if(empty($id_invoice_item)){

                 // savelog
                $datalog['description'] = "Tạo hóa đơn!";
                $datalog['status'] = "Failed";
                $datalog['data_id'] = null;
                Cpalog::savelog($datalog);

                return $this->responseResult(500,"Tạo chi tiết hóa đơn thất bại, vui lòng liên hệ kỹ thuật");
            }
        }

        //  Tạo đơn hàng
        $data_order = array(
                'company_id' => $data['company_id'],
                'invoice_id' => $id_invoice ?? 0,
                'status' => 'Pending',
                'total' => $price_product,
                'order_ip' => $this->request->getIPAddress(),                   
                
        );
        
        $id_order = $this->createOrder($data_order);
        if(empty($id_order)){
            // savelog
            $datalog['description'] = "Tạo đơn hàng!";
            $datalog['status'] = "Failed";
            $datalog['data_id'] = "";
            Cpalog::savelog($datalog);

            return $this->responseResult(500,"Tạo đơn hàng thất bại, vui lòng liên hệ kỹ thuật");
        }

        // Tạo subscription company
        $data_subcription = array(
            'company_id' => $data['company_id'],
            'order_id' => $id_order,
            'plan_id' => $data['product_id'],
            'begin_date' => date('Y-m-d',time()),
            'end_date' => $end_date,
            'first_payment' => $price_product,
            'recurring_payment' => $price_product,
            'billing_cycle' => $data['billing_cycle'],

        );
        $CompanySubScriptionModel = model(CompanySubscriptionModel::class);
        $exit_pagekage = $CompanySubScriptionModel->where(['company_id'=>$data_subcription['company_id']])->first();
        if(!empty($exit_pagekage)){

             // savelog
             $datalog['description'] = "Tạo subcription!";
             $datalog['status'] = "Failed";
             $datalog['data_id'] = null;
             Cpalog::savelog($datalog);

            return $this->responseResult(500,"Cty đã đăng kí gói trước đó, vui lòng liên hệ kỹ thuật");
        }
        $id_subsciption = $this->createSubscription($data_subcription);
        if(empty($id_subsciption)){

            // savelog
            $datalog['description'] = "Tạo subcription!";
            $datalog['status'] = "Failed";
            $datalog['data_id'] = null;
            Cpalog::savelog($datalog);

            return $this->responseResult(500,"Tạo đăng kí công ty thất bại, vui lòng liên hệ kỹ thuật");
        }
        
            // savelog
             $datalog['description'] = "Tạo gói dịch vụ!";
             $datalog['status'] = "Success";
             $datalog['data_id'] = $id_order;
             Cpalog::savelog($datalog);

        return $this->responseResult(200,"Chọn gói thành công dịch vụ thành công!",['order_id'=>$id_order]);
    }


    // [Action]
    protected function createOrder($data){
        $OrderModel = model(OrderModel::class);
        try{
            $OrderModel->insert($data);
            $id_insert_order = $OrderModel->insertID();
            log_message("info","Tạo đơn thành công, ID hóa đơn $id_insert_order");
            return $id_insert_order;
        }catch(\Exception $e){
            log_message("error","Lỗi server tạo đơn hàng :".$e->getMessage());
            return false;
        }
    }

    // [Action]
    protected function createSubscription($data){
        $CompanySubScriptionModel = model(CompanySubscriptionModel::class);
        try{
            $CompanySubScriptionModel->insert($data);
            $id_insert_company_subscription = $CompanySubScriptionModel->insertID();
            return $id_insert_company_subscription;

        }catch(\Exception $e){
            log_message("error","Lỗi server tạo đăng kí công ty:".$e->getMessage());
            return false;
        }
    }

    // [Action]
    protected function createInvoice($data){
        $InvoiceModel = model(InvoiceModel::class);
        try{
            $InvoiceModel->insert($data);
            $id_insert_invoice = $InvoiceModel->insertID();
            log_message("info","Tạo chi tiết hóa đơn thành công, ID hóa đơn $id_insert_invoice");
            return $id_insert_invoice;

        }catch(\Exception $e){
            log_message("error","Lỗi server tạo hóa đơn :".$e->getMessage());
            return false;
        }
    }

    // [Action]
    protected function createInvoiceItem($data){
        $InvoiceItemModel = model(InvoiceItemModel::class);
        try{
            $InvoiceItemModel->insert($data);
            $id_insert_invoice_item = $InvoiceItemModel->insertID();
            log_message("info","Tạo chi tiết hóa đơn thành công, ID hóa đơn $id_insert_invoice_item");
            return $id_insert_invoice_item;

        }catch(\Exception $e){
            log_message("error","Lỗi server tạo chi tiết hóa đơn:".$e->getMessage());
            return false;
        }
    }

    

    
}
