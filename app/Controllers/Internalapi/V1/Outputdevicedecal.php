<?php

namespace App\Controllers\Internalapi\V1;

use App\Controllers\Internalapi\V1\BaseController;
use App\Libraries\MbbClient;
use App\Libraries\MbbMmsClient;
use App\Libraries\OcbClient;
use App\Models\BankSubAccountModel;
use App\Models\ConfigurationModel;
use App\Models\MbbMmsMerchantModel;
use App\Models\MbbMmsTerminalModel;
use App\Models\OutputDeviceDecalModel;
use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\Response;
use Exception;

class Outputdevicedecal extends BaseController
{
    use ResponseTrait;
    
    /**
     * @return Response
     */
    public function create_va(): Response
    {
        if ($this->request->getMethod(true) !== 'POST') {
            return $this->fail('Method not allowed');
        }

        $data = (array) $this->request->getVar();

        if (! $this->validateData($data, [
            'decal_id' => 'required|is_natural_no_zero',
            'bank_id' => 'required|is_natural_no_zero',
            'account_number' => 'required|string',
        ])) {
            return $this->failValidationErrors($this->validator->getErrors());
        }

        $decal = model(OutputDeviceDecalModel::class)
            ->where('account_number', $data['account_number'])
            ->where('bank_id', $data['bank_id'])
            ->where('virtual_account_number', null)
            ->find($data['decal_id']);

        if (! $decal) {
            return $this->failNotFound();
        }

        switch ($decal->bank_id) {
            case 8:
                $virtualAccount = $this->createMbbVa($decal);
                break;
            case 9:
                $virtualAccount = $this->createBIDV();
                break;
            case 18:
                $virtualAccount = $this->createOCB();
                break;
            default:
                if (in_array($decal->bank_id, [2, 3, 6, 12, 19])) {
                    $virtualAccount = $this->createInhouseVa();
                } else {
                    return $this->respond(['status' => false, 'message' => 'Ngân hàng này chưa hỗ trợ dán decal']);
                }
        }

        model(OutputDeviceDecalModel::class)->update($decal->id, [
            'virtual_account_number' => $virtualAccount['account_number'],
            'account_holder_name' => $virtualAccount['account_holder_name'] ?? null,
            'qrcode' => $virtualAccount['qrcode'] ?? null,
        ]);

        return $this->respondCreated([
            'status' => true,
            'id' => $decal->id,
        ]);
    }
    
    /**
     * @return Response
     */
    public function delete_va($id = null): Response
    {
        if ($this->request->getMethod(true) !== 'POST') {
            return $this->fail('Method not allowed');
        }

        if (empty($id)) {
            return $this->failNotFound();
        }        
        
        $decal = model(OutputDeviceDecalModel::class)
            ->where('virtual_account_number IS NOT NULL')
            ->find($id);
            
        if (! $decal) {
            return $this->failNotFound();
        }
        
        try {
            if ($decal->bank_id == 8) {
                $this->deleteMbbVa($decal);
            }
        } catch (Exception $e) {
            return $this->respond(['status' => false, 'message' => $e->getMessage()]);
        }

        model(OutputDeviceDecalModel::class)->update($decal->id, [
            'virtual_account_number' => null,
            'qrcode' => null,
        ]);

        return $this->respondDeleted([
            'status' => true,
            'id' => $decal->id,
        ]);
    }

    protected function deleteMbbVa($decal): void
    {
        $mbbMmsTerminalModel = model(MbbMmsTerminalModel::class);
        $terminal = $mbbMmsTerminalModel
            ->where('output_device_id', $decal->output_device_id)
            ->where('bank_account_id', 0)
            ->first();

        if (! $terminal) {
            return;
        }

        $client = new MbbMmsClient();
        $client->setMerchantById($terminal->merchant_id);
        $response = $client->deleteQrCode($decal->qrcode);

        $data = json_decode($response->getBody(), true);

        if ($data['errorCode'] !== '000' || $data['data']['isSuccess'] !== true) {
            throw new Exception($data['errorDesc'][0] ?? 'Không thể xóa tài khoản ảo');
        }
        
        $mbbMmsTerminalModel->where('id', $terminal->id)->delete();
    }
    /**
     * @return array<string,mixed>
     */
    protected function createMbbVa($decal): array
    {
        $mbbMmsTerminal = model(MbbMmsTerminalModel::class)
            ->where('bank_account_id', null)
            ->where('output_device_id', $decal->output_device_id)
            ->first();

        $client = new MbbMmsClient();
        $terminalId = null;
        $accountHolderName = $decal->account_holder_name ?: null;

        try {
            $response = (new MbbClient())->getBankAccountInfo(trim($this->request->getVar('account_number')), 'ACCOUNT', 'INHOUSE');
            $data = json_decode($response->getBody(), true);

            $errorCode = $data['errorCode'] ?? $data['soaErrorCode'];
            $errorDesc = $data['errorDesc'] ?? $data['soaErrorDesc'];

            if ($errorCode == '000') {
                $accountHolderName = $data['data']['accountName'];
            }

            if (in_array($errorCode, ['3014', '3001', '200'])) {
                throw new Exception($errorDesc[0] ?? 'Số tài khoản không tồn tại trên hệ thống MBBank.');
            }

            throw new Exception($errorCode . ' - ' . implode(', ', $errorDesc));
        } catch (Exception $e) {
            log_message('error', $e->getMessage());
        }

        if (! $accountHolderName) {
            throw new Exception('Không tìm thấy tên chủ tài khoản.');
        }

        if ($mbbMmsTerminal) {
            $client->setMerchantById($mbbMmsTerminal->merchant_id);
            $terminalId = $mbbMmsTerminal->terminal_id;
        } else {
            $merchant = model(MbbMmsMerchantModel::class)->first();

            if (! $merchant) {
                throw new Exception('Không tìm thấy merchant.');
            }

            $client->setMerchantById($merchant->id);

            $response = $client->syncTerminalsWithNonOtp([
                [
                    'terminalName' => $accountHolderName,
                    'provinceCode' => '1',
                    'districtCode' => '6',
                    'wardsCode' => '178',
                    'mccCode' => '1024',
                    'fee' => 0,
                    'bankCode' => '311',
                    'bankCodeBranch' => '********',
                    'bankAccountNumber' => $client->encryptData($decal->account_number),
                    'bankAccountName' => $accountHolderName,
                    'bankCurrencyCode' => 1,
                ],
            ]);

            $data = json_decode($response->getBody(), true);

            if ($data['errorCode'] != '000') {
                throw new Exception($data['errorDesc'][0] ?? 'Không thể tạo tài khoản ảo');
            }

            $result = $data['data']['result'][0];

            if ($result['syncStatus'] === 'create_ok') {
                model(MbbMmsTerminalModel::class)->insert([
                    'merchant_id' => $merchant->id,
                    'terminal_id' => $result['terminalId'],
                    'output_device_id' => $decal->output_device_id,
                ]);

                $terminalId = $result['terminalId'];
            }
        }

        if (! $terminalId) {
            throw new Exception('Không thể tạo tài khoản ảo');
        }

        $response = $client->createQrCode(
            $terminalId,
            1,
            2,
            11,
            null,
            null,
            0,
            0,
            0,
            null,
            $accountHolderName,
            null,
            null
        );

        $data = json_decode($response->getBody(), true);

        if ($data['errorCode'] != '000') {
            throw new Exception($data['errorDesc'][0] ?? 'Không thể tạo tài khoản ảo');
        }

        return [
            'account_number' => $data['data']['accountQR'],
            'account_holder_name' => $accountHolderName,
            'qrcode' => $data['data']['qrcode'],
        ];
    }

    protected function createBIDV(): array
    {
        $configuration = model(ConfigurationModel::class);
        $bidvSpeakerBillingVaOrderConfig = $configuration->where('company_id', 0)->where('setting', 'BidvSpeakerBillingVaOrderConfig')->first();

        if (! $bidvSpeakerBillingVaOrderConfig) {
            $vaOrder = 1;
        } else {
            $vaOrder = $bidvSpeakerBillingVaOrderConfig->value + 1;
        }

        $vav = speaker_billing_feature()->withVaContext()->vaContext()->generateVAV($vaOrder);

        if ($bidvSpeakerBillingVaOrderConfig && $vaOrder > $bidvSpeakerBillingVaOrderConfig->value) {
            $configuration->where('id', $bidvSpeakerBillingVaOrderConfig->id)->set(['value' => (int) $vav])->update();
        }
        
        $bidvConfig = config(\Config\Bidv::class);
        $billingConfig = config(\Config\Billing::class);

        return [
            'account_number' => $bidvConfig->personalVaPrefix . $billingConfig->speakerBillingPrefixVav . $vav,
        ];
    }

    protected function createOCB(): array
    {
        $configuration = model(ConfigurationModel::class);
        $ocbSpeakerBillingVaOrderConfig = $configuration->where('company_id', 0)->where('setting', 'OcbSpeakerBillingVaOrderConfig')->first();

        if (! $ocbSpeakerBillingVaOrderConfig) {
            $vaOrder = 1;
        } else {
            $vaOrder = $ocbSpeakerBillingVaOrderConfig->value + 1;
        }

        $vav = speaker_billing_feature()->withVaContext()->vaContext()->generateVAV($vaOrder);

        if ($ocbSpeakerBillingVaOrderConfig && $vaOrder > $ocbSpeakerBillingVaOrderConfig->value) {
            $configuration->where('id', $ocbSpeakerBillingVaOrderConfig->id)->set(['value' => (int) $vav])->update();
        }
        
        $client = new OcbClient;
        $billingConfig = config(\Config\Billing::class);

        return [
            'account_number' => $client->getPrefixVA() . $billingConfig->speakerBillingPrefixVav . $vav,
        ];
    }
    
    /**
     * @return array<string,string>
     */
    protected function createInhouseVa(): array
    {
        $failedCount = 0;
        
        do {
            $va = 'L' . strtoupper(bin2hex(random_bytes(2)));
            
            if (model(BankSubAccountModel::class)->where('sub_account', $va)->countAllResults() === 0 
            && model(OutputDeviceDecalModel::class)->where('virtual_account_number', $va)->countAllResults() === 0) {
                break;
            }
            
            $failedCount++;
        } while ($failedCount < 3);
        
        if (!$va) {
            throw new Exception('Failed to generate TKP virtual account');
        }
        
        return [
            'account_number' => $va,
        ];
    }
}
