<?php

namespace App\Controllers\Internalapi\V1;

use App\Controllers\Internalapi\V1\BaseController;
use App\Models\AddonModel;
use App\Models\BankAccountModel;
use App\Models\ChannelPartnerCompany;
use App\Models\ChannelPartnerCompanyModel;
use App\Models\ChannelPartnerModel;
use App\Models\ChannelPartnerOfficer;
use App\Models\ChannelPartnerOfficerModel;
use App\Models\CompanyModel;
use App\Models\CompanySubscriptionModel;
use App\Models\TransactionsModel;
use App\Models\CompanyUserModel;
use App\Models\LgTokenModel;
use App\Models\ProductModel;
use App\Models\SmsParserModel;
use Config\InternalApi;

class Company extends BaseController
{
    
    // [POST]
    public function create(){
        $request = service('request');
        $validation = service('validation');

        $data_input = $request->getBody();
        if(empty($data_input)){
            return $this->responseResult("422","Không tìm thấy dữ liệu gửi lên!");
        }

        $data = json_decode($data_input,true);
        $method = $request->getMethod();
        if (strtoupper($method) != 'POST') {
            return $this->responseResult(405, "Phương thức URL không hợp lệ!");
        }

        $validationRules = [
            'full_name' => 'required|string|max_length[255]',        
            'short_name' => 'required|string|max_length[255]',
            'channel_partner_id' => 'is_not_unique[tb_autopay_channel_partner.id]',            
            'channel_partner_officer_id' => 'is_not_unique[tb_autopay_channel_partner_officer.id]',
        ];

        

        $validation->setRules($validationRules);
        if (!$validation->run($data)) {
            return $this->responseResult(422, $validation->getErrors());
        }
        $CompanyModel = model(CompanyModel::class);

        $datalog = array(
            'channel_partner_id' => $data['channel_partner_id'] ?? "",
            'data_type' => "json",
            'ip' => $this->request->getIPAddress() ?? "",
            'user_agent' => trim(xss_clean($this->request->getUserAgent()->getAgentString())),
            'description' => "",
            'status' =>  "",
            'data_id' => "",
        );

        try{
            $data_add = array(
                'full_name' => trim(xss_clean($data['full_name'])),
                'short_name' => trim(xss_clean($data['short_name'])),
                'channel_partner_id' => $data['channel_partner_id'],
                'channel_partner_officer_id' => $data['channel_partner_officer_id'],
            );
            $insert_data = $CompanyModel->insert($data_add);
            
            if($insert_data){
                $id_insert = $CompanyModel->insertID();

                // link chanenl partner company
                $data_channel_partner_company = array(
                    'company_id' => $id_insert,
                    'channel_partner_officer_id' => $data['channel_partner_officer_id'],
                    'channel_partner_id' => $data['channel_partner_id'],
                );
                $link_result = $this->link_channel_partner_company($data_channel_partner_company);

                if(!empty($link_result)){

                    // savelog
                    $datalog['description'] = "Tạo company!";
                    $datalog['status'] = "Success";
                    $datalog['data_id'] = $id_insert;
                    Cpalog::savelog($datalog);
                    
                    return $this->responseResult(200,"Thêm công ty thành công!",['company_id' => $id_insert]);
                
                }

                 // savelog
                 $datalog['description'] = "Tạo company!";
                 $datalog['status'] = "Failed";
                 $datalog['data_id'] = null;
                 Cpalog::savelog($datalog);

                return $this->responseResult(500,"Lỗi hệ thống, không thể liên kết với công ty, Hãy liên hệ kỹ thuật!");
                
            }else{

                 // savelog
                 $datalog['description'] = "Tạo company!";
                 $datalog['status'] = "Failed";
                 $datalog['data_id'] = null;
                 Cpalog::savelog($datalog);

                return $this->responseResult(500,"Lỗi hệ thống, không thể tạo công ty, Hãy liên hệ kỹ thuật!");
            }

        }catch(\Exception $e){
            // savelog
            $datalog['description'] = "Tạo company!";
            $datalog['status'] = "Failed";
            $datalog['data_id'] = null;
            Cpalog::savelog($datalog);

            return $this->responseResult(500,"Lỗi hệ thống, mã lỗi : ".$e->getMessage());
        }

    }

    // [Action]
    protected function link_channel_partner_company($data){


        $ChannelPartnerCompany = model(ChannelPartnerCompanyModel::class);

        try{

            $insert_data = $ChannelPartnerCompany->insert($data);
            
            if(!empty($insert_data)){

                return true;
            }else{
                log_message("warning","Lỗi model insert thông tin liên kết company ");
                return false;
            }

        }catch(\Exception $e){
                log_message("warning","Lỗi 500 insert thông tin liên kết company ". $e->getMessage());
                return false;
        }

    }

    // [GET]
    public function all(){
        $request = service('request');
        
        if (strtoupper($request->getMethod()) !== 'GET') {
            return $this->responseResult(405, "Phương thức URL không hợp lệ!");
        }

        $ChannelPartnerCompanyModel = model(ChannelPartnerCompanyModel::class);

        $channel_partner_id = $request->getGet('channel_partner_id');
        $channel_partner_officer_id = $request->getGet('channel_partner_officer_id');
        $status = $request->getGet('status');
    
        $builder = $ChannelPartnerCompanyModel->select('
        tb_autopay_company.id company_id,
        tb_autopay_company.user_owner_id,
        tb_autopay_company.full_name,
        tb_autopay_company.short_name,
        tb_autopay_company.status company_status,
        tb_autopay_company.merchant_id,
        tb_autopay_company.created_at,
        tb_autopay_company.channel_partner_id,

        tb_autopay_channel_partner_officer.id channel_partner_officer_id,

        tb_autopay_order.id order_id,
        tb_autopay_order.invoice_id,
        
        tb_autopay_company_subscription.plan_id product_id,
        tb_autopay_company_subscription.status subscription_status,
        tb_autopay_company_subscription.billing_cycle,
        COUNT(DISTINCT tb_autopay_company_user.id) as user_count,
        ')
        ->join('tb_autopay_company', 'tb_autopay_company.id = tb_autopay_channel_partner_company.company_id', 'left')
        ->join('tb_autopay_company_user', 'tb_autopay_company_user.company_id = tb_autopay_company.id', 'left')
        ->join('tb_autopay_order', 'tb_autopay_order.company_id = tb_autopay_channel_partner_company.company_id', 'left')
        ->join('tb_autopay_company_subscription', 'tb_autopay_company_subscription.company_id = tb_autopay_channel_partner_company.company_id', 'left')
        ->join('tb_autopay_channel_partner_officer', 'tb_autopay_channel_partner_officer.id = tb_autopay_channel_partner_company.channel_partner_officer_id', 'left')
        ->groupBy('tb_autopay_company.id')
        ->orderBy('tb_autopay_company.id DESC'); 
        if (isset($channel_partner_id)) {
            $builder->where(['tb_autopay_company.channel_partner_id'=>$channel_partner_id]);
        } 

        
        if (isset($channel_partner_officer_id)) {
            $builder->where(['tb_autopay_channel_partner_officer.id'=>$channel_partner_officer_id]);
        } 

        if (isset($status)) {
            $builder->where(['tb_autopay_company.status'=>$status]);
        } 

       
        $data = $builder->findAll();

        return $this->responseResult(200, "Danh sách công ty!", $data);
    }

    // [GET]    
    public function detail($id=null){

        if(empty($id)){
            return $this->responseResult(404,"ID không được phép rỗng");
        }
        $channel_partner_officer_id = $this->request->getGet('channel_partner_officer_id');
        $channel_partner_id = $this->request->getGet('channel_partner_id');

        $condition = array();

        // Thêm điều kiện nếu có channel_partner_officer_id
        if (isset($channel_partner_officer_id)) {
            $condition['tb_autopay_channel_partner_company.channel_partner_officer_id'] = $channel_partner_officer_id;
        }

        // Thêm điều kiện nếu có channel_partner_id
        if (isset($channel_partner_id)) {
            $condition['tb_autopay_channel_partner_company.channel_partner_id'] = $channel_partner_id;
        }

        // Thêm điều kiện company_id
        $condition['tb_autopay_channel_partner_company.company_id'] = $id;

        
        $ChannelPartnerCompanyModel = model(ChannelPartnerCompanyModel::class);
        $data_query = $ChannelPartnerCompanyModel->select('
            tb_autopay_channel_partner.name channel_partner_name,
            tb_autopay_channel_partner.code channel_partner_code,
            tb_autopay_channel_partner.logo_path channel_partner_logo,

            tb_autopay_company.full_name company_full_name,
            tb_autopay_company.short_name company_short_name,
            tb_autopay_company.status company_status,
            tb_autopay_company.merchant_id,
            tb_autopay_company.created_at,
            tb_autopay_company.channel_partner_id,

            tb_autopay_user.id owner_user_id,
            tb_autopay_user.email owner_user_email,
            tb_autopay_user.phonenumber owner_user_phonenumber,
            tb_autopay_user.firstname owner_user_first_name,
            tb_autopay_user.lastname owner_user_last_name,
            tb_autopay_user.created_at owner_user_created_at,
            tb_autopay_company_user.role owner_user_role,


            tb_autopay_order.total order_total,
            tb_autopay_order.status order_status,
            tb_autopay_order.id order_id,

            tb_autopay_invoice.status invoice_status,
            tb_autopay_invoice.id invoice_id,
            tb_autopay_invoice.type invoice_type,
            tb_autopay_invoice.total invoice_total,
            tb_autopay_invoice.subtotal invoice_subtotal,

            tb_autopay_invoice_item.type invoice_item_type,
            tb_autopay_invoice_item.item_id invoice_item_id,
            tb_autopay_invoice_item.description invoice_item_description,

           
        ')
        ->join('tb_autopay_channel_partner', 'tb_autopay_channel_partner.id = tb_autopay_channel_partner_company.channel_partner_id') // Thêm điều kiện nối bảng
        ->join('tb_autopay_channel_partner_officer', 'tb_autopay_channel_partner_officer.id = tb_autopay_channel_partner_company.channel_partner_officer_id') // Thêm điều kiện nối bảng
        ->join('tb_autopay_company', 'tb_autopay_company.id = tb_autopay_channel_partner_company.company_id') // Thêm điều kiện nối bảng
        ->join('tb_autopay_company_user', 'tb_autopay_company_user.company_id = tb_autopay_channel_partner_company.company_id') // Thêm điều kiện nối bảng
        ->join('tb_autopay_user', 'tb_autopay_user.id = tb_autopay_company_user.user_id') 
        ->join('tb_autopay_order', 'tb_autopay_order.company_id = tb_autopay_channel_partner_company.company_id') 
        ->join('tb_autopay_invoice', 'tb_autopay_invoice.id = tb_autopay_order.invoice_id') 
        ->join('tb_autopay_invoice_item', 'tb_autopay_invoice_item.invoice_id = tb_autopay_invoice.id') 
        ->where($condition)
        ->groupBy('tb_autopay_order.id')
        ->get()
        ->getResultArray() ?? [];
        if(empty($data_query)){
            $data_response = array(
                'channel_partner' => array(),
                'channel_partner_company'=>array(),
                'owner_user'=>array(),
                'invoice'=>array(),
                'order'=>array(),
            );
            return $this->responseResult(200,"Chi tiết công ty!",$data_response);
        }

        

        $data_response = array(
            'channel_partner' => array(
                'channel_partner_name' => $data_query[0]['channel_partner_name']??"",
                'channel_partner_code' => $data_query[0]['channel_partner_code']??"",
                'channel_partner_logo' => $data_query[0]['channel_partner_logo']??"",
            ),
            'channel_partner_company' => array(
                'company_full_name' => $data_query[0]['company_full_name']??"",
                'company_short_name' => $data_query[0]['company_short_name']??"",
                'company_status' => $data_query[0]['company_status']??"",
                'company_created_at' => $data_query[0]['created_at']??"",
            ),
            'owner_user' => array(
                'user_id' => $data_query[0]['owner_user_id'],
                'email' => $data_query[0]['owner_user_email'],
                'first_name' => $data_query[0]['owner_user_first_name'],
                'last_name' => $data_query[0]['owner_user_last_name'],
                'phonenumber' => $data_query[0]['owner_user_phonenumber'],
                'created_at' => $data_query[0]['owner_user_created_at'],
                'role' => $data_query[0]['owner_user_role'],
            ),
            'invoice' => array_map(function($item){
                return array(
                    'invoice_id' => $item['invoice_id'],
                    'invoice_status' => $item['invoice_status'],
                    'invoice_total' => $item['invoice_total'],
                    'invoice_subtotal' => $item['invoice_subtotal'],
                    'invoice_item_type' => $item['invoice_item_type'],
                );
            },$data_query),
            'order' => array_map(function($item){
                return array(
                    'order_id' => $item['order_id'],
                    'order_status' => $item['order_status'],
                    'order_total' => $item['order_total'],
                    
                );
            },$data_query)
            
        );
        
        return $this->responseResult(200,"Chi tiết công ty!",$data_response);
    }

    // [GET]
    public function subscription_all() {

        $request = service('request');
        $method = $request->getMethod();
        if (strtoupper($method) != 'GET') {
            return $this->responseResult(405, "Phương thức URL không hợp lệ!");
        }
        $channel_partner_officer_id = $this->request->getGet('channel_partner_officer_id');
        $channel_partner_id = $this->request->getGet('channel_partner_id');
        $company_id = $request->getGet('company_id');

        $ChannelPartnerCompanyModel = model(ChannelPartnerCompanyModel::class);

        if(isset($channel_partner_officer_id)){
            $condition_check['channel_partner_officer_id'] = $channel_partner_officer_id;
        }
        if(isset($channel_partner_id)){
            $condition_check['channel_partner_id'] = $channel_partner_id;
        }
        if(isset($company_id)){
            $condition_check['company_id'] = $company_id;
        }
     
        if(!empty($condition_check)){
            $result_check = $ChannelPartnerCompanyModel->where($condition_check)->get()->getRowArray();
            if(empty($result_check)){
                return $this->responseResult(404,"Thông tin không thuộc hệ thống!");
            }
        }

        $subscription_id = $request->getGet('subscription_id');
        $order_id = $request->getGet('order_id');
        $plan_id = $request->getGet('plan_id');
        $status = $request->getGet('status');

        $CompanySubScriptionModel = model(CompanySubScriptionModel::class);
    
        $builder = $CompanySubScriptionModel->select('
        tb_autopay_order.id  order_id,
        tb_autopay_order.total  order_total,
        tb_autopay_order.status  order_status,

        tb_autopay_company_subscription.id subscription_id,
        tb_autopay_company_subscription.company_id company_id,
        tb_autopay_company_subscription.plan_id product_id,
        tb_autopay_company_subscription.begin_date subscription_begin_date,
        tb_autopay_company_subscription.end_date subscription_end_date,
        tb_autopay_company_subscription.first_payment subscription_first_payment,
        tb_autopay_company_subscription.recurring_payment subscription_recurring_payment,
        tb_autopay_company_subscription.billing_cycle subscription_billing_cycle,
        tb_autopay_company_subscription.status subscription_status,

        tb_autopay_invoice.id invoice_id,
        tb_autopay_invoice.status  invoice_status,
        tb_autopay_invoice.type  invoice_type,

        tb_autopay_invoice_item.type  product_type,

        ')
        ->join('tb_autopay_company', 'tb_autopay_company.id = tb_autopay_company_subscription.company_id', 'left')
        ->join('tb_autopay_order', 'tb_autopay_order.id = tb_autopay_company_subscription.order_id', 'left')
        ->join('tb_autopay_invoice', 'tb_autopay_invoice.id = tb_autopay_order.invoice_id', 'left')
        ->join('tb_autopay_invoice_item', 'tb_autopay_invoice_item.invoice_id = tb_autopay_invoice.id', 'left')
        ->orderBy('tb_autopay_order.id', 'desc')
        ->groupBy('tb_autopay_company_subscription.id');

        if (isset($subscription_id)) {
            $builder->where(['tb_autopay_company_subscription.id'=>$subscription_id]);
        } 

        if (isset($order_id)) {
            $builder->where(['tb_autopay_company_subscription.order_id'=>$order_id]);
        } 

        if (isset($plan_id)) {
            $builder->where(['tb_autopay_company_subscription.plan_id'=>$plan_id]);
        } 

        if (isset($company_id)) {
            $builder->where(['tb_autopay_company_subscription.company_id'=>$company_id]);
        } 


        if (isset($status)) {
            $builder->where(['tb_autopay_company_subscription.status'=>$status]);
        } 

        $data = $builder->get()->getResultArray();
        if (!empty($data)) {
            $ProductModel = model(ProductModel::class);
            $AddonModel = model(AddonModel::class);
        
            foreach ($data as $key => $val) {
                if ($val['product_type'] == "Addon") {
                    $data_product = $AddonModel->where(['id' => $val['product_id']])->get()->getRowArray();
                    $data[$key]['product_name'] = $data_product['name'] ?? "";
                    $data[$key]['product_description'] = $data_product['description'] ?? "";
                    $data[$key]['product_billing_type'] = $data_product['billing_type'] ?? "";
                } else {
                    $data_product = $ProductModel->where(['id' => $val['product_id']])->get()->getRowArray();
                    $data[$key]['product_name'] = $data_product['name'] ?? "";
                    $data[$key]['product_description'] = $data_product['description'] ?? "";
                    $data[$key]['product_billing_type'] = $data_product['billing_type'] ?? "";
                }
            }
            
        }
        // Trả về kết quả
        return $this->responseResult(200, "Thông tin đăng kí dịch vụ công ty",$data);
    }

    // [POST]
    public function login_as_client() {

        // check time sesssion login
        if(empty($this->check_login_user())){
            return $this->responseResult(403,"Đã hết quyền truy cập!");
        }
       

        $request = service('request');
        $validation = service('validation');

        $input =$request->getBody();
        if(empty($input)){
            return $this->responseResult("422","Không tìm thấy dữ liệu gửi lên!");
        }
        $data = json_decode($input,true);
        $method = $request->getMethod();
        if (strtoupper($method) != 'POST') {
            return $this->responseResult(405, "Phương thức URL không hợp lệ!");
        }
        $CompanyUserModel = model(CompanyUserModel::class);
        $ChannelPartnerCompanyModel = model(ChannelPartnerCompanyModel::class);


        $validationRules = [
            'user_id' => 'max_length[11]|is_not_unique[tb_autopay_user.id]',        
            'channel_partner_officer_id' => 'max_length[11]|is_not_unique[tb_autopay_channel_partner_officer.id]',
            'channel_partner_id' => 'max_length[11]|is_not_unique[tb_autopay_channel_partner.id]',
        ];
        $company_id = $CompanyUserModel->select("company_id")->where(['user_id'=>$data['user_id']])->get()->getRowArray()['company_id']??"";
        
        $check_user_officer = $ChannelPartnerCompanyModel->where(['company_id'=>$company_id,'channel_partner_officer_id'=>trim($data['channel_partner_officer_id']),'channel_partner_id'=>trim($data['channel_partner_id'])])->get()->getRowArray();
        if(empty($check_user_officer)){
            return $this->responseResult(500,"Không thể truy cập, tài khoản không thuộc hệ thống!, Hãy liên hệ kỹ thuật");
        };

        $validation->setRules($validationRules);
        if (!$validation->run($data)) {
            return $this->responseResult(422, $validation->getErrors());
        }

        // check time  login
        $company_id = $CompanyUserModel->where(['user_id'=>$data['user_id']])->get()->getRowArray()['company_id']??"";
        if(empty($this->check_login_time($company_id))){
            return $this->responseResult(403,"Đã hết quyền truy cập!");
        }

        $lgTokenModel = model(LgTokenModel::class);

        $token = random_string('alnum',36);

        $data_add = array(
            'user_id' => trim($data['user_id']),
            'channel_partner_officer_id' => trim($data['channel_partner_officer_id']),
            'token' => $token,
        );
        $result = $lgTokenModel->insert($data_add);
        if($result){
            return $this->responseResult(200,"Đăng nhập thành công!",['re_url'=>base_url(). "/login/token/" . $token]);
        }
        return $this->responseResult(500,"Đăng nhập thất bại!, Hãy liên hệ kỹ thuật");

    }

    protected function check_login_time($company_id) {
        if (empty($company_id)) {
            return false;
        }
        return $this->is_login_valid($company_id);
    }
    
    static public function check_login_user() {
        $session = session()->get('user_logged_in') ?? [];
        $company_id = $session['company_id'] ?? "";
        
        if (!empty($session['channel_partner_officer_id']) && !empty($company_id)) {
            return (new self())->is_login_valid($company_id);
        }
        return true;
    }
    
    protected function is_login_valid($company_id) {
        helper("general");
        $SmsParsedModel = slavable_model(TransactionsModel::class,"/Internalapi/V1/Company");
        $list_trans = $SmsParsedModel->select('tb_autopay_sms_parsed.*, tb_autopay_bank_account.company_id')
            ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id = tb_autopay_sms_parsed.bank_account_id', 'left')
            ->where(['tb_autopay_bank_account.company_id' => $company_id])
            ->orderBy('tb_autopay_sms_parsed.transaction_date', 'asc')
            ->limit(1)
            ->get()
            ->getResultArray() ?? [];
        
        if (!empty($list_trans[0])) {
            $transaction_timestamp = strtotime($list_trans[0]['transaction_date']);
            $config = config(InternalApi::class);
            $time_config = $config->loginClientTime ?? 0;
    
            return time() <= $transaction_timestamp + $time_config;
        }
    
        return true;
    }
    
}
