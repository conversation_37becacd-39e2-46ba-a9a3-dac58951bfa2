<?php

namespace App\Controllers\Internalapi\V1;

use App\Models\ZnsQueueModel;
use CodeIgniter\API\ResponseTrait;

class Zns extends BaseController
{
    use ResponseTrait;

    public function callback()
    {
        if(!$this->request->getMethod() == 'POST'){
            return $this->response->setJSON(["status"=>FALSE , "message" => "Phương thức không hợp lệ"]);
        }

        $request = service('request');
        log_message('error', json_encode($request->getPost()));
        $smsid = xss_clean($request->getPost('smsid'));
        $status = xss_clean($request->getPost('status'));

        if (!$smsid || !isset($status)) {
            return $this->response->setJSON(["status"=>FALSE , "message" => "SMSID không được để trống"]);
        }

        if (!is_string($status) || !is_string($smsid)) {
            return $this->response->setJSON(["status"=>FALSE , "message" => "Dữ liệu không hợp lệ"]);
        }

        if ($status != '1' && $status != '0') {
            return $this->response->setJSON(["status"=>FALSE , "message" => "Trạng thái không hợp lệ"]);
        }

        $znsQueueModel = model(ZnsQueueModel::class);

        $result = $znsQueueModel
            ->where('status', 'Sent')
            ->where('smsid', $smsid)
            ->first();

        if (empty($result)) {
            return $this->response->setJSON(["status"=>FALSE , "message" => "Không tìm thấy kết quả"]);
        }

        $newStatus = $status == '1' ? 'Success' : 'Failed';
        $znsQueueModel->update($result->id, ['status' => $newStatus]);

        log_message('error', "ZNS Callback [$smsid] updated to $newStatus");

        return $this->response->setJSON(["status"=>TRUE , "message" => "Cập nhật thành công"]);
    }
}