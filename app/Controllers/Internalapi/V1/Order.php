<?php

namespace App\Controllers\Internalapi\V1;

use App\Controllers\Internalapi\V1\BaseController;
use App\Models\ChannelPartnerCompanyModel;
use App\Models\OrderModel;

class Order extends BaseController
{
    
    // [GET]
    public function all() {
        $request = service('request');
        $method = $request->getMethod();
        if (strtoupper($method) != 'GET') {
            return $this->responseResult(405, "Phương thức URL không hợp lệ!");
        }
        $channel_partner_officer_id = $this->request->getGet('channel_partner_officer_id');
        $channel_partner_id = $this->request->getGet('channel_partner_id');
        $company_id = $request->getGet('company_id');

        $ChannelPartnerCompanyModel = model(ChannelPartnerCompanyModel::class);

        if(isset($channel_partner_officer_id)){
            $condition_check['channel_partner_officer_id'] = $channel_partner_officer_id;
        }
        if(isset($channel_partner_id)){
            $condition_check['channel_partner_id'] = $channel_partner_id;
        }
        if(isset($company_id)){
            $condition_check['company_id'] = $company_id;
        }
     
        if(!empty($condition_check)){
            $result_check = $ChannelPartnerCompanyModel->where($condition_check)->get()->getRowArray();
            if(empty($result_check)){
                return $this->responseResult(404,"Đơn hàng không thuộc hệ thống!");
            }
        }

        $OrderModel = model(OrderModel::class);
    
        $order_id = $request->getGet('order_id');
        $invoice_id = $request->getGet('invoice_id');
        $status = $request->getGet('status');

        $builder = $OrderModel
        ->orderBy('id desc')

        ;

        if (isset($order_id)) {
            $builder->where(['tb_autopay_order.id'=>$order_id]);
        } 

        if (isset($invoice_id)) {
            $builder->where(['tb_autopay_order.invoice_id'=>$invoice_id]);
        } 

        if (isset($company_id)) {
            $builder->where(['tb_autopay_order.company_id'=>$company_id]);
        } 

        if (isset($status)) {
            $builder->where(['tb_autopay_order.status'=>$status]);
        } 

        $data = $builder->get()->getResultArray();

        if(!empty($data)){
            foreach($data as &$val){
                $val['order_id'] = $val['id'];
            }
            unset($val);
        }
    
        return $this->responseResult(200, "Danh sách đơn hàng công ty!",$data);
    }
    
}
