<?php

namespace App\Controllers\Internalapi\V1;

use App\Controllers\BaseController;
use App\Models\TransactionsModel;
use App\Models\OutputDeviceReplayMessageQueueModel;

class Transactions extends BaseController
{

    public function get_transactions_by_bank_id()
    {
        if(!$this->request->getMethod() == 'POST'){
            return $this->response->setJSON(["status"=>FALSE , "message" => "Phương thức không hợp lệ"]);
        }

        $request = service('request');
        $data = $request->getPost();

        $bankId = xss_clean($data['bank_id']);
        $account_number = xss_clean($data['account_number']);
        $sub_account_number = xss_clean($data['sub_account_number']);
        $serial_number = xss_clean($data['serial_number']);
        $amount = xss_clean($data['amount']);
        $date_range = xss_clean($data['date_range']);

        if(!$bankId){
            return $this->response->setStatusCode(400)
            ->setJSON([
                'status' => 'error',
                'message' => 'Bank ID is required',
            ]);
        }

        if(empty($account_number) || !is_array($account_number)){
            return $this->response->setStatusCode(400)
            ->setJSON([
                'status' => 'error',
                'message' => 'Account number is required and must be an array',
            ]);
        }

        $transactionsModel = slavable_model(TransactionsModel::class, 'Transactions');
        $transactions = $transactionsModel->getTransactionsByBankId($bankId, $account_number, $sub_account_number, $serial_number, $amount, $date_range);

        if (count($transactions)) {
            $OutputDeviceReplayMessageQueueModel = slavable_model(OutputDeviceReplayMessageQueueModel::class, 'Transactions');
            $outputDevicePushCounters = $OutputDeviceReplayMessageQueueModel->select(['transaction_id', 'COUNT(transaction_id) as count'])
                ->whereIn('transaction_id', array_column($transactions, 'id'))
                ->groupBy('transaction_id')
                ->get()
                ->getResult();
        }

        $data = [];

        foreach ($transactions as $transaction) {
            $row = [];
            $row[] = esc($transaction->id);
            $row[] = "<span title='".$transaction->brand_name." - ".$transaction->account_holder_name."'>
                <img src='" . esc(base_url('assets/images/banklogo/' . $transaction->icon_path)) . "' class='avatar img-fluid rounded-circle m-auto' style='height:20px; width:20px'>
                " . esc($transaction->account_number) . "
            </span>";        
            $row[] = esc($transaction->sub_account);
            if($transaction->amount_in > 0){
                $row[] = "<span class='text-success'>+" . number_format($transaction->amount_in) . "</span>";
            }
            $row[] = date('H:i:s d-m-Y', strtotime($transaction->transaction_date));
            $row[] = '<code class="fs-5">' . esc($transaction->serial_number) . '</code>';
            $row[] = esc($transaction->transaction_content);
            $data[] = $row;
        }

        return $this->response->setJSON([
            'draw' => $this->request->getPost('draw'),
            'recordsTotal' => $transactionsModel->getTransactionsByBankIdCountAll($bankId),
            'recordsFiltered' => $transactionsModel->getTransactionsByBankIdCountFiltered($bankId, $account_number, $sub_account_number, $serial_number, $amount, $date_range),
            'data' => $data,
        ]);
    }
}