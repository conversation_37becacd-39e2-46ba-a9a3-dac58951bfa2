<?php

namespace App\Controllers\Internalapi\V1;

use App\Controllers\Internalapi\V1\BaseController;
use App\Models\ChannelPartnerCompanyModel;
use App\Models\ChannelPartnerModel;
use App\Models\ChannelPartnerOfficerModel;

class Cpaofficer extends BaseController
{
    
    // [POST] 
    public function create_partner(){

        $request = service('request');
        // data clean by filter
        $input = $request->getBody();
        return $this->responseResult(500,"Đã bị khóa!");
        
        $data = json_decode($input, true);
        $validationRules = [
            'code' => 'required|is_unique[tb_autopay_channel_partner.code]',
        ];
    
        $validation = service('validation');
    
        if (!$validation->setRules($validationRules)->run($data)) {
            return $this->responseResult(422, $validation->getErrors());
        }
        $ChannelPartnerModel = model(ChannelPartnerModel::class);


        $ChannelPartnerModel->insert($data);
        $id_insert = $ChannelPartnerModel->insertID();
        return $this->responseResult(200,"Thêm CPA thành công!",['channel_partner_id' => $id_insert]);
    }

    // [POST] 
    public function create_partner_officer(){

        $input = $this->request->getBody();
        $data = json_decode($input, true);
        $validationRules = [
            'channel_partner_id'    => 'required|max_length[11]|is_not_unique[tb_autopay_channel_partner.id]',
            'email'         => [
                'rules'  => 'required|valid_email|is_unique[tb_autopay_channel_partner_officer.email]',
                'errors' => [
                    'is_unique' => 'Email đã tồn tại trên hệ thống!',
                ],
            ],
            'name'     => 'required|string|max_length[50]',
            'password'     => 'required|string|min_length[8]',
            'role'          => 'required|in_list[Admin,SuperAdmin,Sale]',
        ];
    

        $validation = service('validation');
    
        if (!$validation->setRules($validationRules)->run($data)) {
            return $this->responseResult(422, $validation->getErrors());
        }
        
        $pwd_hash = password_hash(trim($data['password']),PASSWORD_DEFAULT);
        $data['password'] = $pwd_hash;

        $data['active'] = 0;

        $datalog = array(
            'channel_partner_id' => $data['channel_partner_id'] ?? "",
            'data_type' => "json",
            'ip' => $this->request->getIPAddress() ?? "",
            'user_agent' => trim(xss_clean($this->request->getUserAgent()->getAgentString())),
            'description' => "",
            'status' =>  "",
            'data_id' => null,
        );
        try{

            $data_add = array(
                'channel_partner_id'=> $data['channel_partner_id'],
                'email'=> trim($data['email']),
                'name'=> trim(xss_clean($data['name'])),
                'password'=> $data['password'],
                'role'=> $data['role'],
                'active' => $data['active']
            );

            $ChannelPartnerOfficeModel = model(ChannelPartnerOfficerModel::class);
            $insert_data = $ChannelPartnerOfficeModel->insert($data_add);

            if($insert_data){
                $id_insert = $ChannelPartnerOfficeModel->insertID();     
                
                // savelog
                $datalog['description'] = "Tạo Officer!";
                $datalog['status'] = "Success";
                $datalog['data_id'] = $id_insert;
                Cpalog::savelog($datalog);

                return $this->responseResult(200,"Tạo Officer thành công!",['channel_partner_officer_id' => $id_insert]);
            
            }else{

                // savelog
                $datalog['description'] = "Tạo Officer!";
                $datalog['status'] = "Failed";
                $datalog['data_id'] = null;
                Cpalog::savelog($datalog);

                return $this->responseResult(500,"Lỗi hệ thống, không thể thêm Officer, Hãy liên hệ kỹ thuật!");
            }



        }catch(\Exception $e){

            // savelog
            $datalog['description'] = "Tạo Officer!";
            $datalog['status'] = "Failed";
            $datalog['data_id'] = null;
            Cpalog::savelog($datalog);

            log_message("error","Lỗi hệ thông insert company officer: ".$e->getMessage());
            return $this->responseResult(500,"Lỗi hệ thống, không thể thêm Officer, Hãy liên hệ kỹ thuật!");

        }



    }

    // [POST]
    public function login_officer(){
        $input = $this->request->getBody();
        $data = json_decode($input, true);
        
        $validationRules = [
            'password'    => 'required|min_length[8]',
            'email'         => 'required|valid_email|is_not_unique[tb_autopay_channel_partner_officer.email]',      
            'channel_partner_id'         => 'required|is_not_unique[tb_autopay_channel_partner_officer.channel_partner_id]',          
        ];
        $validation = service('validation');
    
        if (!$validation->setRules($validationRules)->run($data)) {
            return $this->responseResult(422, $validation->getErrors());
        }

        $ChannelPartnerOfficeModel = model(ChannelPartnerOfficerModel::class);
        $data_info = $ChannelPartnerOfficeModel->where(['email'=>$data['email']])->get()->getRowArray();

       
        if ($data_info && password_verify(trim($data['password']), $data_info['password'])) {
            $data_res = array(
                'channel_partner_id'=>$data_info['channel_partner_id'],
                'channel_partner_officer_id' => $data_info['id'],
                'name' => $data_info['name'],
                'email'=>$data_info['email'],
                'role'=>$data_info['role'],
                'active'=>$data_info['active']
            );
            return $this->responseResult(200, "Xác thực thành công!",$data_res);
        } else {
            return $this->responseResult(422, "Xác thực thất bại!");
        }
        
    }

     // [POST]
    public function active_account(){
        $input = $this->request->getBody();
        $data = json_decode($input, true);
        
        $validationRules = [
            'channel_partner_id'    => 'required|max_length[11]',
            'email'         => 'required|valid_email|is_not_unique[tb_autopay_channel_partner_officer.email]',               
        ];

        $validation = service('validation');
    
        if (!$validation->setRules($validationRules)->run($data)) {
            return $this->responseResult(422, $validation->getErrors());
        }
        $ChannelPartnerOfficeModel = model(ChannelPartnerOfficerModel::class);
        $data_officer = $ChannelPartnerOfficeModel->where(['channel_partner_id'=>$data['channel_partner_id'],'email'=>$data['email']])->get()->getRowArray();
        if(empty($data_officer)){
            return $this->responseResult(404,"Không tìm thấy email trên hệ thông Channel Partner!");
        }
        try{

            $update = $ChannelPartnerOfficeModel->update($data_officer['id'],['active'=>1]);

            if($update){
                $data_res = array(
                    'channel_partner_id'=>$data_officer['channel_partner_id'],
                    'channel_partner_officer_id' => $data_officer['id'],
                    'name' => $data_officer['name'],
                    'email'=>$data_officer['email'],
                    'role'=>$data_officer['role'],
                    'active'=>$data_officer['active'],
                );
                return $this->responseResult(200, "Kích hoạt CPA  Office thành công!",$data_res);
                     
            
            }else{
                return $this->responseResult(500,"Lỗi hệ thống, không thể kích hoạt Officer, Hãy liên hệ kỹ thuật!");
            }



        }catch(\Exception $e){
            log_message("error","Lỗi hệ thông insert company officer: ".$e->getMessage());

        }

    }

    // [POST]
    public function change_password(){
        $input = $this->request->getBody();
        $data = json_decode($input, true);
        
        $validationRules = [
            'password'    => 'required|min_length[8]',
            'password_new'    => 'required|min_length[8]',
            'channel_partner_id'         => 'required|is_not_unique[tb_autopay_channel_partner_officer.channel_partner_id]',               
            'channel_partner_officer_id'         => 'required|is_not_unique[tb_autopay_channel_partner_officer.id]',               
        ];
        $validation = service('validation');
    
        if (!$validation->setRules($validationRules)->run($data)) {
            return $this->responseResult(422, $validation->getErrors());
        }

        $ChannelPartnerOfficeModel = model(ChannelPartnerOfficerModel::class);

        $data_info = $ChannelPartnerOfficeModel->where(['id'=>$data['channel_partner_officer_id'],'channel_partner_id' =>$data['channel_partner_id']])->get()->getRowArray(); 
        if ($data_info && password_verify(trim($data['password']), $data_info['password'])) {
            
            $pwd_hash = password_hash(trim($data['password_new']),PASSWORD_DEFAULT);
            
            $data_update = array(
                'id' => $data_info['id'],
                'channel_partner_id'=>$data_info['channel_partner_id'],
                'password'=>$pwd_hash
            );
            $update_date = $this->update_password($data_update);

            if(!empty($update_date)){
                return $this->responseResult(200, "Đổi mật khẩu thành công!");
            }else{

                return $this->responseResult(200, "Lỗi đổi mật khẩu, hãy liên hệ kỹ thuật");
            }
        } else {
            return $this->responseResult(422, "Xác thực thất bại, sai mật khẩu!");
        }
    }

    // [Action]
    protected function update_password($data){
        $ChannelPartnerOfficeModel = model(ChannelPartnerOfficerModel::class);

        try{

            $ChannelPartnerOfficeModel->update($data['id'],$data);
            return true;

        }catch(\Exception $e){
            return false;
        }
    }

    // [POST]
    public function reset_password(){
        $input = $this->request->getBody();
        $data = json_decode($input, true);
        
        $validationRules = [
            'password_new'    => 'required|min_length[8]',
            'channel_partner_officer_id'         => 'required|is_not_unique[tb_autopay_channel_partner_officer.id]',               
            'channel_partner_id'         => 'required|is_not_unique[tb_autopay_channel_partner_officer.channel_partner_id]',               
        ];
        $validation = service('validation');
    
        if (!$validation->setRules($validationRules)->run($data)) {
            return $this->responseResult(422, $validation->getErrors());
        }

        $ChannelPartnerOfficeModel = model(ChannelPartnerOfficerModel::class);

        $data_info = $ChannelPartnerOfficeModel->where(['id'=>$data['channel_partner_officer_id'],'channel_partner_id' =>$data['channel_partner_id']])->get()->getRowArray(); 
        if ($data_info) {
            
            $pwd_hash = password_hash(trim($data['password_new']),PASSWORD_DEFAULT);
            $data_update = array(
                'id' => $data_info['id'],
                'channel_partner_id'=>$data_info['channel_partner_id'],
                'password'=>$pwd_hash
            );
            $update_date = $this->update_password($data_update);

            if(!empty($update_date)){
                return $this->responseResult(200, "Đổi mật khẩu thành công!");
            }else{

                return $this->responseResult(200, "Lỗi đổi mật khẩu, hãy liên hệ kỹ thuật");
            }
        } else {
            return $this->responseResult(422, "Xác thực thất bại, sai mật khẩu!");
        }
    }

    // [GET]
    public function all(){
        $request = service('request');
    
        if (strtoupper($request->getMethod()) !== 'GET') {
            return $this->responseResult(405, "Phương thức URL không hợp lệ!");
        }

        $ChannelPartnerOfficerModel = model(ChannelPartnerOfficerModel::class);

        $channel_partner_id = $request->getGet('channel_partner_id');
        $channel_partner_officer_id = $request->getGet('channel_partner_officer_id');
        $active = $request->getGet('active');
        $email = $request->getGet('email');
    
        $builder = $ChannelPartnerOfficerModel->select('id channel_partner_officer_id,name,email,channel_partner_id,active,role,commission_rate,balance_amount,created_at')->orderBy('id desc');

        if (isset($channel_partner_id)) {
            $builder->where(['channel_partner_id'=>$channel_partner_id]);
        } 

        if (isset($channel_partner_officer_id)) {
            $builder->where(['id'=>$channel_partner_officer_id]);
        } 

        if (isset($active)) {
            $builder->where(['active'=>$active]);
        } 

        if (isset($email)) {
            $builder->where(['email'=>$email]);
        } 


        $data = $builder->get()->getResultArray();
       
        return $this->responseResult(200, "Danh sách officer!", $data);
    }
}
