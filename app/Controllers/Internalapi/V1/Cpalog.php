<?php

namespace App\Controllers\Internalapi\V1;

use App\Controllers\Internalapi\V1\BaseController;
use App\Models\ChannelPartnerLogModel;

class Cpalog extends BaseController
{


    // Action
    static function savelog($data){

        $datalog = array(
            'channel_partner_id' =>$data['channel_partner_id']??"",
            'data_id' =>$data['data_id']??"",
            'data_type' =>$data['data_type']??"",
            'description' =>$data['description']??"",
            'ip' =>$data['ip']??"",
            'user_agent' =>trim(xss_clean($data['user_agent']??"")),
            'status' =>$data['status']??"",
        );

        if(!empty($datalog['status'])){

            if($datalog['status']!="Success"|| $datalog['status']!="Failed"){
                
                $datalog['status'] == "Failed";
            }
        }

        try{
            $ChannelPartnerLogModel = model(ChannelPartnerLogModel::class);
            $ChannelPartnerLogModel->insert($datalog);
            return true;

        }catch(\Exception $e){
            log_message("error","Lỗi hệ thống lưu log".$e->getMessage());
            return true;
        }
    }

}
