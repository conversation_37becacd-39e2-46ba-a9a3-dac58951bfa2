<?php

namespace App\Controllers\Internalapi\V1;

use CodeIgniter\Controller;
use CodeIgniter\HTTP\CLIRequest;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use Psr\Log\LoggerInterface;
/**
 * Class BaseController
 *
 * BaseController provides a convenient place for loading components
 * and performing functions that are needed by all your controllers.
 * Extend this class in any new controllers:
 *     class Home extends BaseController
 *
 * For security be sure to declare any new methods as protected or private.
 */
abstract class BaseController extends Controller
{
    /**
     * Instance of the main Request object.
     *
     * @var CLIRequest|IncomingRequest
     */
    protected $request;

    /**
     * An array of helpers to be loaded automatically upon
     * class instantiation. These helpers will be available
     * to all other controllers that extend BaseController.
     *
     * @var array
     */
    protected $helpers = ['general','form','url','text'];

    protected $user_details;

    protected $company_details;

    protected $user_session;

    /**
     * Constructor.
     */
    public function initController(RequestInterface $request, ResponseInterface $response, LoggerInterface $logger)
    {
        // Do Not Edit This Line
        parent::initController($request, $response, $logger);
 
        // Preload any models, libraries, etc, here.

        // E.g.: $this->session = \Config\Services::session();

    }

    protected function responseResult($code, $message, $data = 0): ResponseInterface
    {
        $response = service('response');
        $data_res = [
            'code' => $code,
            'message' => $message,
        ];

        if($data != 0 || $data == []){
            $data_res['data'] = $data;
        }

        $response->setStatusCode($code);
        $response->setJSON($data_res);

        return $response;
    }

    protected function calcMonthToDay($month, $year) {
        if ($month < 1 || $month > 12) {
            log_message('warning','Tháng không hợp lệ. Phải nằm trong khoảng từ 1 đến 12.');
        }
    
        return cal_days_in_month(CAL_GREGORIAN, $month, $year);
    }

    protected function calcYearToDay($year) {
        $isLeapYear = ($year % 4 == 0 && $year % 100 != 0) || $year % 400 == 0;
    
        $daysInYear = $isLeapYear ? 366 : 365;
    
        return $daysInYear;
    }


}
