<?php

namespace App\Controllers\Internalapi\V1;

use App\Models\PhysicalOrderTrackingModel;
use App\Models\PhysicalProductModel;
use App\Services\SpeakerOrderService;
use App\Services\AddressService;
use App\Services\DecalService;
use App\Services\BankBranchesService;
use CodeIgniter\API\ResponseTrait;
use Config\Billing;
use Config\SpeakerOrder as ConfigSpeakerOrder;
use Exception;
use App\Models\PhysicalOrderHistoryModel;

class Speakerorder extends BaseController
{
    use ResponseTrait;

    protected $speakerOrderService;

    protected $addressService;

    protected $decalService;

    protected $bankBranchesService;

    public function __construct()
    {
        $this->speakerOrderService = new SpeakerOrderService();
        $this->addressService = new AddressService();
        $this->decalService = new DecalService();
        $this->bankBranchesService = new BankBranchesService();
    }

    public function products()
    {
        if ($this->request->getMethod(true) !== 'GET') {
            show_404();
        }

        $products = model(PhysicalProductModel::class)->getProducts();

        return $this->respond([
            'status' => true,
            'data' => $products,
        ]);
    }

    public function create()
    {
        if ($this->request->getMethod(true) !== 'POST') {
            show_404();
        }

        $config = config(ConfigSpeakerOrder::class);

        $rules = [
            'customer_type' => 'required|in_list[retail,wholesale]',
            'customer_name' => 'required|min_length[3]',
            'customer_phone' => 'required|regex_match[/^[0-9]{10}$/]',
            'customer_email' => 'permit_empty|valid_email',
            'province' => 'required',
            'district' => 'required',
            'ward' => 'required',
            'address' => 'required|min_length[3]',
            'products' => 'required',
            'products.*' => 'required|is_not_unique[tb_autopay_physical_products.id]',
            'payment_method' => 'required|in_list[' . implode(',', array_keys($config->paymentMethods)) . ']',
            'product_qty' => 'required',
            'product_qty.*' => 'required',
            'notes' => 'permit_empty|min_length[3]',
            'tr_gcid' => 'permit_empty',
            'utm_source' => 'permit_empty',
            'utm_medium' => 'permit_empty',
            'utm_campaign' => 'permit_empty',
            'utm_term' => 'permit_empty',
            'utm_content' => 'permit_empty',
            'user_agent' => 'permit_empty',
            'ip_address' => 'permit_empty',
            'referrer' => 'permit_empty',
            'referral_code' => 'permit_empty|is_not_unique[tb_autopay_partner.referral_code]',
            'bank_referral_code' => 'permit_empty',
            'bank_referral_branch' => 'permit_empty',
        ];

        if (! $this->validate($rules, [
            'products.*' => [
                'is_not_unique' => 'Sản phẩm không hợp lệ',
            ],
            'referral_code' => [
                'is_not_unique' => 'Mã giới thiệu không hợp lệ',
            ],
        ])) {
            return $this->failValidationErrors($this->validator->getErrors());
        }

        $data = [
            'customer_type' => $this->request->getVar('customer_type'),
            'customer_name' => xss_clean($this->request->getVar('customer_name')),
            'customer_phone' => $this->request->getVar('customer_phone'),
            'customer_email' => $this->request->getVar('customer_email'),
            'province' => $this->request->getVar('province'),
            'district' => $this->request->getVar('district'),
            'ward' => $this->request->getVar('ward'),
            'address' => $this->request->getVar('address'),
            'products' => (array) $this->request->getVar('products'),
            'product_qty' => (array) $this->request->getVar('product_qty'),
            'payment_method' => $this->request->getVar('payment_method'),
            'notes' => xss_clean($this->request->getVar('notes')),
            'tr_gcid' => $this->request->getVar('tr_gcid'),
            'referral_code' => $this->request->getVar('referral_code'),
            'bank_referral_code' => xss_clean($this->request->getVar('bank_referral_code')),
            'bank_referral_branch' => xss_clean($this->request->getVar('bank_referral_branch')),
        ];

        try {
            $result = $this->speakerOrderService->createOrder($data, $this->request->getVar('ip_address'));

            $trackingData = [
                'utm_source' => $this->request->getVar('utm_source'),
                'utm_medium' => $this->request->getVar('utm_medium'),
                'utm_campaign' => $this->request->getVar('utm_campaign'),
                'utm_term' => $this->request->getVar('utm_term'),
                'utm_content' => $this->request->getVar('utm_content'),
                'user_agent' => $this->request->getVar('user_agent'),
                'ip_address' => $this->request->getVar('ip_address'),
                'referrer' => $this->request->getVar('referrer'),
            ];

            if (empty($trackingData['utm_source']) && ! empty($trackingData['referrer'])) {
                $url = parse_url($trackingData['referrer']);
                $host = $url['host'];
                $path = $url['path'] ?? '';
    
                $source = $host;
    
                if ($host === 'sepay.vn' && preg_match('/\/blog\//', $path)) {
                    $source = 'sepay.vn/blog';
                }
    
                $trackingData['utm_source'] = $source;
            }

            model(PhysicalOrderTrackingModel::class)->addTracking($result['order_id'], $trackingData);

            return $this->respond([
                'status' => true,
                'message' => $result['message'],
                'order_code' => $result['order_code'],
                'order_id' => $result['order_id'],
            ]);
        } catch (Exception $e) {
            return $this->fail($e->getMessage(), 400);
        } catch (Exception $e) {
            log_message('error', '[API] Lỗi tạo đơn hàng: ' . $e->getMessage());

            return $this->failServerError('Đã xảy ra lỗi khi xử lý đơn hàng.');
        }
    }

    public function details($orderCode = null)
    {
        if ($this->request->getMethod(true) !== 'GET') {
            show_404();
        }

        if (empty($orderCode)) {
            show_404();
        }

        $order = $this->speakerOrderService->getOrderByCode($orderCode);

        if (! $order) {
            show_404();
        }

        return $this->respond([
            'status' => true,
            'data' => array_merge($order, [
                'bank_account' => config(Billing::class)->ownerBankAccount,
                'histories' => model(PhysicalOrderHistoryModel::class)->getHistories($order['order']->id),
            ]),
        ]);
    }

    public function provinces()
    {
        return $this->respond([
            'status' => true,
            'data' => $this->addressService->getProvinces()
        ]);
    }

    public function districts($province = null)
    {
        if (empty($province)) {
            return $this->fail('Vui lòng chọn tỉnh/thành phố');
        }

        return $this->respond([
            'status' => true,
            'data' => $this->addressService->getDistrictsByProvince($province)
        ]);
    }

    public function wards($district = null)
    {
        if (empty($district)) {
            return $this->fail('Vui lòng chọn quận/huyện');
        }

        return $this->respond([
            'status' => true,
            'data' => $this->addressService->getWardsByDistrict($district)
        ]);
    }

    public function calculateShippingFee()
    {
        if ($this->request->getMethod(true) !== 'POST') {
            show_404();
        }

        $rules = [
            'province' => 'required|is_not_unique[tb_autopay_provinces_v2.code]',
            'district' => 'required|is_not_unique[tb_autopay_districts_v2.code]',
            'ward' => 'required|is_not_unique[tb_autopay_wards_v2.code]',
            'products' => 'required',
            'product_qty' => 'required',
        ];

        if (! $this->validate($rules)) {
            return $this->failValidationErrors($this->validator->getErrors());
        }

        $data = (array) $this->request->getVar();

        try {
            $addressInfo = $this->addressService->validateAndGetAddressInfo(
                $data['province'],
                $data['district'],
                $data['ward']
            );

            $productInfo = $this->speakerOrderService->processProducts(
                (array) $data['products'],
                (array) $data['product_qty']
            );

            return $this->response->setJSON([
                'status' => true,
                'data' => [
                    'shipping_fee' => $this->speakerOrderService->calculateShippingFee($addressInfo, $productInfo),
                ],
            ]);
        } catch (Exception $e) {
            return $this->response->setJSON([
                'status' => false,
                'message' => $e->getMessage(),
            ]);
        }
    }

    public function getDecals($orderCode = null)
    {
        if ($this->request->getMethod(true) !== 'GET' || empty($orderCode)) {
            return $this->fail('Mã đơn hàng không hợp lệ', 400);
        }

        $order = $this->speakerOrderService->getOrderByCode($orderCode);

        if (! $order) {
            return $this->failNotFound('Không tìm thấy đơn hàng');
        }

        $decals = $this->decalService->getDecalsByOrder($order['order']->id);
        $decals = array_map(function ($decal) {
            return [
                'id' => (int) $decal->id,
                'physical_order_id' => (int) $decal->physical_order_id,
                'physical_order_item_id' => (int) $decal->physical_order_item_id,
                'account_number' => $decal->account_number,
            ];
        }, $decals);

        return $this->respond([
            'status' => true,
            'data' => $decals,
        ]);
    }

    public function saveDecals($orderCode = null)
    {
        if ($this->request->getMethod(true) !== 'POST' || empty($orderCode)) {
            return $this->fail('Mã đơn hàng không hợp lệ', 400);
        }

        if (!$this->validate([
            'decals' => 'required',
            'decals.*' => 'required|string',
        ])) {
            return $this->failValidationErrors($this->validator->getErrors());
        }

        $orderResult = $this->speakerOrderService->getOrderByCode($orderCode);

        if (! $orderResult) {
            return $this->failNotFound('Không tìm thấy đơn hàng');
        }

        $order = $orderResult['order'];

        if (! in_array($order->status, ['Pending', 'Confirmed', 'Processing'])) {
            return $this->fail('Đơn hàng không thể cập nhật decal', 400);
        }

        try {
            $result = $this->decalService->saveDecalSettings(
                $order,
                $this->request->getVar('decals')
            );

            return $this->respond($result);
        } catch (Exception $e) {
            log_message('error', '[Internal API] Lỗi lưu decal: ' . $e->getMessage());

            return $this->respond([
                'status' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    public function deleteDecals($orderCode = null)
    {
        if ($this->request->getMethod(true) !== 'POST' || empty($orderCode)) {
            return $this->fail('Mã đơn hàng không hợp lệ', 400);
        }

        $orderResult = $this->speakerOrderService->getOrderByCode($orderCode);

        if (! $orderResult) {
            return $this->failNotFound('Không tìm thấy đơn hàng');
        }

        $order = $orderResult['order'];

        if (! in_array($order->status, ['Pending', 'Confirmed', 'Processing'])) {
            return $this->fail('Đơn hàng không thể cập nhật decal', 400);
        }

        try {
            $result = $this->decalService->saveDecalSettings(
                $order,
                []
            );

            return $this->respond($result);
        } catch (Exception $e) {
            log_message('error', '[Internal API] Lỗi xóa decal: ' . $e->getMessage());

            return $this->respond([
                'status' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    public function getBankBranches($bankId = null)
    {
        if ($this->request->getMethod(true) !== 'GET' || empty($bankId)) {
            return $this->fail('Vui lòng chọn ngân hàng', 400);
        }

        $bankBranches = $this->bankBranchesService->getBankBranches($bankId);

        return $this->respond([
            'status' => true,
            'data' => $bankBranches,
        ]);
    }
}
