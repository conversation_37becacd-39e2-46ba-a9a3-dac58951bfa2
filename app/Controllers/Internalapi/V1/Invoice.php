<?php

namespace App\Controllers\Internalapi\V1;

use App\Controllers\Internalapi\V1\BaseController;
use App\Models\AddonModel;
use App\Models\ChannelPartnerCompanyModel;
use App\Models\InvoiceItemModel;
use App\Models\InvoiceModel;
use App\Models\ProductModel;

class Invoice extends BaseController
{

     // [GET]
     public function invoice_view_all() {

        $request = service('request');
        $method = $request->getMethod();
        if (strtoupper($method) != 'GET') {
            return $this->responseResult(405, "Phương thức URL không hợp lệ!");
        }
        $channel_partner_officer_id = $this->request->getGet('channel_partner_officer_id');
        $channel_partner_id = $this->request->getGet('channel_partner_id');
        $condition_check = array();
        
        $invoice_id = $request->getGet('invoice_id');
        $company_id = $request->getGet('company_id');
        $status = $request->getGet('status');

        $InvoiceModel = model(InvoiceModel::class);
        $ChannelPartnerCompanyModel = model(ChannelPartnerCompanyModel::class);

        if(isset($channel_partner_officer_id)){
            $condition_check['channel_partner_officer_id'] = $channel_partner_officer_id;
        }
        if(isset($channel_partner_id)){
            $condition_check['channel_partner_id'] = $channel_partner_id;
        }
        if(isset($company_id)){
            $condition_check['company_id'] = $company_id;
        }
     
        if(!empty($condition_check)){
            $result_check = $ChannelPartnerCompanyModel->where($condition_check)->get()->getRowArray();
            if(empty($result_check)){
                return $this->responseResult(404,"Hóa đơn không thuộc hệ thống!");
            }
        }
    
        $builder = $InvoiceModel->orderBy('id desc');
        
    
        if (isset($invoice_id)) {
            $builder->where(['id'=>$invoice_id]);
        } 

        

        if (isset($company_id)) {
            $builder->where(['company_id'=>$company_id]);
        } 

        if (isset($status)) {
            $builder->where(['status'=>$status]);
        } 

        $data = $builder->get()
        ->getResultArray();

        if(!empty($data)){
            foreach($data as &$val){
                $val['invoice_id'] = $val['id'];
            }
            unset($val);
        }
        return $this->responseResult(200, "Danh sách hóa đơn!", $data);
    }

    // [GET]
    public function invoice_item_view_all() {

        $request = service('request');
        $method = $request->getMethod();
        if (strtoupper($method) != 'GET') {
            return $this->responseResult(405, "Phương thức URL không hợp lệ!");
        }
    
        $InvoiceItemModel = model(InvoiceItemModel::class); 
        $invoice_id = $request->getGet('invoice_id');
        $item_id = $request->getGet('item_id');
        $type = $request->getGet('type');
    
        // Sử dụng Query Builder và join với bảng tb_invoice
        $builder = $InvoiceItemModel
                    ->select('tb_autopay_invoice_item.*, tb_autopay_invoice.company_id') // Chọn các cột cần thiết
                    ->join('tb_autopay_invoice', 'tb_autopay_invoice.id = tb_autopay_invoice_item.invoice_id', 'left') // Join bảng tb_invoice
                    ->orderBy('tb_autopay_invoice_item.id desc');
    
        // Điều kiện lọc dữ liệu
        if (isset($invoice_id)) {
            $builder->where(['tb_autopay_invoice_item.invoice_id' => $invoice_id]);
        }
    
        if (isset($item_id)) {
            $builder->where(['tb_autopay_invoice_item.item_id' => $item_id]);
        }
    
        if (isset($type)) {
            $builder->where(['tb_autopay_invoice_item.type' => $type]);
        }
    
        // Lấy dữ liệu
        $data = $builder->get()->getResultArray();
    
        // Xử lý dữ liệu nếu có
        if (!empty($data)) {
            foreach ($data as &$val) {
                $val['invoice_item_id'] = $val['id'];
                unset($val['id']);
            }
            unset($val);
        }
    
        return $this->responseResult(200, "Thông tin chi tiết hóa đơn!", $data);
    }
    

     // [GET]
     public function invoice_officer_all() {

        $request = service('request');
        $method = $request->getMethod();
        if (strtoupper($method) != 'GET') {
            return $this->responseResult(405, "Phương thức URL không hợp lệ!");
        }


        $channel_partner_officer_id = $request->getGet('channel_partner_officer_id');
        $channel_partner_id = $request->getGet('channel_partner_id');
        $status = $request->getGet('status');
        $no_commission = $request->getGet('no_commission');

        $InvoiceModel = model(InvoiceModel::class);
    
        $builder = $InvoiceModel->select('
        tb_autopay_invoice.id invoice_id,
        tb_autopay_invoice.status invoice_status,
        tb_autopay_invoice.type invoice_type,
        tb_autopay_invoice.subtotal invoice_subtotal,
        tb_autopay_invoice.total invoice_total,

        tb_autopay_channel_partner_company.channel_partner_officer_id,
        tb_autopay_channel_partner_company.channel_partner_id,
        tb_autopay_channel_partner_company.company_id,
        
        ')
        ->join('tb_autopay_channel_partner_company','tb_autopay_channel_partner_company.company_id=tb_autopay_invoice.company_id','left')

        ->orderBy('invoice_id desc');
        
    
         if (isset($channel_partner_id)) {
            $builder->where(['tb_autopay_channel_partner_company.channel_partner_id'=>$channel_partner_id]);
        } 

        if (isset($channel_partner_officer_id)) {
            $builder->where(['tb_autopay_channel_partner_company.channel_partner_officer_id'=>$channel_partner_officer_id]);
        } 

        if (isset($status)) {
            $builder->where(['tb_autopay_invoice.status'=>$status]);
        } 

        if (isset($no_commission)) {
           
            if($no_commission=="true"){
                $builder->where('tb_autopay_invoice.id NOT IN (SELECT invoice_id FROM tb_autopay_channel_partner_commission)');
            }
            
        } 
        $data = $builder->get()
        ->getResultArray();

        

        return $this->responseResult(200, "Danh sách hóa đơn Officer", $data);
    }
}
