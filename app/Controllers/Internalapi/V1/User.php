<?php

namespace App\Controllers\Internalapi\V1;

use App\Controllers\Internalapi\V1\BaseController;
use App\Models\AddonModel;
use App\Models\ChannelPartnerCompanyModel;
use App\Models\CompanyModel;
use App\Models\CompanySubscriptionModel;
use App\Models\CompanyUserModel;
use App\Models\ConfigurationModel;
use App\Models\InvoiceItemModel;
use App\Models\InvoiceModel;
use App\Models\OrderModel;
use App\Models\ProductModel;
use App\Models\UserModel;
use App\Models\UserPermissionFeatureModel;

class User extends BaseController
{
    

    // [POST]
    public function create()
    {
       
        $request = service('request');
        $input = $request->getBody();
        
        if(empty($input)){
            return $this->responseResult("422","Không tìm thấy dữ liệu gửi lên!");
        }
        $data = json_decode($input,true);

        $method = $request->getMethod();
        if (strtoupper($method) != 'POST') {
            return $this->responseResult(405, "Phương thức URL không hợp lệ!");
        }

        $UserModel = model(UserModel::class);
        $CompanyModel = model(CompanyModel::class);

        $validationRules = [
            'company_id'    => 'required|max_length[11]|is_not_unique[tb_autopay_company.id]',
            'email'         => [
                'rules'  => 'required|valid_email|is_unique[tb_autopay_user.email]',
                'errors' => [
                    'is_unique' => 'Email đã tồn tại trên hệ thống!',
                ],
            ],
            'password'      => 'required|min_length[8]',
            'phonenumber'   => 'required|regex_match[/^\+?[0-9]{7,15}$/]',
            'firstname'     => 'required|string|max_length[50]',
            'lastname'      => 'required|string|max_length[50]',
            'role'          => 'required|in_list[User,Admin,SuperAdmin]',
        ];
        
        

        $validation = service('validation');
        $validation->setRules($validationRules);
        if (!$validation->run($data)) {
            return $this->responseResult(422, $validation->getErrors());
        }

        $data_company = $CompanyModel->where(['id'=>$data['company_id']])->get()->getRowArray();

        if(empty($data_company['user_owner_id'])){
            $data['role'] = "SuperAdmin";
            
        }else if(!empty($data_company['user_owner_id']) && $data['role']=="SuperAdmin"){
            return $this->responseResult(422,['role'=>"Vai trò người dùng phải là User hoặc Admin"]);
        }

        
        $data['company_name'] = $data_company['full_name'];            
 
        $pwd_hash = password_hash(trim($data['password']), PASSWORD_DEFAULT);
        $data['password'] = $pwd_hash;

        $datalog = array(
            'channel_partner_id' => $data['channel_partner_id'] ?? "",
            'data_type' => "json",
            'ip' => $this->request->getIPAddress() ?? "",
            'user_agent' => trim(xss_clean($this->request->getUserAgent()->getAgentString())),
            'description' => "",
            'status' =>  "",
            'data_id' => null,
        );

        
        try{
            $data_add = array(
                'company_id' => $data['company_id'],
                'email' => $data['email'],
                'password' => $data['password'],
                'phonenumber' => trim(xss_clean($data['phonenumber'])),
                'firstname' => trim(xss_clean($data['firstname'])),
                'lastname' => trim(xss_clean($data['lastname'])),
                'role' => trim(xss_clean($data['role'])),
            );
            $insert_data = $UserModel->insert($data_add);
            
            if($insert_data){
                $id_insert = $UserModel->insertID();

                $data_company_user = array(
                    'company_id' => $data['company_id'],
                    'user_id' => $id_insert,
                    'role' => trim(xss_clean($data['role'])),
                );

                // init permission
                $userPermissionFeatureModel = model(UserPermissionFeatureModel::class);

                $userPermissionFeatureModel->initialize_permission($id_insert, $data['company_id']);

                $configurationModel = model(ConfigurationModel::class);
                $configurationModel->initialize_data($data['company_id']);

                // 

                $link_result = $this->link_company_user($data_company_user);

                if(!empty($link_result)){

                    if($data['role']=="SuperAdmin"){
                        $CompanyModel->update($data['company_id'],['user_owner_id'=>$id_insert]);
                    }

                    // savelog
                    $datalog['description'] = "Tạo người dùng!";
                    $datalog['status'] = "Success";
                    $datalog['data_id'] = $id_insert;
                    Cpalog::savelog($datalog);
                    
                    return $this->responseResult(200, "Tạo người dùng thành công!",['user_id'=>$id_insert]);
                
                }else{
                     // savelog
                     $datalog['description'] = "Tạo người dùng!";
                     $datalog['status'] = "Failed";
                     $datalog['data_id'] = null;
                     Cpalog::savelog($datalog);

                    return $this->responseResult(500,"Lỗi hệ thống, không thể liên kết người dùng với công ty, Hãy liên hệ kỹ thuật!");
                }

            }else{
                 // savelog
                 $datalog['description'] = "Tạo người dùng!";
                 $datalog['status'] = "Failed";
                 $datalog['data_id'] = null;
                 Cpalog::savelog($datalog);
                return $this->responseResult(500,"Lỗi hệ thống, không thể thêm người dùng, Hãy liên hệ kỹ thuật!");
            }

        }catch(\Exception $e){
                // savelog
                $datalog['description'] = "Tạo người dùng!";
                $datalog['status'] = "Failed";
                $datalog['data_id'] = null;
                Cpalog::savelog($datalog);
                return $this->responseResult(500,"Lỗi hệ thống, mã lỗi : ".$e->getMessage());
        }

    }

    // [Action]
    protected function link_company_user($data){


        $CompanyUser = model(CompanyUserModel::class);

        try{

            $insert_data = $CompanyUser->insert($data);
            
            if(!empty($insert_data)){

                return true;
            }else{

                return false;
            }

        }catch(\Exception $e){
                return false;
        }

    }

    // [GET]
    public function all() {
        $request = service('request');
    
        if (strtoupper($request->getMethod()) !== 'GET') {
            return $this->responseResult(405, "Phương thức URL không hợp lệ!");
        }
    
        $CompanyUserModel = model(CompanyUserModel::class);
    
        $condition = [];
    
        $company_id = $request->getGet('company_id');
        $channel_partner_id = $request->getGet('channel_partner_id');
        $user_id = $request->getGet('user_id');
        $role = $request->getGet('role');
        $email = $request->getGet('email');
        $status = $request->getGet('status');
    
        if (isset($company_id)) {
            $condition['tb_autopay_company_user.company_id'] = $company_id;
        }
        if (isset($user_id)) {
            $condition['tb_autopay_company_user.user_id'] = $user_id;
        }
        if (isset($role)) {
            $condition['tb_autopay_company_user.role'] = $role;
        }
        if (isset($email)) {
            $condition['tb_autopay_user.email'] = $email;
        }
        if (isset($status)) {
            $condition['tb_autopay_company.status'] = $status;
        }
        if (isset($channel_partner_id)) {
            $condition['tb_autopay_company.channel_partner_id'] = $channel_partner_id;
        }
    
        try {
            $list_data_user = $CompanyUserModel->select('
                tb_autopay_company.status,
                tb_autopay_company.short_name AS short_name_company,

                tb_autopay_company_user.role,
                tb_autopay_company_user.company_id,
                tb_autopay_company_user.user_id,

                tb_autopay_user.company_name,
                tb_autopay_user.firstname,
                tb_autopay_user.lastname,
                tb_autopay_user.email,
                tb_autopay_user.phonenumber'
            )
            ->join('tb_autopay_company', 'tb_autopay_company.id = tb_autopay_company_user.company_id', 'LEFT')
            ->join('tb_autopay_user', 'tb_autopay_user.id = tb_autopay_company_user.user_id', 'LEFT')
            ->where($condition) 
            ->orderBy('tb_autopay_user.id DESC')
            ->findAll();
    
            return $this->responseResult(200, "Danh sách người dùng đã tạo", $list_data_user);
    
        } catch (\Exception $e) {
            log_message("error", "Lỗi api GET danh sách người dùng: " . $e->getMessage());
            return $this->responseResult(500, "Lỗi server, Hãy liên hệ kỹ thuật!");
        }
    }

   // [GET]
    public function check_user() {
        $request = service('request');

        if (strtoupper($request->getMethod()) !== 'GET') {
            return $this->responseResult(405, "Phương thức URL không hợp lệ!");
        }

        $CompanyUserModel = model(CompanyUserModel::class);

        $condition = [];

        $company_id = $request->getGet('company_id');
        $channel_partner_id = $request->getGet('channel_partner_id');
        $user_id = $request->getGet('user_id');
        $role = $request->getGet('role');
        $email = $request->getGet('email');
        $status = $request->getGet('status');

        if (isset($company_id)) {
            $condition['tb_autopay_company_user.company_id'] = $company_id;
        }
        if (isset($user_id)) {
            $condition['tb_autopay_company_user.user_id'] = $user_id;
        }
        if (isset($role)) {
            $condition['tb_autopay_company_user.role'] = $role;
        }
        if (isset($email)) {
            $condition['tb_autopay_user.email'] = $email;
        }
        if (isset($status)) {
            $condition['tb_autopay_company.status'] = $status;
        }
        if (isset($channel_partner_id)) {
            $condition['tb_autopay_company.channel_partner_id'] = $channel_partner_id;
        }

        try {
            $exists = $CompanyUserModel->join('tb_autopay_company', 'tb_autopay_company.id = tb_autopay_company_user.company_id', 'LEFT')
                ->join('tb_autopay_user', 'tb_autopay_user.id = tb_autopay_company_user.user_id', 'LEFT')
                ->where($condition)
                ->first(); // Kiểm tra xem có bất kỳ dữ liệu nào không

            if ($exists) {
                return $this->responseResult(200, "User tồn tại", true);
            } else {
                return $this->responseResult(404, "User không tồn tại", false);
            }

        } catch (\Exception $e) {
            log_message("error", "Lỗi api GET kiểm tra người dùng: " . $e->getMessage());
            return $this->responseResult(500, "Lỗi server, Hãy liên hệ kỹ thuật!");
        }
    }

    

    // [GET]
    public function detail($id=null){

        $CompanyUserModel = model(CompanyUserModel::class);
        $ChannelPartnerCompanyModel = model(ChannelPartnerCompanyModel::class);
        $company_id = $CompanyUserModel->where(['user_id'=>$id])->get()->getRowArray()['company_id']?? null;
        $channel_partner_officer_id = $this->request->getGet('channel_partner_officer_id');
        $channel_partner_id = $this->request->getGet('channel_partner_id');

        if(empty($company_id)){
            $array_response = array();
    
            return $this->responseResult(200, "Thông tin chi tiết người dùng",$array_response);
        }

        $condition_check = array();
        if(isset($channel_partner_officer_id)){
            $condition_check['channel_partner_officer_id'] = $channel_partner_officer_id;
        }
        if(isset($channel_partner_id)){
            $condition_check['channel_partner_id'] = $channel_partner_id;
        }
        if(isset($company_id)){
            $condition_check['company_id'] = $company_id;
        }

        if(!empty($condition_check)){
            $result_check = $ChannelPartnerCompanyModel->where($condition_check)->get()->getRowArray();
            if(empty($result_check)){
                return $this->responseResult(404,"Người dùng không thuộc hệ thống");
            }
        }
        
        $array_response = $CompanyUserModel->select('
        tb_autopay_company_user.user_id,
        tb_autopay_company_user.role,
        tb_autopay_user.firstname user_firstname,
        tb_autopay_user.lastname user_lastname,
        tb_autopay_user.email user_email,
        tb_autopay_user.phonenumber user_phonenumber,
        tb_autopay_company_user.company_id,
        tb_autopay_user.company_name,
        tb_autopay_company.short_name AS short_name_company,
        tb_autopay_company.status company_status,
        '
        )
        ->join('tb_autopay_company', 'tb_autopay_company.id = tb_autopay_company_user.company_id', 'LEFT')
        ->join('tb_autopay_user', 'tb_autopay_user.id = tb_autopay_company_user.user_id', 'LEFT')
        ->where(['tb_autopay_user.id'=>$id])
        ->orderBy('tb_autopay_user.id DESC')
        ->get()->getRowArray();

        return $this->responseResult(200, "Thông tin chi tiết người dùng",$array_response);
        
        

    }

    
}
