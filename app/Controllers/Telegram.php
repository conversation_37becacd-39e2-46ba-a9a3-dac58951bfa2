<?php

namespace App\Controllers;

use CodeIgniter\API\ResponseTrait;
use App\Controllers\BaseController;
use App\Features\Store\StoreFeature;
use App\Models\NotificationTelegramModel;
use App\Models\NotificationTelegramQueueModel;

class Telegram extends BaseController
{
    use ResponseTrait;

    public function index()
    {
        if (!has_permission('NotificationTelegram', 'can_view_all')) {
            show_404();
        }
        
        $data = [
            'page_title' => 'Telegram',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];
        
        $storeFeature = new StoreFeature;
        $storeFeature->withCompanyContext($this->user_session['company_id']);
        
        $data['telegram_total'] = $storeFeature->telegramBuilder()->countAllResults();
        
        echo theme_view('templates/autopay/header', $data);
        echo theme_view('telegram/index', $data);
        echo theme_view('templates/autopay/footer', $data);
    }
    
    public function add()
    {
        if (!has_permission('NotificationTelegram', 'can_add')) {
            show_404();
        }
 
        $session = service('session');
        $integrationData = $session->get('telegram_integration');

        $data = [
            'page_title' => 'Telegram',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'default_store_id' => $this->request->getGet('store_id')
        ];
        
        $step = $this->request->getGet('step') ?? 1;
        $fallbackUrl = base_url('telegram/add' . ($data['default_store_id'] ? '?store_id=' . $data['default_store_id'] : ''));
        
        if (!is_numeric($step) || $step < 1 || $step > 4) {
            return redirect()->to($fallbackUrl);
        }
        
        if ($step > 1 && !is_array($integrationData)) {
            return redirect()->to($fallbackUrl);
        }
        
        if ($step > 1 && (!is_array($integrationData) || !$integrationData['is_connected'])) {
            return redirect()->to($fallbackUrl);
        }
        
        $data['step'] = (int) $step;

        echo theme_view('templates/autopay/header', $data);

        $storeFeature = new StoreFeature;
        $storeFeature->withCompanyContext($this->user_session['company_id']);
        
        $data['integration'] = $integrationData;
        
        if ($data['step'] === 2) {
            $data['stores'] = $storeFeature->getLinkableTelegramStores($integrationData['chat_id'], $integrationData['message_thread_id'], $integrationData['transaction_type']);

            if ($data['default_store_id'] ) {
                $stores = $data['stores'];
                $defaultStoreKey = array_search($data['default_store_id'] , array_column($stores, 'id'));

                if ($defaultStoreKey !== false) {
                    $defaultStore = $stores[$defaultStoreKey];
                    unset($stores[$defaultStoreKey]);
                    array_unshift($stores, $defaultStore);
                    $data['stores'] = $stores;
                }
            }
            
            echo theme_view('telegram/add/step2', $data);
        } else if ($data['step'] === 3) {
            echo theme_view('telegram/add/step3', $data);
        } else if ($data['step'] === 4) {
            $session->remove('telegram_integration');
            $data['stores'] = $storeFeature->getLinkedTelegramStores($integrationData['chat_id'], $integrationData['message_thread_id'], $integrationData['transaction_type']);
            echo theme_view('telegram/add/step4', $data);
        } else {
            $session->remove('telegram_integration');
            echo theme_view('telegram/add/step1', $data);
        }

        echo theme_view('templates/autopay/footer', $data);
    }
    
    public function ajax_add_step_1()
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }

        if (! has_permission('NotificationTelegram', 'can_add')) {
            return $this->failNotFound();
        }

        $data = [
            'chat_id' => trim($this->request->getPost('chat_id')),
            'description' => trim($this->request->getPost('description')),
            'message_thread_id' => trim($this->request->getPost('message_thread_id')),
            'transaction_type' => trim($this->request->getPost('transaction_type'))
        ];

        $rules = [
            'chat_id' => ['required', 'regex_match[/^\-\d+$/]'],
            'description' => ['required', 'max_length[100]'],
            'message_thread_id' => ['permit_empty'],
            'transaction_type' => ['required','in_list[All,In_only,Out_only]'],
        ];

        if (! $this->validateData($data, $rules)) {
            return $this->fail($this->validator->getErrors());
        }
        
        $storeFeature = new StoreFeature;
        $storeFeature->withCompanyContext($this->user_session['company_id']);
        
        if ($storeFeature->telegramBuilder()->where('chat_id', $data['chat_id'])->where('message_thread_id', $data['message_thread_id'])->where('transaction_type', $data['transaction_type'])->countAllResults() > 0) {
            return $this->respond(['status' => false, 'message' => 'Tích hợp Telegram đã tồn tại, vui lòng kiểm tra lại.']);
        }
        
        if (!$data['message_thread_id'] || !is_numeric($data['message_thread_id']))
            $data['message_thread_id'] = 0;

        $session = service('session');
        $session->set('telegram_integration', array_merge($session->get('telegram_integration') ?? [], $data));

        return $this->respond(['status' => true]);
    }
    
    public function ajax_add_step_2()
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }

        if (! has_permission('NotificationTelegram', 'can_add')) {
            return $this->failNotFound();
        }
        
        $session = service('session');
        $integrationData = $session->get('telegram_integration');
        
        if (! $session->get('telegram_integration')) {
            return $this->respond(['status' => false, 'redirect_to' => base_url('telegram/add')]);
        }
        
        $storeIds = $this->request->getVar('store_ids');

        if (!is_array($storeIds) || empty($storeIds)) {
            return $this->respond(['status' => false, 'message' => 'Vui lòng chọn ít nhất một cửa hàng']);
        }
        
        $storeFeature = new StoreFeature;
        $storeFeature->withCompanyContext($this->user_session['company_id']);
        $linkableStores = $storeFeature->getLinkableTelegramStores($integrationData['chat_id'], $integrationData['message_thread_id'], $integrationData['transaction_type']);
        
        if (count(array_diff($storeIds, array_column($linkableStores, 'id'))) > 0) {
            return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng tải lại trang']);
        }
        
        $session->set('telegram_integration', array_merge($integrationData, [
            'links' => array_column($linkableStores, 'links'),
            'store_ids' => $storeIds,
        ]));
        
        return $this->respond(['status' => true]);
    }
    
    public function ajax_add_step_3()
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }

        if (! has_permission('NotificationTelegram', 'can_add')) {
            return $this->failNotFound();
        }
        
        $session = service('session');
        $integrationData = $session->get('telegram_integration');
        
        if (! $session->get('telegram_integration')) {
            return $this->respond(['status' => false, 'redirect_to' => base_url('telegram/add')]);
        }
        
        $data = [
            'is_template_custom' => $this->request->getVar('is_template_custom'),
            'template_custom' => $this->request->getVar('template_custom'),
            'template_name' => $this->request->getVar('template_name'),
            'hide_accumulated' => $this->request->getVar('hide_accumulated'),
            'hide_details_link' => $this->request->getVar('hide_details_link')
        ];

        if (!in_array($data['template_name'], ['template_1', 'template_2', 'template_3'])) {
            return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng tải lại trang']);
        }

        if ($data['is_template_custom'] && !trim($data['template_custom'])) {
            return $this->fail(['template_custom' => 'Nội dung tin nhắn là bắt buộc']);
        }

        if (!$integrationData
        || !array_key_exists('chat_id', $integrationData)
        || !array_key_exists('description', $integrationData)
        || !array_key_exists('message_thread_id', $integrationData)
        || !array_key_exists('transaction_type', $integrationData)
        || !array_key_exists('links', $integrationData)
        || !array_key_exists('store_ids', $integrationData)) {
            return redirect()->to('telegram/add?step=1');
        }

        $data = array_merge($data, $integrationData);
        
        $storeFeature = new StoreFeature;
        $storeFeature->withCompanyContext($this->user_session['company_id']);

        $storeFeature->linkStoresToTelegram($data['store_ids'], $data);
        
        add_user_log(['data_id' => 0, 'company_id' => $this->user_session['company_id'], 'data_type' => 'notification_telegram_add', 'description' => 'Thêm tích hợp thông báo Telegram', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);
        
        return $this->respond(['status' => true]);
    }
    
    public function edit(string $telegramId = '')
    {
        if (! has_permission('NotificationTelegram', 'can_edit')) {
            show_404();
        }
        
        $data = [
            'page_title' => 'Telegram',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];
        
        $telegramId = trim($telegramId);
        
        if (!is_string($telegramId)) {
            return $this->failNotFound();
        }
        
        $tab = $this->request->getGet('tab') ?? '';
        
        if ($tab && !in_array($tab, ['store', 'custom-message'])) {
            return redirect()->to(base_url('telegram/edit/' . $telegramId));
        }
        
        $data['tab'] = $tab;
        
        $storeFeature = new StoreFeature;
        $storeFeature->withCompanyContext($this->user_session['company_id']);
        
        $data['telegram'] = $storeFeature->telegramBuilder()
            ->where('id', $telegramId)
            ->first();
            
        if (!$data['telegram']) {
            show_404();
        }
        
        $data['stores'] = $storeFeature->getLinkedTelegramStores($data['telegram']->chat_id, $data['telegram']->message_thread_id, $data['telegram']->transaction_type);
        
        if (!count($data['stores'])) {
            return $this->failNotFound();
        }
        
        echo theme_view('templates/autopay/header', $data);
        
        if ($data['tab'] === 'general') {
            echo theme_view('telegram/edit/general', $data);
        } else if ($data['tab'] === 'store') {
            $data['linkable_stores'] = $storeFeature->getLinkableTelegramStores($data['telegram']->chat_id, $data['telegram']->message_thread_id, $data['telegram']->transaction_type);
            echo theme_view('telegram/edit/store', $data);
        } else if ($data['tab'] === 'custom-message') {
            echo theme_view('telegram/edit/custom-message', $data);
        } else {
            echo theme_view('telegram/edit/general', $data);
        }
        
        echo theme_view('templates/autopay/footer', $data);
    }
    
    public function ajax_edit(string $telegramId = '')
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }

        $telegramId = trim($telegramId);
        
        if (!is_string($telegramId)) {
            return $this->failNotFound();
        }
        
        if (! has_permission('NotificationTelegram', 'can_edit')) {
            return $this->failNotFound();
        }
        
        $storeFeature = new StoreFeature;
        $storeFeature->withCompanyContext($this->user_session['company_id']);
        
        $telegram = $storeFeature->telegramBuilder()->where('id', $telegramId)->first();
            
        if (!$telegram) {
            return $this->failNotFound();
        }
        
        $stores = $storeFeature->getLinkedTelegramStores($telegram->chat_id, $telegram->message_thread_id, $telegram->transaction_type);
        
        if (!count($stores)) {
            return $this->failNotFound();
        }
        
        $data = [
            'chat_id' => trim($this->request->getPost('chat_id')),
            'description' => trim($this->request->getPost('description')),
            'message_thread_id' => trim($this->request->getPost('message_thread_id')),
            'transaction_type' => trim($this->request->getPost('transaction_type')),
            'is_template_custom' => $this->request->getVar('is_template_custom'),
            'template_custom' => $this->request->getVar('template_custom'),
            'template_name' => $this->request->getVar('template_name'),
            'hide_accumulated' => $this->request->getVar('hide_accumulated'),
            'hide_details_link' => $this->request->getVar('hide_details_link'),
            'active' => $this->request->getVar('active')
        ];

        $rules = [
            'chat_id' => ['permit_empty', 'regex_match[/^\-\d+$/]'],
            'description' => ['permit_empty', 'max_length[100]'],
            'message_thread_id' => ['permit_empty'],
            'transaction_type' => ['permit_empty','in_list[All,In_only,Out_only]'],
        ];
        
        if (! $this->validateData($data, $rules)) {
            return $this->fail($this->validator->getErrors());
        }
        
        
        if (($telegram->chat_id != $data['chat_id'] || $telegram->message_thread_id != $data['message_thread_id'] || $telegram->transaction_type != $data['transaction_type']) && $storeFeature->telegramBuilder()->where('chat_id', $data['chat_id'])->where('message_thread_id', $data['message_thread_id'])->where('transaction_type', $data['transaction_type'])->countAllResults() > 0) {
            return $this->respond(['status' => false, 'message' => 'Tích hợp Telegram đã tồn tại, vui lòng kiểm tra lại.']);
        }
        
        if (($data['chat_id'] && $telegram->chat_id != $data['chat_id']) || ($data['message_thread_id'] && $telegram->message_thread_id != $data['message_thread_id'])) {
            $session = service('session');
            $integrationData = $session->get('telegram_integration');
            
            $connected = isset($integrationData) && $integrationData['is_connected'] && $integrationData['chat_id'] == $data['chat_id'];
            
            if (!$connected) {
                return $this->respond(['status' => false, 'message' => 'ID nhóm Telegram chưa được đảm bảo kết nối, vui lòng thử kết nối trước khi thay đổi.']);
            }
        }
        
        if ($data['template_name'] && !in_array($data['template_name'], ['template_1', 'template_2', 'template_3'])) {
            return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng tải lại trang']);
        }

        if ($data['is_template_custom'] && !trim($data['template_custom'])) {
            return $this->fail(['template_custom' => 'Nội dung tin nhắn là bắt buộc']);
        }
        
        $updated = $storeFeature->editStoreTelegram($telegram->chat_id, $telegram->message_thread_id, $telegram->transaction_type, $data);
        
        if ($updated) {
            add_user_log(['data_id' => 0, 'company_id' => $this->user_session['company_id'], 'data_type' => 'notification_telegram_add', 'description' => 'Cập nhật tích hợp thông báo Telegram', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);
        }
        
        return $this->respond(['status' => true, 'message' => 'Cập nhật thành công']);
    }

    public function ajax_test_telegram_connection()
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }

        if (! has_permission('NotificationTelegram', 'can_add')) {
            return $this->failNotFound();
        }

        $data = [
            'chat_id' => trim($this->request->getVar('chat_id')),
            'message_thread_id' => trim($this->request->getVar('message_thread_id'))
        ];
        
        $rules = [
            'chat_id' => ['required', 'regex_match[/^\-\d+$/]'],
            'message_thread_id' => ['permit_empty']
        ];

        if (! $this->validateData($data, $rules)) {
            return $this->fail($this->validator->getErrors());
        }

        if (!$data['message_thread_id'] || !is_numeric($data['message_thread_id']))
            $data['message_thread_id'] = 0;

        return $this->testTelegramConnection($data['chat_id'], $data['message_thread_id']);
    }
    
    public function ajax_test_telegram_message(string $telegramId = '')
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }

        if (!has_permission('NotificationTelegram', 'can_add')) {
            return $this->failNotFound();
        }

        $data = [
            'template_custom' => $this->request->getVar('template_custom'),
            'is_template_custom' => $this->request->getVar('is_template_custom'),
            'hide_accumulated' => $this->request->getVar('hide_accumulated'),
            'hide_details_link' => $this->request->getVar('hide_details_link')
        ];

        if ($data['is_template_custom'] && !trim($data['template_custom'])) {
            return $this->fail(['template_custom' => 'Nội dung tin nhắn là bắt buộc']);
        }

        if ($telegramId) {
            $storeFeature = new StoreFeature;
            $storeFeature->withCompanyContext($this->user_session['company_id']);
            
            $integrationData = $storeFeature->telegramBuilder()->where('id', $telegramId)->first();
                
            if (!$integrationData) {
                return $this->failNotFound();
            }
            
            $integrationData = (array) $integrationData;
        } else {
            $session = service('session');
    
            $integrationData = $session->get('telegram_integration');
            
            if (!is_array($integrationData) 
            || !array_key_exists('chat_id', $integrationData) 
            || !array_key_exists('description', $integrationData) 
            || !array_key_exists('message_thread_id', $integrationData)
            || !array_key_exists('transaction_type', $integrationData)
            || !array_key_exists('store_ids', $integrationData)) {
                return $this->respond(['status' => false, 'redirect_to' => base_url('telegram/add')]);
            }
        }

        $transactionDetails = (object) [
            'id' => '103896',
            'accumulated' => '********',
            'amount_in' => '********',
            'amount_out' => 0,
            'account_number' => '*************',
            'brand_name' => 'Vietcombank',
            'transaction_content' => 'TRAN ANH DUONG chuyen tien',
            'code' => 'HD1029148',
            'reference_number' => '171298.050723.020002',
            'transaction_date' => '2023-07-05 13:59:48',
            'sub_account' => null,
        ];

        $telegramDetails = (object) [
            'template_custom' => $data['template_custom']
        ];

        if (!$data['is_template_custom']) {
            $accumulated = "

⛳️ Số dư: <code>" . number_format($transactionDetails->accumulated) . ' đ</code>
';
            $detailsLink = '
            
Xem <a href="https://my.sepay.vn">chi tiết</a>.
';

            if ($data['hide_accumulated'])
                $accumulated = "
";

            if ($data['hide_details_link'])
                $detailsLink = "
";

            $message = "--------- - ID: " . $transactionDetails->id . " ---------
Có giao dịch mới:

✳️ Tiền vào: <code>" . number_format($transactionDetails->amount_in) . " đ</code>

➖ Tiền ra: <code>" . number_format($transactionDetails->amount_out) . " đ</code>" . $accumulated . 
"
⛪️ Tài khoản chính: <code>" . $transactionDetails->account_number . "</code> " . $transactionDetails->brand_name . "

ℹ️ Nội dung thanh toán: <code>" . $transactionDetails->transaction_content . "</code>

#️⃣ Mã code thanh toán: <code>" . $transactionDetails->code . "</code>

⚓️ Mã tham chiếu: <code>" . $transactionDetails->reference_number . "</code>

⏰ Giao dịch lúc: <code>" . $transactionDetails->transaction_date . "</code>" .
$detailsLink .
"
------------------------------";
        } else {
            $message = model(NotificationTelegramModel::class)->getMessage($transactionDetails, $telegramDetails);
        }

        return $this->testTelegramConnection($integrationData['chat_id'], $integrationData['message_thread_id'], $message);
    }

    protected function testTelegramConnection($chatId, $messageThreadId, $message = null)
    {
        $notificationTelegramQueueModel = model(NotificationTelegramQueueModel::class);
        $message = $message ?? "✅ Kết nối thành công!";
        $result = $notificationTelegramQueueModel->sendTelegramMessage($message, $chatId, 'html', $messageThreadId);

        if (isset($result['success']) && $result['success'] == true && isset($result['response'])) {
            $response = json_decode($result['response']);

            $session = service("session");
            $session_data_integration = $session->get('telegram_integration');
            if (is_object($response) && isset($response->ok) && $response->ok) {

                $session_data_integration['is_connected'] = 1;
                $session_data_integration['chat_id'] = $chatId;
                
                $session->set('telegram_integration', $session_data_integration);

                return $this->response->setJSON(['status' => true, 'data' => [
                    'title' => $response->result->chat->title
                ]]);
            } else {
                $session_data_integration['is_connected'] = 0;
                $session_data_integration['chat_id'] = $chatId;
                $session->set('telegram_integration', $session_data_integration);

                if (strpos($response->description, 'chat not found') > -1) {
                    $message = 'Kết nối thất bại, vui lòng kiểm tra lại ID nhóm Telegram, đảm bảo SePay Bot đã được thêm vào nhóm Telegram của bạn';
                } else if (strpos($response->description, 'message thread not found') > -1) {
                    $message = 'Kết nối thất bại, vui lòng kiểm tra lại ID topic';
                } else {
                    $message = 'Đã có lỗi xảy ra, vui lòng thử lại sau';
                }

                return $this->respond([
                    'status' => false, 
                    'message' => $message
                ]);
            }
        }

        return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ']);
    }
    
    public function ajax_index_telegram()
    {
        $storeId = $this->request->getGet('store_id');
        $type = $this->request->getGet('type');
        
        $storeFeature = new StoreFeature;
        $storeFeature->withCompanyContext($this->user_session['company_id']);
        
        $data = $storeFeature->getStoreTelegrams($storeId ? [$storeId] : [], $type);
        
        return $this->respond([
            'data' => $data
        ]);
    }
    
    public function ajax_sync_store_to_telegram(string $telegramId = '')
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }
        
        if (! has_permission('NotificationTelegram', 'can_edit')) {
            return $this->failNotFound();
        }
        
        $telegramId = trim($telegramId);
        
        if (!is_string($telegramId)) {
            return $this->failNotFound();
        }
        
        $storeFeature = new StoreFeature;
        $storeFeature->withCompanyContext($this->user_session['company_id']);
        
        $telegram = $storeFeature->telegramBuilder()->where('id', $telegramId)->first();
            
        if (!$telegram) {
            return $this->failNotFound();
        }
        
        $linkedStores = $storeFeature->getLinkedTelegramStores($telegram->chat_id, $telegram->message_thread_id, $telegram->transaction_type);
        
        if (!count($linkedStores)) {
            return $this->failNotFound();
        }
        
        $storeIds = $this->request->getVar('store_ids');

        if (!is_array($storeIds) || empty($storeIds)) {
            return $this->respond(['status' => false, 'message' => 'Vui lòng giữ lại ít nhất một cửa hàng']);
        }
        
        $linkableStoreIds = array_values(array_diff($storeIds, array_column($linkedStores, 'id')));
        $unlinkableStoreIds = array_values(array_diff(array_column($linkedStores, 'id'), $storeIds));
        
        $storeFeature = new StoreFeature;
        $storeFeature->withCompanyContext($this->user_session['company_id']);
       
        if (count($linkableStoreIds)) {
            $storeFeature->linkStoresToTelegram($linkableStoreIds, (array) $telegram);
        }
        
        if (count($unlinkableStoreIds)) {
            $storeFeature->unlinkStoresToTelegram($unlinkableStoreIds, $telegram->chat_id, $telegram->message_thread_id, $telegram->transaction_type);
        }
        
        if ($storeFeature->telegramBuilder()->where('id', $telegramId)->countAllResults() === 0) {
            $telegram = $storeFeature->telegramBuilder()
                ->where('chat_id', $telegram->chat_id)
                ->where('message_thread_id', $telegram->message_thread_id)
                ->where('transaction_type', $telegram->transaction_type)
                ->first();
                
            if (!$telegram) {
                return $this->respond([
                    'status' => false,
                    'message' => 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ'
                ]);
            }
            
            return $this->respond([
                'status' => true,
                'message' => 'Cập nhật thành thành công',
                'redirect_url' => base_url('telegram/edit/' . $telegram->id)
            ]);
        }
        
        return $this->respond([
            'status' => true,
            'message' => 'Cập nhật thành thành công',
        ]);
    }

    public function ajax_unlink_store_to_telegram(string $telegramId = '')
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }
        
        if (! has_permission('NotificationTelegram', 'can_edit')) {
            return $this->failNotFound();
        }
        
        $telegramId = trim($telegramId);
        
        if (!is_string($telegramId)) {
            return $this->failNotFound();
        }
        
        $storeId = $this->request->getVar('store_id');
        
        if (!$storeId || !is_numeric($storeId)) {
            return $this->failNotFound();
        }
        
        $storeFeature = new StoreFeature;
        $storeFeature->withCompanyContext($this->user_session['company_id']);
        
        $telegram = $storeFeature->telegramBuilder()->where('id', $telegramId)->first();
            
        if (!$telegram) {
            return $this->failNotFound();
        }
        
        $storeFeature->unlinkStoresToTelegram([$storeId], $telegram->chat_id, $telegram->message_thread_id, $telegram->transaction_type);
        
        return $this->respond([
            'status' => true,
            'message' => 'Đã gỡ tích hợp Telegram khỏi cửa hàng',
        ]);
    }

    public function ajax_link_store_to_telegrams(string $storeId = '')
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }
        
        if (! has_permission('NotificationTelegram', 'can_edit')) {
            return $this->failNotFound();
        }
        
        $storeId = trim($storeId);
        
        if (!$storeId || !is_numeric($storeId)) {
            return $this->failNotFound();
        }
        
        $telegramIds = $this->request->getVar('telegram_ids');
        
        if (!$telegramIds || !is_array($telegramIds) || empty($telegramIds)) {
            return $this->fail('Vui lòng chọn ít nhất một nhóm Telegram', 400);
        }
        
        $storeFeature = new StoreFeature;
        $storeFeature->withCompanyContext($this->user_session['company_id']);
        
        $linkedCount = 0;
        $errors = [];
        
        foreach ($telegramIds as $telegramId) {
            $telegram = $storeFeature->telegramBuilder()->where('id', $telegramId)->first();
                
            if (!$telegram) {
                $errors[] = "Không tìm thấy Telegram ID: {$telegramId}";
                continue;
            }
            
            try {
                $storeFeature->linkStoresToTelegram(
                    [$storeId], 
                    (array) $telegram
                );
                $linkedCount++;
            } catch (\Exception $e) {
                $errors[] = "Lỗi khi liên kết Telegram ID {$telegramId}: " . $e->getMessage();
            }
        }
        
        if ($linkedCount === 0) {
            return $this->fail('Không thể liên kết bất kỳ nhóm Telegram nào: ' . implode(', ', $errors), 400);
        }
        
        return $this->respond([
            'status' => true,
            'message' => "Đã liên kết {$linkedCount} nhóm Telegram với cửa hàng",
            'errors' => $errors
        ]);
    }
        
    public function ajax_delete(string $telegramId = '')
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }
        
        if (! has_permission('NotificationTelegram', 'can_edit')) {
            return $this->failNotFound();
        }
        
        $telegramId = trim($telegramId);
        
        if (!is_string($telegramId)) {
            return $this->failNotFound();
        }
        
        $storeFeature = new StoreFeature;
        $storeFeature->withCompanyContext($this->user_session['company_id']);
        
        $telegram = $storeFeature->telegramBuilder()->where('id', $telegramId)->first();
            
        if (!$telegram) {
            return $this->failNotFound();
        }
        
        $storeFeature->deleteStoreTelegram($telegram->chat_id, $telegram->message_thread_id, $telegram->transaction_type);
        
        add_user_log(['data_id' => 0, 'company_id' => $this->user_session['company_id'], 'data_type' => 'notification_telegram_delete', 'description' => 'Gỡ tích hợp thông báo Telegram', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);
        
        return $this->respond([
            'status' => true,
            'message' => 'Đã xóa tích hợp Telegram',
        ]);
    }
}
