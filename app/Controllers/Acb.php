<?php

namespace App\Controllers;

use Exception;
use App\Models\SimModel;
use App\Libraries\AcbClient;
use App\Models\ProductModel;
use App\Models\SimCompanyModel;
use App\Models\BankAccountModel;
use App\Models\TransactionsModel;
use CodeIgniter\API\ResponseTrait;
use App\Controllers\BaseController;
use App\Models\BankSubAccountModel;
use App\Models\CompanySubscriptionModel;
use App\Models\AcbEnterpriseAccountModel;
use App\Models\AcbBankAccountMetaDataModel;
use App\Exceptions\DisableBankClientException;
use App\Actions\BankAccount\DeleteCompanyBankAccountAction;
use App\Models\NotificationTelegramQueueModel;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use Config\Acb as Config;
use Psr\Log\LoggerInterface;

class Acb extends BaseController
{
    use ResponseTrait;

    protected $acbConfig;

    const BANK_ID = 3;

    public function __construct()
    {
        $this->acbConfig = config(Config::class);
    }

    public function initController(RequestInterface $request, ResponseInterface $response, LoggerInterface $logger)
    {
        parent::initController($request, $response, $logger);

        is_bank_supported_by_promotion(self::BANK_ID, $this->company_details->company_id);
    }

    protected function handleAcbClientException($e, $client = null)
    {
        log_message('error', 'ACB Controller Error: ' . $e->getMessage() . ' - ' . $e->getTraceAsString());

        if ($client) $client->forceDebug();

        if (in_array($e->getCode(), ['2010009']))
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng của bạn đã bị khóa, vui lòng liên hệ ngân hàng.']);

        if (strpos($e->getMessage(), 'timed out') !== false)
            return $this->response->setJSON(['status' => false, 'message' => 'Hệ thống ngân hàng ACB đang bận, vui lòng thử lại sau.']);

        if ($e instanceof DisableBankClientException)
            return $this->response->setJSON(['status' => false, 'message' => 'Hệ thống ngân hàng ACB đang bảo trì, vui lòng liên hệ SePay để biết thêm thông tin chi tiết.']);

        return $this->response->setJSON(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng thử lại sau hoặc liên hệ SePay để được hỗ trợ.']);
    }

    public function step1()
    {
        $data = [
            'page_title' => 'Ngân hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'allowed_switch_api_connection' => $this->acbConfig->allowedSwitchApiConnection,
            'is_enterprise' => $this->request->getGet('is_enterprise') == '1'
        ];

        if (!has_permission('BankAccount', 'can_add'))
            return show_404();

        $session = service('session');
        $bankAccountId = $session->get('acb_request_switch_api_connection');
        $data['bank_account_details'] = null;

        if ($this->acbConfig->allowedSwitchApiConnection && $bankAccountId) {
            $data['bank_account_details'] = $this->getSwitchableApiConnectionBankAccount($bankAccountId);
        }

        $session->remove('acb_request_switch_api_connection');

        echo view('templates/autopay/header', $data);
        echo view('acb/step1', $data);
        echo view('templates/autopay/footer', $data);
    }

    public function ajax_step1()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!has_permission('BankAccount', 'can_add'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền thêm tài khoản ngân hàng']);

        $session = service('session');

        $data = [
            'id' => trim(xss_clean($this->request->getVar('id'))),
            'account_number' => trim(xss_clean($this->request->getVar('account_number'))),
            'account_holder_name' => trim(xss_clean($this->request->getVar('account_holder_name'))),
            'phone_number' => trim(xss_clean($this->request->getVar('phone_number'))),
            'username' => trim(xss_clean($this->request->getVar('username'))),
            'label' => trim(xss_clean($this->request->getVar('label'))),
            'is_enterprise' => (bool) $this->request->getVar('is_enterprise'),
            'user_agreement' => (bool) $this->request->getVar('user_agreement'),
        ];

        if (!$data['user_agreement'])
            return $this->fail(['user_agreement' => 'Bạn cần đồng ý chính sách và điều khoản trước để tiếp tục.']);

        $rules = [
            'account_number' => [
                'label' => 'số tài khoản',
                'rules' => ['required', 'max_length[100]'],
            ],
            'account_holder_name' => [
                'label' => 'tên chủ tài khoản',
                'rules' => ['required', 'max_length[100]'],
            ],
            'phone_number' => [
                'label' => 'số điện thoại',
                'rules' => ['required', 'min_length[10]', 'max_length[20]']
            ],
            'username' => [
                'label' => 'tên đăng nhập ACBO',
                'rules' => $data['is_enterprise'] ? ['required', 'max_length[100]'] : ['permit_empty']
            ],
            'label' => [
                'label' => 'tên gợi nhớ',
                'rules' => ['permit_empty', 'max_length[100]']
            ],
        ];

        if (! $this->validateData($data, $rules)) return $this->fail($this->validator->getErrors());

        $bankAccountModel = model(BankAccountModel::class);
        $bankAccountDetails = $data['id'] ? $this->getSwitchableApiConnectionBankAccount($data['id']) : null;

        // Skip validation account number exist when switch bank account to api conenction
        if ($this->acbConfig->allowedSwitchApiConnection && $bankAccountDetails) {
            $data['account_number'] = $bankAccountDetails->account_number;
        } else {
            if ($bankAccountDetails = $bankAccountModel->where(['account_number' => $data['account_number'], 'bank_id' => Acb::BANK_ID, 'company_id' => $this->user_session['company_id'], 'bank_sms' => 1])->get()->getRow()) {
                return $this->fail(['account_number' => 'Tài khoản này của bạn đang sử dụng phương thức kết nối SMS Banking', 'bank_account_id' => $bankAccountDetails->id]);
            }

            if ($bankAccountModel->where(['account_number' => $data['account_number'], 'bank_id' => Acb::BANK_ID])->countAllResults()) {
                return $this->fail(['account_number' => 'Số tài khoản này đã tồn tại trên hệ thống', 'owner' => false]);
            }
        }

        try {
            $client = new AcbClient;
        } catch (Exception $e) {
            return $this->handleAcbClientException($e);
        }

        try {
            $virtualAccountInfo = null;

            $response = $client->registerPushNotification(
                $data['is_enterprise'] ? 'ORG' : 'PERS',
                $data['account_number'],
                $data['phone_number'],
                'ALL',
                'NONE',
                true,
                $virtualAccountInfo,
                $data['username']
            );

            $responseData = json_decode($response->getBody());
            $errorCode = $responseData->responseStatus->responseCode;

            if (in_array($errorCode, ['********']) && !$bankAccountDetails) {
                $simModel = model(SimModel::class);
                $simCompanyModel = model(SimCompanyModel::class);

                $safeBankAccountData = array(
                    'company_id' => $this->user_session['company_id'],
                    'account_holder_name' => $data['account_holder_name'],
                    'account_number' => $data['account_number'],
                    'bank_id' => Acb::BANK_ID,
                    'label' => $data['label'],
                    'active' => 1,
                    'bank_api' => 1,
                    'bank_api_connected' => 0,
                    'phone_number' => $data['phone_number']
                );

                $sims = $simCompanyModel
                    ->select('tb_autopay_sim.id, tb_autopay_sim.sim_phonenumber')
                    ->join('tb_autopay_sim', 'tb_autopay_sim.id=tb_autopay_sim_company.sim_id')
                    ->where([
                        'tb_autopay_sim_company.company_id' => $this->user_session['company_id'],
                        'tb_autopay_sim.active' => 1
                    ])
                    ->orderBy('tb_autopay_sim_company.created_at', 'ASC')
                    ->get()->getResult();

                if (count($sims) > 0) {
                    $safeBankAccountData['sim_id'] = $sims[0]->id;
                }

                $bankAccountId = $bankAccountModel->insert($safeBankAccountData);

                if ($bankAccountId) {
                    if ($data['is_enterprise']) {
                        model(AcbEnterpriseAccountModel::class)->insert(['bank_account_id' => $bankAccountId, 'username' => $data['username']]);
                    }

                    $acbBankAccountMetaDataModel = model(AcbBankAccountMetaDataModel::class);
                    $acbBankAccountMetaDataModel->insert([
                        'realtime_transfer_type' => 'NONE',
                        'bank_account_id' => $bankAccountId
                    ]);

                    add_user_log(['data_id' => $bankAccountId, 'company_id' => $this->user_session['company_id'], 'data_type' => 'bank_account_add', 'description' => 'Thêm tài khoản ngân hàng ACB API', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);

                    $session->set('acb_step_1_otp_request', [
                        'request_id' => $responseData->responseData->requestId,
                        'authorization_id' => $responseData->responseData->authorizationId,
                    ]);

                    set_alert('success', $this->getSentOtpAlert($responseData->responseData->authorizationType));

                    return $this->response->setJSON(['status' => true, 'id' => $bankAccountId]);
                }

                add_user_log(['data_id' => 0, 'company_id' => $this->user_session['company_id'], 'data_type' => 'bank_account_add', 'description' => 'Thêm tài khoản ngân hàng ACB API', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Failed']);
                return $this->response->setJSON(['status' => false, 'message' => 'Không thể thêm tài khoản ngân hàng. Vui lòng liên hệ SePay để được hỗ trợ.']);
            }

            // Switch ACB bank account to API connection
            if (in_array($errorCode, ['********']) && $bankAccountDetails) {
                $bankAccountUpdated = $bankAccountModel->set([
                    'account_holder_name' => $data['account_holder_name'],
                    'phone_number' => $data['phone_number'],
                ])->where(['id' => $bankAccountDetails->id])->update();

                $acbEnterpriseAccountModel = model(AcbEnterpriseAccountModel::class);
                $acbBankAccountMetaDataModel = model(AcbBankAccountMetaDataModel::class);


                if ($bankAccountUpdated) {
                    $acbEnterpriseAccountDetails = $acbEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->first();
                    $acbBankAccountMetaDataDetails = $acbBankAccountMetaDataModel->where(['bank_account_id' => $bankAccountDetails->id])->first();

                    if ($data['is_enterprise'] && !$acbEnterpriseAccountDetails) {
                        $acbEnterpriseAccountModel->insert(['bank_account_id' => $bankAccountDetails->id, 'username' => $data['username']]);
                    }

                    if ($data['is_enterprise'] && $acbEnterpriseAccountDetails) {
                        $acbEnterpriseAccountModel->where(['id' => $acbEnterpriseAccountDetails->id])->set(['username' => $data['username']])->update();
                    }

                    if (!$acbBankAccountMetaDataDetails) {
                        $acbBankAccountMetaDataModel->insert([
                            'realtime_transfer_type' => 'NONE',
                            'bank_account_id' => $bankAccountDetails->id
                        ]);
                    }

                    add_user_log(['data_id' => $bankAccountDetails->id, 'company_id' => $this->user_session['company_id'], 'data_type' => 'bank_account_add', 'description' => 'Chuyển phương thức kết nối sang API', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);

                    $session->set('acb_step_1_otp_request', [
                        'request_id' => $responseData->responseData->requestId,
                        'authorization_id' => $responseData->responseData->authorizationId,
                    ]);

                    set_alert('success', $this->getSentOtpAlert($responseData->responseData->authorizationType));

                    return $this->response->setJSON(['status' => true, 'id' => $bankAccountDetails->id]);
                }

                add_user_log(['data_id' => 0, 'company_id' => $this->user_session['company_id'], 'data_type' => 'bank_account_add', 'description' => 'Chuyển phương thức kết nối sang API', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Failed']);
                return $this->response->setJSON(['status' => false, 'message' => 'Không thể chuyển sang phương thức kết nối API, vui lòng liên hệ SePay để được hỗ trợ.']);
            }
        } catch (Exception $e) {
            $errorCode = $e->getCode();

            if ($errorCode == '2010008')
                return $this->fail(['account_number' => 'Số tài khoản không tồn tại hoặc đã bị khóa trên hệ thống ngân hàng ACB.']);

            if (in_array($errorCode, ['2010005', '1020002']))
                return $this->fail(['phone_number' => 'Số điện thoại không được đăng ký cho tài khoản trên, vui lòng liên hệ SePay để được hỗ trợ.']);

            if (in_array($errorCode, ['********']))
                return $this->respond(['status' => false, 'message' => 'Quý khách vui lòng gửi lại yêu cầu OTP sau 2 phút nữa.']);

            if (in_array($errorCode, ['********'])) {
                if ($data['is_enterprise']) {
                    return $this->fail(['username' => 'Tên đăng nhập ACBO không được đăng ký cho tài khoản trên.']);
                } else {
                    return $this->fail(['account_number' => 'Số tài khoản này không thuộc nhóm khách hàng cá nhân.']);
                }
            }


            return $this->handleAcbClientException($e);
        }
    }

    public function step2($id = '')
    {
        $data = [
            'page_title' => 'Ngân hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
        ];

        if (!has_permission('BankAccount', 'can_add'))
            show_404();

        if (!is_numeric($id))
            show_404();

        $bankAccountModel = model(BankAccountModel::class);
        $bankAccountDetails = $this->getBankAccountDetails($id);

        if (!is_object($bankAccountDetails))
            show_404();

        $data['bank_account_details'] = $bankAccountDetails;

        $session = session();
        $data['otp_request'] = $session->get('acb_step_1_otp_request');

        if ($session->get('acb_skip_step_2')) {
            $bankAccountModel->set([
                'bank_api_connected' => 1,
                'bank_api' => 1,
                'bank_sms' => 0,
                'bank_sms_connected' => 0,
            ])->update($bankAccountDetails->id);

            set_alert('success', 'Tài khoản đã liên kết ngân hàng trước đó!');
            $session->remove('acb_skip_step_2');

            return redirect()->to('acb/settings/' . $id);
        }

        $transactionsModel = slavable_model(TransactionsModel::class, 'Acb');
        $data['count_transactions'] = $transactionsModel->where([
            'id' => $bankAccountDetails->id,
            'deleted_at' => null
        ])->countAllResults();

        if ($bankAccountDetails->bank_api_connected == 1) {
            set_alert('error', 'Tài khoản này đã mở API rồi. Bạn không cần phải liên kết lại.');
            return redirect()->to('acb/settings/' . $id);
        }

        if ($bankAccountDetails->bank_sms == 1 && !$data['otp_request']) {
            set_alert('error', 'Tài khoản này đang kết nối bằng phương thức SMS Banking.');
            return redirect()->to('bankaccount/details/' . $id);
        }

        $session->remove('acb_step_1_otp_request');

        echo view('templates/autopay/header', $data);
        echo view('acb/step2', $data);
        echo view('templates/autopay/footer', $data);
    }

    public function ajax_step2()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!has_permission('BankAccount', 'can_add'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn chưa được cấp quyền để thực hiện thao tác này']);

        $bankAccountModel = model(BankAccountmodel::class);
        $bankAccountId = trim(xss_clean($this->request->getPost('id')));
        $session = session();

        $bankAccountDetails = $bankAccountModel->where([
            'id' => $bankAccountId,
            'company_id' => $this->user_session['company_id'],
            'bank_id' => Acb::BANK_ID
        ])->get()->getRow();

        if (!is_object($bankAccountDetails))
            return $this->response->setJSON(['status' => false, 'message' => 'Không tìm thấy tài khoản ngân hàng này']);

        $data = [
            'otp' => trim(xss_clean($this->request->getVar('otp'))),
            'request_id' => trim($this->request->getVar('request_id')),
            'authorization_id' => trim($this->request->getVar('authorization_id')),
        ];

        $rules = [
            'otp' => 'required',
            'authorization_id' => 'required',
            'request_id' => 'required'
        ];

        if (! $this->validateData($data, $rules)) return $this->fail($this->validator->getErrors());

        try {
            $client = new AcbClient;
        } catch (Exception $e) {
            return $this->handleAcbClientException($e);
        }

        try {
            try {
                $response = $client->verifyOtpNotification($data['request_id'], $data['authorization_id'], $data['otp']);
                $responseData = json_decode($response->getBody());
            } catch (Exception $e) {
                if ($e->getCode() != '********') throw $e;

                $responseData = null;
            }
            $isSwitchToApiConnection = $bankAccountDetails->bank_sms == 1;

            $bankAccountModel->set([
                'bank_api_connected' => 1,
                'bank_api' => 1,
                'bank_sms' => 0,
                'bank_sms_connected' => 0,
            ])->update($bankAccountId);

            $acbBankAccountMetaDataModel = model(AcbBankAccountMetaDataModel::class);
            $acbBankAccountMetaDataModel->where(['bank_account_id' => $bankAccountId])->set(['realtime_transfer_type' => 'ALL'])->update();

            if ($isSwitchToApiConnection) {
                add_user_log(['data_id' => $bankAccountId, 'company_id' => $this->user_session['company_id'], 'data_type' => 'bank_account_linked', 'description' => 'Chuyển đổi phương thức kết nối sang API Banking', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);
            } else {
                add_user_log(['data_id' => $bankAccountId, 'company_id' => $this->user_session['company_id'], 'data_type' => 'bank_account_linked', 'description' => 'Liên kết tài khoản ngân hàng', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);
            }

            $bankSubAccountConfig = get_configuration('BankSubAccount');

            if ($bankSubAccountConfig == 'off') {
                $configurationModel = model(ConfigurationModel::class);
                $configurationModel->set([
                    'value' => 'on',
                ])->where([
                    'company_id' => $this->user_session['company_id'],
                    'setting' => 'BankSubAccount'
                ])->update();
            }

            set_alert('success', 'Liên kết ngân hàng thành công!');

            return $this->response->setJSON(['status' => true]);
        } catch (Exception $e) {
            $errorCode = $e->getCode();

            if (in_array($errorCode, ['********', '********'])) {
                return $this->fail(['otp' => 'OTP không chính xác hoặc đã hết hiệu lực.']);
            }

            return $this->handleAcbClientException($e, $client);
        }
    }

    public function ajax_resend_otp()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!has_permission('BankAccount', 'can_add'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn chưa được cấp quyền để thực hiện thao tác này']);

        $bankAccountModel = model(BankAccountmodel::class);
        $acbEnterpriseAccountModel = model(AcbEnterpriseAccountModel::class);

        $bankAccountId = trim(xss_clean($this->request->getPost('id')));

        $bankAccountDetails = $bankAccountModel->where([
            'id' => $bankAccountId,
            'company_id' => $this->user_session['company_id'],
            'bank_id' => Acb::BANK_ID
        ])->get()->getRow();

        if (!is_object($bankAccountDetails))
            return $this->response->setJSON(['status' => false, 'message' => 'Không tìm thấy tài khoản ngân hàng này']);

        $acbEnterpriseAccountDetails = $acbEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->first();

        try {
            $client = new AcbClient;
        } catch (Exception $e) {
            return $this->handleVietinbankClientException($e);
        }

        try {
            $virtualAccountInfo = null;

            $response = $client->registerPushNotification(
                is_object($acbEnterpriseAccountDetails) ? 'ORG' : 'PERS',
                $bankAccountDetails->account_number,
                $bankAccountDetails->phone_number,
                'ALL',
                'NONE',
                true,
                $virtualAccountInfo,
                is_object($acbEnterpriseAccountDetails) ? $acbEnterpriseAccountDetails->username : null,
            );

            $responseData = json_decode($response->getBody());

            return $this->response->setJSON([
                'status' => true,
                'message' => $this->getSentOtpAlert($responseData->responseData->authorizationType),
                'otp_request' => [
                    'request_id' => $responseData->responseData->requestId,
                    'authorization_id' => $responseData->responseData->authorizationId,
                    'authorization_type' => $responseData->responseData->authorizationType,
                ]
            ]);
        } catch (Exception $e) {
            $errorCode = $e->getCode();

            if (in_array($errorCode, ['********']))
                return $this->respond(['status' => false, 'message' => 'Quý khách vui lòng gửi lại yêu cầu OTP sau 2 phút nữa.']);

            return $this->handleAcbClientException($e);
        }
    }

    public function ajax_unlink($id = '')
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!has_permission('BankAccount', 'can_add'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn chưa được cấp quyền để thực hiện thao tác này']);

        $bankAccountModel = model(BankAccountmodel::class);
        $bankAccountId = trim(xss_clean($this->request->getPost('id')));

        $bankAccountDetails = $bankAccountModel->where([
            'id' => $bankAccountId,
            'company_id' => $this->user_session['company_id'],
            'bank_id' => Acb::BANK_ID
        ])->get()->getRow();

        if (!is_object($bankAccountDetails))
            return $this->response->setJSON(['status' => false, 'message' => 'Không tìm thấy tài khoản ngân hàng này']);

        DeleteCompanyBankAccountAction::run($bankAccountDetails, $this->company_details, $this->request->getIPAddress(), self::class);

        $acbEnterpriseAccountModel = model(AcbEnterpriseAccountModel::class);
        $acbEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->delete();

        set_alert('success', 'Đã xóa tài khoản ngân hàng.');

        return $this->response->setJSON([
            'status' => true,
        ]);
    }

    public function step3($id = '')
    {
        $data = [
            'page_title' => 'Ngân hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
        ];

        if (!has_permission('BankAccount', 'can_add'))
            show_404();

        if (!is_numeric($id))
            show_404();

        $bankAccountModel = model(BankAccountModel::class);
        $bankAccountDetails = $bankAccountModel
            ->select('tb_autopay_bank_account.id, tb_autopay_bank_account.account_number, tb_autopay_bank_account.label, tb_autopay_bank_account.active, tb_autopay_bank_account.created_at, tb_autopay_bank.full_name, tb_autopay_bank.brand_name, tb_autopay_bank_account.account_number, tb_autopay_bank_account.account_holder_name, tb_autopay_bank.icon_path, tb_autopay_bank.logo_path, tb_autopay_bank_account.last_transaction, tb_autopay_bank_account.accumulated,tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.bank_id,tb_autopay_bank_account.bank_sms, tb_autopay_bank_account.bank_sms_connected, tb_autopay_bank_account.bank_api, tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.identification_number, tb_autopay_bank_account.phone_number, tb_autopay_bank_account.sim_id')
            ->join('tb_autopay_bank', 'tb_autopay_bank.id=tb_autopay_bank_account.bank_id')
            ->where([
                'tb_autopay_bank_account.company_id' => $this->user_session['company_id'],
                'tb_autopay_bank_account.id' => $id,
                'tb_autopay_bank_account.bank_id' => Acb::BANK_ID
            ])->get()->getRow();

        if (!$bankAccountDetails)
            show_404();

        $data['bank_account_details'] = $bankAccountDetails;

        echo view('templates/autopay/header', $data);
        echo view('acb/step3', $data);
        echo view('templates/autopay/footer', $data);
    }

    public function settings($id)
    {
        $data = [
            'page_title' => 'Cấu hình',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
        ];

        if (! has_permission('BankAccount', 'can_add')) {
            show_404();
        }

        if (! is_numeric($id)) {
            show_404();
        }

        $bankAccount = model(BankAccountModel::class)
            ->select('tb_autopay_bank_account.id, tb_autopay_bank_account.account_number, tb_autopay_bank_account.label, tb_autopay_bank_account.active, tb_autopay_bank_account.created_at, tb_autopay_bank.full_name, tb_autopay_bank.brand_name, tb_autopay_bank_account.account_number, tb_autopay_bank_account.account_holder_name, tb_autopay_bank.icon_path, tb_autopay_bank.logo_path, tb_autopay_bank_account.last_transaction, tb_autopay_bank_account.accumulated,tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.bank_id,tb_autopay_bank_account.bank_sms, tb_autopay_bank_account.bank_sms_connected, tb_autopay_bank_account.bank_api, tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.identification_number, tb_autopay_bank_account.phone_number, tb_autopay_bank_account.sim_id')
            ->join('tb_autopay_bank', 'tb_autopay_bank.id=tb_autopay_bank_account.bank_id')
            ->where('tb_autopay_bank_account.company_id', $this->user_session['company_id'])
            ->where('tb_autopay_bank_account.id', $id)
            ->where('tb_autopay_bank_account.bank_id', Acb::BANK_ID)
            ->first();

        if (! $bankAccount) {
            show_404();
        }

        $data['bankAccount'] = $bankAccount;
        $data['settings'] = bank_account_settings($bankAccount->id);

        echo view('templates/autopay/header', $data);
        echo view('acb/settings', $data);
        echo view('templates/autopay/footer', $data);
    }

    public function ajax_check_trans()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!has_permission('BankAccount', 'can_add'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền thực hiện thao tác này']);

        $bankAccountId = trim(xss_clean($this->request->getVar('bank_account_id')));

        if (!is_numeric($bankAccountId))
            show_404();

        $bankAccountModel = slavable_model(BankAccountModel::class, 'Acb');
        $bankAccountDetails = $bankAccountModel
            ->select('tb_autopay_bank_account.id, tb_autopay_bank_account.account_number, tb_autopay_bank_account.label, tb_autopay_bank_account.active, tb_autopay_bank_account.created_at, tb_autopay_bank.brand_name, tb_autopay_bank_account.account_holder_name, tb_autopay_bank.icon_path, tb_autopay_bank.logo_path, tb_autopay_bank_account.last_transaction, tb_autopay_bank_account.accumulated, tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.bank_id')
            ->join('tb_autopay_bank', 'tb_autopay_bank.id=tb_autopay_bank_account.bank_id')
            ->where([
                'tb_autopay_bank_account.company_id' => $this->user_session['company_id'],
                'tb_autopay_bank_account.id' => $bankAccountId,
                'tb_autopay_bank_account.bank_id' => Acb::BANK_ID
            ])
            ->get()->getRow();

        if (!is_object($bankAccountDetails))
            show_404();

        $session = session();

        if (!$session->get('checking_transaction_date'))
            $session->set('checking_transaction_date', date('Y-m-d H:i:s'));

        $checkingTransactionDate = $session->get('checking_transaction_date');
        $transactionsModel = slavable_model(TransactionsModel::class, 'Acb');
        $lastTransaction = $transactionsModel
            ->select('id, amount_in, account_number, sub_account, transaction_content, transaction_date')
            ->where([
                'deleted_at' => null,
                'parser_status' => 'Success',
                'bank_account_id' => $bankAccountId,
                'source' => 'BankAPINotify',
                'transaction_date >= ' => $checkingTransactionDate
            ])
            ->orderBy('id', 'DESC')
            ->get()->getRow();

        if (!is_object($lastTransaction))
            return $this->response->setJSON(['status' => false]);

        $session->remove('checking_transaction_date');

        return $this->response->setJSON(['status' => true, 'last_transaction' => $lastTransaction]);
    }

    public function details($id = '')
    {
        $data = [
            'page_title' => 'Ngân hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
            'allowed_delete_bank_account' => $this->acbConfig->allowedDeleteBankAccount ?? false,
            'allowed_switch_sms_connection' => $this->acbConfig->allowedSwitchSmsConnection ?? false,
            'allowed_switch_api_connection' => $this->acbConfig->allowedSwitchApiConnection ?? false,
            'allowed_enterprise_connection' => $this->acbConfig->allowedEnterpriseConnection ?? true,
            'allowed_modify_primary_account_push_notification' => $this->acbConfig->allowedModifyPrimaryAccountPushNotification ?? false,
            'enterprise_api_connection_manually' => $this->acbConfig->enterpriseApiConnectionManually ?? true
        ];

        if (!has_permission('BankAccount', 'can_view_all'))
            show_404();

        if (!is_numeric($id))
            show_404();

        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $acbEnterpriseAccountModel = model(AcbEnterpriseAccountModel::class);
        $transactionsModel = slavable_model(TransactionsModel::class, 'Acb');
        $simCompanyModel = model(SimCompanyModel::class);

        $bankAccountDetails = $this->getBankAccountDetails($id);

        if (!is_object($bankAccountDetails))
            show_404();

        if ($bankAccountDetails->bank_api_connected == 0 && $bankAccountDetails->bank_api == 1)
            return redirect()->to(base_url('acb/step2/' . $bankAccountDetails->id));

        $data['bank_account_details'] = $bankAccountDetails;

        $data['count_subs'] = $bankSubAccountModel->where(['bank_account_id' => $id, 'deleted_at' => null])->countAllResults();

        $data['bank_sms_info'] = get_bank_sms_info($data['bank_account_details']->brand_name);

        $data['count_transactions'] = $transactionsModel->where(['bank_account_id' => $data['bank_account_details']->id, 'deleted_at' => NULL])->countAllResults();

        $data['count_sms_transactions'] = $transactionsModel->where(['bank_account_id' => $data['bank_account_details']->id, 'deleted_at' => NULL, 'source' => 'SMS'])->countAllResults();

        $data['sims'] = $simCompanyModel
            ->select('tb_autopay_sim.id, tb_autopay_sim.sim_phonenumber')
            ->join('tb_autopay_sim', 'tb_autopay_sim.id=tb_autopay_sim_company.sim_id')
            ->where(['tb_autopay_sim_company.company_id' => $this->user_session['company_id'], 'tb_autopay_sim.active' => 1])
            ->orderBy('tb_autopay_sim_company.created_at', 'DESC')
            ->get()->getResult();

        $data['acb_enterprise_account_details'] = $acbEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->first();

        // data QR
        $data['bank_sub_accounts_custom'] = [];
        if (!empty($data['bank_account_details']->id)) {
            $data['bank_sub_accounts_custom'] = $bankSubAccountModel->select("tb_autopay_bank_sub_account.id,tb_autopay_bank_sub_account.acc_type,tb_autopay_bank.brand_name,tb_autopay_bank.icon_path, tb_autopay_bank_account.account_holder_name, tb_autopay_bank_account.account_number, tb_autopay_bank_sub_account.sub_account, tb_autopay_bank_sub_account.label")
                ->join("tb_autopay_bank_account", "tb_autopay_bank_account.id = tb_autopay_bank_sub_account.bank_account_id")
                ->join("tb_autopay_bank", "tb_autopay_bank.id = tb_autopay_bank_account.bank_id", "left")
                ->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id']])
                ->where('tb_autopay_bank_account.id', $data['bank_account_details']->id)
                ->get()
                ->getResultArray();
        }

        if ($data['acb_enterprise_account_details']) {
            $data['vac'] = $data['acb_enterprise_account_details']->vac;
            $data['va_minlength'] = $this->acbConfig->enterpriseVaMinlength ?? 4;
            $data['va_maxlength'] = $this->acbConfig->enterpriseVaMaxlength ?? 18;
        }

        echo view('templates/autopay/header', $data);
        echo view($data['bank_account_details']->bank_sms
            ? 'acb/sms/details'
            : (is_object($data['acb_enterprise_account_details']) ? 'acb/enterprise/details' : 'acb/individual/details'), $data);
        echo view('templates/autopay/footer', $data);
    }

    public function ajax_request_register_primary_account_push_notification()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!$this->acbConfig->allowedModifyPrimaryAccountPushNotification)
            return $this->failNotFound();

        if (!has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền sửa tài khoản ngân hàng']);

        $data = [
            'bank_account_id' => trim(xss_clean($this->request->getVar('bank_account_id'))),
            'realtime_transfer_type' => trim(xss_clean($this->request->getVar('realtime_transfer_type')))
        ];

        $rules = [
            'bank_account_id' => ['required'],
            'realtime_transfer_type' => ['required', 'in_list[ALL,CREDIT,DEBIT]']
        ];

        if (! $this->validateData($data, $rules))
            return $this->fail($this->validator->getErrors());

        $bankAccountModel = model(BankAccountmodel::class);
        $acbEnterpriseAccountModel = model(AcbEnterpriseAccountModel::class);

        $bankAccountDetails = $this->getBankAccountDetails($data['bank_account_id']);

        if (! $bankAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng không tồn tại']);

        if ($bankAccountDetails->realtime_transfer_type !== 'NONE')
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng đã đăng ký nhận biến động số dư trước đó']);

        $acbEnterpriseAccountDetails = $acbEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->first();

        try {
            $client = new AcbClient;
        } catch (Exception $e) {
            return $this->handleAcbClientException($e);
        }

        try {
            $virtualAccountInfo = null;

            $response = $client->registerPushNotification(
                is_object($acbEnterpriseAccountDetails) ? 'ORG' : 'PERS',
                $bankAccountDetails->account_number,
                $bankAccountDetails->phone_number,
                $data['realtime_transfer_type'],
                'NONE',
                true,
                $virtualAccountInfo,
                is_object($acbEnterpriseAccountDetails) ? $acbEnterpriseAccountDetails->username : null
            );

            $responseData = json_decode($response->getBody());

            return $this->response->setJSON([
                'status' => true,
                'message' => $this->getSentOtpAlert($responseData->responseData->authorizationType),
                'otp_request' => [
                    'request_id' => $responseData->responseData->requestId,
                    'authorization_id' => $responseData->responseData->authorizationId,
                ]
            ]);
        } catch (Exception $e) {
            $errorCode = $e->getCode();

            if ($errorCode == '2010008')
                return $this->respond(['status' => false, 'message' => 'Số tài khoản không tồn tại hoặc đã bị khóa trên hệ thống ngân hàng ACB.']);

            if (in_array($errorCode, ['2010005', '1020002']))
                return $this->respond(['status' => false, 'message' => 'Số điện thoại không được đăng ký cho tài khoản trên, vui lòng liên hệ SePay để được hỗ trợ.']);

            if (in_array($errorCode, ['********']))
                return $this->respond(['status' => false, 'message' => "Quý khách vui lòng thử lại sau ít phút nữa"]);

            if (in_array($errorCode, ['********']))
                return $this->respond(['status' => false, 'message' => 'Tên đăng nhập ACBO không được đăng ký cho tài khoản trên.']);

            return $this->handleAcbClientException($e);
        }
    }

    public function ajax_confirm_register_primary_account_push_notification()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!$this->acbConfig->allowedModifyPrimaryAccountPushNotification)
            return $this->failNotFound();

        if (!has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền sửa tài khoản ngân hàng']);

        $data = [
            'bank_account_id' => trim(xss_clean($this->request->getVar('bank_account_id'))),
            'request_id' => trim($this->request->getVar('request_id')),
            'authorization_id' => trim($this->request->getVar('authorization_id')),
            'otp' => trim($this->request->getVar('otp')),
        ];

        $rules = [
            'bank_account_id' => ['required'],
            'request_id' => ['required'],
            'authorization_id' => ['required'],
            'otp' => ['required'],
        ];

        if (! $this->validateData($data, $rules))
            return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng tải lại trang']);

        $bankAccountModel = model(BankAccountmodel::class);
        $acbBankAccountMetaDataModel = model(AcbBankAccountMetaDataModel::class);

        $bankAccountDetails = $this->getBankAccountDetails($data['bank_account_id']);

        if (! $bankAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng không tồn tại']);

        if ($bankAccountDetails->realtime_transfer_type !== 'NONE')
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng đã đăng ký nhận biến động số dư trước đó']);

        try {
            $client = new AcbClient;
        } catch (Exception $e) {
            return $this->handleAcbClientException($e);
        }

        try {
            $virtualAccountInfo = null;

            $response = $client->verifyOtpNotification(
                $data['request_id'],
                $data['authorization_id'],
                $data['otp']
            );

            $responseData = json_decode($response->getBody());

            $response = $client->getPushNotificationRegistrationDetails($bankAccountDetails->account_number);

            $responseData = json_decode($response->getBody());

            $acbBankAccountMetaDataModel->where('bank_account_id', $bankAccountDetails->id)->set(['realtime_transfer_type' => $responseData->responseData->creditOrDebitRealTime])->update();

            set_alert('success', 'Đăng ký thành công');

            return $this->response->setJSON(['status' => true]);
        } catch (Exception $e) {
            $errorCode = $e->getCode();

            if (in_array($errorCode, ['********', '********'])) {
                return $this->fail(['otp' => 'OTP không chính xác hoặc đã hết hiệu lực.']);
            }

            return $this->handleAcbClientException($e);
        }
    }

    public function ajax_request_modify_primary_account_push_notification()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!$this->acbConfig->allowedModifyPrimaryAccountPushNotification)
            return $this->failNotFound();

        if (!has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền sửa tài khoản ngân hàng']);

        $data = [
            'bank_account_id' => trim(xss_clean($this->request->getVar('bank_account_id'))),
            'realtime_transfer_type' => trim(xss_clean($this->request->getVar('realtime_transfer_type')))
        ];

        $rules = [
            'bank_account_id' => ['required'],
            'realtime_transfer_type' => ['required', 'in_list[ALL,CREDIT,DEBIT]']
        ];

        if (! $this->validateData($data, $rules))
            return $this->fail($this->validator->getErrors());

        $bankAccountModel = model(BankAccountmodel::class);
        $acbEnterpriseAccountModel = model(AcbEnterpriseAccountModel::class);

        $bankAccountDetails = $bankAccountModel->where([
            'id' => $data['bank_account_id'],
            'bank_id' => Acb::BANK_ID,
            'company_id' => $this->user_session['company_id']
        ])->first();

        if (! $bankAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng không tồn tại']);

        $acbEnterpriseAccountDetails = $acbEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->first();

        try {
            $client = new AcbClient;
        } catch (Exception $e) {
            return $this->handleAcbClientException($e);
        }

        try {
            $virtualAccountInfo = null;

            $response = $client->modifyPushNotification(
                is_object($acbEnterpriseAccountDetails) ? 'ORG' : 'PERS',
                $bankAccountDetails->account_number,
                $bankAccountDetails->phone_number,
                [
                    'creditOrDebitRealTime' => $data['realtime_transfer_type'],
                    'creditOrDebitPastDay' => 'NONE'
                ],
                $virtualAccountInfo,
                is_object($acbEnterpriseAccountDetails) ? $acbEnterpriseAccountDetails->username : null
            );

            $responseData = json_decode($response->getBody());

            // SIMULATE
            // $responseData = (object) json_decode(json_encode([
            //     "requestTrace" => "70fbebb9-41bf-4c97-b6a1-cca3f1069458",
            //     "responseDateTime" => "2024-08-21T16:55:36.250+0700",
            //     "responseStatus" => [
            //         "responseCode" => "********",
            //         "responseMessage" => "Success"
            //     ],
            //     "responseData" => [
            //         "requestId" => "70fbebb9-41bf-4c97-b6a1-cca3f1069458",
            //         "authorizationId" => "*********",
            //         "authorizationType" => "SMS",
            //         "code" => "1234567"
            //     ]
            // ]));
            // SIMULATE

            return $this->response->setJSON([
                'status' => true,
                'message' => $this->getSentOtpAlert($responseData->responseData->authorizationType),
                'otp_request' => [
                    'request_id' => $responseData->responseData->requestId,
                    'authorization_id' => $responseData->responseData->authorizationId,
                ]
            ]);
        } catch (Exception $e) {
            $errorCode = $e->getCode();

            if ($errorCode == '2010008')
                return $this->respond(['status' => false, 'message' => 'Số tài khoản không tồn tại hoặc đã bị khóa trên hệ thống ngân hàng ACB.']);

            if (in_array($errorCode, ['2010005', '1020002']))
                return $this->respond(['status' => false, 'message' => 'Số điện thoại không được đăng ký cho tài khoản trên, vui lòng liên hệ SePay để được hỗ trợ.']);

            if (in_array($errorCode, ['********']))
                return $this->respond(['status' => false, 'message' => "Quý khách vui lòng thử lại sau ít phút nữa"]);

            if (in_array($errorCode, ['********']))
                return $this->respond(['status' => false, 'message' => 'Tên đăng nhập ACBO không được đăng ký cho tài khoản trên.']);

            return $this->handleAcbClientException($e);
        }
    }

    public function ajax_confirm_modify_primary_account_push_notification()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!$this->acbConfig->allowedModifyPrimaryAccountPushNotification)
            return $this->failNotFound();

        if (!has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền sửa tài khoản ngân hàng']);

        $data = [
            'bank_account_id' => trim(xss_clean($this->request->getVar('bank_account_id'))),
            'request_id' => trim($this->request->getVar('request_id')),
            'authorization_id' => trim($this->request->getVar('authorization_id')),
            'otp' => trim($this->request->getVar('otp')),
        ];

        $rules = [
            'bank_account_id' => ['required'],
            'request_id' => ['required'],
            'authorization_id' => ['required'],
            'otp' => ['required'],
        ];

        if (! $this->validateData($data, $rules))
            return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng tải lại trang']);

        $bankAccountModel = model(BankAccountmodel::class);
        $acbBankAccountMetaDataModel = model(AcbBankAccountMetaDataModel::class);

        $bankAccountDetails = $bankAccountModel->where([
            'id' => $data['bank_account_id'],
            'bank_id' => Acb::BANK_ID,
            'company_id' => $this->user_session['company_id']
        ])->first();

        if (! $bankAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng không tồn tại']);

        try {
            $client = new AcbClient;
        } catch (Exception $e) {
            return $this->handleAcbClientException($e);
        }

        try {
            $virtualAccountInfo = null;

            $response = $client->verifyOtpNotification(
                $data['request_id'],
                $data['authorization_id'],
                $data['otp']
            );

            $responseData = json_decode($response->getBody());

            $response = $client->getPushNotificationRegistrationDetails($bankAccountDetails->account_number);

            $responseData = json_decode($response->getBody());

            $acbBankAccountMetaDataModel->where('bank_account_id', $bankAccountDetails->id)->set(['realtime_transfer_type' => $responseData->responseData->creditOrDebitRealTime])->update();

            set_alert('success', 'Cập nhật thành công');

            return $this->response->setJSON(['status' => true]);
        } catch (Exception $e) {
            $errorCode = $e->getCode();

            if (in_array($errorCode, ['********', '********'])) {
                return $this->fail(['otp' => 'OTP không chính xác hoặc đã hết hiệu lực.']);
            }

            return $this->handleAcbClientException($e);
        }
    }

    public function ajax_request_cancel_primary_account_push_notification()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!$this->acbConfig->allowedModifyPrimaryAccountPushNotification)
            return $this->failNotFound();

        if (!has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền sửa tài khoản ngân hàng']);

        $data = [
            'bank_account_id' => trim(xss_clean($this->request->getVar('bank_account_id'))),
        ];

        $rules = [
            'bank_account_id' => ['required'],
        ];

        if (! $this->validateData($data, $rules))
            return $this->fail($this->validator->getErrors());

        $bankAccountModel = model(BankAccountmodel::class);
        $acbEnterpriseAccountModel = model(AcbEnterpriseAccountModel::class);

        $bankAccountDetails = $bankAccountModel->where([
            'id' => $data['bank_account_id'],
            'bank_id' => Acb::BANK_ID,
            'company_id' => $this->user_session['company_id']
        ])->first();

        if (! $bankAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng không tồn tại']);

        $acbEnterpriseAccountDetails = $acbEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->first();

        try {
            $client = new AcbClient;
        } catch (Exception $e) {
            return $this->handleAcbClientException($e);
        }

        try {
            $virtualAccountInfo = null;

            $response = $client->cancelPushNotification(
                is_object($acbEnterpriseAccountDetails) ? 'ORG' : 'PERS',
                $bankAccountDetails->account_number,
                $bankAccountDetails->phone_number,
                $virtualAccountInfo,
                is_object($acbEnterpriseAccountDetails) ? $acbEnterpriseAccountDetails->username : null
            );

            $responseData = json_decode($response->getBody());

            return $this->response->setJSON([
                'status' => true,
                'message' => $this->getSentOtpAlert($responseData->responseData->authorizationType),
                'otp_request' => [
                    'request_id' => $responseData->responseData->requestId,
                    'authorization_id' => $responseData->responseData->authorizationId,
                ]
            ]);
        } catch (Exception $e) {
            $errorCode = $e->getCode();

            if ($errorCode == '2010008')
                return $this->respond(['status' => false, 'message' => 'Số tài khoản không tồn tại hoặc đã bị khóa trên hệ thống ngân hàng ACB.']);

            if (in_array($errorCode, ['2010005', '1020002']))
                return $this->respond(['status' => false, 'message' => 'Số điện thoại không được đăng ký cho tài khoản trên, vui lòng liên hệ SePay để được hỗ trợ.']);

            if (in_array($errorCode, ['********']))
                return $this->respond(['status' => false, 'message' => "Quý khách vui lòng thử lại sau ít phút nữa"]);

            if (in_array($errorCode, ['********']))
                return $this->respond(['status' => false, 'message' => 'Tên đăng nhập ACBO không được đăng ký cho tài khoản trên.']);

            return $this->handleAcbClientException($e);
        }
    }

    public function ajax_confirm_cancel_primary_account_push_notification()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!$this->acbConfig->allowedModifyPrimaryAccountPushNotification)
            return $this->failNotFound();

        if (!has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền sửa tài khoản ngân hàng']);

        $data = [
            'bank_account_id' => trim(xss_clean($this->request->getVar('bank_account_id'))),
            'request_id' => trim($this->request->getVar('request_id')),
            'authorization_id' => trim($this->request->getVar('authorization_id')),
            'otp' => trim($this->request->getVar('otp')),
        ];

        $rules = [
            'bank_account_id' => ['required'],
            'request_id' => ['required'],
            'authorization_id' => ['required'],
            'otp' => ['required'],
        ];

        if (! $this->validateData($data, $rules))
            return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng tải lại trang']);

        $bankAccountModel = model(BankAccountmodel::class);
        $acbBankAccountMetaDataModel = model(AcbBankAccountMetaDataModel::class);

        $bankAccountDetails = $bankAccountModel->where([
            'id' => $data['bank_account_id'],
            'bank_id' => Acb::BANK_ID,
            'company_id' => $this->user_session['company_id']
        ])->first();

        if (! $bankAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng không tồn tại']);

        try {
            $client = new AcbClient;
        } catch (Exception $e) {
            return $this->handleAcbClientException($e);
        }

        try {
            $virtualAccountInfo = null;

            $response = $client->verifyOtpNotification(
                $data['request_id'],
                $data['authorization_id'],
                $data['otp']
            );

            $acbBankAccountMetaDataModel->where('bank_account_id', $bankAccountDetails->id)->set(['realtime_transfer_type' => 'NONE'])->update();

            set_alert('success', 'Hủy đăng ký thành công');

            return $this->response->setJSON(['status' => true]);
        } catch (Exception $e) {
            $errorCode = $e->getCode();

            if (in_array($errorCode, ['********', '********'])) {
                return $this->fail(['otp' => 'OTP không chính xác hoặc đã hết hiệu lực.']);
            }

            return $this->handleAcbClientException($e);
        }
    }

    public function ajax_va_list()
    {
        if (!has_permission('BankAccount', 'can_view_all'))
            show_404();

        if ($this->request->getMethod(true) != 'POST')
            return '';

        $bankAccountId = $this->request->getGet('bank_account_id');

        if (!is_numeric($bankAccountId))
            $bankAccountId = false;

        $bankSubAccountModel = model(BankSubAccountModel::class);
        $acbEnterpriseAccountModel = model(AcbEnterpriseAccountModel::class);

        $bankAccountDetails = $this->getBankAccountDetails($bankAccountId);
        $bankSubAccounts = $bankSubAccountModel->getDatatables($this->user_session['company_id'], $bankAccountId);

        $data = [];
        $no = $this->request->getVar('start');
        $draw = $this->request->getVar('draw');
        $canEdit = has_permission('BankAccount', 'can_edit');
        $canDelete = has_permission('BankAccount', 'can_delete');

        foreach ($bankSubAccounts as $bankSubAccount) {
            $no++;
            $row = array();
            $actionsBtnHtml = '';

            if ($canEdit) {
                // if ($bankSubAccount->acc_type == 'Real' && $bankAccountDetails->bank_api && $bankAccountDetails->bank_api_connected) {
                //     if ($bankSubAccount->va_active) {
                //         $actionsBtnHtml .=  "<button type='button' onclick='close_va(" . esc($bankSubAccount->id) . ")' class='d-inline-flex align-items-center btn btn-sm btn-outline-dark btn-disable-va-".$bankSubAccount->id."' style='gap: 0.25rem'><div class='spinner-border text-danger loader' style='width: 10px; height: 10px; display: none;' role='status'></div> <i class='bi bi-toggle-off'></i> Vô hiệu hóa</button>";
                //     } else {
                //         $actionsBtnHtml .=  "<button type='button' onclick='enable_va(" . esc($bankSubAccount->id) . ")' class='d-inline-flex align-items-center btn btn-sm btn-outline-primary btn-enable-va-".$bankSubAccount->id."' style='gap: 0.25rem'><div class='spinner-border text-primary loader' style='width: 10px; height: 10px; display: none;' role='status'></div> <i class='bi bi-toggle-off'></i> Kích hoạt</button>";
                //     }
                // }

                if (($bankSubAccount->acc_type == 'Real' && $bankAccountDetails->bank_api && $bankAccountDetails->bank_api_connected)
                    || ($bankSubAccount->acc_type == 'Virtual' && $bankAccountDetails->bank_sms)
                ) {
                    $actionsBtnHtml .= "<a href='javascript:;' onclick='edit_va(" . esc($bankSubAccount->id) . ")' class='btn btn-sm btn-outline-warning ms-2 me-2'><i class='bi bi-pencil'></i> Sửa</a>";

                    $actionsBtnHtml .= "<a href='javascript:;' onclick='delete_va(" . esc($bankSubAccount->id) . ")' class='btn btn-sm btn-outline-danger'><i class='bi bi-trash'></i> Xóa</a>";
                }
            }

            $row[] = $no;
            $row[] = $bankSubAccount->id;
            $row[] = "<a href='javascript:;' onclick='view_va("  . esc($bankSubAccount->id) . ")'>" . esc($bankSubAccount->sub_account) . ($bankSubAccount->acc_type == 'Real' ? " <i class='bi bi-patch-check-fill'></i>" : '') . "</a>";

            if ($bankSubAccount->va_active == 1)
                $row[] = "<span class='text-success'>Hoạt động</span>";
            else
                $row[] = "<span class='text-danger'>Chưa kích hoạt</span>";

            $acbEnterpriseAccountDetails = $acbEnterpriseAccountModel->where(['bank_account_id' => $bankSubAccount->bank_account_id])->first();

            $row[] = esc($bankSubAccount->label);

            $row[] = esc($bankSubAccount->created_at);
            $row[] = $actionsBtnHtml;
            $data[] = $row;
        }

        return $this->response->setJSON([
            'draw' => $draw,
            'recordsTotal' => $bankSubAccountModel->countAll($this->user_session['company_id'], $bankAccountId),
            'recordsFiltered' => $bankSubAccountModel->countFiltered($this->user_session['company_id'], $bankAccountId),
            'data' => $data,
        ]);
    }

    public function ajax_view_va($id = null)
    {
        if (!has_permission('BankAccount', 'can_view_all'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền xem tài khoản ngân hàng']);

        if (!is_numeric($id))
            return $this->response->setJSON(['status' => false, 'message' => 'ID tài khoản ngân ảo không hợp lệ']);

        $bankSubAccountModel = model(BankSubAccountModel::class);
        $transactionsModel = slavable_model(TransactionsModel::class, 'Acb');

        $data['va_details'] = $bankSubAccountModel
            ->select('tb_autopay_bank_sub_account.id, tb_autopay_bank_sub_account.sub_account, tb_autopay_bank_sub_account.acc_type, tb_autopay_bank_sub_account.bank_account_id, tb_autopay_bank_sub_account.label, tb_autopay_bank_account.account_number, tb_autopay_bank_account.active, tb_autopay_bank_account.created_at, tb_autopay_bank.brand_name, tb_autopay_bank_account.account_number, tb_autopay_bank_account.account_holder_name, tb_autopay_bank.icon_path, tb_autopay_bank.logo_path, tb_autopay_bank.full_name, tb_autopay_bank_sub_account.sub_holder_name, tb_autopay_bank_sub_account.va_active')
            ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id')
            ->join('tb_autopay_bank', 'tb_autopay_bank.id=tb_autopay_bank_account.bank_id')
            ->where([
                'tb_autopay_bank_sub_account.id' => $id,
                'tb_autopay_bank_account.company_id' => $this->user_session['company_id'],
                'tb_autopay_bank_account.bank_id' => Acb::BANK_ID
            ])->get()->getRow();

        $data['count_transactions'] = $transactionsModel->where([
            'sub_account' => $data['va_details']->sub_account
        ])->countAllResults();

        $data['last_transaction'] = $transactionsModel->where([
            'sub_account' => $data['va_details']->sub_account,
            'accumulated != ' => ''
        ])->orderBy('transaction_date', 'DESC')->get()->getRow();

        if ($data['va_details']) {
            $html = view('acb/individual/va_view', $data);
            return $this->response->setJSON(['status' => true, 'html' => $html]);
        }

        return $this->response->setJSON(['status' => false, 'message' => 'Không tìm thấy tài khoản ảo này']);
    }

    public function ajax_request_add_individual_real_va()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (! has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền sửa tài khoản ngân hàng']);

        $data = [
            'bank_account_id' => trim(xss_clean($this->request->getVar('bank_account_id'))),
        ];

        $bankAccountModel = model(BankAccountmodel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $acbEnterpriseAccountModel = model(AcbEnterpriseAccountModel::class);

        $bankAccountDetails = $this->getBankAccountDetails($data['bank_account_id']);

        if (! $bankAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng không tồn tại']);

        if ($bankAccountDetails->bank_sms)
            return $this->respond(['status' => false, 'message' => 'Phương thức kết nối SMS không thể sử dụng tính năng này']);

        $acbEnterpriseAccountDetails = $acbEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->first();

        if ($acbEnterpriseAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng ACB doanh nghiệp không được sử dụng tính năng này']);

        try {
            $client = new AcbClient;
        } catch (Exception $e) {
            return $this->handleAcbClientException($e);
        }

        try {
            $virtualAccountInfo = null;

            $response = $client->registerVa(
                'PERS',
                $bankAccountDetails->account_number,
                $bankAccountDetails->phone_number,
                true,
                [
                    'virtualAccountPrefixCode' => 'LOC',
                    'beneficiaryNameRule' => 1,
                    'virtualAccountExplain' => $bankAccountDetails->account_holder_name
                ]
            );

            $responseData = json_decode($response->getBody());

            return $this->response->setJSON([
                'status' => true,
                'message' => $this->getSentOtpAlert($responseData->responseData->authorizationType),
                'otp_request' => [
                    'request_id' => $responseData->responseData->requestId,
                    'authorization_id' => $responseData->responseData->authorizationId,
                    'authorization_type' => $responseData->responseData->authorizationType,
                ]
            ]);
        } catch (Exception $e) {
            $errorCode = $e->getCode();

            if (in_array($errorCode, ['********']))
                return $this->respond(['status' => false, 'message' => "Tài khoản ngân hàng của bạn đã bị khóa, vui lòng liên hệ ngân hàng."]);

            if (in_array($errorCode, ['********']))
                return $this->respond(['status' => false, 'message' => "Quý khách vui lòng thử lại sau ít phút nữa"]);

            if (in_array($errorCode, ['********']))
                return $this->respond(['status' => false, 'message' => "Quý khách đã đạt giới hạn số lượng VA cho phép"]);

            return $this->handleAcbClientException($e);
        }
    }

    public function ajax_confirm_add_individual_real_va()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (! has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền thêm VA cho tài khoản ngân hàng']);

        $data = [
            'bank_account_id' => trim(xss_clean($this->request->getVar('bank_account_id'))),
            'label' => trim(xss_clean($this->request->getVar('label'))),
            'request_id' => trim($this->request->getVar('request_id')),
            'authorization_id' => trim($this->request->getVar('authorization_id')),
            'otp' => trim($this->request->getVar('otp')),
        ];

        $rules = [
            'request_id' => ['required'],
            'authorization_id' => ['required'],
            'otp' => ['required'],
            'label' => ['permit_empty', 'max_length[100]']
        ];

        if (! $this->validateData($data, $rules))
            return $this->fail($this->validator->getErrors());

        $bankAccountModel = model(BankAccountmodel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $acbEnterpriseAccountModel = model(AcbEnterpriseAccountModel::class);

        $bankAccountDetails = $this->getBankAccountDetails($data['bank_account_id']);

        if (! $bankAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng không tồn tại']);

        if ($bankAccountDetails->bank_sms)
            return $this->respond(['status' => false, 'message' => 'Phương thức kết nối SMS không thể sử dụng tính năng này']);

        $acbEnterpriseAccountDetails = $acbEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->first();

        if ($acbEnterpriseAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng ACB doanh nghiệp không được sử dụng tính năng này']);

        try {
            $client = new AcbClient;
        } catch (Exception $e) {
            return $this->handleAcbClientException($e);
        }

        try {
            $virtualAccountInfo = null;

            $response = $client->verifyOtpVa(
                $data['request_id'],
                $data['authorization_id'],
                $data['otp']
            );

            $responseData = json_decode($response->getBody());

            $subAccount = $responseData->responseData->virtualAccountNumber;

            $bankSubAccountId = $bankSubAccountModel->insert([
                'sub_account' => $subAccount,
                'sub_holder_name' => $bankAccountDetails->account_holder_name,
                'acc_type' => 'Real',
                'bank_account_id' => $bankAccountDetails->id,
                'label' => $data['label'],
                'va_active' => 1,
            ]);

            if (!$bankSubAccountId)
                return $this->response->setJSON(['status' => false, 'message' => 'Không thể thêm tài khoản ảo này. Vui lòng liên hệ SePay để được hỗ trợ.']);

            return $this->response->setJSON(['status' => true, 'id' => $bankSubAccountId]);
        } catch (Exception $e) {
            $errorCode = $e->getCode();

            if (in_array($errorCode, ['********', '********'])) {
                return $this->fail(['otp' => 'OTP không chính xác hoặc đã hết hiệu lực.']);
            }

            if (in_array($errorCode, ['********'])) {
                return $this->fail(['otp' => 'Bạn đã nhập sai quá số lần quy định, vui lòng lấy lại OTP mới.']);
            }

            return $this->handleAcbClientException($e);
        }
    }

    public function ajax_request_enable_individual_real_va()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (! has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền kích hoạt tài khoản VA']);

        $data = [
            'bank_account_id' => trim(xss_clean($this->request->getVar('bank_account_id'))),
            'sub_account_id' => trim(xss_clean($this->request->getVar('sub_account_id'))),
            'user_agreement' => (bool) $this->request->getVar('user_agreement')
        ];

        $bankAccountModel = model(BankAccountmodel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $acbEnterpriseAccountModel = model(AcbEnterpriseAccountModel::class);

        $bankAccountDetails = $this->getBankAccountDetails($data['bank_account_id']);

        if (! $bankAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng không tồn tại']);

        if ($bankAccountDetails->bank_sms)
            return $this->respond(['status' => false, 'message' => 'Phương thức kết nối SMS không thể sử dụng tính năng này']);

        $acbEnterpriseAccountDetails = $acbEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->first();

        if ($acbEnterpriseAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng ACB doanh nghiệp không được sử dụng tính năng này']);

        $bankSubAccountDetails = $this->getBankSubAccountDetails($data['sub_account_id'], $bankAccountDetails->id);

        if (! $bankSubAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản VA không tồn tại']);

        try {
            $client = new AcbClient;
        } catch (Exception $e) {
            return $this->handleAcbClientException($e);
        }

        try {
            $virtualAccountInfo = null;

            $response = $client->registerPushNotification(
                'PERS',
                $bankAccountDetails->account_number,
                $bankAccountDetails->phone_number,
                'CREDIT',
                'NONE',
                $data['user_agreement'],
                [
                    'virtualAccountPrefixCode' => 'LOC',
                    'virtualAccountNumber' => $bankSubAccountDetails->sub_account,
                ]
            );

            $responseData = json_decode($response->getBody());

            return $this->response->setJSON([
                'status' => true,
                'message' => $this->getSentOtpAlert($responseData->responseData->authorizationType),
                'otp_request' => [
                    'request_id' => $responseData->responseData->requestId,
                    'authorization_id' => $responseData->responseData->authorizationId,
                    'authorization_type' => $responseData->responseData->authorizationType,
                ]
            ]);
        } catch (Exception $e) {
            $errorCode = $e->getCode();

            if (in_array($errorCode, ['********']))
                return $this->respond(['status' => false, 'message' => "Quý khách vui lòng thử lại sau ít phút nữa"]);

            return $this->handleAcbClientException($e);
        }
    }

    public function ajax_confirm_enable_individual_real_va()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (! has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền kích hoạt tài khoản VA']);

        $data = [
            'bank_account_id' => trim(xss_clean($this->request->getVar('bank_account_id'))),
            'sub_account_id' => trim(xss_clean($this->request->getVar('sub_account_id'))),
            'request_id' => trim($this->request->getVar('request_id')),
            'authorization_id' => trim($this->request->getVar('authorization_id')),
            'otp' => trim($this->request->getVar('otp')),
        ];

        $rules = [
            'request_id' => ['required'],
            'authorization_id' => ['required'],
            'otp' => ['required'],
        ];

        if (! $this->validateData($data, $rules))
            return $this->fail($this->validator->getErrors());

        $bankAccountModel = model(BankAccountmodel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $acbEnterpriseAccountModel = model(AcbEnterpriseAccountModel::class);

        $bankAccountDetails = $this->getBankAccountDetails($data['bank_account_id']);

        if (! $bankAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng không tồn tại']);

        if ($bankAccountDetails->bank_sms)
            return $this->respond(['status' => false, 'message' => 'Phương thức kết nối SMS không thể sử dụng tính năng này']);

        $acbEnterpriseAccountDetails = $acbEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->first();

        if ($acbEnterpriseAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng ACB doanh nghiệp không được sử dụng tính năng này']);

        $bankSubAccountDetails = $this->getBankSubAccountDetails($data['sub_account_id'], $bankAccountDetails->id);

        if (! $bankSubAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản VA không tồn tại']);

        try {
            $client = new AcbClient;
        } catch (Exception $e) {
            return $this->handleAcbClientException($e);
        }

        try {
            $virtualAccountInfo = null;

            $response = $client->verifyOtpNotification(
                $data['request_id'],
                $data['authorization_id'],
                $data['otp']
            );

            $bankSubAccountModel->where(['id' => $bankSubAccountDetails->id])->set(['va_active' => 1])->update();

            return $this->response->setJSON(['status' => true]);
        } catch (Exception $e) {
            $errorCode = $e->getCode();

            if (in_array($errorCode, ['********', '********'])) {
                return $this->fail(['otp' => 'OTP không chính xác hoặc đã hết hiệu lực.']);
            }

            return $this->handleAcbClientException($e);
        }
    }

    public function ajax_request_close_individual_real_va()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (! has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền vô hiệu hóa tài khoản VA']);

        $data = [
            'bank_account_id' => trim(xss_clean($this->request->getVar('bank_account_id'))),
            'sub_account_id' => trim(xss_clean($this->request->getVar('sub_account_id'))),
        ];

        $bankAccountModel = model(BankAccountmodel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $acbEnterpriseAccountModel = model(AcbEnterpriseAccountModel::class);

        $bankAccountDetails = $this->getBankAccountDetails($data['bank_account_id']);

        if (! $bankAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng không tồn tại']);

        if ($bankAccountDetails->bank_sms)
            return $this->respond(['status' => false, 'message' => 'Phương thức kết nối SMS không thể sử dụng tính năng này']);

        $acbEnterpriseAccountDetails = $acbEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->first();

        if ($acbEnterpriseAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng ACB doanh nghiệp không được sử dụng tính năng này']);

        $bankSubAccountDetails = $this->getBankSubAccountDetails($data['sub_account_id'], $bankAccountDetails->id);

        if (! $bankSubAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản VA không tồn tại']);

        try {
            $client = new AcbClient;
        } catch (Exception $e) {
            return $this->handleAcbClientException($e);
        }

        try {
            $virtualAccountInfo = null;

            $response = $client->cancelPushNotification(
                'PERS',
                $bankAccountDetails->account_number,
                $bankAccountDetails->phone_number,
                [
                    'virtualAccountPrefixCode' => 'LOC',
                    'virtualAccountNumber' => $bankSubAccountDetails->sub_account,
                ],
            );

            $responseData = json_decode($response->getBody());

            return $this->response->setJSON([
                'status' => true,
                'message' => $this->getSentOtpAlert($responseData->responseData->authorizationType),
                'otp_request' => [
                    'request_id' => $responseData->responseData->requestId,
                    'authorization_id' => $responseData->responseData->authorizationId,
                    'authorization_type' => $responseData->responseData->authorizationType,
                ]
            ]);
        } catch (Exception $e) {
            $errorCode = $e->getCode();

            if (in_array($errorCode, ['********']))
                return $this->respond(['status' => false, 'message' => "Quý khách vui lòng thử lại sau ít phút nữa"]);

            return $this->handleAcbClientException($e);
        }
    }

    public function ajax_confirm_close_individual_real_va()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (! has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền vô hiệu hóa tài khoản VA']);

        $data = [
            'bank_account_id' => trim(xss_clean($this->request->getVar('bank_account_id'))),
            'sub_account_id' => trim(xss_clean($this->request->getVar('sub_account_id'))),
            'request_id' => trim($this->request->getVar('request_id')),
            'authorization_id' => trim($this->request->getVar('authorization_id')),
            'otp' => trim($this->request->getVar('otp')),
        ];

        $rules = [
            'request_id' => ['required'],
            'authorization_id' => ['required'],
            'otp' => ['required'],
        ];

        if (! $this->validateData($data, $rules))
            return $this->fail($this->validator->getErrors());

        $bankAccountModel = model(BankAccountmodel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $acbEnterpriseAccountModel = model(AcbEnterpriseAccountModel::class);

        $bankAccountDetails = $this->getBankAccountDetails($data['bank_account_id']);

        if (! $bankAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng không tồn tại']);

        if ($bankAccountDetails->bank_sms)
            return $this->respond(['status' => false, 'message' => 'Phương thức kết nối SMS không thể sử dụng tính năng này']);

        $acbEnterpriseAccountDetails = $acbEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->first();

        if ($acbEnterpriseAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng ACB doanh nghiệp không được sử dụng tính năng này']);

        $bankSubAccountDetails = $this->getBankSubAccountDetails($data['sub_account_id'], $bankAccountDetails->id);

        if (! $bankSubAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản VA không tồn tại']);

        try {
            $client = new AcbClient;
        } catch (Exception $e) {
            return $this->handleAcbClientException($e);
        }

        try {
            $virtualAccountInfo = null;

            $response = $client->verifyOtpNotification(
                $data['request_id'],
                $data['authorization_id'],
                $data['otp']
            );

            $bankSubAccountModel->where(['id' => $bankSubAccountDetails->id])->set(['va_active' => 0])->update();

            return $this->response->setJSON(['status' => true]);
        } catch (Exception $e) {
            $errorCode = $e->getCode();

            if (in_array($errorCode, ['********', '********'])) {
                return $this->fail(['otp' => 'OTP không chính xác hoặc đã hết hiệu lực.']);
            }

            return $this->handleAcbClientException($e);
        }
    }

    public function ajax_request_delete_individual_real_va()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (! has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền xóa tài khoản VA']);

        $data = [
            'bank_account_id' => trim(xss_clean($this->request->getVar('bank_account_id'))),
            'sub_account_id' => trim(xss_clean($this->request->getVar('sub_account_id'))),
        ];

        $bankAccountModel = model(BankAccountmodel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $acbEnterpriseAccountModel = model(AcbEnterpriseAccountModel::class);

        $bankAccountDetails = $this->getBankAccountDetails($data['bank_account_id']);

        if (! $bankAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng không tồn tại']);

        if ($bankAccountDetails->bank_sms)
            return $this->respond(['status' => false, 'message' => 'Phương thức kết nối SMS không thể sử dụng tính năng này']);

        $acbEnterpriseAccountDetails = $acbEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->first();

        if ($acbEnterpriseAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng ACB doanh nghiệp không được sử dụng tính năng này']);

        $bankSubAccountDetails = $this->getBankSubAccountDetails($data['sub_account_id'], $bankAccountDetails->id);

        if (! $bankSubAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản VA không tồn tại']);

        try {
            $client = new AcbClient;
        } catch (Exception $e) {
            return $this->handleAcbClientException($e);
        }

        try {
            $virtualAccountInfo = null;

            $response = $client->cancelVa(
                $bankAccountDetails->account_number,
                $bankAccountDetails->phone_number,
                $bankSubAccountDetails->sub_account
            );

            $responseData = json_decode($response->getBody());

            return $this->response->setJSON([
                'status' => true,
                'message' => $this->getSentOtpAlert($responseData->responseData->authorizationType),
                'otp_request' => [
                    'request_id' => $responseData->responseData->requestId,
                    'authorization_id' => $responseData->responseData->authorizationId,
                    'authorization_type' => $responseData->responseData->authorizationType,
                ]
            ]);
        } catch (Exception $e) {
            $errorCode = $e->getCode();

            if (in_array($errorCode, ['********']))
                return $this->respond(['status' => false, 'message' => "Quý khách vui lòng thử lại sau ít phút nữa"]);

            return $this->handleAcbClientException($e);
        }
    }

    public function ajax_confirm_delete_individual_real_va()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (! has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền xóa tài khoản VA']);

        $data = [
            'bank_account_id' => trim(xss_clean($this->request->getVar('bank_account_id'))),
            'sub_account_id' => trim(xss_clean($this->request->getVar('sub_account_id'))),
            'request_id' => trim($this->request->getVar('request_id')),
            'authorization_id' => trim($this->request->getVar('authorization_id')),
            'otp' => trim($this->request->getVar('otp')),
        ];

        $rules = [
            'request_id' => ['required'],
            'authorization_id' => ['required'],
            'otp' => ['required'],
        ];

        if (! $this->validateData($data, $rules))
            return $this->fail($this->validator->getErrors());

        $bankAccountModel = model(BankAccountmodel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $acbEnterpriseAccountModel = model(AcbEnterpriseAccountModel::class);

        $bankAccountDetails = $this->getBankAccountDetails($data['bank_account_id']);

        if (! $bankAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng không tồn tại']);

        if ($bankAccountDetails->bank_sms)
            return $this->respond(['status' => false, 'message' => 'Phương thức kết nối SMS không thể sử dụng tính năng này']);

        $acbEnterpriseAccountDetails = $acbEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->first();

        if ($acbEnterpriseAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng ACB doanh nghiệp không được sử dụng tính năng này']);

        $bankSubAccountDetails = $this->getBankSubAccountDetails($data['sub_account_id'], $bankAccountDetails->id);

        if (! $bankSubAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản VA không tồn tại']);

        try {
            $client = new AcbClient;
        } catch (Exception $e) {
            return $this->handleAcbClientException($e);
        }

        try {
            $virtualAccountInfo = null;

            $response = $client->verifyOtpVa(
                $data['request_id'],
                $data['authorization_id'],
                $data['otp']
            );

            $bankSubAccountModel->where(['id' => $bankSubAccountDetails->id])->delete();

            return $this->response->setJSON(['status' => true]);
        } catch (Exception $e) {
            $errorCode = $e->getCode();

            if (in_array($errorCode, ['********', '********'])) {
                return $this->fail(['otp' => 'OTP không chính xác hoặc đã hết hiệu lực.']);
            }

            return $this->handleAcbClientException($e);
        }
    }

    public function ajax_add_enterprise_real_va()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (! has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền sửa tài khoản ngân hàng']);

        $data = [
            'bank_account_id' => trim(xss_clean($this->request->getVar('bank_account_id'))),
            'sub_account' => trim(xss_clean($this->request->getVar('sub_account'))),
            'label' => trim(xss_clean($this->request->getVar('label'))),
        ];

        $bankAccountModel = model(BankAccountmodel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $acbEnterpriseAccountModel = model(AcbEnterpriseAccountModel::class);

        $bankAccountDetails = $this->getBankAccountDetails($data['bank_account_id']);

        if (! $bankAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng không tồn tại']);

        if ($bankAccountDetails->bank_sms)
            return $this->respond(['status' => false, 'message' => 'Phương thức kết nối SMS không thể sử dụng tính năng này']);

        $acbEnterpriseAccountDetails = $acbEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->first();

        if (!$acbEnterpriseAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng ACB cá nhân không được sử dụng tính năng này']);

        if (!$acbEnterpriseAccountDetails->vac) {
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng ACB này chưa có đầu định danh tài khoản ảo, vui lòng liên hệ SePay để được hỗ trợ.']);
        }

        $vac = $acbEnterpriseAccountDetails->vac;
        $vaMaxlength = $this->acbConfig->enterpriseVaMaxLength ?? 18;

        if (!$this->validateData($data, [
            'sub_account' => ['label' => 'Số tài khoản ảo', 'rules' => ['required', 'regex_match[/^[A-Z0-9]{1,' . ($vaMaxlength - strlen($vac)) . '}$/]']],
            'label' => ['label' => 'Tên gợi nhớ', 'rules' => ['permit_empty', 'string', 'max_length[100]']]
        ], $data)) {
            return $this->fail($this->validator->getErrors());
        }

        $bankSubAccountModel = model(BankSubAccountModel::class);

        $existOwnBankSubAccount = $bankSubAccountModel
            ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id')
            ->where([
                'tb_autopay_bank_account.company_id' => $this->user_session['company_id'],
                'tb_autopay_bank_sub_account.sub_account' => $data['sub_account']
            ])
            ->get()->getRow();

        if (is_object($existOwnBankSubAccount))
            return $this->fail(['sub_account' => 'Tài khoản ảo này đã tồn tại. Bạn đã tạo tài khoản ảo này rồi.']);

        $existBankSubAccount = $bankSubAccountModel->where(['tb_autopay_bank_sub_account.sub_account' => $data['sub_account']])->get()->getRow();

        if (is_object($existBankSubAccount))
            return $this->fail(['sub_account' => 'Tài khoản ảo này đã tồn tại trên hệ thống. Vui lòng chọn tên khác.']);

        $safeBankSubAccountData = [
            'sub_account' => $vac . $data['sub_account'],
            'sub_holder_name' => $bankAccountDetails->account_holder_name,
            'bank_account_id' => $data['bank_account_id'],
            'acc_type' => 'Real',
            'va_active' => 1,
            'label' => $data['label']
        ];

        $bankSubAccountId = $bankSubAccountModel->insert($safeBankSubAccountData);

        if (!$bankSubAccountId)
            return $this->respond(['status' => false, 'message' => 'Không thể thêm tài khoản ảo này. Vui lòng liên hệ SePay để được hỗ trợ.']);

        return $this->respond(['status' => true, 'id' => $bankSubAccountId]);
    }

    public function ajax_edit_enterprise_real_va()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (! has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền sửa tài khoản ngân hàng']);

        $data = [
            'id' => trim(xss_clean($this->request->getVar('id'))),
            'bank_account_id' => trim(xss_clean($this->request->getVar('bank_account_id'))),
            'label' => trim(xss_clean($this->request->getVar('label'))),
        ];

        if (!$this->validateData($data, [
            'label' => ['label' => 'Tên gợi nhớ', 'rules' => ['permit_empty', 'string', 'max_length[100]']],
        ], $data)) {
            return $this->fail($this->validator->getErrors());
        }

        $bankAccountModel = model(BankAccountmodel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $acbEnterpriseAccountModel = model(AcbEnterpriseAccountModel::class);

        $bankAccountDetails = $this->getBankAccountDetails($data['bank_account_id']);

        if (! $bankAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng không tồn tại']);

        if ($bankAccountDetails->bank_sms)
            return $this->respond(['status' => false, 'message' => 'Phương thức kết nối SMS không thể sử dụng tính năng này']);

        $acbEnterpriseAccountDetails = $acbEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->first();

        if (!$acbEnterpriseAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng ACB cá nhân không được sử dụng tính năng này']);

        $bankSubAccountDetails = $this->getBankSubAccountDetails($data['id']);

        if (!$bankSubAccountDetails) {
            return $this->respond(['status' => false, 'message' => 'Không tìm thấy tài khoản ảo này']);
        }

        $updated = $bankSubAccountModel->update($bankSubAccountDetails->id, [
            'label' => $data['label']
        ]);

        if (!$updated) {
            return $this->respond([
                'status' => false,
                'message' => 'Đã có lỗi xảy ra, vui lòng thử lại sau hoặc liên hệ SePay để được hỗ trợ.'
            ]);
        }

        return $this->respond(['status' => true, 'message' => 'Đã cập nhật VA']);
    }

    public function ajax_request_enable_enterprise_real_va()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (! has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền kích hoạt tài khoản VA']);

        $data = [
            'bank_account_id' => trim(xss_clean($this->request->getVar('bank_account_id'))),
            'sub_account_id' => trim(xss_clean($this->request->getVar('sub_account_id'))),
            'user_agreement' => (bool) $this->request->getVar('user_agreement')
        ];

        $bankAccountModel = model(BankAccountmodel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $acbEnterpriseAccountModel = model(AcbEnterpriseAccountModel::class);

        $bankAccountDetails = $this->getBankAccountDetails($data['bank_account_id']);

        if (! $bankAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng không tồn tại']);

        if ($bankAccountDetails->bank_sms)
            return $this->respond(['status' => false, 'message' => 'Phương thức kết nối SMS không thể sử dụng tính năng này']);

        $acbEnterpriseAccountDetails = $acbEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->first();

        if (!$acbEnterpriseAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng ACB cá nhân không được sử dụng tính năng này']);

        $bankSubAccountDetails = $this->getBankSubAccountDetails($data['sub_account_id'], $bankAccountDetails->id);

        if (! $bankSubAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản VA không tồn tại']);

        try {
            $client = new AcbClient;
        } catch (Exception $e) {
            return $this->handleAcbClientException($e);
        }

        try {
            $virtualAccountInfo = null;

            $response = $client->registerPushNotification(
                'ORG',
                $bankAccountDetails->account_number,
                $bankAccountDetails->phone_number,
                'CREDIT',
                'NONE',
                $data['user_agreement'],
                [
                    'virtualAccountPrefixCode' => substr($bankSubAccountDetails->sub_account, 0, 3),
                    'virtualAccountNumber' => $bankSubAccountDetails->sub_account,
                ],
                $acbEnterpriseAccountDetails->username,
            );

            $responseData = json_decode($response->getBody());

            return $this->response->setJSON([
                'status' => true,
                'message' => $this->getSentOtpAlert($responseData->responseData->authorizationType),
                'otp_request' => [
                    'request_id' => $responseData->responseData->requestId,
                    'authorization_id' => $responseData->responseData->authorizationId,
                    'authorization_type' => $responseData->responseData->authorizationType,
                ]
            ]);
        } catch (Exception $e) {
            $errorCode = $e->getCode();

            if (in_array($errorCode, ['********']))
                return $this->respond(['status' => false, 'message' => "Quý khách vui lòng thử lại sau ít phút nữa"]);

            return $this->handleAcbClientException($e);
        }
    }

    public function ajax_confirm_enable_enterprise_real_va()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (! has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền kích hoạt tài khoản VA']);

        $data = [
            'bank_account_id' => trim(xss_clean($this->request->getVar('bank_account_id'))),
            'sub_account_id' => trim(xss_clean($this->request->getVar('sub_account_id'))),
            'request_id' => trim($this->request->getVar('request_id')),
            'authorization_id' => trim($this->request->getVar('authorization_id')),
            'otp' => trim($this->request->getVar('otp')),
        ];

        $rules = [
            'request_id' => ['required'],
            'authorization_id' => ['required'],
            'otp' => ['required'],
        ];

        if (! $this->validateData($data, $rules))
            return $this->fail($this->validator->getErrors());

        $bankAccountModel = model(BankAccountmodel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $acbEnterpriseAccountModel = model(AcbEnterpriseAccountModel::class);

        $bankAccountDetails = $this->getBankAccountDetails($data['bank_account_id']);

        if (! $bankAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng không tồn tại']);

        if ($bankAccountDetails->bank_sms)
            return $this->respond(['status' => false, 'message' => 'Phương thức kết nối SMS không thể sử dụng tính năng này']);

        $acbEnterpriseAccountDetails = $acbEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->first();

        if (!$acbEnterpriseAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng ACB cá nhân không được sử dụng tính năng này']);

        $bankSubAccountDetails = $this->getBankSubAccountDetails($data['sub_account_id'], $bankAccountDetails->id);

        if (! $bankSubAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản VA không tồn tại']);

        try {
            $client = new AcbClient;
        } catch (Exception $e) {
            return $this->handleAcbClientException($e);
        }

        try {
            $virtualAccountInfo = null;

            $response = $client->verifyOtpNotification(
                $data['request_id'],
                $data['authorization_id'],
                $data['otp']
            );

            $bankSubAccountModel->where(['id' => $bankSubAccountDetails->id])->set(['va_active' => 1])->update();

            return $this->response->setJSON(['status' => true]);
        } catch (Exception $e) {
            $errorCode = $e->getCode();

            if (in_array($errorCode, ['********', '********'])) {
                return $this->fail(['otp' => 'OTP không chính xác hoặc đã hết hiệu lực.']);
            }

            return $this->handleAcbClientException($e);
        }
    }

    public function ajax_request_close_enterprise_real_va()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (! has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền vô hiệu hóa tài khoản VA']);

        $data = [
            'bank_account_id' => trim(xss_clean($this->request->getVar('bank_account_id'))),
            'sub_account_id' => trim(xss_clean($this->request->getVar('sub_account_id'))),
        ];

        $bankAccountModel = model(BankAccountmodel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $acbEnterpriseAccountModel = model(AcbEnterpriseAccountModel::class);

        $bankAccountDetails = $this->getBankAccountDetails($data['bank_account_id']);

        if (! $bankAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng không tồn tại']);

        if ($bankAccountDetails->bank_sms)
            return $this->respond(['status' => false, 'message' => 'Phương thức kết nối SMS không thể sử dụng tính năng này']);

        $acbEnterpriseAccountDetails = $acbEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->first();

        if (!$acbEnterpriseAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng ACB cá nhân không được sử dụng tính năng này']);

        $bankSubAccountDetails = $this->getBankSubAccountDetails($data['sub_account_id'], $bankAccountDetails->id);

        if (! $bankSubAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản VA không tồn tại']);

        try {
            $client = new AcbClient;
        } catch (Exception $e) {
            return $this->handleAcbClientException($e);
        }

        try {
            $virtualAccountInfo = null;

            $response = $client->cancelPushNotification(
                'ORG',
                $bankAccountDetails->account_number,
                $bankAccountDetails->phone_number,
                [
                    'virtualAccountPrefixCode' => substr($bankSubAccountDetails->sub_account, 0, 3),
                    'virtualAccountNumber' => $bankSubAccountDetails->sub_account,
                ],
                $acbEnterpriseAccountDetails->username
            );

            $responseData = json_decode($response->getBody());

            return $this->response->setJSON([
                'status' => true,
                'message' => $this->getSentOtpAlert($responseData->responseData->authorizationType),
                'otp_request' => [
                    'request_id' => $responseData->responseData->requestId,
                    'authorization_id' => $responseData->responseData->authorizationId,
                    'authorization_type' => $responseData->responseData->authorizationType,
                ]
            ]);
        } catch (Exception $e) {
            $errorCode = $e->getCode();

            if (in_array($errorCode, ['********']))
                return $this->respond(['status' => false, 'message' => "Quý khách vui lòng thử lại sau ít phút nữa"]);

            return $this->handleAcbClientException($e);
        }
    }

    public function ajax_confirm_close_enterprise_real_va()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (! has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền vô hiệu hóa tài khoản VA']);

        $data = [
            'bank_account_id' => trim(xss_clean($this->request->getVar('bank_account_id'))),
            'sub_account_id' => trim(xss_clean($this->request->getVar('sub_account_id'))),
            'request_id' => trim($this->request->getVar('request_id')),
            'authorization_id' => trim($this->request->getVar('authorization_id')),
            'otp' => trim($this->request->getVar('otp')),
        ];

        $rules = [
            'request_id' => ['required'],
            'authorization_id' => ['required'],
            'otp' => ['required'],
        ];

        if (! $this->validateData($data, $rules))
            return $this->fail($this->validator->getErrors());

        $bankAccountModel = model(BankAccountmodel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $acbEnterpriseAccountModel = model(AcbEnterpriseAccountModel::class);

        $bankAccountDetails = $this->getBankAccountDetails($data['bank_account_id']);

        if (! $bankAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng không tồn tại']);

        if ($bankAccountDetails->bank_sms)
            return $this->respond(['status' => false, 'message' => 'Phương thức kết nối SMS không thể sử dụng tính năng này']);

        $acbEnterpriseAccountDetails = $acbEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->first();

        if (!$acbEnterpriseAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng ACB cá nhân không được sử dụng tính năng này']);

        $bankSubAccountDetails = $this->getBankSubAccountDetails($data['sub_account_id'], $bankAccountDetails->id);

        if (! $bankSubAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản VA không tồn tại']);

        try {
            $client = new AcbClient;
        } catch (Exception $e) {
            return $this->handleAcbClientException($e);
        }

        try {
            $virtualAccountInfo = null;

            $response = $client->verifyOtpNotification(
                $data['request_id'],
                $data['authorization_id'],
                $data['otp']
            );

            $bankSubAccountModel->where(['id' => $bankSubAccountDetails->id])->set(['va_active' => 0])->update();

            return $this->response->setJSON(['status' => true]);
        } catch (Exception $e) {
            $errorCode = $e->getCode();

            if (in_array($errorCode, ['********', '********'])) {
                return $this->fail(['otp' => 'OTP không chính xác hoặc đã hết hiệu lực.']);
            }

            return $this->handleAcbClientException($e);
        }
    }

    public function ajax_delete_manual_enterprise_real_va()
    {
        if ($this->request->getMethod(true) != 'POST')
            return $this->failNotFound();

        if (! has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền xóa tài khoản VA']);

        $data = [
            'bank_account_id' => trim(xss_clean($this->request->getVar('bank_account_id'))),
            'sub_account_id' => trim(xss_clean($this->request->getVar('sub_account_id'))),
        ];

        $bankAccountModel = model(BankAccountmodel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $acbEnterpriseAccountModel = model(AcbEnterpriseAccountModel::class);

        $bankAccountDetails = $this->getBankAccountDetails($data['bank_account_id']);

        if (! $bankAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng không tồn tại']);

        if ($bankAccountDetails->bank_sms)
            return $this->respond(['status' => false, 'message' => 'Phương thức kết nối SMS không thể sử dụng tính năng này']);

        $acbEnterpriseAccountDetails = $acbEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->first();

        if (!$acbEnterpriseAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng ACB cá nhân không được sử dụng tính năng này']);

        $bankSubAccountDetails = $this->getBankSubAccountDetails($data['sub_account_id'], $bankAccountDetails->id);

        if (! $bankSubAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản VA không tồn tại']);

        $bankSubAccountModel->delete($bankSubAccountDetails->id);

        return $this->response->setJSON(['status' => true]);
    }

    public function ajax_request_delete_enterprise_real_va()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (! has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền xóa tài khoản VA']);

        $data = [
            'bank_account_id' => trim(xss_clean($this->request->getVar('bank_account_id'))),
            'sub_account_id' => trim(xss_clean($this->request->getVar('sub_account_id'))),
        ];

        $bankAccountModel = model(BankAccountmodel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $acbEnterpriseAccountModel = model(AcbEnterpriseAccountModel::class);

        $bankAccountDetails = $this->getBankAccountDetails($data['bank_account_id']);

        if (! $bankAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng không tồn tại']);

        if ($bankAccountDetails->bank_sms)
            return $this->respond(['status' => false, 'message' => 'Phương thức kết nối SMS không thể sử dụng tính năng này']);

        $acbEnterpriseAccountDetails = $acbEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->first();

        if (!$acbEnterpriseAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng ACB cá nhân không được sử dụng tính năng này']);

        $bankSubAccountDetails = $this->getBankSubAccountDetails($data['sub_account_id'], $bankAccountDetails->id);

        if (! $bankSubAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản VA không tồn tại']);

        try {
            $client = new AcbClient;
        } catch (Exception $e) {
            return $this->handleAcbClientException($e);
        }

        try {
            $virtualAccountInfo = null;

            $response = $client->cancelPushNotification(
                'ORG',
                $bankAccountDetails->account_number,
                $bankAccountDetails->phone_number,
                [
                    'virtualAccountPrefixCode' => substr($bankSubAccountDetails->sub_account, 0, 3),
                    'virtualAccountNumber' => $bankSubAccountDetails->sub_account,
                ],
                $acbEnterpriseAccountDetails->username
            );

            $responseData = json_decode($response->getBody());

            return $this->response->setJSON([
                'status' => true,
                'message' => $this->getSentOtpAlert($responseData->responseData->authorizationType),
                'otp_request' => [
                    'request_id' => $responseData->responseData->requestId,
                    'authorization_id' => $responseData->responseData->authorizationId,
                    'authorization_type' => $responseData->responseData->authorizationType,
                ]
            ]);
        } catch (Exception $e) {
            $errorCode = $e->getCode();

            if (in_array($errorCode, ['********']))
                return $this->respond(['status' => false, 'message' => "Quý khách vui lòng thử lại sau ít phút nữa"]);

            return $this->handleAcbClientException($e);
        }
    }

    public function ajax_confirm_delete_enterprise_real_va()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (! has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền xóa tài khoản VA']);

        $data = [
            'bank_account_id' => trim(xss_clean($this->request->getVar('bank_account_id'))),
            'sub_account_id' => trim(xss_clean($this->request->getVar('sub_account_id'))),
            'request_id' => trim($this->request->getVar('request_id')),
            'authorization_id' => trim($this->request->getVar('authorization_id')),
            'otp' => trim($this->request->getVar('otp')),
        ];

        $rules = [
            'request_id' => ['required'],
            'authorization_id' => ['required'],
            'otp' => ['required'],
        ];

        if (! $this->validateData($data, $rules))
            return $this->fail($this->validator->getErrors());

        $bankAccountModel = model(BankAccountmodel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $acbEnterpriseAccountModel = model(AcbEnterpriseAccountModel::class);

        $bankAccountDetails = $this->getBankAccountDetails($data['bank_account_id']);

        if (! $bankAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng không tồn tại']);

        if ($bankAccountDetails->bank_sms)
            return $this->respond(['status' => false, 'message' => 'Phương thức kết nối SMS không thể sử dụng tính năng này']);

        $acbEnterpriseAccountDetails = $acbEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->first();

        if (!$acbEnterpriseAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng ACB cá nhân không được sử dụng tính năng này']);

        $bankSubAccountDetails = $this->getBankSubAccountDetails($data['sub_account_id'], $bankAccountDetails->id);

        if (! $bankSubAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản VA không tồn tại']);

        try {
            $client = new AcbClient;
        } catch (Exception $e) {
            return $this->handleAcbClientException($e);
        }

        try {
            $virtualAccountInfo = null;

            $response = $client->verifyOtpNotification(
                $data['request_id'],
                $data['authorization_id'],
                $data['otp']
            );

            $bankSubAccountModel->delete($bankSubAccountDetails->id);

            return $this->response->setJSON(['status' => true]);
        } catch (Exception $e) {
            $errorCode = $e->getCode();

            if (in_array($errorCode, ['********', '********'])) {
                return $this->fail(['otp' => 'OTP không chính xác hoặc đã hết hiệu lực.']);
            }

            return $this->handleAcbClientException($e);
        }
    }

    public function ajax_request_switch_sms_connection()
    {
        if (!$this->acbConfig->allowedSwitchSmsConnection)
            return $this->failNotFound();

        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn chưa được cấp quyền để thực hiện thao tác này']);

        $bankAccountId = trim(xss_clean($this->request->getPost('id')));
        $bankAccountDetails = $this->getSwitchableSmsConnectionBankAccount($bankAccountId);

        if (!is_object($bankAccountDetails))
            return $this->response->setJSON(['status' => false, 'message' => 'Tài khoản ngân hàng không đủ điều kiện']);

        if (!$this->determineIfAbleSwitchToSmsConnection())
            return $this->response->setJSON(['status' => false, 'message' => 'Gói dịch vụ của bạn không cho phép kết nối Ngân hàng qua SMS báo số dư. Vui lòng nâng cấp gói hoặc liên hệ SePay để được hỗ trợ.']);

        return $this->response->setJSON(['status' => true]);
    }

    public function ajax_confirm_switch_sms_connection()
    {
        if (!$this->acbConfig->allowedSwitchSmsConnection)
            return $this->failNotFound();

        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn chưa được cấp quyền để thực hiện thao tác này']);

        $bankAccountId = trim(xss_clean($this->request->getPost('id')));
        $bankAccountDetails = $this->getSwitchableSmsConnectionBankAccount($bankAccountId);

        if (!is_object($bankAccountDetails))
            return $this->response->setJSON(['status' => false, 'message' => 'Tài khoản ngân hàng không đủ điều kiện']);

        if (!$this->determineIfAbleSwitchToSmsConnection())
            return $this->response->setJSON(['status' => false, 'message' => 'Gói dịch vụ của bạn không cho phép kết nối Ngân hàng qua SMS báo số dư. Vui lòng nâng cấp gói hoặc liên hệ SePay để được hỗ trợ.']);

        $bankAccountModel = model(BankAccountModel::class);
        $simCompanyModel = model(SimCompanyModel::class);

        $simId = trim(xss_clean($this->request->getPost('sim_id')));
        $bankAccountUpdateData = [
            'bank_sms' => 1,
            'bank_sms_connected' => 0,
            'bank_api' => 0,
            'bank_api_connected' => 0
        ];

        if (is_numeric($simId)) {
            $simDetails = $simCompanyModel->select('tb_autopay_sim.id')
                ->join('tb_autopay_sim', 'tb_autopay_sim.id=tb_autopay_sim_company.sim_id')
                ->where([
                    'tb_autopay_sim_company.company_id' => $this->user_session['company_id'],
                    'tb_autopay_sim.active' => 1,
                    'tb_autopay_sim.id' => $simId
                ])
                ->get()->getRow();

            if (!is_object($simDetails))
                return $this->response->setJSON(['status' => false, 'message' => 'SIM nhận SMS mà bạn chọn không khả dụng']);

            $bankAccountUpdateData['sim_id'] = $simId;
        }

        $bankAccountUpdated = $bankAccountModel->set($bankAccountUpdateData)->where('id', $bankAccountId)->update();

        if (!$bankAccountUpdated)
            return $this->response->setJSON(['status' => false, 'message' => 'Không thể chuyển đổi phương thức kết nối sang SMS Banking, vui lòng liên hệ SePay để được hỗ trợ.']);

        add_user_log(['data_id' => $bankAccountId, 'company_id' => $this->user_session['company_id'], 'data_type' => 'bank_account_update', 'description' => 'Chuyển đổi phương thức kết nối sang SMS Banking', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);
        return $this->response->setJSON(['status' => true]);
    }

    public function ajax_request_switch_api_connection()
    {
        if (!$this->acbConfig->allowedSwitchApiConnection)
            return $this->failNotFound();

        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn chưa được cấp quyền để thực hiện thao tác này']);

        $bankAccountId = trim(xss_clean($this->request->getPost('id')));
        $bankAccountDetails = $this->getSwitchableApiConnectionBankAccount($bankAccountId);

        if (!is_object($bankAccountDetails))
            return $this->response->setJSON(['status' => false, 'message' => 'Tài khoản ngân hàng không đủ điều kiện']);

        $bankAccountModel = model(BankAccountModel::class);

        $session = session();
        $session->set('acb_request_switch_api_connection', $bankAccountDetails->id);
        $redirectTo = base_url('acb/step1');

        if ($this->request->getVar('is_enterprise')) {
            $redirectTo .= '?is_enterprise=1';
        }

        return $this->response->setJSON(['status' => true, 'redirect_to' => $redirectTo]);
    }

    public function ajax_request_delete_individual_bank_account()
    {
        if (!$this->acbConfig->allowedDeleteBankAccount)
            return $this->failNotFound();

        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!has_permission('BankAccount', 'can_delete'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn chưa được cấp quyền để thực hiện thao tác này']);

        $acbEnterpriseAccountModel = model(AcbEnterpriseAccountModel::class);

        $bankAccountId = trim(xss_clean($this->request->getPost('id')));
        $bankAccountDetails = $this->getBankAccountDetails($bankAccountId);

        if (!is_object($bankAccountDetails))
            return $this->response->setJSON(['status' => false, 'message' => 'không tìm thấy tài khoản ngân hàng']);

        $acbEnterpriseAccountDetails = $acbEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->first();

        if ($acbEnterpriseAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng ACB doanh nghiệp không được sử dụng tính năng này']);

        $bankSubAccountModel = model(BankSubAccountModel::class);

        $deletable =  $bankSubAccountModel->where(['acc_type' => 'Real', 'bank_account_id' => $bankAccountId])->countAllResults() === 0;

        if (!$deletable)
            return $this->response->setJSON(['status' => false, 'message' => 'Quý khách vui lòng xóa toàn bộ tài khoản ảo (VA) chính thức từ ngân hàng trước khi thực hiện xóa tài khoản chính']);

        try {
            $client = new AcbClient;
        } catch (Exception $e) {
            return $this->handleAcbClientException($e);
        }

        try {
            $virtualAccountInfo = null;

            $response = $client->cancelPushNotification(
                'PERS',
                $bankAccountDetails->account_number,
                $bankAccountDetails->phone_number,
                $virtualAccountInfo,
                null
            );

            $responseData = json_decode($response->getBody());

            return $this->response->setJSON([
                'status' => true,
                'message' => $this->getSentOtpAlert($responseData->responseData->authorizationType),
                'otp_request' => [
                    'request_id' => $responseData->responseData->requestId,
                    'authorization_id' => $responseData->responseData->authorizationId,
                ]
            ]);
        } catch (Exception $e) {
            $errorCode = $e->getCode();

            if ($errorCode == '2010008')
                return $this->respond(['status' => false, 'message' => 'Số tài khoản không tồn tại hoặc đã bị khóa trên hệ thống ngân hàng ACB.']);

            if (in_array($errorCode, ['2010005', '1020002']))
                return $this->respond(['status' => false, 'message' => 'Số điện thoại không được đăng ký cho tài khoản trên, vui lòng liên hệ SePay để được hỗ trợ.']);

            if (in_array($errorCode, ['********']))
                return $this->respond(['status' => false, 'message' => "Quý khách vui lòng thử lại sau ít phút nữa"]);

            if (in_array($errorCode, ['********']))
                return $this->respond(['status' => false, 'message' => 'Tên đăng nhập ACBO không được đăng ký cho tài khoản trên.']);

            return $this->handleAcbClientException($e);
        }
    }

    public function ajax_confirm_delete_individual_bank_account()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!has_permission('BankAccount', 'can_delete'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền xóa tài khoản ngân hàng']);

        $data = [
            'bank_account_id' => trim(xss_clean($this->request->getVar('bank_account_id'))),
            'request_id' => trim($this->request->getVar('request_id')),
            'authorization_id' => trim($this->request->getVar('authorization_id')),
            'otp' => trim($this->request->getVar('otp')),
        ];

        $rules = [
            'bank_account_id' => ['required'],
            'request_id' => ['required'],
            'authorization_id' => ['required'],
            'otp' => ['required'],
        ];

        if (! $this->validateData($data, $rules))
            return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng tải lại trang']);

        $bankAccountModel = model(BankAccountmodel::class);
        $acbBankAccountMetaDataModel = model(AcbBankAccountMetaDataModel::class);
        $acbEnterpriseAccountModel = model(AcbEnterpriseAccountModel::class);

        $bankAccountDetails = $this->getBankAccountDetails($data['bank_account_id']);

        if (! $bankAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng không tồn tại']);

        $acbEnterpriseAccountDetails = $acbEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->first();

        if ($acbEnterpriseAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng ACB doanh nghiệp không được sử dụng tính năng này']);

        try {
            $client = new AcbClient;
        } catch (Exception $e) {
            return $this->handleAcbClientException($e);
        }

        try {
            $virtualAccountInfo = null;

            $response = $client->verifyOtpNotification(
                $data['request_id'],
                $data['authorization_id'],
                $data['otp']
            );

            $acbBankAccountMetaDataModel->where('bank_account_id', $bankAccountDetails->id)->delete();

            DeleteCompanyBankAccountAction::run($bankAccountDetails, $this->company_details, $this->request->getIPAddress(), self::class);

            add_user_log(['data_id' => $bankAccountDetails->id, 'company_id' => $this->user_session['company_id'], 'data_type' => 'bank_account_delete', 'description' => 'Xóa tài khoản ngân hàng ' . $bankAccountDetails->account_number, 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);

            set_alert('success', 'Đã hủy liên kết và xóa tài khoản ngân hàng thành công');

            return $this->response->setJSON(['status' => true]);
        } catch (Exception $e) {
            $errorCode = $e->getCode();

            if (in_array($errorCode, ['********', '********'])) {
                return $this->fail(['otp' => 'OTP không chính xác hoặc đã hết hiệu lực.']);
            }

            return $this->handleAcbClientException($e);
        }
    }

    public function ajax_request_delete_enterprise_bank_account()
    {
        if (!$this->acbConfig->allowedDeleteBankAccount || $this->acbConfig->enterpriseApiConnectionManually)
            return $this->failNotFound();

        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!has_permission('BankAccount', 'can_delete'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn chưa được cấp quyền để thực hiện thao tác này']);

        $acbEnterpriseAccountModel = model(AcbEnterpriseAccountModel::class);

        $bankAccountId = trim(xss_clean($this->request->getPost('id')));
        $bankAccountDetails = $this->getBankAccountDetails($bankAccountId);

        if (!is_object($bankAccountDetails))
            return $this->response->setJSON(['status' => false, 'message' => 'không tìm thấy tài khoản ngân hàng']);

        $acbEnterpriseAccountDetails = $acbEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->first();

        if (!$acbEnterpriseAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng ACB cá nhân không được sử dụng tính năng này']);

        $bankSubAccountModel = model(BankSubAccountModel::class);

        $deletable =  $bankSubAccountModel->where(['acc_type' => 'Real', 'bank_account_id' => $bankAccountId])->countAllResults() === 0;

        if (!$deletable)
            return $this->response->setJSON(['status' => false, 'message' => 'Quý khách vui lòng xóa toàn bộ tài khoản ảo (VA) trước khi thực hiện xóa tài khoản chính']);

        try {
            $client = new AcbClient;
        } catch (Exception $e) {
            return $this->handleAcbClientException($e);
        }

        try {
            $virtualAccountInfo = null;

            $response = $client->cancelPushNotification(
                'ORG',
                $bankAccountDetails->account_number,
                $bankAccountDetails->phone_number,
                $virtualAccountInfo,
                $acbEnterpriseAccountDetails->username,
            );

            $responseData = json_decode($response->getBody());

            return $this->response->setJSON([
                'status' => true,
                'message' => $this->getSentOtpAlert($responseData->responseData->authorizationType),
                'otp_request' => [
                    'request_id' => $responseData->responseData->requestId,
                    'authorization_id' => $responseData->responseData->authorizationId,
                ]
            ]);
        } catch (Exception $e) {
            $errorCode = $e->getCode();

            if ($errorCode == '2010008')
                return $this->respond(['status' => false, 'message' => 'Số tài khoản không tồn tại hoặc đã bị khóa trên hệ thống ngân hàng ACB.']);

            if (in_array($errorCode, ['2010005', '1020002']))
                return $this->respond(['status' => false, 'message' => 'Số điện thoại không được đăng ký cho tài khoản trên, vui lòng liên hệ SePay để được hỗ trợ.']);

            if (in_array($errorCode, ['********']))
                return $this->respond(['status' => false, 'message' => "Quý khách vui lòng thử lại sau ít phút nữa"]);

            if (in_array($errorCode, ['********']))
                return $this->respond(['status' => false, 'message' => 'Tên đăng nhập ACBO không được đăng ký cho tài khoản trên.']);

            return $this->handleAcbClientException($e);
        }
    }

    public function ajax_confirm_delete_enterprise_bank_account()
    {
        if (!$this->acbConfig->allowedDeleteBankAccount || $this->acbConfig->enterpriseApiConnectionManually)
            return $this->failNotFound();

        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!has_permission('BankAccount', 'can_delete'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn không có quyền xóa tài khoản ngân hàng']);

        $data = [
            'bank_account_id' => trim(xss_clean($this->request->getVar('bank_account_id'))),
            'request_id' => trim($this->request->getVar('request_id')),
            'authorization_id' => trim($this->request->getVar('authorization_id')),
            'otp' => trim($this->request->getVar('otp')),
        ];

        $rules = [
            'bank_account_id' => ['required'],
            'request_id' => ['required'],
            'authorization_id' => ['required'],
            'otp' => ['required'],
        ];

        if (! $this->validateData($data, $rules))
            return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng tải lại trang']);

        $bankAccountModel = model(BankAccountmodel::class);
        $acbBankAccountMetaDataModel = model(AcbBankAccountMetaDataModel::class);
        $acbEnterpriseAccountModel = model(AcbEnterpriseAccountModel::class);

        $bankAccountDetails = $this->getBankAccountDetails($data['bank_account_id']);

        if (! $bankAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng không tồn tại']);

        $acbEnterpriseAccountDetails = $acbEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->first();

        if (!$acbEnterpriseAccountDetails)
            return $this->respond(['status' => false, 'message' => 'Tài khoản ngân hàng ACB cá nhân không được sử dụng tính năng này']);

        try {
            $client = new AcbClient;
        } catch (Exception $e) {
            return $this->handleAcbClientException($e);
        }

        try {
            $virtualAccountInfo = null;

            $response = $client->verifyOtpNotification(
                $data['request_id'],
                $data['authorization_id'],
                $data['otp']
            );

            $acbBankAccountMetaDataModel->where('bank_account_id', $bankAccountDetails->id)->delete();
            $acbEnterpriseAccountModel->where('id', $acbEnterpriseAccountDetails->id)->delete();

            DeleteCompanyBankAccountAction::run($bankAccountDetails, $this->company_details, $this->request->getIPAddress(), self::class);

            add_user_log(['data_id' => $bankAccountDetails->id, 'company_id' => $this->user_session['company_id'], 'data_type' => 'bank_account_delete', 'description' => 'Xóa tài khoản ngân hàng ' . $bankAccountDetails->account_number, 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);

            set_alert('success', 'Đã hủy liên kết và xóa tài khoản ngân hàng thành công');

            return $this->response->setJSON(['status' => true]);
        } catch (Exception $e) {
            $errorCode = $e->getCode();

            if (in_array($errorCode, ['********', '********'])) {
                return $this->fail(['otp' => 'OTP không chính xác hoặc đã hết hiệu lực.']);
            }

            return $this->handleAcbClientException($e);
        }
    }

    public function ajax_send_enterprise_connection_request()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!$this->acbConfig->allowedEnterpriseConnection || !$this->acbConfig->enterpriseApiConnectionManually)
            return show_404();

        if (!has_permission('BankAccount', 'can_add'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn chưa được cấp quyền để thực hiện thao tác này']);

        if (! $this->validate([
            'company_name' => 'required|min_length[5]|max_length[100]',
            'has_account' => 'required',
            'phone_number' => 'required|min_length[10]|max_length[20]',
            'notes' => 'permit_empty'
        ])) return $this->fail($this->validator->getErrors());

        $data = [
            'company_name' => trim($this->request->getVar('company_name')),
            'phone_number' => trim($this->request->getVar('phone_number')),
            'has_account' => $this->request->getVar('has_account'),
            'notes' => trim($this->request->getVar('notes'))
        ];

        $message = '
------------------------------

Có yêu cầu kết nối API ACB doanh nghiệp mới:

#️⃣ Tên cá nhân/tổ chức: ' . $data['company_name'] . '

ℹ️ Đã có tài khoản ACB: ' . ($data['has_account'] ? 'Đã có' : 'Chưa') . '

📞 Số điện thoại liên hệ: ' . $data['phone_number'] . '

⏰ Thực hiện lúc: ' . date("Y-m-d H:i:s") . '

✍🏻 Ghi chú: ' . $data['notes'] . '

------------------------------
        ';

        $telegramQueueModel = model(NotificationTelegramQueueModel::class);
        $telegramQueueModel->insert([
            'chat_id' => $this->acbConfig->telegramChatId,
            'status' => 'Pending',
            'message' => $message
        ]);

        return $this->response->setJSON([
            'status' => true,
            'message' => 'SePay đã nhận được yêu cầu của bạn và sẽ liên hệ lại trong thời gian sớm nhất',
        ]);
    }

    public function ajax_connect_enterprise_manually()
    {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!is_admin()) {
            return show_404();
        }

        if (!$this->acbConfig->allowedEnterpriseConnection || !$this->acbConfig->enterpriseApiConnectionManually)
            return show_404();

        if (!has_permission('BankAccount', 'can_add'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn chưa được cấp quyền để thực hiện thao tác này']);

        if (! $this->validate([
            'account_number' => [
                'label' => 'số tài khoản',
                'rules' => 'required|max_length[250]',
            ],
            'account_holder_name' => 'required|max_length[250]',
            'phone_number' => 'required|max_length[11]',
            'username' => 'required|max_length[250]',
            'vac' => ['permit_empty', 'regex_match[/^([A-Z]{3}|(0[A-Z0-9]{2}))$/]'],
            'label' => 'permit_empty|max_length[100]'
        ])) return $this->fail($this->validator->getErrors());

        $bankAccountModel = model(BankAccountModel::class);
        $acbEnterpriseAccountModel = model(AcbEnterpriseAccountModel::class);
        $vac = trim(xss_clean(strtoupper($this->request->getVar('vac'))));
        $accountNumber = trim(xss_clean($this->request->getVar('account_number')));
        $phoneNumber = trim(xss_clean($this->request->getVar('phone_number')));
        $username = trim(xss_clean($this->request->getVar('username')));

        if ($bankAccountModel->where(['bank_id' => Acb::BANK_ID, 'account_number' => $accountNumber])->countAllResults()) {
            return $this->fail(['account_number' => 'Số tài khoản đã tồn tại.']);
        }

        if ($vac && $acbEnterpriseAccountModel->where('vac', $vac)->countAllResults()) {
            return $this->fail(['vac' => 'Mã đầu định danh đã tồn tại.']);
        }

        $bankAccountModel->skipApplyReferral();
        $bankAccountId = $bankAccountModel->insert([
            'company_id' => $this->user_session['company_id'],
            'account_holder_name' => remove_accents(trim(xss_clean($this->request->getVar('account_holder_name'))), true),
            'account_number' => $accountNumber,
            'bank_id' => Acb::BANK_ID,
            'label' => trim(xss_clean($this->request->getVar('label'))),
            'active' => 1,
            'bank_api' => 1,
            'bank_api_connected' => 1,
            'phone_number' => $phoneNumber
        ]);

        if ($bankAccountId) {
            $acbEnterpriseAccountModel->insert(['bank_account_id' => $bankAccountId, 'vac' => $vac ? $vac : null, 'username' => $username]);

            add_user_log(['data_id' => $bankAccountId, 'company_id' => $this->user_session['company_id'], 'data_type' => 'bank_account_add', 'description' => 'Thêm tài khoản ngân hàng ACB API', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);
            return $this->response->setJSON(['status' => true, 'id' => $bankAccountId]);
        }

        add_user_log(['data_id' => 0, 'company_id' => $this->user_session['company_id'], 'data_type' => 'bank_account_add', 'description' => 'Thêm tài khoản ngân hàng ACB API', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Failed']);
        return $this->response->setJSON(['status' => false, 'message' => 'Không thể thêm tài khoản ngân hàng. Vui lòng liên hệ SePay để được hỗ trợ.']);
    }

    public function ajax_switch_enterprise_api_connection_manually()
    {
        if (!$this->acbConfig->allowedSwitchApiConnection)
            return show_404();

        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!has_permission('BankAccount', 'can_edit'))
            return $this->response->setJSON(['status' => false, 'message' => 'Bạn chưa được cấp quyền để thực hiện thao tác này']);

        $bankAccountId = trim(xss_clean($this->request->getPost('bank_account_id')));
        $bankAccountDetails = $this->getSwitchableApiConnectionBankAccount($bankAccountId);

        if (!is_object($bankAccountDetails))
            return $this->response->setJSON(['status' => false, 'message' => 'Tài khoản ngân hàng không đủ điều kiện']);

        $bankAccountModel = model(BankAccountModel::class);
        $acbEnterpriseAccountModel = model(AcbEnterpriseAccountModel::class);
        $isEnterpriseAccountDetails = $acbEnterpriseAccountModel->where(['bank_account_id' => $bankAccountDetails->id])->first();

        if (!$isEnterpriseAccountDetails) {
            if (! $this->validate([
                'account_holder_name' => 'required|max_length[250]',
                'phone_number' => 'required|max_length[11]',
                'username' => 'required|max_length[250]',
                'vac' => ['permit_empty', 'regex_match[/^([A-Z]{3}|(0[A-Z0-9]{2}))$/]'],
                'label' => 'permit_empty|max_length[100]'
            ])) return $this->fail($this->validator->getErrors());

            $vac = trim(xss_clean(strtoupper($this->request->getVar('vac'))));
            $accountHolderName = trim(xss_clean($this->request->getVar('account_holder_name')));
            $phoneNumber = trim(xss_clean($this->request->getVar('phone_number')));
            $username = trim(xss_clean($this->request->getVar('username')));
            $label = trim(xss_clean($this->request->getVar('label')));

            if ($vac && $acbEnterpriseAccountModel->where('vac', $vac)->countAllResults()) {
                return $this->fail(['vac' => 'Mã đầu định danh đã tồn tại.']);
            }

            $acbEnterpriseAccountModel->insert(['bank_account_id' => $bankAccountDetails->id, 'vac' => $vac ? $vac : null, 'username' => $username]);
        } else {
            $accountHolderName = $bankAccountDetails->account_holder_name;
            $phoneNumber = $bankAccountDetails->phone_number;
            $label = $bankAccountDetails->label;
        }

        $updated = $bankAccountModel
            ->set([
                'bank_api_connected' => 1,
                'bank_api' => 1,
                'bank_sms' => 0,
                'accumulated' => 0,
                'account_holder_name' => $accountHolderName,
                'phone_number' => $phoneNumber,
                'label' => $label
            ])->update($bankAccountDetails->id);

        if ($updated) {
            add_user_log(['data_id' => $bankAccountDetails->id, 'company_id' => $this->user_session['company_id'], 'data_type' => 'bank_account_add', 'description' => 'Chuyển phương thức kết nối sang API', 'user_id' => $this->user_details->id, 'ip' => $this->request->getIPAddress(), 'user_agent' => $this->request->getUserAgent()->getAgentString(), 'status' => 'Success']);

            set_alert('success', 'Chuyển đổi phương thức kết nối thành công');

            return $this->respond(['status' => true, 'id' => $bankAccountDetails->id]);
        }

        return $this->respond(['status' => false]);
    }

    protected function getSwitchableApiConnectionBankAccount($id)
    {
        return model(BankAccountModel::class)->where([
            'id' => $id,
            'company_id' => $this->user_session['company_id'],
            'bank_id' => Acb::BANK_ID,
            'bank_sms' => 1,
            'bank_api' => 0,
            'bank_api_connected' => 0,
            'active' => 1,
        ])->get()->getRow();
    }

    protected function getSwitchableSmsConnectionBankAccount($id)
    {
        return model(BankAccountModel::class)->where([
            'id' => $id,
            'company_id' => $this->user_session['company_id'],
            'bank_id' => Acb::BANK_ID,
            'bank_sms' => 0,
            'bank_api' => 1,
            'active' => 1,
        ])->get()->getRow();
    }

    protected function determineIfAbleSwitchToSmsConnection()
    {
        $subscriptionDetails = model(CompanySubscriptionModel::class)->where(['company_id' => $this->user_session['company_id']])->get()->getRow();
        $productDetails = model(ProductModel::class)->where(['id' => $subscriptionDetails->plan_id])->get()->getRow();

        return is_object($subscriptionDetails) && $productDetails->sms_allow == 1;
    }

    protected function getSentOtpAlert($authorizationType)
    {
        return $authorizationType === 'SMS'
            ? 'Đã gửi OTP đến số điện thoại'
            : 'Đã gửi OTP đến ACB ONE';
    }

    protected function getBankAccountDetails($id)
    {
        $bankAccountModel = model(BankAccountModel::class);

        return $bankAccountModel
            ->select('tb_autopay_bank_account.id, tb_autopay_bank_account.account_number, tb_autopay_bank_account.label, tb_autopay_bank_account.active, tb_autopay_bank_account.created_at, tb_autopay_bank.full_name, tb_autopay_bank.brand_name, tb_autopay_bank_account.account_number, tb_autopay_bank_account.account_holder_name, tb_autopay_bank.icon_path, tb_autopay_bank.logo_path, tb_autopay_sim.sim_phonenumber, tb_autopay_bank_account.last_transaction, tb_autopay_bank_account.accumulated,tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.bank_id,tb_autopay_bank_account.bank_sms, tb_autopay_bank_account.bank_sms_connected, tb_autopay_bank_account.bank_api, tb_autopay_bank_account.bank_api_connected, tb_autopay_bank_account.identification_number, tb_autopay_bank_account.phone_number, tb_autopay_bank_account.sim_id, tb_autopay_acb_bank_account_metadata.realtime_transfer_type, tb_autopay_bank_account.merchant_id')
            ->join('tb_autopay_bank', 'tb_autopay_bank.id=tb_autopay_bank_account.bank_id')
            ->join('tb_autopay_sim', 'tb_autopay_sim.id=tb_autopay_bank_account.sim_id', 'left')
            ->join('tb_autopay_acb_bank_account_metadata', 'tb_autopay_acb_bank_account_metadata.bank_account_id = tb_autopay_bank_account.id', 'left')
            ->where([
                'tb_autopay_bank_account.company_id' => $this->user_session['company_id'],
                'tb_autopay_bank_account.id' => $id,
                'tb_autopay_bank_account.bank_id' => Acb::BANK_ID
            ])->first();
    }

    protected function getBankSubAccountDetails($id, $bankAccountId = null)
    {
        $bankSubAccountModel = model(BankSubAccountModel::class);

        $builder = $bankSubAccountModel
            ->select('tb_autopay_bank_sub_account.id, tb_autopay_bank_sub_account.sub_account, tb_autopay_bank_sub_account.label, tb_autopay_bank_sub_account.active, tb_autopay_bank_sub_account.va_active, tb_autopay_bank_sub_account.sub_holder_name, tb_autopay_bank_sub_account.created_at')
            ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id')
            ->where([
                'tb_autopay_bank_account.company_id' => $this->user_session['company_id'],
                'tb_autopay_bank_sub_account.id' => $id,
                'tb_autopay_bank_account.bank_id' => Acb::BANK_ID
            ]);

        if ($bankAccountId) {
            $builder->where(['tb_autopay_bank_account.id' => $bankAccountId]);
        }

        return $builder->first();
    }
}
