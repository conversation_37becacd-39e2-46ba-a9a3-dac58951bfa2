<?php

namespace App\Controllers;

use App\Models\SimModel;
use App\Models\BankModel;
use App\Models\ShopModel;
use App\Models\AddonModel;
use App\Models\CouponModel;
use App\Models\CompanyModel;
use App\Models\CounterModel;
use App\Models\ProductModel;
use App\Models\CouponLogModel;
use App\Models\SimCompanyModel;
use App\Actions\BankAccount\DisableAllBankAccountAction;
use App\Actions\BankAccount\EnableAllBankAccountAction;
use App\Libraries\TaxCodeLookup;
use App\Models\BankAccountModel;
use App\Models\CompanyUserModel;
use App\Models\TransactionsModel;
use App\Models\ConfigurationModel;
use App\Models\BankSubAccountModel;
use App\Models\CompanySubscriptionModel;
use App\Models\CreditLogModel;
use App\Models\CompanySubscriptionChangeModel;
use App\Models\InvoiceItemModel;
use App\Models\InvoiceModel;
use App\Models\OrderModel;
use App\Models\ProductPromotionModel;
use CodeIgniter\API\ResponseTrait;
use App\Actions\Company\ApplyCreditToInvoiceAction;
use App\Config\Invoice;
use App\Features\ShopBilling\ShopBillingFeature;
use App\Models\BankBonusModel;
use Config\Billing;
use Exception;
use App\Models\CompanyPromotionHistoryModel;
use App\Models\InvoiceCustomerInfoModel;
use App\Models\ZNSNotificationModel;
use CodeIgniter\HTTP\Response;
use App\Features\PaymentGateway\PaymentGatewayFeature;
use App\Models\PgAgreementModel;

class Company extends BaseController
{
    use ResponseTrait;
    
    public function profile()
    { 
        $data = [
            'page_title' => 'Hồ sơ công ty',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        if(!in_array($this->company_details->role,['Admin', 'SuperAdmin']))
            show_404();

        $bankAccountModel = model(BankAccountModel::class);
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $companyUserModel = model(CompanyUserModel::class);

        $data['count_bank_accounts'] = $bankAccountModel->where(['company_id' => $this->user_session['company_id']])->countAllResults();
        $data['count_bank_sub_accounts'] = $bankSubAccountModel->join("tb_autopay_bank_account","tb_autopay_bank_account.id=tb_autopay_bank_sub_account.bank_account_id")->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id']])->countAllResults();

        $data['count_users'] = $companyUserModel->where(['company_id' => $this->user_session['company_id']])->countAllResults();
        if($this->channel_partner){
            $data['count_shop_used'] = $this->getChannelPartnerShop();
        }

        echo theme_view('templates/autopay/header',$data);
        echo theme_view('company/profile',$data);
        echo theme_view('templates/autopay/footer',$data);

    } 

    public function ajax_get_profile() {
        if(!in_array($this->company_details->role,['Admin', 'SuperAdmin']))
            show_404();

        $companyModel = model(CompanyModel::class);
        $company_info = $companyModel->select('full_name, short_name')->where(['id' => $this->user_session['company_id']])->get()->getRow();

        if(!is_object($company_info))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Không tim thấy thông tin"));
        
         
        return $this->response->setJSON(["status"=>TRUE, "data"=>$company_info]);
         
    }

    public function ajax_profile_update() {

        if(!in_array($this->company_details->role,['Admin', 'SuperAdmin']))
            show_404();

        if ($this->request->getMethod(true) != 'POST')
            return '';

        if($this->user_session['company_id'] == 5) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Tính năng không khả dụng ở tài khoản Demo"));
        }


        $companyModel = model(CompanyModel::class);
        $company_info = $companyModel->where(['id' => $this->user_session['company_id']])->get()->getRow();

        if(!is_object($company_info))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Không tim thấy thông tin"));
        
        $validation =  \Config\Services::validation();

        if(! $this->validate([
            'full_name' => ['label' => 'Tên công ty', "rules" => "required|max_length[200]"],
            'short_name' =>['label' => 'Tên gọi', "rules" => "required|max_length[20]"],
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }

        $companyModel->set(['full_name' => xss_clean($this->request->getPost('full_name')),'short_name' => xss_clean($this->request->getPost('short_name'))])->where('id', $this->user_session['company_id'])->update();

        add_user_log(array('data_id'=>$this->user_session['company_id'],'company_id' => $this->user_session['company_id'], 'data_type'=>'company_change_info','description'=>'Đổi thông tin công ty','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));


        return $this->response->setJSON(["status"=>TRUE]);

    }

    public function configuration() {
        if(!in_array($this->company_details->role,['Admin', 'SuperAdmin']))
            show_404();

        $data = [
            'page_title' => 'Cấu hình',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        $companyModel = model(CompanyModel::class);

        $companyModel = model(CompanyModel::class);
        $company_info = $companyModel->where(['id' => $this->user_session['company_id']])->get()->getRow();

        if(!is_object($company_info))
            show_404();
        

        echo theme_view('templates/autopay/header',$data);
        echo theme_view('company/configuration',$data);
        echo theme_view('templates/autopay/footer',$data);
    }
   

    public function ajax_configuration_update() {

        if(!in_array($this->company_details->role,['Admin', 'SuperAdmin']))
            show_404();

        if ($this->request->getMethod(true) != 'POST')
            return '';
 
        $companyModel = model(CompanyModel::class);
        $company_info = $companyModel->where(['id' => $this->user_session['company_id']])->get()->getRow();

        if(!is_object($company_info))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Không tim thấy thông tin công ty"));
        
        $validation =  \Config\Services::validation();

        if(! $this->validate([
            'BankSubAccount' => ['label' => 'Tài khoản phụ ngân hàng', "rules" => "required|in_list[on,off]"],
            'PayCode' => ['label' => 'Nhận diện mã thanh toán', "rules" => "required|in_list[on,off]"],
            //'DataStorageTime' => ['label' => 'Thời gian lưu dữ liệu', "rules" => "required|in_list[1Hour,8Hours,1Day,3Days,7Days,1Month,3Months,6Months,1Years,2Years,3Years]"],
            'DataStorageTime' => ['label' => 'Thời gian lưu dữ liệu', "rules" => "required|in_list[1Hour,8Hours,1Day,3Days,7Days,1Month,3Months,6Months]"],
            'PayCodeStructures' => ['label' => 'Cấu trúc mã thanh toán', 'rules' => 'required'],
            'PayCodeStructures.*.is_active' => ['label' => 'Kích hoạt', 'rules' => 'permit_empty|in_list[on,off]'],
            'PayCodeStructures.*.prefix' => ['label' => 'Tiền tố mã thanh toán', 'rules' => 'required|alpha|min_length[2]|max_length[5]'],
            'PayCodeStructures.*.suffix_from' => ['label' => 'Hậu tố mã thanh toán từ', 'rules' => 'required|is_natural|less_than_equal_to[30]|greater_than_equal_to[1]'],
            'PayCodeStructures.*.suffix_to' => ['label' => 'Hậu tố mã thanh toán đến', 'rules' => 'required|is_natural|less_than_equal_to[30]|greater_than_equal_to[1]'],
            'PayCodeStructures.*.character_type' => ['label' => 'Loại ký tự hậu tố', 'rules' => 'required|in_list[NumberOnly,NumberAndLetter]'],
            'ZNSNotificationEnabled' => ['label' => 'Nhận thông báo ZNS', 'rules' => 'required|in_list[on,off]'],
            'default_phone' => ['label' => 'Số điện thoại mặc định', 'rules' => 'permit_empty'],
            'accounting_phone' => ['label' => 'Số điện thoại khác', 'rules' => 'permit_empty'],
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }

        $payCodeStructures = $this->request->getPost('PayCodeStructures');

        if (! is_array($payCodeStructures)) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Cấu trúc mã thanh toán không hợp lệ'
            ]);
        }

        foreach ($payCodeStructures as $index => $structure) {
            $index++;
            $suffixFrom = intval($structure['suffix_from']);
            $suffixTo = intval($structure['suffix_to']);
    
            if ($suffixFrom > $suffixTo) {
                return $this->response->setJSON([
                    'status' => false,
                    'message' => 'Hậu tố mã thanh toán từ phải nhỏ hơn hoặc bằng hậu tố mã thanh toán đến (Cấu trúc mã thanh toán ' . $index . ')'
                ]);
            }
        }

        $configurationModel = model(ConfigurationModel::class);

        $configurationModel->set([
            'value' => $this->request->getPost('BankSubAccount'),
        ])->where(['company_id' => $this->user_session['company_id'],'setting' => 'BankSubAccount'])->update();

        $configurationModel->set([
            'value' => $this->request->getPost('PayCode'),
        ])->where(['company_id' =>  $this->user_session['company_id'],'setting' => 'PayCode'])->update();

        $oldPayCodeStructure = $configurationModel->where('company_id', $this->user_session['company_id'])->where('setting', 'PayCodeStructures')->first();
        
        if ($oldPayCodeStructure) {
            $oldPayCodeStructure = json_decode($oldPayCodeStructure->value, true);
        }

        // Đảm bảo rằng các cấu trúc mã thanh toán mới được tạo nếu chúng chưa tồn tại, fallback cho người dùng với các phiên bản cũ hơn của hệ thống.
        if (
            ! $configurationModel
                ->where('company_id', $this->user_session['company_id'])
                ->where('setting', 'PayCodeStructures')
                ->first()
        ) {
            $configurationModel->insert([
                'company_id' => $this->user_session['company_id'],
                'setting' => 'PayCodeStructures',
                'value' => json_encode($payCodeStructures),
            ]);
        } else {
            $configurationModel
                ->set('value', json_encode($payCodeStructures))
                ->where('company_id', $this->user_session['company_id'])
                ->where('setting', 'PayCodeStructures')
                ->update();
        }

        $this->updateWebhooks(model(\App\Models\HaravanModel::class), $this->user_session['company_id'], $oldPayCodeStructure, $payCodeStructures);
        $this->updateWebhooks(model(\App\Models\ShopifyModel::class), $this->user_session['company_id'], $oldPayCodeStructure, $payCodeStructures);
        $this->updateWebhooks(model(\App\Models\SapoModel::class), $this->user_session['company_id'], $oldPayCodeStructure, $payCodeStructures);

        $new_data_storage_config = $this->request->getPost('DataStorageTime');

        $result = $configurationModel->where(['company_id' => $this->user_session['company_id'], 'setting' => 'DataStorageTime'])->get()->getRow();

        if($new_data_storage_config != $result->value) {
            $configurationModel->set([
                'value' => $new_data_storage_config,
            ])->where(['company_id'=>  $this->user_session['company_id'],'setting' => 'DataStorageTime'])->update();
    
            add_user_log(array('data_id'=>$this->user_session['company_id'],'company_id' => $this->user_session['company_id'], 'data_type'=>'company_DataStorageTime_change_configuration','description'=>'Đổi cấu hình Lưu dữ liệu từ ' . $result->value . ' sang ' . $new_data_storage_config,'user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));

        }
         
        add_user_log(array('data_id'=>$this->user_session['company_id'],'company_id' => $this->user_session['company_id'], 'data_type'=>'company_change_configuration','description'=>'Đổi cấu hình công ty','user_id'=> $this->user_details->id,'ip'=>$this->request->getIPAddress(), 'user_agent'=>$this->request->getUserAgent()->getAgentString(),'status'=>'Success'));

        $configurationModel->set([
            'value' => $this->request->getPost('ZNSNotificationEnabled'),
        ])->where(['company_id' => $this->user_session['company_id'],'setting' => 'ZNSNotificationEnabled'])->update();

        $znsNotificationModel = model(ZNSNotificationModel::class);
        $znsData = $znsNotificationModel->where(['company_id' => $this->user_session['company_id']])->get()->getRow();

        if (!$znsData) {
            $znsNotificationModel->insert([
                'company_id' => $this->user_session['company_id'],
                'default_phone' => $this->request->getPost('default_phone'),
                'accounting_phone' => $this->request->getPost('accounting_phone'),
                'active_phone_type' => $this->request->getPost('active_phone_type') ?? 'default'
            ]);
        } else {
            $znsNotificationModel->set([
                'default_phone' => $this->request->getPost('default_phone'),
                'accounting_phone' => $this->request->getPost('accounting_phone'),
                'active_phone_type' => $this->request->getPost('active_phone_type') ?? 'default'
            ])->where(['company_id' => $this->user_session['company_id']])->update();
        }

        return $this->response->setJSON(["status"=>TRUE]);

    }

    protected function updateWebhooks($model, $companyId, $oldStructure, $newStructure)
    {
        if (empty($companyId) || empty($oldStructure)) {
            return;
        }

        $oldPayCode = array_filter(array_column($oldStructure, 'prefix'));

        if (empty($oldPayCode)) {
            return;
        }

        $webhooks = $model
            ->where('company_id', $companyId)
            ->whereIn('paycode_prefix', $oldPayCode)
            ->findAll();

        if (empty($webhooks)) {
            return;
        }

        foreach ($webhooks as $webhook) {
            if (! $webhook->paycode_prefix || ! $webhook->id) {
                continue;
            }

            $prefixIndex = array_search($webhook->paycode_prefix, $oldPayCode);

            if ($prefixIndex !== false) {
                $newPrefix = $newStructure[$prefixIndex]['prefix'] ?? null;
            } else {
                $newPrefix = null;
            }
 
            if ($newPrefix) {
                $model
                    ->set('paycode_prefix', $newPrefix)
                    ->where('id', $webhook->id)
                    ->update();
            }
        }
    }

    public function plans() {
        $data = [
            'page_title' => 'Gói dịch vụ',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
        ];
        
        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])) {
            show_404();
        }

        $productModel = model(ProductModel::class);

        $companySubscriptionModel = model(CompanySubscriptionModel::class);
        $data['subscription_details'] = $companySubscriptionModel->where(['company_id' => $this->user_session['company_id']])->get()->getRow();
        

        $begin_date = date("Y-m-01 00:00:00");
        $end_date = date("Y-m-t 23:59:59");


        if(is_object($data['subscription_details'])) {
            $data['plan_details'] = $productModel->where(['id' => $data['subscription_details']->plan_id])->get()->getRow();

            $bankBonusModel = model(BankBonusModel::class);
            $data['bank_bonus'] = $bankBonusModel->select('tb_autopay_bank.brand_name, tb_autopay_bank.icon_path, tb_autopay_bank_bonus.account_number, tb_autopay_bank_bonus.bank_bonus')
            ->join('tb_autopay_bank', 'tb_autopay_bank.id = tb_autopay_bank_bonus.bank_id')
            ->where(['tb_autopay_bank_bonus.company_id' => $this->user_session['company_id']])->get()->getResult();

            $count_bank_bonus = $bankBonusModel
            ->where(['company_id' => $this->user_session['company_id']])
            ->selectSum('bank_bonus')
            ->get()
            ->getRow()
            ->bank_bonus ?? 0;

            $configBankBonus = config(\Config\BankBonus::class);

            $data['count_bank_bonus'] = $count_bank_bonus > $configBankBonus->maxBankBonus ? $configBankBonus->maxBankBonus * get_month_by_billing_cycle($data['subscription_details']->billing_cycle) : $count_bank_bonus * get_month_by_billing_cycle($data['subscription_details']->billing_cycle);

            $data['payment_details'] = $companySubscriptionModel->select("tb_autopay_company_subscription.begin_date, tb_autopay_company_subscription.end_date, tb_autopay_order.invoice_id, tb_autopay_invoice.total,tb_autopay_invoice.status as `invoice_status`, tb_autopay_order.status as `order_status`")->join("tb_autopay_order",'tb_autopay_order.id=tb_autopay_company_subscription.order_id')->join("tb_autopay_invoice", "tb_autopay_invoice.id=tb_autopay_order.invoice_id","left")->where(['tb_autopay_company_subscription.company_id' => $this->user_session['company_id']])->get()->getRow();

            $begin_date = $data['payment_details']->begin_date;
            $end_date = $data['payment_details']->end_date;

            $companySubscriptionChangeModel = model(\App\Models\CompanySubscriptionChangeModel::class);
            $data['subscription_change_invoice_details'] = $companySubscriptionChangeModel
                ->join('tb_autopay_order', 'tb_autopay_order.id=tb_autopay_company_subscription_change.order_id')
                ->join('tb_autopay_product', 'tb_autopay_product.id=tb_autopay_company_subscription_change.plan_id')
                ->where(['tb_autopay_company_subscription_change.company_id' => $this->user_session['company_id']])->get()->getRow();
        }

        if ($data['subscription_details'] && $data['subscription_details']->billing_cycle === 'free') {
            $begin_date = date('Y-m-01');
            $end_date = date('Y-m-t');
        }

        $data['begin_date'] = $begin_date;
        $data['end_date'] = $end_date;

        $transactionsModel = model(TransactionsModel::class);
        $counterModel = model(CounterModel::class);

        //$data['count_trans_this_period'] = $transactionsModel->join("tb_autopay_bank_account","tb_autopay_bank_account.account_number=tb_autopay_sms_parsed.account_number")->where(['tb_autopay_bank_account.company_id' => $this->user_session['company_id'], 'transaction_date>=' =>$begin_date, 'transaction_date<=' => $end_date])->countAllResults();

        $result = $counterModel->select("sum(transaction) as `sum_transaction`",FALSE)->where(['company_id' => $this->user_session['company_id'], 'date>=' =>$begin_date, 'date<=' => $end_date])->get()->getRow();
        //$result = $counterModel->selectSum("transaction","sum_transaction")->where(['company_id' => $this->user_session['company_id'], 'date>=' =>$begin_date, 'date<=' => $end_date])->get();
       // $result = $counterModel->selectSum("transaction","sum_transaction")->where(['company_id' => 248, 'date>=' =>"2023-11-01 00:00:00", 'date<=' => "2023-11-30 23:59:59"])->get()->getRow();

       $bankModel = model(BankModel::class);

        $apiBrandNames = ['TPBank', 'ACB', 'Vietinbank', 'MBBANK', 'OCB', 'KienLongBank', 'BIDV', 'MSB', 'VPBank'];
        $data['bank_sms'] = $bankModel->whereNotIn('brand_name', $apiBrandNames)->get()->getResult();
        $data['bank_api'] = $bankModel->whereIn('brand_name', $apiBrandNames)->get()->getResult();

        $productModel = model(ProductModel::class);
        if ($this->channel_partner) {
            $data['plans'] = $this->getChannelPartnerPlans();
            $data['count_shop_used'] = $this->getChannelPartnerShop();
        } else {
            $data['plans'] = $productModel->getPlans();
        }

        $data['plans_free'] = $productModel->getFreePlan();
        $data['plans_api'] = $productModel->getApiPlans();
        $data['plans_sms'] = $productModel->getSmsPlans();

        $vpbankPro = [];

        foreach($data['plans_api'] as $key => $plan) {
            if ($plan->name == 'VPBank PRO') {
                $vpbankProPromotion = model(ProductPromotionModel::class)
                    ->where('product_id', $plan->id)
                    ->where('active', true)
                    ->where('start_date <=', date('Y-m-d'))
                    ->where('end_date >=', date('Y-m-d'))
                    ->first();

                if ($vpbankProPromotion && ! model(CompanyPromotionHistoryModel::class)->hasUsedPromotion($this->user_session['company_id'], $vpbankProPromotion->id, $plan->id)) {
                    $vpbankPro = $plan;
                    $vpbankPro->promotion = $vpbankProPromotion;
                    
                    unset($data['plans_api'][$key]);

                    break;
                }
            }
        }
        
        $data['vpbankPro'] = $vpbankPro;


        if(is_object($result) && is_numeric($result->sum_transaction))
            $data['count_trans_this_period'] = $result->sum_transaction;
        else
            $data['count_trans_this_period'] = 0;
            
        $data['billing_type'] = trim(xss_clean($this->request->getGet('type')));
        
        if (!$data['billing_type'] || !in_array($data['billing_type'], ['transaction', 'speaker', 'promotion', 'shop'])) {
            $data['billing_type'] = 'transaction';
        }

        $data['plans_api'] = array_values($data['plans_api']);

        echo theme_view('templates/autopay/header',$data);

        if (is_object($data['subscription_details'])) {
            if ($this->shop_billing) {
                $shopModel = model(ShopModel::class);
                $data['used_shop_count'] = $shopModel->where(['company_id' => $this->company_details->id])->countAllResults();

                echo theme_view('company/my_plans', $data);
            } else {
                $data['total_transaction_limit'] = $data['subscription_details']->monthly_transaction_limit * get_month_by_billing_cycle($data['subscription_details']->billing_cycle);
                $data['bonus_transactions'] = get_bonus_transactions($this->company_details->id) * get_month_by_billing_cycle($data['subscription_details']->billing_cycle);
                echo theme_view('company/my_plans',$data);
            }
        } else {
            switch($data['billing_type']) {
                case 'transaction':
                    $view = 'company/plans';
                    break;
                case 'speaker':
                    if (! service('speakerBillingFeature')->enabled) {
                        return redirect()->to(base_url('company/plans'));
                    }

                    $banks = model(BankModel::class)
                        ->select('icon_path, brand_name')
                        ->where('invididual_api_connection', true)
                        ->where('active', true)
                        ->findAll();
                    $data['banks'] = $banks;
                    $view = 'company/speaker-billing-pricing';
                    break;
                case 'promotion':
                    $data['plans'] = $productModel->getPromotionPlans();

                    $view = 'company/promotion-billing-pricing';
                    break;
                case 'shop':
                    $data['shop_billing_feature'] = new ShopBillingFeature();
                    $data['shop_billing_feature']->withCompanyContext($this->user_session['company_id']);


                    $addonModel = model(AddonModel::class);

                    $data['plans_api'] = $productModel->where(['hidden' => 0, 'sms_allow' => 0, 'billing_type !=' => 'Free', 'price_monthly >' => 0, 'channel_partner_id' => null, 'shop_limit >' => 0])->orderBy('sort_order', 'ASC')->get()->getResult();

                    foreach ($data['plans_api'] as &$plan) {
                        $plan->addon = $addonModel->where(['product_id' => $plan->id])->first();
                    }

                    $view = 'theme/shop-billing/company/plan';
                    break;
                default:
                    $view = 'company/plans';
                    break;
            }

            echo theme_view($view, $data);
        }

        echo theme_view('templates/autopay/footer',$data);
    }

    public function cart() {
        $data = [
            'page_title' => 'Đơn hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])) {
            show_404();
        }

        $product_id = $this->request->getGet('product_id');

        $companySubscriptionModel = model(CompanySubscriptionModel::class);
        $couponModel = model(CouponModel::class);

        $companySubscriptionChangeModel = model(CompanySubscriptionChangeModel::class);
        $hasSubscriptionChange = $companySubscriptionChangeModel->where(['company_id' => $this->user_session['company_id']])->countAllResults();

        if ($hasSubscriptionChange) {
            set_alert('error', 'Bạn đang có hóa đơn đổi gói dịch vụ, vui lòng thanh toán hoặc hủy đổi gói dịch vụ!');

            return redirect()->to(base_url('company/plans'));
        }

        $data['subscription_details'] = $companySubscriptionModel
            ->select('tb_autopay_company_subscription.*, tb_autopay_product.name as product_name, tb_autopay_product.billing_type as product_billing_type')
            ->join('tb_autopay_product', 'tb_autopay_product.id=tb_autopay_company_subscription.plan_id')
            ->join('tb_autopay_order', 'tb_autopay_order.id=tb_autopay_company_subscription.order_id')
            ->where(['tb_autopay_company_subscription.company_id' => $this->user_session['company_id']])->get()->getRow();

        if ($data['subscription_details'] && $data['subscription_details']->pg_agreement_id) {
            $pgAgreement = model(PgAgreementModel::class)->find($data['subscription_details']->pg_agreement_id);

            if ($pgAgreement && $pgAgreement->auto_renew) {
                set_alert('error', 'Bạn đang đăng ký gói gia hạn tự động, vui lòng hủy trước khi đổi gói!');
                return redirect()->to(base_url('company/plans'));
            }
        } 

        if($this->request->getPost('billing_cycle'))
            $data['billing_cycle'] = $this->request->getPost('billing_cycle');
        else if($this->request->getGet('billing_cycle'))
            $data['billing_cycle'] = $this->request->getGet('billing_cycle');
        else
            $data['billing_cycle'] = "monthly";

        $promotion = null;
        $promotionId = xss_clean($this->request->getGet('promotion_id'));

        if (is_numeric($promotionId)) {
            $promotion = model(ProductPromotionModel::class)
                ->where('product_id', $product_id)
                ->where('active', true)
                ->where('start_date <=', date('Y-m-d'))
                ->where('end_date >=', date('Y-m-d'))
                ->where('billing_cycle', $data['billing_cycle'])
                ->find($promotionId);

            if ($promotion) {
                if (model(CompanyPromotionHistoryModel::class)->hasUsedPromotion($this->user_session['company_id'], $promotion->id, $product_id)) {
                    set_alert('error', 'Bạn đã từng sử dụng gói dịch vụ này. Mỗi công ty chỉ được đăng ký 1 lần duy nhất.');
                    return redirect()->to(base_url('company/plans?type=promotion'));
                }

                $hasBankAccounts = !! model(BankAccountModel::class)->where('company_id', $this->user_session['company_id'])->countAllResults();

                if ($hasBankAccounts) {
                    set_alert('error', 'Vui lòng gỡ liên kết các tài khoản ngân hàng trước khi đăng ký gói khuyến mãi này.');
                    return redirect()->back();
                }
            }
        }

        if (! $promotion) {
            if (! in_array($data['billing_cycle'], ['monthly','annually'])) {
                $data['billing_cycle'] = "monthly";
            }
        }

        $data['promotion'] = $promotion;

        if(!is_numeric($product_id))
            return redirect()->to(base_url('company/plans'));

        $productModel = model(ProductModel::class);

        $conditions = [
            'id' => $product_id,
            'active' => 1,
        ];
        
        if (isset($this->channel_partner['id'])) {
            $conditions['channel_partner_id'] = $this->channel_partner['id'];
        }else{
            $conditions['channel_partner_id'] = null;
        }
        
        $data['product_details'] = $productModel
            ->where('id !=', config(Billing::class)->speakerBillingDefaultProductId)
            ->where($conditions)->get()->getRow();

        if(!is_object($data['product_details']))
            return redirect()->to(base_url('company/plans'));

        $shopBillingFeature = new ShopBillingFeature;
        $shopBillingFeature->withCompanyContext($this->company_details->id);
        $data['shop_billing_feature'] = &$shopBillingFeature;

        if ($data['product_details']->shop_limit > 0 && !$shopBillingFeature->enabled) {
            return redirect()->to(base_url('company/plans'));
        }

        if (is_object($data['subscription_details']) && !$data['subscription_details']->shop_limit
        && $data['subscription_details']->plan_id == $data['product_details']->id
        && ($data['subscription_details']->billing_cycle == $data['billing_cycle'] || $data['subscription_details']->billing_cycle == 'free')) {
            set_alert('error', 'Bạn đang sử dụng gói dịch vụ này, vui lòng chọn một gói dịch vụ khác!');
            
            if ($this->request->getMethod(true) != 'POST') {
                return redirect()->to(base_url('company/change_plan' . ($this->shop_billing ? '?type=shop' : '')));
            } else {
                return $this->response->setJSON(array('status'=>FALSE,'redirect_to'=>base_url('company/change_plan' . ($this->shop_billing ? '?type=shop' : ''))));
            }
        }

        $data['is_shop_billing_product'] = false;
        $data['product_details']->addon = null;
        $data['shop_count'] = null;
        $data['shop_addon_limit'] = null;
        $data['is_change_shop_count'] = false;
        $data['change_shop_diff_count'] = null;
        $data['unpurchasable'] = false;
        $data['can_trial'] = false;
        $data['trial_days'] = null;

        $shopCount = $shopBillingFeature->formatValidShopAmount(trim(xss_clean($this->request->getGet('shop_count') ?? 1)));

        try {
            $shopBillingFeature->withOrderContext($data['product_details']->id, $shopCount, $data['billing_cycle']);

            if (!$shopBillingFeature->companyContext()->canAddShopBillingOrder()) {
                set_alert('error', 'Hiện chưa cho phép đổi sang nhóm gói này, vui lòng liên hệ với SePay để được hỗ trợ.');
                return redirect()->to(base_url('company/change_plan'));
            }
        } catch (Exception $e) {
            if ($shopBillingFeature->companyContext()->isShopBillingSubscription()) {
                set_alert('error', 'Hiện chưa cho phép đổi sang nhóm gói khác, vui lòng liên hệ với SePay để được hỗ trợ.');
                return redirect()->to(base_url('company/change_plan'));
            }
        }

        if ($shopBillingFeature->enabled && $shopBillingFeature->companyContext()->canAddShopBillingOrder()) {
            $data['is_shop_billing_product'] = $shopBillingFeature->determineIfOrderForShopBilling();

            if ($data['is_shop_billing_product']) {
                $data['product_details']->addon = $shopBillingFeature->orderContext()->addon;
                $data['shop_count'] = $shopCount;
                $data['shop_addon_limit'] = $shopBillingFeature->shopAmountLimit;
                $data['is_change_shop_count'] = $shopBillingFeature->determineIfOrderIsChangeShopAmount();
                $data['change_shop_diff_count'] = $shopBillingFeature->getChangeShopDiffCount();
                $data['trial_days'] = $shopBillingFeature->trialDays;

                if ($this->request->getGet('is_trial') == 1 && !$shopBillingFeature->companyContext()->canTrial()) {
                    return redirect()->to(base_url('company/plans'));
                }

                $data['can_trial'] = $shopBillingFeature->companyContext()->canTrial()
                    && $this->request->getGet('is_trial') == 1
                    && $data['trial_days'] > 0;

                if ($shopBillingFeature->determineIfOrderIsDecrementShopAmount() && $shopBillingFeature->companyContext()->shopCountInUse() > $data['shop_count']) {
                    if (!$this->request->getGet('get_cart_summary') == 'yes') {
                        set_alert('error', 'Không thể chọn số lượng cửa hàng thấp hơn số cửa hàng đang sử dụng');
                        return redirect()->to(base_url('company/change_plan'));
                    }
                }

                $data['unpurchasable'] = isset($shopCount) && $shopBillingFeature->determineIfOrderIsSameSubscription();
            }
        }

        $addonModel = model(AddonModel::class);

        $data['addons'] = $addonModel->where(['active' => 1, 'hidden' => 0])->get()->getResult();

        $get_cart_summary = $this->request->getGet('get_cart_summary');

        $addons_checked_form = $this->request->getPost('addon');

        $data['addons_checked'] = [];

        if (is_object($data['subscription_details']) && $data['subscription_details']->status == 'Active' && $data['subscription_details']->first_payment > 0) {
            if ($data['subscription_details']->monthly_transaction_limit > 0) {
                $data['subscription_exchange_remaining_money'] = calc_subscription_exchange_remaining_money_by_transaction_count($data['subscription_details']);
            } else {
                $data['subscription_exchange_remaining_money'] = calc_subscription_exchange_remaining_money($data['subscription_details']->begin_date, $data['subscription_details']->end_date, $data['subscription_details']->recurring_payment);
            }
        } else {
            $data['subscription_exchange_remaining_money'] = 0;
        }

        if(is_array($addons_checked_form)) {
            foreach($addons_checked_form as $key => $value) {
                $result = $addonModel->where(['active' => 1, 'hidden' => 0,'id' => $key])->get()->getRow();
                if(is_object($result)) {
                    array_push($data['addons_checked'], $result);
                }
            }
        }

        $addcoupon = $this->request->getGet('addcoupon');
        $data['discount_code'] = FALSE;
        $data['discount_money'] = 0;
        $data['discount_subscription'] = 0;
        $data['coupon_error_message'] = FALSE;

        if($data['billing_cycle'] == 'annually')
            $product_price = $data['product_details']->price_annually * 12;
        else
            $product_price = $data['product_details']->price_monthly;

        $data['original_product_price'] = $product_price;

        if (is_object($data['subscription_details']) && $data['subscription_details']->status == 'Active' && $data['subscription_details']->first_payment > 0) {
            if ($data['subscription_details']->monthly_transaction_limit > 0) {
                $data['discount_subscription'] = calc_subscription_exchange_remaining_money_by_transaction_count($data['subscription_details']);
            } else {
                $data['discount_subscription'] = calc_subscription_exchange_remaining_money($data['subscription_details']->begin_date, $data['subscription_details']->end_date, $data['subscription_details']->recurring_payment);
            }
        }

        if ($shopBillingFeature->determineIfOrderForShopBilling()) {
            $product_price += $shopBillingFeature->orderContext()->addonTotalPriceByBillingCycle();
        }

        $coupon_apply = $this->request->getPost('coupon_apply');
        $data['coupon_apply'] = '';
        if($coupon_apply) {
            $coupon_details = $couponModel->where(['code' => $coupon_apply,'status' => 'Active'])->get()->getRow();

            if(is_object($coupon_details) && ($coupon_details->billing_cycle == 'All' || ($coupon_details->billing_cycle == 'm' && $data['billing_cycle'] == 'monthly'))) {
                if(strlen(trim($coupon_details->products)) > 0) {
                    $products_apply = explode("|",$coupon_details->products);

                    if($coupon_details->products != "All" && !in_array($product_id, $products_apply))
                        $data['coupon_error_message'] = 'Mã giảm giá không áp dụng cho gói dịch vụ này.';
                    else if(strtotime($coupon_details->expires) < time()) {
                        $data['coupon_error_message'] = 'Mã giảm giá đã hết hạn sử dụng.';
                    } else {
                        $data['discount_code'] = $coupon_apply;
                        if($coupon_details->type == "Percent") {
                            if($data['discount_subscription'] > 0)
                                $data['discount_money'] = (intval($product_price) - intval($data['discount_subscription'])) * ($coupon_details->value/100);
                            else
                                $data['discount_money'] = intval($product_price * ($coupon_details->value/100));
                        } else {
                            if($coupon_details->value > $product_price - $data['discount_subscription'])
                                $data['discount_money'] = $product_price - $data['discount_subscription'];
                            else
                                $data['discount_money'] = intval($coupon_details->value);
                        }
                    }
                }
               
            } else {
                $data['coupon_error_message'] = 'Mã giảm giá không tồn tại.';

            }
            $data['coupon_apply'] = $coupon_apply;
        }
        $data['credit_apply'] = trim($this->request->getPost('credit_apply'));
        $data['credit_apply'] = !is_numeric($data['credit_apply']) || $data['credit_apply'] < 0 ? 0 : $data['credit_apply'];

        if($get_cart_summary == 'yes') {
            echo theme_view('company/cart_sumary',$data);
        } else {
            echo theme_view('templates/autopay/header',$data);
            echo theme_view('company/cart',$data);
            echo theme_view('templates/autopay/footer',$data);
        }
    }

    public function ordersuccess() {
        $data = [
            'page_title' => 'Thanh toán',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
        ];

        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])) {
            show_404();
        }

        $invoice_id = $this->request->getGet('invoice_id');

        $data['invoice_id'] = $invoice_id;
      
        if(is_numeric($invoice_id) && $invoice_id > 0) {
            $invoiceModel = model(InvoiceModel::class);
            $invoiceItemModel = model(InvoiceItemModel::class);
            
            $data['invoice_details'] = $invoiceModel->where(['id' => $invoice_id, 'company_id' =>$this->user_session['company_id'] ])->get()->getRow();
            
            if(!is_object($data['invoice_details'])) {
                set_alert('error', 'Hóa đơn không tồn tại');
                return redirect()->to('/company/plans');
            }
    
            $data['invoice_items'] = $invoiceItemModel->where(['invoice_id' => $invoice_id])->orderBy('position','ASC')->get()->getResult();
    
            $data['customerInfos'] = model(InvoiceCustomerInfoModel::class)
                ->where('company_id', $this->user_session['company_id'])
                ->findAll();

            if($data['invoice_details']->status == "Unpaid") {
                $data['paycode'] = create_paycode($invoice_id);

                $data['qrcode'] = "https://qr.sepay.vn/img?bank=MBBank&acc=*************&template=&amount=" . intval($data['invoice_details']->total) . "&des=" . $data['paycode'];

                $data['order_details'] = model(OrderModel::class)->where(['invoice_id' => $data['invoice_details']->id, 'company_id' => $data['invoice_details']->company_id])->first();
                
                if (!$data['order_details']) {
                    set_alert('error', 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ');
                    return redirect()->to('/company/plans');
                }
                
                $subscriptionChangeDetails = model(CompanySubscriptionChangeModel::class)
                    ->where('order_id', $data['order_details']->id)
                    ->where('company_id', $data['invoice_details']->company_id)
                    ->first();
                
                if ($subscriptionChangeDetails) {
                    $subscriptionDetails = $subscriptionChangeDetails;
                } else {
                    $subscriptionDetails = model(CompanySubscriptionModel::class)
                        ->where('auto_renew', 1)
                        ->where('order_id', $data['order_details']->id)
                        ->where('company_id', $data['invoice_details']->company_id)
                        ->first();
                }
                    
                if (!$subscriptionDetails) {
                    set_alert('error', 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ');
                    return redirect()->to('/company/plans');
                }
            }

            $canRequestVatInvoice = config(Invoice::class)->vatInvoiceRequestEnabled && $data['invoice_details']->total > 0;

            $data['canRequestVatInvoice'] = $canRequestVatInvoice;

            echo theme_view('templates/autopay/header',$data);
            echo theme_view('company/ordersuccess',$data);
            echo theme_view('templates/autopay/footer',$data);
    
        } else {
            echo theme_view('templates/autopay/header',$data);
            echo theme_view('company/ordersuccess_free',$data);
            echo theme_view('templates/autopay/footer',$data);
        }

        
       

       
    }

    public function paymentsuccess() {
        $data = [
            'page_title' => 'Thanh toán',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session
        ];

        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])) {
            show_404();
        }

        echo view('templates/autopay/header',$data);
        echo view('company/paymentsuccess',$data);
        echo view('templates/autopay/footer',$data);
    }

    public function ajax_add_order() {

        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])) {
            show_404();
        }

        helper('general');

        if (is_speaker_billing_subscription() && ! can_switch_plan_from_speaker_billing()) {
            show_404();
        }

        $validation =  \Config\Services::validation();

        if(! $this->validate([
            'billing_cycle' => ['label' => 'Chu kỳ thanh toán', "rules" => "required"],
            'product_id' => ['label' => 'Gói dịch vụ', "rules" => "integer|required"],
            'promotion_id' => ['label' => 'Mã khuyến mãi', "rules" => "permit_empty|integer"],
            'payment_method' => ['label' => 'Hình thức thanh toán', 'rules' => ['permit_empty', 'in_list[BankTransfer,Card]']],
        ])) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>implode(". ", $validation->getErrors())));
        }

        $productModel = model(ProductModel::class);
        $addonModel = model(AddonModel::class);
        $invoiceModel = model(InvoiceModel::class);
        $invoiceItemModel = model(InvoiceItemModel::class);
        $orderModel = model(OrderModel::class);
        $companySubscriptionModel = model(CompanySubscriptionModel::class);
        $couponModel = model(CouponModel::class);
        $companyModel = model(CompanyModel::class);
        $creditLogModel = model(CreditLogModel::class);

        $product_id = $this->request->getVar('product_id');
        $product_details = $productModel
            ->where('id !=', config(Billing::class)->speakerBillingDefaultProductId)
            ->where(['id' =>  $product_id,'active' => 1,'hidden' => 0])->get()->getRow();
        
        if(!is_object($product_details))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Gói dịch vụ này không tồn tại"));

        if ($product_details->shop_limit > 0) {
            $product_details->addon = $addonModel->where(['active' => 1, 'hidden' => 0, 'product_id' => $product_details->id])->first();
        }

        $subscription_details = $companySubscriptionModel
            ->select('tb_autopay_company_subscription.*, tb_autopay_product.name as product_name, tb_autopay_product.billing_type as product_billing_type')
            ->join('tb_autopay_product', 'tb_autopay_product.id=tb_autopay_company_subscription.plan_id')
            ->join('tb_autopay_order', 'tb_autopay_order.id=tb_autopay_company_subscription.order_id')
            ->where(['tb_autopay_company_subscription.company_id' => $this->user_session['company_id']])->get()->getRow();

            if ($subscription_details && $subscription_details->pg_agreement_id) {
                $pgAgreement = model(PgAgreementModel::class)->find($subscription_details->pg_agreement_id);

                if ($pgAgreement && $pgAgreement->auto_renew) {
                    return $this->response->setJSON(['status' => false, 'message' => 'Bạn đang đăng ký gói gia hạn tự động, vui lòng hủy trước khi đổi gói!']);
                }
            }
        $isSubscriptionChange = is_object($subscription_details);
        $billing_cycle = $this->request->getVar('billing_cycle');

        if ($isSubscriptionChange && !$subscription_details->shop_limit && $subscription_details->plan_id == $product_details->id && $subscription_details->billing_cycle == $billing_cycle) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Bạn đang sử dụng gói dịch vụ này, vui lòng chọn một gói dịch vụ khác!"));
        }

        if ($isSubscriptionChange) {
            $billingConfig = config(\App\Config\Billing::class);
            $hasUnpaidInvoice = $invoiceModel->where(['company_id' => $this->user_session['company_id'], 'status' => 'Unpaid'])->countAllResults();

            if ($hasUnpaidInvoice > 0 && !$billingConfig->allowedSubscriptionChangeIfHasUnpaidInvoice) {
                return $this->response->setJSON(['status'=>FALSE,'message'=>"Bạn đang có hóa đơn chưa thanh toán, vui lòng thanh toán trước khi đổi gói dịch vụ!"]);
            }
        }

        $coupon_details = null;
        $promotion = null;
        
        if ($this->request->getPost('promotion_id')) {
            $promotion = model(ProductPromotionModel::class)
                ->where('product_id', $product_id)
                ->where('active', true)
                ->where('start_date <=', date('Y-m-d'))
                ->where('end_date >=', date('Y-m-d'))
                ->where('billing_cycle', $billing_cycle)
                ->find($this->request->getPost('promotion_id'));

            if ($promotion) {
                if (model(CompanyPromotionHistoryModel::class)->hasUsedPromotion($this->user_session['company_id'], $promotion->id, $product_id)) {
                    set_alert('error', 'Bạn đã từng sử dụng gói dịch vụ này. Mỗi công ty chỉ được đăng ký 1 lần duy nhất.');
                    return redirect()->to(base_url('company/plans?type=promotion'));
                }

                $hasBankAccounts = !! model(BankAccountModel::class)->where('company_id', $this->user_session['company_id'])->countAllResults();

                if ($hasBankAccounts) {
                    set_alert('error', 'Vui lòng gỡ liên kết các tài khoản ngân hàng trước khi đăng ký gói khuyến mãi này.');
                    return redirect()->back();
                }
            }
        }

        if (! in_array($billing_cycle, ['monthly','annually']) && ! $promotion) {
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Chu kỳ thanh toán không hợp lệ"));
        }

        $total_sub = 0;
        $total_money = 0;

        $product_tax = 0;
        $total_tax = 0;

        $start_date = date("Y-m-d");

        if ($promotion) {
            $product_subtotal = $promotion->compare_at_price;
            $end_date = date("Y-m-d", strtotime(get_strtotime_by_billing_cycle($billing_cycle)));
        } else {
            if($billing_cycle == "annually")  {
                $product_subtotal = $product_details->price_annually * 12;
                $end_date = date("Y-m-d", strtotime('+1 year'));
            } else {
                $product_subtotal = $product_details->price_monthly;
                $end_date = date("Y-m-d", strtotime('+1 month'));
            }
        }

        $end_date = date("Y-m-d", strtotime('-1 day', strtotime($end_date)));

        if ($isSubscriptionChange && $subscription_details->status == 'Active' && $subscription_details->first_payment > 0) {
            if ($subscription_details->monthly_transaction_limit > 0) {
                $discount_subscription = calc_subscription_exchange_remaining_money_by_transaction_count($subscription_details);
            } else {
                $discount_subscription = calc_subscription_exchange_remaining_money($subscription_details->begin_date, $subscription_details->end_date, $subscription_details->recurring_payment);
            }
        } else {
            $discount_subscription = 0;
        }

        // Shop addon
        $shopBillingFeature = new ShopBillingFeature;
        $shopBillingFeature->withCompanyContext($this->company_details->id);

        if ($product_details->shop_limit > 0 && property_exists($product_details, 'addon')) {
            $shopCount = $shopBillingFeature->formatValidShopAmount(trim(xss_clean($this->request->getVar('shop_count') ?? 1)));

            if ($shopBillingFeature->determineIfOrderIsSameSubscription()) {
                return $this->response->setJSON(['status' => false, 'message' => 'Bạn đang sử dụng gói dịch vụ này, vui lòng tăng/giảm số lượng cửa hàng để tiếp tục']);
            }

            try {
                $shopBillingFeature->withOrderContext($product_details->id, $shopCount, $billing_cycle);

                if (! $shopBillingFeature->companyContext()->canAddShopBillingOrder()) {
                    return $this->response->setJSON(['status' => false, 'message' => 'Hiện chưa cho phép đổi sang nhóm gói này, vui lòng liên hệ với SePay để được hỗ trợ.']);
                }
            } catch (Exception $e) {
                if ($shopBillingFeature->companyContext()->isShopBillingSubscription()) {
                    return $this->response->setJSON(['status' => false, 'message' => 'Hiện chưa cho phép đổi sang nhóm gói khác, vui lòng liên hệ với SePay để được hỗ trợ.']);
                }
            }

            if ($shopBillingFeature->determineIfOrderForShopBilling()
            && $shopBillingFeature->determineIfOrderIsDecrementShopAmount()
            && $shopBillingFeature->companyContext()->shopCountInUse() > $shopCount) {
                return $this->response->setJSON(['status' => false, 'message' => 'Không thể chọn số lượng cửa hàng thấp hơn số cửa hàng đang sử dụng']);
            }

            $isChangeShopCount = isset($shopCount) && $isSubscriptionChange && $subscription_details->shop_limit
                && $subscription_details->plan_id == $product_details->id
                && $subscription_details->billing_cycle == $billing_cycle;

            $isIncrementShopCount = $isChangeShopCount && $subscription_details->shop_limit < $shopCount;
            $changeShopDiffCount = $isChangeShopCount ? abs($subscription_details->shop_limit - $shopCount) : 0;

            $shopAddonPrice = ($shopCount - 1) * ($billing_cycle === 'annually' ? $product_details->addon->price_annually * 12 : $product_details->addon->price_monthly);

            if (!$isChangeShopCount) {
                $total_sub += $shopAddonPrice;
                $total_money += $shopAddonPrice;
                $product_subtotal += $shopAddonPrice;
            }

            if ($isChangeShopCount && $isIncrementShopCount) {
                $discount_subscription = 0;
                $product_subtotal = $changeShopDiffCount * calc_subscription_exchange_remaining_money($subscription_details->begin_date, $subscription_details->end_date, $billing_cycle == 'annually' ? $product_details->addon->price_annually * 12 : $product_details->addon->price_monthly);
            }

            if ($isChangeShopCount && !$isIncrementShopCount) {
                $discount_subscription = 0;
                $product_subtotal = 0;
            }

            if ($this->request->getGet('is_trial') == 1 && !$shopBillingFeature->companyContext()->canTrial()) {
                return $this->response->setJSON(['status' => false, 'Đã có lỗi xảy ra, vui lòng tải lại trang']);
            }

            $trialDays = $shopBillingFeature->trialDays;
            $canTrial = $shopBillingFeature->companyContext()->canTrial()
                && $this->request->getGet('is_trial') == 1
                && $trialDays > 0;

            if ($canTrial) {
                $end_date = date('Y-m-d', strtotime('+' . $trialDays . ' days'));
            }
        }

        // coupon code
        $discount_money = 0;
        $coupon_apply = $this->request->getPost('coupon_apply');
        if($coupon_apply) {
            $coupon_details = $couponModel->where(['code' => $coupon_apply,'status' => 'Active'])->get()->getRow();

            if(is_object($coupon_details) && ($coupon_details->billing_cycle == 'All' || ($coupon_details->billing_cycle == 'm' && $billing_cycle == 'monthly'))) {
                if(strlen(trim($coupon_details->products)) > 0) {
                    $products_apply = explode("|",$coupon_details->products);

                    if($coupon_details->products == "All" || in_array($product_id, $products_apply) && strtotime($coupon_details->expires) > time())  {

                        if($coupon_details->type == "Percent") {
                            $discount_money = (intval($product_subtotal) - intval($discount_subscription)) * ($coupon_details->value/100);
                        } else {
                            if($coupon_details->value > $product_subtotal - $discount_subscription)
                                $discount_money = $product_subtotal - $discount_subscription;
                            else
                                $discount_money = intval($coupon_details->value);
                        }

                    }
                }
            } 
        }

        if ($promotion) {
            $discount_money = $promotion->compare_at_price;
        }

        $product_subtotal_after_discount = $product_subtotal - $discount_money;
        // coupon code

        if(is_numeric($product_details->tax_rate) && $product_details->tax_rate >0)
            $product_tax = $product_subtotal_after_discount * ($product_details->tax_rate/100);

        if (isset($isChangeShopCount) && $isChangeShopCount && !$isIncrementShopCount) {
            $refund_money = $changeShopDiffCount * calc_subscription_exchange_remaining_money($subscription_details->begin_date, $subscription_details->end_date, $billing_cycle == 'annually' ? $product_details->addon->price_annually * 12 : $product_details->addon->price_monthly);
        } else {
            if ($promotion) {
                $refund_money = $discount_subscription;
            } else {
                $refund_money = $discount_subscription > $product_subtotal ? $discount_subscription - $product_subtotal : 0;
            }
        }

        $discount_subscription = $refund_money > 0 ? $product_subtotal : $discount_subscription;
        $total_money = $product_subtotal_after_discount + $product_tax - $discount_subscription;
        $total_sub = $product_subtotal_after_discount - $discount_subscription;
        $total_tax = $product_tax;
        $total_money = $total_money > 0 ? $total_money : 0;
        $total_sub = $total_sub > 0 ? $total_sub : 0;

        $credit_apply = trim($this->request->getPost('credit_apply'));
        $credit_apply = !is_numeric($credit_apply) || $credit_apply < 0 ? 0 : $credit_apply;

        if ($credit_apply > $this->company_details->credit_balance) {
            $credit_apply = (int) $this->company_details->credit_balance;
        }

        if ($credit_apply > $total_money) {
            $credit_apply = $total_money > 0 ? $total_money : 0;
        }

        if ($product_subtotal_after_discount == 0) {
            $credit_apply = 0;
        }

        $addons_checked_form = $this->request->getPost('addon');
        $addons_checked = [];

        if(is_array($addons_checked_form)) {
            foreach($addons_checked_form as $key => $value) {
                $result = $addonModel->where(['active' => 1, 'hidden' => 0,'id' => $key])->get()->getRow();
                if(is_object($result)) {
                    array_push($addons_checked, $result);

                    $this_addon_tax = 0;
                    
                    if($billing_cycle == "annually") 
                        $this_addon_price =  $result->price_annually * 12;  
                    else  
                        $this_addon_price =  $result->price_monthly;


                    if(is_numeric($result->tax_rate) && $result->tax_rate > 0)
                        $this_addon_tax = $this_addon_price * ($result->tax_rate/100);
            
                    $total_money =  $total_money + $this_addon_price + $this_addon_tax;
                    $total_sub = $total_sub + $this_addon_price;
                    $total_tax = $total_tax + $this_addon_tax;
                }
            }
        }

        $first_payment =  $product_subtotal_after_discount;
        $recurring_payment = $product_subtotal;

        if ($shopBillingFeature->enabled && isset($canTrial) && $canTrial) {
            $product_details->billing_type = 'Free';
            $total_money = 0;
        }

        if (is_object($subscription_details) && $subscription_details->status == 'Active' && $subscription_details->first_payment > 0) {
            if ($subscription_details->monthly_transaction_limit) {
                $subscription_exchange_remaining_money = calc_subscription_exchange_remaining_money_by_transaction_count($subscription_details);
            } else {
                $subscription_exchange_remaining_money = calc_subscription_exchange_remaining_money($subscription_details->begin_date, $subscription_details->end_date, $subscription_details->recurring_payment);
            }
        } else {
            $subscription_exchange_remaining_money = 0;
        }

        // Dont create invoice if product is free
        if(($product_details->billing_type != "Free" && !$isSubscriptionChange)
        || ($isSubscriptionChange && $discount_subscription < $product_subtotal))  {
             // create invoice
            $data_invoice = [
                'status' => 'Unpaid',
                'type' => $isSubscriptionChange ? 'SubscriptionChange' : 'NewOrder',
                'company_id' => $this->user_session['company_id'],
                'date' => date("Y-m-d"),
                'duedate' => date('Y-m-d', strtotime('+7 days')),
                'paybefore' => date('Y-m-d', strtotime('+7 days')),
                'subtotal' => round($total_sub, 0),
                'tax' => $total_tax,
                'tax_rate' => $product_details->tax_rate,
                'total' => round($total_money, 0),
            ]; 

            if($total_money == 0)
                $data_invoice['status'] = "Paid";

            $invoice_id = $invoiceModel->insert($data_invoice);

            if(!is_numeric($invoice_id))
                return $this->response->setJSON(array('status'=>FALSE,'message'=>"Có lỗi xảy ra khi tạo hóa đơn. Vui lòng liên hệ SePay để được hỗ trợ"));

            if(is_numeric($product_details->tax_rate) && $product_details->tax_rate >0)
                $is_product_taxed = 1;
            else
                $is_product_taxed = 0;

            $invoiceProductDetails = '';

            if ($product_details->shop_limit > 0 && isset($shopCount) && !$isChangeShopCount) {
                if ($shopBillingFeature->determineIfSamePriceBetweenProductAndAddon()) {
                    $invoiceProductDetails .= sprintf(
                        '<br> <small>%s cửa hàng x %sđ/%s</small>',
                        $shopCount,
                        number_format($billing_cycle === 'annually' ? $product_details->addon->price_annually * 12 : $product_details->addon->price_monthly),
                        $billing_cycle === 'annually' ? 'năm' : 'tháng',
                    );
                } else {
                    $invoiceProductDetails .= sprintf(
                        '<br> <small>Cửa hàng đầu tiên: %sđ/ %s</small>',
                        number_format($billing_cycle === 'annually' ? $product_details->price_annually * 12 : $product_details->price_monthly),
                        $billing_cycle === 'annually' ? 'năm' : 'tháng'
                    );

                    if ($shopCount - 1 > 0) {
                        $invoiceProductDetails .= sprintf(
                            '<br> <small>Cửa hàng tiếp theo: %s x %sđ = %sđ /%s</small>',
                            $shopCount - 1,
                            number_format($billing_cycle === 'annually' ? $product_details->addon->price_annually * 12 : $product_details->addon->price_monthly),
                            number_format($billing_cycle === 'annually' ? $product_details->addon->price_annually * 12 * ($shopCount - 1) : $product_details->addon->price_monthly * ($shopCount - 1)),
                            $billing_cycle === 'annually' ? 'năm' : 'tháng',
                        );
                    }
                }
            }

            if ($product_details->shop_limit > 0 && isset($shopCount) && $isChangeShopCount) {
                $invoiceProductDetails .= sprintf(
                    '<br> <small>%s cửa hàng (%s %s cửa hàng)</small> <br> <small>Hiệu lực từ %s đến %s</small>',
                    $shopCount,
                    $isIncrementShopCount ? 'tăng' : 'giảm',
                    $changeShopDiffCount,
                    date('Y-m-d'),
                    $subscription_details->end_date
                );
            }

            $subscriptionAction = isset($subscription_details)
                ? (($subscription_details->shop_limit > 0 && !$product_details->shop_limit) || (!$subscription_details->shop_limit && $product_details->shop_limit)
                    ? 'change_plan_type'
                    : 'change_plan')
                : 'first_plan';

            // main invoice items for product
            $product_invoice_item = $invoiceItemModel->insert([
                'invoice_id' => $invoice_id,
                'description' => $isSubscriptionChange
                    ? ($subscriptionAction === 'change_plan_type'
                        ? 'Chuyển gói: SePay - ' . $subscription_details->product_name . ' => SePay - ' . $product_details->name  . ' (' . get_text_by_billing_cycle($billing_cycle) . ') (' . $start_date . ' ' . date('H:i:s') . ' - ' . $end_date . ' 23:59:59)'
                        : (isset($isChangeShopCount) && $isChangeShopCount
                            ? sprintf('%s: SePay - %s (%s)', $isIncrementShopCount ? 'Nâng cấp' : 'Hạ cấp', $subscription_details->product_name, get_text_by_billing_cycle($billing_cycle))
                            : 'Nâng cấp/Hạ cấp: SePay - ' . $subscription_details->product_name . ' => SePay - ' . $product_details->name  . ' (' . get_text_by_billing_cycle($billing_cycle) . ') (' . $start_date . ' ' . date('H:i:s') . ' - ' . $end_date . ' 23:59:59)'))
                    : 'SePay - ' . $product_details->name . " (" . $start_date . " - " . $end_date . ")",
                'details' => $invoiceProductDetails,
                'type' => 'Product',
                'item_id' => $product_details->id,
                'amount' => $product_subtotal,
                'position' => 0,
                'start_date' => $start_date,
                'end_date' => $end_date,
                'taxed' => $is_product_taxed,
                'tax_rate' => $product_details->tax_rate,
                'tax' => $product_tax,
            ]);

            if ($discount_subscription > 0) {
                $product_invoice_item_discount = $invoiceItemModel->insert([
                    'invoice_id' => $invoice_id,
                    'description' => 'Khấu trừ từ gói hiện tại: SePay - ' . $subscription_details->product_name . " (" . $subscription_details->begin_date . " - " . $subscription_details->end_date . ")",
                    'type' => 'SubscriptionDiff',
                    'item_id' => $product_details->id,
                    'amount' => '-'.$discount_subscription,
                    'position' => 1,
                    'start_date' => $start_date,
                    'end_date' => $end_date,
                    'taxed' => 0,
                    'tax_rate' => 0,
                    'tax' => 0,
                ]);
            }
            
            if($discount_money > 0) {
                $product_invoice_item_discount = $invoiceItemModel->insert([
                    'invoice_id' => $invoice_id,
                    'description' => 'Giảm giá: SePay - ' . $product_details->name . " (" . $start_date . " - " . $end_date . ")",
                    'type' => 'Discount',
                    'item_id' => $product_details->id,
                    'amount' => '-'.$discount_money,
                    'position' => 2,
                    'start_date' => $start_date,
                    'end_date' => $end_date,
                    'taxed' => 0,
                    'tax_rate' => 0,
                    'tax' => 0,
                ]);

                if(is_object($coupon_details)) {
                    if($coupon_details->cycle == "Recurring") {
                        $recurring_payment = $first_payment;
                    }  
                }
            }

            $i = 1;
            foreach($addons_checked as $addon) {

                if($billing_cycle == "annually") 
                    $addon_amount = $addon->price_annually * 12;  
                else  
                    $addon_amount = $addon->price_annually;


                if(is_numeric($addon->tax_rate) && $addon->tax_rate >0) {
                    $is_addon_taxed = 1;
                    $addon_tax = $addon_amount * ($addon->tax_rate/100);
                }
                else {
                    $is_addon_taxed = 0;
                    $addon_tax = 0;
                }

                $product_invoice_item = $invoiceItemModel->insert([
                    'invoice_id' => $invoice_id,
                    'description' => 'Addon - ' . $addon->name . " (" . $start_date . " - " . $end_date . ")",
                    'type' => 'Addon',
                    'item_id' => $addon->id,
                    'amount' => $addon_amount,
                    'position' => $i,
                    'start_date' => $start_date,
                    'end_date' => $end_date,
                    'taxed' => $is_addon_taxed,
                    'tax_rate' => $addon->tax_rate,
                    'tax' => $addon_tax,
                ]); 

                $i++;
            } 

        } else {
            $invoice_id = 0;
        }

        $order_status = "Pending";
        if(
            $product_details->billing_type == 'Free'
            || ($promotion && $total_money == 0)
        ) {
            $order_status = "Active";
        }

        $order_id = $orderModel->insert([
            'company_id' => $this->user_session['company_id'],
            'invoice_id' => $invoice_id,
            'total' => round($total_money, 0),
            'status' => $order_status,
            'order_ip' => $this->request->getIPAddress()
        ]);

        if(!is_numeric($order_id))
            return $this->response->setJSON(array('status'=>FALSE,'message'=>"Có lỗi xảy ra khi tạo đơn hàng. Vui lòng liên hệ SePay để được hỗ trợ"));

        // create subscription
        if($total_money == 0) {
            $subscription_status = "Active";
            $companyModel->set(['status' => 'Active'])->where(['id' => $this->user_session['company_id']])->update();
        } else {
            $subscription_status = "Pending";
        }

        if ($promotion) {
            model(CompanyPromotionHistoryModel::class)->insert([
                'company_id' => $this->user_session['company_id'],
                'promotion_id' => $promotion->id,
                'product_id' => $product_id,
                'bank_id' => $promotion->bank_id,
                'billing_cycle' => $billing_cycle,
                'start_date' => $start_date,
                'end_date' => $end_date,
            ]);
        }

        if($product_details->billing_type == 'Free')
            $billing_cycle = "free";

        if (isset($isChangeShopCount) && $isChangeShopCount) {
            $productAddonPrice = $billing_cycle == 'annually' ? $product_details->addon->price_annually : $product_details->addon->price_monthly;

            $newSubscriptionData = [
                'company_id' => $this->user_session['company_id'],
                'order_id' => $order_id,
                'plan_id' => $product_id,
                'begin_date' => $subscription_details->begin_date,
                'end_date' => $subscription_details->end_date,
                'first_payment' => $subscription_details->first_payment,
                'recurring_payment' => $subscription_details->recurring_payment + ($isIncrementShopCount ? $changeShopDiffCount * $productAddonPrice : $changeShopDiffCount * $productAddonPrice * -1),
                'billing_cycle' => $subscription_details->billing_cycle,
                'status' => $subscription_status,
                'shop_limit' => $product_details->shop_limit + (isset($shopCount) ? $shopCount - 1 : 0),
            ];
        } else {
            $newSubscriptionData = [
                'company_id' => $this->user_session['company_id'],
                'order_id' => $order_id,
                'plan_id' => $product_id,
                'begin_date' => $start_date,
                'end_date' => $end_date,
                'first_payment' => isset($canTrial) && $canTrial ? 0 : $first_payment,
                'recurring_payment' => isset($canTrial) && $canTrial ? 0 : $recurring_payment,
                'billing_cycle' => $billing_cycle,
                'status' => $subscription_status,
                'monthly_transaction_limit' => $product_details->monthly_transaction_limit + get_bonus_transactions($this->user_session['company_id']),
                'bank_account_limit' => $product_details->bank_account_limit,
                'telegram_intergration_limit' => $product_details->telegram_intergration_limit,
                'webhook_intergration_limit' => $product_details->webhook_intergration_limit,
                'monthly_telegram_send_limit' => $product_details->monthly_telegram_send_limit,
                'monthly_webhooks_send_limit' => $product_details->monthly_webhooks_send_limit,
                'shop_limit' => $product_details->shop_limit + (isset($shopCount) ? $shopCount - 1 : 0),
                'is_trial' => isset($canTrial) && $canTrial ? 1 : 0,
                'auto_renew' => isset($canTrial) && $canTrial ? 0 : 1,
            ];
        }

        $billingConfig = config(\App\Config\Billing::class);
        $simCompanyModel = model(SimCompanyModel::class);

        if ($isSubscriptionChange) {
            $newSubscriptionData['allow_exceed_limit'] = $subscription_details->allow_exceed_limit;
            $companySubscriptionChangeModel = model(\App\Models\CompanySubscriptionChangeModel::class);
            
            if ($total_money > 0) {
                $companySubscriptionChangeModel->insert($newSubscriptionData);
            } else {
                $companySubscriptionModel->set($newSubscriptionData)->update($subscription_details->id);
                $companySubscriptionChangeModel->where(['company_id' => $newSubscriptionData['company_id']])->delete();

                if ($promotion) {
                    DisableAllBankAccountAction::run($this->user_session['company_id']);
                } else {
                    EnableAllBankAccountAction::run($this->user_session['company_id']);
                }
            }
            $subscription_id = $subscription_details->id;
        } else {
            $subscription_id = $companySubscriptionModel->insert($newSubscriptionData);
        }

        $new_subscription_details = $companySubscriptionModel->where(['id' => $subscription_id])->get()->getRow();

        // Cấp SIM nếu discount 100%
        if(is_object($new_subscription_details) && $total_money == 0 && $product_details->sms_allow == 1) {
            $companySubscriptionModel->set(['status' => 'Active'])->update($new_subscription_details->id);
            
            $companyModel->set(['status' => 'Active'])->where(['id' => $new_subscription_details->company_id])->update();

            $orderModel->set(['status' => 'Active'])->where(['id' => $new_subscription_details->id])->update();

            // dedicated sim
            // chỉ cấp sim nếu subscription_details chưa active
            $simModel = model(SimModel::class);
            $simCompanyModel = model(SimCompanyModel::class);

            if(
                (!$isSubscriptionChange && $product_details->dedicated_sim == 1 && $new_subscription_details->status == 'Active')
                || ($isSubscriptionChange && $product_details->dedicated_sim == 1 && $billingConfig->enabledAutoSimConfigForSubscriptionChange)
            ) {
                $sims = $simModel->select("tb_autopay_sim.id ,tb_autopay_sim.sim_phonenumber, tb_autopay_sim.description")->join("tb_autopay_sim_company","tb_autopay_sim_company.sim_id=tb_autopay_sim.id","left")->where("tb_autopay_sim_company.sim_id IS NULL AND tb_autopay_sim.is_shared=0 AND tb_autopay_sim.active=1")->get()->getResult();

                // Nếu đã cấp sim rồi (ví dụ cấp dùng thử) thì không cấp nữa
                $result = $simCompanyModel->where(['company_id' => $new_subscription_details->company_id])->get()->getRow();

                if(!is_object($result) && isset($sims[0]) && is_object($sims[0])) {
                    $simCompanyModel->insert([
                        'company_id' => $new_subscription_details->company_id,
                        'sim_id' => $sims[0]->id
                    ]);
                } else if (!is_object($result) && count($sims) == 0) {
                    notify_sim_config('assign', $this->user_details, $this->company_details, $product_details->name, $newSubscriptionData['billing_cycle']);
                }
            } else if ($isSubscriptionChange && $product_details->dedicated_sim == 1 && !$billingConfig->enabledAutoSimConfigForSubscriptionChange) {
                notify_sim_config('assign', $this->user_details, $this->company_details, $product_details->name, $newSubscriptionData['billing_cycle']);
            }

            if ($isSubscriptionChange) {
                add_system_log(['company_id' => $this->user_session['company_id'], 'data_type' => 'change_subscription_by_user', 'description' => "Change subscription by user from plan #{$subscription_details->plan_id} to #{$new_subscription_details->plan_id}",'level' => 'Info', 'by' => 'Company Controller']);    
            }
        } else if (is_object($new_subscription_details) && $total_money == 0 && $product_details->sms_allow == 0) {
            if ($billingConfig->enabledAutoSimConfigForSubscriptionChange) {
                $simCompanyModel->where(['company_id' => $newSubscriptionData['company_id']])->delete();
            } else {
                $result = $simCompanyModel->where(['company_id' => $new_subscription_details->company_id])->get()->getRow();
                notify_sim_config('unassign', $this->user_details, $this->company_details, $product_details->name, $newSubscriptionData['billing_cycle']);
            }

            if ($isSubscriptionChange) {
                add_system_log(['company_id' => $this->user_session['company_id'], 'data_type' => 'change_subscription_by_user', 'description' => "Change subscription by user from plan #{$subscription_details->plan_id} to #{$new_subscription_details->plan_id}",'level' => 'Info', 'by' => 'Company Controller']);    
            }
        }

        if($discount_money > 0 && is_object($coupon_details)) {
            // add coupon log
            $couponLogModel = model(CouponLogModel::class);
            $couponLogModel->insert([
                'coupon_id' => $coupon_details->id,
                'company_id' => $this->user_session['company_id'],
                'invoice_id' => $invoice_id,
                'date' => date("Y-m-d"),
                'discount' => $discount_money
            ]);
            // update coupon num usage
            $couponModel->set('num_usage', 'num_usage+1', false)->where(['id' => $coupon_details->id])->update();
        }

        if ($refund_money > 0) {
            $companyModel->where(['id' => $this->company_details->id])->set(['credit_balance' => $this->company_details->credit_balance + $refund_money])->update();

            $creditLogModel->insert([
                'company_id' => $this->company_details->id,
                'amount_in' => $refund_money,
                'accumulated' => $this->company_details->credit_balance + $refund_money,
                'description' => 'Hoàn tiền khả dụng khi đổi gói dịch vụ',
                'ip' => $this->request->getIPAddress(),
                'user_agent'=> $this->request->getUserAgent()->getAgentString()
            ]);
        }

        if ($credit_apply > 0) {
            $response = ApplyCreditToInvoiceAction::run(
                $invoice_id,
                $credit_apply,
                $this->user_details->id, $this->request->getIPAddress(), $this->request->getUserAgent()->getAgentString()
            );

            if ($response['status'] == false) {
                set_alert('error', 'Đã có lỗi xảy ra trong quá trình áp dụng tín dụng, vui lòng thử lại');
            }
        }

        if (isset($shopBillingFeature) && isset($canTrial) && $canTrial) {
            $shopBillingFeature->companyContext()->setTrial();
        }

        return $this->response->setJSON(array('status'=>TRUE,'invoice_id' => $invoice_id,'discount' => $discount_money));
    }

    public function ajax_apply_credit_to_invoice()
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }

        if (! in_array($this->company_details->role, ['Admin', 'SuperAdmin'])) {
            return $this->failNotFound();
        }

        $invoiceId = trim(xss_clean($this->request->getVar('invoice_id')));

        if (! is_numeric($invoiceId)) {
            return $this->failNotFound();
        }

        $creditApply = trim(xss_clean($this->request->getVar('credit_apply')));

        try {
            $result = ApplyCreditToInvoiceAction::run(
                $invoiceId,
                $creditApply,
                $this->user_details->id,
                $this->request->getIPAddress(),
                $this->request->getUserAgent()->getAgentString()
            );
        } catch (\Exception $e) {
            log_message('error', $e->getMessage());

            return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ']);
        }

        if (!is_array($result) || (is_array($result && !isset($result['status'])))) {
            return $this->respond(['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ']);
        }

        log_message('error', $result['status']);

        if ($result['status']) {
            set_alert('success', 'Đã áp dụng tín dụng cho hóa đơn.');
        }

        return $this->respond($result);
    }

    public function ajax_cancel_pending_subscription() {

        if ($this->request->getMethod(true) != 'POST')
            return '';

        $subscription_id = $this->request->getPost('subscription_id');

        if(!is_numeric($subscription_id))
            show_404();

        $companySubscriptionModel = model(CompanySubscriptionModel::class);
        $orderModel = model(OrderModel::class);
        $invoiceModel = model(InvoiceModel::class);
        $companyModel = model(CompanyModel::class);

        $subscription_details = $companySubscriptionModel->where(['company_id' => $this->user_session['company_id'], 'id' => $subscription_id])->get()->getRow();

        if($subscription_details->status =="Active") {
            return $this->response->setJSON(["status"=>FALSE, "message" => "Bạn không thể hủy gói dịch vụ đang trạng thái hoạt động"]);

        }
        
        if ($invoiceModel->where('type', 'Excess')->where('company_id', $this->user_session['company_id'])->where('status !=', 'Paid')->countAllResults() > 0) {
            return $this->response->setJSON(["status"=>FALSE, "message" => "Bạn không thể hủy gói dịch vụ đang nợ hóa đơn vượt hạn mức"]);
        }

        if(is_object($subscription_details)) {

            // delete subscription
            $companySubscriptionModel->where(['company_id' => $this->user_session['company_id'], 'id' => $subscription_id])->delete();

            $companyModel->set(['status' => 'Pending'])->where(['id' => $this->user_session['company_id']])->update();

            // cancel order when order is pending
            if(is_numeric($subscription_details->order_id) && $subscription_details->order_id > 0) {
                $order_details = $orderModel->where(['id' => $subscription_details->order_id, 'company_id' => $this->user_session['company_id']])->get()->getRow();
                if(is_object($order_details) && $order_details->status == 'Pending') {
                    $orderModel->set(['status' => 'Cancelled'])->where(['id' => $subscription_details->order_id])->update();

                    // cancel invoice
                    if(is_numeric($order_details->invoice_id) && $order_details->invoice_id > 0)
                    $invoice_details = $invoiceModel->where(['id' => $order_details->invoice_id, 'company_id' => $this->user_session['company_id']])->get()->getRow();

                    if(is_object($invoice_details))
                        $invoiceModel->set(['status' => 'Cancelled'])->where(['id' => $order_details->invoice_id])->update();

                }


            }


        }

        set_alert("success","Hủy gói thành công. Vui lòng chọn gói mới");
        
        return $this->response->setJSON(["status"=>TRUE]);


        
    }

    public function ajax_cancel_subscription_change() {
        if ($this->request->getMethod(true) != 'POST')
            return '';

        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])) {
            show_404();
        }

        $companySubscriptionModelChange = model(CompanySubscriptionChangeModel::class);
        $orderModel = model(OrderModel::class);
        $invoiceModel = model(InvoiceModel::class);
        $companyModel = model(CompanyModel::class);

        $subscription_change_details = $companySubscriptionModelChange->where(['company_id' => $this->user_session['company_id']])->get()->getRow();

        if (!is_object($subscription_change_details))
            show_404();

        $companySubscriptionModelChange->where(['id' => $subscription_change_details->id])->delete();
        $order_details = $orderModel->where(['id' => $subscription_change_details->order_id, 'company_id' => $this->user_session['company_id']])->get()->getRow();
        if(is_object($order_details)) {
            $orderModel->set(['status' => 'Cancelled'])->where(['id' => $subscription_change_details->order_id])->update();

            if(is_numeric($order_details->invoice_id) && $order_details->invoice_id > 0)
                $invoice_details = $invoiceModel->where(['id' => $order_details->invoice_id, 'company_id' => $this->user_session['company_id']])->get()->getRow();

                if(is_object($invoice_details))
                    $invoiceModel->set(['status' => 'Cancelled'])->where(['id' => $order_details->invoice_id])->update();
        }

        set_alert("success","Hủy đổi gói thành công");
        
        return $this->response->setJSON(["status"=>TRUE]);
    }

    public function change_plan() {
        if (is_speaker_billing_subscription() && ! can_switch_plan_from_speaker_billing()) {
            show_404();
        }

        $data = [
            'page_title' => 'Đổi gói',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'user_session' => $this->user_session,
        ];


        if (!in_array($this->company_details->role, ['Admin', 'SuperAdmin'])) {
            show_404();
        }

        $shopBillingFeature = new ShopBillingFeature;
        $shopBillingFeature->withCompanyContext($this->company_details->id);

        $data['shop_billing_feature'] = $shopBillingFeature;

        $billingConfig = config(\Config\Billing::class);

        $data['plan_type'] = $this->shop_billing ? 'shop' : ($this->request->getGet('type') === 'shop' ? 'shop' : 'transaction');

        $invoiceModel = model(InvoiceModel::class);
        $hasUnpaidInvoice = $invoiceModel->where(['company_id' => $this->user_session['company_id'], 'status' => 'Unpaid'])->countAllResults();

        if ($hasUnpaidInvoice > 0 && !$billingConfig->allowedSubscriptionChangeIfHasUnpaidInvoice) {
            set_alert('error', 'Bạn đang có hóa đơn chưa thanh toán, vui lòng thanh toán trước khi đổi gói dịch vụ!');

            return redirect()->to(base_url('invoices'));
        }

        $data['billing_type'] = trim(xss_clean($this->request->getGet('type')));

        $canSwitchBillingTypes = ['transaction', 'promotion'];

        if (can_switch_speaker_plan_from_free_plan()) {
            $canSwitchBillingTypes[] = 'speaker';
        }

        if (!$data['billing_type'] || !in_array($data['billing_type'], $canSwitchBillingTypes)) {
            $data['billing_type'] = 'transaction';
        }

        $companySubscriptionChangeModel = model(CompanySubscriptionChangeModel::class);
        $hasSubscriptionChange= $companySubscriptionChangeModel->where(['company_id' => $this->user_session['company_id']])->countAllResults();

        if ($hasSubscriptionChange) {
            set_alert('error', 'Bạn đang có hóa đơn đổi gói dịch vụ, vui lòng thanh toán hoặc hủy đổi gói dịch vụ!');

            return redirect()->to(base_url('company/plans'));
        }

        $productModel = model(ProductModel::class);

        $companySubscriptionModel = model(CompanySubscriptionModel::class);
        $data['subscription_details'] = $companySubscriptionModel->where(['company_id' => $this->user_session['company_id']])->get()->getRow();

        $begin_date = date("Y-m-01 00:00:00");
        $end_date = date("Y-m-t 23:59:59");

        if(is_object($data['subscription_details'])) {
            $data['plan_details'] = $productModel->where(['id' => $data['subscription_details']->plan_id])->get()->getRow();
            $data['payment_details'] = $companySubscriptionModel->select("tb_autopay_company_subscription.begin_date, tb_autopay_company_subscription.end_date, tb_autopay_order.invoice_id, tb_autopay_invoice.total,tb_autopay_invoice.status as `invoice_status`, tb_autopay_order.status as `order_status`")->join("tb_autopay_order",'tb_autopay_order.id=tb_autopay_company_subscription.order_id')->join("tb_autopay_invoice", "tb_autopay_invoice.id=tb_autopay_order.invoice_id","left")->where(['tb_autopay_company_subscription.company_id' => $this->user_session['company_id']])->get()->getRow();

            $begin_date = $data['payment_details']->begin_date;
            $end_date = $data['payment_details']->end_date;
        }

        if (! $end_date) {
            $end_date = date("Y-m-t 23:59:59");
        }

        if ($data['subscription_details'] && $data['subscription_details']->pg_agreement_id) {
            $pgAgreement = model(PgAgreementModel::class)->find($data['subscription_details']->pg_agreement_id);

            if ($pgAgreement && $pgAgreement->auto_renew) {
                set_alert('error', 'Bạn đang đăng ký gói gia hạn tự động, vui lòng hủy trước khi đổi gói!');
                return redirect()->to(base_url('company/plans'));
            }
        }

        $transactionsModel = model(TransactionsModel::class);
        $counterModel = model(CounterModel::class);

        $result = $counterModel->select("sum(transaction) as `sum_transaction`",FALSE)->where(['company_id' => $this->user_session['company_id'], 'date>=' =>$begin_date, 'date<=' => $end_date])->get()->getRow();

        if(is_object($result) && is_numeric($result->sum_transaction))
            $data['count_trans_this_period'] = $result->sum_transaction;
        else
            $data['count_trans_this_period'] = 0;

        if(!is_object($data['subscription_details'])) {
            return redirect()->to(base_url('company/plans'));
        }

        $bankModel = model(BankModel::class);

        $apiBrandNames = ['TPBank', 'ACB', 'Vietinbank', 'MBBANK', 'OCB', 'KienLongBank', 'BIDV', 'MSB', 'VPBank'];
        $data['bank_sms'] = $bankModel->whereNotIn('brand_name', $apiBrandNames)->get()->getResult();
        $data['bank_api'] = $bankModel->whereIn('brand_name', $apiBrandNames)->get()->getResult();

        $productModel = model(ProductModel::class);
        $addonModel = model(AddonModel::class);

        if ($this->channel_partner) {
            $data['plans'] = $this->getChannelPartnerPlans();
            $data['shop_used_limit'] = $this->checkShopLimit();
        } else {
            $data['plans'] = $productModel->getPlans();
        }

        $data['hide_promotion_products'] = true;

        $vpbankPro = null;

        if ($data['billing_type'] === 'promotion') {
            $data['plans'] = $productModel->getPromotionPlans();
        } else {
            $data['plans_free'] = $productModel->getFreePlan();
            $data['plans_api'] = $productModel->getApiPlans();
            $data['plans_sms'] = $productModel->getSmsPlans();

            foreach($data['plans_api'] as $key => $plan) {
                if ($plan->name == 'VPBank PRO') {
                    unset($data['plans_api'][$key]);
                }
            }
        }

        $data['vpbankPro'] = $vpbankPro;

        if ($data['plan_type'] === 'shop' && $this->shop_billing) {
            $data['plans_api'] = $productModel->where(['hidden' => 0, 'sms_allow' => 0, 'billing_type !=' => 'Free', 'price_monthly >' => 0, 'channel_partner_id' => null, 'shop_limit >' => 0])->orderBy('sort_order', 'ASC')->get()->getResult();

            foreach ($data['plans_api'] as &$plan) {
                $plan->addon = $addonModel->where(['product_id' => $plan->id])->first();
            }
        }

        $data['plans_api'] = array_values($data['plans_api']);

        echo theme_view('templates/autopay/header',$data);
        switch ($data['billing_type']) {
            case 'promotion':
                $data['hasBankAccounts'] = !! model(BankAccountModel::class)->where('company_id', $this->user_session['company_id'])->countAllResults();
                echo theme_view('company/promotion-billing-pricing', $data);
                break;
            case 'speaker':
                if (! service('speakerBillingFeature')->enabled) {
                    return redirect()->to(base_url('company/change_plan'));
                }

                $data['banks'] = model(BankModel::class)
                    ->select('icon_path, brand_name')
                    ->where('invididual_api_connection', true)
                    ->where('active', true)
                    ->findAll();

                echo theme_view('company/speaker-billing-pricing',$data);
                break;
            default:
                echo theme_view('company/change_plan',$data);
                break;
        }
        echo theme_view('templates/autopay/footer',$data);
    }

    public function ajax_checkout_speaker_billing_order()
    {
        if ($this->request->getMethod(true) != 'POST') {
            return $this->failNotFound();
        }
        
        $speakerBillingFeature = service('speakerBillingFeature');
        
        if (! $speakerBillingFeature->defaultProductId) {
            return $this->failNotFound();
        }

        if ($speakerBillingFeature->companyContext()->isSubscribedSpeakerBillingProduct()) {
            return $this->respond([
                'status' => false,
                'message' => 'Hiện bạn đang đăng ký gói dịch vụ này.'
            ]);
        }

        if ( ! $speakerBillingFeature->companyContext()->canOrderSpeakerBillingProduct()) {
            return $this->respond([
                'status' => false,
                'message' => 'Hiện SePay chưa hỗ trợ đổi sang gói tích hợp loa.'
            ]);
        }

        $speakerBillingFeature->withOrderContext();
        
        try {
            $speakerBillingFeature->orderContext()->checkout();

            set_alert('success', 'Gói dịch vụ của bạn đã được kích hoạt, hãy bắt đầu thêm loa thanh toán ngay nào!');
  
            return $this->respond([
                'status' => true,
                'redirect_url' => base_url('outputdevice'),
            ]);
        } catch (\Exception $e) {
            log_message('error', $e->getMessage());
            
            return $this->respond([
                'status' => false,
                'message' => 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ.'
            ]);
        }
    }
    
    protected function getChannelPartnerPlans()
    {
        return model(ProductModel::class)->where(['hidden' => 0, 'channel_partner_id' => $this->channel_partner['id']])
            ->orderBy('sort_order', 'asc')
            ->get()->getResult();
    }
    protected function getChannelPartnerShop()
    {
        return model(ShopModel::class)->where('company_id', $this->user_session['company_id'])
            ->countAllResults();
    }

    protected function getChannelPartnerShopLimit()
    {
        $companySubscriptionModel = model(CompanySubscriptionModel::class);
        $data['subscription_details'] = $companySubscriptionModel->where(['company_id' => $this->user_session['company_id']])->get()->getRow();

        $productModel = model(ProductModel::class);

        if(is_object($data['subscription_details'])) {
            return $productModel->select(['shop_limit'])->where(['id' => $data['subscription_details']->plan_id,
            'channel_partner_id' => $this->channel_partner['id']])->get()->getRow();
        }
    }

    protected function checkShopLimit()
    {
        if($this->channel_partner){
            $shop_used = $this->getChannelPartnerShop();
            $shop_limit = $this->getChannelPartnerShopLimit();
            if(number_format($shop_used) >= number_format($shop_limit->shop_limit)){
                return true;
            }else{
                return null;
            }
        }else{
            return null;
        }
    }

    public function ajax_request_vat_invoice($id = null)
    {
        if (
            $this->request->getMethod(true) !== 'POST'
            || empty($id)
            || ! config(Invoice::class)->vatInvoiceRequestEnabled
        ) {
            show_404();
        }

        $rules = [
            'invoice_type' => 'required|in_list[personal,company]',
            'info_id' => 'permit_empty|numeric'
        ];

        if ($this->request->getPost('invoice_type') === 'personal') {
            $rules['name'] = ['rules' => 'required|string|max_length[70]', 'label' => 'Họ tên'];
            $rules['tax_code'] = ['rules' => 'permit_empty|string|max_length[20]', 'label' => 'Mã số thuế'];
            $rules['address'] = ['rules' => 'required|string', 'label' => 'Địa chỉ'];
            $rules['email'] = ['rules' => 'permit_empty|valid_email|max_length[50]', 'label' => 'Email'];
        } else {
            $rules['company_name'] = ['rules' => 'required|string|max_length[200]', 'label' => 'Tên công ty'];
            $rules['company_tax_code'] = ['rules' => 'required|string|max_length[20]', 'label' => 'Mã số thuế'];
            $rules['company_address'] = ['rules' => 'required|string', 'label' => 'Địa chỉ công ty'];
            $rules['company_email'] = ['rules' => 'permit_empty|valid_email|max_length[50]', 'label' => 'Email công ty'];
        }

        if (! $this->validate($rules)) {
            return $this->failValidationErrors($this->validator->getErrors());
        }

        if ($this->request->getPost('invoice_type') === 'company') {
            if (! (new TaxCodeLookup())->lookupBusiness($this->request->getPost('company_tax_code'))) {
                return $this->failValidationErrors(['company_tax_code' => 'Mã số thuế không hợp lệ']);
            }
        }

        $invoiceModel = model(InvoiceModel::class);
        $invoiceCustomerInfoModel = model(InvoiceCustomerInfoModel::class);

        $invoice = $invoiceModel
            ->where('company_id', $this->user_session['company_id'])
            ->where('tax_issued', false)
            ->whereIn('status', ['Unpaid', 'Paid'])
            ->where('total >', 0)
            ->find($id);

        if (! $invoice) {
            show_404();
        }

        $invoiceCustomerInfo = $invoiceCustomerInfoModel
            ->where('invoice_id', $id)
            ->where('company_id', $this->user_session['company_id'])
            ->first();

        if ($invoiceCustomerInfo) {
            return $this->failValidationErrors(['invoice_type' => 'Hóa đơn đã được gửi yêu cầu xuất hóa đơn VAT']);
        }

        $db = db_connect();
        $db->transStart();

        if ($this->request->getPost('invoice_type') === 'personal') {
            $data = [
                'name' => $this->request->getPost('name'),
                'tax_code' => $this->request->getPost('tax_code'),
                'address' => $this->request->getPost('address'),
                'email' => json_encode((array) $this->request->getPost('email')),
            ];
        } else {
            $data = [
                'company_name' => $this->request->getPost('company_name'),
                'tax_code' => $this->request->getPost('company_tax_code'),
                'address' => $this->request->getPost('company_address'),
                'email' => json_encode((array) $this->request->getPost('company_email')),
            ];
        }

        try {
            $info_id = $this->request->getPost('info_id');

            if ($info_id) {
                $existingInfo = $invoiceCustomerInfoModel
                    ->where('id', $info_id)
                    ->where('company_id', $this->user_session['company_id'])
                    ->first();

                if (! $existingInfo) {
                    return $this->failValidationErrors(['info_id' => 'Thông tin khách hàng không tồn tại']);
                }

                $invoiceCustomerInfoModel->update($info_id, array_merge($data, [
                    'company_id' => $this->user_session['company_id'],
                ]));

                $invoiceModel->update($id, [
                    'customer_info_id' => $info_id,
                ]);
            } else {
                $customerInfoId = $invoiceCustomerInfoModel->insert(array_merge($data, [
                    'company_id' => $this->user_session['company_id'],
                ]));

                $invoiceModel->update($id, [
                    'customer_info_id' => $customerInfoId,
                ]);
            }

            $invoiceModel->update($id, [
                'vat_invoice_requested_at' => date('Y-m-d H:i:s'),
            ]);

            $db->transCommit();
        } catch (Exception $e) {
            $db->transRollback();

            return $this->fail($e->getMessage());
        }

        return $this->respond([
            'status' => true,
            'message' => 'Gửi yêu cầu xuất hóa đơn VAT thành công.',
        ]);
    }

    public function ajax_cancel_auto_renew_subscription(): Response
    {
        if ($this->request->getMethod(true) !== 'POST') {
            return $this->failNotFound();
        }
        
        $billingFeature = new \App\Features\BillingFeature;
        
        if (!$billingFeature->pgEnabled) {
            return $this->failNotFound();
        }
        
        $subscriptionWithAgreement = model(CompanySubscriptionModel::class)
            ->where('company_id', $this->user_session['company_id'])
            ->where('pg_agreement_id !=', null)
            ->first();
            
        if (!$subscriptionWithAgreement) {
            return $this->failForbidden('Gói dịch vụ của bạn chưa đăng ký gia hạn tự động.');
        }
        
        $pgAgreement = model(\App\Models\PgAgreementModel::class)->where('id', $subscriptionWithAgreement->pg_agreement_id)->first();
        
        if (!$pgAgreement) {
            log_message('error', 'PG agreement not found when cancel auto renew');
            return $this->failServerError('Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ.');
        }
        
        $pgMerchant = model(\App\Models\PgMerchantModel::class)
            ->where('id', $pgAgreement->pg_merchant_id)
            ->where('merchant_id', $billingFeature->pgMerchantId)
            ->first();
        
        if (!$pgMerchant) {
            log_message('error', 'PG merchant not found when cancel auto renew');
            return $this->failServerError('Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ.');
        }
        
        $fields = [
            'merchant' => $pgMerchant->merchant_id,
            'agreement_id' => $pgAgreement->agreement_id,
            'customer_id' => $this->user_session['company_id'],
        ];
        
        $paymentGatewayFeature = new PaymentGatewayFeature;
        
        $paymentGatewayFeature->withAgreementContext($pgMerchant->merchant_id, $pgAgreement->agreement_id);
        $paymentGatewayFeature->agreementContext()->loadFields($fields);
        $agreementCheckout = $paymentGatewayFeature->agreementContext();
        
        if (!$agreementCheckout->cancelAgreement()) {
            return $this->failServerError('Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ.');
        }
        
        model(CompanySubscriptionModel::class)->where('pg_agreement_id', $pgAgreement->id)->set(['pg_agreement_id' => null])->update();
        
        return $this->respondNoContent();
    }
}
