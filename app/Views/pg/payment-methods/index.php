<?= $this->extend('layouts/default') ?>

<?= $this->section('content') ?>
<div class="container my-3">
    <div>
        <h1 class="h3 text-gray-800">Ph<PERSON><PERSON><PERSON> thức thanh toán</h1>
        <p class="text-muted">Qu<PERSON>n lý và kích hoạt các phương thức thanh toán cho cổng thanh toán của bạn</p>
    </div>

    <?php if (! $merchant): ?>
        <div class="row">
            <div class="col-12">
                <div class="card border-left-warning shadow h-100 mb-0">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                    Chờ duyệt
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    Merchant chưa được tạo
                                </div>
                                <p class="mt-2 mb-0 text-gray-600">
                                    <PERSON><PERSON> sơ onboarding của bạn đang được xem xét. <PERSON><PERSON><PERSON>,
                                    merchant sẽ được tạo tự động và bạn có thể quản lý các phương thức thanh toán.
                                </p>
                            </div>
                            <div class="col-auto">
                                <i class="bi bi-hourglass-split fa-2x text-warning"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php else: ?>
        <div class="mb-4">
            <div class="list-group list-group-flush">
                <?php
                $bankTransfer = array_filter($payment_methods, fn($m) => $m->payment_method === 'BANK_TRANSFER');
                $bankTransfer = reset($bankTransfer);
                $bankTransferExists = isset($payment_method_exists['BANK_TRANSFER']);
                ?>
                <?php if ($bankTransferExists): ?>
                    <a href="<?= base_url('paymentmethods/banktransfer') ?>" class="list-group-item list-group-item-action">
                    <?php else: ?>
                        <div class="list-group-item">
                        <?php endif; ?>
                        <div class="d-flex align-items-start">
                            <div class="bg-primary bg-opacity-10 rounded d-flex align-items-center justify-content-center me-3" style="width: 48px; height: 48px;">
                                <i class="bi bi-bank text-primary fs-4"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h5 class="mb-1">Chuyển khoản ngân hàng</h5>
                                <p class="text-muted mb-0">Nhận thanh toán qua chuyển khoản trực tiếp</p>
                                <?php if (!$bankTransferExists): ?>
                                    <small class="text-warning">Kích hoạt để sử dụng tính năng này</small>
                                <?php endif; ?>
                            </div>
                            <div class="form-check form-switch">
                                <input class="form-check-input payment-toggle" type="checkbox" data-method="BANK_TRANSFER" <?= ($bankTransfer && $bankTransfer->active) ? 'checked' : '' ?>>
                            </div>
                        </div>
                        <?php if ($bankTransferExists): ?>
                    </a>
                <?php else: ?>
            </div>
        <?php endif; ?>
        <?php
        $card = array_filter($payment_methods, fn($m) => $m->payment_method === 'CARD');
        $card = reset($card);
        $cardExists = isset($payment_method_exists['CARD']);
        ?>
        <div class="list-group-item">
            <div class="d-flex align-items-start">
                <div class="bg-primary bg-opacity-10 rounded d-flex align-items-center justify-content-center me-3" style="width: 48px; height: 48px;">
                    <i class="bi bi-credit-card text-primary fs-4"></i>
                </div>
                <div class="flex-grow-1">
                    <h5 class="mb-1">Thẻ tín dụng/ghi nợ</h5>
                    <p class="text-muted mb-0">Chấp nhận thanh toán bằng thẻ quốc tế và nội địa</p>
                    <?php if (!$cardExists): ?>
                        <small class="text-warning">Kích hoạt để sử dụng tính năng này</small>
                    <?php endif; ?>
                </div>
                <div class="form-check form-switch">
                    <input class="form-check-input payment-toggle" type="checkbox" data-method="CARD" <?= ($card && $card->active) ? 'checked' : '' ?>>
                </div>
            </div>
        </div>
</div>
</div>

<div class="mb-4">
    <div class="mb-3">
        <h4>Tính năng</h4>
    </div>
    <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-3 mb-4">
        <?php foreach ($features as $feature): ?>
            <div class="col">
                <div class="card h-100 shadow-none mb-0" data-feature-id="<?= $feature['id'] ?>">
                    <div class="card-body p-4">
                        <div class="d-flex align-items-start mb-3">
                            <div class="flex-shrink-0 me-3">
                                <div class="bg-primary bg-opacity-10 rounded d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                                    <i class="<?= $feature['icon'] ?> text-primary fs-3"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="card-title mb-2"><?= $feature['name'] ?></h6>
                                <p class="card-text text-muted mb-0"><?= $feature['description'] ?></p>
                            </div>
                        </div>

                        <div class="mb-3">
                            <ul class="list-unstyled text-muted mb-0">
                                <?php foreach ($feature['features'] as $feat): ?>
                                    <li class="mb-1">
                                        <i class="bi bi-check2 text-success me-2"></i><?= $feat ?>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>

                        <div class="d-grid gap-2">
                            <?php if ($feature['status'] === 'inactive'): ?>
                                <button type="button" class="btn btn-primary btn-enable-feature" data-feature-id="<?= $feature['id'] ?>">
                                    <i class="bi bi-power me-2"></i>Kích hoạt
                                </button>
                            <?php elseif ($feature['status'] === 'pending'): ?>
                                <?php
                                $currentStepNumber = (int) str_replace('step', '', $feature['current_step'] ?? 'step1');
                                ?>
                                <a href="<?= base_url('onboarding?feature=' . $feature['id'] . '&step=' . $currentStepNumber) ?>" class="btn btn-outline-primary">
                                    <i class="bi bi-arrow-right me-2"></i>Tiếp tục
                                </a>
                            <?php elseif ($feature['status'] === 'submitted'): ?>
                                <button type="button" class="btn btn-info" disabled>
                                    <i class="bi bi-hourglass-split me-2"></i>Chờ duyệt
                                </button>
                            <?php elseif ($feature['status'] === 'rejected'): ?>
                                <a href="<?= base_url('onboarding?feature=' . $feature['id'] . '&step=1') ?>" class="btn btn-outline-primary">
                                    <i class="bi bi-arrow-clockwise me-2"></i>Đăng ký lại
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
<?php endif; ?>
</div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    $(document).ready(function() {
        $('.btn-enable-feature').on('click', function() {
            const featureId = $(this).data('feature-id');
            const $button = $(this);

            $.ajax({
                url: '<?= base_url('paymentmethods/toggleFeature') ?>',
                method: 'POST',
                beforeSend: function() {
                    $button.prop('disabled', true).find('i').replaceWith('<span class="spinner-border spinner-border-sm align-middle me-2" role="status" aria-hidden="true"></span>');
                },
                data: {
                    '<?= csrf_token() ?>': '<?= csrf_hash() ?>',
                    feature_id: featureId,
                    action: 'enable'
                },
                success: function(response) {
                    if (response.status) {
                        notyf.success(response.message);
                        if (response.redirect) {
                            setTimeout(() => {
                                window.location.href = response.redirect;
                            }, 1500);
                        }
                    } else {
                        notyf.error(response.message);
                    }
                },
                error: function() {
                    notyf.error('Có lỗi xảy ra, vui lòng thử lại');
                },
                complete: function() {
                    $button.prop('disabled', false).find('span').replaceWith('<i class="bi bi-power me-2"></i>');
                }
            });
        });

        $('.payment-toggle').on('change', function() {
            const method = $(this).data('method');
            const action = $(this).is(':checked') ? 'enable' : 'disable';
            const $toggle = $(this);

            $.ajax({
                url: '<?= base_url('paymentmethods/togglePaymentMethod') ?>',
                method: 'POST',
                data: {
                    '<?= csrf_token() ?>': '<?= csrf_hash() ?>',
                    payment_method: method,
                    action: action
                },
                beforeSend: function() {
                    $toggle.prop('disabled', true);
                },
                success: function(response) {
                    if (!response.status) {
                        notyf.error(response.message);
                        $toggle.prop('checked', !$toggle.is(':checked'));
                    }
                },
                error: function() {
                    notyf.error('Có lỗi xảy ra, vui lòng thử lại');
                    $toggle.prop('checked', !$toggle.is(':checked'));
                },
                complete: function() {
                    $toggle.prop('disabled', false);
                }
            });
        });
    });
</script>
<?= $this->endSection() ?>