<?= $this->extend('layouts/default') ?>

<?= $this->section('content') ?>
<link rel="stylesheet" href="<?= base_url('assets/tom-select/tom-select.bootstrap5.css') ?>">

<style>
    .dataTables_info {
        padding-top: 0 !important;
    }

    .filter-chip {
        background: #f3f4f6;
        border: 1px solid #e5e7eb;
        border-radius: var(--bs-border-radius);
        padding: 2px 8px;
        font-size: .75rem;
        display: inline-flex;
        align-items: center;
        gap: 6px
    }

    .filter-chip .remove {
        cursor: pointer
    }

    @media (max-width: 576px) {
        #filtersOffcanvas.offcanvas-end {
            --bs-offcanvas-width: 85vw;
        }
    }
</style>

<div class="container my-3">
    <div class="row mb-3 align-items-center gap-2 mb-3">
        <div class="col">
            <h3 class="mb-0">Đ<PERSON><PERSON> hàng</h3>
        </div>
        <div class="col-auto ms-auto">
            <button class="btn btn-outline-secondary btn-sm" data-bs-toggle="offcanvas" data-bs-target="#filtersOffcanvas">
                <i class="bi bi-funnel me-1"></i> Bộ lọc
            </button>
        </div>
    </div>

    <div class="d-flex flex-wrap gap-2 align-items-center" id="activeFilters"></div>

    <table id="dataTables" class="table table-bordered bg-white">
        <thead>
            <tr>
                <th>Mã đơn hàng</th>
                <th>Số tiền</th>
                <th>Tiền tệ</th>
                <th>Trạng thái</th>
                <th>Mô tả</th>
                <th>Ngày tạo</th>
            </tr>
        </thead>
        <tbody></tbody>
    </table>
</div>

<div class="offcanvas offcanvas-end" tabindex="-1" id="filtersOffcanvas">
    <div class="offcanvas-header border-bottom">
        <h5 class="offcanvas-title">Bộ lọc</h5>
        <button type="button" class="btn-close" data-bs-dismiss="offcanvas"></button>
    </div>
    <div class="offcanvas-body">
        <div class="mb-3">
            <label class="form-label">Từ khóa</label>
            <input type="text" class="form-control" id="f_keyword" name="keyword" placeholder="Mã đơn, invoice, mô tả...">
        </div>
        <div class="row g-3 mb-3">
            <div class="col-6">
                <label class="form-label">Từ ngày</label>
                <input type="date" class="form-control" id="f_from_date" name="from_date">
            </div>
            <div class="col-6">
                <label class="form-label">Đến ngày</label>
                <input type="date" class="form-control" id="f_to_date" name="to_date">
            </div>
        </div>
        <div class="row g-3 mb-3">
            <div class="col-6">
                <label class="form-label">Số tiền tối thiểu</label>
                <input type="number" class="form-control" id="f_amount_min" name="amount_min" min="0" step="0.01" placeholder="0">
            </div>
            <div class="col-6">
                <label class="form-label">Số tiền tối đa</label>
                <input type="number" class="form-control" id="f_amount_max" name="amount_max" min="0" step="0.01" placeholder="">
            </div>
        </div>
        <div class="mb-3">
            <label class="form-label">Trạng thái</label>
            <select id="f_status" class="form-select" name="statuses" multiple size="6">
                <?php foreach (\App\Enums\PgOrderStatus::getLabels() as $key => $value): ?>
                    <option value="<?= $key ?>"><?= $value ?></option>
                <?php endforeach; ?>
            </select>
        </div>
        <div class="mb-3">
            <label class="form-label">Đối tác thanh toán</label>
            <select id="f_merchant_id" class="form-select" name="merchant_ids" placeholder="Chọn đối tác">
                <option value="">Tất cả</option>
                <?php foreach ($merchants as $merchant): ?>
                    <option value="<?= $merchant->id ?>"><?= $merchant->name ?></option>
                <?php endforeach; ?>
            </select>
        </div>
        <div class="mb-3">
            <label class="form-label">ID Khách hàng</label>
            <input type="text" class="form-control" id="f_customer_id" name="customer_id" placeholder="Ví dụ: cus_1234">
        </div>
        <div class="mb-3">
            <label class="form-label">Tiền tệ</label>
            <select id="f_currency" class="form-select" name="currencies">
                <option value="">Tất cả</option>
                <?php foreach ($currencies as $currency): ?>
                    <option value="<?= $currency ?>"><?= $currency ?></option>
                <?php endforeach; ?>
            </select>
        </div>
        <p class="text-muted small">Gợi ý: Bạn có thể kết hợp nhiều điều kiện để thu hẹp kết quả</p>
    </div>
    <div class="offcanvas-footer border-top p-3">
        <div class="d-grid gap-2">
            <button class="btn btn-primary" id="applyFilters" data-bs-dismiss="offcanvas">Áp dụng</button>
            <button class="btn btn-outline-secondary" id="clearFilters">Xóa bộ lọc</button>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script src="<?= base_url('assets/js/jquery.dataTables.min.js') ?>"></script>
<script src="<?= base_url('assets/js/dataTables.bootstrap5.min.js') ?>"></script>
<script src="<?= base_url('assets/js/dataTables.responsive.min.js') ?>"></script>
<script src="<?= base_url('assets/tom-select/tom-select.complete.min.js') ?>"></script>

<script>
    var STATUS_LABELS = <?= json_encode(\App\Enums\PgOrderStatus::getLabels(), JSON_UNESCAPED_UNICODE) ?>;

    $(function() {
        var tsStatus = null;
        var tsMerchant = null;

        var dataTable = $('#dataTables').DataTable({
            responsive: true,
            processing: true,
            serverSide: true,
            order: [
                [0, 'desc']
            ],
            ajax: {
                url: '<?= base_url('pg/orders') ?>',
                type: 'POST',
                data: function(d) {
                    d['<?= csrf_token() ?>'] = '<?= csrf_hash() ?>';
                    var params = getFilters();
                    $.extend(d, params);
                    return d;
                }
            },
            language: {
                sProcessing: 'Đang xử lý...',
                sLengthMenu: 'Xem _MENU_ mục',
                sZeroRecords: 'Không tìm thấy dòng nào phù hợp',
                sInfo: 'Đang xem _START_ đến _END_ trong tổng số _TOTAL_ mục',
                sInfoEmpty: 'Đang xem 0 đến 0 trong tổng số 0 mục',
                sInfoFiltered: '(được lọc từ _MAX_ mục)',
                sInfoPostFix: '',
                sSearch: 'Tìm:',
                oPaginate: {
                    sFirst: 'Đầu',
                    sLast: 'Cuối',
                    sNext: 'Tiếp',
                    sPrevious: 'Trước'
                }
            },
            columnDefs: [
                {
                    targets: [0],
                    render: function(data) {
                        return `<a href="<?= base_url('pg/orders') ?>/${data}">${data}</a>`;
                    },
                },
                {
                    targets: [2, 3, 4],
                    orderable: false,
                },
                                 {
                     targets: [4],
                     render: function(data) {
                         if (data.length > 30) {
                             const truncated = data.substring(0, 30) + '...';
                             return `<span data-bs-toggle="tooltip" title="${data.replace(/&/g, '&amp;').replace(/\"/g, '&quot;').replace(/</g, '&lt;').replace(/>/g, '&gt;')}">${truncated}</span>`;
                         }
                         return `<span>${data}</span>`;
                     },
                 },
            ],
            dom: `<'d-flex flex-wrap gap-2'B>tr<'d-flex flex-wrap gap-2 justify-content-center justify-content-md-between mt-3'<'d-flex flex-wrap justify-content-center align-items-center gap-3'li>p>`,
            drawCallback: function() {
                $('[data-bs-toggle="tooltip"]').tooltip();
            }
        });

        tsStatus = new TomSelect('#f_status', {
            plugins: {
                remove_button: {
                    title: 'Bỏ chọn'
                }
            },
            placeholder: 'Chọn trạng thái',
            maxItems: null,
            closeAfterSelect: false,
            persist: false,
            render: {
                item: function(data, escape) {
                    return '<div>' + escape(data.text) + '</div>';
                },
                option: function(data, escape) {
                    return '<div class="py-1 px-2">' + escape(data.text) + '</div>';
                }
            },
        });

        tsMerchant = new TomSelect('#f_merchant_id', {
            maxItems: null,
            persist: false,
            create: false,
            placeholder: 'Chọn đối tác thanh toán',
            plugins: {
                clear_button: {
                    title: 'Xóa'
                }
            }
        });

        $('#applyFilters').on('click', function() {
            renderActiveChips();
            dataTable.ajax.reload();
        });

        $('#clearFilters').on('click', function() {
            clearFilters();
            renderActiveChips();
            dataTable.ajax.reload();
        });

        function getFilters() {
            return {
                keyword: $('#f_keyword').val() || '',
                from_date: $('#f_from').val() || '',
                to_date: $('#f_to').val() || '',
                amount_min: $('#f_amount_min').val() || '',
                amount_max: $('#f_amount_max').val() || '',
                currencies: $('#f_currency').val() || '',
                statuses: tsStatus ? tsStatus.getValue() : ($('#f_status').val() || []),
                merchant_ids: tsMerchant ? tsMerchant.getValue() : ($('#f_merchant_id').val() || []),
                customer_id: $('#f_customer_id').val() || '',
            };
        }

        function clearFilters() {
            $('#f_keyword, #f_from_date, #f_to_date, #f_amount_min, #f_amount_max').val('');
            $('#f_currency').val('');
            tsStatus.clear();
            tsMerchant.clear();
            $('#activeFilters').empty();
        }

        function renderActiveChips() {
            if ($('#activeFilters').length === 0) return;

            var p = getFilters();
            var $container = $('#activeFilters').empty();

            function addChip(label, key, val) {
                if (!val || ($.isArray(val) && val.length === 0)) return;
                var text = $.isArray(val) ? val.join(', ') : val;
                var $chip = $('<span/>', {
                        'class': 'filter-chip'
                    })
                    .append(document.createTextNode(label + ': '))
                    .append($('<strong/>').text(text))
                    .append(' ')
                    .append($('<span/>', {
                        'class': 'remove',
                        'data-key': key,
                        text: '×',
                    }));
                $container.append($chip);
            }

            addChip('Từ khóa', 'keyword', p.keyword);
            addChip('Khoảng ngày', 'date', $.grep([p.from_date, p.to_date], Boolean).join(' → '));
            addChip('Khoảng tiền', 'amount', $.grep([p.amount_min, p.amount_max], Boolean).join(' → '));
            addChip('Trạng thái', 'statuses', p.statuses.filter(Boolean).map((k) => STATUS_LABELS[k] || k));
            addChip('Tiền tệ', 'currencies', p.currencies);
            addChip('Đối tác thanh toán', 'merchant_ids', p.merchant_ids);
            addChip('ID Khách hàng', 'customer_id', p.customer_id);
        }

        $('#activeFilters').on('click', '.remove', function() {
            var key = $(this).data('key');
            switch (key) {
                case 'keyword':
                    $('#f_keyword').val('');
                    break;
                case 'date':
                    $('#f_from_date, #f_to_date').val('');
                    break;
                case 'amount':
                    $('#f_amount_min, #f_amount_max').val('');
                    break;
                case 'statuses':
                    tsStatus.clear();
                    break;
                case 'currencies':
                    $('#f_currency').val('');
                    break;
                case 'merchant_ids':
                    tsMerchant.clear();
                    break;
                case 'customer_id':
                    $('#f_pg_customer_id').val('');
                    break;
            }

            renderActiveChips();
            dataTable.ajax.reload();
        });

        renderActiveChips();
    });
</script>
<?= $this->endSection() ?>