<?= $this->extend('layouts/default'); ?>

<?= $this->section('content'); ?>
<style>
    .dataTables_info {
        padding-top: 0 !important;
    }
</style>

<div class="container my-3">
    <div class="row mb-3 align-items-center gap-2">
        <div class="col">
            <h3 class="mb-0">Quản lý Chi nhánh</h3>
        </div>
        <div class="d-flex gap-2 col-auto">
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addBranchModal">
                <i class="bi bi-plus me-1"></i> Thêm Chi nhánh
            </button>
        </div>
    </div>
    <table id="dataTables" class="table table-bordered bg-white">
        <thead>
            <tr>
                <th>STT</th>
                <th>Tên chi nhánh</th>
                <th>M<PERSON> chi nhánh</th>
                <th>Trạng thái</th>
                <th><PERSON><PERSON><PERSON> t<PERSON></th>
                <th><PERSON><PERSON><PERSON> cậ<PERSON> nhật</th>
            </tr>
        </thead>
        <tbody></tbody>
    </table>
</div>

<div class="modal fade" id="addBranchModal" tabindex="-1" aria-labelledby="addBranchModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addBranchModalLabel">Thêm chi nhánh</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <?= form_open('branches/ajax_create', ['id' => 'addBranchForm']) ?>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="name" class="form-label">Tên chi nhánh <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="name" name="name" required>
                </div>
                <div class="mb-3">
                    <label for="code" class="form-label">Mã chi nhánh <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="code" name="code" required>
                </div>
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" id="active" name="active" value="1" checked>
                    <label class="form-check-label" for="active">Hoạt động</label>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                <button type="submit" class="btn btn-primary">Thêm</button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<?= $this->endSection(); ?>

<?= $this->section('scripts'); ?>
<script src="<?= base_url('assets/js/jquery.dataTables.min.js') ?>"></script>
<script src="<?= base_url('assets/js/dataTables.bootstrap5.min.js') ?>"></script>
<script src="<?= base_url('assets/js/dataTables.responsive.min.js') ?>"></script>

<script>
    $(() => {
        const dataTable = $('#dataTables').DataTable({
            responsive: true,
            processing: true,
            serverSide: true,
            order: [
                [0, 'desc']
            ],
            ajax: {
                url: '<?= base_url('branches') ?>',
                type: 'POST',
                data: function(d) {
                    d['<?= csrf_token() ?>'] = '<?= csrf_hash() ?>';
                    return d;
                }
            },
            language: {
                sProcessing: 'Đang xử lý...',
                sLengthMenu: 'Xem _MENU_ mục',
                sZeroRecords: 'Không tìm thấy dòng nào phù hợp',
                sInfo: 'Đang xem _START_ đến _END_ trong tổng số _TOTAL_ mục',
                sInfoEmpty: 'Đang xem 0 đến 0 trong tổng số 0 mục',
                sInfoFiltered: '(được lọc từ _MAX_ mục)',
                sInfoPostFix: '',
                sSearch: 'Tìm:',
                oPaginate: {
                    sFirst: 'Đầu',
                    sLast: 'Cuối',
                    sNext: 'Tiếp',
                    sPrevious: 'Trước',
                },
            },
            dom: `<'d-flex flex-wrap gap-2'fB>tr<'d-flex flex-wrap gap-2 justify-content-center justify-content-md-between mt-3'<'d-flex flex-wrap justify-content-center align-items-center gap-3'li>p>`,
        });
    });
</script>
<?= $this->endSection(); ?>