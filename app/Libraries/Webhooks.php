<?php

namespace App\Libraries;

use App\Libraries\ProxyCURLRequest;

class Webhooks 
{
	private $_webhooks_db_info;
	private $_webhooks_id;
	private $_transaction_id;

    public function __construct($options = NULL)
	{
		log_message('info', 'WebHooks Library Class Initialized');
	}

	public function set_webhooks_info($webhook_id)
	{
		$this->_webhooks_id = $webhook_id;
		$model = model(WebhooksModel::class);
		$result = $model->where(['id' => $webhook_id])->get()->getRow();
		if (is_object($result)) {
			$this->_webhooks_db_info = $result;
			$this->_webhooks_id = $webhook_id;

			log_message('info', 'WebHooks Library Class: Webhooks info were set');
			return TRUE;
		} else {
			$this->_webhooks_db_info = FALSE;
			log_message('warning', 'WebHooks Library Class: Webhooks info not set');
			return FALSE;
		}

	}
 
	public function send_post($url, $header_data, $post_data) {
		$return = array(
			'success' => FALSE,
			'status_code' => NULL,
			'request_method'=> 'POST',
			'request_url' => $url,
			'connect_success' => 0,
			'request_header' => NULL,
			'request_body' => NULL,
			'response_body' => NULL,
			'response_status_code' => NULL,
			'error_message' => '',
			'webhook_type' => 'Webhooks',
			'company_id' => $this->_webhooks_db_info->company_id,
			'webhook_id' => $this->_webhooks_id,
			'sms_parsed_id' => $this->_transaction_id
		);

		if(!filter_var($url, FILTER_VALIDATE_URL))
			return $return;

		// Start the request
		$curl = curl_init();

		// Set cURL options
		// Return the response
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, TRUE);

		curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 10); 
		curl_setopt($curl, CURLOPT_TIMEOUT, 15); //timeout in seconds


		// Set the URL
		curl_setopt($curl, CURLOPT_URL, $url);
		$proxy = null;

		if (preg_match('/google\.com/', $url)) {
			$proxy = ProxyCURLRequest::getProxy('google.com');
			curl_setopt($curl, CURLOPT_PROXY, $proxy);
			curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 45);
			curl_setopt($curl, CURLOPT_TIMEOUT, 60);
		}

		$cache = service('cache');
		$noProxyWebhookKey = 'webhook_no_proxy_' . $this->_webhooks_id . '_' . $this->_transaction_id;
		$forceProxyWebhookKey = 'webhook_force_proxy_' . $this->_webhooks_id . '_' . $this->_transaction_id;
		
		try {
		    /** @var \Config\CURLRequest $curlRequestConfig */
    		$curlRequestConfig = config(\Config\CURLRequest::class);

			if (
				(!$cache->get($noProxyWebhookKey) 
					&& !in_array($this->_webhooks_db_info->company_id, $curlRequestConfig->webhookNoProxyCompanyIds) 
					&& !in_array($this->_webhooks_db_info->merchant_id, $curlRequestConfig->webhookNoProxyMerchantIds)) 
				|| $cache->get($forceProxyWebhookKey)
			) {
				$proxy = ProxyCURLRequest::getProxy('webhook');
				curl_setopt($curl, CURLOPT_PROXY, $proxy);
				curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 45); 
				curl_setopt($curl, CURLOPT_TIMEOUT, 60);
			}
		} catch (\Throwable $e) {
            log_message('error', "WebHooks Library Class: Error setting proxy for webhook: " . $e->getMessage());
		}

		// Set useragent
		curl_setopt($curl, CURLOPT_USERAGENT, "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.0.0 Safari/537.36");

		// Send POST data
		curl_setopt($curl, CURLOPT_POST, TRUE);

		// Set POST data
		if($this->_webhooks_db_info->request_content_type == "Json") {
			curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($post_data));
			//curl_setopt( $curl, CURLOPT_HTTPHEADER, ['Content-Type:application/json']);
		} else
			curl_setopt($curl, CURLOPT_POSTFIELDS, $post_data);


		curl_setopt( $curl, CURLOPT_HTTPHEADER, $header_data);

		$return['request_body'] = json_encode($post_data);
 
		// Force CURL to verify the certificate
		curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, TRUE);
		

		// Initiate the request and return the response
		$response = curl_exec($curl);
 
		$return['request_header'] = json_encode($header_data);
		
		 

		// Check if there were any errors
		if($response === FALSE){
			// Log the error
			log_message('error', "WebHooks Library Class: cURL failed with error: Webhook ID " . $this->_webhooks_id . ": " . curl_error($curl));

			$curl_errno = curl_errno($curl);
			$curl_error = curl_error($curl);
			$info = curl_getinfo($curl);

			log_message('error', sprintf(
				"[WEBHOOK_TIMEOUT_ERROR]: %s - %s | Proxy: %s | Diagnosis: %s | Webhook ID: %s | Info: %s",
				$curl_errno,
				$curl_error,
				$proxy ? $proxy : 'No',
				in_array($curl_errno, [CURLE_COULDNT_CONNECT, CURLE_RECV_ERROR]) ? 'Proxy timeout' : 'Destination server timeout',
				$this->_webhooks_id,
				json_encode($info)
			));

			// Prepare data to return
			$return['error_message'] = curl_error($curl);

			if (in_array($this->_webhooks_db_info->company_id, $curlRequestConfig->webhookNoProxyCompanyIds) || in_array($this->_webhooks_db_info->merchant_id, $curlRequestConfig->webhookNoProxyMerchantIds)) {
				$cache->save($forceProxyWebhookKey, 1, 3600 * 5);
			} else {
				$cache->save($noProxyWebhookKey, 1, 3600 * 5);
			}
		} else {
			// Parse the JSON response and prepare to return it
			$return['success'] = TRUE;
			$return['connect_success'] = 1;
			$return['response_status_code'] = curl_getinfo($curl, CURLINFO_HTTP_CODE);
			$return['response_body'] = $response;
			//$return = ['success'=>TRUE,'statusCode' => curl_getinfo($curl, CURLINFO_HTTP_CODE), 'data'=>$response];

			if (in_array($this->_webhooks_db_info->company_id, $curlRequestConfig->webhookNoProxyCompanyIds) || in_array($this->_webhooks_db_info->merchant_id, $curlRequestConfig->webhookNoProxyMerchantIds)) {
				$cache->delete($forceProxyWebhookKey);
			} else {
				$cache->delete($noProxyWebhookKey);
			}
		}
		// Close the cURL session
		curl_close($curl);

		if ($this->_webhooks_db_info->merchant_id) {
			$return['merchant_id'] = $this->_webhooks_db_info->merchant_id;
		}

		$model = model(WebhooksLogModel::class);
		$model->insert($return);

		// Counter
		$counterModel = model(CounterModel::class);
		$counterModel->webhook($this->_webhooks_db_info->company_id,FALSE,$this->_webhooks_db_info->merchant_id);

		return $return;

	}

	public function do_webhooks($webhook_id, $post_data, $transaction_id=FALSE) {
		$this->_transaction_id = $transaction_id;
		$this->set_webhooks_info($webhook_id);

		 

		if(!is_object($this->_webhooks_db_info))
			return FALSE;
		
		$header_data = [];
		if($this->_webhooks_db_info->request_content_type == "Json") {
            $header_data = ['Content-Type:application/json'];
		} else if ($this->_webhooks_db_info->request_content_type == "multipart_form-data") {
            $header_data = ['Content-Type:multipart/form-data'];
		} else if ($this->_webhooks_db_info->request_content_type == "application_x-www-form-urlencoded") {
            $header_data = ['Content-Type:application/x-www-form-urlencoded'];
		} 
		

		if($this->_webhooks_db_info->authen_type == "Api_Key") {
            array_push($header_data, 'Authorization:Apikey ' . $this->_webhooks_db_info->api_key);
		} 

		$webhooks_url = $this->_webhooks_db_info->webhook_url;

		$result = $this->send_post($webhooks_url, $header_data, $post_data);

		return $result;

		
	}

}