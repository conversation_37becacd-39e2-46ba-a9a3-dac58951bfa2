<?php

namespace App\Libraries\Psr7;

use CodeIgniter\HTTP\RequestInterface;
use Psr\Http\Message\ServerRequestFactoryInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Message\StreamFactoryInterface;

class PsrHttpFactory implements HttpMessageFactoryInterface
{
    protected ServerRequestFactoryInterface $serverRequestFactory;

    protected StreamFactoryInterface $streamFactory;

    public function __construct(
        ServerRequestFactoryInterface $serverRequestFactory,
        StreamFactoryInterface $streamFactory
    ) {
        $this->serverRequestFactory = $serverRequestFactory;
        $this->streamFactory = $streamFactory;
    }

    public function createRequest(RequestInterface $request): ServerRequestInterface
    {
        $requestFactory = $this->serverRequestFactory->createServerRequest(
            $request->getMethod(true),
            $request->getUri()->__toString(),
            $request->getServer()
        );

        foreach ($request->headers() as $value) {
            try {
                $requestFactory = $requestFactory->withHeader($value->getName(), $value->getValue());
            } catch (\InvalidArgumentException $e) {
                // ignore invalid header
            }
        }

        $body = $request->getBody() === null
            ? $this->streamFactory->createStreamFromResource('php://memory', 'wb+')
            : $this->streamFactory->createStreamFromResource($request->getBody());

        // Get the property query from the URI, it is not accessible.
        $reflected = new \ReflectionClass($request->getUri());
        $property = $reflected->getProperty('query');
        $property->setAccessible(true);
        $queryParams = $property->getValue($request->getUri());

        return $requestFactory
            ->withBody($body)
            ->withUploadedFiles($request->getFiles())
            ->withCookieParams($request->getCookie())
            ->withQueryParams($queryParams)
            ->withParsedBody($request->getVar());
    }
}
