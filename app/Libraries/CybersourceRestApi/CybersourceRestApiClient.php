<?php

namespace App\Libraries\CybersourceRestApi;

use App\Libraries\ProxyCURLRequest;

class CybersourceRestApiClient
{
    protected string $host;
    
    protected string $merchantId;
    
    protected string $authenticationType; // 'HTTP_SIGNATURE'
    
    protected string $key;
    
    protected string $sharedSecret;
    
    protected int $timeout = 30;
    
    public function __construct(string $merchantId)
    {
        $cybersourceConfig = config(\Config\CybersourceConfig::class);
        
        $this->host = $cybersourceConfig->restApiHost;
        $this->merchantId = $merchantId;
    }
    
    public function authenticateWithHttpSignature(string $key, string $sharedSecret)
    {
        $this->authenticationType = 'HTTP_SIGNATURE';
        $this->key = $key;
        $this->sharedSecret = $sharedSecret;
    }
    
    public function getPlanList(?string $code = null)
    {
        $queries = [];
        
        if ($code) {
            $queries['code'] = $code;
        }
        
        return $this->makeRequest('get', '/rbs/v1/plans' . (count($queries) ? '?' . http_build_query($queries) : ''));
    }
    
    public function createSubscription(array $data)
    {
        return $this->makeRequest('post', '/rbs/v1/subscriptions', $data);
    }
    
    protected function makeNewHttpClient()
    {
        return ProxyCURLRequest::make()->setProxy('cybersource');
    }
    
    protected function makeRequest($method, $uri, array $data = [])
    {
        if (!$this->authenticationType) {
            throw new \Exception('Authentication type not set');
        }
        
        $method = strtolower($method);
        $gmtDate = date("D, d M Y G:i:s ").'GMT';
        $withoutRequestBody = in_array($method, ['get', 'delete']);
        
        if ($withoutRequestBody) {
            $signatureString = "host: ".$this->host."\ndate: ".$gmtDate."\n(request-target): ".$method." ".$uri."\nv-c-merchant-id: ".$this->merchantId;
            $signatureHeaderTemplate = 'keyid="%s",algorithm="HmacSHA256",headers="host date (request-target) v-c-merchant-id",signature="%s"';
        } else {
            $digest = base64_encode(hash("sha256", json_encode($data), true));
            $signatureString = "host: ".$this->host."\ndate: ".$gmtDate."\n(request-target): ".$method." ".$uri."\ndigest: SHA-256=".$digest."\nv-c-merchant-id: ".$this->merchantId;
            $signatureHeaderTemplate = 'keyid="%s",algorithm="HmacSHA256",headers="host date (request-target) digest v-c-merchant-id",signature="%s"';
        }
        
        $signature = base64_encode(hash_hmac('sha256', $signatureString, base64_decode($this->sharedSecret), true));
        $signatureHeader = sprintf($signatureHeaderTemplate, $this->key, $signature);
        
        $headers = [
            'Host' => $this->host,
            'Date' => $gmtDate,
            'v-c-merchant-id' => $this->merchantId,
            'Signature' => $signatureHeader,
            'Content-Type' => 'application/json',
        ];
        
        if (isset($digest)) {
            $headers['Digest'] = 'SHA-256=' . $digest;
        }
        
        return $this->makeNewHttpClient()->request($method, $this->endpoint($uri), [
            'headers' => $headers,
            'http_errors' => false,
            'form_params' => $data,
            'timeout' => $this->timeout
        ]);
    }
    
    protected function endpoint($uri): string
    {
        return 'https://'.$this->host.$uri;
    }
}