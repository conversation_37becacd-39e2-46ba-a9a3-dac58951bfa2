<?php

namespace App\Libraries\OAuthServer\Entities;

use App\Libraries\OAuthServer\Interfaces\ClientEntityInterface;
use League\OAuth2\Server\Entities\Traits\ClientTrait;
use League\OAuth2\Server\Entities\Traits\EntityTrait;

class ClientEntity implements ClientEntityInterface
{
    use EntityTrait;
    use ClientTrait;

    protected $id;

    public function __construct($id, $identifier, $name, $redirectUri, $isConfidential = false)
    {
        $this->setIdentifier((string) $identifier);

        $this->id = $id;
        $this->name = $name;
        $this->isConfidential = $isConfidential;
        $this->redirectUri = explode(',', $redirectUri);
    }

    public function getId(): string
    {
        return $this->id;
    }
}
