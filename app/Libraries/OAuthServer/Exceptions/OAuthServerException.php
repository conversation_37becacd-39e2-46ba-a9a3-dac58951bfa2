<?php

namespace App\Libraries\OAuthServer\Exceptions;

use CodeIgniter\Exceptions\DebugTraceableTrait;
use CodeIgniter\Exceptions\ExceptionInterface;
use League\OAuth2\Server\Exception\OAuthServerException as LeagueException;
use Nyholm\Psr7\Response;
use OutOfBoundsException;

class OAuthServerException extends OutOfBoundsException implements ExceptionInterface
{
    use DebugTraceableTrait;

    protected Response $response;

    public function __construct(LeagueException $e, Response $response)
    {
        parent::__construct($e->getMessage(), $e->getCode(), $e);

        $this->response = $response;
    }
}
