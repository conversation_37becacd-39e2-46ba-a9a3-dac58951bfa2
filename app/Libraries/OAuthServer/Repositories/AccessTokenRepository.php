<?php

namespace App\Libraries\OAuthServer\Repositories;

use App\Models\OAuthTokenModel;

class AccessTokenRepository
{
    public function create(array $attributes)
    {
        return model(OAuthTokenModel::class)->insert($attributes);
    }

    public function revokeAccessToken($tokenId): bool
    {
        return model(OAuthTokenModel::class)->update($tokenId, ['revoked' => true]);
    }

    public function isAccessTokenRevoked($tokenId): bool
    {
        return !! model(OAuthTokenModel::class)
            ->where('id', $tokenId)
            ->where('revoked', true)
            ->first();
    }
}
