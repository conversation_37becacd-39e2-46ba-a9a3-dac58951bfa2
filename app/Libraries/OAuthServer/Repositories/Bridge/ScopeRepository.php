<?php

namespace App\Libraries\OAuthServer\Repositories\Bridge;

use App\Libraries\OAuthServer\Entities\ScopeEntity;
use App\Libraries\OAuthServer\Traits\ResolvesInheritedScopes;
use Config\Services;
use League\OAuth2\Server\Entities\ClientEntityInterface;
use League\OAuth2\Server\Repositories\ScopeRepositoryInterface;
use App\Libraries\OAuthServer\Repositories\ClientRepository;

class ScopeRepository implements ScopeRepositoryInterface
{
    use ResolvesInheritedScopes;

    protected ?ClientRepository $clientRepository;

    public function __construct(?ClientRepository $clientRepository = null)
    {
        $this->clientRepository = $clientRepository;
    }

    /**
     * {@inheritdoc}
     */
    public function getScopeEntityByIdentifier($identifier)
    {
        if (! oauth2()->hasScope($identifier)) {
            return null;
        }

        return new ScopeEntity($identifier);
    }

    /**
     * {@inheritdoc}
     */
    public function finalizeScopes(
        array $scopes,
        $grantType,
        ClientEntityInterface $clientEntity,
        $userIdentifier = null
    ): array {
        if (! in_array($grantType, ['password', 'personal_access', 'client_credentials'], true)) {
            $scopes = array_values($scopes);
        }

        $validScopes = array_filter(
            $scopes,
            fn ($scope): bool => $scope instanceof ScopeEntity && oauth2()->hasScope($scope->getIdentifier())
        );

        if (! $this->clientRepository) {
            return $validScopes;
        }

        $client = $this->clientRepository->findActive($clientEntity->getIdentifier());

        $clientScopes = $this->parseClientScopes($client->scopes ?? '');

        return array_filter(
            $validScopes,
            fn($scope) => $this->hasScope($scope->getIdentifier(), $clientScopes)
        );
    }

    protected function hasScope(string $scope, ?array $clientScopes = null): bool
    {
        if (! $clientScopes) {
            return true;
        }

        $inheritedScopes = $this->resolveInheritedScopes($scope);

        return (bool) array_intersect($inheritedScopes, $clientScopes);
    }

    protected function parseClientScopes(string $scopesJson): ?array
    {
        $scopes = json_decode($scopesJson, true);

        return is_array($scopes) ? $scopes : null;
    }
}
