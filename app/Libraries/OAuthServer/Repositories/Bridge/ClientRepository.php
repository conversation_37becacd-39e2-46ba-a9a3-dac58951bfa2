<?php

namespace App\Libraries\OAuthServer\Repositories\Bridge;

use App\Libraries\OAuthServer\Entities\ClientEntity;
use App\Libraries\OAuthServer\Repositories\ClientRepository as Client;
use League\OAuth2\Server\Repositories\ClientRepositoryInterface;

class ClientRepository implements ClientRepositoryInterface
{
    protected Client $clientRepository;

    public function __construct(Client $clientRepository)
    {
        $this->clientRepository = $clientRepository;
    }

    /**
     * {@inheritdoc}
     */
    public function getClientEntity($clientIdentifier)
    {
        $record = $this->clientRepository->findActive($clientIdentifier);

        if (! $record) {
            return null;
        }

        return new ClientEntity(
            $record->id,
            $record->client_id,
            $record->name,
            $record->redirect_uri,
            ! empty($record->client_secret)
        );
    }

    /**
     * {@inheritdoc}
     */
    public function validateClient($clientIdentifier, $clientSecret, $grantType): bool
    {
        $record = $this->clientRepository->findActive($clientIdentifier);

        if (! $record) {
            return false;
        }

        return empty($record->client_secret) || $this->verifySecret($clientSecret, $record->client_secret);
    }

    protected function verifySecret(string $clientSecret, string $storedHash): bool
    {
        return hash_equals($storedHash, $clientSecret);
    }
}
