<?php

namespace App\Libraries\OAuthServer\Repositories;

use App\Models\OAuthRefreshTokenModel;

class RefreshTokenRepository
{
    public function find($tokenId)
    {
        return model(OAuthRefreshTokenModel::class)->find($tokenId);
    }

    public function create(array $attributes)
    {
        return model(OAuthRefreshTokenModel::class)->insert($attributes);
    }

    public function revokeRefreshToken($tokenId): bool
    {
        return model(OAuthRefreshTokenModel::class)->update($tokenId, ['revoked' => true]);
    }

    public function isRefreshTokenRevoked($tokenId): bool
    {
        if ($token = $this->find($tokenId)) {
            return $token->revoked;
        }

        return true;
    }
}
