<?php

namespace App\Libraries\OAuthServer\Repositories;

use App\Models\OAuthClientModel;

class ClientRepository
{
    public function find($id)
    {
        return model(OAuthClientModel::class)
            ->where('client_id', $id)
            ->first();
    }

    public function findActive($id)
    {
        $client = $this->find($id);

        return $client && ! $client->revoked ? $client : null;
    }
}