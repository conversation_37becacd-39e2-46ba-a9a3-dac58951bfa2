<?php

namespace App\Libraries\OAuthServer\Traits;

use League\OAuth2\Server\Exception\OAuthServerException as LeagueException;
use Nyholm\Psr7\Response;

trait HandlesOAuthErrors
{
    use ConvertsPsrResponses;

    protected function withErrorHandling($callback)
    {
        try {
            return $callback();
        } catch (LeagueException $e) {
            $response = $e->generateHttpResponse(new Response());
            if (empty($response->getBody()->getContents())) {
                return $this->convertResponse($response)
                    ->setBody(json_encode([
                        'error' => $e->getErrorType(),
                        'message' => $e->getMessage(),
                        'hint' => $e->getHint(),
                        'statusCode' => $response->getStatusCode(),
                    ]));
            }

            return $this->convertResponse($response);
        }
    }
}
