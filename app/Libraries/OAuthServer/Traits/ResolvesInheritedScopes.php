<?php

namespace App\Libraries\OAuthServer\Traits;

trait ResolvesInheritedScopes
{
    protected function resolveInheritedScopes($scope): array
    {
        $parts = explode(':', $scope);

        $partsCount = count($parts);

        $scopes = [];

        for ($i = 1; $i <= $partsCount; $i++) {
            $scopes[] = implode(':', array_slice($parts, 0, $i));
        }

        return $scopes;
    }
}
