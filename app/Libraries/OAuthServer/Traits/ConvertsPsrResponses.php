<?php

namespace App\Libraries\OAuthServer\Traits;

use CodeIgniter\HTTP\Response;
use Config\Services;
use Psr\Http\Message\ResponseInterface;

trait ConvertsPsrResponses
{
    public function convertResponse(ResponseInterface $psrResponse): Response
    {
        $response = Services::response()
            ->setStatusCode($psrResponse->getStatusCode())
            ->setBody($psrResponse->getBody())
            ->setJSON(json_decode($psrResponse->getBody()))
            ->setProtocolVersion($psrResponse->getProtocolVersion());

        foreach ($psrResponse->getHeaders() as $name => $values) {
            $response->setHeader($name, $values);
        }

        return $response;
    }
}
