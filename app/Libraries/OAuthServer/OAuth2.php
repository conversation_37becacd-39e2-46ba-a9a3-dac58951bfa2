<?php

namespace App\Libraries\OAuthServer;

use App\Libraries\OAuthServer\Repositories\AccessTokenRepository;
use App\Libraries\OAuthServer\Repositories\Bridge\AccessTokenRepository as BridgeAccessTokenRepository;
use App\Libraries\OAuthServer\Repositories\Bridge\AuthCodeRepository as BridgeAuthCodeRepository;
use App\Libraries\OAuthServer\Repositories\Bridge\ClientRepository as BridgeClientRepository;
use App\Libraries\OAuthServer\Repositories\Bridge\RefreshTokenRepository as BridgeRefreshTokenRepository;
use App\Libraries\OAuthServer\Repositories\Bridge\ScopeRepository as BridgeScopeRepository;
use App\Libraries\OAuthServer\Repositories\ClientRepository;
use App\Libraries\OAuthServer\Repositories\RefreshTokenRepository;
use Config\OAuth2 as Config;
use DateInterval;
use League\OAuth2\Server\AuthorizationServer;
use League\OAuth2\Server\Grant\AuthCodeGrant;
use League\OAuth2\Server\Grant\RefreshTokenGrant;
use League\OAuth2\Server\ResourceServer;

class OAuth2
{
    protected AuthorizationServer $authorizationServer;

    protected ResourceServer $resourceServer;

    protected Config $config;

    public function __construct()
    {
        $this->config = config(Config::class);
    }

    public function authorizationServer(): AuthorizationServer
    {
        if (isset($this->authorizationServer)) {
            return $this->authorizationServer;
        }

        $privateKey = file_get_contents($this->config->privateKey);

        $clientRepository = new ClientRepository();
        $refreshTokenRepository = new RefreshTokenRepository();
        $accessTokenRepository = new AccessTokenRepository();

        $server = new AuthorizationServer(
            new BridgeClientRepository($clientRepository),
            new BridgeAccessTokenRepository($accessTokenRepository),
            new BridgeScopeRepository($clientRepository),
            $privateKey,
            $this->config->encryptionKey
        );

        $server->setDefaultScope($this->config->defaultScope);

        $authCodeGrant = new AuthCodeGrant(
            new BridgeAuthCodeRepository(),
            new BridgeRefreshTokenRepository($refreshTokenRepository),
            new DateInterval($this->config->authorizationCodeTTL)
        );

        $server->enableGrantType(
            $authCodeGrant,
            new DateInterval($this->config->accessTokenTTL)
        );

        $refreshTokenGrant = new RefreshTokenGrant(new BridgeRefreshTokenRepository($refreshTokenRepository));
        $refreshTokenGrant->setRefreshTokenTTL(new DateInterval($this->config->refreshTokenTTL));

        $server->enableGrantType(
            $refreshTokenGrant,
            new DateInterval($this->config->accessTokenTTL)
        );

        // @TODO Add Personal Access Token, Password Grant, Client Credentials Grant and Implicit Grant

        return $server;
    }

    public function resourceServer(): ResourceServer
    {
        if (isset($this->resourceServer)) {
            return $this->resourceServer;
        }

        $publicKey = file_get_contents($this->config->publicKey);

        return new ResourceServer(
            new BridgeAccessTokenRepository(new AccessTokenRepository()),
            $publicKey
        );
    }

    public function hasScope(string $id): bool
    {
        return array_key_exists($id, $this->config->scopes);
    }

    public function scopes(): array
    {
        return array_map(
            fn($scope) => new Scope($scope, $this->config->scopes[$scope]),
            array_keys($this->config->scopes)
        );
    }

    public function scopesFor(array $ids): array
    {
        $result = [];

        foreach ($ids as $id) {
            if (isset($this->config->scopes[$id])) {
                $result[] = new Scope($id, $this->config->scopes[$id]);
            }
        }

        return array_values(array_filter($result));
    }

    public function addScopes(array $scopes, bool $override = false): void
    {
        if ($override) {
            $this->config->scopes = [];
        }

        foreach ($scopes as $id => $description) {
            $this->config->scopes[$id] = $description;
        }
    }

    public function scopeIds(): array
    {
        return array_keys($this->config->scopes);
    }
}
