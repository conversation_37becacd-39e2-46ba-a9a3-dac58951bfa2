<?php

namespace App\Libraries;

use CodeIgniter\HTTP\Response;
use App\Libraries\ProxyCURLRequest;

class Webhook
{
    /**
     * @param array{type?:string,options?:array{secret_key?:string}} $auth
     * @param array{proxy:string,auth} $options
     */
    public function send(string $method, string $url, array $headers = [], array $data = [], array $auth = [], array $options = [])
    {
        $client = ProxyCURLRequest::make();

        if (isset($options['proxy'])) {
            $client->setProxy($options['proxy']);
        }

        foreach ($headers as $key => $value) {
            $client->setHeader($key, $value);
        }

        $requestContentType = $headers['Content-Type'] ?? 'application/json';

        if ($requestContentType === 'application/json') {
            $client->setJSON($data);
        } else {
            $client->setBody(json_encode($data));
        }

        $curlRequestOptions = [
            'http_errors' => false,
        ];

        $response = $client->request($method, $url, $curlRequestOptions);

        return $response;
    }
}