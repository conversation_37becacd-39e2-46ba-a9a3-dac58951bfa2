<?php

namespace App\Filters;

use Config\Mobile;
use Config\Services;
use App\Models\UserModel;
use App\Models\CompanyModel;
use CodeIgniter\HTTP\RequestInterface;
use App\Models\UserPermissionBankModel;
use CodeIgniter\HTTP\ResponseInterface;
use App\Models\PersonalAccessTokenModel;
use CodeIgniter\Filters\FilterInterface;
use App\Models\UserPermissionBankSubModel;
use App\Models\UserPermissionFeatureModel;

class MobileApiAuthenticationFilter implements FilterInterface
{
    protected $mobileConfig;

    public function __construct()
    {
        $this->mobileConfig = config(Mobile::class);
        $this->mobileConfig->personalAccessTokenTtl = $this->mobileConfig->personalAccessTokenTtl ?? null; 
    }

    protected function determineIfUnmatchMobileRoutes($request) 
    {
        $path = $request->getUri()->getPath();

        return !preg_match('/(?=mobile\/.*)/', $path) || preg_match('/(?=mobile\/.*\/user\/login)/', $path);
    }

    public function before(RequestInterface $request, $arguments = null)
    {
        $request->mobileConfig = $this->mobileConfig;

        if ($this->determineIfUnmatchMobileRoutes($request)) {
            return $request;
        };

        $accessToken = explode('Bearer ', $request->getHeaderLine('Authorization'))[1] ?? '';

        if (!$accessToken) return $this->respondUnauthenticatedResponse();

        $personalAccessToken = model(PersonalAccessTokenModel::class)
            ->where('access_token', $accessToken)
            ->groupStart()
                ->where('expires_at >', date('Y-m-d H:i:s'))
                ->orWhere('expires_at', null)
            ->groupEnd()
            ->first();

        if (!$personalAccessToken) return $this->respondUnauthenticatedResponse();

        $user = model(UserModel::class)
            ->join('tb_autopay_company_user', 'tb_autopay_company_user.user_id = tb_autopay_user.id')
            ->where([
                'tb_autopay_user.id' => $personalAccessToken->user_id,
                'tb_autopay_user.active' => 1,
                'tb_autopay_user.deleted_at' => null,
            ])
            ->first();

        if (!$user) return $this->respondUnauthenticatedResponse();

        $company = model(CompanyModel::class)
            ->select(['tb_autopay_company.id', 'tb_autopay_company.merchant_id'])
            ->join('tb_autopay_company_user', 'tb_autopay_company_user.company_id = tb_autopay_company.id')
            ->where([
                'tb_autopay_company_user.user_id' => $user->id,
                'tb_autopay_company.merchant_id' => null,
                'tb_autopay_company.deleted_at' => null,
            ])
            ->get()->getRow();

        if (!$company) return $this->respondUnauthenticatedResponse();

        $user->feature_permissions = model(UserPermissionFeatureModel::class)
            ->select(['feature_slug', 'can_view_all', 'can_add', 'can_edit', 'can_delete'])
            ->where(['user_id' => $user->id, 'deleted_at' => null])
            ->get()->getResult();

        $user->bank_account_permissions = model(UserPermissionBankModel::class)
            ->select(['bank_account_id', 'hide_amount_out', 'hide_accumulated', 'hide_reference_number', 'hide_transaction_content'])
            ->where(['user_id' => $user->id, 'deleted_at' => null])
            ->get()->getResult();

        $user->bank_sub_account_permissions = model(UserPermissionBankSubModel::class)
            ->select(['sub_account_id'])
            ->where(['user_id' => $user->id, 'deleted_at' => null])
            ->get()->getResult();

        $request->user = $user;

        return $request;
    }

    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        if ($this->determineIfUnmatchMobileRoutes($request)) return;
    }

    protected function respondUnauthenticatedResponse()
    {
        $response = Services::response();
        $response->setStatusCode(401);
        $response->setJSON(['code' => 401, 'message' => 'Unathenticated']);

        return $response;
    }
}
