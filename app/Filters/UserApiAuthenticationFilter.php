<?php

namespace App\Filters;

use Config\Services;
use App\Models\CompanyApiModel;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use CodeIgniter\Filters\FilterInterface;

class UserApiAuthenticationFilter implements FilterInterface
{
    public function before(RequestInterface $request, $arguments = null)
    {
        $throttler = service('throttler');
        $capacity = 2;

        $tokenLeft = $throttler->check(md5($request->getIPAddress()), $capacity, SECOND);

        if ($tokenLeft === false) {
            $timeLeft = $throttler->getTokentime();
            return Services::response()->setHeader('X-SePay-UserApi-Retry-After', $time_left)->setStatusCode(429);
        }

        $authenticationHeader = $request->getServer('HTTP_AUTHORIZATION');
        $authenticationHeaderPortion = explode(' ', $authenticationHeader);

        if (count($authenticationHeaderPortion) == 2 && $authenticationHeaderPortion[0] == 'Bearer' && strlen($authenticationHeaderPortion[1]) == 64) {
            $apiKey = $authenticationHeaderPortion[1];

            if (! ctype_alnum($apiKey)) {
                return Services::response()->setBody('Invalid Auth token')->setStatusCode(403);
            }

            $companyApiModel = model(CompanyApiModel::class);

            $result = $companyApiModel->select('tb_autopay_company_api.company_id')
                ->join('tb_autopay_company', 'tb_autopay_company.id = tb_autopay_company_api.company_id')
                ->where([
                    'tb_autopay_company_api.api_key' => $apiKey, 
                    'tb_autopay_company_api.active' => 1, 
                    'tb_autopay_company.active' => 1,
                    'tb_autopay_company.status' => 'Active'
                ])
                ->first();

            if (is_object($result) && is_numeric($result->company_id)) {
                $request->company_id = $result->company_id;
                
                return $request;
            }
    
            return Services::response()->setBody('Unauthorized Access')->setStatusCode(403);
        }

        return Services::response()->setBody('Invalid Auth token')->setStatusCode(403);
    }

    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        //
    }
}
