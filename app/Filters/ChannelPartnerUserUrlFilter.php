<?php

namespace App\Filters;

use CodeIgniter\Filters\FilterInterface;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;

class ChannelPartnerUserUrlFilter implements FilterInterface
{
    /**
     * Do whatever processing this filter needs to do.
     * By default it should not return anything during
     * normal execution. However, when an abnormal state
     * is found, it should return an instance of
     * CodeIgniter\HTTP\Response. If it does, script
     * execution will end and that Response will be
     * sent back to the client, allowing for error pages,
     * redirects, etc.
     *
     * @param RequestInterface $request
     * @param array|null       $arguments
     *
     * @return mixed
     */
    public function before(RequestInterface $request, $arguments = null)
    {
        $session = session();
        $isChannelPartner = $session->get('channel_partner');
        $uri = service('uri');
        $uri1 = $uri->getSegment(1);
      

        $urlAllow = [
            'login' => ['logout', 'index', 'token'],
            'notification' => ['index', 'ajax_list', 'details', 'ajax_mark_read_all_notifications', 'ajax_force_hide_popup_notification', 'ajax_add_device_token', ''],
            'company' => ['profile', 'ajax_get_profile', 'ajax_profile_update', 'plans', 'change_plan', 'cart', 'ajax_add_order', 'ordersuccess', 'configuration', 'ajax_configuration_update', 'ajax_cancel_subscription_change', 'ajax_cancel_pending_subscription'],
            'user' => ['my_profile', 'ajax_get_my_user', 'ajax_my_user_update', 'change_password', 'update_theme'],
            'invoices' => ['index', 'ajax_invoices_list', 'details'],
            'transactions' => ['index_shop', 'ajax_shop_transactions_list', 'ajax_list_stall', 'details', 'ajax_get_telegram_log', 'ajax_get_fcm_log', 'ajax_get_account_balance'],
            'shop' => ['index', 'ajax_index_shop', 'ajax_get_shop', 'ajax_shop_update', 'details', 'transactions', 'ajax_index_transactions', 'qrcodes', 'ajax_get_va', 'createQrCode', 'printQrCode', 'step1', 'ajax_step_1', 'step2', 'ajax_step_2', 'step3', 'shares', 'ajax_index_shares', 'ajax_create_user_by_pos', 'ajax_get_able_assign_pos_permission_user', 'ajax_assign_pos_permission_to_user', 'ajax_revoke_pos_permission_to_user', 'ajax_get_user_by_pos', 'ajax_edit_user_by_pos', 'ajax_shop_larkmessenger_delete', 'ajax_shop_telegram_delete'],
            'ocb' => ['ajax_request_create_qr_shop', 'ajax_confirm_create_qr_shop_with_new_bank_account'],
            'banksubaccount' => ['ajax_get_va', 'ajax_va_update', 'qrcode'],
            'vietinbank' => ['ajax_request_create_qr_shop', 'ajax_confirm_create_qr_shop_with_new_bank_account'],
            'notificationtelegram' => ['index', 'ajax_shop_telegrams_list', 'step1', 'ajax_test_telegram_connection', 'ajax_step1', 'step2', 'ajax_shop_step2', 'step3', 'ajax_test_telegram_message', 'ajax_shop_step3', 'step4', 'ajax_step4', 'edit', 'ajax_edit_step1', 'ajax_edit_step2', 'ajax_edit_step3', 'ajax_shop_telegram_delete'],
            'notificationlarkmessenger' => ['index', 'ajax_shop_larkmessengers_list', 'step1', 'ajax_test_larkmessenger_connection', 'ajax_step1', 'step2', 'ajax_shop_step2', 'step3', 'ajax_test_larkmessenger_message', 'ajax_shop_step3', 'step4', 'ajax_step4', 'edit', 'ajax_edit_step1', 'ajax_edit_step2', 'ajax_edit_step3', 'ajax_shop_larkmessenger_delete'],
            'mobileapp' => ['index', 'shop_step1', 'ajax_shop_step1', 'shop_step2'],
            'coreapi' => ['ocb'],
            'ticket' => ['index', 'ajax_ticket_list', 'details', 'ajax_set_status', 'ajax_reply', 'create', 'ajax_create'],
            'reports' => ['bankaccounts'],
            'statistics' => ['cashflow', 'daily_cashflow', 'transaction'],
            'logs' => ['index', 'ajax_logs_list'],
            'home' => ['changelog']
        ];

        if(!$uri1){
            return;
        }
        
        if (!$isChannelPartner) {
            return;
        }
        
        $allowMethod = $urlAllow[$uri1] ?? [];
        if (!count($allowMethod)) {
            throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound();
        }
    
        $uri2 = $uri->getSegment(2);
        
        if (($uri2 == '' && !in_array('index', $allowMethod)) || ($uri2 && !in_array($uri2, $allowMethod))) {
            throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound();
        }
        
    }




    /**
     * Allows After filters to inspect and modify the response
     * object as needed. This method does not allow any way
     * to stop execution of other after filters, short of
     * throwing an Exception or Error.
     *
     * @param RequestInterface  $request
     * @param ResponseInterface $response
     * @param array|null        $arguments
     *
     * @return mixed
     */
    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        //
    }
}
