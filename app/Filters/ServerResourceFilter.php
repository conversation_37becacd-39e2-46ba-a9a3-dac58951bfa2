<?php

namespace App\Filters;

use App\Libraries\OAuthServer\Traits\HandlesOAuthErrors;
use App\Libraries\Psr7\PsrHttpFactory;
use App\Models\OAuthTokenModel;
use CodeIgniter\Exceptions\PageNotFoundException;
use CodeIgniter\Filters\FilterInterface;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use Config\OAuth2;
use Config\Services;
use Nyholm\Psr7\Factory\Psr17Factory;

class ServerResourceFilter implements FilterInterface
{
    use HandlesOAuthErrors;

    public function before(RequestInterface $request, $arguments = null)
    {
        $routes = Services::routes()->getRoutes();
        $path = $request->getPath();
        $found = false;

        foreach ($routes as $pattern => $handler) {
            $pattern = '#^' . str_replace(['/', '(:any)', '(:num)', '(:alpha)'], ['\\/', '[^/]+', '[0-9]+', '[a-zA-Z]+'], $pattern) . '$#';
            if (preg_match($pattern, $path)) {
                $found = true;
                break;
            }
        }

        if (! $found) {
            return;
        }

        helper('general');

        if (! check_oauth2_allowed_ip()) {
            throw PageNotFoundException::forPageNotFound();
        }

        $response = Services::response();
        $config = config(OAuth2::class);
        $throttler = Services::throttler();
        $ip = $request->getIPAddress();

        if (! $throttler->check(md5("rate_limit:$ip"), $config->maxRequestsPerSecond, SECOND)) {
            return $response
                ->setStatusCode(429)
                ->setJSON([
                    'error' => 'rate_limit_exceeded',
                    'error_description' => 'Rate limit exceeded',
                ]);
        }

        return $this->withErrorHandling(function () use ($request) {
            $psr17Factory = new Psr17Factory();
            $psrHttpFactory = new PsrHttpFactory($psr17Factory, $psr17Factory);

            $response = oauth2()
                ->resourceServer()
                ->validateAuthenticatedRequest($psrHttpFactory->createRequest($request));

            $request->oauthAttributes = $response->getAttributes();

            $accessTokenId = $response->getAttribute('oauth_access_token_id');
            if ($accessTokenId) {
                model(OAuthTokenModel::class)->update(
                    $accessTokenId,
                    ['last_used_at' => date('Y-m-d H:i:s')]
                );
            }
        });
    }

    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        //
    }
}
