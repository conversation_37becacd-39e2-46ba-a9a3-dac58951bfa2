<?php

namespace App\Filters;

use App\Models\OutputDeviceModel;
use CodeIgniter\Filters\FilterInterface;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;

class SpeakerBillingFilter implements FilterInterface
{
    /**
     * Do whatever processing this filter needs to do.
     * By default it should not return anything during
     * normal execution. However, when an abnormal state
     * is found, it should return an instance of
     * CodeIgniter\HTTP\Response. If it does, script
     * execution will end and that Response will be
     * sent back to the client, allowing for error pages,
     * redirects, etc.
     *
     * @param RequestInterface $request
     * @param array|null       $arguments
     *
     * @return mixed
     */
    public function before(RequestInterface $request, $arguments = null)
    {
        helper('general');
        $session = session();

        if ($session->get('user_logged_in') && isset($session->get('user_logged_in')['company_id'])) {
            $speakerBillingFeature = service('speakerBillingFeature');
            $speakerBillingFeature->withCompanyContext($session->get('user_logged_in')['company_id']);
            $isSubscribedSpeakerBillingProduct = $speakerBillingFeature->companyContext()->isSubscribedSpeakerBillingProduct();

            $session->set('speaker_billing', $isSubscribedSpeakerBillingProduct ? 1 : 0);

            if ($isSubscribedSpeakerBillingProduct) {
                is_bank_box_support(true);
            }

            $allowedRoutes = [
                '',
                'home(/.*)?',
                'login(/.*)?',
                'register(/.*)?',
                'forgotpassword(/.*)?',
                'user/profile(/.*)?',
                'transactions(/.*)?',
                'notification(/.*)?',
                'user(/.*)?',
                'company/profile',
                'company/ajax_get_profile',
                'company/ajax_profile_update',
                'company/change_plan',
                'company/ordersuccess',
                'company/cart',
                'company/ajax_add_order',
                'company/ajax_checkout_speaker_billing_order',
                'company/ajax_request_vat_invoice/(.*)?',
                'oauth(/.*)?',
                'ticket(/.*)?',
                'invoices(/.*)?',
                'logs(/.*)?',
                'acb/ajax_request_delete_individual_bank_account',
                'acb/ajax_confirm_delete_individual_bank_account',
                'acb/ajax_request_add_individual_real_va',
                'acb/ajax_confirm_add_individual_real_va',
                'acb/ajax_add_enterprise_real_va',
                'acb/ajax_request_delete_enterprise_bank_account',
                'acb/ajax_confirm_delete_enterprise_bank_account',
                'bankaccount/ajax_get_bank_account/(.*)?',
                'bankaccount/ajax_bank_account_update',
                'bidv/ajax_delete_bank_account',
                'bidv/ajax_request_create_personal_va',
                'bidv/ajax_confirm_create_personal_va',
                'bidv/ajax_add_real_va',
                'klb/ajax_create_va',
                'mbb/ajax_add_real_va',
                'mbb/ajax_request_delete_bank_account',
                'mbb/ajax_confirm_delete_bank_account',
                'ocb/ajax_generate_va_number',
                'ocb/ajax_register_merchant_step_1',
                'ocb/ajax_register_merchant_step_2',
                'ocb/ajax_add_app_va',
                'vietinbank/ajax_request_delete_bank_account',
                'vietinbank/ajax_confirm_delete_bank_account',
                'vpbank/ajax_delete_bank_account',
                'outputdevice(.*)?',
                'banksubaccount(/.*)?',
                'bankBox(/.*)?',
                'vpbank/authorizeCallback',
                'vpbank/loginErrorCallback',
                'vpbank/loginCancelCallback',
                'vpbank/otpConfirmCallback',
                'vpbank/otpErrorCallback',
                'vpbank/otpCloseCallback',
                'speakerorder(/.*)?',
                'abbank/ajax_delete_bank_account/(\d+)/(\d+)',
            ];

            $uri = $request->getUri();
            $currentPath = ltrim($uri->getPath(), '/');

            if ($isSubscribedSpeakerBillingProduct) {
                $isAllowed = false;

                if (is_admin()) {
                    $isAllowed = true;
                } else {
                    foreach ($allowedRoutes as $pattern) {
                        $regex = '#^' . $pattern . '$#';
                        if (preg_match($regex, $currentPath)) {
                            $isAllowed = true;
                            break;
                        }
                    }
                }

                if (! $isAllowed) {
                    show_404();
                }
            }

            $outputDevices = model(OutputDeviceModel::class)
                ->select('bank_id')
                ->where('company_id', $session->get('user_logged_in')['company_id'])
                ->findAll();

            $abbankSupport = false;
            foreach ($outputDevices as $device) {
                if ($device['bank_id'] == 19) {
                    $abbankSupport = true;
                    break;
                }
            }

            $session->set('speaker_billing_abbank_support', $abbankSupport);
        }
    }

    /**
     * Allows After filters to inspect and modify the response
     * object as needed. This method does not allow any way
     * to stop execution of other after filters, short of
     * throwing an Exception or Error.
     *
     * @param RequestInterface  $request
     * @param ResponseInterface $response
     * @param array|null        $arguments
     *
     * @return mixed
     */
    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        //
    }
}
