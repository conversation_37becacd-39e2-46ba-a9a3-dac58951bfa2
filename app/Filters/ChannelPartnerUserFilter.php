<?php

namespace App\Filters;

use App\Models\CompanyModel;
use App\Models\ChannelPartnerModel;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use CodeIgniter\Filters\FilterInterface;

class ChannelPartnerUserFilter implements FilterInterface
{
    /**
     * Do whatever processing this filter needs to do.
     * By default it should not return anything during
     * normal execution. However, when an abnormal state
     * is found, it should return an instance of
     * CodeIgniter\HTTP\Response. If it does, script
     * execution will end and that Response will be
     * sent back to the client, allowing for error pages,
     * redirects, etc.
     *
     * @param RequestInterface $request
     * @param array|null       $arguments
     *
     * @return mixed
     */
    public function before(RequestInterface $request, $arguments = null)
    {
        $session = session();
        $loggedUser = $session->get('user_logged_in');

        if (!$loggedUser) return $request;

        $company = model(CompanyModel::class)->where(['id' => $loggedUser['company_id'], 'active' => 1])->first();

        if (!$company) {
            $session->remove('user_logged_in');
            return redirect()->to(base_url('login'));
        }
        if (!$company->channel_partner_id) return $request;

        $channelPartner = model(ChannelPartnerModel::class)->where(['id' => $company->channel_partner_id, 'active' => 1])->asArray()->first();

        if (!$channelPartner) {
            $session->remove('user_logged_in');
            return redirect()->to(base_url('login'));
        }

        $session->set('channel_partner', [
            'id' => $channelPartner['id'],
            'code' => $channelPartner['code'],
            'logo_path' => $channelPartner['logo_path'],
            'icon_path' => $channelPartner['icon_path'],
        ]);

        return $request;
    }

    /**
     * Allows After filters to inspect and modify the response
     * object as needed. This method does not allow any way
     * to stop execution of other after filters, short of
     * throwing an Exception or Error.
     *
     * @param RequestInterface  $request
     * @param ResponseInterface $response
     * @param array|null        $arguments
     *
     * @return mixed
     */
    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        // log_message('error', json_encode($response));
    }
}
