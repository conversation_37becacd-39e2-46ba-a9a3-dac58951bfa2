<?php

namespace App\Filters;

use CodeIgniter\API\ResponseTrait;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use CodeIgniter\Filters\FilterInterface;

class RateLimitingFilter implements FilterInterface
{
    public function before(RequestInterface $request, $arguments = null)
    {
        $throttler = service('throttler');
        $session = service('session');
        
        $rateLimitingConfig = config(\Config\RateLimiting::class);
        $throttles = $rateLimitingConfig->throttles ?? [];

        $result = array_filter($throttles, function($throttle) use ($request) {
            return $this->throttlable($request, $throttle);
        });

        if (!count($result)) return $request;

        $throttle = array_values($result)[0];

        $identifier = isset($session->get('user_logged_in')['user_id']) && isset($session->get('user_logged_in')['company_id'])
            ? $session->get('user_logged_in')['company_id']
            : $request->getIPAddress();

        if ($throttler->check(md5($identifier.$throttle['matcher']), $throttle['max_attempts'], $throttle['decay_rate']) === false) {
            $response = service('response');
            $response->setStatusCode(429);
            $response->setJSON([
                'status' => false,
                'message' => 'Quý khách đã truy vấn quá nhiều lần, vui lòng thử lại sau.'
            ]);

            return $response;
        }
    }
    
    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        //
    }

    protected function throttlable($request, $throttle)
    {
        $path = $request->getUri()->getPath();

        $path = trim($path, '/');
        $throttle = trim($throttle['matcher'], '/');

        if ($path === $throttle) return true;

        return false;
    }
}
