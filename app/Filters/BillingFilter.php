<?php

namespace App\Filters;

use CodeIgniter\Filters\FilterInterface;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;

class BillingFilter implements FilterInterface
{
    /**
     * Do whatever processing this filter needs to do.
     * By default it should not return anything during
     * normal execution. However, when an abnormal state
     * is found, it should return an instance of
     * CodeIgniter\HTTP\Response. If it does, script
     * execution will end and that Response will be
     * sent back to the client, allowing for error pages,
     * redirects, etc.
     *
     * @param RequestInterface $request
     * @param array|null       $arguments
     *
     * @return mixed
     */
    public function before(RequestInterface $request, $arguments = null)
    {
        
        $session = session();

        if (!$session->get('user_logged_in')) {
            return redirect()->to(base_url('login'));
        }

        if(isset($session->get('user_logged_in')['company_id'])) {
            $user_id = $session->get('user_logged_in')['user_id'];
    
            $companyUserModel = new \App\Models\CompanyUserModel();

            $company = $companyUserModel->select('tb_autopay_company_user.user_id,tb_autopay_company_user.role, tb_autopay_company.status')->join("tb_autopay_company","tb_autopay_company.id=tb_autopay_company_user.company_id")->where(['tb_autopay_company.active'=>1,'tb_autopay_company_user.user_id'=>$user_id])->get()->getRow();
    
            if(!is_object($company)) {
                $session->remove("user_logged_in");
                return redirect()->to(base_url('login'));
            }

            if($company->status !='Active') {
                helper('general');

                if(in_array($company->role, ['Admin', 'SuperAdmin'])) { 
                    if($company->status == 'Pending') {
                        set_alert("error","Vui lòng đăng ký gói dịch vụ để được sử dụng các tính năng của SePay");
                        return redirect()->to(base_url('company/plans'));
                    }   else if($company->status == 'Suspended') {
                        set_alert("error","Vui lòng thanh toán hóa đơn nợ");
                        return redirect()->to(base_url('invoices'));
                    } else {
                        $session->remove("user_logged_in");
                        return redirect()->to(base_url('login'));
                    }
    
                } else {
                    $session->remove("user_logged_in");
                    return redirect()->to(base_url('login'));
                }
            }
           


        } else {
            $session->remove("user_logged_in");
            return redirect()->to(base_url('login'));
        }


        


    }

    /**
     * Allows After filters to inspect and modify the response
     * object as needed. This method does not allow any way
     * to stop execution of other after filters, short of
     * throwing an Exception or Error.
     *
     * @param RequestInterface  $request
     * @param ResponseInterface $response
     * @param array|null        $arguments
     *
     * @return mixed
     */
    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        //
    }
}
