<?php

namespace App\Filters;

use CodeIgniter\Filters\FilterInterface;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use Config\Services;

class InternalApiAuthenticationFilter implements FilterInterface
{
    /**
     * Do whatever processing this filter needs to do.
     * By default it should not return anything during
     * normal execution. However, when an abnormal state
     * is found, it should return an instance of
     * CodeIgniter\HTTP\Response. If it does, script
     * execution will end and that Response will be
     * sent back to the client, allowing for error pages,
     * redirects, etc.
     *
     * @param RequestInterface $request
     * @param array|null       $arguments
     *
     * @return mixed
     */
    public function before(RequestInterface $request, $arguments = null)
    {
        $config = config('InternalApi');
        $accessToken = $this->getAccessToken($request);
        $clientIP = $request->getIPAddress();
    
        $matchedConfig = $this->getMatchedConfig($config->apps ?? [], $accessToken);
    
        if (empty($matchedConfig)) {
            return $this->responseResult(401, "Token không hợp lệ!");
        }
    
        $allowedIPsConfig = $matchedConfig[0]['allowedIPs'] ?? [];
        $tokenVerifyConfig = $matchedConfig[0]['tokenVerify'] ?? "";
        $filterClass = $matchedConfig[0]['filter'] ?? null;
    
        if ($accessToken !== $tokenVerifyConfig) {
            return $this->responseResult(401, "Token không hợp lệ!");
        }
    
        if (!in_array($clientIP, $allowedIPsConfig)) {
            return $this->responseResult(403, "IP không được phép truy cập!");
        }
    
        return $this->applyFilter($filterClass, 'before', $request, $arguments) ?? $request;
    }

    /**
     * Allows After filters to inspect and modify the response
     * object as needed. This method does not allow any way
     * to stop execution of other after filters, short of
     * throwing an Exception or Error.
     *
     * @param RequestInterface  $request
     * @param ResponseInterface $response
     * @param array|null        $arguments
     *
     * @return mixed
     */
    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        $config = config('InternalApi');
        $accessToken = $this->getAccessToken($request);

        $matchedConfig = $this->getMatchedConfig($config->apps ?? [], $accessToken);
        $filterClass = $matchedConfig[0]['filter'] ?? null;

        return $this->applyFilter($filterClass, 'after', $request, $response, $arguments) ?? $response;
    }

    protected function applyFilter(?string $filterClass, string $method, RequestInterface $request, ...$args)
    {
        if (!empty($filterClass) && class_exists($filterClass)) {
            $filterInstance = new $filterClass();
            if (method_exists($filterInstance, $method)) {
                $response = $filterInstance->{$method}($request, ...$args);
                if ($response instanceof ResponseInterface) {
                    return $response;
                }
            }
        }
        return null;
    }

    protected function getAccessToken(RequestInterface $request)
    {
        return explode('Bearer ', $request->getHeaderLine('Authorization'))[1] ?? '';
    }

    protected function getMatchedConfig(array $configs, string $accessToken)
    {
        return array_values(array_filter($configs, function ($item) use ($accessToken) {
            return $item['tokenVerify'] === $accessToken;
        }));
    }

    protected function responseResult($code, $message, $data = null)
    {
        $response = Services::response();
        $data_res = ['code' => $code, 'message' => $message];
        if ($data !== null) {
            $data_res['data'] = $data;
        }
        $response->setStatusCode($code);
        $response->setJSON($data_res);

        return $response;
    }
}
