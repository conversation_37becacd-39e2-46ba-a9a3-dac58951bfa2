<?php

namespace App\Filters;

use Exception;
use App\Models\CompanyModel;
use App\Models\MerchantModel;
use CodeIgniter\Config\Services;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use App\Builders\Company\CompanyBuilder;
use App\Models\MerchantAccessTokenModel;
use CodeIgniter\Filters\FilterInterface;
use App\Controllers\Merchant\V1\BaseController;
use App\Builders\BankAccount\BankAccountBuilder;
use App\Builders\Transaction\TransactionBuilder;
use App\Builders\BankSubAccount\BankSubAccountBuilder;

class MerchantApiAuthenticationFilter implements FilterInterface
{
    protected $merchantConfig;

    public function __construct()
    {
        $this->merchantConfig = config(\App\Config\Merchant::class);
    }

    public function before(RequestInterface $request, $arguments = null)
    {
        if ($this->determineIfUnmatchMerchantRoutes($request)) return;

        $clientMessageId = trim($request->getHeaderLine('Client-Message-Id'));

        if (!$clientMessageId) {
            return $this->respondMissingClientMessageIdHeaderResponse();
        }

        $authorizationHeader = $request->getHeaderLine('Authorization');
        $authorizationHeaderPortion = explode(' ', $authorizationHeader);
        $accessToken = trim($authorizationHeaderPortion[1] ?? '');

        if ($authorizationHeaderPortion[0] !== 'Bearer' || !$accessToken) {
            return $this->respondUnauthorizedResponse();
        }

        $merchantAccessToken = model(MerchantAccessTokenModel::class)->where([
            'token' => $accessToken,
            'expires_at >' => date('Y-m-d H:i:s')
        ])->get()->getRow();

        if (!$merchantAccessToken) {
            return $this->respondUnauthenticatedResponse();
        }

        $merchant = model(MerchantModel::class)->where(['id' => $merchantAccessToken->merchant_id, 'active' => 1])->get()->getRow();

        if (!$merchant) {
            return $this->respondUnauthenticatedResponse();
        }

        $request->merchant = $merchant;
        $request->clientMessageId = $clientMessageId;

        try {
            $this->authorizeIncomingRequest($request);
        } catch (\Exception $e) {
            return $this->respondNotFoundResponse($e->getMessage());
        }

        if ($this->merchantConfig->debug) {
            log_message('error', 'Merchant Client Message ID: ' . $clientMessageId);
            log_message('error', "[{$clientMessageId}] API URL: " . $request->getUri()->getPath());
            log_message('error', "[{$clientMessageId}] Request headers: " . json_encode(array_map(function($header) {
                return $header->getValue();
            }, $request->getHeaders())));
            log_message('error', "[{$clientMessageId}] Request payload: " . json_encode($request->getJSON()));
        }

        return $request;
    }

    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        if ($this->determineIfUnmatchMerchantRoutes($request)) return;

        $body = (array) json_decode($response->getJSON(), true);
        $path = $request->getUri()->getPath();
        $availableCompanyIds = array_map(
            function($row) { return $row->id; }, 
            model(CompanyModel::class)->select(['id'])->where(['merchant_id' => $request->merchant->id])->get()->getResult()
        );
        $companyIdField = preg_match('/company/', $path) && !preg_match('/company\/counter/', $path) ? 'id' : 'company_id';

        $deepResponseCallback = function($data) use (&$deepResponseCallback, $companyIdField, $availableCompanyIds, $request) {
            foreach ($data as $key => $value) {
                if ($key === $companyIdField && !in_array($value, $availableCompanyIds)) {
                    throw new Exception;
                }

                if ($key === 'merchant_id' && $value != $request->merchant->id) {
                    throw new Exception;
                } else if ($key === 'merchant_id' && $value == $request->merchant->id) {
                    unset($data[$key]);
                }

                if (is_array($value)) {
                    $data[$key] = $deepResponseCallback($value);
                }
            }

            return $data;
        };

        try {
            $body = $deepResponseCallback($body);
        } catch (\Exception $e) {
            log_message('error', "[{$request->clientMessageId}] Access violation request: " . json_encode([
                'path' => (string) $request->getUri(),
                'data' => $request->getJSON(),
            ]));
            log_message('error', "[{$request->clientMessageId}] Access violation response " . $response->getStatusCode() . ': ' . json_encode($body));
            return $this->respondUnauthorizedResponse();
        }

        if ($this->merchantConfig->debug) {
            log_message('error', "[{$request->clientMessageId}] Response " . $response->getStatusCode() . ': ' . json_encode(json_decode($response->getJSON())));
        }

        $response->setBody(json_encode($body));

        return $response;
    }

    protected function determineIfUnmatchMerchantRoutes($request) 
    {
        $path = $request->getUri()->getPath();

        return !preg_match('/(?=merchant\/.*)/', $path) || preg_match('/(?=merchant\/.*\/token\/create)/', $path);
    }

    protected function respondUnauthenticatedResponse()
    {
        $response = Services::response();
        $response->setStatusCode(401);
        $response->setJSON(['code' => 401, 'message' => 'Unauthorized']);

        return $response;
    }

    protected function respondUnauthorizedResponse()
    {
        $response = Services::response();
        $response->setStatusCode(403);
        $response->setJSON(['code' => 403, 'message' => 'Unauthorized']);

        return $response;
    }

    protected function respondNotFoundResponse($message)
    {
        $response = Services::response();
        $response->setStatusCode(404);
        $response->setJSON(['code' => 404, 'message' => $message]);

        return $response;
    }

    protected function respondMissingClientMessageIdHeaderResponse()
    {
        $response = Services::response();
        $response->setStatusCode(400);
        $response->setJSON(['code' => 400, 'message' => 'Client-Message-Id header is required']);

        return $response;
    }

    protected function authorizeIncomingRequest($request)
    {
        $data = (array) $request->getJSON();
        $path = $request->getUri()->getPath();
        $query = $request->getUri()->getQuery();
        parse_str($query, $params);

        // Authorize data & query params
        $flattenData = array_flatten_with_dots($data);
        $flattenParams = array_flatten_with_dots($params);

        foreach (array_merge($flattenData, $flattenParams) as $key => $value) {
            if (preg_match('/company_id/', $key) && !CompanyBuilder::make()->whereMerchantId($request->merchant->id)->whereId($value)->count()) {
                throw new Exception(BaseController::COMPANY_ID_NOT_EXIST_MESSAGE);
            }

            if (preg_match('/bank_account_id/', $key) && !BankAccountBuilder::make()->whereMerchantId($request->merchant->id)->whereId($value)->count()) {
                throw new Exception(BaseController::BANK_ACCOUNT_ID_NOT_EXIST_MESSAGE);
            }

            if (preg_match('/va_id/', $key) && !BankSubAccountBuilder::make()->whereMerchantId($request->merchant->id)->whereId($value)->count()) {
                throw new Exception(BaseController::BANK_SUB_ACCOUNT_ID_NOT_EXIST_MESSAGE);
            }

            if (preg_match('/transaction_id/', $key) && !TransactionBuilder::make()->whereMerchantId($request->merchant->id)->whereId($value)->count()) {
                throw new Exception(BaseController::TRANSACTION_ID_NOT_EXIST_MESSAGE);
            }
        }

        // Authorize path
        if (preg_match('/company\/.*\/([\d]+)/', $path, $matches) 
            && count($matches) == 2 
            && !CompanyBuilder::make()->whereMerchantId($request->merchant->id)->whereId($matches[1])->count()) {
            throw new Exception(BaseController::COMPANY_ID_NOT_EXIST_MESSAGE);
        }

        if (preg_match('/bankAccount\/.*\/([\d]+)/', $path, $matches) 
            && count($matches) == 2 
            && !BankAccountBuilder::make()->whereMerchantId($request->merchant->id)->whereId($matches[1])->count()) {
            throw new Exception(BaseController::BANK_ACCOUNT_ID_NOT_EXIST_MESSAGE);
        }

        if (preg_match('/VA\/.*\/([\d]+)/', $path, $matches) 
            && count($matches) == 2 
            && !BankSubAccountBuilder::make()->whereMerchantId($request->merchant->id)->whereId($matches[1])->count()) {
            throw new Exception(BaseController::BANK_SUB_ACCOUNT_ID_NOT_EXIST_MESSAGE);
        }

        if (preg_match('/transaction\/.*\/([\w\-]+)/', $path, $matches) 
            && count($matches) == 2 
            && !TransactionBuilder::make()->whereMerchantId($request->merchant->id)->whereId($matches[1])->count()) {
            throw new Exception(BaseController::TRANSACTION_ID_NOT_EXIST_MESSAGE);
        }

        return true;
    }
}
