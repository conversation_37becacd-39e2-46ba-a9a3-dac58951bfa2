<?php

namespace App\Filters;

use CodeIgniter\Filters\FilterInterface;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;

use App\Controllers\Internalapi\V1\Company;
use App\Features\PaymentGateway\PaymentGatewayFeature;
use App\Models\UserModel;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\URI;

class AuthFilter implements FilterInterface
{
    /**
     * Do whatever processing this filter needs to do.
     * By default it should not return anything during
     * normal execution. However, when an abnormal state
     * is found, it should return an instance of
     * CodeIgniter\HTTP\Response. If it does, script
     * execution will end and that Response will be
     * sent back to the client, allowing for error pages,
     * redirects, etc.
     *
     * @param RequestInterface $request
     * @param array|null       $arguments
     *
     * @return mixed
     */
    public function before(RequestInterface $request, $arguments = null)
    {
        $session = session();

        if (!$session->get('user_logged_in')) {
            $uri = current_url(true);
            $fullUrl = URI::createURIString($uri->getScheme(), $uri->getAuthority(), $uri->getPath(), $uri->getQuery(), $uri->getFragment());

            $session->set('redirect_url', $fullUrl);
            return redirect()->to(base_url('login'));
        }

        if (empty(Company::check_login_user())) {
            $session->remove('user_logged_in');
        }
        
        if(isset($session->get('user_logged_in')['user_id']) && isset($session->get('user_logged_in')['company_id'])) {
            $user_id = $session->get('user_logged_in')['user_id'];

            $userModel = new \App\Models\UserModel();
    
            $user = $userModel->where(['active'=> 1,'id'=>$user_id])->first();
    
            $companyUserModel = new \App\Models\CompanyUserModel();

            $company = $companyUserModel->select('tb_autopay_company_user.user_id,tb_autopay_company.status')->join("tb_autopay_company","tb_autopay_company.id=tb_autopay_company_user.company_id")->where(['tb_autopay_company.active'=>1,'tb_autopay_company_user.user_id'=>$user_id])->get()->getRow();
    
            $lastPasswordChanged = $session->get('user_logged_in')['last_password_changed'] ?? null;

            $isOutdatedSession = !is_null($user->last_password_changed) 
                && (is_null($lastPasswordChanged) || strtotime($lastPasswordChanged) < strtotime($user->last_password_changed));

            if(!is_object($user) || !is_object($company) || $isOutdatedSession) {
                $session->remove("user_logged_in");
                return redirect()->to(base_url('login'));
            }

        } else {
            $session->remove("user_logged_in");
            return redirect()->to(base_url('login'));
        }
    }

    /**
     * Allows After filters to inspect and modify the response
     * object as needed. This method does not allow any way
     * to stop execution of other after filters, short of
     * throwing an Exception or Error.
     *
     * @param RequestInterface  $request
     * @param ResponseInterface $response
     * @param array|null        $arguments
     *
     * @return mixed
     */
    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        //
    }
}
