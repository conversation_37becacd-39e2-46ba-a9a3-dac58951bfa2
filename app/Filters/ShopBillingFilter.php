<?php

namespace App\Filters;

use CodeIgniter\Filters\FilterInterface;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use App\Models\CompanySubscriptionModel;
use App\Models\CompanyUserModel;
use CodeIgniter\HTTP\IncomingRequest;

class ShopBillingFilter implements FilterInterface
{
    /**
     * Do whatever processing this filter needs to do.
     * By default it should not return anything during
     * normal execution. However, when an abnormal state
     * is found, it should return an instance of
     * CodeIgniter\HTTP\Response. If it does, script
     * execution will end and that Response will be
     * sent back to the client, allowing for error pages,
     * redirects, etc.
     *
     * @param RequestInterface $request
     * @param array|null       $arguments
     *
     * @return mixed
     */
    public function before(RequestInterface $request, $arguments = null)
    {
        /** @var IncomingRequest $request */
        helper(['general']);
        
        $session = session();

        if ($session->get('user_logged_in') && isset($session->get('user_logged_in')['company_id'])) {
            $companySubscriptionModel = model(CompanySubscriptionModel::class);
            $subscription = $companySubscriptionModel->where(['company_id' => $session->get('user_logged_in')['company_id'], 'shop_limit >' => 0])->first();
            
            if ($subscription) {
                $filtered = $this->filterRouteForShopBilling($request, $subscription);
                
                if ($filtered !== true) {
                    return $filtered;
                }
            }
            
            $session->set('shop_billing', $subscription ? 1 : 0);
        }
    }

    /**
     * Allows After filters to inspect and modify the response
     * object as needed. This method does not allow any way
     * to stop execution of other after filters, short of
     * throwing an Exception or Error.
     *
     * @param RequestInterface  $request
     * @param ResponseInterface $response
     * @param array|null        $arguments
     *
     * @return mixed
     */
    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        //
    }
    
    protected function filterRouteForShopBilling($request, $subscription)
    {
        $session = session();
        
        if ($subscription->is_trial && $subscription->status === 'Terminated') {
            $allowedRoutes = [
                '',
                'store',
                'store/details/[0-9]+',
                'store/ajax_index_store',
                'store/ajax_edit/[0-9]+',
                'store/ajax_delete/[0-9]+',
                'ticket(/.*)?',
                'invoices(/.*)?',
                'company(/.*)?',
                'logs(/.*)?',
                'user(/.*)?',
                'login(/.*)?',
                'notification(/.*)?',
                'creditlog(/.*)?',
                'home/changelog'
            ];
        } else {
            $allowedRoutes = [
                '',
                'store(/.*)?',
                'transactions(/.*)?',
                'telegram(/.*)?',
                'notificationviber(/.*)?',
                'larkmessenger(/.*)?',
                'staff(/.*)?',
                'mobilesharing(/.*)?',
                'outputdevice(/.*)?',
                'speaker(/.*)?',
                'ticket(/.*)?',
                'invoices(/.*)?',
                'company(/.*)?',
                'logs(/.*)?',
                'user(/.*)?',
                'login(/.*)?',
                'notification(/.*)?',
                'acb/ajax_request_delete_individual_bank_account',
                'acb/ajax_confirm_delete_individual_bank_account',
                'acb/ajax_request_add_individual_real_va',
                'acb/ajax_confirm_add_individual_real_va',
                'acb/ajax_add_enterprise_real_va',
                'bankaccount/ajax_get_bank_account/(.*)?',
                'bankaccount/ajax_bank_account_update',
                'bidv/ajax_delete_bank_account',
                'bidv/ajax_request_create_personal_va',
                'bidv/ajax_confirm_create_personal_va',
                'bidv/ajax_add_real_va',
                'klb/ajax_create_va',
                'mbb/ajax_add_real_va',
                'mbb/ajax_request_delete_bank_account',
                'mbb/ajax_confirm_delete_bank_account',
                'ocb/ajax_generate_va_number',
                'ocb/ajax_register_merchant_step_1',
                'ocb/ajax_register_merchant_step_2',
                'ocb/ajax_add_app_va',
                'vietinbank/ajax_request_delete_bank_account',
                'vietinbank/ajax_confirm_delete_bank_account',
                'vpbank/ajax_delete_bank_account',
                'banksubaccount(/.*)?',
                'bankBox(/.*)?',
                'vpbank/authorizeCallback',
                'vpbank/loginErrorCallback',
                'vpbank/loginCancelCallback',
                'vpbank/otpConfirmCallback',
                'vpbank/otpErrorCallback',
                'vpbank/otpCloseCallback',
                'speakerorder(/.*)?',
                'creditlog(/.*)?',
                'home/changelog'
            ];
        }
        
        $uri = $request->getUri();
        $currentPath = ltrim($uri->getPath(), '/');

        $isAllowed = false;

        if (is_admin()) {
            $isAllowed = true;
        } else {
            foreach ($allowedRoutes as $pattern) {
                $regex = '#^' . $pattern . '$#';
                if (preg_match($regex, $currentPath)) {
                    $isAllowed = true;
                    break;
                }
            }
        }

        if (! $isAllowed) {
            $user = model(CompanyUserModel::class)
                ->where('user_id', $session->get('user_logged_in')['user_id'])
                ->where('company_id', $session->get('user_logged_in')['company_id'])
                ->first();
                
            if (!$user) {
                return redirect()->to(base_url('login'));
            }
            
            if ($subscription->is_trial && $subscription->status === 'Terminated') {
                if ($user->role == 'SuperAdmin') {
                    set_alert('error', 'Bạn đã hết thời hạn dùng thử, vui lòng nâng cấp gói để tiếp tục sử dụng dịch vụ');
                    return redirect()->to(base_url('company/change_plan'));
                } else {
                    set_alert('error', 'Tính năng hiện không khả dụng, vui lòng liên hệ quản trị viên');
                    return redirect()->to(base_url('/'));
                }
            } else {
                show_404();
                return false;
            }
        }
        
        return true;
    }
}
