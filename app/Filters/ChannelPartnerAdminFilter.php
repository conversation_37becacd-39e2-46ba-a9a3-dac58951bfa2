<?php

namespace App\Filters;

use App\Models\ChannelPartnerCompanyModel;
use App\Models\ChannelPartnerModel;
use App\Models\ChannelPartnerOfficerModel;
use CodeIgniter\Filters\FilterInterface;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use Config\Services;

class ChannelPartnerAdminFilter implements FilterInterface
{
    /**
     * Do whatever processing this filter needs to do.
     * By default it should not return anything during
     * normal execution. However, when an abnormal state
     * is found, it should return an instance of
     * CodeIgniter\HTTP\Response. If it does, script
     * execution will end and that Response will be
     * sent back to the client, allowing for error pages,
     * redirects, etc.
     *
     * @param RequestInterface $request
     * @param array|null       $arguments
     *
     * @return mixed
     */
    
    public function before(RequestInterface $request, $arguments = null)
    {
     
        $path = $request->getUri()->getPath();

        // lấy bọc lọc dữ liệu đầu vào
        $data_input = $this->getRequestData($request);
        $company_id = $data_input['company_id'];
        $channel_partner_id = $data_input['channel_partner_id'];
        $channel_partner_officer_id = $data_input['channel_partner_officer_id'];

        if($path == "internalapi/v1/user/check_user"){
            return true;
        }

         // Loại trừ company không thuộc hệ thống channel partner officer
         if(!empty($company_id)){
            $result = $this->checkExitDataCompany($company_id);
            
            if(empty($result)){
                return $this->responseResult(404, "Không tìm thấy đường dẫn");
            }
        }

        // Mảng API partner tĩnh không được phép truy cập
        $list_path_not_allowed = [
            'internalapi/v1/cpacommission/update_status',
            'internalapi/v1/user/all',
            'internalapi/v1/order/all',
            'internalapi/v1/user/detail/{id}',
        ];

        // Kiểm tra các đường dẫn tĩnh
        if (!empty($this->exceptUriDynamic($path, $list_path_not_allowed))) {
            return $this->responseResult(404, "Không tìm thấy đường dẫn");
        }
        
        

        // API tĩnh bắt buộc có channel_partner_id
        $list_path_required_partner_id = [
            "internalapi/v1/cpaofficer/create_partner_officer",
            "internalapi/v1/cpaofficer/login_officer",
            "internalapi/v1/cpaofficer/active_account",
            "internalapi/v1/cpacommission/create",
            
            "internalapi/v1/package/all",
            "internalapi/v1/cpaofficer/all",
            "internalapi/v1/invoice/invoice_officer_all"

        ];
        
        if(in_array($path,$list_path_required_partner_id)){
            if(empty($channel_partner_id)){
                return $this->responseResult(404, "Không tìm thấy đường dẫn");
            }
            $check_partner = model(ChannelPartnerModel::class)->where(['id'=>$channel_partner_id])->find();
            if(empty($check_partner)){
                return $this->responseResult(404, "Không tìm thấy đường dẫn");
            }
            // vô api
            return true;
        }

        $list_path_allowed_dynamic = [
            'internalapi/v1/cpawithdraw/detail/{id}',
            'internalapi/v1/cpacommission/detail/{id}',
            'internalapi/v1/company/detail/{id}',
            'internalapi/v1/user/detail/{id}',

        ];
        // API động bắt buộc có channel_partner_id và channel_partner_officer_id
        if (!empty($this->exceptUriDynamic($path, $list_path_allowed_dynamic))) {
            if (!$this->hasValidDataPartner($channel_partner_id,$channel_partner_officer_id)) {
                return $this->responseResult(404, "Không tìm thấy đường dẫn");
            }
    
            return true;
        }
        

        // Kiểm tra điều kiện sử dụng API cho tất cả api còn lại
        if (!$this->hasValidDataPartner($channel_partner_id,$channel_partner_officer_id)) {
            return $this->responseResult(404, "Không tìm thấy đường dẫn");
        }

        


    }

    /**
     * Kiểm tra điều kiện dữ liệu POST hợp lệ
     *
     * @param array $inputPost
     * @return bool
     */
    protected function checkExitDataCompany($id){
        $ChannelPartnerCompany = model(ChannelPartnerCompanyModel::class);
            $exits_data = $ChannelPartnerCompany->where(['company_id'=>$id])->get()->getRowArray();

            if(!empty($exits_data)){
                return $exits_data;
            }
            return false;
    }

    protected function hasValidDataPartner($channel_partner_id,$channel_partner_officer_id): bool
    {
        if (!empty($channel_partner_id) && !empty($channel_partner_officer_id)) {

            $ChannelPartnerOfficerModel = model(ChannelPartnerOfficerModel::class);

            $check_exit = $ChannelPartnerOfficerModel->where([
                'id' => $channel_partner_officer_id,
                'channel_partner_id' => $channel_partner_id
            ])->get()->getRowArray();

            if (empty($check_exit)) {
                return false;
            }

            return true; 
        }

        return false;
    }

    protected function exceptUriDynamic($path, $except_path_array) {
        foreach ($except_path_array as $except_path) {
            // Thay thế {id} bằng biểu thức chính quy để kiểm tra bất kỳ giá trị nào ở vị trí id
            $pattern = str_replace('{id}', '[0-9]+', $except_path); // {id} là số
            if (preg_match("#^{$pattern}$#", $path)) {
                return true;

            }
        }
        return false;
    }

    protected function getRequestData($request)
    {
        $method = strtoupper($request->getMethod());
        $data = [];

        if ($method === 'POST') {
            $data = (array) $request->getJSON();
        } elseif ($method === 'GET') {
            $data = $request->getGet();
        }

        return [
            'channel_partner_id' => $data['channel_partner_id'] ?? 0,
            'channel_partner_officer_id' => $data['channel_partner_officer_id'] ?? 0,
            'company_id' => $data['company_id'] ?? 0,
        ];
    }
    /**
     * Tạo phản hồi với mã trạng thái và thông báo
     *
     * @param int $code
     * @param string $message
     * @param mixed $data
     * @return ResponseInterface
     */
    protected function responseResult($code, $message, $data = null): ResponseInterface
    {
        $response = Services::response();
        $data_res = [
            'code' => $code,
            'message' => $message,
        ];

        if ($data !== null) {
            $data_res['data'] = $data;
        }

        $response->setStatusCode($code);
        $response->setJSON($data_res);

        return $response;
    }

    
    /**
     * Allows After filters to inspect and modify the response
     * object as needed. This method does not allow any way
     * to stop execution of other after filters, short of
     * throwing an Exception or Error.
     *
     * @param RequestInterface  $request
     * @param ResponseInterface $response
     * @param array|null        $arguments
     *
     * @return mixed
     */

     public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
     {
         
        // Lấy thông tin từ request
        $path = $request->getUri()->getPath();
        $method = strtoupper($request->getMethod());
        // lấy bọc lọc dữ liệu đầu vào
        $data_input = $this->getRequestData($request);
        $company_id = $data_input['company_id'];
        $channel_partner_id = $data_input['channel_partner_id'];
        $channel_partner_officer_id = $data_input['channel_partner_officer_id'];

        // Loại trừ company không thuộc hệ thống channel partner officer
         if(!empty($company_id)){
            $result = $this->checkExitDataCompany($company_id);
            
            if(empty($result)){
                return $this->responseResult(404, "Không tìm thấy đường dẫn");
            }
        }
    
         // Loại trừ api đặc biệt
         if($path == "internalapi/v1/user/check_user"){
             return true;
         }
        // API động bắt buộc có channel_partner_id và channel_partner_officer_id áp dụng cho GET
         $list_path_allowed_dynamic = [
             'internalapi/v1/cpawithdraw/detail/{id}',
             'internalapi/v1/cpacommission/detail/{id}',
             'internalapi/v1/company/detail/{id}',
             'internalapi/v1/user/detail/{id}',
         ];
        
        if (strtoupper($method)=="GET" && !empty($this->exceptUriDynamic($path, $list_path_allowed_dynamic))) {
           if (!$this->hasValidDataPartner($channel_partner_id,$channel_partner_officer_id)) {
               return $this->responseResult(404, "Không tìm thấy đường dẫn");
           }
           // reponse 
            return true;
        }
 
             
        // Lọc giá trị trả về cho tất cả API GET còn lại
        if (strtoupper($method) === 'GET') {
            $body = json_decode($response->getBody(), true);
        
            if (!empty($body['data'])) {
                // Danh sách các điều kiện lọc
                $filters = [
                    'channel_partner_officer_id' => $channel_partner_officer_id ?? null,
                    'channel_partner_id' => $channel_partner_id ?? null,
                    'company_id' => $company_id ?? null,
                ];
        
                // Thực hiện lọc dữ liệu
                $body['data'] = array_filter($body['data'], function ($item) use ($filters) {
                    foreach ($filters as $key => $value) {
                        if (!empty($value) && isset($item[$key]) && $item[$key] != $value) {
                            return false; // Nếu có điều kiện không khớp, loại bỏ item
                        }
                    }
                    return true; // Giữ lại item nếu tất cả điều kiện khớp
                });
        
                // Reset lại chỉ số của mảng sau khi lọc
                $body['data'] = array_values($body['data']);
            }
        
            // Cập nhật lại body của response
            $response->setBody(json_encode($body));
        }
        

     }
 
 }
 
