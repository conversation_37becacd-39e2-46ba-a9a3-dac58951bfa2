<?php

namespace App\Traits\Models;

use CodeIgniter\HTTP\RequestInterface;

trait HasDataTables
{
    protected function modifyDatatablesQuery(RequestInterface $request): void
    {
        // Override this method to modify the query
    }

    protected function _getDatatablesQuery(RequestInterface $request): void
    {
        $searchableColumns = $this->datatableSearchableColumns ?? [];
        $orderableColumns = $this->datatableOrderableColumns ?? [];
        $filterableColumns = $this->datatableFilterableColumns ?? [];

        $this->modifyDatatablesQuery($request);

        if (! empty($searchableColumns)) {
            if ($request->getVar('search') && ! empty($request->getVar('search')['value'])) {
                $searchTerm = trim($request->getVar('search')['value']);
                $this->groupStart();

                foreach ($searchableColumns as $i => $item) {
                    if ($i === 0) {
                        $this->like($item, $searchTerm);
                    } else {
                        $this->orLike($item, $searchTerm);
                    }
                }

                $this->groupEnd();
            }

            foreach ($searchableColumns as $key => $item) {
                if ($item !== null && isset($request->getPost('columns')[$key]['search']['value']) && $request->getPost('columns')[$key]['search']['value'] !== '') {
                    $this->where($item, $request->getPost('columns')[$key]['search']['value']);
                }
            }
        }

        foreach ($filterableColumns as $key => $column) {
            $value = $request->getPost($key);

            if ($value !== null && $value !== '') {
                $this->where($column, $value);
            }
        }

        if (
            $request->getVar('order')
            && isset($request->getVar('order')[0]['column'])
            && isset($orderableColumns[$request->getVar('order')[0]['column']])
        ) {
            $this->orderBy($orderableColumns[$request->getVar('order')[0]['column']], $request->getVar('order')[0]['dir']);
        } else {
            $this->orderBy('id', 'DESC');
        }
    }

    public function getDatatables(RequestInterface $request): array
    {
        $this->_getDatatablesQuery($request);

        if ($request->getVar('length') !== null && $request->getVar('length') !== -1) {
            $this->limit($request->getVar('length'), $request->getVar('start'));
        }

        return $this->get()->getResult();
    }

    public function countFiltered(RequestInterface $request): int
    {
        $this->_getDatatablesQuery($request);

        return $this->countAllResults();
    }

    public function countAll(): int
    {
        return $this->countAllResults();
    }
}
