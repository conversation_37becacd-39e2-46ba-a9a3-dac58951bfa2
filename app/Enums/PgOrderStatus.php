<?php

namespace App\Enums;

class PgOrderStatus
{
    public const AUTHENTICATED = 'AUTHENTICATED';

    public const AUTHENTICATION_INITIATED = 'AUTHENTICATION_INITIATED';

    public const AUTHENTICATION_NOT_NEEDED = 'AUTHENTICATION_NOT_NEEDED';

    public const AUTHENTICATION_UNSUCCESSFUL = 'AUTHENTICATION_UNSUCCESSFUL';

    public const AUTHORIZED = 'AUTHORIZED';

    public const CANCELLED = 'CANCELLED';

    public const CAPTURED = 'CAPTURED';

    public const CHARGEBACK_PROCESSED = 'CHARGEBACK_PROCESSED';

    public const DISBURSED = 'DISBURSED';

    public const DISPUTED = 'DISPUTED';

    public const EXCESSIVELY_REFUNDED = 'EXCESSIVELY_REFUNDED';

    public const FAILED = 'FAILED';

    public const FUNDING = 'FUNDING';

    public const INITIATED = 'INITIATED';

    public const PARTIALLY_CAPTURED = 'PARTIALLY_CAPTURED';

    public const PARTIALLY_REFUNDED = 'PARTIALLY_REFUNDED';

    public const REFUNDED = 'REFUNDED';

    public const REFUND_REQUESTED = 'REFUND_REQUESTED';

    public const VERIFIED = 'VERIFIED';


    public static function getLabels()
    {
        return [
            self::AUTHENTICATED => 'Đã xác thực',
            self::AUTHENTICATION_INITIATED => 'Bắt đầu xác thực',
            self::AUTHENTICATION_NOT_NEEDED => 'Không cần xác thực',
            self::AUTHENTICATION_UNSUCCESSFUL => 'Xác thực thất bại',
            self::AUTHORIZED => 'Đã ủy quyền',
            self::CANCELLED => 'Đã hủy',
            self::CAPTURED => 'Đã thu tiền',
            self::CHARGEBACK_PROCESSED => 'Đã xử lý hoàn trả',
            self::DISBURSED => 'Đã giải ngân',
            self::DISPUTED => 'Đang tranh chấp',
            self::EXCESSIVELY_REFUNDED => 'Hoàn tiền quá mức',
            self::FAILED => 'Thất bại',
            self::FUNDING => 'Đang tài trợ',
            self::INITIATED => 'Đã khởi tạo',
            self::PARTIALLY_CAPTURED => 'Thu tiền một phần',
            self::PARTIALLY_REFUNDED => 'Hoàn tiền một phần',
            self::REFUNDED => 'Đã hoàn tiền',
            self::REFUND_REQUESTED => 'Yêu cầu hoàn tiền',
            self::VERIFIED => 'Đã xác minh',
        ];
    }

    public static function toHtml($status)
    {
        switch ($status) {
            case self::AUTHENTICATED:
                $color = 'success';
                break;
            case self::AUTHENTICATION_INITIATED:
                $color = 'warning';
                break;
            case self::AUTHENTICATION_NOT_NEEDED:
                $color = 'info';
                break;
            case self::AUTHENTICATION_UNSUCCESSFUL:
                $color = 'danger';
                break;
            case self::AUTHORIZED:
                $color = 'primary';
                break;
            case self::CANCELLED:
                $color = 'secondary';
                break;
            case self::CAPTURED:
                $color = 'success';
                break;
            case self::CHARGEBACK_PROCESSED:
                $color = 'warning';
                break;
            case self::DISBURSED:
                $color = 'success';
                break;
            case self::DISPUTED:
                $color = 'warning';
                break;
            case self::EXCESSIVELY_REFUNDED:
                $color = 'danger';
                break;
            case self::FAILED:
                $color = 'danger';
                break;
            case self::FUNDING:
                $color = 'info';
                break;
            case self::INITIATED:
                $color = 'primary';
                break;
            case self::PARTIALLY_CAPTURED:
                $color = 'warning';
                break;
            case self::PARTIALLY_REFUNDED:
                $color = 'warning';
                break;
            case self::REFUNDED:
                $color = 'info';
                break;
            case self::REFUND_REQUESTED:
                $color = 'warning';
                break;
            case self::VERIFIED:
                $color = 'success';
                break;
            default:
                $color = 'secondary';
                break;
        }

        return sprintf('<span class="badge bg-%s">%s</span>', $color, self::getLabel($status));
    }

    public static function getLabel($status)
    {
        return self::getLabels()[$status] ?? $status;
    }
}
