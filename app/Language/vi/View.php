<?php

/**
 * This file is part of CodeIgniter 4 framework.
 *
 * (c) CodeIgniter Foundation <<EMAIL>>
 *
 * For the full copyright and license information, please view
 * the LICENSE file that was distributed with this source code.
 */

// View language settings
return [
    'invalidCellMethod'     => '{class}::{method} không phải là một method hợp lệ.',
    'missingCellParameters' => '{class}::{method} không có thông số.',
    'invalidCellParameter'  => '{0} không phải là một tên param hợp lệ.',
    'noCellClass'           => 'Không có loại ô xem được cung cấp.',
    'invalidCellClass'      => 'Không thể định vị lớp ô xem: {0}.',
    'tagSyntaxError'        => 'Bạn có một lỗi cú pháp trong các thẻ Parser của bạn: {0}',
];
