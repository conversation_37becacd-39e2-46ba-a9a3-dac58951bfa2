<?php

/**
 * This file is part of CodeIgniter 4 framework.
 *
 * (c) CodeIgniter Foundation <<EMAIL>>
 *
 * For the full copyright and license information, please view
 * the LICENSE file that was distributed with this source code.
 */

// Cast language settings
return [
    'jsonErrorDepth'    => 'Vượt quá độ sâu xếp chồng tối đa.',
    'jsonErrorCtrlChar' => 'Tìm thấy ký tự kiểm soát không mong muốn',
    'jsonErrorSyntax'   => 'Lỗi cú pháp, lỗi định dạng JSON.',
    'jsonErrorUtf8'     => 'Các ký tự UTF-8 không đúng định dạng, có thể được mã hóa không chính xác',
    'jsonErrorUnknown'  => 'Không thể nhận biết lỗi',
];
