<?php

/**
 * This file is part of CodeIgniter 4 framework.
 *
 * (c) CodeIgniter Foundation <<EMAIL>>
 *
 * For the full copyright and license information, please view
 * the LICENSE file that was distributed with this source code.
 */

// Database language settings
return [
    'invalidEvent'                     => '{0} không phải là một phản hồi Model Event hợp lệ.',
    'invalidArgument'                  => 'Bạn phải cung cấp {0} hợp lệ.',
    'invalidAllowedFields'             => 'Các trường được phép phải được chỉ định cho mô hình: {0}',
    'emptyDataset'                     => 'Không có dữ liệu cho {0}.',
    'failGetFieldData'                 => 'Không thể lấy dữ liệu thực địa từ cơ sở dữ liệu.',
    'failGetIndexData'                 => 'Không thể lấy dữ liệu chỉ mục từ cơ sở dữ liệu.',
    'failGetForeignKeyData'            => 'Không thể lấy dữ liệu khóa ngoại từ cơ sở dữ liệu.',
    'parseStringFail'                  => 'Phân tích chuỗi khóa thất bại.',
    'featureUnavailable'               => 'Tính năng này không có sẵn cho cơ sở dữ liệu bạn đang sử dụng.',
    'tableNotFound'                    => 'Bảng `{0}` không được tìm thấy trong cơ sở dữ liệu hiện tại.',
    'noPrimaryKey'                     => '`{0}` class model không chỉ định Khóa chính.',
    'noDateFormat'                     => '`{0}` class model không có dateFormat hợp lệ.',
    'fieldNotExists'                   => 'Trường `{0}` không tìm thấy.',
    'forEmptyInputGiven'               => 'Câu lệnh rỗng được đưa ra cho trường `{0}`',
    'forFindColumnHaveMultipleColumns' => 'Chỉ một cột được cho phép trong tên cột.',
];
