<?php
namespace App\Actions\GoogleSheets;

use App\Libraries\GoogleSheetsClient;
use App\Models\WebhooksLogModel;
use App\Models\TransactionsModel;
use App\Models\CompanyModel;
use App\Models\CounterModel;
use App\Models\BankAccountModel;
use App\Models\BankGoogleSheetModel;
use App\Models\BankSubAccountModel;
use App\Models\GoogleSheetLogModel;
use App\Models\GoogleSheetQueueModel;

class ExecuteGoogleSheetsWebhooksAction
{

    public static function run($transactionId)
    {
        $transactionDetails = model(TransactionsModel::class)->where(['id' => $transactionId])->first();

        if (!$transactionDetails || $transactionDetails->parser_status != 'Success' || ($transactionDetails->amount_in <= 0 && $transactionDetails->amount_out <= 0)) return false;

        $bankAccountDetails = model(BankAccountModel::class)->where(['id' => $transactionDetails->bank_account_id, 'active' => 1])->first();
    
        if (!$bankAccountDetails || !is_numeric($bankAccountDetails->company_id)) return false;

        $companyDetails = model(CompanyModel::class)->where(['id' => $bankAccountDetails->company_id, 'active' => 1]);

        if (!$companyDetails) return false;

        $resultBankSubAccount = model(BankSubAccountModel::class)->where([
            'bank_account_id' => $bankAccountDetails->id,
            'sub_account' => $transactionDetails->sub_account,
            'active' => 1
        ])->first();

        $id_sub_account = '';
        if($resultBankSubAccount){
            $id_sub_account = $resultBankSubAccount->id;
        }

        $results = [];

        $googleSheetsAll = model(BankGoogleSheetModel::class)
        ->select('tb_autopay_bank_google_sheet.*')
        ->where([
            'tb_autopay_bank_google_sheet.company_id' => $bankAccountDetails->company_id,
            'tb_autopay_bank_google_sheet.bank_account_id' => null,
            'tb_autopay_bank_google_sheet.bank_sub_account_id' => null,
            ])
        ->get()->getResult();

        if(!empty($googleSheetsAll)){
            foreach ($googleSheetsAll as $googlesheet) {
                $results[$googlesheet->id] = static::isTransactionValidForType($googlesheet, $transactionDetails);
            }
        }

        $googleSheetsBankAll = model(BankGoogleSheetModel::class)
        ->select('tb_autopay_bank_google_sheet.*')
        ->where([
            'tb_autopay_bank_google_sheet.company_id' => $bankAccountDetails->company_id,
            'tb_autopay_bank_google_sheet.bank_account_id IS NOT NULL' => null,
            'tb_autopay_bank_google_sheet.bank_sub_account_id' => null,
        ])
        ->get()->getResult();

        if(!empty($googleSheetsBankAll)){
            foreach ($googleSheetsBankAll as $googlesheet) {
                if($googlesheet->bank_account_id == $bankAccountDetails->id){
                    $results[$googlesheet->id] = static::isTransactionValidForType($googlesheet, $transactionDetails);
                }
            }
        }

        $googleSheetsBankVa = model(BankGoogleSheetModel::class)
        ->select('tb_autopay_bank_google_sheet.*')
        ->where([
            'tb_autopay_bank_google_sheet.company_id' => $bankAccountDetails->company_id,
            'tb_autopay_bank_google_sheet.bank_account_id IS NOT NULL' => null,
            'tb_autopay_bank_google_sheet.bank_sub_account_id IS NOT NULL' => null,
        ])
        ->get()->getResult();

        if(!empty($googleSheetsBankVa)){
            foreach ($googleSheetsBankVa as $googlesheet) {
                
                $bank_account_id = $googlesheet->bank_account_id;
                $bank_sub_account_id = $googlesheet->bank_sub_account_id;
                $google_account_id = $googlesheet->google_account_id;
                $company_id = $googlesheet->company_id;
                $file_id = $googlesheet->file_id;
                $sheet_id = $googlesheet->sheet_id;

                $checkAll = model(BankGoogleSheetModel::class)->where([
                    'google_account_id' => $google_account_id,
                    'company_id' => $company_id,
                    'file_id' => $file_id,
                    'sheet_id' => $sheet_id,
                    'bank_account_id' => $bankAccountDetails->id,
                    'bank_sub_account_id' => null
                ])->first();

                if($checkAll){
                    continue;
                }else{
                    if($bank_account_id == $bankAccountDetails->id && $bank_sub_account_id == $id_sub_account){
                        $results[$googlesheet->id] = static::isTransactionValidForType($googlesheet, $transactionDetails);
                    }
                }
            }
        }

        return $results;
    }


    protected static function isTransactionValidForType($googlesheet, $transactionDetails)
    {

        $transferType = $googlesheet->transfer_type;
        $results = [];

        if ($transferType == 'in' && $transactionDetails->amount_in > 0) {
            $results[] = static::handleGoogleSheetsWebhook($googlesheet, $transactionDetails);
        }

        if ($transferType == 'out' && $transactionDetails->amount_out > 0) {
            $results[] = static::handleGoogleSheetsWebhook($googlesheet, $transactionDetails);
        }

        if ($transferType == 'all') {
            $results[] = static::handleGoogleSheetsWebhook($googlesheet, $transactionDetails);
        }

        return $results;
    }
    
    protected static function handleGoogleSheetsWebhook($googlesheet, $transactionDetails)
    {
        $client = new GoogleSheetsClient();
        
        $googleAccountId = $googlesheet->google_account_id;
        $fileId = $googlesheet->file_id;
        $sheetId = $googlesheet->sheet_id;
        $position = $googlesheet->position;
        $hide_accumulated = $googlesheet->hide_accumulated;

        $sheetNameWebhookLog = static::initializeWebhookLog();
        $batchUpdateWebhookLog = static::initializeWebhookLog();
        $appendWebhookLog = static::initializeWebhookLog();

        $values = static::buildTransactionValues($transactionDetails, $hide_accumulated);
        $range = 'A2';
    
        try {
            $sheetNameResponse = $client->getSheetNameById($googleAccountId, "https://sheets.googleapis.com/v4/spreadsheets/{$fileId}");

            static::logSheetResponse($sheetNameWebhookLog, $sheetNameResponse, $googlesheet, $transactionDetails->id);

            if (isset($sheetNameResponse['error'])) {
                static::handleExceptionQueue($googlesheet, $transactionDetails->id, $range, $values, $sheetNameResponse['error']);
            }
        } catch (\Exception $e) {
            static::handleExceptionQueue($googlesheet, $transactionDetails->id, $range, $values, $e->getMessage());
            return static::handleException($googlesheet, $transactionDetails->id, $sheetNameWebhookLog, $e);
        }
    
        if (!$sheetNameWebhookLog['success']) {
            return false;
        }

        $sheets = json_decode($sheetNameResponse['response']->getBody(), true)['sheets'] ?? [];
        $sheetTitle = static::getSheetTitle($sheets, $sheetId);

        if ($sheetTitle === null) {
            log_message('error', 'Sheet title not found');
            return false;
        }

        if ($position === 'start') {
            if (!static::insertRowAtStart($client, $googleAccountId, $fileId, $sheetId, $googlesheet, $transactionDetails->id, $batchUpdateWebhookLog, $sheetTitle, $range, $values)) {
                log_message('error', 'Insert at start failed');
                return false;
            }
        } else {
            if (!static::appendToSheet($client, $googleAccountId, $fileId, $sheetTitle, $range, $values, $googlesheet, $transactionDetails->id, $appendWebhookLog)) {
                log_message('error', 'Append to sheet failed');
                return false;
            }
        }

        return [
            'sheet_name_log' => $sheetNameWebhookLog,
            'batch_update_sheet_log' => $batchUpdateWebhookLog,
            'append_sheet_log' => $appendWebhookLog
        ];
    }

    protected static function initializeWebhookLog()
    {
        return [
            'success' => false,
            'connect_success' => 0,
            'error_message' => '',
        ];
    }

    protected static function logSheetResponse(&$log, $response, $googlesheet, $transactionId)
    {
        $log['connect_success'] = isset($response['response']) && $response['response']->getStatusCode() === 200 ? 1 : 0;
        $log['request_method'] = $response['request']['method'] ?? null;
        $log['request_url'] = $response['request']['url'] ?? null;
        $log['request_header'] = $response['request']['headers'] ?? null;
        $log['request_body'] = $response['request']['json'] ?? null;
        
        if (isset($response['response'])) {
            $log['status_code'] = $response['response']->getStatusCode();
            $log['response_body'] = json_encode((string) $response['response']->getBody());
            $log['response_status_code'] = $log['status_code'];
            $log['success'] = in_array($log['response_status_code'], [200, 201]);
        } else {
            $log['status_code'] = null;
            $log['response_body'] = json_encode($response['error'] ?? 'Unknown error');
            $log['response_status_code'] = null;
            $log['success'] = false;
        }

        static::logGoogleSheetsWebhook($googlesheet, $transactionId, $log);
    }


    protected static function handleException($googlesheet, $transactionId, &$log, \Exception $e)
    {
        $log['error_message'] = $e->getMessage();
        $log['connect_success'] = 0;
        static::logGoogleSheetsWebhook($googlesheet, $transactionId, $log);
    }

    protected static function buildTransactionValues($transactionDetails, $hide_accumulated)
    {
        return [
            $transactionDetails->gateway,
            $transactionDetails->transaction_date,
            $transactionDetails->account_number,
            $transactionDetails->sub_account,
            $transactionDetails->code,
            $transactionDetails->transaction_content,
            $transactionDetails->amount_in > 0 ? 'Tiền vào' : 'Tiền ra',
            $transactionDetails->amount_in > 0 ? (int) $transactionDetails->amount_in : (int)$transactionDetails->amount_out,
            $transactionDetails->reference_number,
            $hide_accumulated == 0 ? (int) $transactionDetails->accumulated : 0,
        ];
    }

    protected static function insertRowAtStart(
        $client, $googleAccountId, $fileId, $sheetId, $googlesheet,
        $transactionId, &$log, $sheetTitle, $range, $values
    ) {

        $sheetTitleEncoded = urlencode($sheetTitle);

        try {
            $response = $client->getSheetData($googleAccountId, "https://sheets.googleapis.com/v4/spreadsheets/{$fileId}/values/{$sheetTitleEncoded}!A2");

            $existingData =  json_decode($response['response']->getBody(), true)['values'] ?? [];
            static::logSheetResponse($log, $response, $googlesheet, $transactionId);
        } catch (\Exception $e) {
            return static::handleException($googlesheet, $transactionId, $log, $e);
        }

        if (!$log['success']) {
            log_message('error', 'Failed to get sheet data');
            return false;
        }

        if (!empty($existingData)) {
            $requests = [
                [
                    'insertDimension' => [
                        'range' => [
                            'sheetId' => $sheetId,
                            'dimension' => 'ROWS',
                            'startIndex' => 1,
                            'endIndex' => 2
                        ],
                        'inheritFromBefore' => false
                    ]
                ]
            ];
    
            try {
                $response = $client->batchUpdateToSheet(
                    $googleAccountId,
                    "https://sheets.googleapis.com/v4/spreadsheets/{$fileId}:batchUpdate",
                    $requests
                );
                static::logSheetResponse($log, $response, $googlesheet, $transactionId);
            } catch (\Exception $e) {
                return static::handleException($googlesheet, $transactionId, $log, $e);
            }
    
            if (!$log['success']) {
                return false;
            }
        }
    
        try {
            $response = $client->updateSheet($googleAccountId, $fileId, $sheetTitle, $range, $values);
            static::logSheetResponse($log, $response, $googlesheet, $transactionId);
        } catch (\Exception $e) {
            return static::handleException($googlesheet, $transactionId, $log, $e);
        }
    
        return $log['success'];
    }    

    protected static function appendToSheet($client, $googleAccountId, $fileId, $sheetTitle, $range, $values, $googlesheet, $transactionId, &$log)
    {
        try {
            $response = $client->appendToSheet($googleAccountId, $fileId, $sheetTitle, $range, $values);
            static::logSheetResponse($log, $response, $googlesheet, $transactionId);
        } catch (\Exception $e) {
            static::handleExceptionQueue($googlesheet, $transactionId, $range, $values, $e->getMessage());
            return static::handleException($googlesheet, $transactionId, $log, $e);
        }

        return $log['success'];
    }

    protected static function logGoogleSheetsWebhook($googlesheet, $transactionId, $data)
    {

        model(GoogleSheetLogModel::class)->insert(array_merge($data, [
            'company_id' => $googlesheet->company_id,
            'bank_google_sheet_id' => $googlesheet->id,
            'transaction_id' => $transactionId,
            'type' => 'First',
            'request_method' => $data['request_method'] ?? null,
            'request_url' => null,
            'connect_success' => $data['connect_success'] ?? 0,
            'request_header' => null,
            'request_body' => null,
            'response_status_code' => $data['response_status_code'] ?? null,
            'response_body' => null,
        ]));
    }

    protected static function handleExceptionQueue($googlesheet, $transactionId, $range, $value, $error_message){
        model(GoogleSheetQueueModel::class)->insert([
            'company_id' => $googlesheet->company_id,
            'bank_google_sheet_id' => $googlesheet->id,
            'transaction_id' => $transactionId, 
            'google_account_id' => $googlesheet->google_account_id,
            'file_id' => $googlesheet->file_id,
            'sheet_id' => $googlesheet->sheet_id,
            'range' => $range,
            'value' => json_encode($value),
            'position' => $googlesheet->position,
            'status' => 'SoftFailed',
            'last_log' => $error_message,
        ]);
    }

    protected static function getSheetTitle($sheets, $sheetId)
    {
        if (!is_array($sheets)) return null;
        foreach ($sheets as $sheet) {
            if ($sheet['properties']['sheetId'] == $sheetId) {
                return $sheet['properties']['title'];
            }
        }
        return null;
    }
}