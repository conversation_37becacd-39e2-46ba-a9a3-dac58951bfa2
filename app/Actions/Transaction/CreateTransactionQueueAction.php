<?php

namespace App\Actions\Transaction;

use Exception;
use App\Models\SmsParserModel;
use PhpAmqpLib\Message\AMQPMessage;

class CreateTransactionQueueAction
{
    public static function run($transactionDetails, $bankAccountDetails)
    {
        if (!$transactionDetails->transaction_id) throw new Exception("Missing transaction_id in payload.");

        $rabbitmq = new \App\Libraries\RabbitMQClient;
        $rabbitmq->connect();
        $queued = false;

        $msg = new AMQPMessage(
            json_encode([
                'transaction_details' => $transactionDetails,
                'bank_account_details' => $bankAccountDetails,
            ]),
            array('delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT)
        );

        try {

            $rabbitmq->queueDeclare('transaction_insertion');
            $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('transaction_insertion', 'name'));

            $queued = true;
        } catch (Exception $e) {
            log_message('error', 'Transaction insertion queue failed: ' . $e->getMessage());
        }

        $rabbitmq->close();
        
        return $queued ? $transactionDetails->transaction_id : null;
    }
}