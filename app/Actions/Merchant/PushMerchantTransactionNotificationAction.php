<?php

namespace App\Actions\Merchant;

use App\Models\CounterModel;
use App\Models\MerchantModel;
use App\Models\WebhooksModel;
use App\Models\BankAccountModel;
use App\Models\TransactionsModel;
use PhpAmqpLib\Message\AMQPMessage;
use App\Builders\Company\CompanyBuilder;
use App\Models\BankAccountCashflowModel;
use App\Actions\Company\GetCompanyConfigurationAction;
use App\Actions\Company\SetCompanyConfigurationAction;

class PushMerchantTransactionNotificationAction
{
    public static function run($bankAccount, $transaction, $enableWebhookOffload = true)
    {
        if (!$bankAccount->merchant_id) return null;

        $merchantConfig = config(\Config\Merchant::class);
        $merchantConfig->forceOptimize = $merchantConfig->forceOptimize ?? false;

        $merchant = model(MerchantModel::class)->where(['id' => $bankAccount->merchant_id, 'active' => 1])->get()->getRow();
        if (!$merchant) return null;

        $company = CompanyBuilder::make()->whereMerchantId($bankAccount->merchant_id)->whereId($bankAccount->company_id)->first();

        $transactionAmount = GetCompanyConfigurationAction::run($bankAccount->company_id, 'TransactionAmount');

        $pushable = $company->status == 'Active' 
            && $company->active 
            && ($transactionAmount->value > 0 || $transactionAmount->value === 'Unlimited')
            && ($merchant->trans_type == 'DC' 
                || ($merchant->trans_type == 'C' && $transaction->amount_in > 0) 
                || ($merchant->trans_type == 'D' && $transaction->amount_out > 0));
        
        if (!$pushable) return;

        $webhookQueuable = $enableWebhookOffload;
        $forceWebhookManually = ['webhook' => false];
        $rabbitmq = null;

        if ($webhookQueuable) {
            $rabbitmq = new \App\Libraries\RabbitMQClient;
            $webhookQueuable = $rabbitmq->connect();
        }

        if ($rabbitmq && $webhookQueuable) {
            $msg = new AMQPMessage(
                json_encode(['account_number' => $transaction->account_number, 'parser_id' => $transaction->id]),
                array('delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT)
            );

            // webhooks
            try {
                $rabbitmq->queueDeclare('webhook');
                $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('webhook', 'name'));
            } catch (\Exception $e) {
                log_message('error', 'Push Merchant Transaction Notification queue failed: ' . $e->getMessage());
                $forceWebhookManually['webhook'] = true;
            }

            $rabbitmq->close();
        } 
        
        if (!$webhookQueuable || $forceWebhookManually['webhook']) {
            // webhooks
            $webhooksModel = model(WebhooksModel::class);
            $webhooksModel->doWebhooks($transaction->account_number, $transaction->id);
        }

        //counter
        $counterModel = model(CounterModel::class);
        $counterModel->transaction($company->id, false, $transaction->amount_in, $transaction->amount_out, $bankAccount->merchant_id);

        // Decrement transaction amount
        if ($transactionAmount->value !== 'Unlimited' && $transaction->amount_in > 0) {
            SetCompanyConfigurationAction::run($bankAccount->company_id, 'TransactionAmount', $transactionAmount->value - 1);
        }

        if ($merchantConfig->forceOptimize) {
            $lastTransaction = $transaction;
        } else {
            $lastTransaction = model(TransactionsModel::class)->where([
                'account_number' => $transaction->account_number, 
                'bank_account_id' => $bankAccount->id
            ])->orderBy('transaction_date', 'desc')->orderBy('id', 'desc')->first();
        }

        if (is_object($lastTransaction)) {
            $bankAccountModel = model(BankAccountModel::class);
            $bankAccountModel->set([
                'last_transaction' => $lastTransaction->transaction_date, 
                'accumulated' => $lastTransaction->accumulated
            ])->where(['id' => $bankAccount->id])->update();
        }
    }
}