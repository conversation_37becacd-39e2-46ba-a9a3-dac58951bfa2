<?php

namespace App\Actions\Merchant;

use App\Models\MerchantApiRequestModel;

class CreateMerchantApiRequestAction
{
    public static function run($merchantId, $payload, $ttl = 600)
    {
        $merchantApiRequestModel = model(MerchantApiRequestModel::class);
        $requestId = sha1($merchantId . microtime() . uniqid());

        $merchantApiRequestModel->insert([
            'merchant_id' => $merchantId,
            'request_id' => $requestId,
            'payload' => $payload ? json_encode($payload) : null,
            'ttl' => $ttl,
            'expires_at' => date('Y-m-d H:i:s', strtotime("+$ttl seconds"))
        ]);

        return $requestId;
    }
}