<?php

namespace App\Actions;

use App\Actions\Company\GetCompanyConfigurationAction;
use App\Models\ConfigurationModel;

class PayCodeDetector
{
    public static function getCode(string $content, int $companyId): ?string
    {
        $model = model(ConfigurationModel::class);

        $paycodeEnabled = GetCompanyConfigurationAction::run($companyId, 'PayCode');

        if (! $paycodeEnabled || $paycodeEnabled->value !== 'on') {
            return null;
        }

        $paycode = GetCompanyConfigurationAction::run($companyId, 'PayCodeStructures');
        
        if (! $paycode || (is_object($paycode) && property_exists($paycode, 'value') && !$paycode->value)) {
            $regex = $model->getPayCodeRegex($companyId);

            if (! $regex) {
                return null;
            }

            if (! preg_match('/' . $regex . '/', $content, $matches, PREG_OFFSET_CAPTURE)) {
                return null;
            }

            if (! isset($matches[0][0])) {
                return null;
            }

            return trim($matches[0][0]);
        }

        $structures = json_decode($paycode->value, true);

        if (! is_array($structures)) {
            return null;
        }

        foreach ($structures as $structure) {
            if (! isset($structure['is_active']) || $structure['is_active'] === 'off') {
                continue;
            }

            $regex = static::buildRegexFromStructure(
                $structure['prefix'],
                $structure['suffix_from'],
                $structure['suffix_to'],
                $structure['character_type']
            );

            if (preg_match('/' . $regex . '/', $content, $matches, PREG_OFFSET_CAPTURE)) {
                if (isset($matches[0][0])) {
                    return trim($matches[0][0]);
                }
            }
        }

        return null;
    }

    public static function getPayCodeList(int $companyId): array
    {
        $data = [];

        $paycode = GetCompanyConfigurationAction::run($companyId, 'PayCodeStructures');

        if ($paycode) {
            $structures = json_decode($paycode->value, true);

            if (is_array($structures)) {
                foreach ($structures as $structure) {
                    if (! isset($structure['is_active']) || $structure['is_active'] === 'off') {
                        continue;
                    }
                    $data[] = $structure['prefix'];
                }
            }
        } else {
            $payCodePrefix = GetCompanyConfigurationAction::run($companyId, 'PayCodePrefix');
            $data[] = $payCodePrefix->value;
        }

        return $data;
    }

    public static function buildRegexFromStructure(string $prefix, int $suffixFrom, int $suffixTo, string $characterType): string
    {
        $prefix = preg_quote($prefix, '/');

        $charSet = $characterType === 'NumberOnly'
            ? '[0-9]'
            : '[A-Za-z0-9]';

        if ($suffixFrom !== $suffixTo) {
            $length = '{' . $suffixFrom . ',' . $suffixTo . '}';
        } else {
            $length = '{' . $suffixFrom . '}';
        }

        return '(' . $prefix . '|' . strtolower($prefix) . ')' . $charSet . $length;
    }
}
