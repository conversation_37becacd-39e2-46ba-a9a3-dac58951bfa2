<?php

namespace App\Actions\OutputDevice;

use App\Controllers\Outputdevice;
use App\Libraries\FlametechvnClient;
use PhpMqtt\Client\ConnectionSettings;
use PhpMqtt\Client\MqttClient;
use App\Models\BankAccountModel;
use App\Models\BankIntegrationOutputDeviceModel;
use App\Models\BankSubAccountModel;
use App\Models\OutputDeviceLogModel;
use App\Models\OutputDeviceModel;
use App\Models\OutputDeviceReplayMessageQueueModel;
use App\Models\SmsParserModel;
use App\Models\TransactionsModel;
use Config\MQTT;

class ExecuteOutputDevice
{
    public static function run($transactionId,$replay = 0)
    {
        helper('general');
        // Khởi tạo các model
        $OutputDeviceReplayMessageQueueModel = model(OutputDeviceReplayMessageQueueModel::class);
        $TransactionModel = slavable_model(TransactionsModel::class, 'ExecuteOutputDevice');
        $BankAccountModel = model(BankAccountModel::class);
        $BankSubAccountModel = model(BankSubAccountModel::class);
        $OutputDeviceModel = model(OutputDeviceModel::class);
        $BankIntegrationOutputDevice = model(BankIntegrationOutputDeviceModel::class);

        // Lấy dữ liệu giao dịch
        $transactionData = $TransactionModel->where(['id' => $transactionId])->get()->getRowArray();
        if (empty($transactionData)) {
            return "ID transaction not find!";
        }

        // Kiểm tra số tiền nhận vào
        if ($transactionData['amount_out'] > 0) {
            return "Amount out > 0!";
        }

        // Kiểm tra VA or Bank Account
        $is_VA = !empty($transactionData['account_number']) && !empty($transactionData['sub_account']);
        $is_BankAccount = !$is_VA; // Nếu không phải VA thì là tài khoản ngân hàng

        // Xử lý cho tài khoản ngân hàng và tài khoản VA
        $bankAccountId = null;
        $subAccountId = null;

        if ($is_BankAccount) {
            $bankAccountData = $BankAccountModel->where(['account_number' => $transactionData['account_number']])->get()->getRowArray();
            if (empty($bankAccountData)) {
                log_message("debug","Thông tin tài khoản ngân hàng không tồn tại!");
                return "Info Bank not exits!";
            }
            $bankAccountId = $bankAccountData['id'];
            $companyId = $bankAccountData['company_id'];
        } elseif ($is_VA) {
            $bankSubAccountData = $BankSubAccountModel->where(['sub_account' => $transactionData['sub_account']])->get()->getRowArray();
            if (empty($bankSubAccountData)) {
                log_message("debug","Thông tin tài khoản VA không tồn tại - ! :" .$transactionData['sub_account']);
                return "Info Bank VA not exits!";
            }
            $subAccountId = $bankSubAccountData['id'];
            $bankAccountId = $bankSubAccountData['bank_account_id'];
            $bankAccountData = $BankAccountModel->where(['id' => $bankAccountId])->get()->getRowArray();
            if (empty($bankAccountData)) {
                log_message("debug","Thông tin tài khoản ngân hàng của VA không tồn tại!");
                return "Info Bank not exits!";
            }
            $companyId = $bankAccountData['company_id'];
        }

        // Kiểm tra thông tin tích hợp thiết bị
        
        $bankIntegrationOutputDeviceData = $BankIntegrationOutputDevice
            ->where(['company_id' => $companyId, 'bank_account_id' => $bankAccountId, 'sub_account_id' => $subAccountId])
            ->get()
            ->getResultArray();

        if (empty($bankIntegrationOutputDeviceData)) {
            log_message("debug","Không tìm thấy thông tin tích hợp thiết bị loa (chưa gán tài khoản bank vô thiết bị hoặc xóa tích hợp trước đó)!");
            return "Not find data interation!";
        }

        // Join dữ liệu cần thiết từ các bảng liên quan
        $joinDataIntegration = $BankIntegrationOutputDevice->select("
            tb_autopay_bank_integration_output_device.id bank_integration_id,
            tb_autopay_bank_integration_output_device.output_device_id,
            tb_autopay_bank_integration_output_device.company_id,
            tb_autopay_bank_integration_output_device.bank_account_id,
            tb_autopay_bank_integration_output_device.sub_account_id,

            tb_autopay_output_device.mqtt_server_id,
            tb_autopay_output_device.imei,
            tb_autopay_output_device.serial_number,
            tb_autopay_output_device.online_status,
            tb_autopay_output_device.vendor,
            tb_autopay_output_device.max_amount,
            tb_autopay_output_device.min_amount,
            tb_autopay_output_device.required_content,
            tb_autopay_output_device.active,

            tb_autopay_mqtt_server.hostname,
            tb_autopay_mqtt_server.username username_server,
            tb_autopay_mqtt_server.password password_server,
            tb_autopay_mqtt_server.port,
            tb_autopay_mqtt_server.ssl_enabled
        ")
        ->where([
            'tb_autopay_bank_integration_output_device.company_id' => $companyId,
            'tb_autopay_bank_integration_output_device.bank_account_id' => $bankAccountId,
            'tb_autopay_bank_integration_output_device.sub_account_id' => $subAccountId,
            'tb_autopay_output_device.active' => 1,
        ])
        ->join('tb_autopay_output_device', 'tb_autopay_output_device.id = tb_autopay_bank_integration_output_device.output_device_id', 'left')
        ->join('tb_autopay_mqtt_server', 'tb_autopay_mqtt_server.id = tb_autopay_output_device.mqtt_server_id', 'left')
        ->get()
        ->getResultArray();

        if (empty($joinDataIntegration)) {
            log_message("error","Không thể join thông tin tích hợp của hệ thống, hãy kiểm tra lại!");
            return "Not find device interation!";
        }

        // Gửi thông tin tới thiết bị
        foreach ($joinDataIntegration as $device) {


            // Filter min amount
            if (!empty($device['min_amount'])) {
                if ($transactionData['amount_in'] < $device['min_amount']) {
                    log_message("debug","Filter min_amount (lớn hơn hoặc bằng) was applied (".$transactionData['amount_in']." < ". $device['min_amount'].")");
                    continue;
                }
            }

            // Filter max amount
            if (!empty($device['max_amount'])) {
                if ($transactionData['amount_in'] > $device['max_amount']) {
                    log_message("debug","Filter max_amount (bé hơn hoặc bằng) was applied (".$transactionData['amount_in']." > ". $device['max_amount'].")");
                    continue;
                }
            }

            // Filter required content
            if (!empty($device['required_content'])) {
                // Sử dụng strpos để kiểm tra xem nội dung giao dịch có chứa nội dung yêu cầu không
                if (strpos($transactionData['transaction_content'], $device['required_content']) === false) {
                    log_message("debug","Filter required_content was applied (".$device['required_content']. " !include " .$transactionData['transaction_content'].")");
                    continue;
                }
            }

            // data send message

            $queue_message = array(
                'output_device_id' => $device['output_device_id'],
                'company_id' => $companyId,
                'transaction_id' => $transactionId,
            );

            // log data
            $log_data = array(
                'output_device_id' => $device['output_device_id'] ?? "",
                'server_mqtt_id' => $device['mqtt_server_id'] ?? "",
                'company_id' => $companyId ?? "",
                'transaction_id' => $transactionId ?? "",
                'message' => (isset($device['vendor']) && $device['vendor'] == "Aisino") 
                ? ((isset($transactionData['amount_in']) ? (int)$transactionData['amount_in'] : 0) . " - " . ExecuteOutputDevice::amountToAudio((int)($transactionData['amount_in'] ?? 0))) 
                : (int)($transactionData['amount_in'] ?? 0),
            );


            // xử lý thiết bị vendor Aisino và Flametechvn
            if($device['vendor'] == "Flametechvn"){

                $data_device_flametechvn = $OutputDeviceModel->where(['id' => $device['output_device_id'],'vendor'=>'Flametechvn'])->get()->getRowArray();
                if(!empty($data_device_flametechvn)){

                    $FlametechvnClient = new FlametechvnClient;

                    // online
                    $result_push = $FlametechvnClient->pushNoti($data_device_flametechvn['external_device_token'], (int)$transactionData['amount_in'],$transactionId);
                    if(!empty($result_push['status']) && $result_push['code'] == 200){
                        $queue_message['status'] =  "Success";
                        $log_data['status'] = $queue_message['status'];
                        log_message("debug", $queue_message['status'] == "Success" ?
                            "Gửi thông tin tới thiết bị thành công, ID thiết bị " . json_encode($device['output_device_id']) :
                            "Gửi thông tin tới thiết bị thất bại, ID thiết bị " . json_encode($device['output_device_id']));                           
                    }else if(empty($result['status']) && $result_push['code'] == 400){
                        // offline
                        $queue_message['status'] = "Pending";
                        $log_data['status'] = "Pending";
                        log_message("debug", "Thiết bị đang offline, ID thiết bị " . json_encode($device['output_device_id']));
                    }else{
                        // error
                        $queue_message['status'] = "Failed";
                        $log_data['status'] = "Failed";
                        log_message("debug", "Thiết bị đang lỗi không xác định, ID thiết bị " . json_encode($device['output_device_id']));
                    }


                }else{
                    log_message("error","Not find device ". json_encode($data_device_flametechvn));
                }

            }elseif ($device['online_status'] == "on" && $device['vendor'] == "Aisino") {

                // phát lại giao dịch nhỡ
                $message_time = null;
                if($replay == 1){
                    $day_missed = $transactionData['transaction_date'];
                     // Chuẩn bị thông điệp để gửi qua MQTT
                    log_message("debug","ngày nhận giao dịch :".$day_missed);
                    $dateTime = new \DateTime($day_missed); 
                    $message_time = ExecuteOutputDevice::timeToAudio($dateTime);
                }    

                $result_push = ExecuteOutputDevice::sendMessageToMqtt($device, (int)$transactionData['amount_in'],$message_time);
                $queue_message['status'] = $result_push ? "Success" : "Failed";
                $log_data['status'] = $queue_message['status'];
                log_message("debug", $queue_message['status'] == "Success" ?
                    "Gửi thông tin tới thiết bị thành công, ID thiết bị " . json_encode($device['output_device_id']) :
                    "Gửi thông tin tới thiết bị thất bại, ID thiết bị " . json_encode($device['output_device_id']));

              
            } else {
                $queue_message['status'] = "Pending";
                $log_data['status'] = "Pending";
                log_message("debug", "Thiết bị đang offline, ID thiết bị " . json_encode($device['output_device_id']));
            }

            $result_queue = $OutputDeviceReplayMessageQueueModel->insertIfNotExist(['transaction_id' => $transactionId,'output_device_id '=> $device['output_device_id'],'company_id' => $companyId], $queue_message);
            log_message($result_queue ? "debug" : "error", "Thêm queue message device " . ($result_queue ? "thành công" : "thất bại") . ", ID thiết bị " . json_encode($device['output_device_id']));

            $savelog = self::saveLog($log_data);
            log_message($savelog ? "debug" : "error", $savelog ? "Thêm log thành công!" : "Lỗi thêm log message thiết bị, dữ liệu log :" . json_encode($log_data));
        }

        return "Handle case success!";
    }



    protected static function sendMessageToMqtt($data, $amount, $time = 0) {
        // Chuyển đổi số tiền thành file âm thanh
        $audioFile = ExecuteOutputDevice::amountToAudio($amount);
    
        // Kiểm tra xem amount có lớn hơn 99999999 (99tr) không
        $number_type = "0";
        if (strlen($amount) > 7) {
            $number_type = "0";
            // Cắt 3 chữ số cuối nếu amount lớn hơn 99999999
            $amount = substr($amount, 0, strlen($amount) - 3);
        }
        if (strlen($amount) > 10) {
            // Lấy 7 ký tự đầu tiên
            $amount = substr($amount, 0, 7);
    
            // Thêm dấu '.' trước 2 ký tự cuối cùng
            $amount = substr($amount, 0, -2) . '.' . substr($amount, -2);
    
            // Xác định kiểu số
            $number_type = "1";
        }
    
        // Tin nhắn sẽ gửi đi
        $message = json_encode([
            'type' => 'MP',
            'file' => $audioFile,
            'amount' => $amount,
            'number_type' => $number_type,
        ]);
    
        // Phát lại thời gian giao dịch
        if (!empty($time)) {
            $message = json_encode([
                'type' => 'MP',
                'file' => $audioFile,
                'date' => $time,
                'amount' => $amount,
                'number_type' => $number_type,
            ]);
        }
    
       
    
        
            
        $username = $data['username_server'];
        $password = $data['password_server'];
        // Cấu hình thông tin kết nối
        $hostname = $data['hostname'];
        $port = $data['port'];
        $clientId = 'topic_' . $data['serial_number'];
        $topic = 'topic_' . $data['serial_number'];    
        // Tạo client MQTT và cài đặt
        try {
            $client = new MqttClient($hostname, $port, $clientId);    
            $connectSettings = (new ConnectionSettings())
                ->setUsername($username)
                ->setPassword($password)
                ->setKeepAliveInterval(60)
                ->setUseTls($data['ssl_enabled']);  // Dùng TLS nếu cần thiết    
            // Kết nối tới MQTT broker
            $client->connect($connectSettings);    
            // Gửi tin nhắn
            $client->publish(
                $topic,
                $message,
                MqttClient::QOS_AT_MOST_ONCE,
                false
            );    
            // Ngắt kết nối
            $client->disconnect();    
            // Ghi lại log
            log_message("debug", "Nội dung tin nhắn đã gửi: " . $message);
            log_message("debug", "Topic đã gửi: " . $topic);
            log_message("debug", "Client ID: " . $clientId);    
            log_message("debug", "Username : " . $data['username_server']);    
            return true;  // Gửi thành công, dừng vòng lặp và trả về true
        } catch (\Exception $e) {
            log_message("error", "Lỗi gửi tới MQTT" . $e->getMessage());
            return false;
        }
    }
    
    

    static function timeToAudio($dateTime): string
    {
        // Lấy từng thành phần thời gian
        $hour = (int)$dateTime->format('H');
        $minute = (int)$dateTime->format('i');
        $day = (int)$dateTime->format('d');
        $month = (int)$dateTime->format('m');
        $year = (int)$dateTime->format('Y');

        // Mảng thay thế các từ thành các từ âm thanh tương ứng
        $replacement = [
            'mốt' => 'mot_',
            'một' => 'mot',
            'mươi' => 'muoi_',
            'mười' => 'muoi',
            'tỷ' => 'ty',
        ];

        // Khởi tạo NumberFormatter cho từng thành phần
        $formatter = new \NumberFormatter('vi', \NumberFormatter::SPELLOUT);

        // Chuyển đổi từng thành phần thời gian sang chữ và xử lý thay thế
        $hourText = ExecuteOutputDevice::replaceWords($formatter->format($hour), $replacement);
        $minuteText = ExecuteOutputDevice::replaceWords($formatter->format($minute), $replacement);
        $dayText = ExecuteOutputDevice::replaceWords($formatter->format($day), $replacement);
        $monthText = ExecuteOutputDevice::replaceWords($formatter->format($month), $replacement);
        // $yearText = ExecuteOutputDevice::replaceWords($formatter->format($year), $replacement);

        // Tạo chuỗi âm thanh
        $audioFiles = "vao+" . 
        str_replace(' ', '+', ExecuteOutputDevice::removeDiacritics($hourText)) . "+gio+" . 
        str_replace(' ', '+', ExecuteOutputDevice::removeDiacritics($minuteText)) . "+phut+day+" . 
        str_replace(' ', '+', ExecuteOutputDevice::removeDiacritics($dayText)) . "+month+" . 
        str_replace(' ', '+', ExecuteOutputDevice::removeDiacritics($monthText)) . "+" ;
        // str_replace(' ', '+', ExecuteOutputDevice::removeDiacritics($yearText)) . "+";

        return $audioFiles;
    }

    public static function amountToAudio($inputAmount): string
    {
        // Loại bỏ ký tự không phải số
        $inputAmount = preg_replace('/[^0-9]/', '', $inputAmount);

        // Mảng thay thế các từ thành các từ âm thanh tương ứng
        $replacement = [
            'mốt' => 'mot_',
            'một' => 'mot',
            'mươi' => 'muoi_',
            'mười' => 'muoi',
            'tỷ' => 'ty',
        ];

        // Chuyển số thành chữ tiếng Việt
        $formatter = new \NumberFormatter('vi', \NumberFormatter::SPELLOUT);
        $amountText = $formatter->formatCurrency($inputAmount, 'VND');

        // Tách các từ trong chuỗi số thành chữ
        $wordsArray = explode(' ', $amountText);

        // Thay thế các từ theo mảng replacement
        $modifiedWordsArray = array_map(function($word) use ($replacement) {
            return $replacement[$word] ?? $word;
        }, $wordsArray);

        // Ghép các từ lại và loại bỏ dấu
        $modifiedAmountText = implode(' ', $modifiedWordsArray);
        $audioFiles = str_replace(' ', '+', ExecuteOutputDevice::removeDiacritics($modifiedAmountText));

        return "sepay+{$audioFiles}+dong+";
    }

     // Hàm loại bỏ dấu tiếng Việt
     protected static function removeDiacritics($text): string
     {
         $unwanted = [
             'á', 'à', 'ả', 'ã', 'ạ', 'â', 'ầ', 'ấ', 'ậ', 'ẩ', 'ẫ', 'ă', 'ằ', 'ắ', 'ặ', 'ẳ', 'ẵ',
             'é', 'è', 'ẻ', 'ẽ', 'ẹ', 'ê', 'ề', 'ế', 'ệ', 'ể', 'ễ',
             'í', 'ì', 'ỉ', 'ĩ', 'ị',
             'ó', 'ò', 'ỏ', 'õ', 'ọ', 'ô', 'ồ', 'ố', 'ộ', 'ổ', 'ỗ', 'ơ', 'ờ', 'ớ', 'ợ', 'ở', 'ỡ',
             'ú', 'ù', 'ủ', 'ũ', 'ụ', 'ư', 'ừ', 'ứ', 'ự', 'ử', 'ữ',
             'ý', 'ỳ', 'ỷ', 'ỹ', 'ỵ',
             'đ', 'Đ',
         ];
         $replace = [
             'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a',
             'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e',
             'i', 'i', 'i', 'i', 'i',
             'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o',
             'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u',
             'y', 'y', 'y', 'y', 'y',
             'd', 'D',
         ];
 
         return str_replace($unwanted, $replace, $text);
     }

     static function replaceWords($text, $replacement)
     {
         return strtr($text, $replacement);
     }

     // lưu log
     static public function saveLog($data)
    {
        $model = model(OutputDeviceLogModel::class);
        $result = $model->insert($data); 
        if (!$result) {
            return false;
        }
    
        return true;
    }
 
    static function pushMessageQRMqtt($device_id){
        $OutputDeviceModel = model(OutputDeviceModel::class);

        // Truy vấn dữ liệu từ bảng tb_autopay_output_device và tb_autopay_mqtt_server
        $data_device = $OutputDeviceModel
            ->select('
                tb_autopay_output_device.*, 
                tb_autopay_mqtt_server.hostname, 
                tb_autopay_mqtt_server.username AS username_server, 
                tb_autopay_mqtt_server.password AS password_server, 
                tb_autopay_mqtt_server.port, 
                tb_autopay_mqtt_server.ssl_enabled
            ')
            ->where(['tb_autopay_output_device.id' => $device_id])
            ->join('tb_autopay_mqtt_server', 'tb_autopay_mqtt_server.id = tb_autopay_output_device.mqtt_server_id', 'left')
            ->get()
            ->getRowArray();

        // Kiểm tra nếu không có dữ liệu
        if (empty($data_device)) {
            log_message("debug","Không tìm thấy thông tin thiết bị");
            return false;
        }

        if ($data_device['model'] != "Q190") {
            log_message("debug","Thiết bị không hỗ trợ LCD");
            return false;
        }

        if (empty($data_device['qr_content'])) {
            log_message("debug","Thiết bị Q190 chưa cấu hình QR");
            return false;
        }

        if ($data_device['online_status'] != "on") {
            log_message("debug","Thiết bị đang offline");
            return false;
        }

         // Tin nhắn sẽ gửi đi
         $message = json_encode([
            'type' => 'QR',
            'qr_content' => $data_device['qr_content'] ?? "",
            'bank_name' => $data_device['qr_bank_name'] ?? "",
            'bank_account' => $data_device['qr_bank_account'] ?? "",
            'amount' => isset($data_device['qr_amount']) && (float)$data_device['qr_amount'] > 0
                ? number_format((float)$data_device['qr_amount'], 0, ',', '.') . ' VND'
                : "",
            'position' => self::checkStringLength($data_device['qr_content']??"")    
        ]);
        
        
        $username = $data_device['username_server'];
        $password = $data_device['password_server'];
        // Cấu hình thông tin kết nối
        $hostname = $data_device['hostname'];
        $port = $data_device['port'];
        $clientId = 'topic_'.$data_device['serial_number'];
        $topic = 'topic_'.$data_device['serial_number'];    
        try {
            $client = new MqttClient($hostname, $port, $clientId);    
            $connectSettings = (new ConnectionSettings())
                ->setUsername($username)
                ->setPassword($password)
                ->setKeepAliveInterval(60)
                ->setUseTls($data_device['ssl_enabled']);  // Dùng TLS nếu cần thiết    
            // Kết nối tới MQTT broker
            $client->connect($connectSettings);    
            // Gửi tin nhắn
            $client->publish(
                $topic,
                $message,
                MqttClient::QOS_AT_MOST_ONCE,
                false
            );    
            // Ngắt kết nối
            $client->disconnect();    
            // Ghi lại log
            log_message("debug", "Nội dung tin nhắn đã gửi: " . $message);
            log_message("debug", "Topic đã gửi: " . $topic);
            log_message("debug", "Client ID: " . $clientId);    
            log_message("debug", "Username : " . $hostname);    
            return true;  // Gửi thành công, dừng vòng lặp và trả về true
        } catch (\Exception $e) {
            log_message("error", "Lỗi gửi tới MQTT" . $e->getMessage());
            return false;
        }
    }

    // 
    static function checkStringLength($string) {
        $length = strlen($string);
    
        if ($length <= 110) {
            return 1;
        }
    
        if ($length > 200) {
            return 10;
        }
    
        // Tính nhóm dựa trên khoảng 10 ký tự từ 111 đến 200
        $group = ceil(($length - 110) / 10) + 1;
    
        return $group;
    }

    static function pushMessageByDeviceID($device_id,$transactionId,$replay=0){

        $OutputDeviceModel = model(OutputDeviceModel::class);
        // Lấy dữ liệu giao dịch
        helper('general');
        $TransactionModel = slavable_model(TransactionsModel::class, 'ExecuteOutputDevice');
        $transactionData = $TransactionModel->where(['id' => $transactionId])->get()->getRowArray();

        // Truy vấn dữ liệu từ bảng tb_autopay_output_device và tb_autopay_mqtt_server
        $data_device = $OutputDeviceModel
            ->select('
                tb_autopay_output_device.*, 
                tb_autopay_mqtt_server.id AS mqtt_server_id, 
                tb_autopay_mqtt_server.hostname, 
                tb_autopay_mqtt_server.username AS username_server, 
                tb_autopay_mqtt_server.password AS password_server, 
                tb_autopay_mqtt_server.port, 
                tb_autopay_mqtt_server.ssl_enabled
            ')
            ->where(['tb_autopay_output_device.id' => $device_id])
            ->join('tb_autopay_mqtt_server', 'tb_autopay_mqtt_server.id = tb_autopay_output_device.mqtt_server_id', 'left')
            ->get()
            ->getRowArray();

        // Kiểm tra nếu không có dữ liệu
        if (empty($data_device)) {
            log_message("debug","Không tìm thấy thông tin thiết bị");
            return false;
        }

        if (empty($transactionData)) {
            log_message("debug","Không tìm thấy thông tin giao dịch");
            return false;
        }

       

        if ($data_device['online_status'] != "on") {
            log_message("debug","Thiết bị đang offline");
            return false;
        }


        $queue_message = array(
            'output_device_id' => $device_id,
            'transaction_id' => $transactionId,
        );

        // log data
        $log_data = array(
            'output_device_id' => $device_id,
            'server_mqtt_id' => $data_device['mqtt_server_id'] ?? "",
            'transaction_id' => $transactionId ?? "",
            'message' => (int)$transactionData['amount_in'] . " - " . ExecuteOutputDevice::amountToAudio((int)$transactionData['amount_in']),
        );

        $message_time = null;

        if($replay == 1){
            $day_missed = $transactionData['transaction_date'];
             // Chuẩn bị thông điệp để gửi qua MQTT
            log_message("debug","ngày nhận giao dịch :".$day_missed);
            $dateTime = new \DateTime($day_missed); 
            $message_time = ExecuteOutputDevice::timeToAudio($dateTime);
        }    

        $result_push = ExecuteOutputDevice::sendMessageToMqtt($data_device, (int)$transactionData['amount_in'],$message_time);
        $queue_message['status'] = $result_push ? "Success" : "Failed";
        $log_data['status'] = $queue_message['status'];
        log_message("debug", $queue_message['status'] == "Success" ?
        "Gửi thông tin tới thiết bị thành công, ID thiết bị " . json_encode($device_id) :
        "Gửi thông tin tới thiết bị thất bại, ID thiết bị " . json_encode($device_id));

        $savelog = self::saveLog($log_data);
        return "Handle case success!";
    }
   
}