<?php

namespace App\Actions\BankAccount;

use App\Models\BankAccountModel;

class DisableAllBankAccountAction
{
    public static function run($companyId)
    {
        $bankAccountModel = model(BankAccountModel::class);

        if ($bankAccountModel->where('company_id', $companyId)->where('active', true)->countAllResults() == 0) {
            return;
        }

        $bankAccountModel
            ->set('active', false)
            ->where('company_id', $companyId)
            ->update();

        add_system_log([
            'company_id' => $companyId,
            'data_type' => 'disable_all_bank_account_by_user',
            'description' => 'Disable all bank account by user when choosing promotion plan',
            'level' => 'Info',
            'by' => 'DisableAllBankAccountAction',
        ]);
    }
}
