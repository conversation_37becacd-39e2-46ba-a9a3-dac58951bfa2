<?php

namespace App\Actions\BankAccount;

use App\Libraries\MbbMmsClient;
use App\Models\BankIntegrationOutputDeviceModel;
use Exception;
use App\Models\SmsModel;
use App\Models\SapoModel;
use App\Models\HaravanModel;
use App\Models\WebhooksModel;
use App\Models\BankAccountModel;
use App\Models\WebhooksLogModel;
use App\Models\TransactionsModel;
use App\Models\BankSubAccountModel;
use App\Models\UserPermissionBankModel;
use App\Models\BankAccountCashflowModel;
use App\Models\NotificationTelegramModel;
use App\Models\UserPermissionBankSubModel;
use App\Models\BankSubAccountCashflowModel;
use App\Models\NotificationLarkMessengerModel;
use App\Models\NotificationTelegramQueueModel;
use App\Models\BankAccountApiDisconnectionModel;
use App\Models\BankShopLinkModel;
use App\Models\BankSubAccountMetadataModel;
use App\Models\MbbMmsTerminalModel;
use App\Models\NotificationLarkMessengerQueueModel;
use App\Models\NotificationViberModel;
use App\Models\NotificationViberQueueModel;
use App\Models\OutputDeviceReplayMessageQueueModel;
use App\Models\ShopModel;
use App\Models\WebhooksBankSubAccountModel;

class DeleteCompanyBankAccountAction
{
    public const DELETE_DEMO_BANK_ACCOUNT_ERR_CODE = 1;
    public const DELETE_ACTIVE_BANK_ACCOUNT_WITHOUT_ADMIN_ROLE_ERR_CODE = 2;

    public static function run($bankAccountDetails, $companyDetails, $ipAddress, $executionClass)
    {
        helper('general');

        $transactionsModel = slavable_model(TransactionsModel::class, 'DeleteCompanyBankAccountAction');
        $transactionsCount = $transactionsModel->where([
            'bank_account_id' => $bankAccountDetails->id,
            'deleted_at' => null
        ])->countAllResults();

        if ($ipAddress != "**************" && $companyDetails->id == 5 && $transactionsCount > 0)
            throw new Exception('Tài khoản demo không thể xóa ngân hàng đã có giao dịch.', self::DELETE_DEMO_BANK_ACCOUNT_ERR_CODE);
        
        if (!$bankAccountDetails->merchant_id && $transactionsCount > 0 && !in_array($companyDetails->role, ['Admin', 'SuperAdmin']))
            throw new Exception('Tài khoản ngân hàng này đã có giao dịch trên hệ thống. Để xóa tài khoản ngân hàng này, người dùng phải có quyền Admin hoặc Super Admin.', self::DELETE_ACTIVE_BANK_ACCOUNT_WITHOUT_ADMIN_ROLE_ERR_CODE);


        if ($bankAccountDetails->bank_api == 1 && $bankAccountDetails->bank_api_connected == 1) {
            self::archiveBankAccountApiDisconnection($bankAccountDetails, $companyDetails);
        }

        self::deleteWebhooks($bankAccountDetails, $companyDetails, $executionClass);
        self::deleteSapos($bankAccountDetails, $companyDetails, $executionClass);
        self::deleteHaravans($bankAccountDetails, $companyDetails, $executionClass);
        self::deleteTelegrams($bankAccountDetails, $companyDetails, $executionClass);
        self::deleteLarkMessengers($bankAccountDetails, $companyDetails, $executionClass);
        self::deleteVibers($bankAccountDetails, $companyDetails, $executionClass);
        self::deleteBankSubAccounts($bankAccountDetails, $companyDetails, $executionClass);
        $deletedTransactionIds = self::deleteTransactions($bankAccountDetails, $companyDetails, $executionClass);
        self::deleteCashflows($bankAccountDetails, $companyDetails, $executionClass);
        self::deleteBankAccount($bankAccountDetails, $companyDetails, $executionClass);
        self::deleteOutputDeviceIntegration($bankAccountDetails, $companyDetails);
        self::deleteBankShopLink($bankAccountDetails, $companyDetails);

        if (!empty($deletedTransactionIds) && is_array($deletedTransactionIds)) {
            self::deleteOutputDeviceMessageQueue($deletedTransactionIds, $companyDetails);
        }

        return true;
    }

    protected static function archiveBankAccountApiDisconnection($bankAccountDetails, $companyDetails)
    {
        $bankAccountApiDisconnectionModel = model(BankAccountApiDisconnectionModel::class);
        $bankAccountApiDisconnectionModel->insert([
            'bank_id' => $bankAccountDetails->bank_id,
            'account_number' => $bankAccountDetails->account_number,
            'company_id' => $companyDetails->id,
            'status' => 'disconnected',
        ]);
    }

    protected static function deleteWebhooks($bankAccountDetails, $companyDetails, $executionClass)
    {
        $webhooksModel = model(WebhooksModel::class);
        $webhooksLogModel = model(WebhooksLogModel::class);
        $webhooksBankSubAccountModel = model(WebhooksBankSubAccountModel::class);
 
        $webhooks = $webhooksModel->where([
            'bank_account_id' => $bankAccountDetails->id, 
            'company_id' => $companyDetails->id, 
            'deleted_at' => null
        ])->get()->getResult();

        $webhooksModel->where([
            'bank_account_id' => $bankAccountDetails->id, 
            'company_id' => $companyDetails->id, 
            'deleted_at' => null
        ])->delete();

        $webhooksLogModel->where([
            'company_id' => $companyDetails->id,
            'webhook_type' => 'Webhooks'
        ])->delete();

        if (count($webhooks)) {
            $webhooksBankSubAccountModel->whereIn('webhook_id', array_column($webhooks, 'id'))->delete();
        }
        
        foreach ($webhooks as $webhook) {
            add_system_log(['company_id' => $companyDetails->id, 'data_type' => 'hard_delete_webhook_by_user', 'description' => 'Hard delete webhook ID ' . $webhook->id . '. By user delete bank account ' . $bankAccountDetails->account_number, 'level' => 'Info', 'by' => $executionClass]);
        }
    }

    protected static function deleteSapos($bankAccountDetails, $companyDetails, $executionClass)
    {
        $webhooksLogModel = model(WebhooksLogModel::class);
        $sapoModel = model(SapoModel::class);
        
        $sapos = $sapoModel->where([
            'bank_account_id' => $bankAccountDetails->id, 
            'company_id' => $companyDetails->id, 
            'deleted_at' => null
        ])->get()->getResult();

        $sapoModel->where([
            'bank_account_id' => $bankAccountDetails->id, 
            'company_id' => $companyDetails->id, 
            'deleted_at' => null
        ])->delete();

        $webhooksLogModel->where([
            'company_id' => $companyDetails->id,
            'webhook_type' => 'Sapo'
        ])->delete();

        foreach($sapos as $sapo) {
            add_system_log(['company_id' => $companyDetails->id, 'data_type' => 'hard_delete_sapo_by_user', 'description' => 'Hard delete Sapo ID ' . $sapo->id . '. By user delete bank account ' . $bankAccountDetails->account_number, 'level' => 'Info', 'by' => $executionClass]);
        }
    }

    protected static function deleteHaravans($bankAccountDetails, $companyDetails, $executionClass)
    {
        $webhooksLogModel = model(WebhooksLogModel::class);
        $haravanModel = model(HaravanModel::class);
        
        $haravans = $haravanModel->where([
            'bank_account_id' => $bankAccountDetails->id, 
            'company_id' => $companyDetails->id, 
            'deleted_at' => null
        ])->get()->getResult();

        $haravanModel->where([
            'bank_account_id' => $bankAccountDetails->id, 
            'company_id' => $companyDetails->id, 
            'deleted_at' => null
        ])->delete();

        $webhooksLogModel->where([
            'company_id' => $companyDetails->id,
            'webhook_type' => 'Haravan'
        ])->delete();
        
        foreach($haravans as $haravan) {
            add_system_log(['company_id' => $companyDetails->id, 'data_type' => 'hard_delete_haravan_by_user', 'description' => 'Hard delete Haravan ID ' . $haravan->id . '. By user delete bank account ' . $bankAccountDetails->account_number, 'level' => 'Info', 'by' => $executionClass]);
        }
    }

    protected static function deleteTelegrams($bankAccountDetails, $companyDetails, $executionClass)
    {
        $notificationTelegramModel = model(NotificationTelegramModel::class);
        $notificationTelegramQueueModel = model(NotificationTelegramQueueModel::class);

        $telegrams = $notificationTelegramModel->where([
            'bank_account_id' => $bankAccountDetails->id, 
            'company_id' => $companyDetails->id, 
            'deleted_at' => null
        ])->get()->getResult();

        $notificationTelegramModel->where([
            'bank_account_id' => $bankAccountDetails->id, 
            'company_id' => $companyDetails->id, 
            'deleted_at' => null
        ])->delete();

        foreach ($telegrams as $telegram) {
            $notificationTelegramQueueModel->where(['notify_id' => $telegram->id])->delete();
            add_system_log(['company_id' => $companyDetails->id, 'data_type' => 'hard_delete_telegram_by_user', 'description' => 'Hard delete Telegram ID ' . $telegram->id . '. By user delete bank account ' . $bankAccountDetails->account_number, 'level' => 'Info', 'by' => $executionClass]);
        }
    }

    protected static function deleteLarkMessengers($bankAccountDetails, $companyDetails, $executionClass)
    {
        $notificationLarkMessengerModel = model(NotificationLarkMessengerModel::class);
        $notificationLarkMessengerQueueModel = model(NotificationLarkMessengerQueueModel::class);

        $larkMessengers = $notificationLarkMessengerModel->where([
            'bank_account_id' => $bankAccountDetails->id, 
            'company_id' => $companyDetails->id, 
            'deleted_at' => null
        ])->get()->getResult();
        
        $notificationLarkMessengerModel->where([
            'bank_account_id' => $bankAccountDetails->id, 
            'company_id' => $companyDetails->id, 
            'deleted_at' => null
        ])->delete();

        foreach ($larkMessengers as $larkMessenger) {
            $notificationLarkMessengerQueueModel->where(['notify_id' => $larkMessenger->id])->delete();
            add_system_log(['company_id' => $companyDetails->id, 'data_type' => 'hard_delete_lark_messenger_by_user', 'description' => 'Hard delete Lark Messenger ID ' . $larkMessenger->id . '. By user delete bank account ' . $bankAccountDetails->account_number,'level' => 'Info', 'by' => $executionClass]);
        }
    }

    protected static function deleteVibers($bankAccountDetails, $companyDetails, $executionClass)
    {
        $notificationViberModel = model(NotificationViberModel::class);
        $notificationViberQueueModel = model(NotificationViberQueueModel::class);

        $vibers = $notificationViberModel
            ->select('id')
            ->where('company_id', $companyDetails->id)
            ->where('bank_account_id', $bankAccountDetails->id)
            ->where('deleted_at', null)
            ->findAll();
        
        if (empty($vibers)) {
            return;
        }

        $notificationViberModel->delete(array_column($vibers, 'id'));

        foreach ($vibers as $viber) {
            $notificationViberQueueModel->where('notify_id', $viber->id)->delete();

            add_system_log([
                'company_id' => $companyDetails->id,
                'data_type' => 'hard_delete_viber_by_user',
                'description' => 'Hard delete Viber ID ' . $viber->id . '. By user delete bank account ' . $bankAccountDetails->account_number,
                'level' => 'Info',
                'by' => $executionClass,
            ]);
        }
    }

    protected static function deleteBankSubAccounts($bankAccountDetails, $companyDetails, $executionClass)
    {
        $bankSubAccountModel = model(BankSubAccountModel::class);
        $userPermissionBankSubModel = model(UserPermissionBankSubModel::class);

        $bankSubAccounts = $bankSubAccountModel->where([
            'bank_account_id' => $bankAccountDetails->id, 
            'deleted_at' => null
        ])->get()->getResult();

        if ($bankAccountDetails->bank_id == 8) {
            $terminal = model(MbbMmsTerminalModel::class)
                ->where('bank_account_id', $bankAccountDetails->id)
                ->first();

            if ($terminal) {
                $mbbMmsClient = new MbbMmsClient();
                $mbbMmsClient->setMerchantById($terminal->merchant_id);

                $qrCodes = $bankSubAccountModel
                    ->select('qrcode')
                    ->where('bank_account_id', $bankAccountDetails->id)
                    ->where('deleted_at', null)
                    ->where('qrcode IS NOT NULL')
                    ->get()
                    ->getResult();

                foreach ($qrCodes as $qrCode) {
                    $mbbMmsClient->deleteQrCode($qrCode->qrcode);
                }

                model(BankSubAccountMetadataModel::class)
                    ->where('bank_account_id', $bankAccountDetails->id)
                    ->delete();
            }
        }

        $bankSubAccountModel->where([
            'bank_account_id' => $bankAccountDetails->id, 
            'deleted_at' => null
        ])->delete();

        foreach ($bankSubAccounts as $bankSubAccount) {
            $userPermissionBankSubModel->where(['sub_account_id' => $bankSubAccount->id])->delete();
            add_system_log(['company_id' => $companyDetails->id, 'data_type' => 'hard_delete_bank_sub_account_by_user', 'description' => 'Hard delete Bank sub account ID ' . $bankSubAccount->id . '. By user delete bank account ' . $bankAccountDetails->account_number, 'level' => 'Info', 'by' => $executionClass]);
        }
    }

    protected static function deleteTransactions($bankAccountDetails, $companyDetails, $executionClass)
    {
        $transactionsModel = model(TransactionsModel::class);
        $smsModel = model(SmsModel::class);       
 
        $transactions = slavable_model(TransactionsModel::class, 'DeleteCompanyBankAccountAction')
            ->select('tb_autopay_sms_parsed.id, tb_autopay_sms_parsed.sms_id, tb_autopay_sms_parsed.source')
            ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id=tb_autopay_sms_parsed.bank_account_id')
            ->where([
                'tb_autopay_bank_account.company_id' => $companyDetails->id, 
                'tb_autopay_sms_parsed.deleted_at' => null, 
                'tb_autopay_bank_account.id' => $bankAccountDetails->id
            ])->get()->getResult();
        
        if (count($transactions) > 0) {
            $smsCount = 0;

            foreach ($transactions as $transaction) {
                if ($transaction->source == 'SMS' && $transaction->sms_id > 0) {
                    $smsModel->delete($transaction->sms_id);
                    $smsCount++;
                }
                $transactionsModel->delete($transaction->id);
            }

            add_system_log(['company_id' => $companyDetails->id, 'data_type' => 'soft_delete_sms_by_user', 'description' => 'Soft delete ' . $smsCount . ' sms. By user delete bank account ' . $bankAccountDetails->account_number, 'level' => 'Info', 'by' => $executionClass]);
            add_system_log(['company_id' => $companyDetails->id, 'data_type' => 'soft_delete_transactions_by_user', 'description' => 'Soft delete ' . count($transactions) . ' transactions. By user delete bank account ' . $bankAccountDetails->account_number, 'level' => 'Info', 'by' => $executionClass]);    
        }

        if (!empty($transactions)) {
            $transactionIds = [];
            foreach ($transactions as $transaction) {
                $transactionIds[] = $transaction->id;
            }
            return $transactionIds;
        }
        

        return [];
    }

    protected static function deleteCashflows($bankAccountDetails)
    {
        $bankAccountCashflowModel = model(BankAccountCashflowModel::class);
        $bankSubAccountCashflowModel = model(BankSubAccountCashflowModel::class);

        $bankAccountCashflowModel->where(['bank_account_id' => $bankAccountDetails->id])->delete();
        $bankSubAccountCashflowModel->where(['bank_account_id' => $bankAccountDetails->id])->delete();
    }

    protected static function deleteBankAccount($bankAccountDetails)
    {
        $bankAccountModel = model(BankAccountModel::class);
        $userPermissionBankModel = model(UserPermissionBankModel::class);
        $mbbMmsTerminalModel = model(MbbMmsTerminalModel::class);
        
        $bankAccountModel->delete($bankAccountDetails->id);
        $userPermissionBankModel->where('bank_account_id', $bankAccountDetails->id)->delete();
        $mbbMmsTerminalModel->where('bank_account_id', $bankAccountDetails->id)->delete();
    }

    protected static function deleteOutputDeviceIntegration($bankAccountDetails, $companyDetails)
    {
        $integrationModel = model(BankIntegrationOutputDeviceModel::class);

        $integrationModel
            ->where('bank_account_id', $bankAccountDetails->id)
            ->where('company_id', $companyDetails->id)
            ->delete();
    }

    protected static function deleteOutputDeviceMessageQueue($deletedTransactionIds, $companyDetails)
    {
        $outputDeviceMessageQueueModel = model(OutputDeviceReplayMessageQueueModel::class);

        if (empty($deletedTransactionIds)) {
            return;
        }

        log_message("debug","list id transaction delete: ".json_encode($deletedTransactionIds));

        $outputDeviceMessageQueueModel
            ->whereIn('transaction_id', $deletedTransactionIds)
            ->where('company_id', $companyDetails->id)
            ->delete();
    }
    
    protected static function deleteBankShopLink($bankAccountDetails, $companyDetails)
    {
        $shops = model(ShopModel::class)
            ->where('company_id', $companyDetails->id)
            ->get()->getResult();
            
        $shopIds = array_column($shops, 'id');
        
        if (!count($shopIds)) return;
        
        model(BankShopLinkModel::class)
            ->where('bank_account_id', $bankAccountDetails->id)
            ->whereIn('shop_id', $shopIds)
            ->delete();
    }
}
