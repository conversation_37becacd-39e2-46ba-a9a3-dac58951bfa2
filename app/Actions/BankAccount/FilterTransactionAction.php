<?php

namespace App\Actions\BankAccount;

use App\Models\CompanySubscriptionModel;
use App\Models\RejectedTransactionLogModel;
use App\Models\CounterModel;
use Config\Feature;
use Exception;

class FilterTransactionAction
{
    protected array $reasonMessages = [
        'sync_transactions_disabled' => 'Transaction syncing is disabled for this account',
        'sync_amount_in_disabled' => 'Incoming transaction syncing is disabled',
        'sync_amount_out_disabled' => 'Outgoing transaction syncing is disabled',
        'no_whitelist_match' => 'Transaction description does not match any allowed keywords: %s',
        'transaction_limit_exceeded' => 'Transaction limit exceeded',
    ];

    protected int $bankAccountId;

    protected ?string $description = null;

    protected bool $amountIn;

    protected bool $canSyncAccumulated = false;

    protected array $payload = [];

    public function __construct(int $bankAccountId)
    {
        $this->bankAccountId = $bankAccountId;

        helper('general');
    }

    public static function forBankAccount(int $bankAccountId)
    {
        return new static($bankAccountId);
    }

    public function description(?string $description): self
    {
        $this->description = $description;

        return $this;
    }

    public function amountIn(bool $amountIn = true)
    {
        $this->amountIn = $amountIn;

        return $this;
    }

    public function payload(array $payload): self
    {
        $this->payload = $payload;

        return $this;
    }

    protected function checkTransactionLimit(): ?array
    {
        $subscription = model(CompanySubscriptionModel::class)
            ->select([
                'tb_autopay_company_subscription.company_id',
                'tb_autopay_company_subscription.allow_exceed_limit',
                'tb_autopay_company_subscription.monthly_transaction_limit',
                'tb_autopay_company_subscription.begin_date',
                'tb_autopay_company_subscription.end_date',
                'tb_autopay_company_subscription.billing_cycle',
            ])
            ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.company_id = tb_autopay_company_subscription.company_id')
            ->where('tb_autopay_bank_account.id', $this->bankAccountId)
            ->first();

        if (! $subscription) {
            return null;
        }

        if ($subscription->allow_exceed_limit) {
            return null;
        }

        $monthlyTransactionLimit = $subscription->monthly_transaction_limit * get_month_by_billing_cycle($subscription->billing_cycle);

        if ($monthlyTransactionLimit <= 0) {
            return null;
        }

        $transactionsCount = model(CounterModel::class)
            ->selectSum('transaction')
            ->where('company_id', $subscription->company_id)
            ->where('date >=', $subscription->begin_date)
            ->where('date <=', $subscription->end_date)
            ->first();

        if (! $transactionsCount) {
            return null;
        }

        $transactionsCount = $transactionsCount->transaction;

        if (config(Feature::class)->dryRunCheckTransactionLimitEnabled ?? true) {
            log_message('error', '[DRY RUN LIMIT TRANSACTION DETAILS]: ' . $transactionsCount . ' - ' . json_encode($subscription));
        }

        if ($transactionsCount >= $monthlyTransactionLimit) {
            return $this->buildResponse('transaction_limit_exceeded');
        }

        return null;
    }

    /**
     * @return array{canSyncAccumulated: bool, isRejected: bool, message: mixed, reason: string|array{canSyncAccumulated: bool, isRejected: bool, message: null, reason: null}}
     */
    public function filter()
    {
        $settings = bank_account_settings($this->bankAccountId);

        $this->canSyncAccumulated = $settings->get('sync_accumulated', true);

        try {
            if ($limitCheck = $this->checkTransactionLimit()) {
                if (config(Feature::class)->dryRunCheckTransactionLimitEnabled ?? true) {
                    log_message('error', '[DRY RUN LIMIT TRANSACTION]: ' . json_encode($limitCheck));
    
                    return [
                        'isRejected' => false,
                        'canSyncAccumulated' => $this->canSyncAccumulated,
                        'reason' => null,
                        'message' => null,
                    ];
                }
    
                $this->logRejection($settings, $limitCheck['reason'], $limitCheck['message']);
                return $limitCheck;
            }
        } catch (Exception $e) {
            log_message('error', '[ERROR LIMIT TRANSACTION]: ' . $e->getMessage());
        }

        if (! $settings->get('sync_transactions', true)) {
            $result = $this->buildResponse('sync_transactions_disabled');
            $this->logRejection($settings, $result['reason'], $result['message']);
            return $result;
        }

        if ($this->amountIn && ! $settings->get('sync_amount_in', true)) {
            $result = $this->buildResponse('sync_amount_in_disabled');
            $this->logRejection($settings, $result['reason'], $result['message']);
            return $result;
        }

        if (! $this->amountIn && ! $settings->get('sync_amount_out', true)) {
            $result = $this->buildResponse('sync_amount_out_disabled');
            $this->logRejection($settings, $result['reason'], $result['message']);
            return $result;
        }

        $whitelistKeywords = $settings->get('whitelist_keywords', []);

        if (! empty($this->description) && ! empty($whitelistKeywords)) {
            $whitelistKeywords = array_map('strtolower', $whitelistKeywords);
            $tranDesc = strtolower($this->description);

            $isAllowed = false;
            foreach ($whitelistKeywords as $keyword) {
                if (strpos($tranDesc, $keyword) !== false) {
                    $isAllowed = true;
                    break;
                }
            }

            if (! $isAllowed) {
                $result = $this->buildResponse('no_whitelist_match', $whitelistKeywords);
                $this->logRejection($settings, $result['reason'], $result['message']);
                return $result;
            }
        }

        return [
            'isRejected' => false,
            'canSyncAccumulated' => $this->canSyncAccumulated,
            'reason' => null,
            'message' => null,
        ];
    }

    protected function buildResponse(string $reason, array $params = [])
    {
        $message = $this->reasonMessages[$reason];
        
        if (! empty($params)) {
            $message = sprintf($message, implode(', ', $params));
        }

        return [
            'isRejected' => true,
            'canSyncAccumulated' => $this->canSyncAccumulated,
            'reason' => $reason,
            'message' => $message,
        ];
    }

    protected function logRejection($settings, string $reason, string $message): void
    {
        model(RejectedTransactionLogModel::class)->insert([
            'bank_account_id' => $this->bankAccountId,
            'payload' => json_encode($this->payload),
            'reason' => $reason,
            'message' => $message,
            'config_snapshot' => json_encode($settings->toArray()),
        ]);
    }
}
