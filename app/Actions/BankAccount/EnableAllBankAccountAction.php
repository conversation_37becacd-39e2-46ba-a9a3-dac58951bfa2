<?php

namespace App\Actions\BankAccount;

use App\Models\BankAccountModel;

class EnableAllBankAccountAction
{
    public static function run($companyId)
    {
        $bankAccountModel = model(BankAccountModel::class);

        if ($bankAccountModel->where('company_id', $companyId)->where('active', false)->countAllResults() == 0) {
            return;
        }

        $bankAccountModel
            ->set('active', true)
            ->where('company_id', $companyId)
            ->update();

        add_system_log([
            'company_id' => $companyId,
            'data_type' => 'enable_all_bank_account_by_user',
            'description' => 'Enable all bank account by user',
            'level' => 'Info',
            'by' => 'EnableAllBankAccountAction',
        ]);
    }
}
