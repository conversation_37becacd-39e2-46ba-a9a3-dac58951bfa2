<?php

namespace App\Actions\Webhook;

use App\Models\WebhooksModel;

class CreateWebhookAction
{
    public static function run($bankAccount, $company, $merchant = null)
    {
        $safeWebhookData = [
            'name' => "[$merchant->name] Merchant Notify",
            'company_id' => $company->id,
            'bank_account_id' => $bankAccount->id,
            'event_type' => 'All',
            'authen_type' => 'Api_Key',
            'webhook_url' => $merchant->notify_url,
            'api_key' => $merchant->notify_api_key,
        ];

        if ($merchant) {
            $safeWebhookData['merchant_id'] = $merchant->id;
        }        

        return model(WebhooksModel::class)->insert($safeWebhookData);
    }
}