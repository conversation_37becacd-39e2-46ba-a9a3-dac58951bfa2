<?php

namespace App\Actions\Shopify;

use App\Models\CompanyModel;
use App\Models\CounterModel;
use App\Models\ShopifyModel;
use App\Libraries\ShopifyClient;
use App\Models\WebhooksLogModel;
use App\Models\TransactionsModel;
use App\Models\ConfigurationModel;

class ExecuteShopifyWebhooksAction
{
    public static function run($transactionId)
    {
        $transactionDetails = model(TransactionsModel::class)->where(['id' => $transactionId])->first();

        if (!$transactionDetails || $transactionDetails->parser_status != 'Success' || $transactionDetails->amount_in <= 0) return false;

        $bankAccountDetails = model(BankAccountModel::class)->where(['id' => $transactionDetails->bank_account_id, 'active' => 1])->first();
    
        if (!$bankAccountDetails || !is_numeric($bankAccountDetails->company_id)) return false;

        $companyDetails = model(CompanyModel::class)->where(['id' => $bankAccountDetails->company_id, 'active' => 1]);

        if (!$companyDetails) return false;

        $shopifies = model(ShopifyModel::class)->where(['bank_account_id' => $bankAccountDetails->id, 'active' => 1])->get()->getResult();

        $results = [];

        foreach ($shopifies as $shopify) {
            $results[$shopify->id] = static::handleShopifyWebhook($shopify, $transactionDetails);
        }

        return $results;
    }

    protected static function handleShopifyWebhook($shopify, $transactionDetails)
    {
        $client = new ShopifyClient($shopify->api_store_url, $shopify->api_token);

        $paymentCodePrefix = $shopify->paycode_prefix ?: model(ConfigurationModel::class)->getFirstPaycodePrefix($shopify->company_id);
		if (! $paymentCodePrefix) {
			return false;
		}

        preg_match('/'. $paymentCodePrefix . '[A-Za-z0-9]{4,12}/', $transactionDetails->transaction_content, $matches, PREG_OFFSET_CAPTURE);

        if (!is_array($matches) || !isset($matches[0][0])) return false;

        $shopifyOrderNumber = substr($matches[0][0], strlen($paymentCodePrefix));

        $queryOrderWebhookLog = [
            'success' => false,
            'connect_succes' => 0,
            'error_message' => '',
        ];

        try {
            $queryOrder = $client->queryPendingFinancialStatusOrderByOrderCode($shopifyOrderNumber);
            $queryOrderWebhookLog['connect_success'] = 1;
        } catch (\Exception $e) {
            $queryOrderWebhookLog['error_message'] = $e->getMessage();
        }

        if (!is_object($queryOrder['response'])) {
            return false;
        }

        $queryOrderWebhookLog['request_method'] = $queryOrder['request']['method'];
        $queryOrderWebhookLog['request_url'] = $queryOrder['request']['url'];
        $queryOrderWebhookLog['request_header'] = json_encode($queryOrder['request']['headers']);
        $queryOrderWebhookLog['request_body'] = json_encode($queryOrder['request']['json']);
        $queryOrderWebhookLog['response_body'] = $queryOrder['response']->getBody();
        $queryOrderWebhookLog['status_code'] = $queryOrder['response']->getStatusCode();
        $queryOrderWebhookLog['response_status_code'] = $queryOrder['response']->getStatusCode();
        $queryOrderWebhookLog['success'] = in_array($queryOrderWebhookLog['response_status_code'], [200, 201]);

        static::logShopifyWebhook($shopify, $transactionDetails->id, $queryOrderWebhookLog);

        if (!$queryOrderWebhookLog['success']) {
            return false;
        } 

        $queryOrderResponseData = json_decode($queryOrder['response']->getBody());

        if (!is_object($queryOrderResponseData) || !is_array($queryOrderResponseData->orders)) {
            return false;
        }

        if (!$queryOrderResponseData || count($queryOrderResponseData->orders) != 1 || !isset($queryOrderResponseData->orders[0]->id)) {
			return false;
		}

        $shopifyOrderId = $queryOrderResponseData->orders[0]->id;

        $createTransactionWebhookLog = [
            'success' => false,
            'connect_success' => 0,
            'error_message' => '',
        ];

        $shopifyParams = $shopify->params ? json_decode($shopify->params, true) : [];

        try {
            $createTransaction = $client->createTransaction($shopifyOrderId, intval($transactionDetails->amount_in), $shopifyParams['kind'] ?? 'capture', $shopifyParams['source'] ?? null);
            $createTransactionWebhookLog['connect_success'] = 1;
        } catch (\Exception $e) {
            $createTransactionWebhookLog['error_message'] = $e->getMessage();
        }

        if (!is_object($createTransaction['response'])) {
            return false;
        }

        $createTransactionWebhookLog['request_method'] = $createTransaction['request']['method'];
        $createTransactionWebhookLog['request_url'] = $createTransaction['request']['url'];
        $createTransactionWebhookLog['request_header'] = json_encode($createTransaction['request']['headers']);
        $createTransactionWebhookLog['request_body'] = json_encode($createTransaction['request']['json']);
        $createTransactionWebhookLog['response_body'] = $createTransaction['response']->getBody();
        $createTransactionWebhookLog['status_code'] = $createTransaction['response']->getStatusCode();
        $createTransactionWebhookLog['response_status_code'] = $createTransaction['response']->getStatusCode();
        $createTransactionWebhookLog['success'] = in_array($createTransactionWebhookLog['response_status_code'], [200, 201]);

        static::logShopifyWebhook($shopify, $transactionDetails->id, $createTransactionWebhookLog);

        if (!$createTransactionWebhookLog['success']) {
            return false;
        }

        $transactionsModel = model(TransactionsModel::class);
        $createTransactionResponseData = json_decode($createTransactionWebhookLog['response_body']);

        if (is_object($createTransactionResponseData)
        && isset($createTransactionResponseData->transaction) 
        && isset($createTransactionResponseData->transaction->status) 
        && $createTransactionResponseData->transaction->status == 'success') {
            $transactionsModel->set(['webhooks_verify_payment' => 'Success'])->where(['id' => $transactionDetails->id])->update();
        } else {
            $transactionsModel->set(['webhooks_verify_payment' => 'Failed'])->where(['id' => $transactionDetails->id])->update();
        }

        return [
            'query_order_log' => $queryOrderWebhookLog,
            'create_transaction_log' => $createTransactionWebhookLog,
        ];
    }

    protected static function logShopifyWebhook($shopify, $transactionId, $data)
    {
		model(WebhooksLogModel::class)->insert(array_merge([
            'webhook_type' => 'Shopify',
            'company_id' => $shopify->company_id,
            'webhook_id' => $shopify->id,
			'sms_parsed_id' => $transactionId
        ], $data));
		model(CounterModel::class)->webhook($shopify->company_id, false);

        $transactionsModel = model(TransactionsModel::class);

        if (isset($data['success']) && $data['success']) {
            $transactionsModel->set('webhooks_success', 'webhooks_success+1', false)->where(['id' => $transactionId])->update();
        } else {
            $transactionsModel->set('webhooks_failed', 'webhooks_failed+1', false)->where(['id' => $transactionId])->update();
        }
    }
}