<?php

namespace App\Actions;

use App\Models\TrackingModel;
use Config\Services;

class SaveUTMTracking
{
    protected static array $utmParams = [
        'utm_source',
        'utm_medium',
        'utm_campaign',
        'utm_term',
        'utm_content',
    ];

    public static function run(int $companyId)
    {
        if (! isset($_COOKIE['referer']) && ! isset($_COOKIE['utm_source'])) {
            return;
        }

        $request = Services::request();

        $data = [
            'company_id' => $companyId,
            'referer' => isset($_COOKIE['referer']) ? $_COOKIE['referer'] : $request->getServer('HTTP_REFERER'),
        ];

        foreach (static::$utmParams as $param) {
            $data[$param] = isset($_COOKIE[$param]) && is_string($_COOKIE[$param]) ? xss_clean($_COOKIE[$param]) : '';
        }

        if (empty($data['utm_source']) && ! empty($data['referer'])) {
            $url = parse_url($data['referer']);
            $host = $url['host'];
            $path = $url['path'] ?? '';

            $source = $host;

            if ($host === 'sepay.vn' && preg_match('/\/blog\//', $path)) {
                $source = 'sepay.vn/blog';
            }

            $data['utm_source'] = $source;
        }

        if (isset($data['referer']) && ! filter_var($data['referer'], FILTER_VALIDATE_URL)) {
            $data['referer'] = null;
        }

        model(TrackingModel::class)->insert($data);

        foreach (static::$utmParams as $param) {
            delete_cookie($param);
        }

        delete_cookie('referer');
    }
}
