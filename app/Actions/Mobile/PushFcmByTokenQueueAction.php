<?php

namespace App\Actions\Mobile;

use App\Libraries\RabbitMQClient;
use PhpAmqpLib\Message\AMQPMessage;

class PushFcmByTokenQueueAction
{
    /**
     * @see $notificaiton https://firebase.google.com/docs/reference/fcm/rest/v1/projects.messages#Notification
     */
    public static function run($token, $notification, $data = [], $apns = [], $deviceTokenId = null, $transactionId = null, $userId = null)
    {
        $rabbitmq = new RabbitMQClient;
        $rabbitmq->connect();

        $msg = new AMQPMessage(
            json_encode([
                'token' => $token,
                'notification' => $notification,
                'data' => $data,
                'apns' => $apns,
                'device_token_id' => $deviceTokenId,
                'transaction_id' => $transactionId,
                'user_id' => $userId,
            ]),
            array('delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT)
        );

        try {
            $rabbitmq->queueDeclare('fcm_pushing');
            $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('fcm_pushing', 'name'));
        } catch (Exception $e) {
            log_message('error', 'FCM pushing queue failed: ' . $e->getMessage());
        }

        $rabbitmq->close();
    }
}