<?php

namespace App\Actions\Mobile;

use Exception;
use App\Libraries\FcmClient;
use App\Libraries\RabbitMQClient;
use PhpAmqpLib\Message\AMQPMessage;

class PushMobileTransactionNotificationQueueAction
{
    public static function run($transactionDetails, $bankAccountDetails, $bankSubAccountDetails = null)
    {
        if (!is_object($transactionDetails)) throw new Exception("Missing \$transactionDetails.");
        if (!is_object($bankAccountDetails)) throw new Exception("Missing \$bankAccountDetails.");

        $rabbitmq = new RabbitMQClient;
        $rabbitmq->connect();

        $msg = new AMQPMessage(
            json_encode([
                'transaction_details' => $transactionDetails,
                'bank_account_details' => $bankAccountDetails,
                'bank_sub_account_details' => $bankSubAccountDetails,
            ]),
            array('delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT)
        );

        try {
            $rabbitmq->queueDeclare('mobile_transaction_notification_pushing');
            $rabbitmq->channel->basic_publish($msg, '', $rabbitmq->getQueueDeclaration('mobile_transaction_notification_pushing', 'name'));
        } catch (Exception $e) {
            log_message('error', 'Mobile transaction notification pushing queue failed: ' . $e->getMessage());
        }

        $rabbitmq->close();
    }
}