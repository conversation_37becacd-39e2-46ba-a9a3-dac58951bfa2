<?php

namespace App\Actions\Company;

use App\Models\ConfigurationModel;

class GetCompanyConfigurationAction
{
    public static function run($companyId, $key, $default = null, $slave = null)
    {
        helper('general');
        $configurationModel = slavable_model(ConfigurationModel::class, $slave);

        return $configurationModel->where(['company_id' => $companyId, 'setting' => $key])->get()->getRow() ?? $default;
    }
}