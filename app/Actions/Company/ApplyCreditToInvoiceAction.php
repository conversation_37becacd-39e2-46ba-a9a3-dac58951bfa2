<?php

namespace App\Actions\Company;

use Exception;
use App\Models\CompanyModel;
use App\Models\InvoiceModel;
use App\Models\CreditLogModel;
use App\Models\CompanyUserModel;
use Config\Services;

class ApplyCreditToInvoiceAction
{
    public static function run($invoiceId, $creditApply, $userId, $ipAddress = null, $userAgent = null)
    {
        $invoiceModel = model(InvoiceModel::class);
        $companyModel = model(CompanyModel::class);
        $companyUserModel = model(CompanyUserModel::class);
        $creditLogModel = model(CreditLogModel::class);

        $invoiceDetails = $invoiceModel->where(['id' => $invoiceId, 'status' => 'Unpaid', 'total > ' => 0])->first();

        if (!$invoiceDetails) {
            return ['status' => false, 'message' => 'Không tìm thấy hóa đơn', 'code' => 404];
        }

        $companyDetails = $companyModel->where(['id' => $invoiceDetails->company_id])->first();

        if (!$companyDetails) {
            throw new Exception('[ApplyCreditToInvoiceAction] $invoiceDetails->company_id does not exists');
        }

        if (! $companyUserModel->where(['company_id' => $companyDetails->id, 'user_id' => $userId])->countAllResults()) {
            throw new Exception('[ApplyCreditToInvoiceAction] $userId does not belongs to $invoiceDetails->company_id');
        }

        if (! is_numeric($creditApply) || $creditApply <= 0) {
            return ['status' => false, 'message' => 'Số tiền muốn trích phải lớn hơn 0', 'code' => 400];
        }

        if ($creditApply > $companyDetails->credit_balance) {
            $creditApply = $companyDetails->credit_balance;
        }

        if ($creditApply > $invoiceDetails->total) {
            $creditApply = $invoiceDetails->total;
        }

        $billingConfig = config(Config\Billing::class);
        
        if (!property_exists($billingConfig, 'creditUsagable') || (!$billingConfig->creditUsagable && !is_admin())) {
            return ['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ.', 'code' => 500];
        }

        if (! property_exists($billingConfig, 'paymentApiUrl')) {
            log_message('error', '[ApplyCreditToInvoiceAction] Missing the $paymentApiUrl property in app/Config/Billing.php');
            return ['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ.', 'code' => 500];
        }

        try {
            $response = Services::curlrequest()->request('POST', $billingConfig->paymentApiUrl, [
                'json' => [
                    'type' => 'credit',
                    'gateway' => 'Credit',
                    'transactionDate' => date('Y-m-d H:i:s'),
                    'accountNumber' => 'COMPANY-' . $companyDetails->id,
                    'transferAmount' => $creditApply,
                    'code'=> 'SEP' . $invoiceDetails->id,
                    'content' => 'SEP' . $invoiceDetails->id,
                    'transferType' => 'in',
                    'description' => 'Áp dụng tín dụng cho hóa đơn #' . $invoiceDetails->id,
                ]
            ]);
        } catch (\Exception $e) {
            log_message('error', '[ApplyCreditToInvoiceAction] CURL failed: ' . $e->getMessage());
            return ['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ.', 'code' => 500];
        }
        
        $responseData = json_decode($response->getBody());

        if (! is_object($responseData) || (is_object($responseData) && !property_exists($responseData, 'success')) || (is_object($responseData) && !property_exists($responseData, 'status'))) {
            log_message('error', '[ApplyCreditToInvoiceAction] Parsing response failed: ' . $response->getBody());
            return ['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ.', 'code' => 500];
        }

        if ($responseData->status != 201 || !$responseData->success) {
            log_message('error', '[ApplyCreditToInvoiceAction] Payment failed: ' . $response->getBody());
            return ['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ.', 'code' => 500];
        }

        $creditBalanceUpdated = $companyModel->where(['id' => $companyDetails->id])->set(['credit_balance' => $companyDetails->credit_balance - $creditApply])->update();

        if (!$creditBalanceUpdated) {
            log_message('error', '[ApplyCreditToInvoiceAction] Credit balance updated failed');
            return ['status' => false, 'message' => 'Đã có lỗi xảy ra, vui lòng thử lại.', 'code' => 503];
        }

        $creditLogModel->insert([
            'company_id' => $companyDetails->id,
            'user_id' => $userId,
            'amount_out' => $creditApply,
            'model_id' => $invoiceDetails->id,
            'model_type' => 'App\Models\InvoiceModel',
            'accumulated' => $companyDetails->credit_balance - $creditApply,
            'description' => 'Áp dụng tín dụng cho hóa đơn #' . $invoiceDetails->id,
            'ip' => $ipAddress, 
            'user_agent'=> $userAgent
        ]);

        return ['status' => true];
    }
}