<?php

namespace App\Actions\Company;

use App\Models\ConfigurationModel;

class InitCompanyConfigurationAction
{
    public static function run($companyId, $merchantId = null)
    {
        $configurationModel = model(ConfigurationModel::class);

        $defaultSettings = $merchantId ? [
            'BankSubAccount' => 'off', 
            'PayCode' => 'off',
            'PayCodePrefix' => '', 
            'PayCodeSuffixFrom' => '', 
            'PayCodeSuffixTo' => '', 
            'PayCodeSuffixCharacterType' => 'NumberOnly',
            'PayCodeStructures' => '',
            'DataStorageTime' => '6Months',
            'TransactionAmount' => 0,
            'KLBVaOrderLastNumber' => 0
        ] : $configurationModel->defaultSettings;

        foreach($defaultSettings as $key => $value) {
            $configurationModel->insert(['company_id' => $companyId, 'setting' => $key, 'value' => is_array($value) ? json_encode($value) : $value, 'merchant_id' => $merchantId]);
        }
    }
}