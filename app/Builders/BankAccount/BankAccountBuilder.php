<?php

namespace App\Builders\BankAccount;

use App\Builders\Builder;
use App\Models\BankAccountModel;

class BankAccountBuilder extends Builder
{
    public const INDEX_SELECT = ['tb_autopay_bank_account.id', 'tb_autopay_bank_account.company_id', 'tb_autopay_bank_account.bank_id', 'tb_autopay_bank_account.account_holder_name', 'tb_autopay_bank_account.account_number', 'tb_autopay_bank_account.accumulated', 'tb_autopay_bank_account.label', 'tb_autopay_bank_account.bank_api_connected', 'tb_autopay_bank_account.active', 'tb_autopay_bank_account.last_transaction', 'tb_autopay_bank_account.created_at', 'tb_autopay_bank_account.updated_at', 'tb_autopay_bank_account.merchant_id'];
    public const DETAIL_SELECT = ['tb_autopay_bank_account.id', 'tb_autopay_bank_account.company_id', 'tb_autopay_bank_account.bank_id', 'tb_autopay_bank_account.account_holder_name', 'tb_autopay_bank_account.account_number', 'tb_autopay_bank_account.accumulated', 'tb_autopay_bank_account.label', 'tb_autopay_bank_account.bank_api_connected', 'tb_autopay_bank_account.active', 'tb_autopay_bank_account.last_transaction', 'tb_autopay_bank_account.created_at', 'tb_autopay_bank_account.updated_at', 'tb_autopay_bank_account.identification_number', 'tb_autopay_bank_account.phone_number', 'tb_autopay_bank_account.merchant_id'];

    protected $bankId;

    protected $bankAccountId;

    protected $companyId;

    protected $accountNumber;

    public function whereBankId($bankId)
    {
        $this->bankId = $bankId;

        return $this;
    }

    public function whereCompanyId($companyId)
    {
        $this->companyId = $companyId;

        return $this;
    }

    public function whereAccountNumber($accountNumber)
    {
        $this->accountNumber = $accountNumber;

        return $this;
    }

    protected function handle()
    {
        $this->model = new BankAccountModel;

        $this->builder = $this->model
            ->select($this->select);
            
        if ($this->id) {
            $this->builder->where('tb_autopay_bank_account.id', $this->id);
        }
        
        if ($this->merchantId) {
            $this->builder->where('tb_autopay_bank_account.merchant_id', $this->merchantId);
        }

        if ($this->bankId) {
            $this->builder->where('tb_autopay_bank_account.bank_id', $this->bankId);
        }

        if ($this->companyId) {
            $this->builder->where('tb_autopay_bank_account.company_id', $this->companyId);
        }

        if ($this->accountNumber) {
            $this->builder->where('tb_autopay_bank_account.account_number', $this->accountNumber);
        }
            
        if ($this->q) {
            $this->builder->groupStart()
                ->like('tb_autopay_bank_account.account_holder_name', $this->q)
                ->orLike('tb_autopay_bank_account.account_number', $this->q)
                ->orLike('tb_autopay_bank_account.label', $this->q)
            ->groupEnd();
        }

        $this->builder->orderBy('created_at', 'desc');
    }
}