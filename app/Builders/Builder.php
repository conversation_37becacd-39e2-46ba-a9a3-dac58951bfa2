<?php

namespace App\Builders;

abstract class Builder
{
    protected $builder;

    protected $model;

    protected $q;

    protected $merchantId;

    protected $data = [];

    protected $meta = [];

    protected $select = ['*'];

    protected $id;

    protected $resolveRowCallback;

    public function whereId($id)
    {
        if (!$id) throw new \Exception("Missing ID for the builder");

        $this->id = $id;

        return $this;
    }

    public function search($q)
    {
        $this->q = $q;

        return $this;
    }

    public function select($select)
    {
        $this->select = $select;

        return $this;
    }

    public function whereMerchantId($merchantId)
    {
        if (!$merchantId) throw new \Exception("Missing merchant ID for the builder");

        $this->merchantId = $merchantId;

        return $this;
    }

    public static function make()
    {
        return new static;
    }

    abstract protected function handle();

    protected function resolvePager($pager)
    {
        return [
            'per_page' => $pager->getPerPage(),
            'total' => $pager->getTotal(),
            'has_more' => $pager->hasMore(),
            'current_page' => $pager->getCurrentPage(),
            'page_count' => $pager->getPageCount(),
        ];
    }

    public function paginate($perPage)
    {
        $this->handle();
    
        $perPage = is_numeric($perPage) ? $perPage : 20;

        $this->data = $this->builder->paginate($perPage);
        $this->meta = $this->resolvePager($this->model->pager);

        return $this;
    }

    public function first()
    {
        $this->handle();

        return $this->resolveRow($this->builder->get()->getRow());
    }

    public function get()
    {
        $this->handle();

        $this->data = $this->builder->get()->getResult();

        return $this;
    }

    public function count()
    {
        $this->handle();

        return $this->builder->countAllResults();
    }

    public function debug()
    {
        $db = db_connect();

        return (string) $db->getLastQuery();
    }

    protected function resolveRow($data)
    {
        if (is_null($data)) return null;
        
        if (!is_callable($this->resolveRowCallback)) {
            return $data;
        }
        
        $callback = $this->resolveRowCallback;
        
        return $callback($data);
    }

    public function data()
    {
        $data = $this->data;
        $groupedData = [];

        foreach ($data as $index => $row) {
            foreach ($row as $key => $value) {
                $groupPortion = explode('.', $key);

                if (count($groupPortion) == 2) {
                    $groupedData[$index][$groupPortion[0]][$groupPortion[1]] = $value;
                } else {
                    $groupedData[$index][$key] = $value;
                }
            }

            $groupedData[$index] = $this->resolveRow($groupedData[$index]);
        }

        return $groupedData;
    }

    public function meta()
    {
        return $this->meta;
    }
}