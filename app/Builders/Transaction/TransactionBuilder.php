<?php

namespace App\Builders\Transaction;

use App\Builders\Builder;
use App\Models\TransactionsModel;

class TransactionBuilder extends Builder
{
    public const INDEX_SELECT = ['tb_autopay_sms_parsed.id', 'tb_autopay_sms_parsed.transaction_id', 'tb_autopay_sms_parsed.transaction_date', 'tb_autopay_sms_parsed.bank_account_id', 'tb_autopay_sms_parsed.account_number', 'tb_autopay_bank_account.company_id', 'tb_autopay_bank_account.bank_id', 'tb_autopay_bank_sub_account.id as va_id', 'tb_autopay_sms_parsed.sub_account as va', 'tb_autopay_sms_parsed.reference_number', 'tb_autopay_sms_parsed.amount_in', 'tb_autopay_sms_parsed.amount_out', 'tb_autopay_sms_parsed.transaction_content', 'tb_autopay_sms_parsed.code as payment_code', 'tb_autopay_sms_parsed.merchant_id'];

    public const DETAIL_SELECT = ['tb_autopay_sms_parsed.id', 'tb_autopay_sms_parsed.transaction_id', 'tb_autopay_sms_parsed.transaction_date', 'tb_autopay_sms_parsed.bank_account_id', 'tb_autopay_sms_parsed.account_number', 'tb_autopay_bank_account.company_id', 'tb_autopay_bank_account.bank_id', 'tb_autopay_bank_sub_account.id as va_id', 'tb_autopay_sms_parsed.sub_account as va', 'tb_autopay_sms_parsed.reference_number', 'tb_autopay_sms_parsed.amount_in', 'tb_autopay_sms_parsed.amount_out', 'tb_autopay_sms_parsed.transaction_content', 'tb_autopay_sms_parsed.code as payment_code', 'tb_autopay_sms_parsed.merchant_id'];

    protected $companyId = null;

    protected $bankId = null;

    protected $bankAccountId = null;

    protected $transactionDate = null;

    protected $startTransactionDate = null;

    protected $endTransactionDate = null;

    protected $transferType = null;

    protected $vaId = null;

    protected $sortTransactionDate = 'desc';

    protected $webhookSuccess = 1;

    public function __construct()
    {
        $this->resolveRowCallback = function($data) {
            $data = (object) $data;

            if ($data->amount_in > 0) {
                $transferType = 'credit';
            } else {
                $transferType = 'debit';
            }

            $data->transfer_type = $transferType;
            $data->amount = $transferType === 'credit' ? $data->amount_in : $data->amount_out;
                        
            unset($data->amount_in);
            unset($data->amount_out);

            return $data;
        };
    }

    public function whereBankId($bankId)
    {
        $this->bankId = $bankId;

        return $this;
    }

    public function whereCompanyId($companyId)
    {
        $this->companyId = $companyId;

        return $this;
    }

    public function whereBankAccountId($bankAccountId)
    {
        $this->bankAccountId = $bankAccountId;

        return $this;
    }

    public function whereTransactionDate($transactionDate)
    {
        $this->transactionDate = $transactionDate;

        return $this;
    }

    public function whereStartTransactionDate($startTransactionDate)
    {
        $this->startTransactionDate = $startTransactionDate;

        return $this;
    }

    public function whereEndTransactionDate($endTransactionDate)
    {
        $this->endTransactionDate = $endTransactionDate;

        return $this;
    }

    public function whereTransferType($transferType)
    {
        $this->transferType = $transferType;

        return $this;
    }

    public function whereVaId($vaId)
    {
        $this->vaId = $vaId;

        return $this;
    }

    public function whereWebhookSuccess($webhookSuccess = null)
    {
        if ($webhookSuccess === '0' || $webhookSuccess === 0) {
            $this->webhookSuccess = 0;
        } else {
            $this->webhookSuccess = 1;
        }

        return $this;
    }

    public function handle()
    {
        $this->model = new TransactionsModel;

        $this->builder = $this->model->select($this->select);

        $this->builder->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id = tb_autopay_sms_parsed.bank_account_id');
        $this->builder->join('tb_autopay_bank_sub_account', 'tb_autopay_bank_sub_account.sub_account = tb_autopay_sms_parsed.sub_account', 'left');

        $this->builder->where('tb_autopay_sms_parsed.parser_status', 'Success');

        if ($this->id) {
            $this->webhookSuccess = null;
            
            $this->builder->groupStart()
                ->where('tb_autopay_sms_parsed.id', $this->id)
                ->orWhere('tb_autopay_sms_parsed.transaction_id', $this->id)
            ->groupEnd();
        }

        if ($this->webhookSuccess !== null) {
            $this->builder->where('tb_autopay_sms_parsed.webhooks_success', $this->webhookSuccess);
        }

        if ($this->merchantId) {
            $this->builder->where('tb_autopay_sms_parsed.merchant_id', $this->merchantId);
        }

        if ($this->companyId) {
            $this->builder->where('tb_autopay_bank_account.company_id', $this->companyId);
        }

        if ($this->bankId) {
            $this->builder->where('tb_autopay_bank_account.bank_id', $this->bankId);
        }

        if ($this->bankAccountId) {
            $this->builder->where('tb_autopay_sms_parsed.bank_account_id', $this->bankAccountId);
        }

        if ($this->transactionDate && preg_match('/^\d{4}\-\d{2}-\d{2}$/', $this->transactionDate)) {
            $this->builder->where("DATE_FORMAT(tb_autopay_sms_parsed.transaction_date, '%Y-%m-%d')", $this->transactionDate);
        }

        if ($this->transactionDate && preg_match('/^\d{4}\-\d{2}-\d{2} \d{2}\\d{2}\\d{2}$/', $this->transactionDate)) {
            $this->builder->where("DATE_FORMAT(tb_autopay_sms_parsed.transaction_date, '%Y-%m-%d %H%i%s')", $this->transactionDate);
        }

        if ($this->startTransactionDate && preg_match('/^\d{4}\-\d{2}-\d{2}$/', $this->startTransactionDate)) {
            $this->builder->where("DATE_FORMAT(tb_autopay_sms_parsed.transaction_date, '%Y-%m-%d') >=", $this->startTransactionDate);
        }

        if ($this->startTransactionDate && preg_match('/^\d{4}\-\d{2}-\d{2} \d{2}\\d{2}\\d{2}$/', $this->startTransactionDate)) {
            $this->builder->where("DATE_FORMAT(tb_autopay_sms_parsed.transaction_date, '%Y-%m-%d %H%i%s') >=", $this->startTransactionDate);
        }

        if ($this->endTransactionDate && preg_match('/^\d{4}\-\d{2}-\d{2}$/', $this->endTransactionDate)) {
            $this->builder->where("DATE_FORMAT(tb_autopay_sms_parsed.transaction_date, '%Y-%m-%d') <=", $this->endTransactionDate);
        }

        if ($this->endTransactionDate && preg_match('/^\d{4}\-\d{2}-\d{2} \d{2}\\d{2}\\d{2}$/', $this->endTransactionDate)) {
            $this->builder->where("DATE_FORMAT(tb_autopay_sms_parsed.transaction_date, '%Y-%m-%d %H%i%s') <=", $this->endTransactionDate);
        }

        if ($this->transferType == 'credit') {
            $this->builder->where('amount_in > ', 0);
        }

        if ($this->transferType == 'debit') {
            $this->builder->where('amount_out > ', 0);
        }

        if ($this->vaId) {
            $this->builder->where('tb_autopay_bank_sub_account.id', $this->vaId);
        }

        if ($this->q) {
            $this->builder->groupStart()
                ->like('tb_autopay_sms_parsed.transaction_content', $this->q)
                ->orLike('tb_autopay_sms_parsed.body', $this->q)
                ->orLike('tb_autopay_sms_parsed.reference_number', $this->q)
                ->orLike('tb_autopay_sms_parsed.code', $this->q)
                ->orLike('tb_autopay_sms_parsed.sub_account', $this->q)
            ->groupEnd();
        }

        if ($this->sortTransactionDate) {
            $this->builder->orderBy('tb_autopay_sms_parsed.transaction_date', $this->sortTransactionDate);
        }
    }
}