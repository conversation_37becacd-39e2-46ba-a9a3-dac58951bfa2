<?php

namespace App\Builders\Company;

use App\Builders\Builder;
use App\Models\CompanyModel;

class CompanyBuilder extends Builder
{
    public const INDEX_SELECT = ['id', 'full_name', 'short_name', 'status', 'active', 'created_at', 'updated_at', 'merchant_id'];
    public const DETAIL_SELECT = ['id', 'full_name', 'short_name', 'status', 'active', 'created_at', 'updated_at', 'merchant_id'];

    protected $sortCreatedAt = 'desc';

    protected $status = null;

    public function whereStatus($status)
    {
        $this->status = $status;

        return $this;
    }

    public function sortCreatedAt($sort)
    {
        $this->sortCreatedAt = $sort;

        return $this;
    }

    protected function handle()
    {
        $this->model = new CompanyModel;

        $this->builder = $this->model->select($this->select);
            
        if ($this->id) {
            $this->builder->where('tb_autopay_company.id', $this->id);
        }
        
        if ($this->merchantId) {
            $this->builder->where('tb_autopay_company.merchant_id', $this->merchantId);
        }

        if ($this->status) {
            $this->builder->where('tb_autopay_company.status', $this->status);
        }

        if ($this->q) {
            $this->builder->groupStart()
                ->like('tb_autopay_company.full_name', $this->q)
                ->orLike('tb_autopay_company.short_name', $this->q)
            ->groupEnd();
        }

        if ($this->sortCreatedAt) {
            $this->builder->orderBy('created_at', $this->sortCreatedAt);
        }
    }
}