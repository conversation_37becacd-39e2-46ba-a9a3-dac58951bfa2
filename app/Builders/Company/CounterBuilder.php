<?php

namespace App\Builders\Company;

use App\Builders\Builder;
use App\Models\CompanyModel;
use App\Models\CounterModel;
use App\Models\ConfigurationModel;

class CounterBuilder extends Builder
{
    public const INDEX_SELECT = ['tb_autopay_counter.company_id', 'tb_autopay_counter.date', 'tb_autopay_counter.transaction', 'tb_autopay_counter.transaction_in', 'tb_autopay_counter.transaction_out'];

    public const DETAIL_SELECT = ['tb_autopay_counter.company_id', 'tb_autopay_counter.date', 'tb_autopay_counter.transaction', 'tb_autopay_counter.transaction_in', 'tb_autopay_counter.transaction_out'];

    protected $companyId = null;

    protected $date = null;

    public function whereCompanyId($companyId)
    {
        $this->companyId = $companyId;

        return $this;
    }

    public function whereDate($date)
    {
        $this->date = $date;

        return $this;
    }

    protected function handle()
    {
        $this->model = new CounterModel;

        $this->builder = $this->model->select($this->select);
            
        if ($this->id) {
            $this->builder->where('tb_autopay_counter.id', $this->id);
        }
        
        if ($this->merchantId) {
            $this->builder->where('tb_autopay_counter.merchant_id', $this->merchantId);
        }

        if ($this->companyId) {
            $this->builder->where('tb_autopay_counter.company_id', $this->companyId);
        }

        if ($this->date) {
            $this->builder->where("DATE_FORMAT(tb_autopay_counter.date, '%Y-%m-%d')", $this->date);
        }
    }
}