<?php

namespace App\Builders\Company;

use App\Builders\Builder;
use App\Models\CompanyModel;
use App\Models\ConfigurationModel;

class ConfigurationBuilder extends Builder
{
    public const INDEX_SELECT = ['tb_autopay_configuration.setting', 'tb_autopay_configuration.value'];
    public const DETAIL_SELECT = ['tb_autopay_configuration.setting', 'tb_autopay_configuration.value'];
    public const HIDDEN_SETTINGS = ['BankSubAccount'];

    protected $companyId = null;

    public function whereCompanyId($companyId)
    {
        $this->companyId = $companyId;

        return $this;
    }

    protected function handle()
    {
        $this->model = new ConfigurationModel;

        $this->builder = $this->model->select($this->select);
            
        if ($this->id) {
            $this->builder->where('tb_autopay_configuration.id', $this->id);
        }
        
        if ($this->merchantId) {
            $this->builder->where('tb_autopay_configuration.merchant_id', $this->merchantId);
        }

        if ($this->companyId) {
            $this->builder->where('tb_autopay_configuration.company_id', $this->companyId);
        }

        if (count(static::HIDDEN_SETTINGS)) {
            $this->builder->whereNotIn('tb_autopay_configuration.setting', static::HIDDEN_SETTINGS);
        }
    }
}