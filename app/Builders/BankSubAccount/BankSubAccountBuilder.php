<?php

namespace App\Builders\BankSubAccount;

use App\Builders\Builder;
use App\Models\BankSubAccountModel;

class BankSubAccountBuilder extends Builder
{
    public const INDEX_SELECT = ['tb_autopay_bank_sub_account.id', 'tb_autopay_bank_account.company_id', 'tb_autopay_bank_sub_account.bank_account_id', 'tb_autopay_bank_sub_account.sub_account as va', 'tb_autopay_bank_sub_account.label', 'tb_autopay_bank_sub_account.active', 'tb_autopay_bank_sub_account.created_at', 'tb_autopay_bank_sub_account.updated_at', 'tb_autopay_bank_sub_account.merchant_id'];
    public const DETAIL_SELECT = ['tb_autopay_bank_sub_account.id', 'tb_autopay_bank_account.company_id', 'tb_autopay_bank_sub_account.bank_account_id', 'tb_autopay_bank_sub_account.sub_account as va', 'tb_autopay_bank_sub_account.label', 'tb_autopay_bank_sub_account.active', 'tb_autopay_bank_sub_account.va_active', 'tb_autopay_bank_sub_account.va_order', 'tb_autopay_bank_sub_account.created_at', 'tb_autopay_bank_sub_account.updated_at', 'tb_autopay_bank_sub_account.merchant_id'];

    protected $bankId;

    protected $bankAccountId;

    protected $companyId;

    public function whereBankId($bankId)
    {
        $this->bankId = $bankId;

        return $this;
    }

    public function whereCompanyId($companyId)
    {
        $this->companyId = $companyId;

        return $this;
    }

    public function whereBankAccountId($bankAccountId)
    {
        $this->bankAccountId = $bankAccountId;

        return $this;
    }

    protected function handle()
    {
        $this->model = new BankSubAccountModel;

        $this->builder = $this->model
            ->select($this->select)
            ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id = tb_autopay_bank_sub_account.bank_account_id');
            
        if ($this->id) {
            $this->builder->where('tb_autopay_bank_sub_account.id', $this->id);
        }
        
        if ($this->merchantId) {
            $this->builder->where('tb_autopay_bank_sub_account.merchant_id', $this->merchantId);
        }

        if ($this->bankId) {
            $this->builder->where('tb_autopay_bank_account.bank_id', $this->bankId);
        }

        if ($this->companyId) {
            $this->builder->where('tb_autopay_bank_account.company_id', $this->companyId);
        }

        if ($this->bankAccountId) {
            $this->builder->where('tb_autopay_bank_account.id', $this->bankAccountId);
        }
            
        if ($this->q) {
            $this->builder->groupStart()
                ->like('tb_autopay_bank_sub_account.sub_account', $this->q)
                ->orLike('tb_autopay_bank_sub_account.label', $this->q)
            ->groupEnd();
        }
    }
}